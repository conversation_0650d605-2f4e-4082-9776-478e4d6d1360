export class DebugOverlay {
    constructor() {
        this.overlay = this.createOverlay();
        this.stats = {};
    }

    createOverlay() {
        const overlay = document.createElement('div');
        overlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            padding: 10px;
            background: rgba(0, 0, 0, 0.7);
            color: #fff;
            font-family: monospace;
            font-size: 12px;
            z-index: 9999;
            pointer-events: none;
            white-space: pre;
        `;
        document.body.appendChild(overlay);
        return overlay;
    }

    update(stats) {
        this.stats = { ...this.stats, ...stats };

        const text = Object.entries(this.stats)
            .map(([key, value]) => `${key}: ${JSON.stringify(value)}`)
            .join('\n');

        this.overlay.textContent = text;
    }

    toggle() {
        this.overlay.style.display =
            this.overlay.style.display === 'none' ? 'block' : 'none';
    }
} 