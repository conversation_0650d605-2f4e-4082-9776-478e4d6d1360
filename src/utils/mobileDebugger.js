/**
 * Mobile Debugger
 * A simple utility to display debug logs directly on the mobile screen
 * Only activates on mobile devices, not on the host machine
 */

export class MobileDebugger {
    constructor(options = {}) {
        this.options = {
            maxLogs: 50,
            fontSize: '12px',
            backgroundColor: 'rgba(0, 0, 0, 0.8)',
            textColor: '#fff',
            position: 'bottom',  // 'top' or 'bottom'
            ...options
        };

        this.logs = [];
        this.container = null;
        this.logContainer = null;
        this.visible = false;
        this.initialized = false;
        this.isMobileDevice = this._checkIfMobile();

        // Create a global reference for easy access, but only on mobile devices
        if (this.isMobileDevice) {
            window.mobileDebugger = this;
        }
    }

    /**
     * Check if the current device is a mobile device
     * @private
     * @returns {boolean} True if the device is a mobile device
     */
    _checkIfMobile() {
        // Check if this is a mobile device using user agent
        const userAgent = navigator.userAgent || navigator.vendor || window.opera;
        const isMobile = /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(userAgent.toLowerCase());

        // Also check screen size as a fallback
        const isSmallScreen = window.innerWidth <= 768;

        // Check if we're explicitly in controller mode
        const isController = window.location.pathname.includes('/controller') ||
            window.location.search.includes('controller') ||
            document.body.classList.contains('controller-mode');

        return isMobile || isSmallScreen || isController;
    }

    /**
     * Initialize the debugger
     */
    initialize() {
        // Skip initialization on non-mobile devices
        if (this.initialized || !this.isMobileDevice) return;

        // Create the container
        this.container = document.createElement('div');
        this.container.className = 'mobile-debugger';
        this.container.style.position = 'fixed';
        this.container.style.left = '0';
        this.container.style.width = '100%';
        this.container.style.height = '40%';
        this.container.style.backgroundColor = this.options.backgroundColor;
        this.container.style.color = this.options.textColor;
        this.container.style.fontFamily = 'monospace';
        this.container.style.fontSize = this.options.fontSize;
        this.container.style.padding = '10px';
        this.container.style.boxSizing = 'border-box';
        this.container.style.overflowY = 'auto';
        this.container.style.zIndex = '9999';
        this.container.style.display = 'none';

        // Position the container
        if (this.options.position === 'top') {
            this.container.style.top = '0';
        } else {
            this.container.style.bottom = '0';
        }

        // Create the log container
        this.logContainer = document.createElement('div');
        this.logContainer.className = 'mobile-debugger-logs';
        this.container.appendChild(this.logContainer);

        // Create the control bar
        const controlBar = document.createElement('div');
        controlBar.className = 'mobile-debugger-controls';
        controlBar.style.display = 'flex';
        controlBar.style.justifyContent = 'space-between';
        controlBar.style.marginBottom = '10px';

        // Create the title
        const title = document.createElement('div');
        title.textContent = 'Mobile Debug Console';
        title.style.fontWeight = 'bold';
        controlBar.appendChild(title);

        // Create the buttons container
        const buttonsContainer = document.createElement('div');
        buttonsContainer.style.display = 'flex';
        buttonsContainer.style.gap = '10px';

        // Create the clear button
        const clearButton = document.createElement('button');
        clearButton.textContent = 'Clear';
        clearButton.style.padding = '2px 8px';
        clearButton.style.backgroundColor = '#f44336';
        clearButton.style.border = 'none';
        clearButton.style.borderRadius = '3px';
        clearButton.style.color = 'white';
        clearButton.style.cursor = 'pointer';
        clearButton.style.fontSize = '12px';
        clearButton.addEventListener('click', () => this.clear());
        buttonsContainer.appendChild(clearButton);

        // Create the close button
        const closeButton = document.createElement('button');
        closeButton.textContent = 'Close';
        closeButton.style.padding = '2px 8px';
        closeButton.style.backgroundColor = '#2196F3';
        closeButton.style.border = 'none';
        closeButton.style.borderRadius = '3px';
        closeButton.style.color = 'white';
        closeButton.style.cursor = 'pointer';
        closeButton.style.fontSize = '12px';
        closeButton.addEventListener('click', () => this.hide());
        buttonsContainer.appendChild(closeButton);

        controlBar.appendChild(buttonsContainer);
        this.container.insertBefore(controlBar, this.logContainer);

        // Add to the document body
        document.body.appendChild(this.container);

        // Create the toggle button
        this._createToggleButton();

        // Override console methods to capture logs
        this._overrideConsoleMethods();

        this.initialized = true;
        this.log('Mobile Debugger initialized');
    }

    /**
     * Create a toggle button to show/hide the debugger
     * @private
     */
    _createToggleButton() {
        // Skip creating toggle button on non-mobile devices
        if (!this.isMobileDevice) return;

        const toggleButton = document.createElement('button');
        toggleButton.className = 'mobile-debugger-toggle';
        toggleButton.style.position = 'fixed';
        toggleButton.style.right = '20px';
        toggleButton.style.bottom = '20px';
        toggleButton.style.width = '50px';
        toggleButton.style.height = '50px';
        toggleButton.style.borderRadius = '50%';
        toggleButton.style.backgroundColor = '#2196F3';
        toggleButton.style.color = 'white';
        toggleButton.style.border = 'none';
        toggleButton.style.fontSize = '20px';
        toggleButton.style.display = 'flex';
        toggleButton.style.alignItems = 'center';
        toggleButton.style.justifyContent = 'center';
        toggleButton.style.boxShadow = '0 2px 10px rgba(0, 0, 0, 0.3)';
        toggleButton.style.zIndex = '9998';
        toggleButton.style.cursor = 'pointer';
        toggleButton.innerHTML = '🐞';

        toggleButton.addEventListener('click', () => {
            this.toggle();
        });

        document.body.appendChild(toggleButton);
        this.toggleButton = toggleButton;
    }

    /**
     * Override console methods to capture logs
     * @private
     */
    _overrideConsoleMethods() {
        // Skip overriding console methods on non-mobile devices
        if (!this.isMobileDevice) return;

        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;
        const originalInfo = console.info;
        const originalDebug = console.debug;

        const self = this;

        console.log = function () {
            self.log(...arguments);
            originalLog.apply(console, arguments);
        };

        console.error = function () {
            self.error(...arguments);
            originalError.apply(console, arguments);
        };

        console.warn = function () {
            self.warn(...arguments);
            originalWarn.apply(console, arguments);
        };

        console.info = function () {
            self.info(...arguments);
            originalInfo.apply(console, arguments);
        };

        console.debug = function () {
            self.debug(...arguments);
            originalDebug.apply(console, arguments);
        };

        // Store original methods for restoration
        this.originalConsoleMethods = {
            log: originalLog,
            error: originalError,
            warn: originalWarn,
            info: originalInfo,
            debug: originalDebug
        };
    }

    /**
     * Format arguments into a string
     * @private
     * @param {Array} args - The arguments to format
     * @returns {string} - The formatted string
     */
    _formatArgs(args) {
        return Array.from(args).map(arg => {
            if (typeof arg === 'object') {
                try {
                    return JSON.stringify(arg);
                } catch (e) {
                    return String(arg);
                }
            }
            return String(arg);
        }).join(' ');
    }

    /**
     * Add a log entry
     * @param {string} type - The log type (log, error, warn, info, debug)
     * @param {Array} args - The log arguments
     * @private
     */
    _addLogEntry(type, args) {
        // Skip logging on non-mobile devices
        if (!this.isMobileDevice) return;

        if (!this.initialized) {
            this.initialize();
        }

        const timestamp = new Date().toISOString().split('T')[1].split('.')[0];
        const message = this._formatArgs(args);

        this.logs.push({
            type,
            message,
            timestamp
        });

        // Limit the number of logs
        if (this.logs.length > this.options.maxLogs) {
            this.logs.shift();
        }

        // Update the display if visible
        if (this.visible) {
            this._updateDisplay();
        }
    }

    /**
     * Update the display with the current logs
     * @private
     */
    _updateDisplay() {
        if (!this.logContainer) return;

        this.logContainer.innerHTML = '';

        this.logs.forEach(log => {
            const logEntry = document.createElement('div');
            logEntry.className = `mobile-debugger-entry mobile-debugger-${log.type}`;
            logEntry.style.marginBottom = '5px';
            logEntry.style.wordBreak = 'break-word';
            logEntry.style.lineHeight = '1.3';

            // Set color based on log type
            switch (log.type) {
                case 'error':
                    logEntry.style.color = '#ff5252';
                    break;
                case 'warn':
                    logEntry.style.color = '#ffab40';
                    break;
                case 'info':
                    logEntry.style.color = '#40c4ff';
                    break;
                case 'debug':
                    logEntry.style.color = '#69f0ae';
                    break;
                default:
                    logEntry.style.color = this.options.textColor;
            }

            logEntry.textContent = `[${log.timestamp}] ${log.type.toUpperCase()}: ${log.message}`;
            this.logContainer.appendChild(logEntry);
        });

        // Scroll to bottom
        this.logContainer.scrollTop = this.logContainer.scrollHeight;
    }

    /**
     * Log a message
     * @param {...any} args - The arguments to log
     */
    log(...args) {
        // Skip logging on non-mobile devices
        if (!this.isMobileDevice) return;
        this._addLogEntry('log', args);
    }

    /**
     * Log an error
     * @param {...any} args - The arguments to log
     */
    error(...args) {
        // Skip logging on non-mobile devices
        if (!this.isMobileDevice) return;
        this._addLogEntry('error', args);
    }

    /**
     * Log a warning
     * @param {...any} args - The arguments to log
     */
    warn(...args) {
        // Skip logging on non-mobile devices
        if (!this.isMobileDevice) return;
        this._addLogEntry('warn', args);
    }

    /**
     * Log info
     * @param {...any} args - The arguments to log
     */
    info(...args) {
        // Skip logging on non-mobile devices
        if (!this.isMobileDevice) return;
        this._addLogEntry('info', args);
    }

    /**
     * Log debug information
     * @param {...any} args - The arguments to log
     */
    debug(...args) {
        // Skip logging on non-mobile devices
        if (!this.isMobileDevice) return;
        this._addLogEntry('debug', args);
    }

    /**
     * Show the debugger
     */
    show() {
        // Skip on non-mobile devices
        if (!this.isMobileDevice) return;

        if (!this.initialized) {
            this.initialize();
        }

        this.container.style.display = 'block';
        this.visible = true;
        this._updateDisplay();
    }

    /**
     * Hide the debugger
     */
    hide() {
        // Skip on non-mobile devices
        if (!this.isMobileDevice) return;

        if (this.container) {
            this.container.style.display = 'none';
            this.visible = false;
        }
    }

    /**
     * Toggle the debugger visibility
     */
    toggle() {
        // Skip on non-mobile devices
        if (!this.isMobileDevice) return;

        if (this.visible) {
            this.hide();
        } else {
            this.show();
        }
    }

    /**
     * Clear all logs
     */
    clear() {
        // Skip on non-mobile devices
        if (!this.isMobileDevice) return;

        this.logs = [];
        if (this.logContainer) {
            this.logContainer.innerHTML = '';
        }
        this.log('Logs cleared');
    }

    /**
     * Dispose of the debugger and restore original console methods
     */
    dispose() {
        // Skip on non-mobile devices
        if (!this.isMobileDevice) return;

        // Restore original console methods
        if (this.originalConsoleMethods) {
            console.log = this.originalConsoleMethods.log;
            console.error = this.originalConsoleMethods.error;
            console.warn = this.originalConsoleMethods.warn;
            console.info = this.originalConsoleMethods.info;
            console.debug = this.originalConsoleMethods.debug;
        }

        // Remove DOM elements
        if (this.container && this.container.parentNode) {
            this.container.parentNode.removeChild(this.container);
        }

        if (this.toggleButton && this.toggleButton.parentNode) {
            this.toggleButton.parentNode.removeChild(this.toggleButton);
        }

        // Remove global reference
        if (window.mobileDebugger === this) {
            delete window.mobileDebugger;
        }

        this.initialized = false;
        this.visible = false;
    }
}

// Create a singleton instance
const mobileDebugger = new MobileDebugger();
export default mobileDebugger;
