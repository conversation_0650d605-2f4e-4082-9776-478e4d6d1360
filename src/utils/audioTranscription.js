/**
 * Unified Audio Transcription Utility
 * 
 * Provides shared functionality for audio transcription across different UI components.
 * Handles audio format conversion, transcription, and error handling consistently.
 */

/**
 * Convert audio blob to a format compatible with ASR services
 * @param {Blob} audioBlob - The original audio blob
 * @param {string} targetFormat - Target format ('wav', 'mp3', etc.)
 * @returns {Promise<Blob>} Converted audio blob
 */
export async function convertAudioFormat(audioBlob, targetFormat = 'wav') {
    return new Promise((resolve, reject) => {
        try {
            // Create audio context for format conversion
            const audioContext = new (window.AudioContext || window.webkitAudioContext)();

            // Create FileReader to read the blob
            const reader = new FileReader();

            reader.onload = async (event) => {
                try {
                    // Decode the audio data
                    const arrayBuffer = event.target.result;
                    const audioBuffer = await audioContext.decodeAudioData(arrayBuffer);

                    // Convert to WAV format
                    const wavBlob = await audioBufferToWav(audioBuffer);

                    // Close audio context to free resources
                    audioContext.close();

                    resolve(wavBlob);
                } catch (error) {
                    console.error('[AudioTranscription] Error decoding audio:', error);
                    // If conversion fails, try to use original blob
                    resolve(audioBlob);
                }
            };

            reader.onerror = () => {
                console.error('[AudioTranscription] Error reading audio blob');
                reject(new Error('Failed to read audio blob'));
            };

            reader.readAsArrayBuffer(audioBlob);

        } catch (error) {
            console.error('[AudioTranscription] Error in audio conversion:', error);
            // Fallback to original blob if conversion fails
            resolve(audioBlob);
        }
    });
}

/**
 * Convert AudioBuffer to WAV blob
 * @param {AudioBuffer} audioBuffer - The audio buffer to convert
 * @returns {Promise<Blob>} WAV format blob
 */
function audioBufferToWav(audioBuffer) {
    return new Promise((resolve) => {
        try {
            const numberOfChannels = audioBuffer.numberOfChannels;
            const sampleRate = audioBuffer.sampleRate;
            const format = 1; // PCM format
            const bitDepth = 16;

            // Calculate buffer sizes
            const bytesPerSample = bitDepth / 8;
            const blockAlign = numberOfChannels * bytesPerSample;
            const byteRate = sampleRate * blockAlign;
            const dataLength = audioBuffer.length * blockAlign;
            const bufferLength = 44 + dataLength;

            // Create ArrayBuffer for WAV file
            const arrayBuffer = new ArrayBuffer(bufferLength);
            const view = new DataView(arrayBuffer);

            // WAV header
            let offset = 0;

            // RIFF chunk descriptor
            writeString(view, offset, 'RIFF'); offset += 4;
            view.setUint32(offset, bufferLength - 8, true); offset += 4;
            writeString(view, offset, 'WAVE'); offset += 4;

            // fmt sub-chunk
            writeString(view, offset, 'fmt '); offset += 4;
            view.setUint32(offset, 16, true); offset += 4; // Sub-chunk size
            view.setUint16(offset, format, true); offset += 2; // Audio format
            view.setUint16(offset, numberOfChannels, true); offset += 2; // Number of channels
            view.setUint32(offset, sampleRate, true); offset += 4; // Sample rate
            view.setUint32(offset, byteRate, true); offset += 4; // Byte rate
            view.setUint16(offset, blockAlign, true); offset += 2; // Block align
            view.setUint16(offset, bitDepth, true); offset += 2; // Bits per sample

            // data sub-chunk
            writeString(view, offset, 'data'); offset += 4;
            view.setUint32(offset, dataLength, true); offset += 4;

            // Write audio data
            const channels = [];
            for (let i = 0; i < numberOfChannels; i++) {
                channels.push(audioBuffer.getChannelData(i));
            }

            let sampleIndex = 0;
            while (sampleIndex < audioBuffer.length) {
                for (let channel = 0; channel < numberOfChannels; channel++) {
                    const sample = Math.max(-1, Math.min(1, channels[channel][sampleIndex]));
                    const intSample = sample < 0 ? sample * 0x8000 : sample * 0x7FFF;
                    view.setInt16(offset, intSample, true);
                    offset += 2;
                }
                sampleIndex++;
            }

            resolve(new Blob([arrayBuffer], { type: 'audio/wav' }));

        } catch (error) {
            console.error('[AudioTranscription] Error converting to WAV:', error);
            // Return empty blob as fallback
            resolve(new Blob([], { type: 'audio/wav' }));
        }
    });
}

/**
 * Write string to DataView
 * @param {DataView} view - DataView to write to
 * @param {number} offset - Offset to start writing
 * @param {string} string - String to write
 */
function writeString(view, offset, string) {
    for (let i = 0; i < string.length; i++) {
        view.setUint8(offset + i, string.charCodeAt(i));
    }
}

/**
 * Transcribe audio using the talking avatar's STT service
 * @param {Object} talkingAvatar - The talking avatar instance with STT service
 * @param {Blob} audioBlob - The audio blob to transcribe
 * @param {Object} options - Transcription options
 * @returns {Promise<Object>} Transcription result with text and metadata
 */
export async function transcribeAudioWithSTT(talkingAvatar, audioBlob, options = {}) {
    try {
        console.log('[AudioTranscription] Starting transcription with STT service');

        if (!talkingAvatar || !talkingAvatar.sttServiceInstance) {
            throw new Error('STT service not available - no talking avatar found');
        }

        // Convert audio to WAV format for better compatibility
        console.log('[AudioTranscription] Converting audio to WAV format');
        const convertedAudioBlob = await convertAudioFormat(audioBlob, 'wav');

        // Get the STT service instance
        const sttService = talkingAvatar.sttServiceInstance;

        // Get the current language from the avatar's voice config or use 'auto'
        let language = options.language || 'auto';
        if (talkingAvatar.voiceConfig?.currentLanguage) {
            language = talkingAvatar.voiceConfig.currentLanguage;

            // Convert language codes for STT service compatibility
            if (language === 'chinese') {
                language = 'zh';
                console.log('[AudioTranscription] Converting language "chinese" to "zh" for transcription');
            }
        }

        console.log(`[AudioTranscription] Transcribing audio with language: ${language}`);

        // Perform transcription
        const transcriptionResult = await sttService.transcribeAudio(convertedAudioBlob, {
            language: language,
            ...options
        });

        // Handle both old string format and new enhanced format with metadata
        let transcribedText;
        let metadata = {};

        if (typeof transcriptionResult === 'string') {
            // Legacy format - just text
            transcribedText = transcriptionResult;
        } else if (transcriptionResult && transcriptionResult.text) {
            // New enhanced format with metadata
            transcribedText = transcriptionResult.text;
            metadata = transcriptionResult.metadata || {};
            console.log('[AudioTranscription] Extracted transcription metadata:', metadata);
        } else {
            throw new Error('Invalid transcription result format');
        }

        if (!transcribedText || !transcribedText.trim()) {
            throw new Error('No text was transcribed from the audio');
        }

        console.log('[AudioTranscription] Transcription successful:', transcribedText);

        return {
            text: transcribedText.trim(),
            metadata: metadata,
            language: language,
            success: true
        };

    } catch (error) {
        console.error('[AudioTranscription] Transcription error:', error);
        return {
            text: null,
            error: error.message,
            success: false
        };
    }
}

/**
 * Create optimized recording options for better ASR compatibility
 * @param {Object} customOptions - Custom recording options to override defaults
 * @returns {Object} MediaRecorder options
 */
export function createOptimizedRecordingOptions(customOptions = {}) {
    // Default options optimized for ASR services
    const defaultOptions = {
        audio: {
            sampleRate: 16000,      // Standard ASR sample rate
            channelCount: 1,        // Mono audio
            echoCancellation: true,
            noiseSuppression: true,
            autoGainControl: true   // Help with volume normalization
        }
    };

    // Try different MIME types in order of preference for ASR compatibility
    const mimeTypes = [
        'audio/webm;codecs=opus',
        'audio/webm',
        'audio/mp4',
        'audio/wav'
    ];

    let selectedMimeType = null;
    for (const mimeType of mimeTypes) {
        if (MediaRecorder.isTypeSupported(mimeType)) {
            selectedMimeType = mimeType;
            break;
        }
    }

    const mediaRecorderOptions = {
        mimeType: selectedMimeType || 'audio/webm;codecs=opus'
    };

    return {
        getUserMediaOptions: {
            ...defaultOptions,
            ...customOptions
        },
        mediaRecorderOptions
    };
}

/**
 * Show transcription notification with consistent styling across UI components
 * @param {Object} uiComponent - UI component with showNotification method
 * @param {string} message - Message to display
 * @param {boolean} isError - Whether this is an error notification
 * @param {number} duration - Duration to show notification
 */
export function showTranscriptionNotification(uiComponent, message, isError = false, duration = 3000) {
    if (uiComponent && typeof uiComponent.showNotification === 'function') {
        uiComponent.showNotification(message, isError, duration);
    } else {
        // Fallback to console logging if no notification system available
        if (isError) {
            console.error('[AudioTranscription]', message);
        } else {
            console.log('[AudioTranscription]', message);
        }
    }
}

/**
 * Handle common transcription workflow
 * @param {Object} params - Parameters object
 * @param {Blob} params.audioBlob - Audio blob to transcribe
 * @param {Object} params.talkingAvatar - Talking avatar instance
 * @param {Object} params.uiComponent - UI component for notifications
 * @param {Function} params.onSuccess - Callback for successful transcription
 * @param {Function} params.onError - Callback for transcription error
 * @param {Object} params.options - Additional transcription options
 * @returns {Promise<void>}
 */
export async function handleTranscriptionWorkflow({
    audioBlob,
    talkingAvatar,
    uiComponent,
    onSuccess,
    onError,
    options = {}
}) {
    try {
        console.log('[AudioTranscription] Starting transcription workflow');

        // Show processing notification
        showTranscriptionNotification(uiComponent, 'Transcribing audio...');

        // Perform transcription
        const result = await transcribeAudioWithSTT(talkingAvatar, audioBlob, options);

        if (result.success && result.text) {
            // Show success notification
            showTranscriptionNotification(
                uiComponent,
                'Audio transcribed successfully!'
            );

            // Call success callback
            if (typeof onSuccess === 'function') {
                await onSuccess(result);
            }
        } else {
            throw new Error(result.error || 'Transcription failed');
        }

    } catch (error) {
        console.error('[AudioTranscription] Transcription workflow error:', error);

        // Show error notification with fallback suggestion
        showTranscriptionNotification(
            uiComponent,
            'Failed to transcribe audio. Please try again or type manually.',
            true,
            5000
        );

        // Call error callback
        if (typeof onError === 'function') {
            onError(error);
        }
    }
} 