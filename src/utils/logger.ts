/**
 * Unified logging utility for consistent logging across the application
 * Works in both TypeScript and JavaScript environments
 * 
 * Features:
 * - Global log level control
 * - Per-module log level control
 * - Hierarchical log level resolution (module-specific overrides global)
 */

// Log levels
export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
  NONE = 4
}

// Global log level (default for all modules)
let globalLogLevel = LogLevel.DEBUG;

// Per-module log levels (overrides global level)
const moduleLogLevels = new Map<string, LogLevel>();

/**
 * Set the global log level (affects all modules unless they have specific overrides)
 * @param level - Log level from LogLevel enum
 */
export function setLogLevel(level: LogLevel): void {
  globalLogLevel = level;
  console.log(`[Logger] Global log level set to ${getLogLevelName(level)}`);
}

/**
 * Set log level for a specific module
 * @param moduleName - Name of the module
 * @param level - Log level from LogLevel enum
 */
export function setModuleLogLevel(moduleName: string, level: LogLevel): void {
  moduleLogLevels.set(moduleName, level);
  console.log(`[Logger] Module '${moduleName}' log level set to ${getLogLevelName(level)}`);
}

/**
 * Clear log level override for a specific module (falls back to global level)
 * @param moduleName - Name of the module
 */
export function clearModuleLogLevel(moduleName: string): void {
  const wasSet = moduleLogLevels.delete(moduleName);
  if (wasSet) {
    console.log(`[Logger] Module '${moduleName}' log level cleared, using global level (${getLogLevelName(globalLogLevel)})`);
  }
}

/**
 * Get the effective log level for a module (module-specific or global)
 * @param moduleName - Name of the module
 * @returns Effective log level
 */
export function getEffectiveLogLevel(moduleName: string): LogLevel {
  return moduleLogLevels.get(moduleName) ?? globalLogLevel;
}

/**
 * Get the global log level
 * @returns Current global log level
 */
export function getLogLevel(): LogLevel {
  return globalLogLevel;
}

/**
 * Get log level name for display purposes
 * @param level - Log level number
 * @returns Log level name
 */
function getLogLevelName(level: LogLevel): string {
  return LogLevel[level] || 'UNKNOWN';
}

/**
 * List all module-specific log level overrides
 * @returns Object with module names as keys and log levels as values
 */
export function getModuleLogLevels(): Record<string, { level: LogLevel; levelName: string }> {
  const result: Record<string, { level: LogLevel; levelName: string }> = {};
  for (const [module, level] of Array.from(moduleLogLevels.entries())) {
    result[module] = {
      level,
      levelName: getLogLevelName(level)
    };
  }
  return result;
}

/**
 * Format a log message with a module name
 * @param module - Module name
 * @param message - Log message
 * @returns Formatted message
 */
function formatLogMessage(module: string, message: string): string {
  return `[${module}] ${message}`;
}

/**
 * Log a debug message
 * @param module - Module name
 * @param message - Log message
 * @param args - Additional arguments
 */
export function debug(module: string, message: string, ...args: any[]): void {
  if (getEffectiveLogLevel(module) <= LogLevel.DEBUG) {
    console.debug(formatLogMessage(module, message), ...args);
  }
}

/**
 * Log an info message
 * @param module - Module name
 * @param message - Log message
 * @param args - Additional arguments
 */
export function info(module: string, message: string, ...args: any[]): void {
  if (getEffectiveLogLevel(module) <= LogLevel.INFO) {
    console.log(formatLogMessage(module, message), ...args);
  }
}

/**
 * Log a warning message
 * @param module - Module name
 * @param message - Log message
 * @param args - Additional arguments
 */
export function warn(module: string, message: string, ...args: any[]): void {
  if (getEffectiveLogLevel(module) <= LogLevel.WARN) {
    console.warn(formatLogMessage(module, message), ...args);
  }
}

/**
 * Log an error message
 * @param module - Module name
 * @param message - Log message
 * @param args - Additional arguments
 */
export function error(module: string, message: string, ...args: any[]): void {
  if (getEffectiveLogLevel(module) <= LogLevel.ERROR) {
    console.error(formatLogMessage(module, message), ...args);
  }
}

/**
 * Logger interface with utility methods
 */
export interface Logger {
  // Logging methods
  debug: (message: string, ...args: any[]) => void;
  info: (message: string, ...args: any[]) => void;
  warn: (message: string, ...args: any[]) => void;
  error: (message: string, ...args: any[]) => void;

  // Module-specific log level control
  setLogLevel: (level: LogLevel) => void;
  clearLogLevel: () => void;
  getLogLevel: () => LogLevel;

  // Utility methods
  getModuleName: () => string;
  isDebugEnabled: () => boolean;
  isInfoEnabled: () => boolean;
  isWarnEnabled: () => boolean;
  isErrorEnabled: () => boolean;
}

/**
 * Create a logger for a specific module
 * @param module - Module name
 * @returns Logger object with debug, info, warn, and error methods, plus level control
 */
export function createLogger(module: string): Logger {
  return {
    // Logging methods
    debug: (message: string, ...args: any[]) => debug(module, message, ...args),
    info: (message: string, ...args: any[]) => info(module, message, ...args),
    warn: (message: string, ...args: any[]) => warn(module, message, ...args),
    error: (message: string, ...args: any[]) => error(module, message, ...args),

    // Module-specific log level control
    setLogLevel: (level: LogLevel) => setModuleLogLevel(module, level),
    clearLogLevel: () => clearModuleLogLevel(module),
    getLogLevel: () => getEffectiveLogLevel(module),

    // Utility methods
    getModuleName: () => module,
    isDebugEnabled: () => getEffectiveLogLevel(module) <= LogLevel.DEBUG,
    isInfoEnabled: () => getEffectiveLogLevel(module) <= LogLevel.INFO,
    isWarnEnabled: () => getEffectiveLogLevel(module) <= LogLevel.WARN,
    isErrorEnabled: () => getEffectiveLogLevel(module) <= LogLevel.ERROR
  };
}
