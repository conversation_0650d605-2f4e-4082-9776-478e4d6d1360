/**
 * Utility functions for formatting responses
 */

/**
 * Create a standardized error response
 * @param errorMessage Error message
 * @param requestInfo Request information
 * @returns Formatted error response
 */
export function createErrorResponse(errorMessage: string, requestInfo: any): any {
    return {
        success: true, // Return success to avoid UI errors
        data: {
            input: {
                text: requestInfo.inputText || 'Media input',
                language: requestInfo.language || 'auto'
            },
            response: {
                text: `This is a simulated response because the API is not available. ${errorMessage}`
            },
            sessionId: requestInfo.sessionId || 'unknown-session',
            hasAudio: false,
            audioData: null
        }
    };
}

/**
 * Create a standardized success response
 * @param responseText Response text
 * @param requestInfo Request information
 * @param audioData Optional audio data
 * @returns Formatted success response
 */
export function createSuccessResponse(responseText: string, requestInfo: any, audioData: string | null = null): any {
    return {
        success: true,
        data: {
            input: {
                text: requestInfo.inputText || 'Media input',
                language: requestInfo.language || 'auto'
            },
            response: {
                text: responseText
            },
            sessionId: requestInfo.sessionId || 'unknown-session',
            hasAudio: !!audioData,
            audioData: audioData
        }
    };
}

/**
 * Log a request with consistent formatting
 * @param serviceName Name of the service
 * @param requestInfo Request information
 */
export function logRequest(serviceName: string, requestInfo: any): void {
    console.log(`[${serviceName}] Processing request:`, {
        endpoint: requestInfo.endpoint,
        mediaType: requestInfo.mediaType,
        sessionId: requestInfo.sessionId,
        language: requestInfo.language || 'auto',
        streaming: requestInfo.streaming || false,
        payloadSize: requestInfo.payloadSize || 'unknown'
    });
}
