/**
 * Utility functions for voice cloning and audio file handling
 */

import { ASSETS } from '@apps/viewer/viewerConfig.js';
import { config } from '../config/client';
import { storeSeed, generateCacheKey } from './cache.js';
import {getDownloadServerPort } from '@/utils/portManager'
// Constants
export const FAVORITE_AUDIO_FOLDER = '__favorite__';

/**
 * Check if an audio file exists at the given path
 * @param {string} audioFilePath - Path to the audio file
 * @returns {Promise<{exists: boolean, path: string}>} - Whether the file exists and the resolved path
 */
export async function checkAudioFileExists(audioFilePath) {
    if (!audioFilePath) {
        return { exists: false, path: null };
    }

    try {
        // Check if the audio file exists
        const response = await fetch(audioFilePath, { method: 'HEAD' });
        if (response.ok) {
            console.log(`[VoiceUtils] Audio file exists at ${audioFilePath}`);
            return { exists: true, path: audioFilePath };
        }

        // Try with the full URL including host if it doesn't already have it
        if (!audioFilePath.startsWith('http')) {
            const port = getDownloadServerPort();
            const fullUrl = `http://${config.host}:${port}${audioFilePath}`;
            console.log(`[VoiceUtils] Trying with full URL: ${fullUrl}`);

            try {
                const fullResponse = await fetch(fullUrl, { method: 'HEAD' });
                if (fullResponse.ok) {
                    console.log(`[VoiceUtils] Found audio file with full URL: ${fullUrl}`);
                    return { exists: true, path: fullUrl };
                }
            } catch (error) {
                console.warn(`[VoiceUtils] Error checking full URL: ${error.message}`);
            }
        }

        return { exists: false, path: audioFilePath };
    } catch (error) {
        console.warn(`[VoiceUtils] Error checking audio file: ${error.message}`);
        return { exists: false, path: audioFilePath };
    }
}

/**
 * Check if a reference voice file exists on the server
 * @param {string} roleName - The role name to check
 * @returns {Promise<boolean>} - Whether the file exists
 */
export async function checkClonedVoiceFileExists(roleName) {
    try {
        // Use the simplified endpoint with port 2994 for the proxy request
        const port = getDownloadServerPort();
        const proxyUrl = `http://${config.host}:${port}/sparktts-proxy/check_voice_file?name=${encodeURIComponent(roleName)}`;
        console.log(`[VoiceUtils] Checking reference voice file at: ${proxyUrl}`);

        const response = await fetch(proxyUrl);
        if (response.ok) {
            const result = await response.json();
            const exists = result.exists === true;
            console.log(`[VoiceUtils] Reference voice file exists on server: ${exists}`);
            return exists;
        }
    } catch (error) {
        console.warn('[VoiceUtils] Error checking if reference voice file exists on server:', error);
    }

    return false;
}

/**
 * Find a reference voice file in the favorite folder
 * @param {string} avatarName - The name of the avatar
 * @param {string} roleName - The role name to find
 * @returns {Promise<{exists: boolean, path: string}>} - Whether the file exists and the resolved path
 */
export async function findClonedVoiceInFavoriteFolder(avatarName, roleName) {
    try {
        // Try to list files in the favorite folder
        const port = getDownloadServerPort();
        const listUrl = `http://${config.host}:${port}/list?path=assets/audio/${FAVORITE_AUDIO_FOLDER}`;
        console.log(`[VoiceUtils] Listing files in favorite folder: ${listUrl}`);

        try {
            const listResponse = await fetch(listUrl);
            if (listResponse.ok) {
                const files = await listResponse.json();
                console.log(`[VoiceUtils] Found ${files.length} files in favorite folder:`, files);

                // Look for files that match our patterns
                for (const file of files) {
                    const fileName = file.name;

                    // Check if the file contains the avatar name and is a reference voice file
                    if (fileName.includes(avatarName) && fileName.includes('_reference.mp3')) {
                        const filePath = `/assets/audio/${FAVORITE_AUDIO_FOLDER}/${fileName}`;
                        console.log(`[VoiceUtils] Found matching favorite file: ${filePath}`);

                        // Check if the file exists
                        try {
                            const fileResponse = await fetch(filePath, { method: 'HEAD' });
                            if (fileResponse.ok) {
                                console.log(`[VoiceUtils] Using favorite file: ${filePath}`);
                                return { exists: true, path: filePath };
                            }
                        } catch (error) {
                            console.warn(`[VoiceUtils] Error checking favorite file: ${error.message}`);
                        }
                    }
                }
            }
        } catch (error) {
            console.warn(`[VoiceUtils] Error listing favorite folder: ${error.message}`);
        }

        // If we still don't have a file, try specific paths
        const possiblePaths = [
            // Pattern 1: avatarName_roleName_cloned.mp3
            `/assets/audio/${FAVORITE_AUDIO_FOLDER}/${avatarName}_${roleName}_cloned.mp3`,
            // Pattern 2: Just roleName_cloned.mp3
            `/assets/audio/${FAVORITE_AUDIO_FOLDER}/${roleName}_cloned.mp3`
        ];

        for (const path of possiblePaths) {
            console.log(`[VoiceUtils] Checking for favorite file: ${path}`);

            try {
                const response = await fetch(path, { method: 'HEAD' });
                if (response.ok) {
                    console.log(`[VoiceUtils] Found favorite file: ${path}`);
                    return { exists: true, path };
                }
            } catch (error) {
                console.warn(`[VoiceUtils] Error checking favorite file: ${error.message}`);
            }
        }
    } catch (error) {
        console.warn(`[VoiceUtils] Error checking favorite files: ${error.message}`);
    }

    return { exists: false, path: null };
}

/**
 * Update the seed file with voice configuration
 * @param {string} avatarName - The name of the avatar
 * @param {Object} voiceConfig - The voice configuration to save
 * @param {boolean} [forceUpdate=false] - Whether to force update even if not favorite
 * @returns {Promise<boolean>} - Whether the update was successful
 */
export async function updateSeedWithVoiceConfig(avatarName, voiceConfig, forceUpdate = false) {
    try {
        // Skip for the default avatar
        if (avatarName === 'default') {
            return false;
        }

        // Only update the seed file if it's marked as favorite or if forceUpdate is true
        if (!voiceConfig.favorite && !forceUpdate) {
            console.log(`[VoiceUtils] Skipping seed file update because voice is not marked as favorite`);
            return false;
        }

        // Generate the voice cache key
        const voiceCacheKey = await generateCacheKey('voice', avatarName, null);
        console.log(`[VoiceUtils] Generated voice cache key for update: ${voiceCacheKey}`);

        // If this is a favorite voice, ensure the flag is set
        if (voiceConfig.favorite) {
            // Store the voice configuration in the seed file
            await storeSeed(voiceCacheKey, JSON.stringify(voiceConfig));
            console.log(`[VoiceUtils] Updated seed file with favorite voice config:`, voiceConfig);
        } else if (forceUpdate) {
            // Store the voice configuration but don't mark as favorite
            await storeSeed(voiceCacheKey, JSON.stringify(voiceConfig));
            console.log(`[VoiceUtils] Force updated seed file with non-favorite voice config:`, voiceConfig);
        }

        return true;
    } catch (error) {
        console.error(`[VoiceUtils] Error updating seed file: ${error.message}`);
        return false;
    }
}

/**
 * Check if a path is in the favorite folder
 * @param {string} path - The path to check
 * @returns {boolean} - Whether the path is in the favorite folder
 */
export function isInFavoriteFolder(path) {
    if (!path) return false;
    return path.includes(`/${FAVORITE_AUDIO_FOLDER}/`) || path.includes(`\\${FAVORITE_AUDIO_FOLDER}\\`);
}
