# Utilities

This directory contains utility functions and modules used throughout the application.

## Logger

The `logger.ts` module provides a unified logging utility for consistent logging across the application. It supports different log levels and module-specific loggers.

### Usage

```typescript
import { createLogger, setLogLevel, LogLevel } from './logger';

// Create a logger for a specific module
const logger = createLogger('myModule');

// Log messages at different levels
logger.debug('Debug message');
logger.info('Info message');
logger.warn('Warning message');
logger.error('Error message', someErrorObject);

// Set the global log level
setLogLevel(LogLevel.DEBUG); // Show all logs
setLogLevel(LogLevel.INFO);  // Show info, warn, and error logs
setLogLevel(LogLevel.WARN);  // Show only warn and error logs
setLogLevel(LogLevel.ERROR); // Show only error logs
setLogLevel(LogLevel.NONE);  // Disable all logs
```

### Benefits

- **Consistent Format**: All log messages follow the same format: `[moduleName] message`
- **Configurable Verbosity**: Change the log level at runtime to control verbosity
- **Module-Specific Loggers**: Create loggers for specific modules
- **Easy to Extend**: Add new log levels or output destinations as needed

## Other Utilities

- `apiProxy.ts`: Utilities for proxying API requests
- `openaiStreamUtils.ts`: Utilities for processing streaming responses from OpenAI-compatible APIs (deprecated, use `@media/api` instead)
- `responseUtils.ts`: Utilities for creating standardized API responses
