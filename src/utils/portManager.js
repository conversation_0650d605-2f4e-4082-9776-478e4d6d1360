/**
 * Port Management Utility
 * 
 * Provides centralized port management for the application to ensure
 * components can adapt when server ports change during launch.
 */

/**
 * Simple environment variable getter for JavaScript usage
 * @param {string} key - Environment variable key (without VITE_ prefix)
 * @param {number} defaultValue - Default value if env var not found
 * @param {boolean} logWarning - Whether to log warnings
 * @returns {number} The environment variable as number or default
 */
function getEnvVarAsNumber(key, defaultValue, logWarning = true) {
    const fullKey = key.startsWith('VITE_') ? key : `VITE_${key}`;

    // Check if we're in Node.js environment
    const isNode = typeof process !== 'undefined' && process.env;

    let envValue;
    if (isNode) {
        envValue = process.env[fullKey];
    } else if (typeof import.meta !== 'undefined' && import.meta.env) {
        envValue = import.meta.env[fullKey];
    }

    if (envValue) {
        const numValue = Number(envValue);
        if (!isNaN(numValue)) {
            return numValue;
        }
    }

    if (logWarning && !envValue) {
        console.warn(`[PortManager] Using default value for ${fullKey}: ${defaultValue}`);
    }

    return defaultValue;
}

// Config object using centralized environment variable handling
const config = {
    downloadConfig: {
        port: getEnvVarAsNumber('DOWNLOAD_SERVER_PORT', 2994, false),
        actualPort: null
    },
    host: 'localhost'
};

/**
 * Get the current download server port with fallback hierarchy
 * @returns {number} The port to use for download server requests
 */
export function getDownloadServerPort() {
    // Priority: actual running port > configured port > environment > default
    return config.downloadConfig?.actualPort ||
        config.downloadConfig?.port ||
        getEnvVarAsNumber('DOWNLOAD_SERVER_PORT', 2994, false) ||
        getEnvVarAsNumber('DEFAULT_SERVER_PORT', 2994, false) ||
        2994;
}

/**
 * Get the download server base URL
 * @returns {string} The complete base URL for the download server
 */
export function getDownloadServerUrl() {
    const port = getDownloadServerPort();
    const host = config.host || 'localhost';
    return `http://${host}:${port}`;
}

/**
 * Build a complete URL for a download server endpoint
 * @param {string} path - The endpoint path (should start with /)
 * @returns {string} The complete URL
 */
export function buildDownloadServerUrl(path) {
    const baseUrl = getDownloadServerUrl();
    const cleanPath = path.startsWith('/') ? path : `/${path}`;
    return `${baseUrl}${cleanPath}`;
}

/**
 * Check if the download server is available
 * @returns {Promise<boolean>} True if server responds, false otherwise
 */
export async function isDownloadServerAvailable() {
    try {
        const response = await fetch(`${getDownloadServerUrl()}/proxy-status`, {
            method: 'GET',
            timeout: 5000 // 5 second timeout
        });
        return response.ok;
    } catch (error) {
        console.warn('[PortManager] Download server not available:', error.message);
        return false;
    }
}

/**
 * Wait for the download server to become available
 * @param {number} maxAttempts - Maximum number of attempts
 * @param {number} delay - Delay between attempts in milliseconds
 * @returns {Promise<boolean>} True if server becomes available, false if timeout
 */
export async function waitForDownloadServer(maxAttempts = 10, delay = 1000) {
    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
        console.log(`[PortManager] Checking download server availability (attempt ${attempt}/${maxAttempts})`);

        if (await isDownloadServerAvailable()) {
            console.log('[PortManager] Download server is available');
            return true;
        }

        if (attempt < maxAttempts) {
            console.log(`[PortManager] Server not ready, waiting ${delay}ms...`);
            await new Promise(resolve => setTimeout(resolve, delay));
        }
    }

    console.error('[PortManager] Download server did not become available within timeout');
    return false;
}

/**
 * Register a callback to be notified when the server port changes
 * This is useful for components that cache server URLs
 * @param {Function} callback - Function to call when port changes
 */
const portChangeCallbacks = new Set();

export function onPortChange(callback) {
    portChangeCallbacks.add(callback);
    return () => portChangeCallbacks.delete(callback); // Return unsubscribe function
}

/**
 * Notify all registered callbacks that the port has changed
 * This should be called by the server when it updates the actualPort
 * @param {number} newPort - The new port number
 */
export function notifyPortChange(newPort) {
    console.log(`[PortManager] Port changed to ${newPort}, notifying ${portChangeCallbacks.size} listeners`);
    portChangeCallbacks.forEach(callback => {
        try {
            callback(newPort);
        } catch (error) {
            console.error('[PortManager] Error in port change callback:', error);
        }
    });
}

/**
 * Update the actual port in config and notify listeners
 * This should be called by the server when it determines the final port
 * @param {number} port - The actual port being used
 */
export function updateActualPort(port) {
    if (config.downloadConfig) {
        config.downloadConfig.actualPort = port;
        notifyPortChange(port);
    }
}
