export class PerformanceMonitor {
    constructor() {
        this.fps = 0;
        this.frames = 0;
        this.lastTime = performance.now();
        this.frameHistory = new Array(60).fill(16.67); // Initialize with 60fps values
    }

    update() {
        const now = performance.now();
        const delta = now - this.lastTime;

        this.frames++;

        if (delta >= 1000) {
            this.fps = (this.frames * 1000) / delta;
            this.frames = 0;
            this.lastTime = now;

            // Update frame history
            this.frameHistory.shift();
            this.frameHistory.push(1000 / this.fps);
        }

        return {
            fps: this.fps,
            frameTime: this.frameHistory[this.frameHistory.length - 1],
            averageFrameTime: this.frameHistory.reduce((a, b) => a + b) / this.frameHistory.length
        };
    }
} 