/**
 * Utility for building WebAssembly files from ONNX models
 * This utility manages the process of building WASM files for sherpa-onnx
 */

import { spawn } from 'child_process';
import fs from 'fs';
import path from 'path';
import { WASM_BUILD_CONFIG } from '../config/wasm-build.js';

class WasmBuilder {
    constructor(options = {}) {
        this.config = {
            ...WASM_BUILD_CONFIG,
            ...options
        };

        this.buildInProgress = false;
        this.buildQueue = [];
        this.listeners = [];
    }

    /**
     * Check if WASM files exist for a specific model
     * @param {string} modelType - Type of model to check
     * @returns {boolean} - Whether WASM files exist
     */
    checkWasmFilesExist(modelType = this.config.build.defaultModel) {
        const modelConfig = this.config.models[modelType];
        if (!modelConfig) {
            console.error(`[WasmBuilder] Unknown model type: ${modelType}`);
            return false;
        }

        const wasmOutputDir = modelConfig.wasmOutputDir;
        const wasmJsPath = path.join(wasmOutputDir, 'sherpa-onnx-asr.js');
        const wasmBinaryPath = path.join(wasmOutputDir, 'sherpa-onnx-asr.wasm');

        return fs.existsSync(wasmJsPath) && fs.existsSync(wasmBinaryPath);
    }

    /**
     * Check if required dependencies are installed
     * @returns {Promise<boolean>} - Whether all dependencies are installed
     */
    async checkDependencies() {
        try {
            // Check for CMake
            await new Promise((resolve, reject) => {
                const cmakeProcess = spawn('cmake', ['--version'], {
                    stdio: 'pipe'
                });

                cmakeProcess.on('close', (code) => {
                    if (code === 0) {
                        resolve();
                    } else {
                        reject(new Error('CMake is not installed or not in PATH'));
                    }
                });

                cmakeProcess.on('error', () => {
                    reject(new Error('CMake is not installed or not in PATH'));
                });
            });

            return true;
        } catch (error) {
            console.error(`[WasmBuilder] Dependency check failed: ${error.message}`);
            this._notifyListeners('dependencyError', { error: error.message });
            return false;
        }
    }

    /**
     * Build WASM files for a specific model
     * @param {string} modelType - Type of model to build
     * @param {boolean} force - Whether to force rebuild even if files exist
     * @returns {Promise<boolean>} - Whether build was successful
     */
    async buildWasm(modelType = this.config.build.defaultModel, force = false) {
        // Check if build is already in progress
        if (this.buildInProgress) {
            console.log(`[WasmBuilder] Build already in progress, queueing ${modelType}`);
            return new Promise((resolve, reject) => {
                this.buildQueue.push({ modelType, force, resolve, reject });
            });
        }

        // Check if WASM files already exist
        if (!force && this.checkWasmFilesExist(modelType)) {
            console.log(`[WasmBuilder] WASM files for ${modelType} already exist`);
            return true;
        }

        // Check for required dependencies
        const dependenciesInstalled = await this.checkDependencies();
        if (!dependenciesInstalled) {
            const errorMessage = 'Required dependencies are not installed. Please install CMake before continuing.';
            console.error(`[WasmBuilder] ${errorMessage}`);
            throw new Error(errorMessage);
        }

        this.buildInProgress = true;
        this._notifyListeners('buildStart', { modelType });

        return new Promise((resolve, reject) => {
            console.log(`[WasmBuilder] Starting build for ${modelType}`);

            // Prepare build command
            const buildScript = this.config.build.buildScript;
            const args = [
                '--model', modelType
            ];

            if (force) {
                args.push('--force');
            }

            // Create temp directory if it doesn't exist
            const tempDir = this.config.build.tempDir;
            if (!fs.existsSync(tempDir)) {
                console.log(`[WasmBuilder] Creating temp directory: ${tempDir}`);
                fs.mkdirSync(tempDir, { recursive: true });
            }

            // Spawn build process
            console.log(`[WasmBuilder] Running command: bash ${buildScript} ${args.join(' ')}`);
            console.log(`[WasmBuilder] Build files will be kept in: ${tempDir}/${modelType}`);

            const buildProcess = spawn('bash', [buildScript, ...args], {
                stdio: 'pipe',
                cwd: process.cwd() // Ensure we're using the correct working directory
            });

            let stdout = '';
            let stderr = '';

            buildProcess.stdout.on('data', (data) => {
                const output = data.toString();
                stdout += output;
                this._notifyListeners('buildOutput', { modelType, output });
                console.log(`[WasmBuilder] ${output}`);
            });

            buildProcess.stderr.on('data', (data) => {
                const output = data.toString();
                stderr += output;
                this._notifyListeners('buildError', { modelType, output });
                console.error(`[WasmBuilder] ${output}`);
            });

            // Set timeout for build process
            const timeout = setTimeout(() => {
                buildProcess.kill();
                this.buildInProgress = false;
                this._notifyListeners('buildTimeout', { modelType });
                reject(new Error(`Build process timed out after ${this.config.build.buildTimeout / 60000} minutes`));
                this._processQueue();
            }, this.config.build.buildTimeout);

            buildProcess.on('close', (code) => {
                clearTimeout(timeout);
                this.buildInProgress = false;

                if (code === 0) {
                    console.log(`[WasmBuilder] Build completed successfully for ${modelType}`);
                    this._notifyListeners('buildComplete', { modelType, success: true });
                    resolve(true);
                } else {
                    console.error(`[WasmBuilder] Build failed for ${modelType} with code ${code}`);
                    this._notifyListeners('buildComplete', {
                        modelType,
                        success: false,
                        error: stderr || 'Unknown error'
                    });
                    reject(new Error(`Build process exited with code ${code}: ${stderr}`));
                }

                this._processQueue();
            });

            buildProcess.on('error', (error) => {
                clearTimeout(timeout);
                this.buildInProgress = false;
                console.error(`[WasmBuilder] Failed to start build process: ${error.message}`);
                this._notifyListeners('buildComplete', {
                    modelType,
                    success: false,
                    error: error.message
                });
                reject(error);
                this._processQueue();
            });
        });
    }

    /**
     * Process the build queue
     * @private
     */
    _processQueue() {
        if (this.buildQueue.length > 0 && !this.buildInProgress) {
            const nextBuild = this.buildQueue.shift();
            this.buildWasm(nextBuild.modelType, nextBuild.force)
                .then(nextBuild.resolve)
                .catch(nextBuild.reject);
        }
    }

    /**
     * Add a listener for build events
     * @param {Function} listener - Listener function
     */
    addListener(listener) {
        if (typeof listener === 'function' && !this.listeners.includes(listener)) {
            this.listeners.push(listener);
        }
    }

    /**
     * Remove a listener for build events
     * @param {Function} listener - Listener function to remove
     */
    removeListener(listener) {
        const index = this.listeners.indexOf(listener);
        if (index !== -1) {
            this.listeners.splice(index, 1);
        }
    }

    /**
     * Notify all listeners of a build event
     * @param {string} event - Event name
     * @param {Object} data - Event data
     * @private
     */
    _notifyListeners(event, data) {
        for (const listener of this.listeners) {
            try {
                listener(event, data);
            } catch (error) {
                console.error(`[WasmBuilder] Error in listener: ${error.message}`);
            }
        }
    }
}

// Export singleton instance
export const wasmBuilder = new WasmBuilder();

export default wasmBuilder;
