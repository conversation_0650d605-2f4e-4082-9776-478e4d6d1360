import * as THREE from 'three'
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader'
import { DRACOLoader } from 'three/examples/jsm/loaders/DRACOLoader'
import { RGBELoader } from 'three/examples/jsm/loaders/RGBELoader'
import { FBXLoader } from 'three/examples/jsm/loaders/FBXLoader'
import { CONFIG } from '../../app/config.js'
import { config as serverConfig } from '@/config/client'
import { getCachedModel, cacheModel } from './cache'

/**
 * Asset loader class that handles loading of various 3D assets
 * with automatic Draco compression support through GLTFLoader
 */
export class AssetLoader {
    constructor() {
        // Initialize loaders
        this.gltfLoader = new GLTFLoader();
        this.fbxLoader = new FBXLoader();
        // this.textureLoader = new THREE.TextureLoader();
        this.rgbeLoader = new RGBELoader();

        // Setup Draco decoder for GLTF compression if enabled
        if (CONFIG.DRACO.COMPRESSION.AUTO_CONVERT) {
            this.dracoLoader = new DRACOLoader();
            this.dracoLoader.setDecoderPath(CONFIG.DRACO.DRACO_URL);
            this.gltfLoader.setDRACOLoader(this.dracoLoader);
        }

        // Asset cache for three.js objects
        this.threeCache = new Map();

        // Initialize seed management
        this.currentSeed = serverConfig.defaultSeed;
        this.shouldRegenerate = false;
    }

    /**
     * Set current seed and regeneration flag
     * @param {number} seed - Seed value to set
     * @param {boolean} shouldRegenerate - Whether to regenerate with new seed
     */
    setSeed(seed, shouldRegenerate = false) {
        this.currentSeed = shouldRegenerate ? Math.floor(Math.random() * 1000) : seed;
        this.shouldRegenerate = shouldRegenerate;
        console.log(`[AssetLoader] Set seed: ${this.currentSeed}, regenerate: ${shouldRegenerate}`);
    }

    /**
     * Get current seed value
     * @returns {number} Current seed value
     */
    getCurrentSeed() {
        return this.currentSeed;
    }

    /**
     * Get a cached model or load it from storage
     * @param {string} source - Source type (text, image, doll)
     * @param {any} input - Input data
     * @returns {Promise<{meshResult: any, imageResult: any, videoResult: any} | null>}
     */
    async getCachedModel(source, input) {
        try {
            // Check cache first (with current seed parameter)
            const cachedResult = await getCachedModel(source, input, this.currentSeed);

            if (cachedResult && !this.shouldRegenerate) {
                console.log('[AssetLoader] Using cached model');
                return cachedResult;
            }

            return null;
        } catch (error) {
            console.error('[AssetLoader] Error getting cached model:', error);
            return null;
        }
    }

    /**
     * Cache a model and its associated assets
     * @param {string} source - Source type
     * @param {any} input - Input data
     * @param {{meshResult: any, imageResult: any, videoResult: any}} data - Model data
     */
    async cacheModel(source, input, data) {
        await cacheModel(source, input, data, this.currentSeed);
    }

    /**
     * Load a mesh with automatic format detection (GLTF/GLB/FBX)
     * @param {string} path - Path to the mesh file
     * @param {boolean} forceReload - Whether to force reload from disk instead of using cache
     * @param {string} cache_key - Optional cache key, defaults to path if not provided
     * @returns {Promise<{mesh: THREE.Object3D, animations: THREE.AnimationClip[]}>}
     */
    async loadMesh(path, forceReload = false, cache_key = null) {
        if (!cache_key) {
            cache_key = path;
        }
        // Check memory cache unless force reload is requested
        if (!forceReload && this.threeCache.has(cache_key)) {
            const cached = this.threeCache.get(cache_key);
            return {
                mesh: cached.mesh ? cached.mesh.clone() : cached.clone(),
                animations: cached.animations || []
            };
        }

        try {
            // If force reload and exists in cache, remove it
            if (forceReload && this.threeCache.has(cache_key)) {
                this.threeCache.delete(cache_key);
            }

            // Determine file type from extension
            const extension = path.split('.').pop().toLowerCase();
            let result;

            if (extension === 'fbx') {
                // Load FBX model
                const fbx = await new Promise((resolve, reject) => {
                    this.fbxLoader.load(
                        path,
                        (object) => resolve(object),
                        undefined,
                        reject
                    );
                });

                this.processModel(fbx);
                result = {
                    mesh: fbx,
                    animations: fbx.animations || []
                };

                // Cache the model and animations
                this.threeCache.set(cache_key, {
                    mesh: fbx.clone(),
                    animations: fbx.animations || []
                });
            } else {
                // Load GLTF/GLB model
                const gltf = await new Promise((resolve, reject) => {
                    this.gltfLoader.load(
                        path,
                        (gltf) => {
                            this.processModel(gltf.scene);
                            resolve(gltf);
                        },
                        undefined,
                        reject
                    );
                });

                result = {
                    mesh: gltf.scene,
                    animations: gltf.animations || []
                };

                // Cache the model and animations
                this.threeCache.set(cache_key, {
                    mesh: gltf.scene.clone(),
                    animations: gltf.animations || []
                });
            }

            return result;

        } catch (error) {
            console.error(`Error loading mesh from ${path}:`, error);
            throw error;
        }
    }

    /**
     * Process a loaded model with common settings
     * @param {THREE.Object3D} model - The model to process
     * @param {Object} [options={}] - Additional processing options
     * @param {boolean} [options.castShadow=true] - Whether meshes should cast shadows
     * @param {boolean} [options.receiveShadow=true] - Whether meshes should receive shadows
     */
    processModel(model, options = {}) {
        const {
            castShadow = true,
            receiveShadow = true
        } = options;

        // Calculate the bounding box
        const boundingBox = new THREE.Box3().setFromObject(model);
        const size = new THREE.Vector3();
        boundingBox.getSize(size);

        // Get the largest dimension
        const maxDim = Math.max(size.x, size.y, size.z);

        // Target size (adjust this value to change the normalized size)
        const targetSize = 2.0;

        // Calculate scale factor to normalize to target size
        const scale = targetSize / maxDim;

        // Apply the scale uniformly to maintain proportions
        model.scale.multiplyScalar(scale);

        // Center the object
        const center = new THREE.Vector3();
        boundingBox.getCenter(center);
        model.position.sub(center.multiplyScalar(scale));

        // Apply standard mesh settings
        let meshCount = 0;
        model.traverse((child) => {
            if (child.isMesh || child.type === 'Mesh' || child.type === 'SkinnedMesh') {
                meshCount++;
                child.castShadow = castShadow;
                child.receiveShadow = receiveShadow;

                // Ensure materials are set up for shadows
                if (child.material) {
                    if (Array.isArray(child.material)) {
                        child.material.forEach(material => {
                            material.needsUpdate = true;
                        });
                    } else {
                        child.material.needsUpdate = true;
                    }
                }
            }
        });

        // Position the model slightly above the floor to avoid z-fighting
        if (model.position.y === 0) {
            model.position.y = 0.01;
        }

        console.log(`[AssetLoader] Processed model with ${meshCount} meshes (castShadow: ${castShadow}, receiveShadow: ${receiveShadow})`);
    }

    /**
     * Loads an HDR texture for environment mapping
     * @param {string} path - Path to the HDR file
     * @param {boolean} forceReload - Whether to force reload from disk instead of using cache
     */
    async loadHDR(path, forceReload = false, cache_key = null) {
        if (!cache_key) {
            cache_key = path;
        }
        // Check memory cache unless force reload is requested
        if (!forceReload && this.threeCache.has(cache_key)) {
            return this.threeCache.get(cache_key);
        }

        try {
            // If force reload and exists in cache, remove it
            if (forceReload && this.threeCache.has(cache_key)) {
                this.threeCache.delete(cache_key);
            }

            const texture = await this.rgbeLoader.loadAsync(path);
            texture.mapping = THREE.EquirectangularReflectionMapping;

            // Cache the texture
            this.threeCache.set(cache_key, texture);
            return texture;
        } catch (error) {
            console.error('Failed to load HDR:', error);
            throw error;
        }
    }

    /**
     * Loads a regular texture
     * @param {string} path - Path to the texture file
     * @param {boolean} forceReload - Whether to force reload from disk instead of using cache
     */
    async loadTexture(path, forceReload = false, cache_key = null) {
        if (!cache_key) {
            cache_key = path;
        }
        // Check memory cache unless force reload is requested
        if (!forceReload && this.threeCache.has(cache_key)) {
            return this.threeCache.get(cache_key);
        }

        try {
            // If force reload and exists in cache, remove it
            if (forceReload && this.threeCache.has(cache_key)) {
                this.threeCache.delete(cache_key);
            }

            const texture = await new Promise((resolve, reject) => {
                this.textureLoader.load(path, resolve, undefined, reject);
            });
            this.threeCache.set(cache_key, texture);
            return texture;
        } catch (error) {
            console.error(`Error loading texture from ${path}:`, error);
            throw error;
        }
    }

    async createColoredEnvMap(color = '#FFFFFF') {
        // Create a larger texture for better quality
        const width = 256;
        const height = 128;
        const size = width * height;

        // Convert hex color to RGB
        const threeColor = new THREE.Color(color);
        const r = Math.floor(threeColor.r * 255);
        const g = Math.floor(threeColor.g * 255);
        const b = Math.floor(threeColor.b * 255);

        // Create data with a gradient to simulate lighting
        const data = new Uint8Array(size * 4); // Using RGBA format
        for (let y = 0; y < height; y++) {
            for (let x = 0; x < width; x++) {
                const i = (y * width + x) * 4;

                // Calculate gradient factors based on position
                const yFactor = Math.sin((y / height) * Math.PI); // Creates a top-down gradient
                const xFactor = 0.5 + 0.5 * Math.cos((x / width) * 2 * Math.PI); // Creates a left-right gradient
                const lightFactor = Math.max(0.5, yFactor * xFactor); // Combine gradients, minimum 0.5 to maintain some ambient light

                // Apply the gradient to the base color
                data[i] = Math.floor(r * lightFactor);     // R
                data[i + 1] = Math.floor(g * lightFactor); // G
                data[i + 2] = Math.floor(b * lightFactor); // B
                data[i + 3] = 255; // A (fully opaque)
            }
        }

        const texture = new THREE.DataTexture(
            data,
            width,
            height,
            THREE.RGBAFormat
        );

        texture.needsUpdate = true;
        texture.mapping = THREE.EquirectangularReflectionMapping;
        texture.encoding = THREE.sRGBEncoding;
        texture.minFilter = THREE.LinearFilter;
        texture.magFilter = THREE.LinearFilter;
        texture.generateMipmaps = true;

        return texture;
    }

    /**
     * Cleans up a specific mesh from the cache
     * @param {string} cacheKey - The cache key of the mesh to remove
     */
    cleanupMesh(cacheKey) {
        if (this.threeCache.has(cacheKey)) {
            const resource = this.threeCache.get(cacheKey);

            // Handle both direct mesh objects and {mesh, animations} objects
            if (resource.mesh) {
                this.disposeMeshResources(resource.mesh);
            } else if (resource.isObject3D) {
                this.disposeMeshResources(resource);
            }

            this.threeCache.delete(cacheKey);
            console.log(`[AssetLoader] Cleaned up mesh: ${cacheKey}`);
        }
    }

    /**
     * Helper method to dispose of mesh resources
     * @private
     * @param {THREE.Object3D} object - The 3D object to dispose
     */
    disposeMeshResources(object) {
        if (!object) return;

        // Recursively traverse the object
        object.traverse((child) => {
            // Dispose geometries
            if (child.geometry) {
                child.geometry.dispose();
            }

            // Dispose materials and their textures
            if (child.material) {
                if (Array.isArray(child.material)) {
                    child.material.forEach(material => this.disposeMaterial(material));
                } else {
                    this.disposeMaterial(child.material);
                }
            }
        });
    }

    /**
     * Helper method to dispose of material resources
     * @private
     * @param {THREE.Material} material - The material to dispose
     */
    disposeMaterial(material) {
        if (!material) return;

        // Dispose textures
        Object.keys(material).forEach(prop => {
            if (material[prop] && material[prop].isTexture) {
                material[prop].dispose();
            }
        });

        // Dispose material itself
        material.dispose();
    }

    /**
     * Cleans up resources and clears the cache
     */
    dispose() {
        if (this.dracoLoader) {
            this.dracoLoader.dispose();
        }
        // Dispose all cached resources
        this.threeCache.forEach((resource) => {
            if (resource.dispose) {
                resource.dispose();
            } else if (resource.mesh) {
                this.disposeMeshResources(resource.mesh);
            } else if (resource.isObject3D) {
                this.disposeMeshResources(resource);
            }
        });
        this.threeCache.clear();
    }
}