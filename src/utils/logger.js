/**
 * Unified logging utility for consistent logging across the application
 * Works in both TypeScript and JavaScript environments
 * 
 * Features:
 * - Global log level control
 * - Per-module log level control
 * - Hierarchical log level resolution (module-specific overrides global)
 * examples:
 * /**
 * Logger Demo - Shows how to use the enhanced logging system
 * Run this to see different logging configurations in action
 *

 *import { createLogger, setLogLevel, LogLevel, setModuleLogLevel, getModuleLogLevels } from './logger.js';
import { applyLoggingConfig, LoggingConfigs } from './loggerConfig.js';

// Create loggers for different modules
const avatarLogger = createLogger('TalkingAvatar');
const animatorLogger = createLogger('BaseAnimator');
const audioLogger = createLogger('AudioProcessor');

console.log('\n=== Logger Demo ===\n');

// Demo 1: Global logging control
console.log('1. Setting global log level to INFO...');
setLogLevel(LogLevel.INFO);

avatarLogger.debug('This debug message should NOT appear');
avatarLogger.info('This info message SHOULD appear');
animatorLogger.warn('This warning SHOULD appear');
audioLogger.error('This error SHOULD appear');

console.log('\n');

// Demo 2: Module-specific logging control
console.log('2. Setting TalkingAvatar to DEBUG level only...');
avatarLogger.setLogLevel(LogLevel.DEBUG);

avatarLogger.debug('This debug message SHOULD appear (module override)');
animatorLogger.debug('This debug message should NOT appear (still using global INFO)');

console.log('\n');

// Demo 3: Using global module configuration
console.log('3. Setting BaseAnimator to ERROR level using global function...');
setModuleLogLevel('BaseAnimator', LogLevel.ERROR);

animatorLogger.info('This info message should NOT appear');
animatorLogger.error('This error message SHOULD appear');

console.log('\n');

// Demo 4: Check current configuration
console.log('4. Current logging configuration:');
console.log('Module overrides:', getModuleLogLevels());

console.log('\n');

// Demo 5: Using predefined configurations
console.log('5. Applying development configuration...');
applyLoggingConfig('development');

avatarLogger.debug('Development mode - all debug messages visible');
animatorLogger.debug('Development mode - all debug messages visible');
audioLogger.debug('Development mode - all debug messages visible');

console.log('\n');

console.log('6. Applying production configuration...');
applyLoggingConfig('production');

avatarLogger.debug('Production mode - debug should be hidden');
avatarLogger.warn('Production mode - warnings still visible');
animatorLogger.info('Production mode - most logs hidden');
audioLogger.error('Production mode - only errors visible');

console.log('\n');

// Demo 6: Individual logger utility methods
console.log('7. Using logger utility methods...');
console.log(`Avatar logger debug enabled: ${avatarLogger.isDebugEnabled()}`);
console.log(`Avatar logger module name: ${avatarLogger.getModuleName()}`);
console.log(`Avatar logger current level: ${avatarLogger.getLogLevel()}`);

// Demo 7: Clear module override
console.log('\n8. Clearing module override for TalkingAvatar...');
avatarLogger.clearLogLevel();
avatarLogger.debug('This should follow global level now');

console.log('\n=== Demo Complete ===\n');

// Show usage examples in console
console.log('Usage Examples:');
console.log('- Set global level: setLogLevel(LogLevel.DEBUG)');
console.log('- Set module level: logger.setLogLevel(LogLevel.INFO)');
console.log('- Apply config: applyLoggingConfig("development")');
console.log('- Check status: window.LoggerConfig.printLoggingStatus()');
 */

// Log levels
export const LogLevel = {
  DEBUG: 0,
  INFO: 1,
  WARN: 2,
  ERROR: 3,
  NONE: 4
};

// Global log level (default for all modules)
let globalLogLevel = LogLevel.INFO;

// Per-module log levels (overrides global level)
const moduleLogLevels = new Map();

/**
 * Set the global log level (affects all modules unless they have specific overrides)
 * @param {number} level - Log level from LogLevel enum
 */
export function setLogLevel(level) {
  globalLogLevel = level;
  console.log(`[Logger] Global log level set to ${getLogLevelName(level)}`);
}

/**
 * Set log level for a specific module
 * @param {string} moduleName - Name of the module
 * @param {number} level - Log level from LogLevel enum
 */
export function setModuleLogLevel(moduleName, level) {
  moduleLogLevels.set(moduleName, level);
  console.log(`[Logger] Module '${moduleName}' log level set to ${getLogLevelName(level)}`);
}

/**
 * Clear log level override for a specific module (falls back to global level)
 * @param {string} moduleName - Name of the module
 */
export function clearModuleLogLevel(moduleName) {
  const wasSet = moduleLogLevels.delete(moduleName);
  if (wasSet) {
    console.log(`[Logger] Module '${moduleName}' log level cleared, using global level (${getLogLevelName(globalLogLevel)})`);
  }
}

/**
 * Get the effective log level for a module (module-specific or global)
 * @param {string} moduleName - Name of the module
 * @returns {number} - Effective log level
 */
export function getEffectiveLogLevel(moduleName) {
  return moduleLogLevels.get(moduleName) ?? globalLogLevel;
}

/**
 * Get the global log level
 * @returns {number} - Current global log level
 */
export function getLogLevel() {
  return globalLogLevel;
}

/**
 * Get log level name for display purposes
 * @param {number} level - Log level number
 * @returns {string} - Log level name
 */
function getLogLevelName(level) {
  const levelNames = Object.keys(LogLevel);
  return levelNames.find(name => LogLevel[name] === level) || 'UNKNOWN';
}

/**
 * List all module-specific log level overrides
 * @returns {Object} - Object with module names as keys and log levels as values
 */
export function getModuleLogLevels() {
  const result = {};
  for (const [module, level] of moduleLogLevels.entries()) {
    result[module] = {
      level,
      levelName: getLogLevelName(level)
    };
  }
  return result;
}

/**
 * Format a log message with a module name
 * @param {string} module - Module name
 * @param {string} message - Log message
 * @returns {string} - Formatted message
 */
function formatLogMessage(module, message) {
  return `[${module}] ${message}`;
}

/**
 * Log a debug message
 * @param {string} module - Module name
 * @param {string} message - Log message
 * @param {...any} args - Additional arguments
 */
export function debug(module, message, ...args) {
  if (getEffectiveLogLevel(module) <= LogLevel.DEBUG) {
    console.debug(formatLogMessage(module, message), ...args);
  }
}

/**
 * Log an info message
 * @param {string} module - Module name
 * @param {string} message - Log message
 * @param {...any} args - Additional arguments
 */
export function info(module, message, ...args) {
  if (getEffectiveLogLevel(module) <= LogLevel.INFO) {
    console.log(formatLogMessage(module, message), ...args);
  }
}

/**
 * Log a warning message
 * @param {string} module - Module name
 * @param {string} message - Log message
 * @param {...any} args - Additional arguments
 */
export function warn(module, message, ...args) {
  if (getEffectiveLogLevel(module) <= LogLevel.WARN) {
    console.warn(formatLogMessage(module, message), ...args);
  }
}

/**
 * Log an error message
 * @param {string} module - Module name
 * @param {string} message - Log message
 * @param {...any} args - Additional arguments
 */
export function error(module, message, ...args) {
  if (getEffectiveLogLevel(module) <= LogLevel.ERROR) {
    console.error(formatLogMessage(module, message), ...args);
  }
}

/**
 * Create a logger for a specific module
 * @param {string} module - Module name
 * @returns {Object} - Logger object with debug, info, warn, and error methods, plus level control
 */
export function createLogger(module) {
  const logger = {
    // Logging methods
    debug: (message, ...args) => debug(module, message, ...args),
    info: (message, ...args) => info(module, message, ...args),
    warn: (message, ...args) => warn(module, message, ...args),
    error: (message, ...args) => error(module, message, ...args),

    // Module-specific log level control
    setLogLevel: (level) => setModuleLogLevel(module, level),
    clearLogLevel: () => clearModuleLogLevel(module),
    getLogLevel: () => getEffectiveLogLevel(module),

    // Utility methods
    getModuleName: () => module,
    isDebugEnabled: () => getEffectiveLogLevel(module) <= LogLevel.DEBUG,
    isInfoEnabled: () => getEffectiveLogLevel(module) <= LogLevel.INFO,
    isWarnEnabled: () => getEffectiveLogLevel(module) <= LogLevel.WARN,
    isErrorEnabled: () => getEffectiveLogLevel(module) <= LogLevel.ERROR
  };

  return logger;
}
