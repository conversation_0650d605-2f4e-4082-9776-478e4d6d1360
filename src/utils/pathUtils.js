/**
 * Utility functions for handling paths with special characters (like Chinese)
 */

/**
 * Safely decode a URL-encoded path, especially for Chinese characters
 * @param {string} encodedPath - The potentially URL-encoded path
 * @returns {string} The decoded path or the original path if decoding fails
 */
export function safeDecodeURIComponent(encodedPath) {
  if (!encodedPath) return encodedPath;

  try {
    // Check if the path is actually encoded
    const isEncoded = encodedPath !== decodeURIComponent(encodedPath);

    if (isEncoded) {
      const decodedPath = decodeURIComponent(encodedPath);
      console.log(`[PathUtils] Decoded path: "${encodedPath}" -> "${decodedPath}"`);
      return decodedPath;
    } else {
      // Path is not encoded, return as-is
      console.log(`[PathUtils] Path not encoded, using as-is: "${encodedPath}"`);
      return encodedPath;
    }
  } catch (error) {
    console.warn(`[PathUtils] Failed to decode path, using as-is: "${encodedPath}", Error: ${error.message}`);
    return encodedPath;
  }
}

/**
 * Safely encode a path for use in URLs, especially for Chinese characters
 * @param {string} path - The path to encode
 * @returns {string} The encoded path
 */
export function safeEncodeURIComponent(path) {
  if (!path) return path;

  try {
    // Check if the path needs encoding (contains special characters)
    const needsEncoding = /[^a-zA-Z0-9\-_.~/]/.test(path);

    if (needsEncoding) {
      const encodedPath = encodeURIComponent(path);
      console.log(`[PathUtils] Encoded path: "${path}" -> "${encodedPath}"`);
      return encodedPath;
    } else {
      // Path doesn't need encoding, return as-is
      console.log(`[PathUtils] Path doesn't need encoding, using as-is: "${path}"`);
      return path;
    }
  } catch (error) {
    console.warn(`[PathUtils] Failed to encode path, using as-is: "${path}", Error: ${error.message}`);
    return path;
  }
}

/**
 * Validate if a path contains only allowed characters
 * @param {string} path - The path to validate
 * @returns {boolean} True if the path is valid, false otherwise
 */
export function isValidPath(path) {
  if (!path) return false;

  // More permissive regex that allows:
  // - Letters, numbers, underscores, forward slashes, hyphens
  // - Chinese characters (CJK Unified Ideographs: \u4e00-\u9fff)
  // - Japanese characters (Hiragana: \u3040-\u309F, Katakana: \u30A0-\u30FF)
  // - Korean characters (Hangul: \uAC00-\uD7AF)
  // - Periods (for file extensions)
  // - Spaces (will be encoded in URLs)
  const regex = /^[a-zA-Z0-9_/\u4e00-\u9fff\u3040-\u309F\u30A0-\u30FF\uAC00-\uD7AF\-\. ]+$/;
  const isValid = regex.test(path);

  console.log(`[PathUtils] Path validation: "${path}" -> ${isValid ? 'valid' : 'invalid'}`);

  return isValid;
}

/**
 * Ensure a path doesn't escape the public directory
 * @param {string} fullPath - The full path to check
 * @param {string} basePath - The base path that should be the prefix
 * @returns {boolean} True if the path is safe, false otherwise
 */
export function isPathSafe(fullPath, basePath) {
  if (!fullPath || !basePath) {
    console.warn(`[PathUtils] Path safety check failed: Missing parameters - fullPath: ${fullPath}, basePath: ${basePath}`);
    return false;
  }

  try {
    // Simple path normalization that works in both Node.js and browser environments
    // Replace backslashes with forward slashes for consistency
    const normalizeSimple = (p) => {
      // Replace backslashes with forward slashes
      let normalized = p.replace(/\\/g, '/');

      // Remove duplicate slashes
      normalized = normalized.replace(/\/+/g, '/');

      // Resolve .. and . segments
      const segments = normalized.split('/');
      const result = [];

      for (const segment of segments) {
        if (segment === '..') {
          result.pop();
        } else if (segment !== '.' && segment !== '') {
          result.push(segment);
        }
      }

      // Preserve leading slash if present in original path
      const prefix = p.startsWith('/') ? '/' : '';

      // Preserve trailing slash if present in original path
      const suffix = p.endsWith('/') ? '/' : '';

      return prefix + result.join('/') + suffix;
    };

    const normalizedPath = normalizeSimple(fullPath);
    const normalizedBasePath = normalizeSimple(basePath);

    const isSafe = normalizedPath.startsWith(normalizedBasePath);

    if (isSafe) {
      console.log(`[PathUtils] Path safety check passed: "${fullPath}" is within "${basePath}"`);
    } else {
      console.warn(`[PathUtils] Path safety check failed: "${fullPath}" is outside "${basePath}"`);
      console.warn(`[PathUtils] Normalized path: "${normalizedPath}"`);
    }

    return isSafe;
  } catch (error) {
    console.error(`[PathUtils] Error in path safety check: ${error.message}`);
    return false;
  }
}

/**
 * Normalize a path with Chinese characters for file system operations
 * This function handles both URL encoding/decoding and path normalization
 * @param {string} inputPath - The path to normalize
 * @param {boolean} forUrl - Whether the path is for URL use (default: false)
 * @returns {string} The normalized path
 */
export function normalizeChinesePath(inputPath, forUrl = false) {
  if (!inputPath) return inputPath;

  try {
    // First decode if it might be encoded
    let decodedPath = safeDecodeURIComponent(inputPath);

    // Remove leading slash if present
    if (decodedPath.startsWith('/')) {
      decodedPath = decodedPath.substring(1);
    }

    // Replace backslashes with forward slashes for consistency
    let normalizedPath = decodedPath.replace(/\\/g, '/');

    // If this is for URL use, encode it
    if (forUrl) {
      normalizedPath = safeEncodeURIComponent(normalizedPath);
    }

    console.log(`[PathUtils] Normalized path: "${inputPath}" -> "${normalizedPath}" (for ${forUrl ? 'URL' : 'filesystem'})`);
    return normalizedPath;
  } catch (error) {
    console.warn(`[PathUtils] Error normalizing path: ${error.message}, using original: "${inputPath}"`);
    return inputPath;
  }
}
