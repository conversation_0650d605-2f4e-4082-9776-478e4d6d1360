[build-system]
requires = ["setuptools>=65.5.3"]
build-backend = "setuptools.build_meta"

[project]
name = "seva"
version = "0.0.0"
requires-python = ">=3.10"
dependencies = [
    "torch",
    "roma",
    "viser",
    "tyro",
    "fire",
    "ninja",
    "gradio==5.17.0",
    "einops",
    "colorama",
    "splines",
    "kornia",
    "open-clip-torch",
    "diffusers",
    "numpy==1.24.4",
    "imageio[ffmpeg]",
    "huggingface-hub",
    "opencv-python",
    "aiohttp"
]

[project.optional-dependencies]
dev = ["ruff", "ipdb", "pytest", "line_profiler", "pre-commit"]

[tool.setuptools.packages.find]
include = ["seva"]

[tool.pyright]
extraPaths = ["third_party/dust3r"]

[tool.ruff]
lint.ignore = ["E741"]
