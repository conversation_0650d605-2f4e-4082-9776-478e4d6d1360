default_language_version:
  python: python3
default_stages: [pre-commit]
repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v5.0.0
    hooks:
      - id: trailing-whitespace
      - id: end-of-file-fixer
  - repo: https://github.com/charliermarsh/ruff-pre-commit
    rev: v0.8.3
    hooks:
      - id: ruff
        types_or: [python, pyi, jupyter]
        args: [--fix, --extend-ignore=E402]
      - id: ruff-format
        types_or: [python, pyi, jupyter]
  - repo: https://github.com/pre-commit/mirrors-prettier
    rev: v3.1.0
    hooks:
      - id: prettier
        types_or: [markdown]
