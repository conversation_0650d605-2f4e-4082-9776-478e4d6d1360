{"permissions": {"allow": ["<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(mv:*)", "<PERSON><PERSON>(python:*)", "Bash(rm:*)", "<PERSON><PERSON>(source:*)", "Bash(conda activate:*)", "Bash(PYTHONPATH=/Volumes/SN580-2T/Users/<USER>/Hologram-Software/src/utils/stable-virtual-camera python -c \"\nfrom traj_tool.quilt_camera import QuiltCameraModel\nfrom traj_tool.lookingglass_config import get_default_config\nimport numpy as np\n\nconfig = get_default_config()\ncamera = QuiltCameraModel(config)\ncenter_pos = np.array([0.0, 0.0, 2.0])\nlook_at = np.array([0.0, 0.0, 0.0])\n\nposes, _ = camera.generate_quilt_trajectory(center_position=center_pos, look_at=look_at)\npositions = poses[:, :3, 3].numpy()\nrotations = poses[:, :3, :3].numpy()\n\nprint(''Camera 0 debug:'')\npos = positions[0]\nrot = rotations[0]\nprint(f''Position: {pos}'')\nprint(f''Rotation matrix:\\n{rot}'')\n\n# Get camera forward direction (Z-axis)\nforward = rot[:, 2]\nprint(f''Forward direction: {forward}'')\n\n# Calculate expected direction\nexpected = look_at - pos\nexpected = expected / np.linalg.norm(expected)\nprint(f''Expected direction: {expected}'')\n\n# Check alignment\ndot_product = np.dot(forward, expected)\nprint(f''Dot product: {dot_product}'')\nangle_error = np.degrees(np.arccos(np.clip(dot_product, -1, 1)))\nprint(f''Angle error: {angle_error}°'')\n\")"], "deny": []}}