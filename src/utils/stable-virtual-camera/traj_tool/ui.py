"""
Trajectory Tool UI Components

This module contains UI-related components and functions for the trajectory tool.

Key features:
- Integrates with SEVA keyframe system for camera trajectory design
- Supports Looking Glass quilt generation from keyframes
- Automatically creates quilt units in a rows×columns grid layout for holographic display
- Follows Looking Glass specifications for proper row-major ordering
- Scales quilt units proportionally when input frames are scaled

Looking Glass Quilt Organization:
- A "quilt" consists of multiple images arranged in a rows×columns grid
- Each "quilt unit" represents a single viewpoint position in 3D space
- For each unit, the system generates rows×columns keyframes in a grid pattern
- Views are ordered in row-major format (left-to-right, bottom-to-top)
- The bottom-left view is view 0, increasing left-to-right, then bottom-to-top
- Each device type has specific grid requirements:
  - Portrait: 8 columns × 6 rows = 48 keyframes per unit
  - Go: 11 columns × 6 rows = 66 keyframes per unit
  - 16"/32" Landscape: 7 columns × 7 rows = 49 keyframes per unit
  - 65": 8 columns × 9 rows = 72 keyframes per unit

Keyframe Management in Different Modes:
- Normal Mode: Each keyframe is an individual camera position
- Quilt Mode: Keyframes are organized into quilt units, where each unit
  generates rows×columns keyframes arranged in a grid for holographic display
"""

import numpy as np
import torch
import time
from typing import Dict, Any, Optional, List, Tuple
import PIL.Image as Image

import viser
import viser.transforms as vt

from .managers import (
    TestObjectManager,
    TrajectoryManager,
    CameraAnimator,
    TrajectoryExporter,
    LookingGlassManager,
    LOOKING_GLASS_AVAILABLE,
    SEVA_AVAILABLE
)
from .lookingglass_config import list_available_devices

try:
    from seva.gui import define_gui, GuiState, Keyframe
except ImportError:
    define_gui = None
    GuiState = None
    Keyframe = None


def setup_scene(server: viser.ViserServer):
    """
    Set up the default scene with grid and coordinate system
    
    Args:
        server: The viser server to set up
    """
    # Add a grid for reference
    server.scene.add_grid(
        "/scene/grid",
        width=10,
        height=10,
        plane="xz"
    )
    
    # Set background color
    server.scene.set_background_image(
        np.array([[[39, 39, 42]]], dtype=np.uint8)
    )
    
    # Set default camera position
    server.scene.set_up_direction(np.array([0.0, 1.0, 0.0]))
    
    print("Scene setup complete")


def create_test_object_ui(test_object_manager: TestObjectManager):
    """
    Create UI controls for test objects
    
    Args:
        test_object_manager: The test object manager instance
    """
    server = test_object_manager.server
    
    with server.gui.add_folder("Test Object", expand_by_default=True, order=100):
        # Test object visibility
        test_object_visible = server.gui.add_checkbox(
            "Show Test Object", True, 
            hint="Display test object in scene"
        )
        
        # Test object type
        test_object_type = server.gui.add_dropdown(
            "Object Type", 
            options=["sphere", "cube", "arrow"],
            initial_value="sphere",
            hint="Type of test object to display"
        )
        
        # Test object position
        test_pos_x = server.gui.add_slider(
            "Position X", initial_value=0.0, min=-5.0, max=5.0, step=0.1
        )
        test_pos_y = server.gui.add_slider(
            "Position Y", initial_value=0.0, min=-5.0, max=5.0, step=0.1
        )
        test_pos_z = server.gui.add_slider(
            "Position Z", initial_value=0.0, min=-5.0, max=5.0, step=0.1
        )
        
        # Auto-center button
        auto_center_button = server.gui.add_button(
            "Auto-Center to Trajectory",
            icon=viser.Icon.TARGET,
            hint="Automatically position test object at trajectory center"
        )
        
        # Event handlers
        def update_test_object():
            position = np.array([test_pos_x.value, test_pos_y.value, test_pos_z.value])
            test_object_manager.update_test_object(
                position, test_object_type.value, test_object_visible.value
            )
        
        # Use separate handlers for each UI element type
        @test_object_visible.on_update
        def on_visibility_update(event):
            update_test_object()
            
        @test_object_type.on_update  
        def on_type_update(event):
            update_test_object()
            
        @test_pos_x.on_update
        @test_pos_y.on_update
        @test_pos_z.on_update
        def on_position_update(event):
            update_test_object()
        
        @auto_center_button.on_click
        def on_auto_center(event):
            # Auto-center test object to trajectory center
            pass
            
    return test_pos_x, test_pos_y, test_pos_z


def create_animation_ui(camera_animator: CameraAnimator):
    """
    Create UI controls for animation
    
    Args:
        camera_animator: The camera animator instance
    """
    server = camera_animator.server
    
    with server.gui.add_folder("Animation", expand_by_default=True, order=101):
        animation_fps = server.gui.add_slider(
            "Animation FPS", initial_value=10.0, min=1.0, max=30.0, step=1.0,
            hint="Animation playback speed"
        )
        
        animation_loop = server.gui.add_checkbox(
            "Loop Animation", True,
            hint="Restart animation when it reaches the end"
        )
        
        play_button = server.gui.add_button(
            "Play Animation",
            icon=viser.Icon.PLAYER_PLAY,
            hint="Start trajectory animation"
        )
        
        stop_button = server.gui.add_button(
            "Stop Animation", 
            icon=viser.Icon.PLAYER_STOP,
            hint="Stop trajectory animation"
        )
        
        # These will be connected to the app's methods
        @play_button.on_click
        def _(_):
            pass
        
        @stop_button.on_click 
        def _(_):
            camera_animator.stop_animation()
            
    return animation_fps, animation_loop, play_button, stop_button


def create_trajectory_ui(trajectory_manager: TrajectoryManager):
    """
    Create UI controls for camera trajectory
    
    Args:
        trajectory_manager: The trajectory manager instance
        
    Returns:
        Tuple of UI elements (add_keyframe_button, clear_keyframes_button, keyframe_status)
    """
    server = trajectory_manager.server
    
    with server.gui.add_folder("Camera Trajectory", expand_by_default=True, order=102):
        # Add keyframe button - supports both regular keyframes and quilt units
        add_keyframe_button = server.gui.add_button(
            "Add Keyframe",
            icon=viser.Icon.PLUS,
            hint="Add camera keyframe (or quilt unit in quilt mode)" 
        )
        
        # Clear keyframes button
        clear_keyframes_button = server.gui.add_button(
            "Clear All Keyframes",
            icon=viser.Icon.TRASH,
            color="red",
            hint="Remove all keyframes/units"
        )
            
        # Status text for keyframes
        keyframe_status = server.gui.add_text(
            "Keyframes", 
            initial_value="No keyframes",
            hint="Number of keyframes/units added"
        )
        
        # Connect add keyframe button to SEVA keyframe system
        @add_keyframe_button.on_click
        def _(event: viser.GuiEvent):
            """Add a keyframe at the current camera position"""
            if event.client is None:
                return
                
            client = event.client
            camera = client.camera
            
            # Check if we're in quilt mode
            is_quilt_mode = hasattr(trajectory_manager, 'trajectory_type') and trajectory_manager.trajectory_type == "quilt"
            
            # Create keyframe data structure
            keyframe = {
                "position": np.array(camera.position),
                "wxyz": np.array(camera.wxyz),
                "fov": camera.fov
            }
            
            # Add to trajectory manager
            trajectory_manager.add_seva_keyframe(keyframe)
            keyframe_count = len(trajectory_manager.seva_keyframes)
            
            if is_quilt_mode:
                # In quilt mode, we need to generate a quilt unit
                # The quilt unit handler will be triggered separately
                
                # Update status with quilt unit count
                unit_count = len(trajectory_manager.quilt_units) if hasattr(trajectory_manager, 'quilt_units') else 0
                keyframe_status.value = f"{keyframe_count} keyframe{'s' if keyframe_count > 1 else ''} ({unit_count + 1} unit{'s' if unit_count + 1 > 1 else ''})"
                
                # Trigger quilt mode visualization
                trajectory_manager.visualize_trajectory(quilt_mode=True)
                print(f"Added keyframe/quilt unit at position {camera.position}")
            else:
                # In normal mode, add a visual indicator for this keyframe
                frustum = server.scene.add_camera_frustum(
                    f"/scene/keyframes/{keyframe_count-1}",
                    fov=camera.fov,
                    aspect=1.0,
                    scale=0.1,
                    color=(200, 10, 30),
                    wxyz=camera.wxyz,
                    position=camera.position
                )
                trajectory_manager.trajectory_nodes.append(frustum)
                
                # Update keyframe status
                keyframe_status.value = f"{keyframe_count} keyframe{'s' if keyframe_count > 1 else ''}"
                print(f"Added keyframe at position {camera.position}")
            
        @clear_keyframes_button.on_click
        def _(event: viser.GuiEvent):
            """Clear all keyframes and quilt units"""
            # Check if we're in quilt mode
            is_quilt_mode = hasattr(trajectory_manager, 'trajectory_type') and trajectory_manager.trajectory_type == "quilt"
            
            # Clear keyframes in trajectory manager
            trajectory_manager.clear_seva_keyframes()
            
            # Clear trajectory visualization
            trajectory_manager.clear_trajectory()
            
            # If in quilt mode, also clear quilt units
            if is_quilt_mode and hasattr(trajectory_manager, 'quilt_units'):
                trajectory_manager.quilt_units = []
                print("Cleared all keyframes and quilt units")
            else:
                print("Cleared all keyframes")
            
            # Update keyframe status
            keyframe_status.value = "No keyframes"
            
    return add_keyframe_button, clear_keyframes_button, keyframe_status


def create_export_ui(exporter: TrajectoryExporter, trajectory_manager: TrajectoryManager):
    """
    Create UI controls for export
    
    Args:
        exporter: The trajectory exporter instance
        trajectory_manager: The trajectory manager instance
    """
    server = trajectory_manager.server
    fastapi_url = "http://localhost:8000"
    
    with server.gui.add_folder("Export for FastAPI", expand_by_default=True, order=103):
        # Export name input
        export_name = server.gui.add_text(
            "Export Name",
            initial_value="my_trajectory",
            hint="Name for the exported trajectory file"
        )
        
        # Preset type dropdown for export
        preset_type_export = server.gui.add_dropdown(
            "Trajectory Type",
            options=["orbit", "spiral", "lemniscate", "zoom-in", "zoom-out", 
                    "dolly zoom-in", "dolly zoom-out", "move-forward", "move-backward",
                    "move-up", "move-down", "move-left", "move-right", "custom"],
            initial_value="orbit",
            hint="Type of trajectory for export metadata"
        )
        
        # FastAPI server URL
        fastapi_url_input = server.gui.add_text(
            "FastAPI Server URL",
            initial_value=fastapi_url,
            hint="URL of the FastAPI server for video inference"
        )
        
        # Export trajectory button
        export_button = server.gui.add_button(
            "Export Trajectory JSON",
            icon=viser.Icon.DOWNLOAD,
            hint="Export current trajectory as JSON file for FastAPI inference"
        )
        
        # Generate curl command button
        curl_button = server.gui.add_button(
            "Generate cURL Command",
            icon=viser.Icon.CODE,
            hint="Generate example cURL command for FastAPI custom trajectory endpoint"
        )
        
        # Copy trajectory to clipboard button
        copy_button = server.gui.add_button(
            "Copy Trajectory Data",
            icon=viser.Icon.COPY,
            hint="Copy trajectory data to clipboard for FastAPI requests"
        )
        
        # Export status text
        export_status = server.gui.add_text(
            "Status", 
            initial_value="Ready to export",
            hint="Export operation status"
        )
        
    return export_name, preset_type_export, fastapi_url_input, export_button, curl_button, copy_button, export_status


def create_lookingglass_ui(lg_manager: LookingGlassManager, trajectory_manager: TrajectoryManager):
    """
    Create simplified UI controls for Looking Glass
    
    Args:
        lg_manager: The Looking Glass manager instance
        trajectory_manager: The trajectory manager instance
        
    Returns:
        The quilt mode checkbox UI element or None if Looking Glass is not available
    """
    if not LOOKING_GLASS_AVAILABLE:
        print("Looking Glass modules not available, skipping UI creation")
        return None
        
    server = lg_manager.server
    
    with server.gui.add_folder("Looking Glass", expand_by_default=True, order=104):
        # Quilt mode checkbox - the primary control for the Viser UI
        quilt_mode_checkbox = server.gui.add_checkbox(
            "Quilt Mode", False,
            hint="Enable quilt generation mode - creates units with rows×columns grid layout following Looking Glass specifications"
        )
        
        # Status text
        lg_status = server.gui.add_text(
            "Status", 
            initial_value="Ready",
            hint="Looking Glass operation status"
        )
        
        # Device type dropdown - expose lookingglass_config.py devices
        device_options = list_available_devices()
        device_type = server.gui.add_dropdown(
            "Device Type",
            options=device_options,
            initial_value="portrait",
            hint="Looking Glass device type"
        )
        
        # Camera parameters from lookingglass_config.py
        with server.gui.add_folder("Camera Parameters", expand_by_default=False):
            viewcone_angle = server.gui.add_slider(
                "View Cone Angle", 
                initial_value=lg_manager.config.camera.viewcone_angle,
                min=10.0, max=60.0, step=1.0,
                hint="Angle between leftmost and rightmost views (degrees)"
            )
            
            depthiness = server.gui.add_slider(
                "Depthiness",
                initial_value=lg_manager.config.camera.depthiness,
                min=0.5, max=3.0, step=0.1,
                hint="Controls perceived depth by adjusting camera separation"
            )
            
            focus_distance = server.gui.add_slider(
                "Focus Distance",
                initial_value=lg_manager.config.camera.focus_distance,
                min=0.5, max=5.0, step=0.1,
                hint="Distance to focal plane"
            )
        
        # Connect device type to config
        @device_type.on_update
        def _(event: viser.GuiEvent):
            device_name = device_type.value
            lg_manager.update_device_config(device_name)
            
            # Update UI elements with new config
            viewcone_angle.value = lg_manager.config.camera.viewcone_angle
            depthiness.value = lg_manager.config.camera.depthiness
            focus_distance.value = lg_manager.config.camera.focus_distance
            
            # Update status with device info
            rows = lg_manager.config.quilt.rows
            cols = lg_manager.config.quilt.columns
            lg_status.value = f"Device set to {lg_manager.config.device_name} ({cols}×{rows} grid)"
            
            # If in quilt mode, regenerate all quilt units with new device config
            if hasattr(trajectory_manager, 'trajectory_type') and trajectory_manager.trajectory_type == "quilt":
                regenerate_quilt_units()
        
        # Connect camera parameters to config
        @viewcone_angle.on_update
        def _(event: viser.GuiEvent):
            lg_manager.config.camera.viewcone_angle = viewcone_angle.value
            if hasattr(trajectory_manager, 'trajectory_type') and trajectory_manager.trajectory_type == "quilt":
                regenerate_quilt_units()
                
        @depthiness.on_update
        def _(event: viser.GuiEvent):
            lg_manager.config.camera.depthiness = depthiness.value
            if hasattr(trajectory_manager, 'trajectory_type') and trajectory_manager.trajectory_type == "quilt":
                regenerate_quilt_units()
                
        @focus_distance.on_update
        def _(event: viser.GuiEvent):
            lg_manager.config.camera.focus_distance = focus_distance.value
            if hasattr(trajectory_manager, 'trajectory_type') and trajectory_manager.trajectory_type == "quilt":
                regenerate_quilt_units()
        
        @quilt_mode_checkbox.on_update
        def _(event: viser.GuiEvent):
            """Toggle between normal camera trajectory and quilt mode"""
            is_quilt_mode = quilt_mode_checkbox.value
            
            # First, clear existing trajectory visualization
            trajectory_manager.clear_trajectory()
            
            # In quilt mode, we'll just reset the scene which will remove input frustums
            if is_quilt_mode:
                # Reset the scene - this will remove all visualizations including input frustums
                server.scene.reset()
                
                # Check if we have keyframes available
                if hasattr(trajectory_manager, 'seva_keyframes') and trajectory_manager.seva_keyframes:
                    lg_status.value = "Generating quilt units from keyframes..."
                    generate_quilt_units_from_keyframes()
                else:
                    # Create a default quilt unit at the current camera position
                    update_quilt_units()
            else:
                # Restore scene state when disabling quilt mode
                server.scene.reset()
                
                # Visualize standard keyframes if they exist
                if hasattr(trajectory_manager, 'seva_keyframes') and trajectory_manager.seva_keyframes:
                    for i, keyframe in enumerate(trajectory_manager.seva_keyframes):
                        # Extract position and orientation
                        position = keyframe.position if hasattr(keyframe, 'position') else keyframe['position']
                        wxyz = keyframe.wxyz if hasattr(keyframe, 'wxyz') else keyframe.get('wxyz', [1, 0, 0, 0])
                        fov = keyframe.fov if hasattr(keyframe, 'fov') else keyframe.get('fov', 45.0)
                        
                        # Create visual indicator for this keyframe
                        frustum = server.scene.add_camera_frustum(
                            f"/scene/keyframes/{i}",
                            fov=fov,
                            aspect=1.0,
                            scale=0.1,
                            color=(200, 10, 30),
                            wxyz=wxyz,
                            position=position
                        )
                        trajectory_manager.trajectory_nodes.append(frustum)
            
            lg_status.value = f"🔄 Quilt mode: {'enabled' if is_quilt_mode else 'disabled'}"
            print(f"🔄 Quilt mode: {'enabled' if is_quilt_mode else 'disabled'}")
        
        def generate_quilt_units_from_keyframes():
            """Generate quilt units from keyframes with proper scaling"""
            # For each keyframe, create a quilt unit (each unit contains rows×columns keyframes)
            unit_count = 0
            trajectory_manager.quilt_units = []  # Reset quilt units
            
            # Use a default scene scale - this will be properly proportioned
            # based on the keyframe positions themselves
            scene_scale = 1.0  # Default value
            
            # Scale quilt units proportionally to input frames
            for keyframe in trajectory_manager.seva_keyframes:
                # Get position from keyframe
                center_position = np.array(keyframe.position if hasattr(keyframe, 'position') else keyframe['position'])
                
                # Use keyframe's orientation if available, otherwise look at origin
                if hasattr(keyframe, 'wxyz'):
                    # Get quaternion components
                    qw, qx, qy, qz = keyframe.wxyz
                    # Create a viser rotation from quaternion
                    rotation = vt.SO3(wxyz=np.array([qw, qx, qy, qz]))
                    # Get forward direction (z-axis)
                    look_direction = rotation.as_matrix()[:, 2]
                    # Use forward direction to compute look_at point
                    look_at = center_position + look_direction
                elif 'wxyz' in keyframe:
                    qw, qx, qy, qz = keyframe['wxyz']
                    rotation = vt.SO3(wxyz=np.array([qw, qx, qy, qz]))
                    look_direction = rotation.as_matrix()[:, 2]
                    look_at = center_position + look_direction
                else:
                    # Default to looking at origin
                    look_at = np.array([0.0, 0.0, 0.0])
                
                # Generate quilt grid around this keyframe using the camera model
                # This follows Looking Glass specifications for camera array generation
                poses, fovs = lg_manager.camera_model.generate_quilt_trajectory(
                    center_position=center_position,
                    look_at=look_at,
                    up_direction=np.array([0.0, 1.0, 0.0])
                )
                
                # Add this unit to the trajectory manager
                trajectory_manager.quilt_units.append({
                    "id": unit_count,
                    "poses": poses,
                    "fovs": fovs,
                    "center": center_position,
                    "look_at": look_at
                })
                unit_count += 1
            
            # Store trajectory type
            trajectory_manager.trajectory_type = "quilt"
            
            # Visualize all quilt units with proper color coding
            trajectory_manager.visualize_trajectory(quilt_mode=True)
            
            # Update status with detailed information about the grid layout
            rows = lg_manager.config.quilt.rows
            cols = lg_manager.config.quilt.columns
            keyframes_per_unit = rows * cols
            total_keyframes = unit_count * keyframes_per_unit
            device_name = lg_manager.config.device_name if hasattr(lg_manager.config, 'device_name') else "Looking Glass"
            lg_status.value = f"✅ Created {unit_count} quilt unit{'s' if unit_count > 1 else ''} for {device_name}, each with {rows}×{cols} grid ({total_keyframes} total keyframes)"
            print(f"Quilt visualization: {unit_count} unit{'s' if unit_count > 1 else ''} with {rows}×{cols} grid for {device_name} device ({total_keyframes} total keyframes)")
        
        def regenerate_quilt_units():
            """Regenerate all quilt units with updated configuration"""
            if not hasattr(trajectory_manager, 'quilt_units') or not trajectory_manager.quilt_units:
                return
                
            lg_status.value = "Regenerating quilt units with new configuration..."
            
            # Store the original units for reference
            original_units = trajectory_manager.quilt_units
            
            # Clear trajectory
            trajectory_manager.clear_trajectory()
            trajectory_manager.quilt_units = []
            
            # Regenerate each unit with updated config
            for i, unit in enumerate(original_units):
                center_position = unit["center"]
                look_at = unit.get("look_at", np.array([0.0, 0.0, 0.0]))
                
                # Generate quilt grid with new config
                poses, fovs = lg_manager.camera_model.generate_quilt_trajectory(
                    center_position=center_position,
                    look_at=look_at,
                    up_direction=np.array([0.0, 1.0, 0.0])
                )
                
                # Add updated unit
                trajectory_manager.quilt_units.append({
                    "id": i,
                    "poses": poses,
                    "fovs": fovs,
                    "center": center_position,
                    "look_at": look_at
                })
            
            # Visualize updated units
            trajectory_manager.visualize_trajectory(quilt_mode=True)
            
            # Update status
            rows = lg_manager.config.quilt.rows
            cols = lg_manager.config.quilt.columns
            unit_count = len(trajectory_manager.quilt_units)
            device_name = lg_manager.config.device_name if hasattr(lg_manager.config, 'device_name') else "Looking Glass"
            lg_status.value = f"✅ Regenerated {unit_count} quilt unit{'s' if unit_count > 1 else ''} for {device_name} with {rows}×{cols} grid"
        
        def update_quilt_units():
            """Generate quilt units at current camera position following Looking Glass specifications"""
            try:
                lg_status.value = "Generating quilt unit at current position..."
                
                # Get current camera parameters from any client
                clients = list(server.get_clients().values())
                if not clients:
                    lg_status.value = "Error: No clients connected"
                    return
                    
                current_camera = clients[0].camera
                
                # Clear existing trajectory
                trajectory_manager.clear_trajectory()
                
                # Use default scene scale - position will be in the camera space
                scene_scale = 1.0
                
                # Add this as a SEVA keyframe for consistency
                keyframe = {
                    "position": np.array(current_camera.position),
                    "wxyz": np.array(current_camera.wxyz),
                    "fov": current_camera.fov
                }
                
                # Check if this is a new keyframe or already exists
                is_new = True
                for existing in trajectory_manager.seva_keyframes:
                    pos = existing.position if hasattr(existing, 'position') else existing.get('position', None)
                    if pos is not None and np.allclose(pos, current_camera.position, atol=1e-3):
                        is_new = False
                        break
                        
                if is_new:
                    trajectory_manager.add_seva_keyframe(keyframe)
                
                # Generate quilt trajectory using the Looking Glass camera model
                # This creates cameras in a row-major grid (left-to-right, bottom-to-top)
                poses, fovs = lg_manager.camera_model.generate_quilt_trajectory(
                    center_position=np.array(current_camera.position),
                    look_at=np.array(current_camera.look_at),
                    up_direction=np.array(current_camera.up_direction)
                )
                
                # Store the generated trajectory
                trajectory_manager.current_poses = poses
                trajectory_manager.current_fovs = fovs
                trajectory_manager.trajectory_type = "quilt"
                
                # Get the next available unit ID
                next_id = 0
                if hasattr(trajectory_manager, 'quilt_units') and trajectory_manager.quilt_units:
                    next_id = max(unit['id'] for unit in trajectory_manager.quilt_units) + 1
                
                # Initialize quilt units if needed
                if not hasattr(trajectory_manager, 'quilt_units'):
                    trajectory_manager.quilt_units = []
                
                # Add the new unit
                trajectory_manager.quilt_units.append({
                    "id": next_id,
                    "poses": poses,
                    "fovs": fovs,
                    "center": current_camera.position,
                    "look_at": current_camera.look_at
                })
                
                # Update visualization with quilt mode enabled and color coding
                trajectory_manager.visualize_trajectory(quilt_mode=True)
                
                rows = lg_manager.config.quilt.rows
                cols = lg_manager.config.quilt.columns
                keyframes_per_unit = rows * cols
                device_name = lg_manager.config.device_name if hasattr(lg_manager.config, 'device_name') else "Looking Glass"
                unit_count = len(trajectory_manager.quilt_units)
                
                lg_status.value = f"✅ Created quilt unit #{next_id} for {device_name} with {rows}×{cols} grid ({keyframes_per_unit} keyframes)"
                print(f"Quilt visualization: {unit_count} unit(s) total, added unit #{next_id} with {rows}×{cols} grid")
                
            except Exception as e:
                lg_status.value = f"Error: {str(e)}"
                print(f"Error generating quilt unit: {e}")
                import traceback
                traceback.print_exc()
        
        return quilt_mode_checkbox


def connect_preview_render_buttons(server: viser.ViserServer, trajectory_manager: TrajectoryManager):
    """
    Connect Looking Glass quilt generation with preview render functionality
    
    This function prepares quilt unit data to be consumed by the Gradio UI
    for rendering using demo_gr.py patterns. It doesn't create UI elements itself
    but makes the trajectory data ready for the Gradio interface to render.
    
    Args:
        server: The viser server instance
        trajectory_manager: The trajectory manager instance
        
    Returns:
        Tuple of (success_flag, render_poses, render_fovs) with data for the renderer
    """
    # Check if trajectory data is available in quilt mode
    if (hasattr(trajectory_manager, 'trajectory_type') and 
            trajectory_manager.trajectory_type == "quilt" and
            hasattr(trajectory_manager, 'quilt_units') and 
            trajectory_manager.quilt_units):
        
        print("Preparing quilt data for preview rendering...")
        
        # Count total units and keyframes
        unit_count = len(trajectory_manager.quilt_units)
        
        # Get a representative unit to check dimensions
        if unit_count > 0:
            sample_unit = trajectory_manager.quilt_units[0]
            keyframes_per_unit = len(sample_unit['poses'])
            
            # Prepare a consolidated list of poses and fovs for the renderer
            # This matches the expected format in demo_gr.py
            all_poses = []
            all_fovs = []
            
            # Add all quilt units to the rendering queue
            for unit in trajectory_manager.quilt_units:
                poses = unit['poses']
                fovs = unit['fovs']
                all_poses.append(poses)
                all_fovs.append(fovs)
            
            # Combine all pose data for the renderer
            render_poses = torch.cat(all_poses) if all_poses else torch.empty(0, 4, 4)
            render_fovs = torch.cat(all_fovs) if all_fovs else torch.empty(0)
            
            print(f"Ready for preview rendering: {unit_count} quilt units with {keyframes_per_unit} keyframes each")
            print(f"Total camera poses prepared: {len(render_poses)}")
            
            # Return the prepared data
            return True, render_poses, render_fovs
        else:
            print("No quilt units available for preview rendering")
            return False, None, None
    else:
        print("Trajectory not in quilt mode or no quilt units available")
        return False, None, None


def prepare_quilt_for_rendering(trajectory_manager: TrajectoryManager, input_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Prepare quilt data for rendering in the Gradio UI
    
    This function formats the quilt trajectory data in the expected format
    for demo_gr.py's renderer to process. It prepares the camera and image
    conditioning data based on the quilt unit configuration.
    
    Args:
        trajectory_manager: The trajectory manager instance
        input_data: Dictionary containing input images and camera data
        
    Returns:
        Dictionary with camera and image conditioning data for demo_gr.py renderer
    """
    # Check if we have valid quilt data
    success, render_poses, render_fovs = connect_preview_render_buttons(
        trajectory_manager.server, trajectory_manager
    )
    
    if not success or render_poses is None or render_fovs is None:
        return {}
    
    # Extract input data
    input_imgs = input_data.get('input_imgs', None)
    input_Ks = input_data.get('input_Ks', None)
    input_c2ws = input_data.get('input_c2ws', None)
    input_wh = input_data.get('input_wh', (576, 576))
    
    if input_imgs is None or input_Ks is None or input_c2ws is None:
        print("Missing required input data for rendering")
        return {}
    
    # Prepare camera conditioning data for demo_gr.py
    W, H = input_wh
    num_inputs = len(input_imgs)
    num_targets = render_poses.shape[0]  # Use shape instead of len
    
    # Combine input and target cameras
    all_c2ws = torch.cat([input_c2ws, render_poses], dim=0)
    all_Ks = torch.cat([input_Ks, input_Ks.repeat(num_targets, 1, 1)], dim=0)
    
    # Create image conditioning for demo_gr.py
    # Pad target images with zeros
    all_imgs_np = torch.zeros((num_inputs + num_targets, H, W, 3))
    all_imgs_np[:num_inputs] = input_imgs
    all_imgs_np = (all_imgs_np.numpy() * 255.0).astype(np.uint8)
    
    # Create indices
    input_indices = list(range(num_inputs))
    target_indices = list(range(num_inputs, num_inputs + num_targets))
    
    # Create conditioning dictionaries
    image_cond = {
        "img": all_imgs_np,
        "input_indices": input_indices,
        "prior_indices": input_indices  # Use input images as prior
    }
    
    camera_cond = {
        "c2w": all_c2ws,
        "K": all_Ks * all_Ks.new_tensor([W, H, 1])[:, None],  # Unnormalize K
        "input_indices": list(range(num_inputs + num_targets))
    }
    
    # Return conditioning data for demo_gr.py renderer
    render_data = {
        "image_cond": image_cond,
        "camera_cond": camera_cond,
        "num_inputs": num_inputs,
        "num_targets": num_targets,
        "input_indices": input_indices,
        "target_indices": target_indices,
        "img_wh": input_wh
    }
    
    print(f"Prepared quilt rendering data: {num_inputs} input images, {num_targets} target views")
    return render_data


def create_fastapi_ui(trajectory_manager: TrajectoryManager):
    """
    Create UI controls for FastAPI remote rendering
    
    Args:
        trajectory_manager: The trajectory manager instance
    """
    server = trajectory_manager.server
    fastapi_url = "http://localhost:8000"
    
    with server.gui.add_folder("Remote Rendering", expand_by_default=True, order=105):
        # FastAPI URL
        fastapi_url_input = server.gui.add_text(
            "FastAPI URL",
            initial_value=fastapi_url,
            hint="URL of the FastAPI server"
        )
        
        # Enable FastAPI checkbox
        fastapi_enable = server.gui.add_checkbox(
            "Enable FastAPI", False,
            hint="Enable remote rendering via FastAPI"
        )
        
        # Enable Dust3r checkbox
        dust3r_enable = server.gui.add_checkbox(
            "Use Dust3r", False,
            hint="Enable Dust3r for multi-view rendering"
        )
        
        # Browse image button
        browse_image_button = server.gui.add_button(
            "Select Input Image",
            icon=viser.Icon.FOLDER,
            hint="Select an input image for rendering"
        )
        
        # Image path display
        image_path_display = server.gui.add_text(
            "Image Path", 
            initial_value="No image selected",
            hint="Path to selected input image"
        )
        
        # Camera scale slider
        camera_scale = server.gui.add_slider(
            "Camera Scale", initial_value=2.0, min=0.1, max=15.0, step=0.1,
            hint="Camera scale for rendering"
        )
        
        # CFG slider
        cfg_slider = server.gui.add_slider(
            "CFG Value", initial_value=4.0, min=1.0, max=7.0, step=0.1,
            hint="CFG value for rendering"
        )
        
        # Seed input
        seed_input = server.gui.add_number(
            "Random Seed", 
            initial_value=23,
            hint="Random seed for rendering"
        )
        
        # Generate video button
        generate_video_button = server.gui.add_button(
            "Generate Video",
            icon=viser.Icon.VIDEO,
            hint="Generate video using FastAPI"
        )
        
        # Status text
        fastapi_status = server.gui.add_text(
            "Status", 
            initial_value="Ready",
            hint="FastAPI operation status"
        )
        
    return fastapi_url_input, fastapi_enable, dust3r_enable, browse_image_button


def render_preview_frame(camera_data: Dict[str, Any], frame_index: int) -> Image.Image:
    """
    Render a preview frame using seva's rendering system.
    This is a placeholder that would integrate with seva's actual render function.
    
    Args:
        camera_data: Camera data from gui_state.camera_traj_list
        frame_index: Frame index
        
    Returns:
        Rendered image as PIL Image
    """
    # For now, create a placeholder image with frame info
    # In a real implementation, this would call seva's rendering system
    width, height = 576, 576
    if 'img_wh' in camera_data:
        width, height = camera_data['img_wh']
        
    # Create a simple placeholder image
    image_array = np.zeros((height, width, 3), dtype=np.uint8)
    
    # Add gradient and frame number
    for y in range(height):
        for x in range(width):
            # Create a radial gradient from the center
            center_x, center_y = width // 2, height // 2
            dx, dy = x - center_x, y - center_y
            distance = np.sqrt(dx*dx + dy*dy)
            max_distance = np.sqrt(center_x*center_x + center_y*center_y)
            
            # Normalize distance and create gradient
            norm_distance = min(distance / max_distance, 1.0)
            
            # Color based on frame index - more sophisticated than the test pattern
            hue = (frame_index * 30) % 360
            r, g, b = hsv_to_rgb(hue, 0.7, 1.0 - 0.7 * norm_distance)
            
            image_array[y, x] = [r, g, b]
    
    # Add frame number as text in the center
    # (This is a very simplistic text rendering)
    font_size = 128
    center_x = width // 2 - font_size // 2
    center_y = height // 2 - font_size // 2
    
    # Draw a simple frame number
    for y in range(center_y, center_y + font_size):
        for x in range(center_x, center_x + font_size):
            if 0 <= x < width and 0 <= y < height:
                # Simple digit pattern
                nx, ny = (x - center_x) / font_size, (y - center_y) / font_size
                if get_digit_pattern(frame_index % 10, nx, ny):
                    image_array[y, x] = [255, 255, 255]  # White text
    
    return Image.fromarray(image_array)


def hsv_to_rgb(h: float, s: float, v: float) -> Tuple[int, int, int]:
    """Convert HSV to RGB color."""
    h = h / 60.0
    c = v * s
    x = c * (1 - abs((h % 2) - 1))
    m = v - c
    
    if 0 <= h < 1:
        r, g, b = c, x, 0
    elif 1 <= h < 2:
        r, g, b = x, c, 0
    elif 2 <= h < 3:
        r, g, b = 0, c, x
    elif 3 <= h < 4:
        r, g, b = 0, x, c
    elif 4 <= h < 5:
        r, g, b = x, 0, c
    else:
        r, g, b = c, 0, x
    
    return (int((r + m) * 255), int((g + m) * 255), int((b + m) * 255))


def get_digit_pattern(digit: int, nx: float, ny: float) -> bool:
    """Simple digit pattern for overlay (returns True if pixel should be white)"""
    # Very simple digit patterns
    if digit == 0:
        return (0.2 < nx < 0.8 and 0.1 < ny < 0.3) or (0.2 < nx < 0.8 and 0.7 < ny < 0.9) or \
               (0.1 < nx < 0.3 and 0.2 < ny < 0.8) or (0.7 < nx < 0.9 and 0.2 < ny < 0.8)
    elif digit == 1:
        return 0.4 < nx < 0.6 and 0.1 < ny < 0.9
    elif digit == 2:
        return (0.2 < nx < 0.8 and 0.1 < ny < 0.3) or (0.2 < nx < 0.8 and 0.4 < ny < 0.6) or \
               (0.2 < nx < 0.8 and 0.7 < ny < 0.9) or (0.6 < nx < 0.8 and 0.2 < ny < 0.5) or \
               (0.2 < nx < 0.4 and 0.5 < ny < 0.8)
    else:
        # Default pattern for other digits
        return 0.3 < nx < 0.7 and 0.3 < ny < 0.7 


def render_scene(server: viser.ViserServer, test_object_manager: TestObjectManager, trajectory_manager: TrajectoryManager):
    """
    Render the scene with current test objects and trajectory
    
    Args:
        server: The viser server
        test_object_manager: The test object manager
        trajectory_manager: The trajectory manager
    """
    # This function would update the scene with the current state
    # For now, it's just a placeholder
    pass 