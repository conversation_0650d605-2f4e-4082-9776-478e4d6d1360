"""
Trajectory Tool - A modular trajectory design system built on SEVA

This package provides a structured approach to trajectory design with:
- Interactive 3D trajectory design
- Preset trajectory generation
- Test objects with auto-centering
- Camera animation with proper look-at behavior
- Export functionality for FastAPI video inference
- Looking Glass quilt generation (optional)
"""


from .quilt_generator import (
    QuiltImageGenerator,
    FastAPIQuiltGenerator,
    SevaQuiltGenerator,
    create_test_quilt,
    generate_quilt_preview,
    create_quilt_from_seva_preview
)

from .managers import (
    TrajectoryState,
    QuiltState,
    TestObjectManager,
    TrajectoryManager,
    CameraAnimator,
    TrajectoryExporter,
    LookingGlassManager
)

# New SEVA-integrated components
try:
    from .quilt_trajectory import QuiltTrajectoryManager, QuiltKeyframe
    from .quilt_gui import QuiltGuiManager
    QUILT_COMPONENTS_AVAILABLE = True
except ImportError:
    QUILT_COMPONENTS_AVAILABLE = False

__version__ = "1.0.0"
__all__ = [
    "QuiltImageGenerator",
    "FastAPIQuiltGenerator", 
    "SevaQuiltGenerator",
    "create_test_quilt",
    "generate_quilt_preview",
    "create_quilt_from_seva_preview",
    "TrajectoryState",
    "QuiltState",
    "TestObjectManager",
    "TrajectoryManager",
    "CameraAnimator",
    "TrajectoryExporter",
    "LookingGlassManager"
]

# Add SEVA-integrated components if available
if QUILT_COMPONENTS_AVAILABLE:
    __all__.extend([
        "QuiltTrajectoryManager",
        "QuiltKeyframe", 
        "QuiltGuiManager"
    ])
