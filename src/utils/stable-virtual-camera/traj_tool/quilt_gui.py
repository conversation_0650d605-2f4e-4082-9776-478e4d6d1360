"""
SEVA-Style Quilt GUI Manager

This module provides GUI management for quilt generation following the patterns
established in seva/gui.py for consistent UI design and interaction.
"""

import numpy as np
from typing import Optional, Callable, Any, Dict, List
from pathlib import Path

import viser
import viser.transforms as vt

from .lookingglass_config import LookingGlassConfig, get_device_config, list_available_devices
from .quilt_trajectory import QuiltTrajectoryManager

# Try to import SEVA components for consistency
try:
    from seva.gui import GuiState
    SEVA_AVAILABLE = True
except ImportError:
    SEVA_AVAILABLE = False
    # Create a simple fallback GuiState
    class GuiState:
        def __init__(self):
            self.quilt_mode = False


class QuiltGuiManager:
    """Manages quilt-related GUI elements following SEVA patterns"""
    
    def __init__(self, server: viser.ViserServer, scene_scale: float = 1.0,
                 scene_node_prefix: str = "/quilt"):
        self.server = server
        self.scene_scale = scene_scale
        self.scene_node_prefix = scene_node_prefix
        
        # Initialize config and trajectory manager
        self.quilt_config = get_device_config("portrait")
        self.trajectory_manager = QuiltTrajectoryManager(server, self.quilt_config, scene_scale)
        
        # GUI state (following SEVA patterns)
        if SEVA_AVAILABLE:
            self.gui_state = GuiState(
                preview_render=False,
                preview_fov=0.0,
                preview_aspect=1.0,
                camera_traj_list=None,
                active_input_index=0
            )
            self.gui_state.quilt_mode = False
        else:
            self.gui_state = GuiState()
            self.gui_state.quilt_mode = False
        
        # Callbacks
        self.on_mode_change: Optional[Callable[[bool], None]] = None
        self.on_config_change: Optional[Callable[[LookingGlassConfig], None]] = None
        self.on_input_visibility_change: Optional[Callable[[bool], None]] = None
        self.on_quilt_visibility_change: Optional[Callable[[bool], None]] = None
        
        # GUI handles
        self._gui_handles = {}
        
    def define_gui(self, expand_by_default: bool = True, order: int = 1000) -> None:
        """Define GUI controls following SEVA patterns"""
        
        with self.server.gui.add_folder("Looking Glass", expand_by_default=expand_by_default, order=order):
            # Device configuration section (following SEVA folder organization)
            with self.server.gui.add_folder("Device Configuration", expand_by_default=True):
                device_dropdown = self.server.gui.add_dropdown(
                    "Device Type",
                    options=list_available_devices(),
                    initial_value="portrait",
                    hint="Select Looking Glass device type"
                )
                self._gui_handles["device"] = device_dropdown
                
                @device_dropdown.on_update
                def _on_device_change(event) -> None:
                    self._update_device_config(device_dropdown.value)
            
            # Quilt layout section
            with self.server.gui.add_folder("Quilt Layout", expand_by_default=True):
                rows_slider = self.server.gui.add_slider(
                    "Quilt Rows",
                    min=4,
                    max=12,
                    step=1,
                    initial_value=self.quilt_config.quilt.rows,
                    hint="Number of rows in quilt grid"
                )
                self._gui_handles["rows"] = rows_slider
                
                columns_slider = self.server.gui.add_slider(
                    "Quilt Columns",
                    min=4,
                    max=16,
                    step=1,
                    initial_value=self.quilt_config.quilt.columns,
                    hint="Number of columns in quilt grid"
                )
                self._gui_handles["columns"] = columns_slider
                
                @rows_slider.on_update
                @columns_slider.on_update
                def _on_layout_change(event) -> None:
                    self._update_quilt_layout()
            
            # Camera settings section (following SEVA parameter organization)
            with self.server.gui.add_folder("Camera Settings", expand_by_default=True):
                fov_slider = self.server.gui.add_slider(
                    "Field of View (degrees)",
                    min=8.0,
                    max=60.0,
                    step=0.5,
                    initial_value=self.quilt_config.camera.fov_degrees,
                    hint="Camera field of view - Portrait uses ~14°, others use ~54°"
                )
                self._gui_handles["fov"] = fov_slider
                
                viewcone_slider = self.server.gui.add_slider(
                    "View Cone Angle (degrees)",
                    min=20.0,
                    max=50.0,
                    step=1.0,
                    initial_value=self.quilt_config.camera.viewcone_angle,
                    hint="Total viewing angle - Portrait uses 35°, others use 40°"
                )
                self._gui_handles["viewcone"] = viewcone_slider
                
                focus_distance_slider = self.server.gui.add_slider(
                    "Focus Distance",
                    min=0.5,
                    max=5.0,
                    step=0.1,
                    initial_value=self.quilt_config.camera.focus_distance,
                    hint="Distance to focal plane where objects appear on screen"
                )
                self._gui_handles["focus"] = focus_distance_slider
                
                depthiness_slider = self.server.gui.add_slider(
                    "Depthiness",
                    min=0.5,
                    max=3.0,
                    step=0.1,
                    initial_value=self.quilt_config.camera.depthiness,
                    hint="Controls depth effect strength - higher values increase depth"
                )
                self._gui_handles["depthiness"] = depthiness_slider
                
                @fov_slider.on_update
                @viewcone_slider.on_update
                @focus_distance_slider.on_update
                @depthiness_slider.on_update
                def _on_camera_change(event) -> None:
                    self._update_camera_settings()
            
            # Visibility controls (following SEVA keyframe visibility patterns)
            with self.server.gui.add_folder("Visibility", expand_by_default=True):
                quilt_mode_checkbox = self.server.gui.add_checkbox(
                    "Quilt Mode",
                    initial_value=False,
                    hint="Enable quilt mode for Looking Glass display"
                )
                self._gui_handles["quilt_mode"] = quilt_mode_checkbox
                
                input_keyframes_visible = self.server.gui.add_checkbox(
                    "Show Input Cameras",
                    initial_value=True,
                    hint="Toggle visibility of input camera keyframes"
                )
                self._gui_handles["input_visible"] = input_keyframes_visible
                
                quilt_keyframes_visible = self.server.gui.add_checkbox(
                    "Show Quilt Views",
                    initial_value=True,
                    hint="Toggle visibility of quilt camera views"
                )
                self._gui_handles["quilt_visible"] = quilt_keyframes_visible
                
                @quilt_mode_checkbox.on_update
                def _on_mode_change(event) -> None:
                    self.gui_state.quilt_mode = quilt_mode_checkbox.value
                    if self.on_mode_change:
                        self.on_mode_change(self.gui_state.quilt_mode)
                
                @input_keyframes_visible.on_update
                def _on_input_visibility_change(event) -> None:
                    if self.on_input_visibility_change:
                        self.on_input_visibility_change(input_keyframes_visible.value)
                
                @quilt_keyframes_visible.on_update
                def _on_quilt_visibility_change(event) -> None:
                    self.trajectory_manager.set_keyframes_visible(quilt_keyframes_visible.value)
                    if self.on_quilt_visibility_change:
                        self.on_quilt_visibility_change(quilt_keyframes_visible.value)
            
            # Quilt unit management (following SEVA keyframe management patterns)
            with self.server.gui.add_folder("Quilt Units", expand_by_default=True):
                unit_count_text = self.server.gui.add_text(
                    "Unit Count",
                    initial_value="0 units",
                    hint="Number of quilt units created"
                )
                self._gui_handles["unit_count"] = unit_count_text
                
                add_unit_button = self.server.gui.add_button(
                    "Add Quilt Unit",
                    hint="Add a new quilt unit at current camera position",
                    icon=viser.Icon.PLUS
                )
                self._gui_handles["add_unit"] = add_unit_button
                
                clear_units_button = self.server.gui.add_button(
                    "Clear All Units",
                    hint="Remove all quilt units",
                    icon=viser.Icon.TRASH,
                    color="red"
                )
                self._gui_handles["clear_units"] = clear_units_button
                
                @add_unit_button.on_click
                def _on_add_unit(event) -> None:
                    self._add_unit_from_camera()
                
                @clear_units_button.on_click
                def _on_clear_units(event) -> None:
                    self._clear_all_units()
    
    def _update_device_config(self, device_name: str) -> None:
        """Update device configuration and sync GUI"""
        self.quilt_config = get_device_config(device_name)
        
        # Use the update_config method to ensure consistent configuration
        self.trajectory_manager.update_config(self.quilt_config)
        
        # Update GUI values to match new device
        if "rows" in self._gui_handles:
            self._gui_handles["rows"].value = self.quilt_config.quilt.rows
        if "columns" in self._gui_handles:
            self._gui_handles["columns"].value = self.quilt_config.quilt.columns
        if "fov" in self._gui_handles:
            self._gui_handles["fov"].value = self.quilt_config.camera.fov_degrees
        if "viewcone" in self._gui_handles:
            self._gui_handles["viewcone"].value = self.quilt_config.camera.viewcone_angle
        if "focus" in self._gui_handles:
            self._gui_handles["focus"].value = self.quilt_config.camera.focus_distance
        if "depthiness" in self._gui_handles:
            self._gui_handles["depthiness"].value = self.quilt_config.camera.depthiness
        
        if self.on_config_change:
            self.on_config_change(self.quilt_config)
    
    def _update_quilt_layout(self) -> None:
        """Update quilt layout settings"""
        if "rows" in self._gui_handles and "columns" in self._gui_handles:
            self.quilt_config.quilt.rows = int(self._gui_handles["rows"].value)
            self.quilt_config.quilt.columns = int(self._gui_handles["columns"].value)
            
            # Update the trajectory manager with the new configuration
            self.trajectory_manager.update_config(self.quilt_config)
    
    def _update_camera_settings(self) -> None:
        """Update camera settings"""
        if all(key in self._gui_handles for key in ["fov", "viewcone", "focus", "depthiness"]):
            self.quilt_config.camera.fov_degrees = float(self._gui_handles["fov"].value)
            self.quilt_config.camera.viewcone_angle = float(self._gui_handles["viewcone"].value)
            self.quilt_config.camera.focus_distance = float(self._gui_handles["focus"].value)
            self.quilt_config.camera.depthiness = float(self._gui_handles["depthiness"].value)
            
            # Update the trajectory manager with the new configuration
            self.trajectory_manager.update_config(self.quilt_config)
    
    def _add_unit_from_camera(self) -> None:
        """Add a quilt unit from current camera position"""
        # Get current camera position from clients
        clients = self.server.get_clients()
        if not clients:
            print("No clients connected")
            return
        
        # Use the first client's camera
        client = next(iter(clients.values()))
        
        # Get camera position and orientation
        camera_position = np.array(client.camera.position)
        camera_wxyz = np.array(client.camera.wxyz)
        
        # Get rotation matrix from quaternion (this is in viser/OpenGL convention)
        viser_rotation = vt.SO3(camera_wxyz).as_matrix()
        
        # Calculate look_at point (assume looking forward at a reasonable distance)
        # In viser/OpenGL convention, forward is -Z
        look_direction = viser_rotation @ np.array([0, 0, -1])  # OpenGL convention
        look_at = camera_position + look_direction * 2.0  # 2 units forward
        
        print(f"Adding unit from camera: pos={camera_position}, look_at={look_at}")
        
        # Convert from viser/OpenGL convention to OpenCV convention
        # The key insight: In viser/OpenGL, forward is -Z, but in OpenCV, forward is +Z
        
        # Calculate forward vector from look direction (this gives us the correct direction)
        forward = look_at - camera_position
        forward = forward / np.linalg.norm(forward)  # Normalize
        
        # Ensure forward direction has positive Z component (OpenCV convention)
        # If it's pointing backwards (negative Z), flip it
        if forward[2] < 0:
            forward = -forward
            # Also update the look_at point to maintain consistency
            look_at = camera_position + forward * 2.0
            print(f"[DEBUG] Flipped forward direction to ensure positive Z: {forward}")
        
        # Use world up as reference
        world_up = np.array([0.0, 1.0, 0.0])
        
        # Calculate right vector
        right = np.cross(forward, world_up)
        if np.linalg.norm(right) < 1e-6:  # Handle degenerate case
            right = np.array([1.0, 0.0, 0.0])
        else:
            right = right / np.linalg.norm(right)
        
        # Calculate camera up vector
        up = np.cross(right, forward)
        up = up / np.linalg.norm(up)
        
        # Create OpenCV-convention rotation matrix (right, down, forward)
        # Note: In OpenCV convention, Y is down, so we negate the up vector
        opencv_rotation = np.column_stack([right, -up, forward])
        
        # Debug: Check the forward direction
        forward_direction = opencv_rotation[:, 2]
        print(f"[DEBUG] Final OpenCV forward direction: {forward_direction}")
        print(f"[DEBUG] Forward Z component: {forward_direction[2]}")
        
        # Use the add_quilt_unit_from_pose method which will pass this to QuiltTrajectoryManager
        unit_id = self.add_quilt_unit_from_pose(camera_position, opencv_rotation, look_at)
        
        print(f"Added quilt unit {unit_id} at position {camera_position}")
    
    def _clear_all_units(self) -> None:
        """Clear all quilt units"""
        # Use the enhanced clear method that properly handles red frustums
        if hasattr(self.trajectory_manager, 'handle_clear_all_units_button'):
            self.trajectory_manager.handle_clear_all_units_button()
        else:
            # Fallback to standard clear method
            self.trajectory_manager.clear_all_units()
            
        self._update_unit_count()
        print("Cleared all quilt units")
    
    def _update_unit_count(self) -> None:
        """Update the unit count display"""
        if "unit_count" in self._gui_handles:
            count = len(self.trajectory_manager.quilt_units)
            self._gui_handles["unit_count"].value = f"{count} units"
    
    def add_quilt_unit_from_pose(self, input_position: np.ndarray, input_rotation: np.ndarray, 
                                scene_center: np.ndarray) -> int:
        """Add a quilt unit from a camera pose
        
        Args:
            input_position: Camera position in world coordinates
            input_rotation: Camera rotation matrix (OpenCV convention)
            scene_center: Target look-at point
            
        Returns:
            Unit ID of the added quilt unit
        """
        # The QuiltTrajectoryManager.add_quilt_unit method will handle the proper
        # conversion using opencv_to_viser_rotation internally, so we pass the
        # OpenCV rotation matrix directly
        unit_id = self.trajectory_manager.add_quilt_unit(
            input_position, input_rotation, scene_center
        )
        # Make sure to visualize the quilt units after adding
        self.trajectory_manager.visualize_quilt_units()
        self._update_unit_count()
        return unit_id
    
    def set_scene_scale(self, scale: float) -> None:
        """Update scene scale"""
        self.scene_scale = scale
        self.trajectory_manager.update_scene_scale(scale)
    
    def get_input_keyframes_visible(self) -> bool:
        """Get input keyframes visibility state"""
        if "input_visible" in self._gui_handles:
            return self._gui_handles["input_visible"].value
        return True
    
    def get_quilt_keyframes_visible(self) -> bool:
        """Get quilt keyframes visibility state"""
        if "quilt_visible" in self._gui_handles:
            return self._gui_handles["quilt_visible"].value
        return True
    
    def is_quilt_mode_enabled(self) -> bool:
        """Check if quilt mode is enabled"""
        return self.gui_state.quilt_mode
    
    def export_quilt_data(self) -> Dict[str, Any]:
        """Export all quilt data for rendering"""
        return {
            "config": {
                "device_name": self.quilt_config.device_name,
                "rows": self.quilt_config.quilt.rows,
                "columns": self.quilt_config.quilt.columns,
                "width": self.quilt_config.quilt.width,
                "height": self.quilt_config.quilt.height,
                "aspect_ratio": self.quilt_config.quilt.aspect_ratio,
                "camera": {
                    "fov_degrees": self.quilt_config.camera.fov_degrees,
                    "viewcone_angle": self.quilt_config.camera.viewcone_angle,
                    "focus_distance": self.quilt_config.camera.focus_distance,
                    "depthiness": self.quilt_config.camera.depthiness
                }
            },
            "units": [
                {
                    "id": unit["id"],
                    "center_position": unit["center_position"],
                    "keyframes": [
                        {
                            "position": kf.position.tolist(),
                            "wxyz": kf.wxyz.tolist(),
                            "fov": kf.fov,
                            "aspect": kf.aspect,
                            "row": kf.row,
                            "col": kf.col
                        }
                        for kf in unit["keyframes"]
                    ]
                }
                for unit in self.trajectory_manager.quilt_units
            ]
        }