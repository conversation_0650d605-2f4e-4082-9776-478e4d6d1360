"""
Looking Glass Camera Configuration

Camera parameters and quilt settings for different Looking Glass devices.
Default configuration is for Looking Glass Portrait (Web).

References:
- https://docs.lookingglassfactory.com/keyconcepts/camera
- https://docs.lookingglassfactory.com/software-tools/looking-glass-studio/quilt-photo-video
"""

import numpy as np
from typing import Dict, Any, Tuple
from dataclasses import dataclass


@dataclass
class QuiltSettings:
    """Quilt image layout parameters"""
    rows: int
    columns: int
    aspect_ratio: float
    width: int
    height: int
    
    @property
    def total_views(self) -> int:
        """Total number of views in the quilt"""
        return self.rows * self.columns
    
    @property
    def view_width(self) -> int:
        """Width of each individual view"""
        return self.width // self.columns
    
    @property
    def view_height(self) -> int:
        """Height of each individual view"""
        return self.height // self.rows
    
    def get_filename_pattern(self, base_name: str = "quilt") -> str:
        """Generate filename following Looking Glass naming convention"""
        return f"{base_name}_qs{self.columns}x{self.rows}_a{self.aspect_ratio:.2f}.png"


@dataclass
class CameraSettings:
    """Camera parameters for trajectory generation"""
    fov_degrees: float = 54.0  # Default field of view
    viewcone_angle: float = 40.0  # Angle between leftmost and rightmost views
    focus_distance: float = 2.0  # Distance to focus plane
    depthiness: float = 1.2  # Controls depth effect strength
    inward_angle: float = 0.0  # Degrees views angle inward toward center
    
    @property
    def fov_radians(self) -> float:
        """FOV in radians"""
        return np.radians(self.fov_degrees)
    
    @property
    def viewcone_radians(self) -> float:
        """View cone angle in radians"""
        return np.radians(self.viewcone_angle)


@dataclass
class LookingGlassConfig:
    """Complete Looking Glass configuration"""
    device_name: str
    quilt: QuiltSettings
    camera: CameraSettings
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization"""
        return {
            "device_name": self.device_name,
            "quilt": {
                "rows": self.quilt.rows,
                "columns": self.quilt.columns,
                "aspect_ratio": self.quilt.aspect_ratio,
                "width": self.quilt.width,
                "height": self.quilt.height
            },
            "camera": {
                "fov_degrees": self.camera.fov_degrees,
                "viewcone_angle": self.camera.viewcone_angle,
                "focus_distance": self.camera.focus_distance,
                "depthiness": self.camera.depthiness,
                "inward_angle": self.camera.inward_angle
            }
        }


# Device presets based on Looking Glass documentation (Updated 2025)
DEVICE_PRESETS = {
    "go": LookingGlassConfig(
        device_name="Looking Glass Go",
        quilt=QuiltSettings(
            rows=6,
            columns=11,
            aspect_ratio=1.0,  # Square quilt
            width=4092,
            height=4092
        ),
        camera=CameraSettings(
            fov_degrees=14.0,
            viewcone_angle=35.0,
            focus_distance=2.0,
            depthiness=1.2
        )
    ),
    
    "portrait": LookingGlassConfig(
        device_name="Looking Glass Portrait",
        quilt=QuiltSettings(
            rows=6,
            columns=8,
            aspect_ratio=0.75,  # 4:3 aspect ratio
            width=3360,
            height=3360
        ),
        camera=CameraSettings(
            fov_degrees=14.0,   # Per LKG docs: 14° for Portrait at typical viewing distance
            viewcone_angle=35.0,  # Per LKG docs: 35° digital view angle for Portrait
            focus_distance=2.0,
            depthiness=1.2
        )
    ),
    
    "16_landscape": LookingGlassConfig(
        device_name="Looking Glass 16\" Light Field Display (Landscape)",
        quilt=QuiltSettings(
            rows=7,
            columns=7,
            aspect_ratio=1.0,  # Square quilt
            width=5999,
            height=5999
        ),
        camera=CameraSettings(
            fov_degrees=54.0,
            viewcone_angle=40.0,
            focus_distance=2.0,
            depthiness=1.2
        )
    ),
    
    "16_portrait": LookingGlassConfig(
        device_name="Looking Glass 16\" Light Field Display (Portrait)",
        quilt=QuiltSettings(
            rows=6,
            columns=11,
            aspect_ratio=1.0,  # Approximately square
            width=5995,
            height=6000
        ),
        camera=CameraSettings(
            fov_degrees=54.0,
            viewcone_angle=40.0,
            focus_distance=2.0,
            depthiness=1.2
        )
    ),
    
    "32_landscape": LookingGlassConfig(
        device_name="Looking Glass 32\" Light Field Display (Landscape)",
        quilt=QuiltSettings(
            rows=7,
            columns=7,
            aspect_ratio=1.0,  # Square quilt
            width=8190,
            height=8190
        ),
        camera=CameraSettings(
            fov_degrees=54.0,
            viewcone_angle=40.0,
            focus_distance=2.0,
            depthiness=1.2
        )
    ),
    
    "32_portrait": LookingGlassConfig(
        device_name="Looking Glass 32\" Light Field Display (Portrait)",
        quilt=QuiltSettings(
            rows=6,
            columns=11,
            aspect_ratio=1.0,  # Approximately square
            width=8184,
            height=8184
        ),
        camera=CameraSettings(
            fov_degrees=54.0,
            viewcone_angle=40.0,
            focus_distance=2.0,
            depthiness=1.2
        )
    ),
    
    "65": LookingGlassConfig(
        device_name="Looking Glass 65\" Light Field Display",
        quilt=QuiltSettings(
            rows=9,
            columns=8,
            aspect_ratio=1.0,  # Square quilt
            width=8192,
            height=8192
        ),
        camera=CameraSettings(
            fov_degrees=54.0,
            viewcone_angle=40.0,
            focus_distance=2.0,
            depthiness=1.2
        )
    )
}


def get_default_config() -> LookingGlassConfig:
    """Get the default Looking Glass Portrait configuration"""
    return DEVICE_PRESETS["portrait"]


def get_device_config(device_name: str) -> LookingGlassConfig:
    """Get configuration for a specific device"""
    if device_name not in DEVICE_PRESETS:
        raise ValueError(f"Unknown device: {device_name}. Available: {list(DEVICE_PRESETS.keys())}")
    return DEVICE_PRESETS[device_name]


def list_available_devices() -> list[str]:
    """List all available device configurations"""
    return list(DEVICE_PRESETS.keys())


def create_custom_config(
    device_name: str,
    rows: int,
    columns: int,
    aspect_ratio: float,
    width: int,
    height: int,
    **camera_kwargs
) -> LookingGlassConfig:
    """Create a custom Looking Glass configuration"""
    camera_settings = CameraSettings(**camera_kwargs)
    quilt_settings = QuiltSettings(
        rows=rows,
        columns=columns,
        aspect_ratio=aspect_ratio,
        width=width,
        height=height
    )
    
    return LookingGlassConfig(
        device_name=device_name,
        quilt=quilt_settings,
        camera=camera_settings
    ) 