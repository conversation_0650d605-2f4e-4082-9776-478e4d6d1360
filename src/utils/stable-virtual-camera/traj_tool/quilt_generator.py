"""
Quilt Image Generator

Generates and renders quilt images for Looking Glass displays.
Handles the arrangement of multiple camera views into the quilt grid format.
Integrates with seva's Preview render system for realistic image generation.

References:
- https://docs.lookingglassfactory.com/software-tools/looking-glass-studio/quilt-photo-video
- https://github.com/jaxzin/lkg-quilt
"""

# Exports
__all__ = ['QuiltImageGenerator', 'FastAPIQuiltGenerator', 'SEVA_AVAILABLE', 
           'create_test_quilt', 'generate_quilt_preview', 'SevaQuiltGenerator',
           'create_quilt_from_seva_preview']

import numpy as np
import torch
import json
import requests
import asyncio
import aiohttp
from typing import List, Dict, Any, Tuple, Optional, Union, Callable
from pathlib import Path
import PIL.Image as Image
import PIL.ImageDraw as ImageDraw

# Handle PIL constants compatibility for newer Pillow versions
try:
    LANCZOS = Image.Resampling.LANCZOS
    BICUBIC = Image.Resampling.BICUBIC
except AttributeError:
    # Fallback for older PIL versions
    LANCZOS = Image.LANCZOS  # type: ignore
    BICUBIC = Image.BICUBIC  # type: ignore
import io
import base64
import time
import os
import glob
import sys
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

from .lookingglass_config import LookingGlassConfig
from .quilt_camera import QuiltCameraModel

# Import OpenCV for optimized image processing
try:
    import cv2
    CV2_AVAILABLE = True
except ImportError:
    CV2_AVAILABLE = False
    print("OpenCV not available, using PIL for image processing")

# Optional seva imports for enhanced rendering
try:
    # Only import the basic seva modules that we know work
    from seva.geometry import get_preset_pose_fov
    from seva.gui import GuiState
    SEVA_AVAILABLE = True
except ImportError:
    SEVA_AVAILABLE = False
    print("SEVA modules not available. Using basic quilt generation only.")


class SevaQuiltGenerator:
    """
    Generates quilt images using seva's Preview render system.
    This class interfaces with seva's rendering pipeline to create quilts
    from actual rendered images rather than procedural test patterns.
    """
    
    def __init__(self, config: LookingGlassConfig):
        """
        Initialize the seva quilt generator.
        
        Args:
            config: Looking Glass configuration
        """
        if not SEVA_AVAILABLE:
            raise ImportError("SEVA modules not available")
            
        self.config = config
        self.camera_model = QuiltCameraModel(config)
        
    def create_quilt_from_seva_renders(
        self,
        camera_traj_list: List[Dict[str, Any]],
        render_function: Optional[Callable] = None
    ) -> Optional[Image.Image]:
        """
        Create a quilt image from seva's camera trajectory data.
        This method is designed to work with seva's 'Preview render' system.
        
        Args:
            camera_traj_list: List of camera trajectory data from seva (gui_state.camera_traj_list)
            render_function: Optional render function (uses placeholder if None)
            
        Returns:
            Composed quilt image or None on error
        """
        if not camera_traj_list:
            print("No camera trajectory data provided")
            return None
            
        expected_views = len(camera_traj_list)
        print(f"Creating quilt from {expected_views} seva camera views")
        
        # For Looking Glass, we need to ensure we have the right number of views
        required_views = self.config.quilt.total_views
        if expected_views != required_views:
            print(f"Warning: Expected {required_views} views for {self.config.device_name}, got {expected_views}")
            # Pad or truncate to match expected views
            if expected_views < required_views:
                # Duplicate last view to fill remaining slots
                while len(camera_traj_list) < required_views:
                    camera_traj_list.append(camera_traj_list[-1])
            else:
                # Truncate to required views
                camera_traj_list = camera_traj_list[:required_views]
            print(f"Adjusted to {len(camera_traj_list)} views")
        
        # Create rendered images from seva's camera trajectory data
        rendered_images = []
        
        for i, camera_data in enumerate(camera_traj_list):
            if render_function is not None:
                # Use provided render function (e.g., from seva's Preview render)
                try:
                    image = render_function(camera_data, i)
                    if isinstance(image, Image.Image):
                        rendered_images.append(image)
                    else:
                        print(f"Render function returned invalid image type for view {i}")
                        return None
                except Exception as e:
                    print(f"Render function failed for view {i}: {e}")
                    return None
            else:
                # Create enhanced placeholder image with camera information
                view_image = self._create_enhanced_placeholder_view(i, camera_data)
                rendered_images.append(view_image)
        
        if len(rendered_images) != len(camera_traj_list):
            print(f"Expected {len(camera_traj_list)} rendered images, got {len(rendered_images)}")
            return None
            
        # Arrange images into quilt layout
        quilt_image = self._arrange_images_into_quilt(rendered_images)
        print(f"✅ Created quilt from seva camera trajectory: {quilt_image.size if quilt_image else 'Failed'}")
        return quilt_image
        
        if len(rendered_images) != expected_views:
            print(f"Expected {expected_views} views, got {len(rendered_images)}")
            return None
            
        # Arrange images into quilt layout
        quilt_image = self._arrange_images_into_quilt(rendered_images)
        return quilt_image
    
    def _create_enhanced_placeholder_view(self, view_index: int, camera_data: Dict[str, Any]) -> Image.Image:
        """Create an enhanced placeholder view image with camera information for quilt testing"""
        # Extract camera info from seva's camera trajectory format
        w2c_flat = camera_data.get('w2c', [])
        if len(w2c_flat) == 16:
            w2c = np.array(w2c_flat).reshape(4, 4)
            # Get camera position from world-to-camera matrix
            c2w = np.linalg.inv(w2c)
            position = c2w[:3, 3]
        else:
            position = np.array([0, 0, 0])
        
        # Get image dimensions from config or camera data
        img_wh = camera_data.get('img_wh', (self.config.quilt.view_width, self.config.quilt.view_height))
        width, height = img_wh if len(img_wh) == 2 else (self.config.quilt.view_width, self.config.quilt.view_height)
        
        # Create gradient image with view info
        img_array = np.zeros((height, width, 3), dtype=np.uint8)
        
        # Create a more sophisticated pattern for Looking Glass testing
        for y in range(height):
            for x in range(width):
                # Create a radial gradient from the center
                center_x, center_y = width // 2, height // 2
                dx, dy = x - center_x, y - center_y
                distance = np.sqrt(dx*dx + dy*dy)
                max_distance = np.sqrt(center_x*center_x + center_y*center_y)
                
                # Normalize distance and create gradient
                norm_distance = min(distance / max_distance, 1.0)
                
                # Color based on view index and position for easy identification
                hue_shift = (view_index * 30) % 360  # Different hue per view
                intensity = int(128 + 80 * (1 - norm_distance))  # Brighter in center
                
                # Convert HSV-like to RGB
                if hue_shift < 120:  # Red to Green
                    r = max(0, min(255, intensity + (120 - hue_shift)))
                    g = max(0, min(255, intensity + hue_shift))
                    b = max(0, min(255, intensity - 40))
                elif hue_shift < 240:  # Green to Blue
                    r = max(0, min(255, intensity - 40))
                    g = max(0, min(255, intensity + (240 - hue_shift)))
                    b = max(0, min(255, intensity + (hue_shift - 120)))
                else:  # Blue to Red
                    r = max(0, min(255, intensity + (hue_shift - 240)))
                    g = max(0, min(255, intensity - 40))
                    b = max(0, min(255, intensity + (360 - hue_shift)))
                
                img_array[y, x] = [r, g, b]
        
        # Add view number as overlay (simple text rendering)
        # Draw view number in corner
        corner_size = min(width, height) // 8
        start_x, start_y = width - corner_size, 0
        for y in range(start_y, start_y + corner_size):
            for x in range(start_x, width):
                # Simple digit pattern for view index (mod 10)
                digit = view_index % 10
                pattern = self._get_digit_pattern(digit, x - start_x, y - start_y, corner_size)
                if pattern:
                    img_array[y, x] = [255, 255, 255]  # White text
        
        image = Image.fromarray(img_array)
        return image
    
    def _get_digit_pattern(self, digit: int, x: int, y: int, size: int) -> bool:
        """Simple digit pattern for overlay (returns True if pixel should be white)"""
        # Normalize coordinates to 0-1
        nx, ny = x / size, y / size
        
        # Very simple digit patterns
        if digit == 0:
            return (0.2 < nx < 0.8 and 0.1 < ny < 0.3) or (0.2 < nx < 0.8 and 0.7 < ny < 0.9) or \
                   (0.1 < nx < 0.3 and 0.2 < ny < 0.8) or (0.7 < nx < 0.9 and 0.2 < ny < 0.8)
        elif digit == 1:
            return 0.4 < nx < 0.6 and 0.1 < ny < 0.9
        elif digit == 2:
            return (0.2 < nx < 0.8 and 0.1 < ny < 0.3) or (0.2 < nx < 0.8 and 0.4 < ny < 0.6) or \
                   (0.2 < nx < 0.8 and 0.7 < ny < 0.9) or (0.6 < nx < 0.8 and 0.2 < ny < 0.5) or \
                   (0.2 < nx < 0.4 and 0.5 < ny < 0.8)
        else:
            # Default pattern for other digits
            return 0.3 < nx < 0.7 and 0.3 < ny < 0.7
        
    def _create_placeholder_view(self, view_index: int, camera_data: Dict[str, Any]) -> Image.Image:
        """Legacy method - use _create_enhanced_placeholder_view instead"""
        return self._create_enhanced_placeholder_view(view_index, camera_data)
    
    def _arrange_images_into_quilt(self, images: List[Image.Image]) -> Image.Image:
        """
        Arrange a list of PIL images into a quilt layout.
        
        Args:
            images: List of PIL Image objects to arrange
            
        Returns:
            PIL Image containing the arranged quilt
        """
        print(f"Arranging {len(images)} images into quilt layout")
        
        # Get quilt configuration
        quilt_cols = self.config.quilt.columns
        quilt_rows = self.config.quilt.rows
        expected_views = self.config.quilt.total_views
        
        # Validate and prepare images
        view_images = []
        for i, img in enumerate(images[:expected_views]):
            if isinstance(img, Image.Image):
                view_images.append(img.convert('RGB'))
                if i == 0:
                    print(f"First image size: {img.size}")
            else:
                print(f"Warning: Item {i} is not a PIL Image, skipping")
                continue
                
        if len(view_images) < expected_views:
            print(f"Warning: Only have {len(view_images)} images, expected {expected_views}")
            # Create placeholder images to fill remaining views
            if len(view_images) > 0:
                placeholder = view_images[0].copy()
            else:
                # Create a simple placeholder image
                placeholder = Image.new('RGB', (400, 400), color=(128, 128, 128))
            
            while len(view_images) < expected_views:
                view_images.append(placeholder.copy())
        
        # Arrange images into quilt
        return self._arrange_views_into_quilt(view_images)
    
    def create_quilt_from_camera_trajectory(
        self,
        poses: torch.Tensor,
        fovs: torch.Tensor,
        image_cond: Dict[str, Any],
        camera_cond: Dict[str, Any],
        output_dir: str = "exported_trajectories",
        use_seva_model: bool = True
    ) -> Optional[Image.Image]:
        """
        Create a quilt by rendering a camera trajectory using seva's model.
        
        Args:
            poses: Camera poses tensor
            fovs: Field of view tensor
            image_cond: Image conditioning data
            camera_cond: Camera conditioning data
            output_dir: Directory to save rendered images
            use_seva_model: Whether to use seva's model for rendering
            
        Returns:
            Composed quilt image or None if rendering fails
        """
        if not use_seva_model:
            print("Seva model not available, falling back to test patterns")
            return self._create_procedural_quilt()
            
        try:
            # Prepare output directory
            render_dir = Path(output_dir) / "seva_renders"
            render_dir.mkdir(parents=True, exist_ok=True)
            
            # TODO: Integrate with seva's full rendering pipeline
            # For now, we'll use a simplified approach
            print("Direct seva rendering integration not yet implemented")
            print("Using procedural test patterns with seva-style geometry")
            
            return self._create_procedural_quilt()
            
        except Exception as e:
            print(f"Seva rendering failed: {e}")
            return self._create_procedural_quilt()
    
    def _arrange_views_into_quilt(self, view_images: List[Image.Image]) -> Image.Image:
        """
        Arrange a list of view images into a quilt layout.
        
        Args:
            view_images: List of view images
            
        Returns:
            Composed quilt image
        """
        if len(view_images) != self.config.quilt.total_views:
            print(f"Warning: Expected {self.config.quilt.total_views} views, got {len(view_images)}")
            # Pad or truncate as needed
            while len(view_images) < self.config.quilt.total_views:
                view_images.append(view_images[-1].copy())
            view_images = view_images[:self.config.quilt.total_views]
        
        # Create empty quilt
        quilt = Image.new(
            'RGB',
            (self.config.quilt.width, self.config.quilt.height),
            (0, 0, 0)
        )
        
        # Calculate view dimensions
        view_width = self.config.quilt.view_width
        view_height = self.config.quilt.view_height
        
        # Arrange views in grid
        for i, view_img in enumerate(view_images):
            row = i // self.config.quilt.columns
            col = i % self.config.quilt.columns
            
            # Resize view to fit in grid cell
            view_resized = view_img.resize((view_width, view_height), LANCZOS)
            
            # Calculate position in quilt
            x = col * view_width
            y = row * view_height
            
            # Paste view into quilt
            quilt.paste(view_resized, (x, y))
        
        return quilt
    
    def _create_procedural_quilt(self) -> Image.Image:
        """
        Create a procedural quilt with seva-style geometry as fallback.
        """
        view_images = []
        
        for i in range(self.config.quilt.total_views):
            # Generate test image with seva-style geometry
            width = self.config.quilt.view_width
            height = self.config.quilt.view_height
            
            # Create test scene with procedural geometry
            img_array = self._render_seva_style_view(i, width, height)
            view_images.append(Image.fromarray(img_array))
        
        return self._arrange_views_into_quilt(view_images)
    
    def _render_seva_style_view(self, view_idx: int, width: int, height: int) -> np.ndarray:
        """Render a single view with seva-style procedural geometry."""
        # Create base image with realistic colors
        img_array = np.ones((height, width, 3), dtype=np.uint8) * 20  # Dark background
        
        # Add some procedural geometry that looks more realistic
        center_x, center_y = width // 2, height // 2
        
        # Create multiple objects at different depths with proper parallax
        objects = [
            {"pos": (center_x, center_y), "radius": 50, "color": (220, 180, 140), "depth": 0.0},
            {"pos": (center_x - 80, center_y - 60), "radius": 35, "color": (180, 220, 140), "depth": 0.3},
            {"pos": (center_x + 70, center_y + 40), "radius": 40, "color": (140, 180, 220), "depth": -0.2},
            {"pos": (center_x - 40, center_y + 80), "radius": 25, "color": (220, 140, 180), "depth": 0.5},
        ]
        
        # Simulate parallax effect based on view index
        # Convert view index to normalized position (-1 to 1)
        view_progress = view_idx / (self.config.quilt.total_views - 1)
        view_u = (view_progress * 2 - 1) * 0.5  # Scale to reasonable parallax range
        
        # Apply aspect ratio correction
        if self.config.quilt.aspect_ratio < 1.0:  # Portrait
            view_u *= self.config.quilt.aspect_ratio
        
        for obj in objects:
            # Apply parallax offset based on depth
            parallax_offset = view_u * obj["depth"] * 100  # Scale parallax effect
            obj_x = obj["pos"][0] + parallax_offset
            obj_y = obj["pos"][1]
            
            # Only render if object is within view
            if obj_x + obj["radius"] < 0 or obj_x - obj["radius"] > width:
                continue
                
            # Draw object with proper 3D shading
            for y in range(max(0, int(obj_y - obj["radius"])), 
                          min(height, int(obj_y + obj["radius"]))):
                for x in range(max(0, int(obj_x - obj["radius"])), 
                              min(width, int(obj_x + obj["radius"]))):
                    
                    dx = x - obj_x
                    dy = y - obj_y
                    dist_sq = dx*dx + dy*dy
                    
                    if dist_sq <= obj["radius"]**2:
                        # Calculate 3D position on sphere
                        z = np.sqrt(obj["radius"]**2 - dist_sq)
                        
                        # Improved lighting with multiple light sources
                        normal = np.array([dx, dy, z]) / obj["radius"]
                        
                        # Main light from upper right
                        light_dir1 = np.array([0.5, 0.5, 1.0])
                        light_dir1 /= np.linalg.norm(light_dir1)
                        intensity1 = max(0.1, np.dot(normal, light_dir1))
                        
                        # Fill light from left
                        light_dir2 = np.array([-0.3, 0.1, 0.8])
                        light_dir2 /= np.linalg.norm(light_dir2)
                        intensity2 = max(0.0, np.dot(normal, light_dir2)) * 0.3
                        
                        # Ambient light
                        ambient = 0.2
                        
                        # Combine lighting
                        total_intensity = intensity1 + intensity2 + ambient
                        total_intensity = min(1.0, total_intensity)
                        
                        # Apply intensity to color
                        color = np.array(obj["color"]) * total_intensity
                        img_array[y, x] = np.clip(color, 0, 255).astype(np.uint8)
        
        # Add subtle depth-of-field effect
        if view_idx % 8 == 0:  # Add some variation
            # Add some noise/grain for realism
            noise = np.random.normal(0, 5, img_array.shape)
            img_array = np.clip(img_array + noise, 0, 255).astype(np.uint8)
        
        return img_array
    
    def save_quilt_image(
        self,
        quilt_image: Image.Image,
        filename: Optional[str] = None,
        output_dir: str = "exported_trajectories"
    ) -> str:
        """
        Save quilt image with proper Looking Glass naming convention.
        
        Args:
            quilt_image: Quilt image to save
            filename: Custom filename (auto-generated if None)
            output_dir: Output directory
            
        Returns:
            Path to saved file
        """
        if filename is None:
            filename = self.config.quilt.get_filename_pattern("lookingglass_seva")
        
        # Ensure filename has .png extension
        if not filename.lower().endswith('.png'):
            filename += '.png'
        
        output_path = Path(output_dir)
        output_path.mkdir(exist_ok=True)
        
        file_path = output_path / filename
        quilt_image.save(file_path, 'PNG')
        
        print(f"Saved quilt: {file_path}")
        print(f"  Device: {self.config.device_name}")
        print(f"  Views: {self.config.quilt.columns}x{self.config.quilt.rows} ({self.config.quilt.total_views} total)")
        print(f"  Resolution: {self.config.quilt.width}x{self.config.quilt.height}")
        
        return str(file_path)


class QuiltImageGenerator:
    """
    Generates quilt images by arranging multiple camera view images
    into the Looking Glass quilt grid format.
    """
    
    def __init__(self, config: LookingGlassConfig):
        """
        Initialize the quilt generator.
        
        Args:
            config: Looking Glass configuration
        """
        self.config = config
        
    def create_empty_quilt(self, background_color: Tuple[int, int, int] = (128, 128, 128)) -> Image.Image:
        """
        Create an empty quilt image with the correct dimensions.
        
        Args:
            background_color: RGB background color
            
        Returns:
            Empty quilt image
        """
        quilt_image = Image.new(
            'RGB',
            (self.config.quilt.width, self.config.quilt.height),
            background_color
        )
        return quilt_image
    
    def arrange_views_into_quilt(self, view_images: List[Image.Image]) -> Image.Image:
        """
        Arrange a list of view images into a quilt layout.
        
        Args:
            view_images: List of individual view images
            
        Returns:
            A quilt image with all views arranged in the proper layout
        """
        # Get quilt configuration
        rows = self.config.quilt.rows
        cols = self.config.quilt.columns
        
        # Check if we have the right number of views
        if len(view_images) != rows * cols:
            print(f"Warning: Expected {rows * cols} views, got {len(view_images)}")
            
            # Pad with black images if we have too few
            if len(view_images) < rows * cols:
                black_img = Image.new("RGB", (self.config.quilt.view_width, self.config.quilt.view_height), (0, 0, 0))
                while len(view_images) < rows * cols:
                    view_images.append(black_img)
            # Truncate if we have too many
            elif len(view_images) > rows * cols:
                view_images = view_images[:rows * cols]
        
        # Create empty quilt image
        quilt_width = self.config.quilt.width
        quilt_height = self.config.quilt.height
        quilt_image = Image.new("RGB", (quilt_width, quilt_height), (0, 0, 0))
        
        # Calculate view dimensions
        view_width = quilt_width // cols
        view_height = quilt_height // rows
        
        # Arrange views in row-major order (left to right, top to bottom)
        for i, view_img in enumerate(view_images):
            row = i // cols
            col = i % cols
            
            # Calculate position
            x = col * view_width
            y = row * view_height
            
            # Resize view to fit in quilt
            if view_img.size != (view_width, view_height):
                # Handle different PIL versions (LANCZOS vs Image.LANCZOS)
                try:
                    # For newer PIL versions
                    if hasattr(Image, 'Resampling'):
                        resized_view = view_img.resize((view_width, view_height), Image.Resampling.LANCZOS)
                    else:
                        # For older PIL versions
                        resized_view = view_img.resize((view_width, view_height), LANCZOS)
                except AttributeError:
                    # Fallback to BICUBIC if LANCZOS is not available
                    resized_view = view_img.resize((view_width, view_height), BICUBIC)
            else:
                resized_view = view_img
            
            # Paste into quilt
            quilt_image.paste(resized_view, (x, y))
        
        return quilt_image
    
    def create_test_quilt(self, pattern: str = "gradient") -> Image.Image:
        """
        Create a test quilt image with generated patterns.
        
        Args:
            pattern: Pattern type ("gradient", "checkerboard", "numbered", "seva")
            
        Returns:
            Test quilt image
        """
        if pattern == "seva" and SEVA_AVAILABLE:
            return self._create_seva_test_quilt()
        else:
            view_images = []
            
            for i in range(self.config.quilt.total_views):
                view_img = self._create_test_view(i, pattern)
                view_images.append(view_img)
            
            return self.arrange_views_into_quilt(view_images)
    
    def _create_seva_test_quilt(self) -> Image.Image:
        """
        Create a test quilt using seva's rendering capabilities.
        This generates more realistic test images with proper camera projections.
        """
        if not SEVA_AVAILABLE:
            print("SEVA not available, falling back to gradient pattern")
            return self.create_test_quilt("gradient")
        
        try:
            # Use the new SevaQuiltGenerator for better integration
            seva_generator = SevaQuiltGenerator(self.config)
            return seva_generator._create_procedural_quilt()
        except Exception as e:
            print(f"Failed to create SEVA test quilt: {e}")
            print("Falling back to gradient pattern")
            return self.create_test_quilt("gradient")
    
    def _create_test_view(self, view_index: int, pattern: str) -> Image.Image:
        """Create a test view image for the specified index"""
        width = self.config.quilt.view_width
        height = self.config.quilt.view_height
        
        # Create a base image
        img = Image.new("RGB", (width, height), (0, 0, 0))
        
        if pattern == "gradient":
            # Create a gradient pattern
            for y in range(height):
                for x in range(width):
                    # Create a radial gradient from the center
                    center_x, center_y = width // 2, height // 2
                    dx, dy = x - center_x, y - center_y
                    distance = np.sqrt(dx*dx + dy*dy)
                    max_distance = np.sqrt(center_x*center_x + center_y*center_y)
                    
                    # Normalize distance and create gradient
                    norm_distance = min(distance / max_distance, 1.0)
                    
                    # Color based on view index
                    hue = (view_index / self.config.quilt.total_views) * 360
                    saturation = 0.7
                    value = 1.0 - 0.7 * norm_distance
                    
                    r, g, b = self._hsv_to_rgb(hue, saturation, value)
                    img.putpixel((x, y), (r, g, b))
            
        elif pattern == "checkerboard":
            # Create a checkerboard pattern
            cell_size = min(width, height) // 8
            draw = ImageDraw.Draw(img)
            
            for y in range(0, height, cell_size):
                for x in range(0, width, cell_size):
                    # Alternate black and white cells
                    if ((x // cell_size) + (y // cell_size)) % 2 == 0:
                        draw.rectangle([x, y, x + cell_size, y + cell_size], fill=(255, 255, 255))
        
        elif pattern == "numbered":
            # Create a numbered pattern with view index
            draw = ImageDraw.Draw(img)
            
            # Fill with a color based on view index
            hue = (view_index / self.config.quilt.total_views) * 360
            r, g, b = self._hsv_to_rgb(hue, 0.7, 0.9)
            draw.rectangle([0, 0, width, height], fill=(r, g, b))
            
            # Try to load a font
            try:
                # Try to find a font on the system
                font_path = None
                for font_name in ["Arial.ttf", "DejaVuSans.ttf", "FreeSans.ttf"]:
                    try:
                        from PIL import ImageFont
                        font_path = ImageFont.truetype(font_name, size=width//4)
                        break
                    except IOError:
                        continue
                
                # Draw the view number
                if font_path:
                    draw.text(
                        (width//2, height//2),
                        str(view_index),
                        font=font_path,
                        fill=(255, 255, 255),
                        anchor="mm"
                    )
                else:
                    # Fallback to simple text
                    draw.text(
                        (width//2 - 10, height//2 - 10),
                        str(view_index),
                        fill=(255, 255, 255)
                    )
            except Exception as e:
                # If text drawing fails, draw a simple shape
                print(f"Warning: Could not draw text: {e}")
                draw.rectangle([width//3, height//3, 2*width//3, 2*height//3], fill=(255, 255, 255))
        
        elif pattern == "seva":
            # Create a SEVA-style pattern (fallback to numbered if SEVA not available)
            if SEVA_AVAILABLE:
                # Use the SevaQuiltGenerator's method
                seva_generator = SevaQuiltGenerator(self.config)
                img_array = seva_generator._render_seva_style_view(view_index, width, height)
                img = Image.fromarray(img_array)
            else:
                # Fallback to numbered pattern
                draw = ImageDraw.Draw(img)
                hue = (view_index / self.config.quilt.total_views) * 360
                r, g, b = self._hsv_to_rgb(hue, 0.7, 0.9)
                draw.rectangle([0, 0, width, height], fill=(r, g, b))
        
        else:
            # Default pattern - just use view index as color
            hue = (view_index / self.config.quilt.total_views) * 360
            r, g, b = self._hsv_to_rgb(hue, 0.7, 0.9)
            draw = ImageDraw.Draw(img)
            draw.rectangle([0, 0, width, height], fill=(r, g, b))
        
        # Add view index overlay
        draw = ImageDraw.Draw(img)
        draw.text((10, 10), f"View {view_index}", fill=(255, 255, 255))
        
        # Resize if needed
        if img.size != (width, height):
            # Handle different versions of PIL
            try:
                # For newer PIL versions
                resample = Image.Resampling.LANCZOS
            except AttributeError:
                # For older PIL versions
                resample = LANCZOS
                
            img = img.resize((width, height), resample)
        
        return img
    
    def _hsv_to_rgb(self, h: float, s: float, v: float) -> np.ndarray:
        """Convert HSV to RGB color."""
        h = h / 60.0
        c = v * s
        x = c * (1 - abs((h % 2) - 1))
        m = v - c
        
        if 0 <= h < 1:
            r, g, b = c, x, 0
        elif 1 <= h < 2:
            r, g, b = x, c, 0
        elif 2 <= h < 3:
            r, g, b = 0, c, x
        elif 3 <= h < 4:
            r, g, b = 0, x, c
        elif 4 <= h < 5:
            r, g, b = x, 0, c
        else:
            r, g, b = c, 0, x
        
        return np.array([(r + m) * 255, (g + m) * 255, (b + m) * 255], dtype=np.uint8)
    
    def save_quilt_image(
        self,
        quilt_image: Image.Image,
        filename: Optional[str] = None,
        output_dir: str = "exported_trajectories"
    ) -> str:
        """
        Save quilt image with proper naming convention.
        
        Args:
            quilt_image: Quilt image to save
            filename: Custom filename (auto-generated if None)
            output_dir: Output directory
            
        Returns:
            Path to saved file
        """
        if filename is None:
            filename = self.config.quilt.get_filename_pattern("lookingglass")
        
        # Ensure filename has .png extension
        if not filename.lower().endswith('.png'):
            filename += '.png'
        
        output_path = Path(output_dir)
        output_path.mkdir(exist_ok=True)
        
        file_path = output_path / filename
        quilt_image.save(file_path, 'PNG')
        
        return str(file_path)


class FastAPIQuiltGenerator:
    """
    Generates quilt images by calling the FastAPI trajectory service
    to generate images from camera trajectories.
    """
    
    def __init__(
        self,
        fastapi_url: str = "http://localhost:8000",
        camera_model: Optional[QuiltCameraModel] = None
    ):
        """
        Initialize the FastAPI quilt generator.
        
        Args:
            fastapi_url: Base URL of the FastAPI service
            camera_model: Looking Glass camera model
        """
        self.fastapi_url = fastapi_url
        self.camera_model = camera_model
        
    async def generate_quilt_from_image(
        self,
        image_path: str,
        poses: torch.Tensor,
        fovs: torch.Tensor,
        output_dir: str = "exported_trajectories",
        filename_prefix: str = "lookingglass"
    ) -> Dict[str, Any]:
        """
        Generate a quilt image from a single input image using camera trajectory.
        
        Args:
            image_path: Path to input image
            poses: Camera poses tensor
            fovs: Field of view tensor
            output_dir: Output directory
            filename_prefix: Prefix for output files
            
        Returns:
            Dictionary with generation results
        """
        # Convert poses to camera poses format for FastAPI
        camera_poses = []
        
        for i, (pose, fov) in enumerate(zip(poses, fovs)):
            pose_np = pose.numpy()
            
            # Extract position and rotation from 4x4 matrix
            position = pose_np[:3, 3].tolist()
            
            # Convert rotation matrix to Euler angles (degrees)
            rotation_matrix = pose_np[:3, :3]
            rotation_euler = self._rotation_matrix_to_euler(rotation_matrix)
            rotation_degrees = [np.degrees(angle) for angle in rotation_euler]
            
            # Convert FOV to degrees
            fov_degrees = np.degrees(fov.item())
            
            camera_poses.append({
                "position": position,
                "rotation": rotation_degrees,
                "fov": fov_degrees
            })
        
        # Prepare request data
        request_data = {
            "data_path": image_path,
            "camera_poses": json.dumps(camera_poses),
            "cfg": "4.0,2.0",
            "camera_scale": 2.0,
            "seed": 23,
            "H": self.camera_model.config.quilt.view_height if self.camera_model else 576,
            "W": self.camera_model.config.quilt.view_width if self.camera_model else 576,
            "T": len(camera_poses),
            "num_steps": 50,
            "video_save_fps": 30.0
        }
        
        # Make request to FastAPI
        async with aiohttp.ClientSession() as session:
            async with session.post(
                f"{self.fastapi_url}/generate/custom-trajectory",
                data=request_data
            ) as response:
                if response.status == 200:
                    result = await response.json()
                    job_id = result["job_id"]
                    
                    # Wait for completion
                    return await self._wait_for_completion(job_id, session)
                else:
                    error_text = await response.text()
                    raise Exception(f"FastAPI request failed: {error_text}")
    
    async def _wait_for_completion(
        self,
        job_id: str,
        session: aiohttp.ClientSession,
        timeout: int = 300
    ) -> Dict[str, Any]:
        """Wait for FastAPI job completion."""
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            async with session.get(f"{self.fastapi_url}/status/{job_id}") as response:
                if response.status == 200:
                    status = await response.json()
                    
                    if status["status"] == "completed":
                        return status
                    elif status["status"] == "failed":
                        raise Exception(f"Job failed: {status.get('error', 'Unknown error')}")
                    
                    # Still running, wait and check again
                    await asyncio.sleep(5)
                else:
                    raise Exception(f"Status check failed: {response.status}")
        
        raise Exception(f"Job {job_id} timed out after {timeout} seconds")
    
    def _rotation_matrix_to_euler(self, R: np.ndarray) -> Tuple[float, float, float]:
        """Convert rotation matrix to Euler angles (XYZ order)."""
        sy = np.sqrt(R[0, 0] * R[0, 0] + R[1, 0] * R[1, 0])
        
        singular = sy < 1e-6
        
        if not singular:
            x = np.arctan2(R[2, 1], R[2, 2])  # pitch
            y = np.arctan2(-R[2, 0], sy)      # yaw
            z = np.arctan2(R[1, 0], R[0, 0])  # roll
        else:
            x = np.arctan2(-R[1, 2], R[1, 1])  # pitch
            y = np.arctan2(-R[2, 0], sy)       # yaw
            z = 0                              # roll
        
        return x, y, z


def create_test_quilt(
    device_type: str = "portrait",
    pattern: str = "gradient",
    output_dir: str = "exported_trajectories"
) -> str:
    """
    Create a test quilt image for the specified device.
    
    Args:
        device_type: Device configuration to use
        pattern: Test pattern type ("gradient", "checkerboard", "numbered", "seva")
        output_dir: Output directory
        
    Returns:
        Path to saved quilt image
    """
    from .lookingglass_config import get_device_config, list_available_devices
    
    # Normalize device type name
    device_type = device_type.lower().replace(" ", "_").replace("\"", "").replace("'", "")
    
    # Handle device name variations
    if "portrait" in device_type:
        device_type = "portrait"
    elif "go" in device_type:
        device_type = "go"
    elif "16" in device_type and "landscape" in device_type:
        device_type = "16_landscape"
    elif "16" in device_type and "portrait" in device_type:
        device_type = "16_portrait"
    elif "32" in device_type and "landscape" in device_type:
        device_type = "32_landscape"
    elif "32" in device_type and "portrait" in device_type:
        device_type = "32_portrait"
    elif "65" in device_type:
        device_type = "65"
    
    # Validate device type
    available_devices = list_available_devices()
    if device_type not in available_devices:
        print(f"Warning: Unknown device type '{device_type}'. Using 'portrait' instead.")
        print(f"Available devices: {available_devices}")
        device_type = "portrait"
    
    config = get_device_config(device_type)
    generator = QuiltImageGenerator(config)
    
    quilt_image = generator.create_test_quilt(pattern)
    output_path = generator.save_quilt_image(
        quilt_image,
        filename=f"test_{pattern}_{device_type}",
        output_dir=output_dir
    )
    
    return output_path


def generate_quilt_preview(
    camera_model: QuiltCameraModel,
    poses: torch.Tensor,
    output_dir: str = "exported_trajectories"
) -> str:
    """
    Generate a preview quilt showing camera positions.
    
    Args:
        camera_model: Looking Glass camera model
        poses: Camera poses tensor
        output_dir: Output directory
        
    Returns:
        Path to saved preview image
    """
    generator = QuiltImageGenerator(camera_model.config)
    
    # Create preview images for each camera position
    view_images = []
    
    for i, pose in enumerate(poses):
        # Create a simple visualization of the camera position
        # This could be enhanced to show actual 3D rendering
        view_img = generator._create_test_view(i, "numbered")
        view_images.append(view_img)
    
    # Arrange into quilt
    preview_quilt = generator.arrange_views_into_quilt(view_images)
    
    # Save preview
    filename = f"quilt_preview_{camera_model.config.quilt.columns}x{camera_model.config.quilt.rows}"
    output_path = generator.save_quilt_image(
        preview_quilt,
        filename=filename,
        output_dir=output_dir
    )
    
    return output_path 


def create_quilt_from_seva_preview(
    device_type: str = "portrait",
    render_output_dir: str = "exported_trajectories",
    output_dir: str = "exported_trajectories"
) -> str:
    """
    Create a Looking Glass quilt from seva's Preview render output.
    
    Usage:
    1. Generate a camera trajectory in the trajectory app
    2. Use seva's 'Preview render' button to render the trajectory
    3. Call this function to create a quilt from the rendered images
    
    A quilt unit consists of multiple keyframes arranged in a rows×columns grid layout
    according to Looking Glass specifications. For example, a Portrait device uses
    a 6×8 grid, resulting in 48 keyframes per quilt unit.
    
    Args:
        device_type: Device configuration to use
        render_output_dir: Directory containing seva's rendered images
        output_dir: Output directory for the quilt
        
    Returns:
        Path to saved quilt image
    """
    from .lookingglass_config import get_device_config
    
    # Normalize device type name
    device_type = device_type.lower().replace(" ", "_").replace("\"", "").replace("'", "")
    
    # Handle device name variations
    if "portrait" in device_type:
        device_type = "portrait"
    elif "16" in device_type:
        device_type = "16_landscape"
    elif "32" in device_type:
        device_type = "32_landscape"
    elif "65" in device_type:
        device_type = "65"
    else:
        # Default to portrait
        device_type = "portrait"
    
    # Get device configuration
    try:
        config = get_device_config(device_type)
    except ValueError:
        print(f"Unknown device type: {device_type}, using portrait")
        config = get_device_config("portrait")
        
    # Create quilt generator
    quilt_generator = QuiltImageGenerator(config)
    
    # Find rendered images
    render_dir = Path(render_output_dir)
    rendered_images = sorted(list(render_dir.glob("*.png")))
    
    if not rendered_images:
        print(f"No rendered images found in {render_dir}")
        return ""
        
    # Load images
    view_images = []
    for img_path in rendered_images:
        try:
            img = Image.open(img_path)
            view_images.append(img)
        except Exception as e:
            print(f"Error loading {img_path}: {e}")
            
    if not view_images:
        print("No valid images found")
        return ""
        
    # Check if we have enough images for a quilt
    required_views = config.quilt.total_views
    rows = config.quilt.rows
    cols = config.quilt.columns
    
    print(f"Device {config.device_name} requires {required_views} views in a {rows}×{cols} grid layout")
    
    if len(view_images) < required_views:
        print(f"Warning: Not enough images for a complete quilt ({len(view_images)}/{required_views})")
        # Duplicate last image to fill the quilt
        while len(view_images) < required_views:
            view_images.append(view_images[-1])
    elif len(view_images) > required_views:
        print(f"Warning: Too many images for a quilt ({len(view_images)}/{required_views})")
        # Truncate to required views
        view_images = view_images[:required_views]
        
    # Create quilt
    quilt_image = quilt_generator.arrange_views_into_quilt(view_images)
    
    # Save quilt
    quilt_path = quilt_generator.save_quilt_image(quilt_image, output_dir=output_dir)
    print(f"Saved quilt image to {quilt_path}")
    
    return quilt_path 