"""
SEVA-Integrated Quilt Trajectory Manager

This module provides quilt trajectory generation and management with SEVA support,
following the patterns established in seva/gui.py for consistent UI and interaction.
"""

import numpy as np
import torch
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
from pathlib import Path

import viser
import viser.transforms as vt

# Import Looking Glass components
from .lookingglass_config import LookingGlassConfig
from .quilt_camera import QuiltCameraModel


def opencv_to_viser_rotation(opencv_rotation_matrix):
    """
    Convert OpenCV rotation matrix to viser format for visualization.
    
    In QuiltCameraModel._look_at_matrix, we use -forward to follow right-handed 
    coordinate system convention, but this causes cameras to visually point
    away from scene in viser visualization.
    
    For visualization purposes ONLY, we need to correct this by ensuring frustums
    point toward the scene center. This function doesn't change the mathematical
    correctness of the camera model, only how it appears in the visualization.
    
    Note: This is purely a visualization fix. The camera model itself remains
    mathematically consistent with the coordinate system conventions.
    """
    # For visualization correctness: apply a 180-degree rotation around Y axis
    # to make frustums point toward the scene visually, while maintaining the
    # mathematical correctness of the camera model.
    correction = np.array([
        [-1, 0, 0],
        [0, 1, 0],
        [0, 0, -1]
    ])
    
    # Apply correction to rotation matrix
    viser_rotation = opencv_rotation_matrix @ correction
    
    return viser_rotation

# Try to import SEVA components
try:
    from seva.gui import Keyframe, CameraTrajectory
    from seva.geometry import get_preset_pose_fov
    SEVA_AVAILABLE = True
except ImportError:
    SEVA_AVAILABLE = False
    print("Warning: SEVA not available, using simplified trajectory generation")
    # Define fallback types when SEVA is not available
    Keyframe = None
    CameraTrajectory = None


@dataclass
class QuiltKeyframe:
    """Keyframe for Looking Glass quilt generation, extending SEVA's Keyframe concept"""
    position: np.ndarray
    wxyz: np.ndarray
    fov: float
    aspect: float
    unit_id: int
    view_id: int
    row: int
    col: int
    
    @classmethod
    def from_seva_keyframe(cls, keyframe, unit_id: int, view_id: int, row: int, col: int):
        """Create QuiltKeyframe from SEVA Keyframe"""
        return cls(
            position=keyframe.position,
            wxyz=keyframe.wxyz,
            fov=keyframe.override_fov_rad if keyframe.override_fov_enabled else keyframe.override_fov_rad,
            aspect=keyframe.aspect,
            unit_id=unit_id,
            view_id=view_id,
            row=row,
            col=col
        )
    
    def to_seva_keyframe(self):
        """Convert to SEVA Keyframe"""
        if SEVA_AVAILABLE and Keyframe is not None:
            return Keyframe(
                position=self.position,
                wxyz=self.wxyz,
                override_fov_enabled=True,
                override_fov_rad=self.fov,
                aspect=self.aspect,
                override_transition_enabled=False,
                override_transition_sec=None
            )
        else:
            # Return a dict if SEVA not available
            return {
                "position": self.position,
                "wxyz": self.wxyz,
                "fov": self.fov,
                "aspect": self.aspect
            }


class QuiltTrajectoryManager:
    """Manages quilt trajectory generation with SEVA integration"""
    
    def __init__(self, server: viser.ViserServer, quilt_config: LookingGlassConfig, scene_scale: float = 1.0):
        self.server = server
        self.quilt_config = quilt_config
        self.scene_scale = scene_scale
        self.camera_model = QuiltCameraModel(quilt_config)
        
        # Visibility state
        self.keyframes_visible = True
        
        # SEVA integration
        self.seva_trajectory = None
        if SEVA_AVAILABLE and CameraTrajectory is not None:
            # Create a SEVA CameraTrajectory for managing keyframes
            duration_element = server.gui.add_number("Duration", initial_value=3.0, visible=False)
            self.seva_trajectory = CameraTrajectory(
                server=server,
                duration_element=duration_element,
                scene_scale=scene_scale,
                scene_node_prefix="/quilt_trajectory"
            )
        
        # Quilt units storage
        self.quilt_units: List[Dict[str, Any]] = []
        self.unit_nodes: List[viser.SceneNodeHandle] = []
        self.current_unit: Optional[int] = None
        
    def generate_quilt_unit(self, center_position: np.ndarray, center_rotation: np.ndarray, 
                           look_at: np.ndarray, unit_id: int) -> Dict[str, Any]:
        """Generate a single quilt unit with SEVA-style keyframes"""
        
        rows = self.quilt_config.quilt.rows
        cols = self.quilt_config.quilt.columns
        num_views = rows * cols
        
        # Generate quilt trajectory using Looking Glass camera model
        try:
            target_c2ws_tensor, target_fovs_tensor = self.camera_model.generate_quilt_trajectory(
                center_position=center_position,
                look_at=look_at,
                up_direction=np.array([0.0, 1.0, 0.0])
            )
            
            target_c2ws = target_c2ws_tensor.numpy()
            target_fovs = target_fovs_tensor.numpy()
            
        except Exception as e:
            print(f"Warning: Using fallback trajectory generation: {e}")
            # Fallback: simple grid pattern
            target_c2ws, target_fovs = self._generate_fallback_grid(
                center_position, center_rotation, num_views
            )
        
        # Create QuiltKeyframes
        quilt_keyframes = []
        for i in range(num_views):
            c2w = target_c2ws[i]
            fov = target_fovs[i]
            
            row = i // cols
            col = i % cols
            
            # Convert OpenCV rotation to viser-compatible format
            opencv_rotation = c2w[:3, :3]
            viser_rotation = opencv_to_viser_rotation(opencv_rotation)
            
            quilt_keyframe = QuiltKeyframe(
                position=c2w[:3, 3],
                wxyz=vt.SO3.from_matrix(viser_rotation.astype(np.float32)).wxyz,
                fov=float(fov),
                aspect=self.quilt_config.quilt.aspect_ratio,
                unit_id=unit_id,
                view_id=i,
                row=row,
                col=col
            )
            quilt_keyframes.append(quilt_keyframe)
        
        # Create unit data structure
        quilt_unit = {
            "id": unit_id,
            "center_position": center_position.tolist(),
            "center_rotation": center_rotation.tolist(),
            "keyframes": quilt_keyframes,
            "rows": rows,
            "columns": cols,
            "look_at": look_at.tolist(),
            "generated_video": None,
            "quilt_image": None
        }
        
        return quilt_unit
    
    def _generate_fallback_grid(self, center_position: np.ndarray, center_rotation: np.ndarray, 
                               num_views: int) -> Tuple[np.ndarray, np.ndarray]:
        """Fallback grid generation when SEVA is not available"""
        target_c2ws = []
        target_fovs = []
        
        rows = self.quilt_config.quilt.rows
        cols = self.quilt_config.quilt.columns
        camera_separation = 0.02  # Small separation for holographic viewing
        
        # Calculate grid offsets
        row_offsets = np.linspace(-(rows-1)/2, (rows-1)/2, rows) * camera_separation
        col_offsets = np.linspace(-(cols-1)/2, (cols-1)/2, cols) * camera_separation
        
        for row in range(rows):
            for col in range(cols):
                # Calculate camera offset
                offset_x = col_offsets[col]
                offset_y = row_offsets[row] * 0.1  # Minimal vertical offset
                offset_z = 0.0
                
                # Apply offset relative to center camera's orientation
                offset_world = center_rotation @ np.array([offset_x, offset_y, offset_z])
                camera_position = center_position + offset_world
                
                # Create camera-to-world matrix
                c2w = np.eye(4)
                c2w[:3, :3] = center_rotation
                c2w[:3, 3] = camera_position
                
                target_c2ws.append(c2w)
                target_fovs.append(np.radians(self.quilt_config.camera.fov_degrees))
        
        return np.array(target_c2ws), np.array(target_fovs)
    
    def add_quilt_unit(self, center_position: np.ndarray, center_rotation: np.ndarray, 
                      look_at: np.ndarray) -> int:
        """Add a new quilt unit and return its ID
        
        Args:
            center_position: Camera position in world coordinates
            center_rotation: Camera rotation matrix (OpenCV convention)
            look_at: Target look-at point
            
        Returns:
            Unit ID of the added quilt unit
        """
        # Always assume center_rotation is in OpenCV convention (from dust3r or UI)
        # Do NOT convert to viser format here - this was causing the direction inversion
        # The conversion should only happen for visualization, not for trajectory generation
        
        # Debug: Check the forward direction in the OpenCV rotation matrix
        forward_opencv = center_rotation[:, 2]  # Z column is forward in OpenCV
        look_dir = (look_at - center_position) / np.linalg.norm(look_at - center_position)
        print(f"Adding quilt unit: pos={center_position}, look_at={look_at}, forward={forward_opencv}")
        # Uncomment for detailed debugging:
        # print(f"[DEBUG] OpenCV forward direction: {forward_opencv}")
        # print(f"[DEBUG] Look direction (look_at - center): {look_dir}")
        
        unit_id = len(self.quilt_units)
        quilt_unit = self.generate_quilt_unit(center_position, center_rotation, look_at, unit_id)
        self.quilt_units.append(quilt_unit)
        
        # Add SEVA keyframes if available
        if SEVA_AVAILABLE and self.seva_trajectory:
            for keyframe in quilt_unit["keyframes"]:
                seva_keyframe = keyframe.to_seva_keyframe()
                self.seva_trajectory.add_camera(seva_keyframe)
        
        return unit_id
    
    def update_config(self, new_config: LookingGlassConfig) -> None:
        """Update the quilt configuration with new settings
        
        This ensures that all new units will use the updated configuration.
        
        Args:
            new_config: New Looking Glass configuration
        """
        self.quilt_config = new_config
        self.camera_model = QuiltCameraModel(new_config)
        print(f"Updated quilt configuration to {new_config.device_name}")
        
        # Visualize existing units with new configuration
        if self.quilt_units:
            self.visualize_quilt_units()
    
    def visualize_quilt_units(self) -> None:
        """Visualize quilt units using SEVA-style camera frustums"""
        # Check if we need to recreate visualizations or just update existing ones
        if len(self.unit_nodes) == len(self.quilt_units):
            # Update existing visualizations
            for i, (unit, node) in enumerate(zip(self.quilt_units, self.unit_nodes)):
                if isinstance(node, viser.CameraFrustumHandle):
                    # Update position and scale
                    node.position = tuple(unit["center_position"])
                    node.scale = 0.15 * self.scene_scale
            return
        
        # Clear existing unit center visualizations only if count mismatch
        for node in self.unit_nodes:
            node.remove()
        self.unit_nodes.clear()
        
        # Visualize each quilt unit center (only the center, not individual keyframes)
        for unit_idx, quilt_unit in enumerate(self.quilt_units):
            center_position = np.array(quilt_unit["center_position"])
            look_at = np.array(quilt_unit["look_at"])
            
            # Calculate proper center camera orientation using camera model
            center_pose_matrix = self.camera_model._look_at_matrix(
                center_position, look_at, np.array([0.0, 1.0, 0.0])
            )
            center_rotation_matrix = center_pose_matrix[:3, :3]
            
            # Convert OpenCV rotation to viser-compatible format for center frustum
            center_viser_rotation = opencv_to_viser_rotation(center_rotation_matrix)
            
            # Add center frustum (following SEVA patterns)
            unit_center = self.server.scene.add_camera_frustum(
                f"/quilt_units/unit_{unit_idx}_center",
                fov=np.radians(self.quilt_config.camera.fov_degrees),
                aspect=1.0,
                scale=0.15 * self.scene_scale,
                color=(200, 10, 200),  # Purple for unit centers
                wxyz=vt.SO3.from_matrix(center_viser_rotation.astype(np.float32)).wxyz,
                position=center_position,
            )
            self.unit_nodes.append(unit_center)
            
            # Add click handler for center frustum
            @unit_center.on_click
            def handle_center_click(event) -> None:
                print(f"Selected unit {unit_idx} center")
                
                # Move camera to center position with proper look-at setting
                if event.client is not None:
                    client = event.client
                    
                    # Use the same approach as demo_gr.py for consistent camera direction
                    with client.atomic():
                        client.camera.position = center_position
                        client.camera.wxyz = vt.SO3.from_matrix(center_viser_rotation.astype(np.float32)).wxyz
                        
                        # Set look_at as the projected origin onto the frustum's forward direction
                        look_direction = vt.SO3(client.camera.wxyz).as_matrix()[:, 2]
                        position_origin = -center_position  # Vector from camera to origin
                        client.camera.look_at = (
                            center_position
                            + np.dot(look_direction, position_origin)
                            / np.linalg.norm(position_origin)
                            * look_direction
                        )
    
    def set_keyframes_visible(self, visible: bool) -> None:
        """Set visibility of all quilt keyframes (following SEVA patterns)"""
        self.keyframes_visible = visible
        
        # Set unit center frustums visibility
        for node in self.unit_nodes:
            node.visible = visible
            
        # Set SEVA keyframes visibility if available
        if SEVA_AVAILABLE and self.seva_trajectory:
            self.seva_trajectory.set_keyframes_visible(visible)
    
    def update_scene_scale(self, new_scale: float) -> None:
        """Update scene scale for all visualizations"""
        if new_scale == self.scene_scale:
            return
            
        scale_ratio = new_scale / self.scene_scale if self.scene_scale != 0 else 1.0
        self.scene_scale = new_scale
        
        # Update SEVA trajectory scale if available
        if SEVA_AVAILABLE and self.seva_trajectory:
            # Update the SEVA trajectory's scene_scale property
            self.seva_trajectory.scene_scale = new_scale
            
            # Batch update SEVA keyframes to reduce individual updates
            if hasattr(self.seva_trajectory, '_keyframes') and self.seva_trajectory._keyframes:
                # Store original keyframes for batch update
                keyframes_to_update = []
                for keyframe_index, (keyframe, frustum) in self.seva_trajectory._keyframes.items():
                    # Scale the position relative to origin
                    keyframe.position = keyframe.position * scale_ratio
                    keyframes_to_update.append((keyframe_index, keyframe))
                
                # Batch update all keyframes
                for keyframe_index, keyframe in keyframes_to_update:
                    self.seva_trajectory.add_camera(keyframe, keyframe_index=keyframe_index)
        
        # Update quilt unit data: scale positions of all keyframes
        for unit in self.quilt_units:
            # Scale the unit center position
            unit["center_position"] = (np.array(unit["center_position"]) * scale_ratio).tolist()
            
            # Scale the look_at position
            unit["look_at"] = (np.array(unit["look_at"]) * scale_ratio).tolist()
            
            # Scale all keyframe positions in this unit
            for keyframe in unit["keyframes"]:
                keyframe.position = keyframe.position * scale_ratio
        
        # Update unit center frustums efficiently by directly updating properties
        for i, node in enumerate(self.unit_nodes):
            if hasattr(node, 'scale') and isinstance(node, viser.CameraFrustumHandle):
                # Update scale
                node.scale = 0.15 * new_scale
                # Update position if corresponding unit exists
                if i < len(self.quilt_units):
                    node.position = tuple(self.quilt_units[i]["center_position"])
        
        # Only re-visualize if we have keyframes visible
        if self.keyframes_visible:
            self._update_keyframe_visualizations()
    
    def get_all_keyframes(self) -> List[QuiltKeyframe]:
        """Get all keyframes from all quilt units"""
        all_keyframes = []
        for unit in self.quilt_units:
            all_keyframes.extend(unit["keyframes"])
        return all_keyframes
    
    def export_for_seva(self) -> List[Dict[str, Any]]:
        """Export quilt data in SEVA-compatible format"""
        seva_data = []
        for keyframe in self.get_all_keyframes():
            if SEVA_AVAILABLE and Keyframe is not None:
                # Handle both SEVA Keyframe objects and dict fallbacks
                seva_keyframe = keyframe.to_seva_keyframe()
                if isinstance(seva_keyframe, dict):
                    # Dict fallback when SEVA not available
                    seva_data.append({
                        "position": seva_keyframe["position"].tolist(),
                        "wxyz": seva_keyframe["wxyz"].tolist(),
                        "fov": seva_keyframe["fov"],
                        "aspect": seva_keyframe["aspect"],
                        "unit_id": keyframe.unit_id,
                        "view_id": keyframe.view_id
                    })
                else:
                    # SEVA Keyframe object
                    seva_data.append({
                        "position": seva_keyframe.position.tolist(),
                        "wxyz": seva_keyframe.wxyz.tolist(),
                        "fov": seva_keyframe.override_fov_rad,
                        "aspect": seva_keyframe.aspect,
                        "unit_id": keyframe.unit_id,
                        "view_id": keyframe.view_id
                    })
            else:
                # Direct keyframe export when SEVA not available
                seva_data.append({
                    "position": keyframe.position.tolist(),
                    "wxyz": keyframe.wxyz.tolist(),
                    "fov": keyframe.fov,
                    "aspect": keyframe.aspect,
                    "unit_id": keyframe.unit_id,
                    "view_id": keyframe.view_id
                })
        return seva_data
    
    def clear_all_units(self) -> None:
        """Clear all quilt units and visualizations"""
        # Clear unit center visualizations
        for node in self.unit_nodes:
            node.remove()
        self.unit_nodes.clear()
        
        # Clear data
        self.quilt_units.clear()
        self.current_unit = None
        
        # Clear SEVA trajectory if available
        if SEVA_AVAILABLE and self.seva_trajectory:
            # Reset the SEVA trajectory to remove all keyframes and frustums
            self.seva_trajectory.reset()
            
            # Create a temporary frame at the root path to ensure all children are removed
            # This fixes the issue with dual frustums persisting after clear
            try:
                # Add temporary frames that will remove all children at these paths
                temp_frame1 = self.server.scene.add_frame("/quilt_trajectory", visible=False)
                temp_frame2 = self.server.scene.add_frame("/quilt_units", visible=False)
                
                # Remove the temporary frames (which removes all children)
                temp_frame1.remove()
                temp_frame2.remove()
            except:
                pass  # Frames might not exist or removal might fail

    def handle_clear_all_units_button(self) -> None:
        """Handle the 'Clear All Units' button click
        
        This method ensures all frustums are properly removed, including:
        1. QuiltTrajectoryManager's unit nodes
        2. SEVA CameraTrajectory frustums
        3. Any orphaned frustums in the scene
        """
        # First use the standard clear method
        self.clear_all_units()
        
        # Additional cleanup for SEVA integration
        if SEVA_AVAILABLE and CameraTrajectory is not None:
            # Force scene cleanup of any remaining camera frustums
            # This ensures red frustums are completely removed
            try:
                # Create a temporary root frame that will clear everything when removed
                temp_root = self.server.scene.add_frame("/", visible=False)
                temp_root.remove()
                
                # Recreate essential scene structure
                self.server.scene.add_frame("/scene_assets", visible=True)
            except:
                pass
            
            # Recreate SEVA trajectory if needed
            if self.seva_trajectory is None:
                duration_element = self.server.gui.add_number("Duration", initial_value=3.0, visible=False)
                self.seva_trajectory = CameraTrajectory(
                    server=self.server,
                    duration_element=duration_element,
                    scene_scale=self.scene_scale,
                    scene_node_prefix="/quilt_trajectory"
                )
        
        print("✅ All quilt units and frustums cleared")
    
    def _update_keyframe_visualizations(self) -> None:
        """Update keyframe visualizations without recreating everything"""
        # Only update keyframe frustums if they are visible
        if not self.keyframes_visible:
            return
            
        # Use SEVA trajectory to handle keyframe visualization updates efficiently
        if SEVA_AVAILABLE and self.seva_trajectory:
            # SEVA will handle the individual keyframe updates
            pass
            
        # For quilt keyframes, we can batch update their positions
        for unit in self.quilt_units:
            for keyframe in unit["keyframes"]:
                # The keyframe positions have already been updated above
                # The visualization will pick up the new positions automatically
                pass