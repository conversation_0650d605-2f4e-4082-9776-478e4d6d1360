"""
Trajectory Tool Managers

This module contains the manager classes for the trajectory tool:
- TestObjectManager: Manages test objects in the 3D scene
- TrajectoryManager: Manages trajectory generation
- CameraAnimator: Handles camera animation
- TrajectoryExporter: Handles exporting trajectory data
- LookingGlassManager: Manages Looking Glass functionality (if available)
- TrajectoryState: Manages state for trajectory visualization
- QuiltState: Manages state for Looking Glass quilt generation
"""

import numpy as np
import torch
import json
import os
import time
import threading
from typing import List, Dict, Any, Tuple, Optional, Union, Literal
from pathlib import Path

import viser
import viser.transforms as vt

# Try to import Looking Glass components (optional)
try:
    from .lookingglass_config import (
        LookingGlassConfig, get_default_config, get_device_config, 
        list_available_devices, DEVICE_PRESETS
    )
    from .quilt_camera import QuiltCameraModel
    from .quilt_generator import QuiltImageGenerator
    from .quilt_trajectory import QuiltTrajectoryManager
    from .quilt_gui import QuiltGuiManager
    LOOKING_GLASS_AVAILABLE = True
except ImportError:
    print("Looking Glass modules not available. Basic trajectory functionality only.")
    LOOKING_GLASS_AVAILABLE = False

# Try to import seva functions
try:
    from seva.geometry import get_preset_pose_fov, DEFAULT_FOV_RAD
    from seva.gui import define_gui, GuiState
    SEVA_AVAILABLE = True
except ImportError as e:
    import traceback
    print("SEVA modules not available. Using basic trajectory generation only.")
    print("ImportError details:", e)
    traceback.print_exc()
    SEVA_AVAILABLE = False
    # Define fallbacks for missing imports
    from .lookingglass_config import get_default_config
    # Use the FOV from Looking Glass config instead of hardcoding
    DEFAULT_FOV_RAD = np.radians(get_default_config().camera.fov_degrees)
    
    # Define a simple fallback for get_preset_pose_fov
    def fallback_get_preset_pose_fov(option, num_frames, start_w2c, look_at, up_direction, fov, **kwargs):
        """Fallback implementation when SEVA is not available"""
        # Simple orbit trajectory
        poses = []
        fovs = []
        for i in range(num_frames):
            angle = 2 * np.pi * i / num_frames
            # Simple circular orbit
            x = np.cos(angle) * 2.0
            z = np.sin(angle) * 2.0
            y = 0.0
            
            # Create a camera matrix looking at the center
            pos = np.array([x, y, z])
            forward = -pos / np.linalg.norm(pos)  # Look toward origin
            right = np.cross(up_direction.numpy(), forward)
            right = right / np.linalg.norm(right)
            up = np.cross(forward, right)
            
            # Create rotation matrix
            rot = np.stack([right, up, forward], axis=1)
            
            # Create camera-to-world matrix
            c2w = np.eye(4)
            c2w[:3, :3] = rot
            c2w[:3, 3] = pos
            
            poses.append(c2w)
            fovs.append(fov)
            
        return torch.tensor(poses), torch.tensor(fovs)
    
    # Use the fallback function
    get_preset_pose_fov = fallback_get_preset_pose_fov

    # Define empty class as placeholder
    class FallbackGuiState:
        """Fallback implementation when SEVA is not available"""
        pass
    
    # Use the fallback class
    GuiState = FallbackGuiState


class TrajectoryState:
    """Manages state for trajectory visualization and camera control"""
    
    def __init__(self):
        # Basic scene information
        self.input_wh: Optional[Tuple[int, int]] = None
        self.scene_scale: float = 1.0
        self.data_path: Optional[str] = None
        self.all_points: List = []
        
        # Camera trajectory
        self.camera_traj_list: List[Dict[str, Any]] = []
        
        # UI controls
        self.quilt_controls: Dict[str, Any] = {}


class QuiltState:
    """Manages state for Looking Glass quilt generation"""
    
    def __init__(self):
        # Basic scene information
        self.input_wh: Optional[Tuple[int, int]] = None
        self.scene_scale: float = 1.0
        self.data_path: Optional[str] = None
        self.all_points: List = []
        
        # Input camera poses from Dust3R reconstruction
        self.input_camera_poses: List[Dict[str, Any]] = []  # Predicted poses for input images
        self.input_camera_frustums: List = []  # Viser frustum nodes for input cameras
        
        # Quilt units - one per input camera pose
        self.quilt_units: List[Dict[str, Any]] = []  # Each unit has rows×columns views around one input pose
        self.current_unit: Optional[int] = None  # Currently selected unit
        
        # UI state
        self.quilt_mode: bool = False
        self.hide_input_images: bool = False  # Whether to hide input image frustums
        self.unit_nodes: List = []  # Visualization nodes for quilt units
        self.camera_traj_list: List[Dict[str, Any]] = []  # For compatibility with TrajectoryState
        self.quilt_controls: Dict[str, Any] = {}  # UI controls for quilt generation
        
        # View positions for quilt generation
        self.view_positions: List[Dict[str, Any]] = []  # Camera positions for quilt views


class TestObjectManager:
    """Manages test objects in the 3D scene with auto-centering"""
    
    def __init__(self, server: viser.ViserServer):
        self.server = server
        self.test_object_node: Optional[viser.SceneNodeHandle] = None
        self.current_position = np.array([0.0, 0.0, 0.0])
        self.current_type = "sphere"
        self.visible = True
        
    def update_test_object(self, position: np.ndarray, object_type: str = "sphere", 
                          visible: bool = True, color: tuple = (255, 100, 100)):
        """Update or create test object at specified position"""
        # Remove existing object
        if self.test_object_node is not None:
            self.test_object_node.remove()
            self.test_object_node = None
        
        if not visible:
            return
            
        self.current_position = position.copy()
        self.current_type = object_type
        self.visible = visible
        
        # Create new test object
        if object_type == "sphere":
            self.test_object_node = self.server.scene.add_icosphere(
                "/test_object",
                radius=0.15,
                color=color,
                position=position
            )
        elif object_type == "cube":
            self.test_object_node = self.server.scene.add_box(
                "/test_object",
                dimensions=(0.3, 0.3, 0.3),
                color=color,
                position=position
            )
        elif object_type == "arrow":
            # Create exactly two positions with exactly three float values each
            pos1 = (float(position[0]), float(position[1]), float(position[2] - 0.2))
            pos2 = (float(position[0]), float(position[1]), float(position[2] + 0.2))
            # This creates a tuple of exactly two tuples, each with exactly three floats
            positions = (pos1, pos2)
            
            self.test_object_node = self.server.scene.add_spline_catmull_rom(
                "/test_object",
                positions=positions,
                color=color,
                line_width=5.0
            )
        
        print(f"Updated test object ({object_type}) at position: {position}")

    def auto_center_to_trajectory(self, trajectory_center: np.ndarray):
        """Automatically center test object at trajectory center"""
        self.update_test_object(
            trajectory_center, 
            self.current_type, 
            self.visible
        )
        print(f"Auto-centered test object to trajectory center: {trajectory_center}")


class TrajectoryManager:
    """Manages camera trajectories and visualization"""
    
    def __init__(self, server: viser.ViserServer):
        self.server = server
        self.trajectory_nodes = []  # List of trajectory visualization nodes
        self.input_frustums = []    # List of input camera frustum nodes
        self.current_poses = None  # Current camera poses
        self.current_fovs = None  # Current camera FOVs
        self.trajectory_type = None  # Type of trajectory (e.g., "orbit", "quilt")
        self.seva_keyframes = []  # List of keyframes from SEVA
        self.quilt_units = []  # List of quilt units for Looking Glass
        
    def clear_trajectory(self):
        """Clear the current trajectory visualization"""
        for node in self.trajectory_nodes:
            try:
                node.remove()
            except:
                pass
        self.trajectory_nodes = []
        
    def add_seva_keyframe(self, keyframe):
        """Add a SEVA keyframe"""
        self.seva_keyframes.append(keyframe)
        
        # If in quilt mode, automatically update quilt units
        if self.trajectory_type == "quilt" and hasattr(self, 'quilt_units'):
            # This will be handled by the UI's quilt mode checkbox handler
            pass
        
    def clear_seva_keyframes(self):
        """Clear all SEVA keyframes"""
        self.seva_keyframes = []
        
    def visualize_trajectory(self, quilt_mode: bool = False):
        """
        Visualize the current trajectory in the scene
        
        Args:
            quilt_mode: Whether to visualize as quilt units (True) or normal trajectory (False)
        """
        # Clear existing visualization
        self.clear_trajectory()
            
        if quilt_mode and hasattr(self, 'quilt_units') and self.quilt_units:
            # Visualize quilt units (each unit contains rows×columns keyframes in a grid)
            self._visualize_quilt_units()
        elif self.current_poses is not None:
            # Visualize regular trajectory
            if isinstance(self.current_poses, torch.Tensor):
                poses = self.current_poses.detach().cpu().numpy()
            else:
                poses = self.current_poses
                
            if self.current_fovs is not None:
                if isinstance(self.current_fovs, torch.Tensor):
                    fovs = self.current_fovs.detach().cpu().numpy()
                else:
                    fovs = self.current_fovs
            else:
                # Default FOV
                fovs = np.radians(54.0)
                
            self._visualize_trajectory_cameras(poses, fovs)
    
    def _visualize_quilt_units(self):
        """
        Visualize quilt units with proper grid layout
        
        Each quilt unit consists of rows×columns keyframes arranged in a grid pattern
        according to Looking Glass specifications.
        """
        if not hasattr(self, 'quilt_units') or not self.quilt_units:
            return
            
        # For each quilt unit
        for unit_idx, unit in enumerate(self.quilt_units):
            if 'poses' not in unit:
                continue
                
            poses = unit['poses']
            fovs = unit['fovs'] if 'fovs' in unit else np.radians(54.0)
            
            if isinstance(poses, torch.Tensor):
                poses = poses.detach().cpu().numpy()
            if isinstance(fovs, torch.Tensor):
                fovs = fovs.detach().cpu().numpy()
                
            # Get unit center for indicator
            center = unit['center'] if 'center' in unit else poses[0][:3, 3]
            
            # Add unit indicator (small sphere)
            try:
                # Use add_icosphere if available (newer viser versions)
                sphere = self.server.scene.add_icosphere(
                    f"/scene/quilt_units/unit_{unit_idx}/indicator",
                    radius=0.05,
                    position=center,
                    color=(255, 255, 255)
                )
                self.trajectory_nodes.append(sphere)
            except:
                # Fallback if add_icosphere is not available
                pass
            
            # Add cameras with different colors per unit
            hue = unit_idx / max(1, len(self.quilt_units) - 1)
            color = self._hsv_to_rgb(hue, 0.8, 1.0)
            
            # Visualize cameras in this unit with grid structure
            self._visualize_trajectory_cameras(poses, fovs, color=color, unit_idx=unit_idx)
            
            # Add text label using camera frustum
            try:
                label_pos = center + np.array([0.0, 0.1, 0.0])  # Slightly above center
                label_frustum = self.server.scene.add_camera_frustum(
                    f"/scene/quilt_units/unit_{unit_idx}/label",
                    fov=0.5,  # Small FOV for compact frustum
                    aspect=1.0,
                    scale=0.02,
                    color=(255, 255, 255),
                    wxyz=np.array([1.0, 0.0, 0.0, 0.0]),  # Identity rotation
                    position=label_pos
                )
                self.trajectory_nodes.append(label_frustum)
            except:
                pass
    
    def _visualize_trajectory_cameras(self, poses, fovs, color=None, unit_idx=None):
        """Visualize cameras along a trajectory"""
        num_frames = len(poses)
        
        # Default color gradient if not specified
        if color is None:
            color = (255, 100, 100)  # Default red
            
        # Create clickable camera frustums
        for i in range(num_frames):
            c2w = poses[i]
            
            # Extract camera parameters
            if c2w.shape == (4, 4):
                # Handle 4x4 matrix
                position = c2w[:3, 3]
                rotation = c2w[:3, :3]
                wxyz = vt.SO3.from_matrix(rotation).wxyz
            else:
                # Handle flat array (position + quaternion)
                position = c2w[:3]
                # Default quaternion if not available
                wxyz = np.array([1.0, 0.0, 0.0, 0.0])
                
            # Get FOV for this camera (ensure it's a float)
            if isinstance(fovs, (list, np.ndarray)) and len(fovs) > i:
                fov_val = fovs[i]
                fov = float(fov_val) if not isinstance(fov_val, (list, np.ndarray)) else float(fov_val[0] if len(fov_val) > 0 else 0.785)
            elif isinstance(fovs, (int, float)):
                fov = float(fovs)
            else:
                # Fallback to a reasonable default FOV (45 degrees in radians)
                fov = 0.785
                
            # Create node path with unit index if provided
            if unit_idx is not None:
                node_path = f"/scene/quilt_units/unit_{unit_idx}/camera_{i}"
            else:
                node_path = f"/scene/trajectory/camera_{i}"
                
            # Create camera frustum
            frustum = self.server.scene.add_camera_frustum(
                node_path,
                fov=fov,
                aspect=1.0,
                scale=0.05,
                color=color,
                wxyz=wxyz,
                position=position
            )
                
            # Make camera clickable
            frustum.on_click(self._make_click_handler(
                frame_index=i,
                camera_position=position,
                camera_wxyz=wxyz,
                camera_fov=fov
            ))
            
            # Add to trajectory nodes
            self.trajectory_nodes.append(frustum)
    
    def _hsv_to_rgb(self, h, s, v):
        """Convert HSV to RGB color"""
        h = h / 60.0
        c = v * s
        x = c * (1 - abs((h % 2) - 1))
        m = v - c
        
        if 0 <= h < 1:
            r, g, b = c, x, 0
        elif 1 <= h < 2:
            r, g, b = x, c, 0
        elif 2 <= h < 3:
            r, g, b = 0, c, x
        elif 3 <= h < 4:
            r, g, b = 0, x, c
        elif 4 <= h < 5:
            r, g, b = x, 0, c
        else:
            r, g, b = c, 0, x
        
        return (int((r + m) * 255), int((g + m) * 255), int((b + m) * 255))
    
    def _make_click_handler(self, frame_index, camera_position, camera_wxyz, camera_fov):
        def click_handler(_) -> None:
            """Handle camera frustum click - move view to this camera"""
            try:
                # Move all connected clients to this camera view
                for client in self.server.get_clients().values():
                    with client.atomic():
                        client.camera.wxyz = camera_wxyz
                        client.camera.position = camera_position
                        client.camera.fov = float(camera_fov)
                
                print(f"Moved to camera {frame_index}: pos={camera_position.round(3)}, fov={float(camera_fov):.3f}")
                
            except Exception as e:
                print(f"Error moving to camera {frame_index}: {e}")
        
        return click_handler
    def generate_preset_trajectory(self, preset_type: Literal[
        "orbit", "spiral", "lemniscate", "zoom-in", "zoom-out", 
        "dolly zoom-in", "dolly zoom-out", "move-forward", "move-backward", 
        "move-up", "move-down", "move-left", "move-right", "roll"
    ], num_frames: int, 
                                 start_camera_pose: Optional[vt.SE3] = None,
                                 look_at: Optional[np.ndarray] = None,
                                 **kwargs) -> tuple[torch.Tensor, torch.Tensor]:
        """Generate trajectory using seva.geometry.get_preset_pose_fov"""
        # Default start pose (looking at origin from positive Z)
        if start_camera_pose is None:
            start_camera_pose = vt.SE3.from_rotation_and_translation(
                vt.SO3.from_matrix(np.eye(3)),
                np.array([0.0, 0.0, 2.0])
            )
        
        # Default look-at point
        if look_at is None:
            look_at = np.array([0.0, 0.0, 0.0])
        
        # Convert to torch tensors for seva.geometry
        start_w2c = torch.linalg.inv(torch.tensor(start_camera_pose.as_matrix(), dtype=torch.float32))
        look_at_tensor = torch.tensor(look_at, dtype=torch.float32)
        up_direction = torch.tensor([0.0, 1.0, 0.0], dtype=torch.float32)
        
        # Get trajectory from get_preset_pose_fov (either from SEVA or our fallback)
        poses, fovs = get_preset_pose_fov(
            option=preset_type,
            num_frames=num_frames,
            start_w2c=start_w2c,
            look_at=look_at_tensor,
            up_direction=up_direction,
            fov=DEFAULT_FOV_RAD,
            **kwargs
        )
        
        self.current_poses = torch.tensor(poses, dtype=torch.float32)
        self.current_fovs = torch.tensor(fovs, dtype=torch.float32)
        self.trajectory_type = preset_type
        
        # Automatically visualize the trajectory
        self.visualize_trajectory()
        
        return self.current_poses, self.current_fovs
    
    def get_trajectory_center(self, preset_type: str) -> np.ndarray:
        """Get the center point for different trajectory types"""
        if preset_type in ["orbit", "spiral", "lemniscate", "zoom-in", "zoom-out", 
                          "dolly zoom-in", "dolly zoom-out"]:
            return np.array([0.0, 0.0, 0.0])  # Origin-centered trajectories
        elif preset_type.startswith("move-"):
            return np.array([0.0, 0.0, 0.0])  # Movement trajectories center at origin
        elif preset_type == "roll":
            return np.array([0.0, 0.0, 0.0])  # Roll around origin
        else:
            return np.array([0.0, 0.0, 0.0])  # Default to origin


class CameraAnimator:
    """Handles camera animation with proper look-at behavior"""
    
    def __init__(self, server: viser.ViserServer):
        self.server = server
        self.playing = False
        self.animation_thread: Optional[threading.Thread] = None
        
    def start_animation(self, poses: torch.Tensor, fovs: torch.Tensor, 
                       trajectory_center: np.ndarray,
                       fps: float = 10.0, loop: bool = True):
        """Start camera animation with proper look-at behavior"""
        if self.playing:
            self.stop_animation()
            
        self.playing = True
        
        def animate():
            frame = 0
            while self.playing:
                if frame >= len(poses):
                    if loop:
                        frame = 0
                    else:
                        break
                
                # Get pose for this frame
                pose_matrix = poses[frame].numpy()
                fov = fovs[frame].item() if fovs.dim() > 0 else fovs.item()
                
                # Convert pose to SE3
                current_pose = vt.SE3.from_matrix(pose_matrix)
                
                # Update all connected clients
                for client in self.server.get_clients().values():
                    with client.atomic():
                        # Set camera position and orientation
                        client.camera.wxyz = current_pose.rotation().wxyz
                        client.camera.position = current_pose.translation()
                        client.camera.fov = fov
                        
                        # Set look-at to trajectory center (our general solution)
                        client.camera.look_at = trajectory_center
                
                print(f"Animation frame {frame}: pos={current_pose.translation().round(2)}, look_at={trajectory_center}")
                
                time.sleep(1.0 / fps)
                frame += 1
            
            self.playing = False
            print("Animation finished")
        
        self.animation_thread = threading.Thread(target=animate, daemon=True)
        self.animation_thread.start()
        print(f"Started animation: {len(poses)} frames at {fps} FPS")
    
    def stop_animation(self):
        """Stop current animation"""
        self.playing = False
        if self.animation_thread:
            self.animation_thread.join(timeout=1.0)
        print("Animation stopped")


class TrajectoryExporter:
    """Handles exporting trajectory data for FastAPI video inference"""
    
    def __init__(self):
        self.output_dir = "exported_trajectories"
        os.makedirs(self.output_dir, exist_ok=True)
    
    def export_trajectory_for_fastapi(self, poses: torch.Tensor, fovs: torch.Tensor, 
                                     preset_type: str = "custom", 
                                     export_name: Optional[str] = None) -> str:
        """Export trajectory data in format compatible with FastAPI server"""
        if export_name is None:
            export_name = f"trajectory_{preset_type}_{int(time.time())}"
        
        # Convert poses to camera poses format expected by FastAPI
        camera_poses = []
        
        for i, (pose_matrix, fov) in enumerate(zip(poses, fovs)):
            # Convert from camera-to-world matrix to position and rotation
            pose_np = pose_matrix.numpy() if isinstance(pose_matrix, torch.Tensor) else pose_matrix
            
            # Extract position (translation)
            position = pose_np[:3, 3].tolist()
            
            # Extract rotation matrix and convert to Euler angles (in degrees)
            rotation_matrix = pose_np[:3, :3]
            
            # Convert rotation matrix to Euler angles (XYZ order)
            # Using the same convention as in computer graphics (pitch, yaw, roll)
            sy = np.sqrt(rotation_matrix[0, 0] * rotation_matrix[0, 0] + 
                        rotation_matrix[1, 0] * rotation_matrix[1, 0])
            
            singular = sy < 1e-6
            
            if not singular:
                x = np.arctan2(rotation_matrix[2, 1], rotation_matrix[2, 2])
                y = np.arctan2(-rotation_matrix[2, 0], sy)
                z = np.arctan2(rotation_matrix[1, 0], rotation_matrix[0, 0])
            else:
                x = np.arctan2(-rotation_matrix[1, 2], rotation_matrix[1, 1])
                y = np.arctan2(-rotation_matrix[2, 0], sy)
                z = 0
            
            # Convert to degrees and ensure they are Python floats (not numpy types)
            rotation_deg = [float(np.degrees(x)), float(np.degrees(y)), float(np.degrees(z))]
            
            # Convert FOV from radians to degrees
            fov_deg = float(np.degrees(fov.item() if isinstance(fov, torch.Tensor) else fov))
            
            camera_poses.append({
                "position": position,
                "rotation": rotation_deg,
                "fov": fov_deg
            })
        
        # Save to JSON file
        export_path = os.path.join(self.output_dir, f"{export_name}.json")
        with open(export_path, 'w') as f:
            json.dump({
                "trajectory_type": preset_type,
                "num_frames": len(camera_poses),
                "camera_poses": camera_poses,
                "export_timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
                "format_version": "1.0"
            }, f, indent=2)
        
        print(f"Exported trajectory to: {export_path}")
        return export_path
    
    def export_camera_poses_string(self, poses: torch.Tensor, fovs: torch.Tensor) -> str:
        """Export trajectory as JSON string compatible with FastAPI custom-trajectory endpoint"""
        camera_poses = []
        
        for i, (pose_matrix, fov) in enumerate(zip(poses, fovs)):
            pose_np = pose_matrix.numpy() if isinstance(pose_matrix, torch.Tensor) else pose_matrix
            
            # Extract position
            position = pose_np[:3, 3].tolist()
            
            # Extract rotation and convert to Euler angles
            rotation_matrix = pose_np[:3, :3]
            sy = np.sqrt(rotation_matrix[0, 0] * rotation_matrix[0, 0] + 
                        rotation_matrix[1, 0] * rotation_matrix[1, 0])
            
            singular = sy < 1e-6
            
            if not singular:
                x = np.arctan2(rotation_matrix[2, 1], rotation_matrix[2, 2])
                y = np.arctan2(-rotation_matrix[2, 0], sy)
                z = np.arctan2(rotation_matrix[1, 0], rotation_matrix[0, 0])
            else:
                x = np.arctan2(-rotation_matrix[1, 2], rotation_matrix[1, 1])
                y = np.arctan2(-rotation_matrix[2, 0], sy)
                z = 0
            
            rotation_deg = [float(np.degrees(x)), float(np.degrees(y)), float(np.degrees(z))]
            fov_deg = float(np.degrees(fov.item() if isinstance(fov, torch.Tensor) else fov))
            
            camera_poses.append({
                "position": position,
                "rotation": rotation_deg,
                "fov": fov_deg
            })
        
        return json.dumps(camera_poses)
    
    def get_fastapi_curl_example(self, poses: torch.Tensor, fovs: torch.Tensor, 
                                image_path: str = "path/to/your/image.jpg",
                                fastapi_url: str = "http://localhost:8000") -> str:
        """Generate a curl command example for using the exported trajectory with FastAPI"""
        camera_poses_str = self.export_camera_poses_string(poses, fovs)
        
        curl_command = f'''curl -X POST "{fastapi_url}/generate/custom-trajectory" \\
  -F "data_path={image_path}" \\
  -F "camera_poses='{camera_poses_str}'" \\
  -F "cfg=4.0,2.0" \\
  -F "camera_scale=2.0" \\
  -F "seed=23" \\
  -F "H=576" \\
  -F "W=576" \\
  -F "T=21" \\
  -F "num_steps=50" \\
  -F "video_save_fps=30.0"'''
        
        return curl_command


class QuiltUnitManager:
    """Manages Looking Glass quilt units based on input camera poses from Dust3R.
    Each quilt unit corresponds to one input camera pose and contains rows×columns views around that pose.
    """
    
    def __init__(self, config: 'LookingGlassConfig'):
        if not LOOKING_GLASS_AVAILABLE:
            raise ImportError("Looking Glass modules not available")
            
        self.config = config
        self.quilt_units = []  # List of quilt units, one per input pose
        self.current_unit = None  # Currently selected unit
        
    def create_units_from_input_poses(self, input_camera_poses: List[Dict[str, Any]], 
                                     scene_center: np.ndarray) -> List[Dict[str, Any]]:
        """Create quilt units from input camera poses predicted by Dust3R.
        
        Args:
            input_camera_poses: Camera poses from Dust3R preprocessing
            scene_center: Center of the reconstructed scene
            
        Returns:
            List of quilt unit configurations
        """
        self.quilt_units = []
        
        rows = self.config.quilt.rows
        cols = self.config.quilt.columns
        num_views_per_unit = rows * cols
        
        for unit_id, input_pose in enumerate(input_camera_poses):
            # Extract the input camera's position and orientation
            input_matrix = np.array(input_pose["matrix"]).reshape(4, 4)
            input_position = input_matrix[:3, 3]
            input_rotation = input_matrix[:3, :3]
            
            # Generate multiple viewing angles around this input pose for the quilt
            unit_views = self._generate_quilt_views_around_pose(
                input_position, input_rotation, scene_center, num_views_per_unit
            )
            
            # Create the quilt unit
            quilt_unit = {
                "id": unit_id,
                "input_pose_id": unit_id,  # Reference to which input image this unit is for
                "center_position": input_position,
                "views": unit_views,  # rows×columns camera poses around the input pose
                "rows": rows,
                "columns": cols,
                "generated_video": None,  # Will store path to generated video
                "quilt_image": None  # Will store path to final quilt image
            }
            
            self.quilt_units.append(quilt_unit)
        
        print(f"✅ Created {len(self.quilt_units)} quilt units, each with {num_views_per_unit} views ({rows}x{cols})")
        return self.quilt_units
    
    def _generate_quilt_views_around_pose(self, center_pos: np.ndarray, center_rot: np.ndarray, 
                                          scene_center: np.ndarray, num_views: int) -> List[Dict[str, Any]]:
        """Generate camera views around a central pose for Looking Glass quilt display."""
        views = []
        
        if SEVA_AVAILABLE:
            # Use SEVA's trajectory generation around the input pose
            from seva.geometry import get_preset_pose_fov, DEFAULT_FOV_RAD
            
            # Create initial pose matrix
            start_c2w = np.eye(4)
            start_c2w[:3, :3] = center_rot
            start_c2w[:3, 3] = center_pos
            start_w2c = np.linalg.inv(start_c2w)
            
            # Generate views in a subtle pattern around the central pose
            target_c2ws, target_fovs = get_preset_pose_fov(
                "lemniscate",  # Good pattern for holographic viewing
                num_views,
                torch.tensor(start_w2c),
                torch.tensor(scene_center),
                torch.tensor([0.0, 1.0, 0.0]),  # Up direction
                DEFAULT_FOV_RAD,
                spiral_radii=[0.05, 0.05, 0.01],  # Very small radius for subtle parallax
            )
            
            # Convert to numpy arrays
            if isinstance(target_c2ws, torch.Tensor):
                target_c2ws = target_c2ws.detach().cpu().numpy()
            if isinstance(target_fovs, torch.Tensor):
                target_fovs = target_fovs.detach().cpu().numpy()
            
            for i in range(num_views):
                c2w = target_c2ws[i]
                fov = target_fovs[i] if hasattr(target_fovs, '__len__') else target_fovs
                
                views.append({
                    "view_id": i,
                    "c2w": c2w,
                    "fov": fov,
                    "position": c2w[:3, 3],
                    "rotation": c2w[:3, :3]
                })
        else:
            # Fallback: generate simple circular pattern around the pose
            for i in range(num_views):
                angle = 2 * np.pi * i / num_views
                offset = 0.1 * np.array([np.cos(angle), 0, np.sin(angle)])
                
                view_pos = center_pos + offset
                view_c2w = np.eye(4)
                view_c2w[:3, :3] = center_rot  # Keep same orientation
                view_c2w[:3, 3] = view_pos
                
                views.append({
                    "view_id": i,
                    "c2w": view_c2w,
                    "fov": np.radians(54.0),  # Default FOV
                    "position": view_pos,
                    "rotation": center_rot
                })
        
        return views
    
    def get_unit_for_input_pose(self, input_pose_id: int) -> Optional[Dict[str, Any]]:
        """Get the quilt unit corresponding to a specific input pose."""
        for unit in self.quilt_units:
            if unit["input_pose_id"] == input_pose_id:
                return unit
        return None
    
    def select_unit(self, unit_id: int) -> bool:
        """Select a quilt unit for operations."""
        if 0 <= unit_id < len(self.quilt_units):
            self.current_unit = unit_id
            return True
        return False
    
    def get_current_unit(self) -> Optional[Dict[str, Any]]:
        """Get the currently selected quilt unit."""
        if self.current_unit is not None and 0 <= self.current_unit < len(self.quilt_units):
            return self.quilt_units[self.current_unit]
        return None


class LookingGlassManager:
    """Manages Looking Glass-specific functionality"""
    
    def __init__(self, server: viser.ViserServer):
        if not LOOKING_GLASS_AVAILABLE:
            raise ImportError("Looking Glass modules not available")
            
        self.server = server
        
        # Import necessary components here to avoid unbound errors
        from .lookingglass_config import LookingGlassConfig, get_default_config
        from .quilt_camera import QuiltCameraModel
        from .quilt_generator import QuiltImageGenerator
        
        self.config = get_default_config()
        self.camera_model = QuiltCameraModel(self.config)
        self.quilt_generator = QuiltImageGenerator(self.config)
        self.quilt_unit_manager = QuiltUnitManager(self.config)
        self.current_poses = None
        self.current_fovs = None
        
    def _setup_camera_model(self):
        """Initialize camera model, quilt generator, and unit manager"""
        from .quilt_camera import QuiltCameraModel
        from .quilt_generator import QuiltImageGenerator
        
        self.camera_model = QuiltCameraModel(self.config)
        self.quilt_generator = QuiltImageGenerator(self.config)
        self.quilt_unit_manager = QuiltUnitManager(self.config)
        
    def update_device_config(self, device_type: str):
        """Update device configuration and reinitialize components"""
        from .lookingglass_config import get_device_config
        
        self.config = get_device_config(device_type)
        self._setup_camera_model()
        print(f"Updated to {self.config.device_name} configuration")
        
    def generate_quilt_trajectory(
        self,
        center_position: Optional[np.ndarray] = None,
        look_at: Optional[np.ndarray] = None,
        distance_override: Optional[float] = None
    ) -> tuple[torch.Tensor, torch.Tensor]:
        """Generate Looking Glass camera trajectory"""
        if center_position is None:
            center_position = np.array([0.0, 0.0, self.config.camera.focus_distance])
        if look_at is None:
            look_at = np.array([0.0, 0.0, 0.0])
            
        # Handle the optional distance_override parameter
        kwargs = {}
        if distance_override is not None:
            kwargs["distance_from_center"] = distance_override
            
        poses, fovs = self.camera_model.generate_quilt_trajectory(
            center_position=center_position,
            look_at=look_at,
            **kwargs
        )
        
        self.current_poses = poses
        self.current_fovs = fovs
        
        return poses, fovs
    
    def create_test_quilt_image(self, pattern: str = "gradient") -> str:
        """Create a test quilt image"""
        quilt_image = self.quilt_generator.create_test_quilt(pattern)
        output_path = self.quilt_generator.save_quilt_image(
            quilt_image,
            filename=f"test_{pattern}_{self.config.device_name.lower().replace(' ', '_')}"
        )
        return output_path
    
    def export_for_fastapi(self, filename_prefix: str = "lookingglass") -> Dict[str, Any]:
        """Export current trajectory for FastAPI"""
        if self.current_poses is None or self.current_fovs is None:
            raise ValueError("No trajectory generated. Generate trajectory first.")
            
        return self.camera_model.export_for_fastapi(
            self.current_poses,
            self.current_fovs,
            filename=filename_prefix
        )
    
    def get_config_info(self) -> Dict[str, Any]:
        """Get current configuration information"""
        return {
            "device": self.config.device_name,
            "quilt_layout": self.camera_model.get_quilt_layout_info(),
            "camera_settings": self.config.camera.__dict__,
            "total_views": self.config.quilt.total_views
        } 