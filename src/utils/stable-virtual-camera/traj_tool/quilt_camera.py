#!/usr/bin/env python3
"""
Quilt Camera Model

Enhanced Looking Glass camera model with proper coordinate system handling
and correct view ordering for holographic displays.
"""

import numpy as np
import torch
from typing import List, Dict, Any, Tuple, Optional
from pathlib import Path

from traj_tool.lookingglass_config import LookingGlassConfig, get_default_config
import viser.transforms as vt


class QuiltCameraModel:
    """
    Enhanced Camera model for generating Looking Glass quilt trajectories.
    
    Key improvements:
    1. Proper view numbering (View 0 = bottom-left)
    2. Consistent coordinate system handling
    3. Proper camera spacing based on light field principles
    """
    
    def __init__(self, config: Optional[LookingGlassConfig] = None):
        self.config = config or get_default_config()
        
    def generate_quilt_trajectory(
        self,
        center_position: np.ndarray = np.array([0.0, 0.0, 0.0]),
        look_at: np.ndarray = np.array([0.0, 0.0, 0.0]),
        up_direction: np.ndarray = np.array([0.0, 1.0, 0.0]),
        distance_from_center: Optional[float] = None
    ) -> <PERSON><PERSON>[torch.Tensor, torch.Tensor]:
        """
        Generate camera trajectory for quilt creation with proper Looking Glass ordering.
        """
        if distance_from_center is None:
            distance_from_center = self.config.camera.focus_distance
            
        # Generate camera positions using grid pattern
        camera_positions = self._generate_camera_grid(
            center_position, look_at, up_direction, distance_from_center
        )
        
        # Generate camera poses (position + orientation)
        poses = []
        fovs = []
        
        for pos in camera_positions:
            # Create camera-to-world matrix
            pose_matrix = self._look_at_matrix(pos, look_at, up_direction)
            
            # Convert to 4x4 homogeneous matrix
            pose_4x4 = np.eye(4)
            pose_4x4[:3, :4] = pose_matrix
            
            poses.append(pose_4x4)
            fovs.append(self.config.camera.fov_radians)
        
        # Convert to torch tensors
        poses_tensor = torch.tensor(np.array(poses), dtype=torch.float32)
        fovs_tensor = torch.tensor(fovs, dtype=torch.float32)
        
        return poses_tensor, fovs_tensor
    
    def _generate_camera_grid(
        self,
        center_position: np.ndarray,
        look_at: np.ndarray,
        up_direction: np.ndarray,
        distance: float
    ) -> List[np.ndarray]:
        """
        Generate camera positions following EXACT Looking Glass specifications.
        
        Key fixes:
        1. View 0 is bottom-left (not top-left)
        2. Proper camera spacing for light field parallax
        3. Cameras arranged on a plane perpendicular to viewing direction
        """
        rows = self.config.quilt.rows
        cols = self.config.quilt.columns
        
        # Calculate view direction and orthonormal basis
        view_dir = look_at - center_position
        view_dir_norm = np.linalg.norm(view_dir)
        if view_dir_norm < 1e-6:
            # Handle degenerate case where center_position == look_at
            view_dir = np.array([0.0, 0.0, -1.0])  # Default forward direction
        else:
            view_dir = view_dir / view_dir_norm
        
        # Normalize up direction
        up_direction = up_direction / np.linalg.norm(up_direction)
        
        # Calculate right vector (perpendicular to view_dir and up_direction)
        right = np.cross(view_dir, up_direction)
        right = right / np.linalg.norm(right)
        
        # Recalculate up to ensure perfect orthogonality
        up = np.cross(right, view_dir)
        up = up / np.linalg.norm(up)
        
        # Calculate camera spacing based on Looking Glass light field theory
        view_cone_rad = np.radians(self.config.camera.viewcone_angle)
        
        # The viewcone defines the total angular range covered by all cameras
        # At the focal distance, this translates to a physical width
        focal_plane_width = 2.0 * distance * np.tan(view_cone_rad / 2.0)
        
        # Maintain aspect ratio for the camera array
        focal_plane_height = focal_plane_width * (rows / cols)
        
        # Camera spacing (distance between adjacent cameras)
        dx = focal_plane_width / (cols - 1) if cols > 1 else 0.0
        dy = focal_plane_height / (rows - 1) if rows > 1 else 0.0
        
        # Generate camera positions in CORRECT Looking Glass order
        camera_positions = []
        
        # Looking Glass documentation: View 0 is bottom-left
        # View numbering increases left-to-right, then bottom-to-top
        for row in range(rows):
            for col in range(cols):
                # Calculate position in the camera grid
                # Note: row=0 corresponds to BOTTOM of the display (Looking Glass convention)
                x_offset = (col - (cols - 1) / 2.0) * dx
                y_offset = (row - (rows - 1) / 2.0) * dy  # row=0 is bottom, higher row = higher Y
                
                # Calculate world position
                camera_pos = (center_position + 
                             x_offset * right + 
                             y_offset * up)
                
                camera_positions.append(camera_pos)
        
        return camera_positions
    
    def _look_at_matrix(
        self,
        camera_pos: np.ndarray,
        target: np.ndarray,
        up: np.ndarray
    ) -> np.ndarray:
        """
        Create look-at camera matrix with proper coordinate system handling.
        
        Ensures consistent coordinate system with SEVA OpenGL conventions.
        """
        # Calculate camera coordinate system
        forward = target - camera_pos
        forward_norm = np.linalg.norm(forward)
        if forward_norm < 1e-6:
            # Handle degenerate case where camera_pos == target
            forward = np.array([0.0, 0.0, -1.0])  # Default forward direction
        else:
            forward = forward / forward_norm
        
        # Calculate right vector
        right = np.cross(forward, up)
        if np.linalg.norm(right) < 1e-6:
            # Handle degenerate case (up parallel to forward)
            # Create arbitrary perpendicular vector
            if abs(forward[0]) < 0.9:
                right = np.cross(forward, [1, 0, 0])
            else:
                right = np.cross(forward, [0, 1, 0])
        right = right / np.linalg.norm(right)
        
        # Calculate corrected up vector (ensure right-handed coordinate system)
        up_corrected = np.cross(right, forward)
        up_corrected = up_corrected / np.linalg.norm(up_corrected)
        
        # Create rotation matrix following OpenGL/SEVA convention
        # Camera-to-world matrix: columns are [right, up, -forward] for right-handed system
        rotation = np.array([
            right,
            up_corrected,
            -forward  # Negative forward for right-handed coordinate system
        ]).T  # Transpose to make columns instead of rows
        
        # Create 3x4 camera-to-world matrix
        pose_matrix = np.zeros((3, 4))
        pose_matrix[:3, :3] = rotation  # Direct rotation matrix for correct orientation
        pose_matrix[:3, 3] = camera_pos
        
        return pose_matrix


def test_camera_model():
    """Test the camera model implementation"""
    print("Testing Looking Glass Camera Model")
    print("=" * 50)
    
    # Create model
    config = get_default_config()
    model = QuiltCameraModel(config)
    
    # Test parameters
    center_position = np.array([0.0, 0.0, 2.0])
    look_at = np.array([0.0, 0.0, 0.0])
    
    # Generate trajectory
    poses, fovs = model.generate_quilt_trajectory(
        center_position=center_position,
        look_at=look_at
    )
    
    print(f"Generated {len(poses)} poses for {config.quilt.rows}×{config.quilt.columns} grid")
    
    # Verify grid structure
    positions = poses[:, :3, 3].numpy()
    rotations = poses[:, :3, :3].numpy()
    
    # Check grid organization
    rows, cols = config.quilt.rows, config.quilt.columns
    
    print(f"\nView 0 (bottom-left): {positions[0]}")
    print(f"View {cols-1} (bottom-right): {positions[cols-1]}")
    print(f"View {(rows-1)*cols} (top-left): {positions[(rows-1)*cols]}")
    print(f"View {rows*cols-1} (top-right): {positions[rows*cols-1]}")
    
    # Check camera orientations
    print(f"\nCamera orientations (first 5):")
    for i in range(min(5, len(poses))):
        pos = positions[i]
        forward = rotations[i, :, 2]  # Z-axis
        
        # Expected direction toward look_at
        expected = look_at - pos
        expected = expected / np.linalg.norm(expected)
        
        # Calculate alignment
        dot_product = np.dot(forward, expected)
        angle_deg = np.degrees(np.arccos(np.clip(dot_product, -1, 1)))
        
        print(f"  Camera {i}: forward={forward}, angle_error={angle_deg:.2f}°")
    
    return model, poses, fovs


def main():
    """Run the camera model test"""
    model, poses, fovs = test_camera_model()
    
    print("\n" + "=" * 50)
    print("Camera model ready for integration!")
    print("Key improvements:")
    print("1. Proper Looking Glass view ordering (View 0 = bottom-left)")
    print("2. Correct camera spacing for light field parallax")
    print("3. Consistent coordinate system with SEVA")
    
    # Save the results for comparison
    np.save("camera_poses.npy", poses.numpy())
    print("Saved poses to 'camera_poses.npy'")


if __name__ == "__main__":
    main()