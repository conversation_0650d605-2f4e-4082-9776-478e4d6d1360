"""
SEVA Model Optimization Utilities

This module provides optimization functions for ensuring compatibility with the 
Stable Virtual Camera (SEVA) diffusion model, including dimension scaling and 
frame count limiting for Looking Glass quilt generation.

SEVA Model Constraints:
- Maximum frames per batch: 40
- Image dimensions must be divisible by 64
- Minimum dimension: 64 pixels
"""

import math
from typing import Tuple, Dict, Any, Literal, List
import numpy as np

# SEVA model constraints
SEVA_MAX_FRAMES = 40
SEVA_DIMENSION_DIVISOR = 64
SEVA_MIN_DIMENSION = 64

# Type definitions
StrategyType = Literal["no_reduction", "reduce_rows", "reduce_columns", "balanced", "prioritize_rows", "prioritize_columns", "fallback"]


def make_seva_compatible(width: int, height: int) -> Tuple[int, int]:
    """
    Adjust dimensions to be compatible with SEVA model (divisible by 64).
    
    Args:
        width: Original width in pixels
        height: Original height in pixels
        
    Returns:
        Tuple of (adjusted_width, adjusted_height) both divisible by 64
        
    Example:
        >>> make_seva_compatible(420, 560)
        (384, 512)
    """
    seva_width = (width // SEVA_DIMENSION_DIVISOR) * SEVA_DIMENSION_DIVISOR
    seva_height = (height // SEVA_DIMENSION_DIVISOR) * SEVA_DIMENSION_DIVISOR
    
    # Ensure minimum dimensions
    seva_width = max(seva_width, SEVA_MIN_DIMENSION)
    seva_height = max(seva_height, SEVA_MIN_DIMENSION)
    
    return seva_width, seva_height


def calculate_optimal_quilt_size(rows: int, columns: int, max_frames: int = SEVA_MAX_FRAMES) -> Tuple[int, int, int, StrategyType]:
    """
    Calculate optimal quilt dimensions within SEVA limits for any Looking Glass config.
    
    Uses multiple optimization strategies to maximize view count while staying within
    the SEVA model's frame processing limit.
    
    Args:
        rows: Original number of rows in quilt
        columns: Original number of columns in quilt  
        max_frames: Maximum frames SEVA can process (default: 40)
        
    Returns:
        Tuple of (optimal_rows, optimal_columns, total_views, strategy_used)
        
    Strategies:
        - no_reduction: Already within limits
        - reduce_rows: Maintain horizontal resolution, reduce vertical
        - reduce_columns: Maintain vertical resolution, reduce horizontal
        - balanced: Maintain aspect ratio as much as possible
        - prioritize_rows: Maximize rows when rows >= columns
        - prioritize_columns: Maximize columns when columns > rows
        - fallback: Simple reduction when other strategies fail
        
    Example:
        >>> calculate_optimal_quilt_size(6, 8, 40)  # Portrait: 48 views -> 40 limit
        (5, 8, 40, 'reduce_rows')
        
        >>> calculate_optimal_quilt_size(9, 8, 40)  # 65": 72 views -> 40 limit  
        (5, 8, 40, 'reduce_rows')
    """
    total_views = rows * columns
    
    if total_views <= max_frames:
        return rows, columns, total_views, "no_reduction"
    
    # Strategy 1: Reduce rows only (preserve column resolution)
    optimal_rows_only = min(rows, max_frames // columns)
    option1_views = optimal_rows_only * columns if optimal_rows_only > 0 else 0
    
    # Strategy 2: Reduce columns only (preserve row resolution)
    optimal_cols_only = min(columns, max_frames // rows)
    option2_views = rows * optimal_cols_only if optimal_cols_only > 0 else 0
    
    # Strategy 3: Balanced reduction (maintain aspect ratio as much as possible)
    aspect_ratio = columns / rows
    
    # Find dimensions that maintain aspect ratio and fit within max_frames
    sqrt_max = math.sqrt(max_frames)
    if aspect_ratio >= 1.0:  # Wider than tall
        balanced_rows = max(1, int(sqrt_max / math.sqrt(aspect_ratio)))
        balanced_cols = min(columns, max_frames // balanced_rows)
    else:  # Taller than wide
        balanced_cols = max(1, int(sqrt_max * math.sqrt(aspect_ratio)))
        balanced_rows = min(rows, max_frames // balanced_cols)
    
    option3_views = balanced_rows * balanced_cols
    
    # Strategy 4: Maximize one dimension (choose the larger original dimension)
    if rows >= columns:
        # Prioritize rows
        max_rows = min(rows, max_frames)
        max_cols = max(1, min(columns, max_frames // max_rows))
        option4_views = max_rows * max_cols
        option4_strategy = "prioritize_rows"
    else:
        # Prioritize columns
        max_cols = min(columns, max_frames)
        max_rows = max(1, min(rows, max_frames // max_cols))
        option4_views = max_rows * max_cols
        option4_strategy = "prioritize_columns"
    
    # Choose the strategy that maximizes views while staying within limits
    strategies = [
        (option1_views, optimal_rows_only, columns, "reduce_rows"),
        (option2_views, rows, optimal_cols_only, "reduce_columns"),
        (option3_views, balanced_rows, balanced_cols, "balanced"),
        (option4_views, max_rows if rows >= columns else rows, 
         columns if rows >= columns else max_cols, option4_strategy)
    ]
    
    # Filter out invalid strategies (where views would be 0 or negative)
    valid_strategies = [(v, r, c, s) for v, r, c, s in strategies if v > 0 and v <= max_frames]
    
    if not valid_strategies:
        # Fallback: simple reduction to exactly max_frames
        fallback_rows = max_frames // columns if columns <= max_frames else 1
        fallback_cols = columns if columns <= max_frames else max_frames
        return fallback_rows, fallback_cols, fallback_rows * fallback_cols, "fallback"
    
    # Choose the strategy with maximum views
    best_views, best_rows, best_cols, strategy = max(valid_strategies, key=lambda x: x[0])
    
    return int(best_rows), int(best_cols), int(best_views), strategy


def get_seva_constraints() -> Dict[str, Any]:
    """
    Get SEVA model constraints as a dictionary.
    
    Returns:
        Dictionary containing SEVA model limitations and requirements
    """
    return {
        "max_frames": SEVA_MAX_FRAMES,
        "dimension_divisor": SEVA_DIMENSION_DIVISOR,
        "min_dimension": SEVA_MIN_DIMENSION,
        "description": "SEVA (Stable Virtual Camera) 1.3B parameter diffusion model constraints"
    }


def validate_seva_compatibility(width: int, height: int, frame_count: int) -> Dict[str, Any]:
    """
    Validate parameters against SEVA model constraints.
    
    Args:
        width: Image width in pixels
        height: Image height in pixels
        frame_count: Number of frames/views
        
    Returns:
        Dictionary with validation results and recommendations
    """
    issues = []
    recommendations = []
    
    # Check frame count
    if frame_count > SEVA_MAX_FRAMES:
        issues.append(f"Frame count {frame_count} exceeds SEVA limit of {SEVA_MAX_FRAMES}")
        recommendations.append(f"Reduce frame count to {SEVA_MAX_FRAMES} or less")
    
    # Check width divisibility
    if width % SEVA_DIMENSION_DIVISOR != 0:
        issues.append(f"Width {width} is not divisible by {SEVA_DIMENSION_DIVISOR}")
        compatible_width = (width // SEVA_DIMENSION_DIVISOR) * SEVA_DIMENSION_DIVISOR
        recommendations.append(f"Adjust width to {compatible_width}")
    
    # Check height divisibility
    if height % SEVA_DIMENSION_DIVISOR != 0:
        issues.append(f"Height {height} is not divisible by {SEVA_DIMENSION_DIVISOR}")
        compatible_height = (height // SEVA_DIMENSION_DIVISOR) * SEVA_DIMENSION_DIVISOR
        recommendations.append(f"Adjust height to {compatible_height}")
    
    # Check minimum dimensions
    if width < SEVA_MIN_DIMENSION:
        issues.append(f"Width {width} is below minimum {SEVA_MIN_DIMENSION}")
        recommendations.append(f"Increase width to at least {SEVA_MIN_DIMENSION}")
    
    if height < SEVA_MIN_DIMENSION:
        issues.append(f"Height {height} is below minimum {SEVA_MIN_DIMENSION}")
        recommendations.append(f"Increase height to at least {SEVA_MIN_DIMENSION}")
    
    return {
        "compatible": len(issues) == 0,
        "issues": issues,
        "recommendations": recommendations,
        "constraints": get_seva_constraints()
    }


def get_optimization_summary(original_rows: int, original_cols: int, optimized_rows: int, optimized_cols: int, strategy: StrategyType) -> Dict[str, Any]:
    """
    Generate a summary of quilt optimization results.
    
    Args:
        original_rows: Original number of rows
        original_cols: Original number of columns
        optimized_rows: Optimized number of rows
        optimized_cols: Optimized number of columns
        strategy: Strategy used for optimization
        
    Returns:
        Dictionary with optimization statistics and analysis
    """
    original_views = original_rows * original_cols
    optimized_views = optimized_rows * optimized_cols
    efficiency = optimized_views / original_views if original_views > 0 else 0
    views_removed = original_views - optimized_views
    
    return {
        "original": {
            "rows": original_rows,
            "columns": original_cols,
            "total_views": original_views
        },
        "optimized": {
            "rows": optimized_rows,
            "columns": optimized_cols,
            "total_views": optimized_views
        },
        "reduction": {
            "views_removed": views_removed,
            "efficiency_percent": efficiency * 100,
            "strategy_used": strategy
        },
        "seva_compliant": optimized_views <= SEVA_MAX_FRAMES
    }


# Vectorized Camera Calculation Optimizations

def optimize_camera_calculations_vectorized(
    positions: np.ndarray, 
    rotations: np.ndarray
) -> Tuple[np.ndarray, np.ndarray]:
    """
    Vectorized camera matrix calculations for improved performance.
    
    Args:
        positions: Array of camera positions (N, 3)
        rotations: Array of rotation matrices (N, 3, 3)
        
    Returns:
        Tuple of (c2w_matrices, w2c_matrices) both shape (N, 4, 4)
    """
    batch_size = len(positions)
    
    # Pre-allocate matrices for efficiency
    c2w_matrices = np.tile(np.eye(4), (batch_size, 1, 1))
    
    # Vectorized assignment - much faster than loops
    c2w_matrices[:, :3, :3] = rotations
    c2w_matrices[:, :3, 3] = positions
    
    # Batch inverse calculation (more efficient than individual inversions)
    w2c_matrices = np.linalg.inv(c2w_matrices)
    
    return c2w_matrices, w2c_matrices


def validate_seva_batch_optimized(
    dimensions: List[Tuple[int, int]], 
    frame_counts: List[int]
) -> Dict[str, Any]:
    """
    Batch validation with early exit optimization for fail-fast behavior.
    
    Args:
        dimensions: List of (width, height) tuples
        frame_counts: List of frame counts corresponding to each dimension
        
    Returns:
        Dictionary with validation results and performance metrics
    """
    # Early exit on first failure for fail-fast behavior
    for i, ((width, height), frame_count) in enumerate(zip(dimensions, frame_counts)):
        if frame_count > SEVA_MAX_FRAMES:
            return {
                "compatible": False,
                "first_failure_index": i,
                "issue": f"Frame count {frame_count} exceeds limit {SEVA_MAX_FRAMES}",
                "validation_time": "early_exit"
            }
        
        if width % SEVA_DIMENSION_DIVISOR != 0 or height % SEVA_DIMENSION_DIVISOR != 0:
            return {
                "compatible": False,
                "first_failure_index": i,
                "issue": f"Dimensions {width}x{height} not divisible by {SEVA_DIMENSION_DIVISOR}",
                "validation_time": "early_exit"
            }
        
        if width < SEVA_MIN_DIMENSION or height < SEVA_MIN_DIMENSION:
            return {
                "compatible": False,
                "first_failure_index": i,
                "issue": f"Dimensions {width}x{height} below minimum {SEVA_MIN_DIMENSION}",
                "validation_time": "early_exit"
            }
    
    return {
        "compatible": True, 
        "validated_count": len(dimensions),
        "total_frames": sum(frame_counts),
        "validation_time": "complete"
    }


def batch_dimension_optimization(
    dimensions_list: List[Tuple[int, int]]
) -> List[Tuple[int, int]]:
    """
    Vectorized dimension optimization for multiple inputs.
    
    Args:
        dimensions_list: List of (width, height) tuples to optimize
        
    Returns:
        List of optimized (width, height) tuples
    """
    # Convert to numpy arrays for vectorized operations
    dimensions_array = np.array(dimensions_list)
    
    # Vectorized division and multiplication
    optimized_widths = (dimensions_array[:, 0] // SEVA_DIMENSION_DIVISOR) * SEVA_DIMENSION_DIVISOR
    optimized_heights = (dimensions_array[:, 1] // SEVA_DIMENSION_DIVISOR) * SEVA_DIMENSION_DIVISOR
    
    # Ensure minimum dimensions (vectorized max operation)
    optimized_widths = np.maximum(optimized_widths, SEVA_MIN_DIMENSION)
    optimized_heights = np.maximum(optimized_heights, SEVA_MIN_DIMENSION)
    
    # Convert back to list of tuples
    return list(zip(optimized_widths.astype(int), optimized_heights.astype(int)))

