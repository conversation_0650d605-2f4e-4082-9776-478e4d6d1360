"""
Looking Glass Stable Virtual Camera App

This application integrates:
1. Viser-based 3D visualization for camera trajectory design (similar to demo_gr.py)
2. FastAPI remote service for rendering (http://10.30.58.120:4013/)
3. Looking Glass quilt generation for holographic displays

The app allows users to:
- Upload or select images
- Design camera trajectories visually using viser
- Generate Looking Glass quilt images from trajectories via FastAPI
"""

import os
import sys
import json
import time
import secrets
import asyncio
import copy
import argparse
import requests
import threading
import traceback
import random
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple, Union, Literal
from PIL import Image
from aiohttp import TCPConnector

import gradio as gr
import numpy as np
import torch
import viser
import viser.transforms as vt
import io
import aiohttp
import scipy.spatial.transform as sst
import cv2

# Import traj_tool components for Looking Glass support
from traj_tool.lookingglass_config import (
    LookingGlassConfig, 
    get_default_config, 
    get_device_config,
    list_available_devices
)
from traj_tool.managers import (
    TrajectoryState,
    QuiltState,
    SEVA_AVAILABLE
)
from traj_tool.quilt_generator import create_test_quilt, FastAPIQuiltGenerator
from traj_tool.quilt_camera import QuiltCameraModel
from traj_tool.seva_optimization import make_seva_compatible, calculate_optimal_quilt_size

# Import new SEVA-integrated components
try:
    from traj_tool.quilt_trajectory import QuiltTrajectoryManager
    from traj_tool.quilt_gui import QuiltGuiManager
    QUILT_COMPONENTS_AVAILABLE = True
except ImportError:
    QUILT_COMPONENTS_AVAILABLE = False
    # Define placeholder classes for type checking
    QuiltTrajectoryManager = None  # type: ignore
    QuiltGuiManager = None  # type: ignore
    print("Warning: New quilt components not available, using legacy implementation")

# SEVA availability is handled in traj_tool modules
# Import seva GUI patterns for consistency
try:
    from seva.gui import Keyframe
    SEVA_GUI_AVAILABLE = True
except ImportError:
    SEVA_GUI_AVAILABLE = False
    print("Warning: seva.gui not available, using simplified patterns")

# All image processing is done via FastAPI service
# No local seva imports needed

# Constants
FASTAPI_URL = "http://10.30.58.120:4013"
WORK_DIR = "work_dirs/lookingglass_app"
UPLOAD_DIR = os.path.join(WORK_DIR, "uploads")
EXPORT_DIR = os.path.join(WORK_DIR, "exports")
MAX_SESSIONS = 5

# Optimized HTTP Client with Connection Pooling
class OptimizedAPIClient:
    """Optimized HTTP client with connection pooling and proper timeout management"""
    
    def __init__(self, base_url: str, max_connections: int = 10):
        self.base_url = base_url
        self.max_connections = max_connections
        self.connector = None
        self.timeout = aiohttp.ClientTimeout(
            total=3600,  # 1 hour total for long SEVA jobs
            connect=30,  # 30s connection timeout
            sock_read=300  # 5 minutes read timeout
        )
        self._session = None
    
    def _create_connector(self):
        """Create TCPConnector only when needed (inside event loop)"""
        if self.connector is None:
            self.connector = TCPConnector(
                limit=self.max_connections,
                limit_per_host=self.max_connections,
                ttl_dns_cache=300,
                use_dns_cache=True,
                keepalive_timeout=30,
                enable_cleanup_closed=True
            )
        return self.connector
    
    async def get_session(self):
        """Get or create HTTP session with connection pooling"""
        if self._session is None or self._session.closed:
            self._session = aiohttp.ClientSession(
                connector=self._create_connector(),
                timeout=self.timeout,
                connector_owner=False
            )
        return self._session
    
    async def post(self, endpoint: str, **kwargs):
        """Make POST request with connection reuse"""
        session = await self.get_session()
        url = f"{self.base_url}{endpoint}"
        return await session.post(url, **kwargs)
    
    async def get(self, endpoint: str, **kwargs):
        """Make GET request with connection reuse"""
        session = await self.get_session()
        url = f"{self.base_url}{endpoint}"
        return await session.get(url, **kwargs)
    
    async def close(self):
        """Clean up resources"""
        if self._session and not self._session.closed:
            await self._session.close()
        if self.connector:
            await self.connector.close()

# Global HTTP client instance
_http_client = OptimizedAPIClient(FASTAPI_URL)
ADVANCED_EXAMPLE_MAP = [
    (
        "assets/advance/blue-car.jpg",
        ["assets/advance/blue-car.jpg"],
    ),
    (
        "assets/advance/garden-4_0.jpg",
        [
            "assets/advance/garden-4_0.jpg",
            "assets/advance/garden-4_1.jpg",
            "assets/advance/garden-4_2.jpg",
            "assets/advance/garden-4_3.jpg",
        ],
    ),
    (
        "assets/advance/vgg-lab-4_0.png",
        [
            "assets/advance/vgg-lab-4_0.png",
            "assets/advance/vgg-lab-4_1.png",
            "assets/advance/vgg-lab-4_2.png",
            "assets/advance/vgg-lab-4_3.png",
        ],
    ),
]

# Create necessary directories
for directory in [WORK_DIR, UPLOAD_DIR, EXPORT_DIR]:
    os.makedirs(directory, exist_ok=True)

# Global storage for sessions
SERVERS = {}
RENDERER_STATES = {}


# LookingGlassState is now replaced by QuiltState from traj_tool.managers


class LookingGlassRenderer:
    """Main renderer class that handles visualization and LKG quilt generation"""
    
    def __init__(self, server: viser.ViserServer):
        self.server = server
        self.state = QuiltState()  # Use QuiltState for both state and quilt_state
        self.quilt_state = self.state  # Use the same object for compatibility
        self.quilt_config = get_default_config()
        self.camera_model = QuiltCameraModel(self.quilt_config)
        self.fastapi_generator = FastAPIQuiltGenerator(FASTAPI_URL, self.camera_model)
        
        # Initialize new SEVA-integrated components if available
        if QUILT_COMPONENTS_AVAILABLE and QuiltGuiManager is not None:
            self.quilt_gui_manager = QuiltGuiManager(server, scene_scale=1.0)
            self.quilt_gui_manager.on_mode_change = self._on_quilt_mode_change
            self.quilt_gui_manager.on_config_change = self._on_quilt_config_change
            self.quilt_gui_manager.on_input_visibility_change = self._on_input_visibility_change
            self.quilt_gui_manager.on_quilt_visibility_change = self._on_quilt_visibility_change
        else:
            self.quilt_gui_manager = None
    
    def _on_quilt_mode_change(self, enabled: bool):
        """Callback for quilt mode changes from the new GUI manager"""
        self.quilt_state.quilt_mode = enabled
        print(f"🔄 Quilt mode: {'enabled' if enabled else 'disabled'}")
        
        # Auto-hide input images when quilt mode is enabled
        self._set_input_keyframes_visible(not enabled)
        
        # Only initialize quilt units if:
        # 1. Quilt mode is being enabled
        # 2. Input camera poses exist
        # 3. No quilt units have been created yet
        if (enabled and 
            hasattr(self.state, 'input_camera_poses') and self.state.input_camera_poses and
            self.quilt_gui_manager and 
            len(self.quilt_gui_manager.trajectory_manager.quilt_units) == 0):
            # Initialize quilt units from input camera poses using new manager
            print("🆕 Initializing quilt units from predicted input poses")
            self._initialize_quilt_units_with_new_manager()
        else:
            if enabled:
                print("📋 Quilt mode enabled - using existing units or no input poses available")
    
    def _on_quilt_config_change(self, config: LookingGlassConfig):
        """Callback for quilt configuration changes"""
        self.quilt_config = config
        self.camera_model = QuiltCameraModel(config)
        self.fastapi_generator = FastAPIQuiltGenerator(FASTAPI_URL, self.camera_model)
        print(f"🔄 Updated quilt config: {config.device_name}")
    
    def _on_input_visibility_change(self, visible: bool):
        """Callback for input camera visibility changes from GUI controls"""
        print(f"🔍 Input cameras visibility: {'visible' if visible else 'hidden'}")
        self._set_input_keyframes_visible(visible)
    
    def _on_quilt_visibility_change(self, visible: bool):
        """Callback for quilt camera visibility changes from GUI controls"""
        print(f"🔍 Quilt cameras visibility: {'visible' if visible else 'hidden'}")
        # Quilt cameras are already handled by the trajectory manager
        # This callback can be used for additional coordination if needed
    
    def configure_client_theme(self, client, theme="dark"):
        """Configure theme for a viser client
        
        Args:
            client: ViserClient instance
            theme: "dark" or "light" theme mode
        """
        client.gui.configure_theme(
            dark_mode=(theme == "dark"),
            show_share_button=False,
            control_layout="collapsible",
        )
        
        # Set consistent background color (Gradio will handle theme automatically)
        client.scene.set_background_image(np.array([[[39, 39, 42]]], dtype=np.uint8))
    
    def set_theme(self, theme="dark"):
        """Set theme for all connected clients and server
        
        Args:
            theme: "dark" or "light" theme mode
        """
        # Configure theme for all existing clients
        for client in self.server.get_clients().values():
            self.configure_client_theme(client, theme)
        
        # Set consistent background color (matches demo_gr.py)
        self.server.scene.set_background_image(np.array([[[39, 39, 42]]], dtype=np.uint8))
    
    def _initialize_quilt_units_with_new_manager(self):
        if not self.quilt_gui_manager:
            print("❌ QuiltGuiManager not available for initialization")
            return
        
        print(f"🔍 Available input camera poses: {len(self.state.input_camera_poses) if hasattr(self.state, 'input_camera_poses') else 0}")
        print(f"🔍 Existing quilt units before initialization: {len(self.quilt_gui_manager.trajectory_manager.quilt_units)}")
        
        # Calculate scene center as fallback
        scene_center = np.array([0.0, 0.0, 0.0])
        if hasattr(self.state, 'all_points') and self.state.all_points:
            scene_center = np.mean(np.array(self.state.all_points), axis=0)
            print(f"🔍 Calculated scene center: {scene_center}")
        
        # Add quilt units for each input camera pose
        for idx, input_pose in enumerate(self.state.input_camera_poses):
            print(f"🔍 Processing input pose {idx + 1}/{len(self.state.input_camera_poses)}")
            input_matrix = np.array(input_pose["matrix"]).reshape(4, 4)
            input_position = input_matrix[:3, 3]
            input_rotation = input_matrix[:3, :3]
            
            # Calculate individual look_at target based on camera orientation
            # Extract forward direction from camera rotation matrix
            # In OpenCV convention, camera looks along positive Z axis
            camera_forward = input_rotation[:, 2]  # Third column is Z axis (forward)
            
            # Calculate viewing distance based on scene scale
            viewing_distance = 2.0  # Default viewing distance
            if hasattr(self.state, 'all_points') and self.state.all_points:
                scene_bounds = np.array(self.state.all_points)
                scene_size = np.max(np.ptp(scene_bounds, axis=0)) if len(scene_bounds) > 0 else 1.0
                viewing_distance = scene_size * 0.8  # Look at 80% of scene size
            
            # Calculate look_at point that is in front of the camera
            individual_look_at = input_position + camera_forward * viewing_distance
            
            # Debug output
            print(f"🔍 Input matrix shape: {input_matrix.shape}")
            print(f"🔍 Input position: {input_position}")
            print(f"🔍 Input rotation matrix: {input_rotation}")
            print(f"🔍 Camera forward vector: {camera_forward}")
            print(f"🔍 Viewing distance: {viewing_distance}")
            print(f"🔍 Individual look_at: {individual_look_at}")
            # print(f"Adding quilt unit: pos={input_position}, look_at={individual_look_at}, forward={camera_forward}")  # Removed duplicate - logged in QuiltTrajectoryManager
            
            # Create a quilt unit with the proper look_at point
            # Note: We pass the original input_rotation here - the QuiltTrajectoryManager's add_quilt_unit method
            # will handle the proper conversion using opencv_to_viser_rotation internally
            self.quilt_gui_manager.add_quilt_unit_from_pose(
                input_position, input_rotation, individual_look_at
            )
            
            # Check how many units we have after adding each one
            print(f"🔍 Total quilt units after adding pose {idx + 1}: {len(self.quilt_gui_manager.trajectory_manager.quilt_units)}")
        
        print(f"🔍 Final quilt unit count: {len(self.quilt_gui_manager.trajectory_manager.quilt_units)}")
        
    def preprocess(self, input_imgs) -> tuple[dict, str]:
        """
        Preprocess input images via FastAPI service.
        """
        if input_imgs is None:
            return {}, "No input images provided"
        
        # Extract file paths from Gradio Gallery format
        input_img_paths = self._extract_image_paths(input_imgs)
        
        if not input_img_paths:
            return {}, "No valid image paths found"
        
        print(f"Processing {len(input_img_paths)} images: {input_img_paths}")
        
        # Process via FastAPI service
        return self._process_via_fastapi(input_img_paths)
    
    def _extract_image_paths(self, input_imgs):
        """Extract file paths from various Gradio Gallery formats."""
        input_img_paths = []
        
        if isinstance(input_imgs, list):
            for item in input_imgs:
                if isinstance(item, tuple) and len(item) > 0 and item[0]:
                    input_img_paths.append(item[0])
                elif isinstance(item, dict) and 'name' in item:
                    input_img_paths.append(item['name'])
                elif isinstance(item, str) and item:
                    input_img_paths.append(item)
                elif hasattr(item, 'name') and getattr(item, 'name'):
                    input_img_paths.append(getattr(item, 'name'))
        else:
            if isinstance(input_imgs, str):
                input_img_paths = [input_imgs]
            elif hasattr(input_imgs, 'name') and getattr(input_imgs, 'name'):
                input_img_paths = [getattr(input_imgs, 'name')]
        
        # Filter valid paths
        return [path for path in input_img_paths if path and os.path.exists(path)]
    
    def _process_via_fastapi(self, input_img_paths):
        """Process images via FastAPI service"""
        file_handles = []  # Initialize before try block
        try:
            # Upload to FastAPI
            files = []
            for img_path in input_img_paths:
                file_handle = open(img_path, "rb")
                file_handles.append(file_handle)
                files.append(("files", (os.path.basename(img_path), file_handle, "image/jpeg")))
            
            upload_response = requests.post(f"{FASTAPI_URL}/upload-images", files=files)
            upload_response.raise_for_status()
            data_path = upload_response.json()["data_path"]
            
            # Preprocess via FastAPI
            preprocess_response = requests.post(
                f"{FASTAPI_URL}/preprocess",
                json={"data_path": data_path, "extract_cameras": True, "normalize_scene": True}
            )
            preprocess_response.raise_for_status()
            preprocessed_data = preprocess_response.json()
            
            # Store basic info for trajectory visualization
            W, H = preprocessed_data["width"], preprocessed_data["height"]
            scene_scale = preprocessed_data.get("scene_scale", 1.0)
            
            self.state.input_wh = (W, H)
            self.state.scene_scale = scene_scale
            self.state.data_path = data_path
            self.state.input_camera_poses = preprocessed_data.get("camera_poses", [])
            
            return {
                "input_wh": (W, H),
                "scene_scale": scene_scale,
                "camera_poses": preprocessed_data.get("camera_poses", []),
                "points": preprocessed_data.get("points", []),
                "point_colors": preprocessed_data.get("point_colors", []),
                "original_img_paths": input_img_paths,
                "data_path": data_path
            }, f"Successfully processed {len(input_img_paths)} images"
            
        except requests.RequestException as e:
            raise gr.Error(f"Failed to preprocess images: {str(e)}")
        finally:
            for f in file_handles:
                f.close()
    
    
    def visualize_scene(self, preprocessed: dict):
        """
        Set up the 3D visualization of the scene using viser.
        """
        server = self.server
        server.scene.reset()
        # Clear input camera frustums when scene is reset
        self.state.input_camera_frustums.clear()
        # Remove the server.gui.reset() call to preserve existing UI elements
        # Define enhanced background color synchronization
        def set_bkgd_color(server, theme="dark"):
            """Set background color for the scene with theme synchronization
            
            Args:
                server: ViserServer instance
                theme: "dark" or "light" theme mode
            """
            # Theme-aware background colors
            # Set consistent background color (same as demo_gr.py)
            server.scene.set_background_image(np.array([[[39, 39, 42]]], dtype=np.uint8))
        
        # Configure client theme and background
        for client in server.get_clients().values():
            client.gui.configure_theme(
                dark_mode=True,  # Enable dark mode by default
                show_share_button=False,
                control_layout="collapsible",
            )
            
        # Set background color with dark theme
        set_bkgd_color(server, theme="dark")
        
        # Use FastAPI format - simplified visualization
        self._visualize_scene_fastapi_format(preprocessed)
    
    
    def _visualize_scene_fastapi_format(self, preprocessed: dict):
        """Simplified visualization for FastAPI data"""
        server = self.server
        
        # Extract basic data
        camera_poses = preprocessed.get("camera_poses", [])
        points = preprocessed.get("points", [])
        point_colors = preprocessed.get("point_colors", [])
        scene_scale = preprocessed.get("scene_scale", 1.0)
        img_wh = preprocessed.get("input_wh", (576, 576))
        original_img_paths = preprocessed.get("original_img_paths", [])
        
        W, H = img_wh
        init_fov_deg = 54.0
        
        # Calculate scene center for camera positioning
        all_points = []
        for point_set in points:
            if point_set and len(point_set) > 0:
                all_points.extend(point_set)
        
        # Store all points in state for later access
        self.state.all_points = all_points
        
        # Set up direction based on camera poses (similar to demo_gr.py)
        if camera_poses:
            up_directions = []
            for cam in camera_poses:
                matrix = np.array(cam["matrix"], dtype=np.float32).reshape(4, 4)
                # Extract up direction (-Y axis in camera space)
                up_directions.append(-matrix[:3, 1])
            
            # Use average up direction
            if up_directions:
                avg_up_direction = np.mean(np.array(up_directions), axis=0)
                server.scene.set_up_direction(avg_up_direction)
        
        # Store image paths for reference
        frustum_image_paths = {}
        
        # Add camera frustums and point clouds
        frustum_nodes, pcd_nodes = [], []
        for i, cam in enumerate(camera_poses):
            matrix = np.array(cam["matrix"], dtype=np.float32).reshape(4, 4)
            position = matrix[:3, 3]
            rotation = matrix[:3, :3]
            
            # Load image for frustum display
            image = None
            image_path = None
            
            # First try to use image_data if available
            if cam.get("image_data") is not None:
                try:
                    image_array = np.array(cam["image_data"])
                    # Ensure image is in the right format (HWC, uint8, 0-255)
                    if image_array.dtype == np.float32 or image_array.dtype == np.float64:
                        if image_array.max() <= 1.0:  # Normalized to [0,1]
                            image_array = (image_array * 255.0).astype(np.uint8)
                        else:
                            image_array = image_array.astype(np.uint8)
                    else:
                        image_array = image_array.astype(np.uint8)
                    
                    # Ensure it's HWC format
                    if len(image_array.shape) == 3 and image_array.shape[2] in [3, 4]:
                        image = image_array[..., :3]  # Take only RGB channels
                        print(f"✅ Loaded image data for camera {i}: {image.shape}")
                    else:
                        print(f"Warning: Image data for camera {i} has unexpected shape: {image_array.shape}")
                except Exception as e:
                    print(f"Warning: Could not load image data for camera {i}: {e}")
            
            # If no image_data, try to use image_path
            elif cam.get("image_path") and os.path.exists(cam["image_path"]):
                try:
                    image_path = cam["image_path"]
                    pil_image = Image.open(image_path)
                    image = np.array(pil_image)
                    # Ensure uint8 format
                    if image.dtype != np.uint8:
                        if image.dtype == np.float32 or image.dtype == np.float64:
                            if image.max() <= 1.0:
                                image = (image * 255.0).astype(np.uint8)
                            else:
                                image = image.astype(np.uint8)
                        else:
                            image = image.astype(np.uint8)
                    # Take only RGB channels if RGBA
                    if len(image.shape) == 3 and image.shape[2] == 4:
                        image = image[..., :3]
                    print(f"✅ Loaded image from file for camera {i}: {image.shape}")
                except Exception as e:
                    print(f"Warning: Could not load image {cam['image_path']}: {e}")
            
            # If still no image, try to use original_img_paths
            elif i < len(original_img_paths) and os.path.exists(original_img_paths[i]):
                try:
                    image_path = original_img_paths[i]
                    pil_image = Image.open(image_path)
                    image = np.array(pil_image)
                    # Ensure uint8 format
                    if image.dtype != np.uint8:
                        if image.dtype == np.float32 or image.dtype == np.float64:
                            if image.max() <= 1.0:
                                image = (image * 255.0).astype(np.uint8)
                            else:
                                image = image.astype(np.uint8)
                        else:
                            image = image.astype(np.uint8)
                    # Take only RGB channels if RGBA
                    if len(image.shape) == 3 and image.shape[2] == 4:
                        image = image[..., :3]
                    print(f"✅ Loaded image from original path for camera {i}: {image.shape}")
                except Exception as e:
                    print(f"Warning: Could not load original image {original_img_paths[i]}: {e}")
            
            # Calculate FOV (convert from degrees to radians if needed)
            fov_value = cam.get("fov", 54.0)
            if fov_value > 6.28:  # Likely in degrees (> 360 degrees is impossible)
                fov_radians = np.radians(fov_value)
            else:
                fov_radians = fov_value  # Already in radians
            
            # Add camera frustum with consistent sizing
            frustum = server.scene.add_camera_frustum(
                f"/scene_assets/cameras/{i}",
                fov=fov_radians,
                aspect=W / H,
                scale=0.1 * scene_scale,  # Consistent scale based on scene size
                image=image,
                wxyz=vt.SO3.from_matrix(rotation).wxyz,
                position=position,
            )
            
            # Store frustum for visibility control
            self.state.input_camera_frustums.append(frustum)
            
            # Store image path in dictionary for reference
            if image_path:
                frustum_image_paths[f"/scene_assets/cameras/{i}"] = image_path
            
            # Add click handler to move camera to this viewpoint
            def get_handler(frustum):
                def handler(event: viser.GuiEvent) -> None:
                    assert event.client_id is not None
                    client = server.get_clients()[event.client_id]
                    with client.atomic():
                        client.camera.position = frustum.position
                        client.camera.wxyz = frustum.wxyz
                        # Set look_at as the projected origin onto the
                        # frustum's forward direction
                        look_direction = vt.SO3(frustum.wxyz).as_matrix()[:, 2]
                        position_origin = -frustum.position
                        client.camera.look_at = (
                            frustum.position
                            + np.dot(look_direction, position_origin)
                            / np.linalg.norm(position_origin)
                            * look_direction
                        )
                return handler
            
            frustum.on_click(get_handler(frustum))  # type: ignore
            frustum_nodes.append(frustum)
            
            # Add point cloud if available
            if i < len(points) and len(points[i]) > 0:
                pcd = server.scene.add_point_cloud(
                    f"/scene_assets/points/{i}",
                    np.array(points[i]),
                    np.array(point_colors[i]) if i < len(point_colors) else np.ones((len(points[i]), 3)) * 0.8,
                    point_size=0.01 * scene_scale,
                    point_shape="circle",
                )
                pcd_nodes.append(pcd)
        
        # Add scene controls
        self._add_scene_scale_controls(server, frustum_nodes, pcd_nodes, scene_scale)
        
        
        # Add Looking Glass trajectory controls
        if QUILT_COMPONENTS_AVAILABLE and self.quilt_gui_manager:
            # Use the new SEVA-integrated GUI manager
            self.quilt_gui_manager.define_gui(expand_by_default=True, order=1000)
            self.quilt_gui_manager.set_scene_scale(scene_scale)
        else:
            # Fall back to legacy GUI
            self.define_camera_gui(server, init_fov=init_fov_deg, img_wh=img_wh, scene_scale=scene_scale)
        
        # Add quilt unit visualization for legacy mode only (new SEVA components handle this automatically)
        if not (QUILT_COMPONENTS_AVAILABLE and self.quilt_gui_manager):
            self._visualize_quilt_units(server, scene_scale)
        
        # Store scene data in gui_state for later use
        self.state.scene_scale = scene_scale
        self.state.input_wh = img_wh
        
        # Update quilt state with scene data
        self.quilt_state.input_wh = img_wh
        self.quilt_state.scene_scale = scene_scale
        self.quilt_state.data_path = preprocessed.get("data_path")
        
        # Initialize quilt units only if quilt mode is enabled
        if self.quilt_state.quilt_mode:
            if QUILT_COMPONENTS_AVAILABLE and self.quilt_gui_manager:
                # Initialization is handled automatically by the new GUI manager
                pass
            else:
                # Legacy initialization would go here if needed
                print("Quilt mode enabled but new components not available")
        
        # Center the view on the scene automatically
        self._center_view_on_scene(server, all_points, scene_scale)
    
    def _center_view_on_scene(self, server, all_points, scene_scale):
        """Center the camera view on the scene point cloud"""
        if not all_points:
            return
        
        try:
            points_array = np.array(all_points)
            center = np.mean(points_array, axis=0)
            bounds = np.max(np.abs(points_array - center)) * 1.5
            
            # Set a reasonable default view
            for client_id, client in server.get_clients().items():
                with client.atomic():
                    # Position camera at a distance based on scene bounds
                    client.camera.position = center + np.array([bounds, bounds, bounds]) * 0.577
                    
                    # Look at center
                    client.camera.look_at = center
        except Exception as e:
            print(f"Error centering view: {e}")
    
    def _add_camera_navigation_controls(self, server, all_points, scene_scale):
        """Add camera navigation controls to the scene"""
        with server.gui.add_folder("Camera Navigation", expand_by_default=False, order=150):
            # Center view button
            center_view_button = server.gui.add_button(
                "Center View on Scene",
                icon=viser.Icon.CROSSHAIR,
                hint="Center the camera view on the scene point cloud"
            )
            
            @center_view_button.on_click
            def _(event):
                # Calculate scene center and bounds
                if not all_points:
                    return
                
                points_array = np.array(all_points)
                center = np.mean(points_array, axis=0)
                bounds = np.max(np.abs(points_array - center)) * 1.5
                
                # Update all client cameras
                for client_id, client in server.get_clients().items():
                    with client.atomic():
                        # Position camera at a distance based on scene bounds
                        client.camera.position = center + np.array([0, 0, bounds])
                        
                        # Look at center
                        client.camera.look_at = center
            
            # Orbit controls
            orbit_speed = server.gui.add_slider(
                "Orbit Speed",
                min=0.0,
                max=2.0,
                step=0.1,
                initial_value=0.5,
                hint="Speed of automatic orbit around the scene"
            )
            
            orbit_button = server.gui.add_button(
                "Start Orbit",
                icon=viser.Icon.REFRESH,
                hint="Start automatic orbit around the scene center"
            )
            
            stop_orbit_button = server.gui.add_button(
                "Stop Orbit",
                icon=viser.Icon.SQUARE,
                visible=False,
                hint="Stop automatic orbit"
            )
            
            # Store orbit state
            orbit_state = {"running": False, "task": None}
            
            @orbit_button.on_click
            def _(event):
                if orbit_state["running"]:
                    return
                
                orbit_button.visible = False
                stop_orbit_button.visible = True
                orbit_state["running"] = True
                
                # Calculate scene center if we have points
                if all_points:
                    center = np.mean(np.array(all_points), axis=0)
                else:
                    center = np.zeros(3)
                
                async def orbit_task():
                    angle = 0.0
                    while orbit_state["running"]:
                        for client_id, client in server.get_clients().items():
                            with client.atomic():
                                # Get current camera position and calculate distance to center
                                pos = np.array(client.camera.position)
                                look_at = np.array(client.camera.look_at) if client.camera.look_at is not None else center
                                
                                # Calculate vector from look_at to position
                                vec = pos - look_at
                                distance = np.linalg.norm(vec)
                                
                                # Rotate around the up axis
                                angle += 0.01 * orbit_speed.value
                                new_pos = look_at + np.array([
                                    distance * np.sin(angle),
                                    vec[1],  # Keep same height
                                    distance * np.cos(angle)
                                ])
                                
                                client.camera.position = new_pos
                                client.camera.look_at = look_at
                                
                        await asyncio.sleep(0.03)
                
                try:
                    loop = asyncio.get_event_loop()
                    orbit_state["task"] = loop.create_task(orbit_task())
                except RuntimeError:
                    # If no event loop exists, create one
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    orbit_state["task"] = loop.create_task(orbit_task())
            
            @stop_orbit_button.on_click
            def _(event):
                orbit_state["running"] = False
                if orbit_state["task"]:
                    orbit_state["task"].cancel()
                    orbit_state["task"] = None
                orbit_button.visible = True
                stop_orbit_button.visible = False
            
            # View presets
            view_presets = server.gui.add_dropdown(
                "View Presets",
                options=["Front", "Top", "Side", "Isometric"],
                initial_value="Isometric",
                hint="Select a preset camera view"
            )
            
            @view_presets.on_update
            def _(event):
                # Calculate scene center and bounds
                if not all_points:
                    return
                
                points_array = np.array(all_points)
                center = np.mean(points_array, axis=0)
                bounds = np.max(np.abs(points_array - center)) * 1.5
                
                for client_id, client in server.get_clients().items():
                    with client.atomic():
                        if event.value == "Front":
                            client.camera.position = center + np.array([0, 0, bounds])
                            client.camera.look_at = center
                        elif event.value == "Top":
                            client.camera.position = center + np.array([0, bounds, 0])
                            client.camera.look_at = center
                        elif event.value == "Side":
                            client.camera.position = center + np.array([bounds, 0, 0])
                            client.camera.look_at = center
                        elif event.value == "Isometric":
                            client.camera.position = center + np.array([bounds, bounds, bounds]) * 0.577
                            client.camera.look_at = center
    
    def _add_scene_scale_controls(self, server, frustum_nodes, pcd_nodes, scene_scale):
        """Add GUI controls for scene scale"""
        with server.gui.add_folder("Scene Scale", expand_by_default=False, order=200):
            camera_scale_slider = server.gui.add_slider(
                "Log camera scale", initial_value=-0.8, min=-2.0, max=2.0, step=0.1
            )

            @camera_scale_slider.on_update
            def _(event) -> None:
                # Update input camera frustums
                for i in range(len(frustum_nodes)):
                    frustum_nodes[i].scale = (
                        0.1 * scene_scale * 10**camera_scale_slider.value
                    )
                # Update input camera frustums stored in state
                for frustum in self.state.input_camera_frustums:
                    frustum.scale = (
                        0.1 * scene_scale * 10**camera_scale_slider.value
                    )
                # Update quilt unit frustums with appropriate scaling
                new_scale = scene_scale * 10**camera_scale_slider.value
                
                if QUILT_COMPONENTS_AVAILABLE and self.quilt_gui_manager:
                    # Use new GUI manager to update scale
                    self.quilt_gui_manager.set_scene_scale(new_scale)
                elif hasattr(self.quilt_state, 'unit_nodes'):
                    # Legacy scaling update
                    for node in self.quilt_state.unit_nodes:
                        if hasattr(node, 'scale'):  # Camera frustums have scale attribute
                            if "_center" in node.name:
                                # Larger scale for unit centers
                                node.scale = 0.15 * new_scale
                            else:
                                # Smaller scale for individual quilt views
                                node.scale = 0.08 * new_scale

            point_scale_slider = server.gui.add_slider(
                "Log point scale", initial_value=0.0, min=-2.0, max=2.0, step=0.1
            )

            @point_scale_slider.on_update
            def _(event) -> None:
                for i in range(len(pcd_nodes)):
                    pcd_nodes[i].point_size = (
                        0.01 * scene_scale * 10**point_scale_slider.value
                    )
    
    def define_camera_gui(self, server: viser.ViserServer, init_fov: Optional[float] = None, img_wh: tuple[int, int] = (576, 576), scene_scale: float = 1.0):
        """LEGACY: Define camera GUI controls for Looking Glass quilt generation
        
        NOTE: This method is deprecated in favor of QuiltGuiManager from traj_tool.
        It's kept for backward compatibility when SEVA components are not available.
        """
        # Use the FOV from the quilt_config if not specified
        if init_fov is None:
            init_fov = self.quilt_config.camera.fov_degrees
            
        # Create a folder for Looking Glass controls
        with server.gui.add_folder("Looking Glass", expand_by_default=True, order=1000):
            device_dropdown = server.gui.add_dropdown(
                "Device Type",
                options=list_available_devices(),
                initial_value="portrait",
                hint="Select Looking Glass device type"
            )
            
            @device_dropdown.on_update
            def _(event) -> None:
                self.quilt_config = get_device_config(event.value)
                self.camera_model = QuiltCameraModel(self.quilt_config)
                self.fastapi_generator = FastAPIQuiltGenerator(FASTAPI_URL, self.camera_model)
                # Update sliders to match new device
                rows_slider.value = self.quilt_config.quilt.rows
                columns_slider.value = self.quilt_config.quilt.columns
                fov_slider.value = self.quilt_config.camera.fov_degrees
                viewcone_slider.value = self.quilt_config.camera.viewcone_angle
                focus_distance_slider.value = self.quilt_config.camera.focus_distance
                depthiness_slider.value = self.quilt_config.camera.depthiness
            
            # Quilt Layout Settings
            rows_slider = server.gui.add_slider(
                "Quilt Rows", 
                min=4, 
                max=12, 
                step=1, 
                initial_value=self.quilt_config.quilt.rows,
                hint="Number of rows in quilt grid"
            )
            
            columns_slider = server.gui.add_slider(
                "Quilt Columns",
                min=4,
                max=16,
                step=1,
                initial_value=self.quilt_config.quilt.columns,
                hint="Number of columns in quilt grid"
            )
            
            # Camera Settings
            fov_slider = server.gui.add_slider(
                "Field of View (degrees)",
                min=8.0,
                max=60.0,
                step=0.5,
                initial_value=self.quilt_config.camera.fov_degrees,
                hint="Camera field of view - Portrait uses ~14°, others use ~54°"
            )
            
            viewcone_slider = server.gui.add_slider(
                "View Cone Angle (degrees)",
                min=20.0,
                max=50.0,
                step=1.0,
                initial_value=self.quilt_config.camera.viewcone_angle,
                hint="Total viewing angle - Portrait uses 35°, others use 40°"
            )
            
            focus_distance_slider = server.gui.add_slider(
                "Focus Distance",
                min=0.5,
                max=5.0,
                step=0.1,
                initial_value=self.quilt_config.camera.focus_distance,
                hint="Distance to focal plane where objects appear on screen"
            )
            
            depthiness_slider = server.gui.add_slider(
                "Depthiness",
                min=0.5,
                max=3.0,
                step=0.1,
                initial_value=self.quilt_config.camera.depthiness,
                hint="Controls depth effect strength - higher values increase depth"
            )
            
            # Update handlers - separate for int and float sliders
            @rows_slider.on_update
            @columns_slider.on_update
            def _on_int_slider_update(event) -> None:
                # Update quilt configuration when integer sliders change
                self.quilt_config.quilt.rows = int(rows_slider.value)
                self.quilt_config.quilt.columns = int(columns_slider.value)
                self.camera_model = QuiltCameraModel(self.quilt_config)
            
            @fov_slider.on_update
            @viewcone_slider.on_update
            @focus_distance_slider.on_update
            @depthiness_slider.on_update
            def _on_float_slider_update(event) -> None:
                # Update quilt configuration when float sliders change
                self.quilt_config.camera.fov_degrees = float(fov_slider.value)
                self.quilt_config.camera.viewcone_angle = float(viewcone_slider.value)
                self.quilt_config.camera.focus_distance = float(focus_distance_slider.value)
                self.quilt_config.camera.depthiness = float(depthiness_slider.value)
                self.camera_model = QuiltCameraModel(self.quilt_config)
                
            # Quilt Mode Toggle
            quilt_mode_checkbox = server.gui.add_checkbox(
                "Quilt Mode",
                initial_value=False,
                hint="Enable quilt mode for Looking Glass display"
            )
            
            # Visibility controls (following seva/gui.py patterns)
            input_keyframes_visible = server.gui.add_checkbox(
                "Show Input Cameras",
                initial_value=True,
                hint="Toggle visibility of input camera keyframes"
            )
            
            quilt_keyframes_visible = server.gui.add_checkbox(
                "Show Quilt Views",
                initial_value=True,
                hint="Toggle visibility of quilt camera views"
            )
            
            @quilt_mode_checkbox.on_update
            def _(event) -> None:
                is_quilt_mode = quilt_mode_checkbox.value
                print(f"🔄 Quilt mode: {'enabled' if is_quilt_mode else 'disabled'}")
                self._toggle_quilt_mode(is_quilt_mode, server)
                
            @input_keyframes_visible.on_update
            def _(event) -> None:
                self._set_input_keyframes_visible(input_keyframes_visible.value)
                
            @quilt_keyframes_visible.on_update
            def _(event) -> None:
                self._set_quilt_keyframes_visible(quilt_keyframes_visible.value)
        
        # Add camera trajectory controls
        with server.gui.add_folder("Camera Settings", expand_by_default=True, order=300):
            fov_degree_slider = server.gui.add_slider(
                "Field of view (degrees)",
                min=10.0,
                max=120.0,
                step=1.0,
                initial_value=init_fov,
            )
            
            duration_number = server.gui.add_number(
                "Duration (s)", initial_value=3.0, min=0.1, step=0.1
            )
            
            framerate_number = server.gui.add_number(
                "Frame rate", initial_value=30.0, min=1.0, step=1.0
            )
            
            transition_sec_number = server.gui.add_number(
                "Default transition (s)", initial_value=1.0, min=0.1, step=0.1
            )
            
        # Add Looking Glass controls
        with server.gui.add_folder("Looking Glass", expand_by_default=True, order=300):
            lg_status = server.gui.add_text(
                "Status", 
                initial_value="Looking Glass integration ready",
                hint="Looking Glass operation status"
            )
            
            # Quilt mode checkbox
            quilt_mode_checkbox = server.gui.add_checkbox(
                "Quilt Mode", False,
                hint="Enable quilt generation mode - automatically creates units with rows×columns grid layout"
            )
            
            @quilt_mode_checkbox.on_update
            def _(event: viser.GuiEvent):
                """Toggle between normal camera trajectory and quilt mode"""
                is_quilt_mode = quilt_mode_checkbox.value
                
                # Use the existing toggle method which handles all the necessary checks
                self._toggle_quilt_mode(is_quilt_mode, server)
                
                lg_status.value = f"Quilt mode: {'enabled' if is_quilt_mode else 'disabled'}"
        
        # Add preview render controls
        with server.gui.add_folder("Preview Render", expand_by_default=True, order=500):
            preview_render_button = server.gui.add_button(
                "Preview Render",
                hint="Show a preview of the render in the viewport",
                icon=viser.Icon.CAMERA_CHECK,
            )
            
            preview_render_stop_button = server.gui.add_button(
                "Exit Preview",
                color="red",
                icon=viser.Icon.CAMERA_CANCEL,
                visible=False,
            )
            
            @preview_render_button.on_click
            def _(event):
                # Switch to preview render mode
                preview_render_button.visible = False
                preview_render_stop_button.visible = True
                self.start_preview_render()
            
            @preview_render_stop_button.on_click
            def _(event):
                # Exit preview render mode
                preview_render_button.visible = True
                preview_render_stop_button.visible = False
                self.stop_preview_render()
    
    def get_camera_trajectory(self) -> List[Dict[str, Any]]:
        """Extract camera trajectory from viser GUI state"""
        if hasattr(self.state, "camera_traj_list") and self.state.camera_traj_list:
            return self.state.camera_traj_list
        return []
    
    def create_test_quilt(self, device_type: str = "portrait_web"):
        """Generate a test pattern quilt for device testing"""
        try:
            quilt_path = create_test_quilt(
                device_type=device_type,
                pattern="gradient",
                output_dir=EXPORT_DIR
            )
            print(f"✅ Test quilt created: {quilt_path}")
        except Exception as e:
            print(f"❌ Error creating test quilt: {str(e)}")
            print(f"Error creating test quilt: {str(e)}")
    
    # Legacy _initialize_quilt_units() method removed - now handled by QuiltGuiManager
    
    # Legacy _add_quilt_unit_controls() method removed - now handled by QuiltGuiManager
    
    def start_preview_render(self):
        """Start preview render mode (placeholder)"""
        print("Preview render mode not yet implemented")
    
    def stop_preview_render(self):
        """Stop preview render mode (placeholder)"""
        print("Exiting preview render mode")
    
    def _visualize_quilt_units(self, server, scene_scale):
        """LEGACY: Visualize quilt unit trajectories in the 3D scene (for positioning only)
        
        NOTE: This method is deprecated in favor of QuiltTrajectoryManager.visualize_quilt_units().
        It's kept for backward compatibility when SEVA components are not available.
        """
        # Store unit visualization nodes for later manipulation
        if not hasattr(self.quilt_state, 'unit_nodes'):
            self.quilt_state.unit_nodes = []
        
        # Clear existing unit visualizations
        for node in self.quilt_state.unit_nodes:
            node.remove()
        self.quilt_state.unit_nodes.clear()
        
        # Only visualize if we have quilt units
        if not hasattr(self.state, 'quilt_units') or not self.state.quilt_units:
            return
        
        # Visualize each quilt unit's trajectory positions
        for unit_idx, quilt_unit in enumerate(self.state.quilt_units):
            unit_views = quilt_unit.get("views", [])
            center_position = np.array(quilt_unit.get("center_position", [0, 0, 0]))
            
            # Create center frustum to represent the unit center (matching seva/gui.py style)
            unit_center = server.scene.add_camera_frustum(
                f"/quilt_units/unit_{unit_idx}_center",
                fov=np.radians(self.quilt_config.camera.fov_degrees),
                aspect=1.0,  # Square aspect for quilt views
                scale=0.15 * scene_scale,  # Larger scale for unit center
                color=(200, 10, 200),  # Purple for unit centers (matching seva style)
                wxyz=vt.SO3.from_matrix(np.eye(3)).wxyz,  # Identity rotation
                position=center_position,
            )
            self.quilt_state.unit_nodes.append(unit_center)
            
            # Add camera frustums for each view position in the unit (matching demo_gr.py style)
            # NOTE: Commented out to remove duplicate green frustums - only use red seva GUI frustums
            # for view_idx, view in enumerate(unit_views):
            #     position = np.array(view["position"])
            #     rotation = np.array(view["rotation"])
            #     fov = view["fov"]
            #     
            #     # Create camera frustum matching seva/gui.py keyframe style
            #     view_frustum = server.scene.add_camera_frustum(
            #         f"/quilt_units/unit_{unit_idx}_view_{view_idx}",
            #         fov=fov,
            #         aspect=self.quilt_config.quilt.aspect_ratio,  # Use quilt aspect ratio
            #         scale=0.08 * scene_scale,  # Slightly larger than original for visibility
            #         color=(10, 200, 30),  # Green for quilt views (matching seva preview style)
            #         wxyz=vt.SO3.from_matrix(rotation).wxyz,
            #         position=position,
            #     )
            #     self.quilt_state.unit_nodes.append(view_frustum)
            #     
            #     # Add click handler for view selection
            #     def get_view_click_handler(unit_idx, view_idx):
            #         def handler(event: viser.GuiEvent) -> None:
            #             print(f"Selected unit {unit_idx}, view {view_idx}")
            #             # Update the selected view state
            #             self.state.current_unit = unit_idx
            #             # Could add visual feedback here if needed
            #         return handler
            #     
            #     view_frustum.on_click(get_view_click_handler(unit_idx, view_idx))
        
        # Update quilt info in UI
        if hasattr(self.state, 'quilt_controls'):
            rows = self.quilt_config.quilt.rows
            cols = self.quilt_config.quilt.columns
            total_views = sum(len(unit.get("views", [])) for unit in self.state.quilt_units)
            print(f"Quilt visualization: {len(self.state.quilt_units)} units with {rows}x{cols} grid ({total_views} total views)")
    
    def generate_quilt_from_available_trajectories(self):
        """Generate quilt from any available trajectory data (SEVA camera_traj_list or quilt units)"""
        try:
            all_keyframes = []
            
            # Method 1: Try to get keyframes from new SEVA-integrated quilt components
            if (hasattr(self, 'quilt_gui_manager') and 
                self.quilt_gui_manager and
                hasattr(self.quilt_gui_manager, 'trajectory_manager')):
                
                quilt_units = self.quilt_gui_manager.trajectory_manager.quilt_units
                if quilt_units:
                    print(f"🎬 Found {len(quilt_units)} quilt units from SEVA-integrated components")
                    
                    # Process all quilt units in parallel for better efficiency
                    print(f"🚀 Processing {len(quilt_units)} quilt units in parallel...")
                    
                    # Prepare all unit data first
                    unit_data = []
                    for unit_idx, quilt_unit in enumerate(quilt_units):
                        print(f"📱 Preparing quilt unit {unit_idx + 1}/{len(quilt_units)}")
                        print(f"🔍 Quilt unit structure: {list(quilt_unit.keys()) if isinstance(quilt_unit, dict) else type(quilt_unit)}")
                        
                        # Extract keyframes from this specific quilt unit
                        unit_keyframes = []
                        keyframes = quilt_unit.get("keyframes", [])
                        print(f"🔍 Raw keyframes count for unit {unit_idx + 1}: {len(keyframes)}")
                        print(f"🔍 Keyframes type: {type(keyframes)}")
                        if keyframes and len(keyframes) > 0:
                            print(f"🔍 First keyframe type: {type(keyframes[0])}")
                            print(f"🔍 First keyframe attributes: {dir(keyframes[0]) if hasattr(keyframes[0], '__dict__') else 'No attributes'}")
                        
                        for kf in keyframes:
                            # Convert QuiltKeyframe to our format
                            if hasattr(kf, 'position') and hasattr(kf, 'wxyz'):
                                # Extract position and rotation
                                position = np.array(kf.position)
                                wxyz = np.array(kf.wxyz)
                                
                                # Convert quaternion to rotation matrix, then to c2w
                                from scipy.spatial.transform import Rotation as R
                                rotation = R.from_quat([wxyz[1], wxyz[2], wxyz[3], wxyz[0]])  # xyzw format
                                rotation_matrix = rotation.as_matrix()
                                
                                # Create c2w matrix
                                c2w = np.eye(4)
                                c2w[:3, :3] = rotation_matrix
                                c2w[:3, 3] = position
                                
                                # Create w2c matrix
                                w2c = np.linalg.inv(c2w)
                                
                                keyframe = {
                                    "w2c": w2c.tolist(),
                                    "c2w": c2w.tolist(), 
                                    "fov": kf.fov,
                                    "unit_id": unit_idx,
                                    "view_id": getattr(kf, 'view_id', len(unit_keyframes))
                                }
                                unit_keyframes.append(keyframe)
                        
                        # Store unit data for parallel processing
                        if unit_keyframes:
                            print(f"✅ Prepared quilt {unit_idx + 1} with {len(unit_keyframes)} keyframes")
                            print(f"🔍 Final unit_keyframes structure for unit {unit_idx + 1}:")
                            for i, kf in enumerate(unit_keyframes[:3]):  # Show first 3 keyframes
                                print(f"🔍   Keyframe {i+1}: fov={kf.get('fov', 'None')}, has_w2c={bool(kf.get('w2c'))}, unit_id={kf.get('unit_id')}")
                            if len(unit_keyframes) > 3:
                                print(f"🔍   ... and {len(unit_keyframes) - 3} more keyframes")
                            unit_data.append((unit_idx, unit_keyframes))
                    
                    # Process all units in parallel using asyncio
                    async def process_all_quilts():
                        """Process all quilt units concurrently"""
                        tasks = []
                        for unit_idx, unit_keyframes in unit_data:
                            task = self._generate_quilt_via_fastapi_async(unit_keyframes, unit_id=unit_idx)
                            tasks.append(task)
                        
                        print(f"🚀 Starting parallel processing of {len(tasks)} quilt units...")
                        results = await asyncio.gather(*tasks, return_exceptions=True)
                        
                        # Filter successful results
                        generated_quilts = []
                        for i, result in enumerate(results):
                            if isinstance(result, Exception):
                                print(f"❌ Unit {i + 1} failed: {result}")
                            elif result:
                                generated_quilts.append({
                                    "unit_id": i,
                                    "result": result,
                                    "keyframe_count": len(unit_data[i][1]) if i < len(unit_data) else 0
                                })
                                print(f"✅ Successfully generated quilt for unit {i + 1}")
                            else:
                                print(f"❌ Failed to generate quilt for unit {i + 1}")
                        
                        return generated_quilts
                    
                    # Run the optimized processing - try batch API first, fall back to parallel individual calls
                    async def run_optimized_processing():
                        try:
                            return await self._generate_batch_quilts_optimized(unit_data)
                        except Exception as batch_error:
                            print(f"⚠️ Batch API failed ({batch_error}), falling back to parallel individual calls...")
                            return await process_all_quilts()
                    
                    generated_quilts = asyncio.run(run_optimized_processing())
                    
                    if generated_quilts:
                        print(f"🎉 Successfully generated {len(generated_quilts)} quilt images from {len(quilt_units)} units")
                        
                        # Process quilt images and store in app state
                        return asyncio.run(self._process_and_display_quilts(generated_quilts))
                    else:
                        print("❌ No quilts were successfully generated")
                        return None
            
            # Method 2: Try to get keyframes from legacy quilt units
            if hasattr(self.state, 'quilt_units') and self.state.quilt_units:
                print(f"🎬 Found {len(self.state.quilt_units)} legacy quilt units")
                
                for unit_idx, quilt_unit in enumerate(self.state.quilt_units):
                    unit_views = quilt_unit.get("views", [])
                    for view in unit_views:
                        keyframe = {
                            "w2c": view["w2c"],
                            "c2w": view["c2w"], 
                            "fov": view["fov"],
                            "unit_id": unit_idx,
                            "view_id": view.get("view_id", len(all_keyframes))
                        }
                        all_keyframes.append(keyframe)
                
                if all_keyframes:
                    print(f"✅ Generated {len(all_keyframes)} keyframes from legacy quilt units")
                    return self._generate_quilt_via_fastapi(all_keyframes)
            
            # Method 3: Try to get keyframes from camera trajectory list (seva GUI state)
            if hasattr(self.state, "camera_traj_list") and self.state.camera_traj_list:
                print(f"🎬 Found {len(self.state.camera_traj_list)} keyframes from camera trajectory")
                all_keyframes = self.state.camera_traj_list.copy()
                return self._generate_quilt_via_fastapi(all_keyframes)
            
            # If no trajectory data found
            print("❌ No camera trajectory data available for quilt generation")
            print("Please create camera keyframes in the viser viewport first")
            return None
            
        except Exception as e:
            print(f"❌ Error generating quilt from trajectories: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    def _generate_quilt_via_fastapi(self, keyframes, unit_id=None):
        """Generate quilt image via FastAPI using the provided keyframes"""
        try:
            if not keyframes:
                print("❌ No keyframes provided for quilt generation")
                return None
            
            print(f"🔄 Generating quilt with {len(keyframes)} keyframes via FastAPI...")
            
            # Get data path from preprocessing
            data_path = getattr(self.state, 'data_path', None)
            if not data_path:
                print("❌ No preprocessed data available for quilt generation")
                print("Please preprocess images first by clicking 'Preprocess Images'")
                return None
            
            # Calculate scene center from point cloud data
            scene_center = np.array([0.0, 0.0, 0.0])
            scene_scale = getattr(self.state, 'scene_scale', 1.0)
            
            if hasattr(self.state, 'all_points') and self.state.all_points:
                scene_center = np.mean(np.array(self.state.all_points), axis=0)
            
            focus_distance = self.quilt_config.camera.focus_distance * scene_scale
            print(f"Scene center: {scene_center}")
            print(f"Focus distance: {focus_distance}")
            print(f"Quilt config: {self.quilt_config.quilt.columns}x{self.quilt_config.quilt.rows}")
            
            # Convert keyframes to FastAPI camera poses format
            camera_poses = []
            input_wh = self.state.input_wh or (576, 576)
            
            for kf in keyframes:
                w2c = np.array(kf["w2c"]) if isinstance(kf["w2c"], list) else kf["w2c"]
                
                # Handle different keyframe formats
                if "K" in kf:
                    K = np.array(kf["K"]) if isinstance(kf["K"], list) else kf["K"]
                else:
                    # Estimate K matrix from FOV if not provided
                    fov = kf.get("fov", np.radians(54.0))
                    if fov > 6.28:  # Likely in degrees
                        fov = np.radians(fov)
                    focal_length = input_wh[1] / (2 * np.tan(fov / 2))
                    K = np.array([
                        [focal_length, 0, input_wh[0] / 2],
                        [0, focal_length, input_wh[1] / 2],
                        [0, 0, 1]
                    ])
                
                # Convert w2c to c2w
                c2w = np.linalg.inv(w2c)
                position = c2w[:3, 3].tolist()
                rotation_matrix = c2w[:3, :3]
                
                # Convert rotation matrix to Euler angles directly (without faulty alignment)
                import scipy.spatial.transform as sst
                rotation = sst.Rotation.from_matrix(rotation_matrix)
                euler = rotation.as_euler('xyz', degrees=True).tolist()
                
                # Extract FOV from K matrix
                focal_y = K[1, 1]
                H = input_wh[1]
                fov_radians = 2 * np.arctan(0.5 * H / focal_y)
                fov_degrees = float(np.degrees(fov_radians))
                
                camera_poses.append({
                    "position": position,
                    "rotation": euler,
                    "fov": fov_degrees
                })
            
            # Generate quilt via FastAPI custom trajectory endpoint
            from datetime import datetime
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            # Include unit_id in filename if provided
            if unit_id is not None:
                output_path = f"{EXPORT_DIR}/quilt_unit{unit_id}_{timestamp}.png"
            else:
                output_path = f"{EXPORT_DIR}/quilt_{timestamp}.png"
            
            print(f"🔄 Calling FastAPI to generate quilt with {len(camera_poses)} camera poses...")
            
            # Use proper quilt configuration for FastAPI payload
            quilt_config = self.quilt_config
            total_views = quilt_config.quilt.total_views
            
            # Synchronize scene scale with algorithm inputs
            # Get the current scene scale from state or use default
            current_scene_scale = float(getattr(self.state, 'scene_scale', 1.0))
            
            # Apply scene scale to camera_scale parameter for consistent scaling
            # Use focus_distance * scene_scale for depth-aware scaling
            algorithm_camera_scale = float(quilt_config.camera.focus_distance) * current_scene_scale
            
            # Get SEVA-compatible view dimensions
            seva_width, seva_height = make_seva_compatible(
                quilt_config.quilt.view_width, 
                quilt_config.quilt.view_height
            )
            
            # Calculate optimal quilt dimensions using generic algorithm
            max_seva_frames = 40
            optimal_rows, optimal_cols, effective_views, strategy = calculate_optimal_quilt_size(
                quilt_config.quilt.rows, 
                quilt_config.quilt.columns, 
                max_seva_frames
            )
            
            if strategy != "no_reduction":
                print(f"🔧 Optimizing {quilt_config.device_name} quilt:")
                print(f"    Original: {quilt_config.quilt.rows}×{quilt_config.quilt.columns} = {total_views} views")
                print(f"    Optimized: {optimal_rows}×{optimal_cols} = {effective_views} views") 
                print(f"    Strategy: {strategy} (SEVA limit: {max_seva_frames})")
                print(f"    Efficiency: {effective_views/total_views:.1%} views retained")
            else:
                print(f"✅ {quilt_config.device_name} quilt fits SEVA limits: {effective_views} views")
            
            effective_columns = optimal_cols
            
            # Adjust camera poses to match effective views
            if len(camera_poses) > effective_views:
                # Take a subset of camera poses - evenly distributed
                indices = np.linspace(0, len(camera_poses) - 1, effective_views, dtype=int)
                effective_camera_poses = [camera_poses[i] for i in indices]
                print(f"🔧 Using {len(effective_camera_poses)} camera poses (reduced from {len(camera_poses)})")
            else:
                effective_camera_poses = camera_poses
            
            # Debug the FastAPI payload
            payload = {
                "data_path": data_path,
                "camera_poses": json.dumps(effective_camera_poses),
                "cfg": "4.0,2.0",
                "camera_scale": float(algorithm_camera_scale),  # Ensure float type
                "seed": 23,
                "H": seva_height,  # Use SEVA-compatible dimensions
                "W": seva_width,
                "T": effective_views,  # Use effective views that fit SEVA constraints
                "num_steps": 50,
                "video_save_fps": 30.0,
                "L_short": min(seva_width, seva_height)
            }
            print(f"🔍 Using quilt config: {quilt_config.device_name}")
            print(f"🔍 Original quilt dimensions: {quilt_config.quilt.columns}x{quilt_config.quilt.rows} = {total_views} views")
            print(f"🔍 Effective quilt dimensions: {effective_columns}x{quilt_config.quilt.rows} = {effective_views} views")
            print(f"🔍 Original view size: {quilt_config.quilt.view_width}x{quilt_config.quilt.view_height}")
            print(f"🔍 SEVA-compatible view size: {seva_width}x{seva_height} (divisible by 64)")
            print(f"🔍 Scene scale: {current_scene_scale}, algorithm camera_scale: {algorithm_camera_scale}")
            print(f"🔍 FastAPI payload T parameter: {payload['T']} (type: {type(payload['T'])})")
            print(f"🔍 FastAPI payload camera_poses count: {len(effective_camera_poses)}")
            print(f"🔍 Camera settings: FOV={quilt_config.camera.fov_degrees}°, viewcone={quilt_config.camera.viewcone_angle}°")
            
            try:
                response = requests.post(
                    f"{FASTAPI_URL}/generate/custom-trajectory", 
                    data=payload
                )
                response.raise_for_status()
                result = response.json()
                job_id = result["job_id"]
                
                print(f"🔄 FastAPI job started: {job_id}")
                print("Waiting for video generation to complete...")
                
                # Wait for completion and check for output
                max_wait_time = 300  # 5 minutes
                check_interval = 5   # 5 seconds
                waited_time = 0
                
                while waited_time < max_wait_time:
                    status_response = requests.get(f"{FASTAPI_URL}/status/{job_id}")
                    if status_response.status_code == 200:
                        status_data = status_response.json()
                        if status_data["status"] == "completed":
                            print("✅ Video generation completed")
                            
                            # For now, return a success message since we're generating video, not quilt
                            # In a full implementation, you'd convert the video frames to a quilt
                            print(f"Generated video available at: {status_data.get('output_path', 'Check server')}")
                            
                            # Create a placeholder success image for now
                            from PIL import Image, ImageDraw
                            success_img = Image.new('RGB', (800, 600), color=(50, 50, 50))
                            draw = ImageDraw.Draw(success_img)
                            draw.text((50, 250), f"Video generated successfully!", fill=(255, 255, 255))
                            draw.text((50, 280), f"Job ID: {job_id}", fill=(200, 200, 200))
                            draw.text((50, 310), f"Frames: {len(camera_poses)}", fill=(200, 200, 200))
                            
                            # Save the success image
                            success_path = f"{EXPORT_DIR}/generation_success_{timestamp}.png"
                            success_img.save(success_path)
                            return success_path
                            
                        elif status_data["status"] == "failed":
                            print(f"❌ Video generation failed: {status_data.get('error', 'Unknown error')}")
                            return None
                        else:
                            print(f"🔄 Status: {status_data['status']} - {status_data.get('message', '')}")
                    
                    time.sleep(check_interval)
                    waited_time += check_interval
                
                print(f"❌ Timeout waiting for job {job_id} to complete")
                return None
                
            except Exception as e:
                print(f"❌ FastAPI error: {str(e)}")
                return None
            
        except Exception as e:
            print(f"❌ Error in _generate_quilt_via_fastapi: {e}")
            import traceback
            traceback.print_exc()
            return None
    

    async def _generate_quilt_via_fastapi_async(self, keyframes, unit_id=None):
        """Async version of _generate_quilt_via_fastapi for parallel processing"""
        try:
            if not keyframes:
                print(f"❌ No keyframes provided for quilt generation (unit {unit_id})")
                return None
            
            print(f"🔄 [Unit {unit_id}] Generating quilt with {len(keyframes)} keyframes via FastAPI...")
            
            # Get data path from preprocessing
            data_path = getattr(self.state, 'data_path', None)
            if not data_path:
                print(f"❌ [Unit {unit_id}] No preprocessed data available for quilt generation")
                return None
            
            # Calculate scene center from point cloud data
            scene_center = np.array([0.0, 0.0, 0.0])
            scene_scale = getattr(self.state, 'scene_scale', 1.0)
            
            if hasattr(self.state, 'all_points') and self.state.all_points:
                scene_center = np.mean(np.array(self.state.all_points), axis=0)
            
            # Use proper quilt configuration for FastAPI payload
            quilt_config = self.quilt_config
            total_views = quilt_config.quilt.total_views
            
            # Synchronize scene scale with algorithm inputs
            current_scene_scale = float(getattr(self.state, 'scene_scale', 1.0))
            algorithm_camera_scale = float(quilt_config.camera.focus_distance) * current_scene_scale
            
            # Get SEVA-compatible dimensions
            seva_width, seva_height = make_seva_compatible(
                quilt_config.quilt.view_width, 
                quilt_config.quilt.view_height
            )
            
            # Calculate optimal quilt dimensions using generic algorithm
            max_seva_frames = 40
            optimal_rows, optimal_cols, effective_views, strategy = calculate_optimal_quilt_size(
                quilt_config.quilt.rows, 
                quilt_config.quilt.columns, 
                max_seva_frames
            )
            
            if strategy != "no_reduction":
                print(f"🔧 [Unit {unit_id}] Optimizing {quilt_config.device_name} quilt:")
                print(f"    Original: {quilt_config.quilt.rows}×{quilt_config.quilt.columns} = {total_views} views")
                print(f"    Optimized: {optimal_rows}×{optimal_cols} = {effective_views} views") 
                print(f"    Strategy: {strategy} (SEVA limit: {max_seva_frames})")
                print(f"    Efficiency: {effective_views/total_views:.1%} views retained")
            else:
                print(f"✅ [Unit {unit_id}] {quilt_config.device_name} quilt fits SEVA limits: {effective_views} views")
            
            # Convert keyframes to camera poses format 
            camera_poses = []
            input_wh = self.state.input_wh or (576, 576)
            
            for kf in keyframes[:effective_views]:  # Limit to effective views
                w2c = np.array(kf["w2c"]) if isinstance(kf["w2c"], list) else kf["w2c"]
                
                # Estimate K matrix from FOV if not provided
                fov = kf.get("fov", np.radians(54.0))
                if fov > 6.28:  # Likely in degrees
                    fov = np.radians(fov)
                focal_length = input_wh[1] / (2 * np.tan(fov / 2))
                K = np.array([
                    [focal_length, 0, input_wh[0] / 2],
                    [0, focal_length, input_wh[1] / 2],
                    [0, 0, 1]
                ])
                
                # Convert w2c to c2w and extract camera pose
                c2w = np.linalg.inv(w2c)
                position = c2w[:3, 3].tolist()
                rotation_matrix = c2w[:3, :3]
                
                # Convert rotation matrix to Euler angles
                import scipy.spatial.transform as sst
                rotation = sst.Rotation.from_matrix(rotation_matrix)
                euler = rotation.as_euler('xyz', degrees=True).tolist()
                
                # Extract FOV from K matrix
                focal_y = K[1, 1]
                H = input_wh[1]
                fov_radians = 2 * np.arctan(0.5 * H / focal_y)
                fov_degrees = float(np.degrees(fov_radians))
                
                camera_poses.append({
                    "position": position,
                    "rotation": euler,
                    "fov": fov_degrees
                })
            
            print(f"🔧 [Unit {unit_id}] Using {len(camera_poses)} camera poses (reduced from {len(keyframes)})")
            
            # Create FastAPI payload with SEVA-compatible parameters
            payload = {
                "data_path": data_path,
                "camera_poses": json.dumps(camera_poses),
                "cfg": "4.0,2.0",
                "camera_scale": float(algorithm_camera_scale),
                "seed": 23,
                "H": seva_height,
                "W": seva_width,
                "T": len(camera_poses),
                "num_steps": 50,
                "video_save_fps": 30.0,
                "L_short": min(seva_width, seva_height)
            }
            
            print(f"🔍 [Unit {unit_id}] SEVA-compatible view size: {seva_width}x{seva_height} (divisible by 64)")
            print(f"🔍 [Unit {unit_id}] FastAPI payload T parameter: {payload['T']} (type: {type(payload['T'])})")
            
            # Make async HTTP request to FastAPI
            async with aiohttp.ClientSession() as session:
                print(f"🔄 [Unit {unit_id}] FastAPI job starting...")
                
                async with session.post(
                    f"{FASTAPI_URL}/generate/custom-trajectory",
                    data=payload,
                    timeout=aiohttp.ClientTimeout(total=1800)  # 30 minute timeout
                ) as response:
                    
                    if response.status == 200:
                        result = await response.json()
                        job_id = result["job_id"]
                        print(f"🔄 [Unit {unit_id}] FastAPI job started: {job_id}")
                        
                        # Poll for completion
                        while True:
                            await asyncio.sleep(5)  # Wait 5 seconds between checks
                            
                            async with session.get(f"{FASTAPI_URL}/status/{job_id}") as status_response:
                                if status_response.status == 200:
                                    status_data = await status_response.json()
                                    status = status_data["status"]
                                    message = status_data.get("message", "")
                                    
                                    print(f"🔄 [Unit {unit_id}] Status: {status} - {message}")
                                    
                                    if status == "completed":
                                        print(f"✅ [Unit {unit_id}] Video generation completed successfully")
                                        # Return success result - could be video path or status info
                                        return {
                                            "success": True,
                                            "job_id": job_id,
                                            "output_path": status_data.get("output_path"),
                                            "unit_id": unit_id
                                        }
                                    elif status == "failed":
                                        error_msg = status_data.get("error", "Unknown error")
                                        print(f"❌ [Unit {unit_id}] Video generation failed: {error_msg}")
                                        return None
                                else:
                                    print(f"❌ [Unit {unit_id}] Failed to check job status")
                                    return None
                    else:
                        print(f"❌ [Unit {unit_id}] FastAPI request failed with status {response.status}")
                        return None
                        
        except Exception as e:
            print(f"❌ [Unit {unit_id}] Error in async quilt generation: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    async def _generate_batch_quilts_optimized(self, unit_data):
        """Optimized batch processing using the new FastAPI batch endpoint"""
        try:
            if not unit_data:
                print("❌ No unit data provided for batch processing")
                return None
            
            print(f"🚀 Using optimized batch API for {len(unit_data)} units...")
            
            # Get data path from preprocessing
            data_path = getattr(self.state, 'data_path', None)
            if not data_path:
                print("❌ No preprocessed data available for batch processing")
                return None
            
            # Prepare batch data
            quilt_config = self.quilt_config
            current_scene_scale = float(getattr(self.state, 'scene_scale', 1.0))
            algorithm_camera_scale = float(quilt_config.camera.focus_distance) * current_scene_scale
            
            # Get SEVA-compatible dimensions
            seva_width, seva_height = make_seva_compatible(
                quilt_config.quilt.view_width, 
                quilt_config.quilt.view_height
            )
            
            # Build batch payload
            batch_units = []
            for unit_idx, unit_keyframes in unit_data:
                # Calculate optimal dimensions for this unit
                optimal_rows, optimal_cols, effective_views, strategy = calculate_optimal_quilt_size(
                    quilt_config.quilt.rows, 
                    quilt_config.quilt.columns, 
                    40  # SEVA limit
                )
                
                # Convert keyframes to camera poses format
                camera_poses = []
                input_wh = self.state.input_wh or (576, 576)
                
                for kf in unit_keyframes[:effective_views]:  # Limit to effective views
                    w2c = np.array(kf["w2c"]) if isinstance(kf["w2c"], list) else kf["w2c"]
                    
                    # Estimate K matrix from FOV
                    fov = kf.get("fov", np.radians(54.0))
                    if fov > 6.28:  # Likely in degrees
                        fov = np.radians(fov)
                    focal_length = input_wh[1] / (2 * np.tan(fov / 2))
                    K = np.array([
                        [focal_length, 0, input_wh[0] / 2],
                        [0, focal_length, input_wh[1] / 2],
                        [0, 0, 1]
                    ])
                    
                    # Convert w2c to c2w and extract pose
                    c2w = np.linalg.inv(w2c)
                    position = c2w[:3, 3].tolist()
                    rotation_matrix = c2w[:3, :3]
                    
                    # Convert to Euler angles
                    import scipy.spatial.transform as sst
                    rotation = sst.Rotation.from_matrix(rotation_matrix)
                    euler = rotation.as_euler('xyz', degrees=True).tolist()
                    
                    # Extract FOV from K matrix
                    focal_y = K[1, 1]
                    H = input_wh[1]
                    fov_radians = 2 * np.arctan(0.5 * H / focal_y)
                    fov_degrees = float(np.degrees(fov_radians))
                    
                    camera_poses.append({
                        "position": position,
                        "rotation": euler,
                        "fov": fov_degrees
                    })
                
                # Add unit to batch
                batch_units.append({
                    "unit_id": unit_idx,
                    "camera_poses": camera_poses,
                    "rows": optimal_rows,
                    "columns": optimal_cols,
                    "total_views": effective_views
                })
                
                print(f"✅ [Batch] Prepared unit {unit_idx}: {len(camera_poses)} poses ({strategy} optimization)")
            
            # Create batch payload
            payload = {
                "data_path": data_path,
                "quilt_units": json.dumps(batch_units),
                "cfg": "4.0,2.0",
                "camera_scale": float(algorithm_camera_scale),
                "seed": 23,
                "H": seva_height,
                "W": seva_width,
                "L_short": min(seva_width, seva_height),
                "num_steps": 50,
                "video_save_fps": 30.0
            }
            
            print(f"🚀 Sending batch request for {len(batch_units)} units...")
            print(f"🔍 SEVA-compatible dimensions: {seva_width}x{seva_height}")
            print(f"🔍 Total frames across all units: {sum(unit['total_views'] for unit in batch_units)}")
            
            # Use connection pooling for better performance
            connector = aiohttp.TCPConnector(
                limit=10,  # Connection pool size
                limit_per_host=5,  # Max connections per host
                keepalive_timeout=30,  # Keep connections alive
                enable_cleanup_closed=True
            )
            
            timeout = aiohttp.ClientTimeout(total=3600)  # 1 hour timeout for batch
            
            async with aiohttp.ClientSession(connector=connector, timeout=timeout) as session:
                print(f"🔄 [Batch] Starting batch job...")
                
                async with session.post(
                    f"{FASTAPI_URL}/generate/batch-quilts",
                    data=payload
                ) as response:
                    
                    if response.status == 200:
                        result = await response.json()
                        batch_job_id = result["job_id"]
                        total_units = result["total_units"]
                        print(f"🔄 [Batch] Job started: {batch_job_id} ({total_units} units)")
                        
                        # Poll for completion with adaptive intervals
                        poll_interval = 10  # Start with 10 seconds
                        max_poll_interval = 60  # Max 60 seconds between polls
                        consecutive_running = 0
                        
                        while True:
                            await asyncio.sleep(poll_interval)
                            
                            async with session.get(f"{FASTAPI_URL}/status/{batch_job_id}") as status_response:
                                if status_response.status == 200:
                                    status_data = await status_response.json()
                                    status = status_data["status"]
                                    message = status_data.get("message", "")
                                    completed_units = status_data.get("completed_units", 0)
                                    
                                    print(f"🔄 [Batch] Progress: {completed_units}/{total_units} units - {message}")
                                    
                                    if status == "completed":
                                        unit_results = status_data.get("unit_results", {})
                                        print(f"✅ [Batch] All units completed!")
                                        
                                        # Convert to expected format
                                        generated_quilts = []
                                        for unit_id, result in unit_results.items():
                                            if result.get("success"):
                                                generated_quilts.append({
                                                    "unit_id": int(unit_id),
                                                    "result": result,
                                                    "keyframe_count": result.get("frames_processed", 0)
                                                })
                                        
                                        return generated_quilts
                                        
                                    elif status == "failed":
                                        error_msg = status_data.get("error", "Unknown batch error")
                                        print(f"❌ [Batch] Job failed: {error_msg}")
                                        return None
                                    elif status == "running":
                                        consecutive_running += 1
                                        # Gradually increase poll interval for long-running jobs
                                        if consecutive_running > 3:
                                            poll_interval = min(poll_interval + 5, max_poll_interval)
                                    else:
                                        consecutive_running = 0
                                        poll_interval = 10  # Reset to fast polling
                                else:
                                    print(f"❌ [Batch] Failed to check job status")
                                    return None
                    else:
                        error_text = await response.text()
                        print(f"❌ [Batch] Request failed: {response.status} - {error_text}")
                        return None
                        
        except Exception as e:
            print(f"❌ [Batch] Error in batch processing: {e}")
            import traceback
            traceback.print_exc()
            raise  # Re-raise for fallback handling
    
    async def _process_and_display_quilts(self, generated_quilts: List[Dict]) -> Optional[str]:
        """
        Process batch quilt generation results and download/display quilt images.
        
        Args:
            generated_quilts: List of quilt generation results from batch API
            
        Returns:
            Path to the first quilt image for Gradio display, or None if failed
        """
        if not generated_quilts:
            print("❌ No quilt results to process")
            return None
            
        try:
            print(f"🖼️ Processing {len(generated_quilts)} quilt results...")
            
            # Download and store quilt images
            downloaded_quilts = []
            
            for quilt_data in generated_quilts:
                unit_id = quilt_data.get("unit_id", "unknown")
                result = quilt_data.get("result", {})
                
                if not result.get("success", False):
                    print(f"❌ Unit {unit_id} was not successful: {result.get('error', 'Unknown error')}")
                    continue
                
                # Extract quilt image path
                quilt_path = result.get("quilt_image_path") or result.get("output_path")
                if not quilt_path:
                    print(f"❌ Unit {unit_id} has no quilt image path")
                    continue
                
                print(f"📥 Downloading quilt for unit {unit_id}: {quilt_path}")
                
                try:
                    # Download quilt image from FastAPI server
                    if quilt_path.startswith("/videos/"):
                        # Convert web path to full URL
                        download_url = f"{FASTAPI_URL}{quilt_path}"
                    else:
                        download_url = quilt_path
                    
                    async with aiohttp.ClientSession() as session:
                        async with session.get(download_url) as response:
                            if response.status == 200:
                                image_data = await response.read()
                                
                                # Save to local export directory
                                local_filename = f"quilt_unit_{unit_id}_{int(time.time())}.png"
                                local_path = os.path.join(EXPORT_DIR, local_filename)
                                
                                # Ensure export directory exists
                                os.makedirs(EXPORT_DIR, exist_ok=True)
                                
                                with open(local_path, 'wb') as f:
                                    f.write(image_data)
                                
                                downloaded_quilts.append({
                                    "unit_id": unit_id,
                                    "local_path": local_path,
                                    "remote_path": quilt_path,
                                    "frames_processed": result.get("frames_processed", 0),
                                    "frame_count": result.get("frame_count", 0)
                                })
                                
                                print(f"✅ Downloaded quilt for unit {unit_id}: {local_path}")
                                
                            else:
                                print(f"❌ Failed to download quilt for unit {unit_id}: HTTP {response.status}")
                                
                except Exception as download_error:
                    print(f"❌ Error downloading quilt for unit {unit_id}: {download_error}")
                    continue
            
            if not downloaded_quilts:
                print("❌ No quilts were successfully downloaded")
                return None
            
            # Store quilts in app state
            if not hasattr(self.state, 'generated_quilts'):
                self.state.generated_quilts = []
            
            self.state.generated_quilts.extend(downloaded_quilts)
            
            # Update quilt units with generated images
            if hasattr(self.state, 'quilt_units') and self.state.quilt_units:
                for quilt_data in downloaded_quilts:
                    unit_id = quilt_data["unit_id"]
                    if isinstance(unit_id, int) and unit_id < len(self.state.quilt_units):
                        self.state.quilt_units[unit_id]["quilt_image"] = quilt_data["local_path"]
                        self.state.quilt_units[unit_id]["generated_frames"] = quilt_data.get("frame_count", 0)
                        print(f"🔗 Updated quilt unit {unit_id} with generated image")
            
            print(f"🎉 Successfully processed {len(downloaded_quilts)} quilt images")
            
            # Return the first quilt image for Gradio display
            first_quilt = downloaded_quilts[0]["local_path"]
            print(f"🖼️ Displaying first quilt: {first_quilt}")
            return first_quilt
            
        except Exception as e:
            print(f"❌ Error processing quilt results: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    def _create_sphere_vertices(self, radius: float) -> np.ndarray:
        """Create vertices for a simple sphere mesh"""
        # Create a simple icosphere for unit visualization
        vertices = []
        
        # Create vertices for a simplified sphere (just an octahedron for now)
        vertices.extend([
            [0, radius, 0],     # top
            [radius, 0, 0],     # right
            [0, 0, radius],     # front
            [-radius, 0, 0],    # left
            [0, 0, -radius],    # back
            [0, -radius, 0]     # bottom
        ])
        
        return np.array(vertices, dtype=np.float32)
    
    def _create_sphere_faces(self) -> np.ndarray:
        """Create faces for a simple sphere mesh"""
        # Define faces for the octahedron
        faces = [
            [0, 1, 2], [0, 2, 3], [0, 3, 4], [0, 4, 1],  # top faces
            [5, 2, 1], [5, 3, 2], [5, 4, 3], [5, 1, 4]   # bottom faces
        ]
        
        return np.array(faces, dtype=np.int32)
    
    def _toggle_quilt_mode(self, enabled: bool, server):
        """Toggle quilt unit adjustment mode and automatically hide/show input images"""
        print(f"🔄 Quilt mode: {'enabled' if enabled else 'disabled'}")
        self.quilt_state.quilt_mode = enabled
        
        # Automatically toggle input image visibility with quilt mode
        self._toggle_input_image_visibility(enabled, server)
        
        if enabled:
            # Initialize quilt units when quilt mode is enabled (handled by new GUI manager)
            if QUILT_COMPONENTS_AVAILABLE and self.quilt_gui_manager:
                # Initialization is handled automatically by the new GUI manager
                pass
            else:
                # Legacy initialization would go here if needed
                print("Quilt mode enabled but new components not available")
            # Show unit visualizations more prominently (legacy mode only)
            if not (QUILT_COMPONENTS_AVAILABLE and self.quilt_gui_manager):
                self._visualize_quilt_units(server, self.state.scene_scale)
        else:
            # Hide or dim unit visualizations
            for node in self.quilt_state.unit_nodes:
                node.visible = False
    
    def _toggle_input_image_visibility(self, hide: bool, server):
        """Toggle visibility of input image keyframes"""
        print(f"🔄 Input images: {'hidden' if hide else 'visible'}")
        self.quilt_state.hide_input_images = hide
        
        # Toggle visibility of stored input camera frustums
        for frustum in self.state.input_camera_frustums:
            frustum.visible = not hide
    
    def _set_input_keyframes_visible(self, visible: bool):
        """Set visibility of input camera keyframes (following seva/gui.py patterns)"""
        for frustum in self.state.input_camera_frustums:
            frustum.visible = visible
            
    def _set_quilt_keyframes_visible(self, visible: bool):
        """Set visibility of quilt camera views (following seva/gui.py patterns)"""
        if QUILT_COMPONENTS_AVAILABLE and self.quilt_gui_manager:
            # Use new trajectory manager
            self.quilt_gui_manager.trajectory_manager.set_keyframes_visible(visible)
        elif hasattr(self.quilt_state, 'unit_nodes'):
            # Legacy visibility control
            for node in self.quilt_state.unit_nodes:
                node.visible = visible
    
    # Legacy _select_quilt_unit() and _highlight_selected_unit() methods removed - now handled by QuiltGuiManager
    
    # Legacy _update_unit_position() method removed - now handled by QuiltGuiManager
    
    def generate_looking_glass_quilt_from_keyframes(self):
        """Generate a Looking Glass quilt using current keyframes from GUI"""
        try:
            # Check if we have quilt units defined
            if not self.quilt_state.quilt_units:
                raise gr.Error("No quilt configuration available. Please add camera positions first.")
            
            # Get the quilt unit (we only support one quilt for now)
            quilt_unit = self.quilt_state.quilt_units[0]
            keyframes = quilt_unit["keyframes"]
            
            if not keyframes:
                raise gr.Error("No keyframes available in quilt configuration.")
            
            print(f"🔄 Generating quilt with {len(keyframes)} keyframes...")
            
            # Calculate scene center from point cloud data
            scene_center = np.array([0.0, 0.0, 0.0])
            scene_scale = getattr(self.state, 'scene_scale', 1.0)
            
            if hasattr(self.state, 'all_points') and self.state.all_points:
                scene_center = np.mean(np.array(self.state.all_points), axis=0)
            
            focus_distance = self.quilt_config.camera.focus_distance * scene_scale
            print(f"Scene center: {scene_center}")
            print(f"Focus distance: {focus_distance}")
            print(f"Quilt config: {self.quilt_config.quilt.columns}x{self.quilt_config.quilt.rows}")
            
            # Convert keyframes to FastAPI format
            camera_poses = []
            input_wh = self.state.input_wh or (576, 576)
            
            for kf in keyframes:
                w2c = np.array(kf["w2c"]) if isinstance(kf["w2c"], list) else kf["w2c"]
                K = np.array(kf["K"]) if isinstance(kf["K"], list) else kf["K"]
                
                # Convert w2c to c2w
                c2w = np.linalg.inv(w2c)
                position = c2w[:3, 3].tolist()
                rotation_matrix = c2w[:3, :3]
                rotation = sst.Rotation.from_matrix(rotation_matrix)
                euler = rotation.as_euler('xyz', degrees=True).tolist()
                
                # Extract FOV from K matrix
                focal_y = K[1, 1]
                H = input_wh[1]
                fov_radians = 2 * np.arctan(0.5 * H / focal_y)
                fov_degrees = float(np.degrees(fov_radians))
                
                camera_poses.append({
                    "position": position,
                    "rotation": euler,
                    "fov": fov_degrees
                })
            
            # Get data path from preprocessing
            data_path = getattr(self.state, 'data_path', None)
            if not data_path:
                raise gr.Error("No preprocessed data available for quilt generation")
            
            # Request quilt generation through FastAPI
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_path = f"{EXPORT_DIR}/quilt_{timestamp}.png"
            
            # Call FastAPI directly
            try:
                response = requests.post(
                    f"{FASTAPI_URL}/generate-quilt", 
                    json={
                        "data_path": data_path,
                        "camera_poses": camera_poses,
                        "quilt_config": {
                            "device_type": self.quilt_config.device_name,
                            "rows": self.quilt_config.quilt.rows,
                            "columns": self.quilt_config.quilt.columns,
                            "width": self.quilt_config.quilt.width,
                            "height": self.quilt_config.quilt.height
                        },
                        "output_path": output_path,
                        "scene_center": scene_center.tolist(),
                        "focus_distance": focus_distance
                    }
                )
                response.raise_for_status()
                result = response.json()
                
                if "quilt_path" in result:
                    print(f"✅ Looking Glass quilt generated: {output_path}")
                    gr.Info(f"Quilt saved to {output_path}")
                    
                    # Store the generated quilt in the quilt unit
                    quilt_unit["generated_frames"] = output_path
                    
                    return output_path
                else:
                    raise gr.Error("Failed to generate Looking Glass quilt")
            except Exception as e:
                raise gr.Error(f"FastAPI error: {str(e)}")
                
        except Exception as e:
            error_msg = f"Error generating quilt: {str(e)}"
            print(f"❌ {error_msg}")
            gr.Error(error_msg)
            return None
    
    # Legacy _generate_unit_frames() and _compose_final_quilt() methods removed - now handled by QuiltGuiManager


def change_theme(theme_mode, session_hash):
    """Change the theme for viser viewport
    
    Args:
        theme_mode: "dark" or "light" theme mode
        session_hash: Session hash to identify the renderer
    """
    renderer = RENDERER_STATES.get(session_hash)
    if renderer:
        renderer.set_theme(theme_mode)
        return f"Theme changed to {theme_mode} mode"
    return "No renderer found for this session"


def start_server_and_renderer(request: gr.Request):
    """Start a viser server and renderer for this session"""
    # Create a viser server for this session with a specific port
    # Use a random port between 8000-8999 to avoid conflicts
    import random
    port = random.randint(8000, 8999)
    server = viser.ViserServer(port=port)
    
    print(f"Starting server on port {server.get_port()}")
    
    # Generate a secure URL for the server
    server_url = f"http://{server.get_host()}:{server.get_port()}"
    
    # Create a renderer for this session
    renderer = LookingGlassRenderer(server)
    
    # Configure theme for server and clients
    renderer.set_theme("dark")  # Default to dark theme
    
    # Set up client connection handler to configure theme for new clients
    @server.on_client_connect
    def _(client):
        renderer.configure_client_theme(client, "dark")
    
    # Store the server and renderer in session storage
    SERVERS[request.session_hash] = server
    RENDERER_STATES[request.session_hash] = renderer
    
    # Give it enough time to start
    time.sleep(1)
    
    return (
        renderer,
        gr.HTML(
            f'<iframe src="{server_url}" style="display: block; margin: auto; width: 100%; height: min(70vh, 700px);" frameborder="0"></iframe>',
            container=True,
        ),
        request.session_hash,
    )


def stop_server_and_renderer(request: gr.Request):
    """Stop the viser server and renderer for this session"""
    if request.session_hash in SERVERS:
        print(f"Stopping server for session {request.session_hash}")
        server = SERVERS.pop(request.session_hash)
        server.stop()
    
    if request.session_hash in RENDERER_STATES:
        print(f"Cleaning up renderer for session {request.session_hash}")
        RENDERER_STATES.pop(request.session_hash)


def get_example(selection: gr.SelectData):
    """Get example images"""
    index = selection.index
    return (
        gr.Gallery(ADVANCED_EXAMPLE_MAP[index][1], visible=True),
        gr.update(visible=True),
    )


def main(server_port: int | None = None, share: bool = False):
    """Main application function"""
    with gr.Blocks() as app:
        renderer = gr.State()
        session_hash = gr.State()
        
        # Markdown header
        gr.Markdown("""
        # Looking Glass Stable Virtual Camera
        
        This app integrates:
        1. Viser 3D visualization for camera trajectory design
        2. FastAPI rendering service (via remote server)
        3. Looking Glass quilt generation for holographic displays
        
        Upload or select images, design camera trajectories, and generate Looking Glass quilts.
        """)
        
        # Main interface
        viewport = gr.HTML(container=True, render=False)
        
        with gr.Row():
            viewport.render()
        
        with gr.Row():
            with gr.Column():
                
                with gr.Group():
                    preprocess_btn = gr.Button("Preprocess Images", interactive=False)
                    preprocess_progress = gr.Textbox(
                        label="",
                        visible=False,
                        interactive=False,
                    )
                
                with gr.Group():
                    input_imgs = gr.Gallery(
                        interactive=True,
                        label="Input Images",
                        columns=4,
                        height=200,
                    )
                    
                    # Example images
                    example_imgs = gr.Gallery(
                        [e[0] for e in ADVANCED_EXAMPLE_MAP],
                        allow_preview=False,
                        preview=False,
                        label="Example Images",
                        columns=20,
                        rows=1,
                        height=115,
                    )
                    
                    example_imgs_expander = gr.Gallery(
                        visible=False,
                        interactive=False,
                        label="Selected Examples",
                        preview=True,
                        columns=20,
                        rows=1,
                    )
                    
                    with gr.Row():
                        example_imgs_backer = gr.Button("Go Back", visible=False)
                        example_imgs_confirmer = gr.Button("Confirm", visible=False)
                    
                    example_imgs.select(
                        get_example,
                        outputs=[example_imgs_expander, example_imgs_confirmer],
                    )
                    
                    example_imgs_confirmer.click(
                        lambda x: (
                            x,
                            gr.update(visible=False),
                            gr.update(visible=False),
                            gr.update(visible=False),
                            gr.update(visible=True),
                            gr.update(interactive=bool(x)),
                        ),
                        inputs=[example_imgs_expander],
                        outputs=[
                            input_imgs,
                            example_imgs_expander,
                            example_imgs_confirmer,
                            example_imgs_backer,
                            example_imgs,
                            preprocess_btn,
                        ],
                    )
                    
                    example_imgs_backer.click(
                        lambda: (
                            gr.update(visible=False),
                            gr.update(visible=False),
                            gr.update(visible=False),
                            gr.update(visible=True),
                        ),
                        outputs=[
                            example_imgs_expander,
                            example_imgs_confirmer,
                            example_imgs_backer,
                            example_imgs,
                        ],
                    )
                    
                    preprocessed = gr.State()
                    
                    # Enable preprocess button when images are selected
                    input_imgs.change(
                        lambda imgs: gr.update(interactive=bool(imgs)),
                        inputs=[input_imgs],
                        outputs=[preprocess_btn],
                    )
                    
                    preprocess_btn.click(
                        lambda r, imgs: r.preprocess(imgs) if r and imgs else (None, "No renderer or images provided"),
                        inputs=[renderer, input_imgs],
                        outputs=[preprocessed, preprocess_progress],
                        show_progress_on=[preprocess_progress],
                    )
                    
                    preprocess_btn.click(
                        lambda: gr.update(visible=True),
                        outputs=[preprocess_progress],
                    )
                    
                    preprocessed.change(
                        lambda r, *args: r.visualize_scene(*args),
                        inputs=[renderer, preprocessed],
                    )
            
            with gr.Column():
                with gr.Group():
                    output_image = gr.Image(
                        label="Generated Quilt",
                        type="filepath",
                        interactive=False,
                    )
                    
                    generate_quilt_btn = gr.Button(
                        "Generate Quilt from Camera Trajectory", 
                        variant="primary",
                        interactive=False,
                    )
        
        # Set up callbacks for direct UI interaction
        # Generate quilt from UI button (separate from viser UI)
        def enable_generate_btn(state):
            """Enable generate button if renderer has camera trajectory or quilt units"""
            renderer_obj = RENDERER_STATES.get(state)
            if renderer_obj and hasattr(renderer_obj, "state"):
                # Check for camera trajectory in seva GUI state
                has_camera_traj = (
                    hasattr(renderer_obj.state, "camera_traj_list") and 
                    renderer_obj.state.camera_traj_list
                )
                
                # Check for quilt units (new SEVA-integrated components)
                has_quilt_units = (
                    hasattr(renderer_obj, 'quilt_gui_manager') and 
                    renderer_obj.quilt_gui_manager and
                    hasattr(renderer_obj.quilt_gui_manager, 'trajectory_manager') and
                    len(renderer_obj.quilt_gui_manager.trajectory_manager.quilt_units) > 0
                )
                
                # Check for legacy quilt units
                has_legacy_quilt_units = (
                    hasattr(renderer_obj.state, 'quilt_units') and 
                    renderer_obj.state.quilt_units
                )
                
                return gr.update(interactive=has_camera_traj or has_quilt_units or has_legacy_quilt_units)
            return gr.update(interactive=False)
        
        # Register callbacks
        app.load(
            start_server_and_renderer,
            outputs=[renderer, viewport, session_hash],
        )
        app.unload(stop_server_and_renderer)
        
        # Update UI components based on renderer state
        gr.Timer(1.0).tick(
            enable_generate_btn,
            inputs=[session_hash],
            outputs=[generate_quilt_btn],
        )
        
        # Generate quilt from UI button (works with any available trajectory data)
        generate_quilt_btn.click(
            lambda r: r.generate_quilt_from_available_trajectories() if r else None,
            inputs=[renderer],
            outputs=[output_image],
        )
    
    # Start the Gradio app
    app.queue(max_size=10).launch(
        server_port=server_port,
        share=share,
        show_error=True,
        allowed_paths=[WORK_DIR, EXPORT_DIR],
        server_name="127.0.0.1",  # Use localhost for local access
    )


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Looking Glass Stable Virtual Camera App")
    parser.add_argument("--port", type=int, default=7863, help="Server port")
    parser.add_argument("--share", action="store_true", help="Create a public URL")
    args = parser.parse_args()
    
    main(server_port=args.port, share=args.share)