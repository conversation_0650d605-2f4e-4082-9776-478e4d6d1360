#!/usr/bin/env python3
"""
Test script to verify the quilt unit direction fix
"""
import numpy as np
import sys
import os

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_opencv_rotation_creation():
    """Test that OpenCV rotation matrices are created correctly"""
    
    # Test case 1: Forward direction (positive Z)
    camera_position = np.array([0.0, 0.0, 0.0])
    look_at = np.array([0.0, 0.0, 1.0])  # Look forward
    
    # Calculate forward vector
    forward = look_at - camera_position
    forward = forward / np.linalg.norm(forward)
    
    # World up
    world_up = np.array([0.0, 1.0, 0.0])
    
    # Calculate right vector
    right = np.cross(forward, world_up)
    right = right / np.linalg.norm(right)
    
    # Calculate up vector
    up = np.cross(right, forward)
    up = up / np.linalg.norm(up)
    
    # Create OpenCV rotation matrix
    opencv_rotation = np.column_stack([right, -up, forward])
    
    print("Test 1: Forward direction")
    print(f"Forward vector: {forward}")
    print(f"OpenCV rotation matrix:\n{opencv_rotation}")
    print(f"OpenCV forward direction (Z column): {opencv_rotation[:, 2]}")
    print(f"Should be positive Z: {opencv_rotation[2, 2] > 0}")
    print()
    
    # Test case 2: Backward direction (should be corrected)
    look_at_backward = np.array([0.0, 0.0, -1.0])  # Look backward
    
    forward_backward = look_at_backward - camera_position
    forward_backward = forward_backward / np.linalg.norm(forward_backward)
    
    # Check if we need to flip
    if forward_backward[2] < 0:
        print("Test 2: Backward direction detected - flipping")
        forward_backward = -forward_backward
        look_at_backward = camera_position + forward_backward * 2.0
    
    right_backward = np.cross(forward_backward, world_up)
    right_backward = right_backward / np.linalg.norm(right_backward)
    
    up_backward = np.cross(right_backward, forward_backward)
    up_backward = up_backward / np.linalg.norm(up_backward)
    
    opencv_rotation_backward = np.column_stack([right_backward, -up_backward, forward_backward])
    
    print("Test 2: Backward direction (corrected)")
    print(f"Forward vector: {forward_backward}")
    print(f"OpenCV rotation matrix:\n{opencv_rotation_backward}")
    print(f"OpenCV forward direction (Z column): {opencv_rotation_backward[:, 2]}")
    print(f"Should be positive Z: {opencv_rotation_backward[2, 2] > 0}")
    print()

if __name__ == "__main__":
    test_opencv_rotation_creation()
