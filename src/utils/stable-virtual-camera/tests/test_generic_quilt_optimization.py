#!/usr/bin/env python3
"""
Test script to demonstrate generic SEVA frame limiting for all Looking Glass configurations
"""

import math

def calculate_optimal_quilt_size(rows: int, columns: int, max_frames: int = 40):
    """Calculate optimal quilt dimensions within SEVA limits for any Looking Glass config"""
    total_views = rows * columns
    
    if total_views <= max_frames:
        return rows, columns, total_views, "no_reduction"
    
    # Strategy 1: Reduce rows only (preserve column resolution)
    optimal_rows_only = min(rows, max_frames // columns)
    option1_views = optimal_rows_only * columns if optimal_rows_only > 0 else 0
    
    # Strategy 2: Reduce columns only (preserve row resolution)
    optimal_cols_only = min(columns, max_frames // rows)
    option2_views = rows * optimal_cols_only if optimal_cols_only > 0 else 0
    
    # Strategy 3: Balanced reduction (maintain aspect ratio as much as possible)
    aspect_ratio = columns / rows
    
    # Find dimensions that maintain aspect ratio and fit within max_frames
    sqrt_max = math.sqrt(max_frames)
    if aspect_ratio >= 1.0:  # Wider than tall
        balanced_rows = max(1, int(sqrt_max / math.sqrt(aspect_ratio)))
        balanced_cols = min(columns, max_frames // balanced_rows)
    else:  # Taller than wide
        balanced_cols = max(1, int(sqrt_max * math.sqrt(aspect_ratio)))
        balanced_rows = min(rows, max_frames // balanced_cols)
    
    option3_views = balanced_rows * balanced_cols
    
    # Strategy 4: Maximize one dimension (choose the larger original dimension)
    if rows >= columns:
        # Prioritize rows
        max_rows = min(rows, max_frames)
        max_cols = max(1, min(columns, max_frames // max_rows))
        option4_views = max_rows * max_cols
        option4_strategy = "prioritize_rows"
    else:
        # Prioritize columns
        max_cols = min(columns, max_frames)
        max_rows = max(1, min(rows, max_frames // max_cols))
        option4_views = max_rows * max_cols
        option4_strategy = "prioritize_columns"
    
    # Choose the strategy that maximizes views while staying within limits
    strategies = [
        (option1_views, optimal_rows_only, columns, "reduce_rows"),
        (option2_views, rows, optimal_cols_only, "reduce_columns"),
        (option3_views, balanced_rows, balanced_cols, "balanced"),
        (option4_views, max_rows if rows >= columns else rows, 
         columns if rows >= columns else max_cols, option4_strategy)
    ]
    
    # Filter out invalid strategies (where views would be 0 or negative)
    valid_strategies = [(v, r, c, s) for v, r, c, s in strategies if v > 0 and v <= max_frames]
    
    if not valid_strategies:
        # Fallback: simple reduction to exactly max_frames
        return max_frames // columns, columns if columns <= max_frames else 1, max_frames, "fallback"
    
    # Choose the strategy with maximum views
    best_views, best_rows, best_cols, strategy = max(valid_strategies, key=lambda x: x[0])
    
    return int(best_rows), int(best_cols), int(best_views), strategy


def test_all_looking_glass_configs():
    """Test optimization for all Looking Glass device configurations"""
    
    # Device configurations from lookingglass_config.py
    devices = {
        "Portrait": (6, 8),      # 6×8 = 48 views
        "Go": (6, 11),           # 6×11 = 66 views  
        "16\" Landscape": (7, 7), # 7×7 = 49 views
        "16\" Portrait": (6, 11), # 6×11 = 66 views
        "32\" Landscape": (7, 7), # 7×7 = 49 views
        "32\" Portrait": (6, 11), # 6×11 = 66 views
        "65\"": (9, 8),          # 9×8 = 72 views
    }
    
    print("🔍 SEVA Frame Limiting Optimization for All Looking Glass Devices")
    print("=" * 80)
    print(f"{'Device':<15} {'Original':<12} {'Optimized':<12} {'Strategy':<15} {'Efficiency':<10} {'Speedup'}")
    print("-" * 80)
    
    for device_name, (rows, cols) in devices.items():
        original_views = rows * cols
        opt_rows, opt_cols, opt_views, strategy = calculate_optimal_quilt_size(rows, cols, 40)
        
        efficiency = opt_views / original_views
        
        # Calculate potential parallel speedup
        # Assume each unit takes 2 minutes, and we have multiple units
        typical_units = 4  # Common case: 4 quilt units
        if original_views <= 40:
            speedup = f"{typical_units}x (parallel)"
        else:
            speedup = f"{typical_units}x (parallel)"
        
        print(f"{device_name:<15} {rows}×{cols}={original_views:<5} {opt_rows}×{opt_cols}={opt_views:<5} {strategy:<15} {efficiency:.1%}      {speedup}")
    
    print("\n" + "=" * 80)
    print("🚀 Key Benefits:")
    print("• All configs automatically optimized for SEVA's 40-frame limit")
    print("• Parallel processing provides 4x+ speedup regardless of optimization")
    print("• Strategy selection maximizes view count within constraints")
    print("• Works seamlessly across all Looking Glass device types")
    
    print("\n🎯 Optimization Strategies:")
    print("• reduce_rows: Maintain horizontal resolution, reduce vertical")
    print("• reduce_columns: Maintain vertical resolution, reduce horizontal") 
    print("• balanced: Maintain aspect ratio as much as possible")
    print("• prioritize_rows/columns: Maximize the larger dimension")
    print("• no_reduction: Already fits within limits")


def demo_specific_cases():
    """Demonstrate optimization for specific challenging cases"""
    
    print("\n" + "=" * 60)
    print("🔬 Detailed Analysis of Optimization Strategies")
    print("=" * 60)
    
    test_cases = [
        ("Looking Glass Go", 6, 11, "High column count"),
        ("Looking Glass 65\"", 9, 8, "High row count"), 
        ("Custom Large", 10, 10, "Square high-res"),
        ("Custom Wide", 4, 20, "Very wide aspect"),
        ("Already Optimal", 5, 8, "Within limits"),
    ]
    
    for name, rows, cols, description in test_cases:
        print(f"\n📱 {name} ({description})")
        print(f"Original: {rows}×{cols} = {rows*cols} views")
        
        opt_rows, opt_cols, opt_views, strategy = calculate_optimal_quilt_size(rows, cols, 40)
        
        print(f"Optimized: {opt_rows}×{opt_cols} = {opt_views} views")
        print(f"Strategy: {strategy}")
        print(f"Efficiency: {opt_views/(rows*cols):.1%} views retained")
        print(f"Reduction: {rows*cols - opt_views} views removed")
        
        # Show all strategy options for comparison
        total_views = rows * cols
        if total_views > 40:
            print("All strategies considered:")
            
            # Strategy 1: Reduce rows
            opt_rows_only = min(rows, 40 // cols)
            if opt_rows_only > 0:
                option1_views = opt_rows_only * cols
                print(f"  • Reduce rows: {opt_rows_only}×{cols} = {option1_views} views")
            
            # Strategy 2: Reduce columns
            opt_cols_only = min(cols, 40 // rows)
            if opt_cols_only > 0:
                option2_views = rows * opt_cols_only
                print(f"  • Reduce columns: {rows}×{opt_cols_only} = {option2_views} views")
            
            # Strategy 3: Balanced
            aspect_ratio = cols / rows
            sqrt_max = math.sqrt(40)
            if aspect_ratio >= 1.0:
                balanced_rows = max(1, int(sqrt_max / math.sqrt(aspect_ratio)))
                balanced_cols = min(cols, 40 // balanced_rows)
            else:
                balanced_cols = max(1, int(sqrt_max * math.sqrt(aspect_ratio)))
                balanced_rows = min(rows, 40 // balanced_cols)
            option3_views = balanced_rows * balanced_cols
            print(f"  • Balanced: {balanced_rows}×{balanced_cols} = {option3_views} views")
            
            print(f"  ✅ Selected: {strategy} (maximizes views)")


if __name__ == "__main__":
    test_all_looking_glass_configs()
    demo_specific_cases()