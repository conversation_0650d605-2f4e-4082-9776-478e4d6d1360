#!/usr/bin/env python3
"""
Comprehensive Test Suite for QuiltCameraModel

Tests the enhanced camera model with proper Looking Glass specifications.
"""

import numpy as np
import torch
import pytest
from typing import <PERSON><PERSON>

from traj_tool.quilt_camera import QuiltCameraModel
from traj_tool.lookingglass_config import get_default_config, get_device_config


class TestQuiltCameraModel:
    """Test suite for QuiltCameraModel"""
    
    def test_camera_initialization(self):
        """Test camera model initialization"""
        config = get_default_config()
        camera = QuiltCameraModel(config)
        assert camera.config == config
        
    def test_trajectory_generation(self):
        """Test basic trajectory generation"""
        camera = QuiltCameraModel()
        center_pos = np.array([0.0, 0.0, 2.0])
        look_at = np.array([0.0, 0.0, 0.0])
        
        poses, fovs = camera.generate_quilt_trajectory(
            center_position=center_pos,
            look_at=look_at
        )
        
        # Check output types and shapes
        assert isinstance(poses, torch.Tensor)
        assert isinstance(fovs, torch.Tensor)
        assert poses.shape[0] == fovs.shape[0]
        assert poses.shape[1:] == (4, 4)  # 4x4 pose matrices
        
    def test_view_ordering(self):
        """Test proper Looking Glass view ordering (View 0 = bottom-left)"""
        config = get_default_config()
        camera = QuiltCameraModel(config)
        
        poses, _ = camera.generate_quilt_trajectory()
        positions = poses[:, :3, 3].numpy()
        
        rows, cols = config.quilt.rows, config.quilt.columns
        
        # View 0 should be bottom-left
        view_0_pos = positions[0]
        # View (cols-1) should be bottom-right
        view_br_pos = positions[cols-1]
        # View ((rows-1)*cols) should be top-left
        view_tl_pos = positions[(rows-1)*cols]
        
        # Bottom-right should be to the right of bottom-left
        assert view_br_pos[0] > view_0_pos[0]
        # Top-left should be above bottom-left
        assert view_tl_pos[1] > view_0_pos[1]
        
    def test_camera_orientations(self):
        """Test that all cameras point toward the look_at point"""
        camera = QuiltCameraModel()
        center_pos = np.array([0.0, 0.0, 2.0])
        look_at = np.array([0.0, 0.0, 0.0])
        
        poses, _ = camera.generate_quilt_trajectory(
            center_position=center_pos,
            look_at=look_at
        )
        
        positions = poses[:, :3, 3].numpy()
        rotations = poses[:, :3, :3].numpy()
        
        for i, (pos, rot) in enumerate(zip(positions, rotations)):
            # Get camera forward direction (negative Z-axis for OpenGL)
            forward = -rot[:, 2]
            
            # Calculate expected direction
            expected = look_at - pos
            expected = expected / np.linalg.norm(expected)
            
            # Check alignment (should be very close)
            dot_product = np.dot(forward, expected)
            angle_error = np.degrees(np.arccos(np.clip(dot_product, -1, 1)))
            
            assert angle_error < 1.0, f"Camera {i} angle error: {angle_error:.2f}°"
            
    def test_coordinate_system_consistency(self):
        """Test coordinate system consistency with SEVA OpenGL conventions"""
        camera = QuiltCameraModel()
        poses, _ = camera.generate_quilt_trajectory()
        
        # Check that all poses are valid 4x4 matrices
        for pose in poses:
            pose_np = pose.numpy()
            
            # Check homogeneous coordinates
            assert np.allclose(pose_np[3, :], [0, 0, 0, 1])
            
            # Check rotation matrix orthogonality
            rotation = pose_np[:3, :3]
            should_be_identity = rotation @ rotation.T
            assert np.allclose(should_be_identity, np.eye(3), atol=1e-6)
            
            # Check determinant (should be 1 for proper rotation)
            assert np.allclose(np.linalg.det(rotation), 1.0, atol=1e-6)
            
    def test_different_device_configs(self):
        """Test with different Looking Glass device configurations"""
        devices = ["portrait", "go", "16_landscape", "32_landscape", "65"]
        
        for device_type in devices:
            config = get_device_config(device_type)
            camera = QuiltCameraModel(config)
            
            poses, fovs = camera.generate_quilt_trajectory()
            
            # Check expected number of views
            expected_views = config.quilt.rows * config.quilt.columns
            assert len(poses) == expected_views
            assert len(fovs) == expected_views
            
    def test_camera_spacing(self):
        """Test proper camera spacing for light field parallax"""
        camera = QuiltCameraModel()
        poses, _ = camera.generate_quilt_trajectory()
        positions = poses[:, :3, 3].numpy()
        
        # Check that cameras are not all at the same position
        position_variance = np.var(positions, axis=0)
        # Only check X and Y variance (Z can be constant for light field cameras)
        assert np.all(position_variance[:2] > 0), "Cameras should have different X,Y positions"
        
        # Check that spacing is reasonable (not too close or too far)
        min_distance = float('inf')
        max_distance = 0.0
        
        for i in range(len(positions)):
            for j in range(i+1, len(positions)):
                dist = np.linalg.norm(positions[i] - positions[j])
                min_distance = min(min_distance, dist)
                max_distance = max(max_distance, dist)
        
        # Reasonable spacing constraints
        assert min_distance > 0.01, "Cameras too close together"
        assert max_distance < 10.0, "Cameras too far apart"


def test_coordinate_system_fixes():
    """Test that coordinate system fixes from the issues are working"""
    camera = QuiltCameraModel()
    
    # Test parameters from the original issue
    center_pos = np.array([0.0, 0.0, 2.0])
    look_at = np.array([0.0, 0.0, 0.0])
    
    poses, _ = camera.generate_quilt_trajectory(
        center_position=center_pos,
        look_at=look_at
    )
    
    # Extract first camera for detailed testing
    first_pose = poses[0].numpy()
    first_position = first_pose[:3, 3]
    first_rotation = first_pose[:3, :3]
    
    # Test that camera is pointing toward look_at (not away)
    forward_vector = -first_rotation[:, 2]  # Negative Z-axis for OpenGL
    expected_direction = look_at - first_position
    expected_direction = expected_direction / np.linalg.norm(expected_direction)
    
    # Should be pointing toward look_at, not away
    dot_product = np.dot(forward_vector, expected_direction)
    assert dot_product > 0.9, f"Camera pointing wrong direction: dot={dot_product}"
    
    print("✓ Coordinate system fixes verified")


def run_comprehensive_tests():
    """Run all tests and report results"""
    print("Running Comprehensive QuiltCameraModel Tests")
    print("=" * 50)
    
    test_suite = TestQuiltCameraModel()
    
    tests = [
        ("Initialization", test_suite.test_camera_initialization),
        ("Trajectory Generation", test_suite.test_trajectory_generation),
        ("View Ordering", test_suite.test_view_ordering),
        ("Camera Orientations", test_suite.test_camera_orientations),
        ("Coordinate System", test_suite.test_coordinate_system_consistency),
        ("Device Configs", test_suite.test_different_device_configs),
        ("Camera Spacing", test_suite.test_camera_spacing),
        ("Coordinate Fixes", test_coordinate_system_fixes)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        try:
            test_func()
            print(f"✓ {test_name}")
            passed += 1
        except Exception as e:
            print(f"✗ {test_name}: {e}")
            failed += 1
    
    print(f"\nResults: {passed} passed, {failed} failed")
    return failed == 0


if __name__ == "__main__":
    success = run_comprehensive_tests()
    exit(0 if success else 1)