#!/usr/bin/env python3
"""
Test Runner for Looking Glass Components

Runs all Looking Glass tests and provides summary report.
"""

import sys
import os
import subprocess
from pathlib import Path

def run_test_file(test_file: Path) -> bool:
    """Run a single test file and return success status"""
    try:
        # Set up environment
        env = os.environ.copy()
        env['PYTHONPATH'] = str(test_file.parent.parent.parent)
        
        result = subprocess.run(
            [sys.executable, str(test_file)],
            capture_output=True,
            text=True,
            timeout=60,  # Increased timeout for debug scripts
            env=env
        )
        
        print(f"\n{'='*60}")
        print(f"Running: {test_file.name}")
        print(f"{'='*60}")
        print(result.stdout)
        
        if result.stderr:
            print("STDERR:")
            print(result.stderr)
        
        return result.returncode == 0
        
    except subprocess.TimeoutExpired:
        print(f"✗ {test_file.name}: TIMEOUT")
        return False
    except Exception as e:
        print(f"✗ {test_file.name}: ERROR - {e}")
        return False

def main():
    """Run all Looking Glass tests"""
    test_dir = Path(__file__).parent
    
    print("Looking Glass Test Suite")
    print("=" * 60)
    
    # Find all test files
    test_files = [
        test_dir / "test_quilt_camera.py",
        test_dir / "test_lookingglass_camera.py", 
        test_dir / "test_camera_issues.py",
        test_dir / "debug_quilt_orientation.py"
    ]
    
    # Filter to existing files and report missing ones
    existing_files = []
    missing_files = []
    
    for f in test_files:
        if f.exists():
            existing_files.append(f)
        else:
            missing_files.append(f.name)
    
    if missing_files:
        print(f"Missing test files: {', '.join(missing_files)}")
    
    if not existing_files:
        print("No test files found!")
        return 1
    
    print(f"Found {len(existing_files)} test files:")
    for f in existing_files:
        print(f"  - {f.name}")
    
    # Run each test
    results = []
    for test_file in existing_files:
        success = run_test_file(test_file)
        results.append((test_file.name, success))
    
    # Summary
    print(f"\n{'='*60}")
    print("TEST SUMMARY")
    print(f"{'='*60}")
    
    passed = sum(1 for _, success in results if success)
    total = len(results)
    
    for test_name, success in results:
        status = "✓ PASS" if success else "✗ FAIL"
        print(f"{status:8} {test_name}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    return 0 if passed == total else 1

if __name__ == "__main__":
    sys.exit(main())