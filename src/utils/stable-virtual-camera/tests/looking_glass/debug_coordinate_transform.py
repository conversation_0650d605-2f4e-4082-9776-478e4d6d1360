#!/usr/bin/env python3
"""
Debug coordinate system transformations in the Looking Glass implementation.

This script analyzes the coordinate system transformations between:
1. Input camera poses (from dust3r/FastAPI)
2. SEVA coordinate system
3. Looking Glass camera model
"""

import numpy as np


def analyze_coordinate_transforms():
    """Analyze the coordinate system transformations that might be causing issues"""
    print("Coordinate System Analysis")
    print("=" * 50)
    
    # Sample input pose matrix from user's log
    # This represents a camera from dust3r prediction
    sample_input_matrix = np.array([
        [-1.55462809, -0.2578397, -1.54485265, 1.0],  # This looks wrong - should be 4x4
        [0, 0, 0, 0],
        [0, 0, 0, 0],
        [0, 0, 0, 1]
    ])
    
    # Let's create a more realistic input matrix based on the log data
    # From: "Adding quilt unit: pos=[-1.55462809 -0.2578397  -1.54485265], look_at=[-5.63933061 -0.98975507 -4.43287787], forward=[-0.80792295 -0.14476727 -0.57122932]"
    
    input_position = np.array([-1.55462809, -0.2578397, -1.54485265])
    forward_from_log = np.array([-0.80792295, -0.14476727, -0.57122932])
    
    print("Sample data from user's log:")
    print(f"Input position: {input_position}")
    print(f"Forward vector: {forward_from_log}")
    
    # This forward vector seems problematic - let's analyze it
    print(f"Forward vector magnitude: {np.linalg.norm(forward_from_log)}")
    print(f"Forward vector normalized: {forward_from_log / np.linalg.norm(forward_from_log)}")
    
    # Let's reconstruct what the input matrix should look like
    # From the log: "look_at=[-5.63933061 -0.98975507 -4.43287787]"
    look_at_from_log = np.array([-5.63933061, -0.98975507, -4.43287787])
    
    # Calculate the expected forward direction
    expected_forward = look_at_from_log - input_position
    expected_forward_norm = expected_forward / np.linalg.norm(expected_forward)
    
    print(f"\nExpected forward calculation:")
    print(f"look_at: {look_at_from_log}")
    print(f"Expected forward: {expected_forward}")
    print(f"Expected forward normalized: {expected_forward_norm}")
    print(f"Expected forward magnitude: {np.linalg.norm(expected_forward)}")
    
    # Compare with what's in the log
    print(f"\nComparison:")
    print(f"Log forward:      {forward_from_log}")
    print(f"Expected forward: {expected_forward}")
    print(f"Difference:       {expected_forward - forward_from_log}")
    
    # The issue might be in how the camera matrix is being constructed or extracted
    
    print("\n" + "=" * 50)
    print("POTENTIAL ISSUES IDENTIFIED:")
    print("1. Forward vector calculation in lookingglass_app.py line 204")
    print("2. Camera matrix extraction from input_pose['matrix']")
    print("3. Coordinate system conversion between dust3r and SEVA")


def debug_camera_matrix_extraction():
    """Debug how camera matrices are being extracted"""
    print("\nCamera Matrix Extraction Analysis")
    print("=" * 30)
    
    # Simulate the problematic code from lookingglass_app.py
    print("Current code in lookingglass_app.py:")
    print("camera_forward = -input_rotation[:, 2]  # Line 204")
    
    # Create a test rotation matrix
    # Let's assume this is what comes from dust3r
    test_rotation = np.array([
        [1, 0, 0],
        [0, 0, -1],  # Y points down (OpenCV convention)
        [0, 1, 0]    # Z points forward (OpenCV convention)
    ])
    
    print(f"\nTest rotation matrix (OpenCV style):")
    print(test_rotation)
    print(f"X-axis (right): {test_rotation[:, 0]}")
    print(f"Y-axis (down):  {test_rotation[:, 1]}")
    print(f"Z-axis (forward): {test_rotation[:, 2]}")
    
    # Apply the current extraction method
    current_forward = -test_rotation[:, 2]
    print(f"\nCurrent extraction (-Z): {current_forward}")
    
    # What it should be for OpenGL (SEVA convention)
    # In OpenGL: X=right, Y=up, Z=back (so camera looks down -Z)
    correct_forward_opengl = -test_rotation[:, 2]  # This is actually correct for OpenGL
    
    # But the issue might be that dust3r returns matrices in a different convention
    print(f"OpenGL forward (-Z):     {correct_forward_opengl}")
    
    # Let's test different conventions
    opencv_forward = test_rotation[:, 2]  # OpenCV: camera looks down +Z
    print(f"OpenCV forward (+Z):     {opencv_forward}")
    
    print("\nThe issue might be that dust3r matrices are in a different coordinate system!")


def propose_fix():
    """Propose a fix for the coordinate system issues"""
    print("\n" + "=" * 50)
    print("PROPOSED FIX:")
    print("=" * 50)
    
    print("""
The issue appears to be in coordinate system handling. Here's the fix:

1. VERIFY INPUT MATRIX FORMAT:
   - Check if dust3r returns OpenCV or OpenGL matrices
   - Ensure proper 4x4 matrix format

2. FIX FORWARD VECTOR EXTRACTION:
   Current: camera_forward = -input_rotation[:, 2]
   
   Need to check the coordinate system of input matrices:
   - If OpenCV (Y down, Z forward): forward = input_rotation[:, 2]
   - If OpenGL (Y up, Z back): forward = -input_rotation[:, 2]

3. COORDINATE SYSTEM CONVERSION:
   Add explicit conversion if needed:
   
   # Convert from OpenCV to OpenGL if necessary
   if input_is_opencv:
       # OpenCV to OpenGL conversion
       conversion_matrix = np.array([
           [1,  0,  0],
           [0, -1,  0],  # Flip Y
           [0,  0, -1]   # Flip Z
       ])
       input_rotation = input_rotation @ conversion_matrix

4. VALIDATE QUILT UNIT ORIENTATION:
   - Ensure all cameras in a quilt unit point toward the same focal region
   - Check that camera spacing creates proper parallax for holographic viewing
""")


def main():
    """Run the coordinate system analysis"""
    analyze_coordinate_transforms()
    debug_camera_matrix_extraction()
    propose_fix()
    
    print("\n" + "=" * 50)
    print("RECOMMENDATION:")
    print("Test the coordinate system conversion in lookingglass_app.py")
    print("Focus on line 204: camera_forward = -input_rotation[:, 2]")
    print("Try: camera_forward = input_rotation[:, 2] instead")


if __name__ == "__main__":
    main()