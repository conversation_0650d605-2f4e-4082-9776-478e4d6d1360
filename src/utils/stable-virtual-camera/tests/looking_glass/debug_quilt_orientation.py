#!/usr/bin/env python3
"""
Debug script to understand quilt camera orientation issues
"""

import sys
import os
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

import numpy as np
import viser.transforms as vt
from traj_tool.quilt_camera import QuiltCameraModel
from traj_tool.lookingglass_config import get_default_config

def test_quilt_orientation():
    """Test camera orientation from quilt generation"""
    print("=== Debugging Quilt Camera Orientation ===")
    
    # Setup camera model
    config = get_default_config()
    camera_model = QuiltCameraModel(config)
    
    # Test parameters
    center_position = np.array([0.0, 0.0, 2.0])
    look_at = np.array([0.0, 0.0, 0.0])
    
    print(f"Center position: {center_position}")
    print(f"Look at: {look_at}")
    print(f"Expected camera forward: {(look_at - center_position) / np.linalg.norm(look_at - center_position)}")
    
    # Generate quilt trajectory
    poses_tensor, fovs_tensor = camera_model.generate_quilt_trajectory(
        center_position=center_position,
        look_at=look_at
    )
    
    poses = poses_tensor.numpy()
    
    print(f"\nGenerated {len(poses)} poses")
    
    # Check first few poses
    for i in range(min(3, len(poses))):
        pose = poses[i]
        position = pose[:3, 3]
        rotation = pose[:3, :3]
        
        print(f"\n--- Camera {i} ---")
        print(f"Position: {position}")
        print(f"Rotation matrix:\n{rotation}")
        
        # Extract camera axes
        right = rotation[:, 0]    # X-axis
        up = rotation[:, 1]       # Y-axis
        forward = rotation[:, 2]  # Z-axis
        
        print(f"Right: {right}")
        print(f"Up: {up}")
        print(f"Forward (Z-axis): {forward}")
        
        # OpenGL convention: cameras look down negative Z
        opengl_forward = -forward
        print(f"OpenGL forward (-Z): {opengl_forward}")
        
        # Expected direction toward look_at
        expected = (look_at - position) / np.linalg.norm(look_at - position)
        print(f"Expected direction: {expected}")
        
        # Check alignment
        dot_z = np.dot(forward, expected)
        dot_neg_z = np.dot(opengl_forward, expected)
        
        print(f"Dot with Z-axis: {dot_z:.4f}")
        print(f"Dot with -Z-axis: {dot_neg_z:.4f}")
        
        # Test viser conversion
        wxyz = vt.SO3.from_matrix(rotation).wxyz
        print(f"Viser wxyz: {wxyz}")
        
        # Convert back to test
        recovered_rotation = vt.SO3(wxyz).as_matrix()
        print(f"Recovered rotation matches: {np.allclose(rotation, recovered_rotation)}")
        
        # Test SE3 transformation
        se3 = vt.SE3.from_rotation_and_translation(vt.SO3(wxyz), position)
        print(f"SE3 translation: {se3.translation()}")
        print(f"SE3 rotation wxyz: {se3.rotation().wxyz}")
        
        # Test with offset for camera movement
        se3_offset = se3 @ vt.SE3.from_translation(np.array([0.0, 0.0, -0.5]))
        print(f"SE3 with offset translation: {se3_offset.translation()}")
        print(f"SE3 with offset rotation: {se3_offset.rotation().wxyz}")

if __name__ == "__main__":
    test_quilt_orientation()