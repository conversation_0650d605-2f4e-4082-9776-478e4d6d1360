/**
 * llmBackpressureUtils.js
 * Utilities for adjusting LLM request parameters based on backpressure
 */

/**
 * Adjust LLM request parameters based on backpressure state
 * @param {Object} originalParams - Original LLM request parameters
 * @param {Object} backpressureState - Current backpressure state
 * @returns {Object} - Adjusted LLM request parameters
 */
export function adjustLLMRequestParams(originalParams, backpressureState) {
    if (!backpressureState) {
        return originalParams;
    }
    
    // Make a copy of the original parameters
    const adjustedParams = { ...originalParams };
    
    // Get backpressure level (0-10 scale)
    const backpressureLevel = backpressureState.backpressureLevel || 0;
    
    // Only adjust if there's significant backpressure
    if (backpressureLevel >= 3) {
        // Calculate adjustment factor (0.3 to 1.0)
        const adjustmentFactor = Math.max(0.3, 1 - (backpressureLevel / 10 * 0.7));
        
        // Adjust max_tokens to reduce generation length under pressure
        if (adjustedParams.max_tokens) {
            adjustedParams.max_tokens = Math.floor(adjustedParams.max_tokens * adjustmentFactor);
        }
        
        // Increase frequency_penalty to slow down generation
        if (adjustedParams.frequency_penalty !== undefined) {
            adjustedParams.frequency_penalty = Math.min(
                2.0, 
                adjustedParams.frequency_penalty + (backpressureLevel / 10 * 0.5)
            );
        } else {
            adjustedParams.frequency_penalty = backpressureLevel / 10 * 0.5;
        }
        
        // Increase presence_penalty to encourage diversity and potentially shorter responses
        if (adjustedParams.presence_penalty !== undefined) {
            adjustedParams.presence_penalty = Math.min(
                2.0,
                adjustedParams.presence_penalty + (backpressureLevel / 10 * 0.5)
            );
        } else {
            adjustedParams.presence_penalty = backpressureLevel / 10 * 0.5;
        }
        
        // Adjust temperature to be slightly lower under high backpressure
        // This can help produce more predictable and potentially shorter responses
        if (adjustedParams.temperature !== undefined && backpressureLevel >= 7) {
            adjustedParams.temperature = Math.max(
                0.3,
                adjustedParams.temperature * 0.8
            );
        }
    }
    
    return adjustedParams;
}

/**
 * Get backpressure state from streaming processor
 * @param {Object} streamProcessor - The streaming processor instance
 * @returns {Object|null} - Backpressure state or null if not available
 */
export function getBackpressureState(streamProcessor) {
    if (!streamProcessor || !streamProcessor.backpressureController) {
        return null;
    }
    
    try {
        // Get current state
        const currentState = {
            ttsTextBufferLength: streamProcessor.ttsTextBuffer?.length || 0,
            ttsQueueLength: streamProcessor.ttsQueue?.length || 0,
            audioQueueSize: streamProcessor.audioPlayer?.playbackQueue?.length || 0,
            pendingTtsRequests: streamProcessor.pendingTtsRequests || 0,
            isPlaying: streamProcessor.audioPlayer?.isPlaying || false,
            streamEnded: streamProcessor.streamEnded || false
        };
        
        // Update and return backpressure state
        return streamProcessor.backpressureController.updateState(currentState);
    } catch (error) {
        console.error('Error getting backpressure state:', error);
        return null;
    }
}

/**
 * Check if LLM request should be throttled based on backpressure
 * @param {Object} streamProcessor - The streaming processor instance
 * @returns {boolean} - Whether the request should be throttled
 */
export function shouldThrottleLLMRequest(streamProcessor) {
    const backpressureState = getBackpressureState(streamProcessor);
    if (!backpressureState) {
        return false;
    }
    
    // Throttle if backpressure is high or LLM is paused
    return backpressureState.backpressureLevel >= 8 || backpressureState.shouldPauseLLM;
}

/**
 * Wait for backpressure to be relieved before proceeding
 * @param {Object} streamProcessor - The streaming processor instance
 * @param {number} maxWaitTime - Maximum time to wait in milliseconds
 * @returns {Promise<boolean>} - Whether backpressure was relieved
 */
export async function waitForBackpressureRelief(streamProcessor, maxWaitTime = 10000) {
    if (!streamProcessor || !streamProcessor.backpressureController) {
        return true; // No backpressure controller, assume no backpressure
    }
    
    try {
        return await streamProcessor.backpressureController.waitForBackpressureRelief(maxWaitTime);
    } catch (error) {
        console.error('Error waiting for backpressure relief:', error);
        return false;
    }
}
