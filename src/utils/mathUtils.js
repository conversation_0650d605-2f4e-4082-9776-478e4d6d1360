/**
 * Math Utilities Module
 * Contains functions for mathematical operations and transformations
 */

/**
 * Helper that returns the parameter or, if it is a function, its return value.
 * @param {Any} value Parameter
 * @return {Any} Value
 */
export function valueFn(value) {
  return (typeof value === 'function' ? value() : value);
}

/**
 * Helper to deep copy and edit an object.
 * @param {Object} obj Object to copy and edit
 * @param {function} [editFn=null] Callback function for editing the new object
 * @return {Object} Deep copy of the object.
 */
export function deepCopy(obj, editFn = null) {
  const copy = JSON.parse(JSON.stringify(obj));
  if (editFn && typeof editFn === "function") editFn(copy);
  return copy;
}

/**
 * Create a sigmoid function factory with adjustable steepness
 * @param {number} steepness Steepness of the sigmoid curve
 * @returns {function} Sigmoid function
 */
export function sigmoidFactory(steepness = 5) {
  return (x) => {
    // Clamp x to [0, 1]
    x = Math.max(0, Math.min(1, x));
    
    // Apply sigmoid function
    return 1 / (1 + Math.exp(-steepness * (2 * x - 1)));
  };
}

/**
 * Linear interpolation between two values
 * @param {number} a Start value
 * @param {number} b End value
 * @param {number} t Interpolation factor (0-1)
 * @returns {number} Interpolated value
 */
export function lerp(a, b, t) {
  return a + (b - a) * t;
}

/**
 * Generate a random number between min and max
 * @param {number} min Minimum value
 * @param {number} max Maximum value
 * @returns {number} Random number
 */
export function random(min, max) {
  return min + Math.random() * (max - min);
}

/**
 * Clamp a value between min and max
 * @param {number} value Value to clamp
 * @param {number} min Minimum value
 * @param {number} max Maximum value
 * @returns {number} Clamped value
 */
export function clamp(value, min, max) {
  return Math.max(min, Math.min(max, value));
}

/**
 * Map a value from one range to another
 * @param {number} value Value to map
 * @param {number} inMin Input range minimum
 * @param {number} inMax Input range maximum
 * @param {number} outMin Output range minimum
 * @param {number} outMax Output range maximum
 * @returns {number} Mapped value
 */
export function mapRange(value, inMin, inMax, outMin, outMax) {
  return outMin + (outMax - outMin) * (value - inMin) / (inMax - inMin);
}

/**
 * Convert degrees to radians
 * @param {number} degrees Angle in degrees
 * @returns {number} Angle in radians
 */
export function degToRad(degrees) {
  return degrees * Math.PI / 180;
}

/**
 * Convert radians to degrees
 * @param {number} radians Angle in radians
 * @returns {number} Angle in degrees
 */
export function radToDeg(radians) {
  return radians * 180 / Math.PI;
}

/**
 * Calculate the distance between two points
 * @param {number} x1 First point x coordinate
 * @param {number} y1 First point y coordinate
 * @param {number} x2 Second point x coordinate
 * @param {number} y2 Second point y coordinate
 * @returns {number} Distance between points
 */
export function distance(x1, y1, x2, y2) {
  return Math.sqrt(Math.pow(x2 - x1, 2) + Math.pow(y2 - y1, 2));
}

/**
 * Calculate the angle between two points
 * @param {number} x1 First point x coordinate
 * @param {number} y1 First point y coordinate
 * @param {number} x2 Second point x coordinate
 * @param {number} y2 Second point y coordinate
 * @returns {number} Angle in radians
 */
export function angle(x1, y1, x2, y2) {
  return Math.atan2(y2 - y1, x2 - x1);
}
