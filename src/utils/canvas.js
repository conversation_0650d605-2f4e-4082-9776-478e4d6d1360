export function createOffscreenCanvas(width = 640, height = 480) {
    try {
        // Try OffscreenCanvas first
        if (typeof OffscreenCanvas !== 'undefined') {
            return new OffscreenCanvas(width, height);
        }
        // Fall back to regular canvas
        const canvas = document.createElement('canvas');
        canvas.width = width;
        canvas.height = height;
        return canvas;
    } catch (error) {
        console.error('[Canvas] Failed to create canvas:', error);
        return null;
    }
}
