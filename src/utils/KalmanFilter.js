import { KalmanFilter as ExternalKalmanFilter } from 'kalman-filter';

/**
 * Legacy single-dimension Kalman filter implementation for backward compatibility
 */
class SimpleLegacyKalmanFilter {
    constructor(initialState = 0, initialProcessError = 1, initialMeasurementError = 1, processNoise = 0.1, measurementNoise = 0.1) {
        this.state = initialState;
        this.processError = initialProcessError;
        this.measurementError = initialMeasurementError;
        this.processNoise = processNoise;
        this.measurementNoise = measurementNoise;
    }

    predict() {
        this.processError += this.processNoise;
        return this.state;
    }

    update(measurement) {
        const kalmanGain = this.processError / (this.processError + this.measurementNoise);
        this.state += kalmanGain * (measurement - this.state);
        this.processError = (1 - kalmanGain) * this.processError;
    }
}

/**
 * Advanced Kalman filter wrapper for single dimension
 */
class KalmanFilterWrapper {
    constructor(initialState = 0, processCovariance = 0.01, measurementCovariance = 0.1) {
        this.filter = new ExternalKalmanFilter({
            observation: {
                dimension: 3, // 3 dimensions for Euler angles
                covariance: [measurementCovariance, measurementCovariance, measurementCovariance]
            },
            dynamic: {
                dimension: 3,
                name: 'constant-position', // Assume constant position for rotations to avoid over-smoothing
                covariance: [processCovariance, processCovariance, processCovariance]
            }
        });

        this.lastFilteredValue = initialState;
        this.initialized = false;
    }

    predict() {
        return this.lastFilteredValue;
    }

    update(measurement) {
        if (!this.initialized) {
            this.lastFilteredValue = measurement;
            this.initialized = true;
            return;
        }

        this.lastFilteredValue = this.filter.filterAll([[measurement]])[0][0];
    }
}

/**
 * Multi-dimensional Kalman filter for 3D position data (x, y, z)
 * Uses the official API's multi-dimensional capabilities
 */
class KalmanFilter3D {
    constructor(initialX = 0, initialY = 0, initialZ = 0,
        processCovariance = 0.01, measurementCovariance = 0.1) {
        // Create filter with initial parameters
        this._createFilter(processCovariance, measurementCovariance);

        // Store initial values
        this.lastFilteredValues = [initialX, initialY, initialZ];
        this.initialized = false;
    }

    /**
     * Internal method to create the filter with specified parameters
     */
    _createFilter(processCovariance, measurementCovariance) {
        this.filter = new ExternalKalmanFilter({
            observation: {
                dimension: 3, // 3 dimensions for x, y, z
                covariance: [measurementCovariance, measurementCovariance, measurementCovariance]
            },
            dynamic: {
                name: 'constant-position',
                covariance: [processCovariance, processCovariance, processCovariance]
            }
        });

        // Store parameter values
        this._processCovariance = processCovariance;
        this._measurementCovariance = measurementCovariance;
    }

    /**
     * Update filter parameters without resetting state
     */
    updateParameters(processCovariance, measurementCovariance) {
        // Only recreate filter if parameters changed significantly
        if (Math.abs(this._processCovariance - processCovariance) > 0.0001 ||
            Math.abs(this._measurementCovariance - measurementCovariance) > 0.0001) {

            // Store current state
            const currentValues = [...this.lastFilteredValues];
            const wasInitialized = this.initialized;

            // Recreate filter with new parameters
            this._createFilter(processCovariance, measurementCovariance);

            // Restore state
            this.lastFilteredValues = currentValues;
            this.initialized = wasInitialized;

            console.log("KalmanFilter3D parameters updated:", {
                processCovariance,
                measurementCovariance
            });
        }
    }

    predict() {
        return [...this.lastFilteredValues];
    }
    update(x, y, z) {
        // Ensure x, y, z are not null/undefined
        if (x === undefined || y === undefined || z === undefined ||
            Number.isNaN(x) || Number.isNaN(y) || Number.isNaN(z)) {
            return this.lastFilteredValues;
        }


        if (!this.initialized) {
            this.lastFilteredValues = [x, y, z];
            this.initialized = true;
            return this.lastFilteredValues; // Add this line to return values during initialization
        }
        // console.log('KalmanFilter3D update:', x, y, z);
        this.lastFilteredValues = this.filter.filterAll([[x, y, z]])[0];
        return this.lastFilteredValues; // Add this line to ensure we always return values
    }

    filter(x, y, z) {
        this.update(x, y, z);
        return this.predict();
    }

    // Add method to reinitialize with new values
    reinitialize(x, y, z) {
        if (x === undefined || y === undefined || z === undefined ||
            Number.isNaN(x) || Number.isNaN(y) || Number.isNaN(z)) {
            return;
        }

        this.lastFilteredValues = [x, y, z];
        this.initialized = true;

        // Reset the filter's internal state if possible
        if (this.filter && this.filter.reset) {
            this.filter.reset([x, y, z]);
        }
    }
}

/**
 * Creates a multidimensional Kalman filter for quaternion/rotation data
 */
class KalmanFilterRotation {
    constructor(initialX = 0, initialY = 0, initialZ = 0,
        processCovariance = 0.005, measurementCovariance = 0.05, dimension = 3) {

        // Store dimension (2D or 3D)
        this.dimension = dimension;

        // Create filter with initial parameters
        this._createFilter(processCovariance, measurementCovariance);

        // Store initial values (for 2D or 3D)
        if (this.dimension === 2) {
            this.lastFilteredValues = [initialX, initialY];
        } else {
            this.lastFilteredValues = [initialX, initialY, initialZ];
        }
        this.initialized = false;
    }

    /**
     * Internal method to create the filter with specified parameters
     */
    _createFilter(processCovariance, measurementCovariance) {
        // Create covariance arrays based on dimension
        const covarianceArray = new Array(this.dimension).fill(measurementCovariance);
        const processCovarianceArray = new Array(this.dimension).fill(processCovariance);

        this.filter = new ExternalKalmanFilter({
            observation: {
                dimension: this.dimension,
                covariance: covarianceArray
            },
            dynamic: {
                dimension: this.dimension,
                name: 'constant-position',
                covariance: processCovarianceArray
            }
        });

        // Store parameter values
        this._processCovariance = processCovariance;
        this._measurementCovariance = measurementCovariance;
    }

    /**
     * Update filter parameters without resetting state
     */
    updateParameters(processCovariance, measurementCovariance) {
        // Only recreate filter if parameters changed significantly
        if (Math.abs(this._processCovariance - processCovariance) > 0.0001 ||
            Math.abs(this._measurementCovariance - measurementCovariance) > 0.0001) {

            // Store current state
            const currentValues = [...this.lastFilteredValues];
            const wasInitialized = this.initialized;

            // Recreate filter with new parameters
            this._createFilter(processCovariance, measurementCovariance);

            // Restore state
            this.lastFilteredValues = currentValues;
            this.initialized = wasInitialized;

            console.log("KalmanFilterRotation parameters updated:", {
                processCovariance,
                measurementCovariance
            });
        }
    }

    predict() {
        return [...this.lastFilteredValues];
    }

    update(x, y, z) {
        // Handle 2D case - ignore z parameter
        if (this.dimension === 2) {
            // Ensure x, y are not null/undefined
            if (x === undefined || y === undefined ||
                Number.isNaN(x) || Number.isNaN(y)) {
                return this.lastFilteredValues;
            }

            if (!this.initialized) {
                this.lastFilteredValues = [x, y];
                this.initialized = true;
                return this.lastFilteredValues;
            }

            // Filter the rotation angles together (2D case)
            this.lastFilteredValues = this.filter.filterAll([[x, y]])[0];
            return this.lastFilteredValues;
        }
        else {
            // 3D case - original behavior
            // Ensure x, y, z are not null/undefined
            if (x === undefined || y === undefined || z === undefined ||
                Number.isNaN(x) || Number.isNaN(y) || Number.isNaN(z)) {
                return this.lastFilteredValues;
            }

            if (!this.initialized) {
                this.lastFilteredValues = [x, y, z];
                this.initialized = true;
                return this.lastFilteredValues;
            }

            // Filter the rotation angles together (3D case)
            this.lastFilteredValues = this.filter.filterAll([[x, y, z]])[0];
            return this.lastFilteredValues;
        }
    }

    // Add method to reinitialize with new values - updated for 2D support
    reinitialize(x, y, z) {
        if (this.dimension === 2) {
            if (x === undefined || y === undefined ||
                Number.isNaN(x) || Number.isNaN(y)) {
                return;
            }

            this.lastFilteredValues = [x, y];
        } else {
            if (x === undefined || y === undefined || z === undefined ||
                Number.isNaN(x) || Number.isNaN(y) || Number.isNaN(z)) {
                return;
            }

            this.lastFilteredValues = [x, y, z];
        }

        this.initialized = true;

        // Reset the filter's internal state if possible
        if (this.filter && this.filter.reset) {
            if (this.dimension === 2) {
                this.filter.reset([x, y]);
            } else {
                this.filter.reset([x, y, z]);
            }
        }
    }
}

/**
 * Factory function to create the appropriate filter based on the data type
 */
function createKalmanFilter(type = 'position', initialValues = [0, 0, 0], options = {}) {
    const { processCovariance = 0.01, measurementCovariance = 0.1 } = options;

    switch (type) {
        case 'rotation':
            return new KalmanFilterRotation(
                initialValues[0], initialValues[1], initialValues[2],
                processCovariance, measurementCovariance
            );
        case 'position':
        default:
            return new KalmanFilter3D(
                initialValues[0], initialValues[1], initialValues[2],
                processCovariance, measurementCovariance
            );
    }
}

// Export all variants to maintain compatibility while providing enhanced options
export {
    SimpleLegacyKalmanFilter as default,
    KalmanFilterWrapper,
    KalmanFilter3D,
    KalmanFilterRotation,
    createKalmanFilter
};