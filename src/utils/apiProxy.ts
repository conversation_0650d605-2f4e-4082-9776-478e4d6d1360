/**
 * API Proxy Utility
 * Provides a unified interface for making API requests to different endpoints
 * Supports both direct and proxied requests
 */

import { config } from '../config/client';
import { getEnvVar } from '../config/env';
import { getDownloadServerUrl } from '@/utils/portManager.js';

// Define the supported API types
export type ApiType = 'vllm' | 'sglang' | 'letta' | 'sparkTTS' | 'asr' | 'textTo3D';

/**
 * Get the base URL for an API endpoint
 * @param apiType The type of API to get the URL for
 * @param useProxy Whether to use the proxy endpoint
 * @returns The base URL for the API
 */
export function getApiBaseUrl(apiType: ApiType, useProxy: boolean = true): string {
    // Map 'asr' to 'speechRecog' and 'textTo3D' to 'textTo3D' for backward compatibility
    const configKey = apiType === 'asr' ? 'speechRecog' : apiType;

    // Get the endpoint from config
    let baseUrl = config.endpoints?.[configKey];

    // If we don't have a valid endpoint, throw an error
    if (!baseUrl) {
        throw new Error(`${apiType.toUpperCase()}_API_ENDPOINT is not defined in config`);
    }

    // Log the endpoint for debugging
    console.log(`[apiProxy] Raw ${apiType} endpoint from config: ${baseUrl}`);

    // Verify the endpoint is valid
    if (!baseUrl.startsWith('http')) {
        throw new Error(`${apiType.toUpperCase()}_API_ENDPOINT has invalid format: ${baseUrl}. Must start with http:// or https://`);
    }

    // Ensure consistent URL format with trailing slash
    const cleanBaseUrl = baseUrl.endsWith('/') ? baseUrl : baseUrl + '/';

    // If we're using the proxy, convert the URL to a proxy URL
    if (useProxy && typeof window !== 'undefined') {
        // For browser environment, use the proxy
        // Extract the hostname and port from the URL
        const url = new URL(cleanBaseUrl);

        // Determine if we're already using a proxy URL
        const isAlreadyProxy = url.pathname.includes(`/${apiType}-proxy/`);

        if (!isAlreadyProxy) {
            // Get the current origin but replace the port with the download server port
            const origin = window.location.origin;
            const downloadServerPort = getEnvVar('VITE_DOWNLOAD_SERVER_PORT', '2994'); // Use the download server port
            const proxyBaseUrl = origin.replace(/:\d+/, `:${downloadServerPort}`);

            // Create the proxy URL
            const proxyName = apiType === 'textTo3D' ? 'textto3d' : apiType;
            const proxyUrl = `${proxyBaseUrl}/${proxyName}-proxy/`;
            console.log(`[apiProxy] Original origin: ${origin}, download server port: ${downloadServerPort}`);
            console.log(`[apiProxy] Proxy base URL: ${proxyBaseUrl}`);
            console.log(`[apiProxy] Using proxy URL: ${proxyUrl}`);
            return proxyUrl;
        }
    }

    // Return the clean base URL
    return cleanBaseUrl;
}

/**
 * Make an API request to a specific endpoint
 * @param apiType The type of API to make the request to
 * @param path The path to append to the base URL
 * @param options The fetch options
 * @param useProxy Whether to use the proxy endpoint
 * @returns The response from the API
 */
export async function fetchApi(
    apiType: ApiType,
    path: string,
    options: RequestInit = {},
    useProxy: boolean = true
): Promise<Response> {
    // Set default headers if not provided
    if (!options.headers) {
        options.headers = {
            'Content-Type': 'application/json',
        };
    }

    // Get the base URL
    const baseUrl = getApiBaseUrl(apiType, useProxy);

    // Ensure the path doesn't start with a slash
    const cleanPath = path.startsWith('/') ? path.substring(1) : path;

    // Create the full URL
    const url = `${baseUrl}${cleanPath}`;

    // Log detailed request information
    console.log(`[apiProxy] Making ${apiType} request to ${url}`, {
        method: options.method || 'GET',
        headers: options.headers,
        bodyLength: options.body ? (typeof options.body === 'string' ? options.body.length : '[non-string body]') : 'none'
    });

    // Log the actual request body for debugging streaming issues
    // if (options.body && typeof options.body === 'string') {
    //     try {
    //         // Try to parse and log as JSON
    //         const bodyObj = JSON.parse(options.body);
    //         console.log(`[apiProxy] Request body:`, {
    //             stream: bodyObj.stream,
    //             messageCount: bodyObj.messages?.length,
    //             firstMessageRole: bodyObj.messages?.[0]?.role,
    //             contentTypes: bodyObj.messages?.map((m: any) => {
    //                 if (typeof m.content === 'string') return 'string';
    //                 if (Array.isArray(m.content)) {
    //                     return m.content.map((c: any) => c.type).join(',');
    //                 }
    //                 return typeof m.content;
    //             })
    //         });
    //     } catch (e) {
    //         // If not JSON, log as string (truncated)
    //         console.log(`[apiProxy] Request body (not JSON):`, options.body.substring(0, 200) + '...');
    //     }
    // }

    try {
        // Make the request
        const response = await fetch(url, options);

        // Log response details
        console.log(`[apiProxy] Received response from ${url}`, {
            status: response.status,
            statusText: response.statusText,
            headers: Object.fromEntries(response.headers.entries()),
            url: response.url,
            isStream: response.headers.get('content-type')?.includes('text/event-stream')
        });

        // For non-streaming responses, try to log the response body
        if (!response.headers.get('content-type')?.includes('text/event-stream')) {
            try {
                // Clone the response to avoid consuming it
                const clonedResponse = response.clone();
                const text = await clonedResponse.text();

                try {
                    // Try to parse as JSON
                    const json = JSON.parse(text);
                    console.log(`[apiProxy] Response body:`, json);
                } catch (e) {
                    // If not JSON, log as text (truncated)
                    console.log(`[apiProxy] Response body (not JSON):`,
                        text.length > 500 ? text.substring(0, 500) + '...' : text);
                }
            } catch (e) {
                console.log(`[apiProxy] Could not log response body:`, e);
            }
        } else {
            console.log(`[apiProxy] Streaming response detected, body will be processed by stream handler`);
        }

        return response;
    } catch (error) {
        console.error(`[apiProxy] Error making request to ${url}:`, error);
        throw error;
    }
}

/**
 * Make a vLLM API request
 * @param path The path to append to the base URL
 * @param options The fetch options
 * @param useProxy Whether to use the proxy endpoint
 * @returns The response from the API
 */
export async function fetchVllmApi(
    path: string,
    options: RequestInit = {},
    useProxy: boolean = true
): Promise<Response> {
    return fetchApi('vllm', path, options, useProxy);
}

/**
 * Make a Letta API request
 * @param path The path to append to the base URL
 * @param options The fetch options
 * @param useProxy Whether to use the proxy endpoint
 * @returns The response from the API
 */
export async function fetchLettaApi(
    path: string,
    options: RequestInit = {},
    useProxy: boolean = true
): Promise<Response> {
    return fetchApi('letta', path, options, useProxy);
}

/**
 * Make a SparkTTS API request
 * @param path The path to append to the base URL
 * @param options The fetch options
 * @param useProxy Whether to use the proxy endpoint
 * @returns The response from the API
 */
export async function fetchSparkTTSApi(
    path: string,
    options: RequestInit = {},
    useProxy: boolean = true
): Promise<Response> {
    return fetchApi('sparkTTS', path, options, useProxy);
}

/**
 * Make an ASR API request
 * @param path The path to append to the base URL
 * @param options The fetch options
 * @param useProxy Whether to use the proxy endpoint
 * @returns The response from the API
 */
export async function fetchAsrApi(
    path: string,
    options: RequestInit = {},
    useProxy: boolean = true
): Promise<Response> {
    return fetchApi('asr', path, options, useProxy);
}

/**
 * Make a SGLang API request
 * @param path The path to append to the base URL
 * @param options The fetch options
 * @param useProxy Whether to use the proxy endpoint
 * @returns The response from the API
 */
export async function fetchSglangApi(
    path: string,
    options: RequestInit = {},
    useProxy: boolean = true
): Promise<Response> {
    return fetchApi('sglang', path, options, useProxy);
}

/**
 * Make a TextTo3D API request
 * @param path The path to append to the base URL
 * @param options The fetch options
 * @param useProxy Whether to use the proxy endpoint
 * @returns The response from the API
 */
export async function fetchTextTo3DApi(
    path: string,
    options: RequestInit = {},
    useProxy: boolean = true
): Promise<Response> {
    return fetchApi('textTo3D', path, options, useProxy);
}

/**
 * Call the unified LLM backend proxy endpoint (/api/llm) with the given payload.
 * @param payload The request body, must include at least { provider, model, messages }
 * @returns The parsed JSON response
 */
export async function fetchLLMApi(payload: any): Promise<any> {
    const apiUrl = `${getDownloadServerUrl()}/api/llm`;
    console.debug(`[fetchLLMApi] Using API URL: ${apiUrl}`);
    const response = await fetch(apiUrl, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload)
    });
    if (!response.ok) {
        const error = await response.json().catch(() => ({}));
        throw new Error(error.error || 'LLM API error');
    }
    return response.json();
}
