# Migration Guide: openaiStreamUtils.ts

The `openaiStreamUtils.ts` file has been deprecated and its functionality has been moved to more specialized modules in the `@src/media/` directory. This guide will help you migrate your code to use the new modules.

## Migration Table

| Old Import | New Import | Notes |
|------------|------------|-------|
| `import { processOpenAIStream } from '@src/utils/openaiStreamUtils'` | `import { processLLMStream } from '@src/media/api/vllmAPI'` | The new function has the same interface but better performance |
| `import { processAudioForAPI } from '@src/utils/openaiStreamUtils'` | `import { processAudioForAPI } from '@src/media/api/audioAPI'` | Direct replacement |
| `import { formatMediaForAPI } from '@src/utils/openaiStreamUtils'` | `import { formatMediaForAPI } from '@src/media/api/vllmAPI'` | Direct replacement |
| `import { sendAudioToAPI } from '@src/utils/openaiStreamUtils'` | `import { sendAudioToAPI } from '@src/media/api/audioAPI'` | Direct replacement |
| `import { blobToBase64 } from '@src/utils/openaiStreamUtils'` | `import { blobToBase64 } from '@src/media/utils/audioUtils'` | Direct replacement |
| `import { arrayBufferToBase64 } from '@src/utils/openaiStreamUtils'` | `import { arrayBufferToBase64 } from '@src/media/utils/audioUtils'` | Direct replacement |
| `import { processSSEStream } from '@src/utils/openaiStreamUtils'` | `import { processStream } from '@src/media/streaming/streamingFramework'` | The new function has a slightly different interface |

## Example Migrations

### Before:

```typescript
import { processOpenAIStream, sendAudioToAPI } from '@src/utils/openaiStreamUtils';

// Process a streaming response
const result = await processOpenAIStream(response, {
  onText: (text, fullText) => {
    console.log('Received text:', text);
  },
  onComplete: (data) => {
    console.log('Stream complete:', data);
  }
});

// Send audio to API
const audioResponse = await sendAudioToAPI(audioData, {
  systemPrompt: 'Transcribe and respond to this audio',
  stream: true
});
```

### After:

```typescript
import { processLLMStream } from '@src/media/api/vllmAPI';
import { sendAudioToAPI } from '@src/media/api/audioAPI';

// Process a streaming response
const result = await processLLMStream(response, {
  onText: (text, fullText) => {
    console.log('Received text:', text);
  },
  onComplete: (data) => {
    console.log('Stream complete:', data);
  }
});

// Send audio to API
const audioResponse = await sendAudioToAPI(audioData, {
  systemPrompt: 'Transcribe and respond to this audio',
  stream: true
});
```

## Benefits of the New Modules

1. **Better Organization**: Functionality is now organized by purpose and domain
2. **Improved Performance**: The new implementations are more efficient
3. **Better Type Safety**: More specific TypeScript types
4. **Environment Agnostic**: Works in both browser and server environments
5. **Reduced Code Duplication**: Shared implementations for common functionality

## Timeline

The `openaiStreamUtils.ts` file will be completely removed in a future release. Please migrate your code as soon as possible to avoid disruption.

If you have any questions or need help with migration, please contact the development team.
