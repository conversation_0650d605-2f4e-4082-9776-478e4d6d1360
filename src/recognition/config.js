export const CONFIG = {
    CAMERA: {
        // Default camera settings
        WIDTH: 1280,
        HEIGHT: 720,
        FPS: 30,
        
        // Video file fallback path (can be overridden by environment variable)
        VIDEO_FILE: import.meta.env.VITE_VIDEO_FILE_PATH || null,
        
        // Camera constraints
        CONSTRAINTS: {
            audio: false,
            video: {
                width: { ideal: 1280 },
                height: { ideal: 720 },
                frameRate: { ideal: 30 },
                facingMode: 'user'
            }
        },

        // Error messages
        ERRORS: {
            NO_WEBCAM: 'No webcam found',
            NO_VIDEO_FILE: 'No video file path configured',
            INIT_FAILED: 'Failed to initialize camera',
            STREAM_FAILED: 'Failed to get camera stream',
            FILE_LOAD_FAILED: 'Failed to load video file'
        },

        // Timeouts (in milliseconds)
        TIMEOUTS: {
            INIT: 10000,  // 10 seconds timeout for initialization
            STREAM: 5000  // 5 seconds timeout for stream start
        }
    },

    HAND_TRACKING: {
        maxNumHands: 2,
        modelComplexity: 1,
        minDetectionConfidence: 0.5,
        minTrackingConfidence: 0.5,
        selfieMode: true
    }
};
