import { CONFIG } from './config.js';

export class Camera {
    constructor() {
        this.videoElement = document.createElement('video');
        this.videoElement.playsInline = true;
        this.stream = null;
        this.isVideoFile = false;
        this.initWebcam();
    }

    async checkDevices() {
        try {
            const devices = await navigator.mediaDevices.enumerateDevices();
            const videoDevices = devices.filter(device => device.kind === 'videoinput');
            return videoDevices.length > 0;
        } catch (error) {
            console.warn('Error checking video devices:', error);
            return false;
        }
    }

    async initVideoFile() {
        if (!CONFIG.CAMERA.VIDEO_FILE) {
            return null;
        }

        this.isVideoFile = true;
        this.videoElement.src = CONFIG.CAMERA.VIDEO_FILE;
        this.videoElement.loop = true;

        return new Promise((resolve) => {
            this.videoElement.onloadedmetadata = () => {
                this.videoElement.play()
                    .then(() => resolve(this.videoElement))
                    .catch(() => resolve(null));
            };
            this.videoElement.onerror = () => resolve(null);
        });
    }

    async initWebcam() {
        try {
            this.stream = await navigator.mediaDevices.getUserMedia({
                video: {
                    width: { ideal: CONFIG.CAMERA.WIDTH },
                    height: { ideal: CONFIG.CAMERA.HEIGHT },
                    frameRate: { ideal: CONFIG.CAMERA.FPS },
                    facingMode: 'user'
                }
            });

            this.videoElement.srcObject = this.stream;
            return new Promise((resolve) => {
                this.videoElement.onloadedmetadata = () => {
                    this.videoElement.play()
                        .then(() => resolve(this.videoElement))
                        .catch(() => resolve(null));
                };
                this.videoElement.onerror = () => resolve(null);
            });
        } catch (error) {
            console.warn('Error initializing webcam:', error);
            return null;
        }
    }

    async init() {
        // First try webcam
        const hasWebcam = await this.checkDevices();
        if (hasWebcam) {
            const webcamResult = await this.initWebcam();
            if (webcamResult) return webcamResult;
        }

        // If no webcam or webcam failed, try video file
        console.warn('No webcam found or failed to initialize, trying video file');
        const videoFileResult = await this.initVideoFile();
        if (videoFileResult) return videoFileResult;

        // If both failed, return null instead of throwing
        console.warn('Neither webcam nor video file available');
        return null;
    }

    stop() {
        if (this.stream) {
            this.stream.getTracks().forEach(track => track.stop());
            this.stream = null;
        }
        if (this.isVideoFile) {
            this.videoElement.pause();
            this.videoElement.src = '';
        }
        this.videoElement.srcObject = null;
    }

    get isActive() {
        if (this.isVideoFile) {
            return !this.videoElement.paused;
        }
        return this.stream !== null && this.stream.active;
    }
} 