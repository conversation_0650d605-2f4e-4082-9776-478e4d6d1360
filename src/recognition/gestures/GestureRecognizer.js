import { GestureUtils } from './GestureUtils.js';

export class GestureRecognizer {
    /**
     * Detect rotation gesture and calculate rotation amount
     * @param {Array} currentLandmarks - Current frame hand landmarks
     * @param {Array} previousLandmarks - Previous frame hand landmarks
     * @returns {Object|null} - Rotation data or null if no rotation detected
     */
    // static detectRotation(currentLandmarks, previousLandmarks) {
    //     // First check if the hand is in rotation gesture position
    //     const fingerStates = this.calculateFingerStates(currentLandmarks);
        
    //     if (!this.isRotationGesture(fingerStates) || !previousLandmarks) {
    //         return null;
    //     }
        
    //     // Calculate wrist position (landmark 0)
    //     const wrist = currentLandmarks[0];
    //     const prevWrist = previousLandmarks[0];
        
    //     // Get index (8) and middle (12) fingertips
    //     const indexTip = currentLandmarks[8];
    //     const prevIndexTip = previousLandmarks[8];
        
    //     const middleTip = currentLandmarks[12];
    //     const prevMiddleTip = previousLandmarks[12];
        
    //     // Calculate the center point between index and middle fingertips
    //     const centerPoint = {
    //         x: (indexTip.x + middleTip.x) / 2,
    //         y: (indexTip.y + middleTip.y) / 2
    //     };
        
    //     const prevCenterPoint = {
    //         x: (prevIndexTip.x + prevMiddleTip.x) / 2,
    //         y: (prevIndexTip.y + prevMiddleTip.y) / 2
    //     };
        
    //     // Calculate movement relative to the wrist (to account for hand translation)
    //     const wristRelativeX = centerPoint.x - wrist.x;
    //     const wristRelativeY = centerPoint.y - wrist.y;
        
    //     const prevWristRelativeX = prevCenterPoint.x - prevWrist.x;
    //     const prevWristRelativeY = prevCenterPoint.y - prevWrist.y;
        
    //     // Calculate deltas of the relative movements
    //     let horizontalDelta = wristRelativeX - prevWristRelativeX;
    //     let verticalDelta = wristRelativeY - prevWristRelativeY;
        
    //     // Apply a small deadzone to filter out tiny movements
    //     const deadzone = 0.003;
    //     if (Math.abs(horizontalDelta) < deadzone) horizontalDelta = 0;
    //     if (Math.abs(verticalDelta) < deadzone) verticalDelta = 0;
        
    //     // Apply sensitivity to make rotation more controllable
    //     const rotationSensitivity = 3.5;
        
    //     return {
    //         type: 'rotation',
    //         rotation: {
    //             // Moving hand up/down (Y axis) controls X rotation (pitch)
    //             x: -verticalDelta * rotationSensitivity,
                
    //             // Moving hand left/right (X axis) controls Y rotation (yaw)
    //             y: horizontalDelta * rotationSensitivity
    //         }
    //     };
    // }
    
    /**
     * Calculate finger positions and states for gesture detection
     */
    static calculateFingerStates(landmarks) {
        const fingertips = [4, 8, 12, 16, 20];
        const mcp = [2, 5, 9, 13, 17];

        const states = {
            curled: [false, false, false, false, false],
            extended: [false, false, false, false, false],
            positions: fingertips.map(idx => landmarks[idx]),
            bases: mcp.map(idx => landmarks[idx])
        };

        for (let i = 0; i < 5; i++) {
            const tipToWristDist = GestureUtils.calculateDistance(landmarks[fingertips[i]], landmarks[0]);
            const mcpToWristDist = GestureUtils.calculateDistance(landmarks[mcp[i]], landmarks[0]);

            states.curled[i] = tipToWristDist < mcpToWristDist * 1.1;
            states.extended[i] = tipToWristDist > mcpToWristDist * 1.5;
        }

        return states;
    }

    static isPinchGesture(fingerStates) {
        const thumbAndIndexActive = !fingerStates.curled[0] && !fingerStates.curled[1];
        const otherFingersInactive = !fingerStates.extended[2] && !fingerStates.extended[3] && !fingerStates.extended[4];
        const pinchDistance = GestureUtils.calculateDistance(fingerStates.positions[0], fingerStates.positions[1]);

        return thumbAndIndexActive && otherFingersInactive && pinchDistance < 0.3;
    }

    static isRotationGesture(fingerStates) {
        // console.log(fingerStates.extended);
        // Check if ONLY index and middle fingers are extended, others are closed
        return !fingerStates.extended[0] && // thumb closed
               fingerStates.extended[1] &&  // index extended
               fingerStates.extended[2] &&  // middle extended
               !fingerStates.extended[3] && // ring closed
               !fingerStates.extended[4];   // pinky closed
    }
    
    /**
     * Determine if a hand is present with sufficient confidence
     * @param {Array} landmarks - Hand landmarks
     * @param {number} confidenceThreshold - Minimum confidence to consider hand present (0-1)
     * @param {number} minVisibleLandmarks - Minimum number of visible landmarks required
     * @returns {boolean} - True if hand is present with sufficient confidence
     */
    static isHandPresent(landmarks, confidenceThreshold = 0.7, minVisibleLandmarks = 15) {
        if (!landmarks || landmarks.length < 21) return false;
        
        let visibleLandmarks = 0;
        let totalConfidence = 0;
        
        // Count visible landmarks and sum confidence
        landmarks.forEach(landmark => {
            if (landmark.visibility !== undefined) {
                // MediaPipe-style landmarks with visibility property
                if (landmark.visibility > 0.5) {
                    visibleLandmarks++;
                    totalConfidence += landmark.visibility;
                }
            } else if (landmark.x !== undefined && landmark.y !== undefined) {
                // For models without visibility, check if positions are valid
                const validPosition =
                    landmark.x > 0.05 && landmark.x < 0.95 &&
                    landmark.y > 0.05 && landmark.y < 0.95;
                
                if (validPosition) {
                    visibleLandmarks++;
                    totalConfidence += 0.8; // Assume decent confidence for valid positions
                }
            }
        });
        
        // Calculate average confidence
        const avgConfidence = visibleLandmarks > 0 ? totalConfidence / visibleLandmarks : 0;
        
        // Return true if hand meets both minimum landmarks and confidence threshold
        return visibleLandmarks >= minVisibleLandmarks && avgConfidence >= confidenceThreshold;
    }
}
