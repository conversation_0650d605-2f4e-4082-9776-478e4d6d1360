import { GestureUtils } from './GestureUtils.js';
import * as THREE from 'three';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';

export class GestureOperations {
    static handleRotation(controls, rotation, speed, lookingGlassConfig = null) {
        if (!controls || !(controls instanceof OrbitControls)) {
            console.warn('Invalid controls object provided.');
            return false;
        }

        // console.log('Applying rotation via target manipulation:', rotation, 'with speed:', speed);

        const rotationAmount = new THREE.Vector3(rotation.x * speed, rotation.y * speed, 0); // Adjust speed factor as needed

        // Create a rotation matrix based on the desired rotation
        const rotationMatrix = new THREE.Matrix4().makeRotationFromEuler(
            new THREE.Euler(rotationAmount.x, rotationAmount.y, 0, 'XYZ')
        );

        // Get the current target and camera position
        const currentTarget = controls.target.clone();
        const currentPosition = controls.object.position.clone(); // Assuming controls.object is the camera

        // Calculate the vector from the target to the camera
        const offset = new THREE.Vector3().subVectors(currentPosition, currentTarget);

        // Apply the rotation to the offset vector
        offset.applyMatrix4(rotationMatrix);

        // Calculate the new camera position
        const newCameraPosition = new THREE.Vector3().addVectors(currentTarget, offset);

        // Set the new camera position
        controls.object.position.copy(newCameraPosition);

        // Ensure the camera is still looking at the original target
        controls.target.copy(currentTarget);

        // Update the controls
        controls.update();

        // Also apply rotation to Looking Glass if config is provided
        if (lookingGlassConfig) {
            // LookingGlass uses trackballX/Y for rotation
            lookingGlassConfig.trackballX += rotation.x * speed;
            lookingGlassConfig.trackballY += rotation.y * speed;
            // success = true;
        }
        return true;
    }

    static handleScaling(controls, scale, speed, lookingGlassConfig = null) {
        let success = false;
        // Fallback for other control types that might use zoom property
        // Use the findCamera helper to locate the camera object regardless of structure
        const camera = this.findCamera(controls);
        
        if (camera) {
            // Try to adjust camera zoom directly with the found camera
            success = GestureUtils.applyZoomToCamera(camera, scale, speed);
        } else {
            console.warn('[GestureOperations] Could not find camera object from controls');
        }
        // Update controls if method exists
        if (typeof controls.update === 'function') {
            controls.update();
        }
        // Apply scaling to Looking Glass if config is provided
        if (lookingGlassConfig) {
            // For Looking Glass, scaling affects targetDiam
            if (scale > 1) {
                // Zoom out - increase target diameter
                lookingGlassConfig.targetDiam *= (1 + (scale - 1) * speed);
            } else if (scale < 1) {
                // Zoom in - decrease target diameter
                lookingGlassConfig.targetDiam *= (1 - (1 - scale) * speed);
            }
            success = true;
        }
        return success;
    }

    /**
     * Try to find camera object from various possible locations
     * @param {Object} controls - Controls object
     * @returns {Object|null} Camera object if found
     */
    static findCamera(controls) {
        if (!controls) return null;

        // Try various paths to find camera
        if (controls.camera) return controls.camera;
        if (controls.object) return controls.object;
        if (controls.viewer && controls.viewer.camera) return controls.viewer.camera;

        return null;
    }
}
