export class GestureUtils {
    // Default zoom constraints
    static MIN_ZOOM = 0.1;
    static MAX_ZOOM = 10.0;

    /**
     * Calculate distance between two landmark points
     */
    static calculateDistance(p1, p2) {
        return Math.sqrt(
            Math.pow(p1.x - p2.x, 2) +
            Math.pow(p1.y - p2.y, 2) +
            Math.pow(p1.z - p2.z, 2)
        );
    }

    /**
     * Calculate rotation from hand landmarks using index and middle finger movement
     * @param {Array} landmarks - Current frame hand landmarks
     * @param {Array} previousLandmarks - Optional previous frame landmarks
     * @returns {Object} - Rotation values {x, y}
     */
    static calculateRotation(landmarks, previousLandmarks) {
        // If no previous landmarks are provided, return zero rotation
        if (!previousLandmarks) {
            return { x: 0, y: 0 };
        }

        // Calculate wrist position (landmark 0)
        const wrist = landmarks[0];
        const prevWrist = previousLandmarks[0];

        // Get index (8) and middle (12) fingertips
        const indexTip = landmarks[8];
        const prevIndexTip = previousLandmarks[8];

        const middleTip = landmarks[12];
        const prevMiddleTip = previousLandmarks[12];

        // Calculate the center point between index and middle fingertips
        const centerPoint = {
            x: (indexTip.x + middleTip.x) / 2,
            y: (indexTip.y + middleTip.y) / 2
        };

        const prevCenterPoint = {
            x: (prevIndexTip.x + prevMiddleTip.x) / 2,
            y: (prevIndexTip.y + prevMiddleTip.y) / 2
        };

        // Calculate movement relative to the wrist (to account for hand translation)
        const wristRelativeX = centerPoint.x - wrist.x;
        const wristRelativeY = centerPoint.y - wrist.y;

        const prevWristRelativeX = prevCenterPoint.x - prevWrist.x;
        const prevWristRelativeY = prevCenterPoint.y - prevWrist.y;

        // Calculate deltas of the relative movements
        let horizontalDelta = wristRelativeX - prevWristRelativeX;
        let verticalDelta = wristRelativeY - prevWristRelativeY;

        // Apply a small deadzone to filter out tiny movements
        const deadzone = 0.003;
        if (Math.abs(horizontalDelta) < deadzone) horizontalDelta = 0;
        if (Math.abs(verticalDelta) < deadzone) verticalDelta = 0;

        // Apply sensitivity to make rotation more controllable
        const rotationSensitivity = 3.5;

        return {
            // MODIFIED: Reversed both rotation directions for more intuitive control
            x: -verticalDelta * rotationSensitivity,   // Up/down movement controls pitch (x rotation)
            y: horizontalDelta * rotationSensitivity  // Left/right movement controls yaw (y rotation)
        };
    }
    /**
     * Calculate pinch scale using relative movement and adaptive reference
     * @param {number} currentDistance - Current pinch distance
     * @param {number} referenceDistance - Reference pinch distance
     * @param {number} sensitivity - Scaling sensitivity (1-10)
     * @param {number} adaptationRate - How quickly reference adapts (0-1)
     * @returns {Object} - Scale factor and updated reference
     */
    static calculateRelativePinchScale(currentDistance, referenceDistance, sensitivity = 5, adaptationRate = 0.05) {
        // Handle first detection or invalid reference
        if (!referenceDistance || referenceDistance <= 0) {
            return {
                scale: 1.0,  // No scaling on first detection
                updatedReference: currentDistance
            };
        }

        // Calculate relative scale factor
        // Values > 1 mean fingers are moving apart (zoom out)
        // Values < 1 mean fingers are moving together (zoom in)
        let rawScale = referenceDistance / currentDistance;

        // Apply non-linear curve for better control at extreme ranges
        // This dampens very large/small values while preserving direction
        const normalized = Math.pow(rawScale, 0.7);

        // Apply sensitivity adjustment (higher values = more aggressive scaling)
        const scaleFactor = 1.0 + ((normalized - 1.0) * sensitivity / 5);

        // Adapt reference distance slowly to prevent drift while maintaining responsiveness
        // Lower adaptation rate = more relative to initial position
        // Higher adaptation rate = more responsive to current position
        const updatedReference = referenceDistance * (1 - adaptationRate) +
            currentDistance * adaptationRate;

        // console.log(`[GestureUtils] Pinch: current=${currentDistance.toFixed(4)}, ` +
        //             `ref=${referenceDistance.toFixed(4)}, scale=${scaleFactor.toFixed(4)}`);

        return {
            scale: scaleFactor,
            updatedReference: updatedReference
        };
    }
    /**
     * Calculate pinch scale with absolute distance thresholds
     * @param {Array} landmarks - Hand landmarks
     * @param {number} sensitivity - Sensitivity adjustment (default: 10)
     * @param {number|null} preCalculatedDistance - Optional pre-calculated distance between thumb and index
     * @returns {number} - Scale factor
     */
    static calculateAbsolutePinchScale(landmarks, sensitivity = 10, preCalculatedDistance = null) {
        // Calculate current distance between fingers if not provided
        let currentDistance;

        if (preCalculatedDistance !== null) {
            currentDistance = preCalculatedDistance;
        } else {
            // Get thumb and index finger tips
            const thumbTip = landmarks[4];
            const indexTip = landmarks[8];
            currentDistance = this.calculateDistance(thumbTip, indexTip);
        }

        // Use continuous mapping with expanded thresholds  
        // Map distance from 0.03 to 0.35 to scale factors from 1.15 to 0.85
        let scale = 1.0; if (currentDistance < 0.05) {
            scale = 1.1; // Zoom out
        } else if (currentDistance > 0.15) {
            scale = 0.9; // Zoom in
        }

        // Apply sensitivity adjustment
        scale = 1.0 + ((scale - 1.0) * sensitivity / 10);

        console.log(`[GestureUtils] Pinch distance: ${currentDistance.toFixed(4)}, Scale: ${scale.toFixed(4)}`);

        return scale;
    }
    // /**
    //  * Calculate pinch scale factor with better relative distance handling
    //  * @param {Array} landmarks - Hand landmarks
    //  * @param {number} lastPinchDistance - Previous pinch distance
    //  * @returns {Object} Scale value and updated reference distance
    //  */
    // static calculatePinchScale(landmarks, lastPinchDistance) {
    //     // Get current distance between thumb and index finger
    //     const currentDist = this.calculateDistance(landmarks[4], landmarks[8]);

    //     // Use dynamic reference distance for better relative scaling
    //     if (!lastPinchDistance || lastPinchDistance === 0) {
    //         // Initialize reference distance if not set
    //         return {
    //             scale: 1.0,  // No change on first detection
    //             updatedDistance: currentDist
    //         };
    //     }

    //     // Calculate relative scale compared to last distance
    //     // Invert the relationship: increasing distance means zoom in (scale < 1)
    //     const relativeScale = lastPinchDistance / currentDist;

    //     // Update reference distance (with smoothing to avoid jumps)
    //     const updatedDistance = lastPinchDistance * 0.9 + currentDist * 0.1;

    //     return {
    //         scale: relativeScale,
    //         updatedDistance: updatedDistance
    //     };
    // }

    /**
     * Apply zoom to camera
     * @param {Object} camera - Three.js camera object 
     * @param {number} scale - Scale factor
     * @param {number} speed - Zoom speed multiplier
     * @param {Object} options - Optional parameters like min/max zoom
     * @returns {boolean} Success status
     */
    static applyZoomToCamera(camera, scale, speed, options = {}) {
        if (!camera || camera.zoom === undefined) return false;

        // Get zoom constraints from options or use defaults
        const minZoom = options?.minZoom || this.MIN_ZOOM;
        const maxZoom = options?.maxZoom || this.MAX_ZOOM;

        try {
            let newZoom = camera.zoom;
            let zoomFactor = 1.0;

            if (scale > 1) {
                // Zoom out - decrease zoom value
                zoomFactor = (scale * speed * 0.25 + 0.75); // More aggressive scaling (was 0.1/0.9)
                newZoom /= zoomFactor;
            } else if (scale < 1) {
                // Zoom in - increase zoom value
                zoomFactor = ((1 / scale) * speed * 0.25 + 0.75); // More aggressive scaling
                newZoom *= zoomFactor;
            }

            // Apply constraints
            const oldZoom = camera.zoom;
            newZoom = Math.min(Math.max(newZoom, minZoom), maxZoom);

            if (newZoom !== camera.zoom) {
                camera.zoom = newZoom;
                camera.updateProjectionMatrix();
                // console.log(`[GestureOperations] Zoom changed: ${oldZoom.toFixed(4)} → ${newZoom.toFixed(4)}, Factor: ${zoomFactor.toFixed(4)}`);
            }

            return true;
        } catch (error) {
            console.warn('[GestureOperations] Camera zoom failed:', error);
            return false;
        }
    }

    /**
     * Set global zoom constraints
     * @param {number} minZoom - Minimum zoom level
     * @param {number} maxZoom - Maximum zoom level
     */
    static setZoomConstraints(minZoom, maxZoom) {
        if (typeof minZoom === 'number' && minZoom > 0) {
            this.MIN_ZOOM = minZoom;
        }

        if (typeof maxZoom === 'number' && maxZoom > minZoom) {
            this.MAX_ZOOM = maxZoom;
        }

        console.log(`[GestureUtils] Zoom constraints set: min=${this.MIN_ZOOM}, max=${this.MAX_ZOOM}`);
    }

    /**
     * Handles gesture locking and unlocking based on hand presence
     * @param {boolean} isHandPresent - Whether hand is currently detected
     * @param {Object} lockState - Object containing lock state information
     * @param {number} timeoutMs - How long to wait before locking when hands disappear
     * @returns {Object} Updated lock state
     */
    static handleGestureLock(isHandPresent, lockState = {}, timeoutMs = 500) {
        const now = performance.now();
        const currentState = {
            locked: lockState.locked || false,
            lastHandPresenceTime: lockState.lastHandPresenceTime || 0,
            lastValue: lockState.lastValue || null
        };

        // When hand is present, always update the timestamp and unlock
        if (isHandPresent) {
            currentState.lastHandPresenceTime = now;
            currentState.locked = false;
        }
        // When hand is not present, check if we should lock based on timeout
        else {
            const timeSinceHandPresent = now - currentState.lastHandPresenceTime;

            // Lock after timeout period with no hand present
            if (timeSinceHandPresent > timeoutMs) {
                currentState.locked = true;
            }
        }

        return currentState;
    }

    /**
     * Updates the last value when gesture is active (not locked)
     * @param {Object} lockState - The current lock state object
     * @param {any} newValue - The new value to potentially store
     * @returns {Object} Updated lock state with the new value if not locked
     */
    static updateGestureValue(lockState, newValue) {
        if (!lockState.locked) {
            lockState.lastValue = newValue;
        }
        return lockState;
    }
}
