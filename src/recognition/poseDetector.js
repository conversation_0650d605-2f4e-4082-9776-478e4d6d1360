import {
    <PERSON>et<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Hand<PERSON>andmarker, HolisticLandmarker,
} from '@mediapipe/tasks-vision';
import { Vector3 } from '@babylonjs/core';
import { config } from '../config/client.js';
import { MODEL_CONFIGS } from '../config/models.js';
import ModelDownloader from '../utils/modelDownloader.js';

// Add utility functions for path handling in browser
function dirname(path) {
    return path.split('/').slice(0, -1).join('/');
}

function basename(path) {
    return path.split('/').pop();
}

// Add detection mode enum for clearer configuration
const DetectionMode = {
    POSE_ONLY: 'pose_only',
    HANDS_ONLY: 'hands_only',
    POSE_AND_HANDS: 'pose_and_hands',
    HOLISTIC: 'holistic'
};

class PoseDetector {
    constructor(options = {}) {
        this.detector = null;  // 替换 poseLandmarker
        this.debugCanvas = null;
        this.fpsText = null;
        this.lastFrameTime = Date.now();
        this.isLandmarkMode = false;
        this._isInitialized = false;  // 重命名为私有属性
        this.debug = false;

        // Separate detectors for pose and hands
        this.poseDetector = null;
        this.handDetector = null;

        // Set detection mode from options or default to pose-only
        if (options.detectorType === 'holistic') {
            this.detectionMode = DetectionMode.HOLISTIC;
        } else {
            // Parse detection mode from options
            if (options.detectionMode) {
                this.detectionMode = options.detectionMode;
            } else if (options.useHands === true && options.usePose === false) {
                this.detectionMode = DetectionMode.HANDS_ONLY;
            } else if (options.useHands === true && (options.usePose === true || options.usePose === undefined)) {
                this.detectionMode = DetectionMode.POSE_AND_HANDS;
            } else {
                // Default to pose-only detection if not specified
                this.detectionMode = DetectionMode.POSE_ONLY;
            }
        }

        // For backward compatibility
        this.useHands = this.detectionMode === DetectionMode.HANDS_ONLY ||
            this.detectionMode === DetectionMode.POSE_AND_HANDS;
        this.usePose = this.detectionMode === DetectionMode.POSE_ONLY ||
            this.detectionMode === DetectionMode.POSE_AND_HANDS;

        console.log(`[PoseDetector] Initialized with detection mode: ${this.detectionMode}`);

        // Define unified model configurations from shared config
        this.modelConfigs = {
            HOLISTIC: {
                modelPath: MODEL_CONFIGS.mediapipe?.holistic?.modelPath || 'mediapipe/holistic_landmarker.task',
                fallbackPath: MODEL_CONFIGS.mediapipe?.holistic?.fallbackPath || 'https://storage.googleapis.com/mediapipe-models/holistic_landmarker/holistic_landmarker/float16/latest/holistic_landmarker.task',
                options: {
                    runningMode: "VIDEO",
                    minFaceDetectionConfidence: 0.5,
                    minFacePresenceConfidence: 0.5,
                    minFaceSuppressionThreshold: 0.3,
                    outputFaceBlendshapes: false,
                    minPoseDetectionConfidence: 0.5,
                    minPoseSuppressionThreshold: 0.3,
                    minPosePresenceConfidence: 0.5,
                    outputPoseSegmentationMasks: false,
                    minHandLandmarksConfidence: 0.5,
                }
            },
            POSE: {
                modelPath: MODEL_CONFIGS.mediapipe?.pose?.modelPath || 'mediapipe/pose_landmarker_lite.task',
                fallbackPath: MODEL_CONFIGS.mediapipe?.pose?.fallbackPath || 'https://storage.googleapis.com/mediapipe-models/pose_landmarker/pose_landmarker_lite/float16/1/pose_landmarker_lite.task',
                options: {
                    runningMode: "VIDEO",
                    numPoses: 1,
                    minPoseDetectionConfidence: 0.5,
                    minPosePresenceConfidence: 0.5,
                    minTrackingConfidence: 0.5,
                    outputSegmentationMasks: false,
                }
            },
            HAND: {
                modelPath: MODEL_CONFIGS.mediapipe?.hand?.modelPath || 'mediapipe/hand_landmarker.task',
                fallbackPath: MODEL_CONFIGS.mediapipe?.hand?.fallbackPath || 'https://storage.googleapis.com/mediapipe-models/hand_landmarker/hand_landmarker/float16/1/hand_landmarker.task',
                options: {
                    runningMode: "VIDEO",
                    numHands: 2,
                    minHandDetectionConfidence: 0.5,
                    minHandPresenceConfidence: 0.8,
                    minTrackingConfidence: 0.5
                },
            }
        };

        // Initialize ModelDownloader
        this.modelDownloader = new ModelDownloader({ uiManager: options.uiManager });

        // Add hand pose tracking
        this.handPoses = {
            left: null,
            right: null
        };

        this.landmarks = null;
        this.worldLandmarks = null;
        this.boneLengths = new Map();

        // Add resolution setting

        this.mediapipeLoaded = false;
        this.initPromise = null;

        // Keep only basic WebAssembly check
        this.hasWasmSupport = this.checkWasmSupport();

        // Legacy parameter kept for compatibility
        this.detectorType = options.detectorType ||
            (this.detectionMode === DetectionMode.HOLISTIC ? 'holistic' : 'separate');

        this.holisticDetector = null;
        this._initializationPromise = null;
        this.uiManager = options.uiManager;  // Store uiManager reference

        // Initialize gesture detector only if needed
        this.useGestureDetection = options.useGestureDetection !== false &&
            (this.detectionMode === DetectionMode.HANDS_ONLY ||
                this.detectionMode === DetectionMode.POSE_AND_HANDS ||
                this.detectionMode === DetectionMode.HOLISTIC);


        // Set up visualizer reference for debug mode
        this.visualizer = null;

        // Add gesture callbacks collection
        this.gestureCallbacks = new Set();

        // Add debug visualization settings
        this.debugVisualization = {
            enabled: false,
            canvas: null,
            context: null,
            drawHandLandmarks: true,
            drawConnections: true,
            colors: {
                landmarks: 'rgba(0, 255, 0, 0.8)',
                connections: 'rgba(255, 255, 255, 0.5)',
                leftHand: 'rgba(0, 255, 0, 0.8)',
                rightHand: 'rgba(255, 0, 0, 0.8)'
            }
        };
    }

    async initializeModelCache() {
        try {
            const request = this.modelStorage.open('ModelCache', 1);

            request.onerror = () => {
                console.error('[PoseDetector] Failed to open model cache database');
            };

            request.onupgradeneeded = (event) => {
                const db = event.target.result;
                if (!db.objectStoreNames.contains('models')) {
                    db.createObjectStore('models', { keyPath: 'name' });
                }
            };

            request.onsuccess = () => {
                console.log('[PoseDetector] Model cache initialized');
            };
        } catch (error) {
            console.error('[PoseDetector] Model cache initialization failed:', error);
        }
    }

    async saveModelToPublic(modelName, modelBuffer) {
        try {
            if (this.app?.uiManager) {
                this.app.uiManager.showLoadingPanel();
                this.app.uiManager.updateLoadingProgress('Preparing upload...');
            }

            // Clean up the model directory path
            const modelDir = dirname(modelName).replace(/^\/+|\/+$/g, '');
            console.log('[PoseDetector] Preparing upload:', {
                modelName,
                modelDir,
                bufferSize: modelBuffer.byteLength
            });

            // Create directory first
            try {
                const baseUrl = config.modelConfig.baseUrl;
                const createDirResponse = await fetch(`${baseUrl}/${modelDir}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ createDir: true })
                });

                if (!createDirResponse.ok) {
                    throw new Error(`Failed to create directory: ${await createDirResponse.text()}`);
                }

                this.app?.uiManager?.updateLoadingProgress('Directory created...');
            } catch (error) {
                console.error('[PoseDetector] Directory creation failed:', error);
                this.app?.uiManager?.hideLoadingPanel();
                return null;
            }

            // Prepare and send upload
            const formData = new FormData();
            const blob = new Blob([modelBuffer], { type: 'application/octet-stream' });
            const filename = basename(modelName);

            // Important: append path first, before the file
            formData.append('path', modelDir);
            formData.append('model', blob, filename);

            const baseUrl = config.modelConfig.baseUrl;
            const uploadUrl = `${baseUrl}/upload`;
            this.app?.uiManager?.updateLoadingProgress('Uploading model...');

            console.log('[PoseDetector] Starting upload:', {
                url: uploadUrl,
                filename,
                modelDir,
                blobSize: blob.size,
                formDataEntries: Array.from(formData.entries()).map(([key]) => key)
            });

            try {
                const response = await fetch(uploadUrl, {
                    method: 'POST',
                    body: formData
                });

                console.log('[PoseDetector] Upload response status:', response.status);
                const responseText = await response.text();
                console.log('[PoseDetector] Upload response:', responseText);

                if (!response.ok) {
                    throw new Error(`Upload failed: ${response.status} ${response.statusText}\n${responseText}`);
                }

                const result = JSON.parse(responseText);
                console.log('[PoseDetector] Upload successful:', result);
                this.app?.uiManager?.hideLoadingPanel();
                return result;

            } catch (error) {
                console.error('[PoseDetector] Upload error:', error);
                this.app?.uiManager?.hideLoadingPanel();
                throw error;
            }
        } catch (error) {
            console.error('[PoseDetector] Save model error:', error);
            this.app?.uiManager?.hideLoadingPanel();
            return null;
        }
    }

    convertToHolisticFormat(results) {
        return {
            poseLandmarks: results.landmarks,
            poseWorldLandmarks: results.worldLandmarks,
            leftHandLandmarks: results.handPoses?.left?.worldLandmarks || null,
            rightHandLandmarks: results.handPoses?.right?.worldLandmarks || null,
            // Add any additional properties needed by MediapipePoseCalculator
        };
    }

    // 修改为 getter 方法
    isInitialized() {
        // Updated to reflect the detection mode
        if (this.detectionMode === DetectionMode.HOLISTIC) {
            return this._isInitialized && this.holisticDetector;
        } else if (this.detectionMode === DetectionMode.POSE_ONLY) {
            return this._isInitialized && this.poseDetector;
        } else if (this.detectionMode === DetectionMode.HANDS_ONLY) {
            return this._isInitialized && this.handDetector;
        } else if (this.detectionMode === DetectionMode.POSE_AND_HANDS) {
            return this._isInitialized && this.poseDetector && this.handDetector;
        }
        return false; // Unrecognized mode
    }

    async initialize(canvas = null) {
        // Prevent multiple concurrent initializations
        if (this._initializationPromise) {
            return this._initializationPromise;
        }

        this._initializationPromise = (async () => {
            try {
                if (this._isInitialized) {
                    console.log('[PoseDetector] Already initialized');
                    return true;
                }

                console.log('[PoseDetector] Starting initialization...');

                // Initialize MediaPipe
                const vision = await this.initializeMediaPipe();
                const hasGPU = await this.checkGPUSupport();
                let delegate = hasGPU ? "GPU" : "CPU";

                // Initialize detectors based on mode
                if (this.detectionMode === DetectionMode.HOLISTIC) {
                    // Show loading panel before starting initialization
                    this.uiManager?.showLoadingPanel('Initializing holistic pose detection...');
                    this.holisticDetector = await this.initializeHolisticDetector(vision, delegate, canvas);
                } else {
                    // Initialize based on the selected detection mode
                    if (this.detectionMode === DetectionMode.POSE_ONLY ||
                        this.detectionMode === DetectionMode.POSE_AND_HANDS) {
                        this.uiManager?.showLoadingPanel('Initializing pose detection...');
                        await this.initializePoseDetector(vision, delegate);
                    }

                    if (this.detectionMode === DetectionMode.HANDS_ONLY ||
                        this.detectionMode === DetectionMode.POSE_AND_HANDS) {
                        this.uiManager?.showLoadingPanel('Initializing hand detection...');
                        await this.initializeHandDetector(vision, delegate);
                    }
                }

                this.uiManager?.hideLoadingPanel();

                this._isInitialized = true;
                console.log('[PoseDetector] Initialization complete for mode:', this.detectionMode);
                return true;

            } catch (error) {
                console.error('[PoseDetector] Initialization failed:', error);
                this._isInitialized = false;
                this.uiManager?.hideLoadingPanel();
                throw error;
            } finally {
                this._initializationPromise = null;
            }
        })();

        return this._initializationPromise;
    }

    async initializeMediaPipe() {
        if (this.initPromise) {
            return this.initPromise;
        }

        this.initPromise = (async () => {
            try {
                const hasGPU = await this.checkGPUSupport();

                console.log('[PoseDetector] Creating vision runtime with:', {
                    useGPU: hasGPU
                });

                // MediaPipe Vision tasks need specific runtime config
                const vision = await FilesetResolver.forVisionTasks(
                    "https://cdn.jsdelivr.net/npm/@mediapipe/tasks-vision@latest/wasm",
                );

                this.mediapipeLoaded = true;
                return vision;
            } catch (error) {
                console.error('[PoseDetector] MediaPipe initialization error:', error);
                throw error;
            }
        })();

        return this.initPromise;
    }

    async _getVideoDimensionsWithTimeout(videoElement, timeout = 5000) {
        if (!videoElement) {
            console.warn('[PoseDetector] No video element provided, using defaults');
            return {
                width: this.resolutionConfig.width,
                height: this.resolutionConfig.height
            };
        }

        // If video already has dimensions, use them
        if (videoElement.videoWidth && videoElement.videoHeight) {
            return {
                width: Math.max(videoElement.videoWidth, 640),
                height: Math.max(videoElement.videoHeight, 480)
            };
        }

        // Wait for video metadata with timeout
        try {
            const dimensions = await Promise.race([
                new Promise((resolve) => {
                    const handleMetadata = () => {
                        resolve({
                            width: Math.max(videoElement.videoWidth, 640),
                            height: Math.max(videoElement.videoHeight, 480)
                        });
                    };
                    videoElement.addEventListener('loadedmetadata', handleMetadata, { once: true });
                }),
                new Promise((_, reject) =>
                    setTimeout(() => reject(new Error('Video metadata load timeout')), timeout)
                )
            ]);
            console.log('[PoseDetector] Got video dimensions:', dimensions);
            return dimensions;
        } catch (error) {
            console.warn('[PoseDetector] Failed to get video dimensions:', error);
            // Return default dimensions if timeout
            return {
                width: 640,
                height: 480
            };
        }
    }

    async checkGPUSupport() {
        try {
            // Add more comprehensive GPU checks
            const checks = await Promise.all([
                // Check WebGL2
                new Promise(resolve => {
                    const canvas = document.createElement('canvas');
                    const gl = canvas.getContext('webgl2');
                    resolve(!!gl);
                }),
                // Check WebGPU
                new Promise(resolve => {
                    if ('gpu' in navigator) {
                        navigator.gpu.requestAdapter()
                            .then(adapter => resolve(!!adapter))
                            .catch(() => resolve(false));
                    } else {
                        resolve(false);
                    }
                })
            ]);

            const hasGPU = checks.some(result => result);
            console.log('[PoseDetector] GPU support check:', {
                webgl2: checks[0],
                webgpu: checks[1],
                hasGPU
            });

            return hasGPU;
        } catch (e) {
            console.warn('[PoseDetector] GPU check failed:', e);
            return false;
        }
    }

    async reset() {
        console.log('[PoseDetector] Resetting detector');
        try {
            // Close existing detectors properly
            if (this.holisticDetector) {
                await this.holisticDetector.close();
                this.holisticDetector = null;
            }
            if (this.poseDetector) {
                await this.poseDetector.close();
                this.poseDetector = null;
            }
            if (this.handDetector) {
                await this.handDetector.close();
                this.handDetector = null;
            }

            // Reset state
            this._isInitialized = false;
            this.mediapipeLoaded = false;
            this.initPromise = null;

            console.log('[PoseDetector] Reset completed');
        } catch (error) {
            console.error('[PoseDetector] Reset failed:', error);
            throw error;
        }
    }

    setupDebugVisualization() {
        // ... debug canvas setup ...
    }

    // Remove drawDebugView method as visualization should be handled by DebugVisualizer


    async start(videoElement) {
        try {
            if (!this.detector) {
                await this.initialize(videoElement);
            }
        } catch (error) {
            console.error("Failed to start pose detection:", error);
        }
    }

    async detectPose(video, timestamp) {
        // Add input validation
        if (!video || video.readyState !== 4 || video.videoWidth === 0 || video.videoHeight === 0) {
            console.debug('[PoseDetector] Video not ready:', {
                readyState: video?.readyState,
                width: video?.videoWidth,
                height: video?.videoHeight
            });
            return null;
        }

        if (!this.isInitialized()) {
            console.warn('[PoseDetector] Not initialized, initializing now...');
            try {
                await this.initialize();
            } catch (error) {
                console.error('[PoseDetector] Failed to initialize:', error);
                throw new Error('[PoseDetector] Not initialized');
            }
        }

        try {
            const currentTimestamp = timestamp || performance.now();
            let poseResult = {
                landmarks: null,
                worldLandmarks: null,
                handPoses: {},
                timestamp: currentTimestamp
            };

            // Use detectionMode rather than detectorType for clarity
            if (this.detectionMode === DetectionMode.HOLISTIC) {
                // Holistic detection (combined pose and hands)
                if (this.holisticDetector) {
                    const results = await this.holisticDetector.detectForVideo(video, currentTimestamp);
                    if (results) {
                        poseResult = this.convertHolisticResults(results);
                    }
                }
            } else {
                // Handle separate detection modes
                if (this.detectionMode === DetectionMode.POSE_ONLY ||
                    this.detectionMode === DetectionMode.POSE_AND_HANDS) {
                    // Detect pose
                    if (this.poseDetector) {
                        const poseResults = await this.poseDetector.detectForVideo(video, currentTimestamp);
                        if (poseResults) {
                            poseResult.landmarks = poseResults.landmarks || null;
                            poseResult.worldLandmarks = poseResults.worldLandmarks || null;
                        }
                    }
                }

                if (this.detectionMode === DetectionMode.HANDS_ONLY ||
                    this.detectionMode === DetectionMode.POSE_AND_HANDS) {
                    if (this.handDetector) {
                        // Detect hands
                        const handResults = await this.handDetector.detectForVideo(video, currentTimestamp);
                        if (handResults) {
                            poseResult.handPoses = this.processHandResults(handResults);
                        }
                    }
                }
            }

            // Ensure timestamp is always set
            poseResult.timestamp = currentTimestamp;

            // Store landmarks for action detection
            this.landmarks = poseResult.landmarks;
            this.worldLandmarks = poseResult.worldLandmarks;
            this.handPoses = poseResult.handPoses;

            // Notify visualizer if debug mode is on
            if (this.visualizer && this.debug) {
                this.visualizer.drawDebugVisualization(poseResult, video);
            }

            // Return results without gesture detection
            return poseResult;

        } catch (error) {
            console.error('[PoseDetector] Detection error:', error);
            return {
                landmarks: null,
                worldLandmarks: null,
                handPoses: {},
                timestamp: timestamp || performance.now()
            };
        }
    }

    // Add new method to combine pose and hand results
    combinePoseAndHandResults(poseResults, handResults, timestamp) {
        // If useHands is false or no handResults, provide empty hand poses
        const handPoses = handResults ? this.processHandResults(handResults) : {};

        return {
            landmarks: poseResults.landmarks,
            worldLandmarks: poseResults.worldLandmarks,
            handPoses: handPoses,
            timestamp: timestamp
        };
    }

    processHandResults(handResults) {
        if (!handResults?.handedness || !handResults?.landmarks) return {};

        const poses = {};
        handResults.handedness.forEach((hand, index) => {
            const side = hand[0].categoryName.toLowerCase();
            const landmarks = handResults.landmarks[index].map(lm => ({
                x: lm.x,
                y: lm.y,
                z: lm.z || 0,
                visibility: lm.visibility || 1
            }));

            poses[side] = {
                landmarks: landmarks,
                worldLandmarks: handResults.worldLandmarks?.[index],
            };
        });
        return poses;
    }

    convertHolisticResults(results) {
        if (!results) return {
            landmarks: null,
            worldLandmarks: null,
            handPoses: {
                left: null,
                right: null
            }
        };

        return {
            landmarks: results.poseLandmarks || null,
            worldLandmarks: results.poseWorldLandmarks || null,
            handPoses: {
                left: results.leftHandLandmarks ? {
                    landmarks: results.leftHandLandmarks,
                    worldLandmarks: results.leftHandWorldLandmarks
                } : null,
                right: results.rightHandLandmarks ? {
                    landmarks: results.rightHandLandmarks,
                    worldLandmarks: results.rightHandWorldLandmarks
                } : null
            }
        };
    }

    analyzeHandPose(landmarks) {
        if (!landmarks || landmarks.length === 0) return null;

        // Calculate finger curls
        const fingerCurls = this.calculateFingerCurls(landmarks);

        // Determine hand pose based on finger curls
        const pose = this.determineHandPose(fingerCurls);

        return {
            pose,
            fingerCurls,
            confidence: landmarks[0].visibility || 0
        };
    }

    calculateFingerCurls(landmarks) {
        // MediaPipe hand landmarks indices
        const fingerBases = [1, 5, 9, 13, 17]; // thumb, index, middle, ring, pinky
        const fingerTips = [4, 8, 12, 16, 20];

        return fingerBases.map((base, i) => {
            const tip = fingerTips[i];
            const mid = base + 2;

            // Calculate angles between finger segments
            const curl = this.calculateFingerCurl(
                landmarks[base],
                landmarks[mid],
                landmarks[tip]
            );
            return curl;
        });
    }

    calculateFingerCurl(base, mid, tip) {
        // Convert landmarks to vectors using BabylonJS Vector3
        const v1 = new Vector3(
            mid.x - base.x,
            mid.y - base.y,
            (mid.z || 0) - (base.z || 0)
        );
        const v2 = new Vector3(
            tip.x - mid.x,
            tip.y - mid.y,
            (tip.z || 0) - (mid.z || 0)
        );

        // Calculate angle between vectors using BabylonJS methods
        const dotProduct = Vector3.Dot(v1, v2);
        const length1 = v1.length();
        const length2 = v2.length();

        if (length1 === 0 || length2 === 0) return 0;
        const angle = Math.acos(dotProduct / (length1 * length2));
        return angle;
    }

    determineHandPose(fingerCurls) {
        // Threshold for considering a finger curled
        const curlThreshold = 0.7;

        // Check if all fingers are curled (fist)
        const isFist = fingerCurls.every(curl => curl > curlThreshold);

        // Check if all fingers are extended
        const isOpen = fingerCurls.every(curl < curlThreshold);

        if (isFist) return 'fist';
        if (isOpen) return 'open';
        return 'other';
    }

    convertTo3DSkeleton(landmarks, worldLandmarks) {
        if (!landmarks || !worldLandmarks) return null;

        // Initialize bone lengths on first detection
        if (this.boneLengths.size === 0) {
            this.calculateBoneLengths(worldLandmarks);
        }

        const skeleton = {
            joints: {},
            bones: {}
        };

        // Convert landmarks to joint positions
        this.keypointPairs.forEach(([joint, index]) => {
            if (worldLandmarks[index]) {
                const position = new Vector3(
                    worldLandmarks[index].x,
                    worldLandmarks[index].y,
                    worldLandmarks[index].z
                );
                skeleton.joints[joint] = {
                    position,
                    confidence: landmarks[index].visibility || 0
                };
            }
        });

        // Calculate bone rotations using inverse kinematics
        this.calculateBoneRotations(skeleton);

        return skeleton;
    }

    calculateBoneLengths(worldLandmarks) {
        this.bonePairs.forEach(([start, end, boneName]) => {
            const startPoint = worldLandmarks[start];
            const endPoint = worldLandmarks[end];
            if (startPoint && endPoint) {
                const length = new Vector3(
                    endPoint.x - startPoint.x,
                    endPoint.y - startPoint.y,
                    endPoint.z - startPoint.z
                ).length();
                this.boneLengths.set(boneName, length);
            }
        });
    }


    enableDebug(enabled = true) {
        this.debug = enabled;
    }

    // Add method to update dimensions when video becomes available
    updateDimensions(video) {
        if (video && video.videoWidth && video.videoHeight) {
            this.resolutionConfig.width = video.videoWidth;
            this.resolutionConfig.height = video.videoHeight;
            console.log('[PoseDetector] Updated dimensions:', {
                width: video.videoWidth,
                height: video.videoHeight
            });
        }
    }

    checkWasmSupport() {
        if (typeof WebAssembly === 'object') {
            try {
                const module = new WebAssembly.Module(new Uint8Array([
                    0x00, 0x61, 0x73, 0x6d, 0x01, 0x00, 0x00, 0x00
                ]));
                if (module instanceof WebAssembly.Module) {
                    const instance = new WebAssembly.Instance(module);
                    return instance instanceof WebAssembly.Instance;
                }
            } catch (e) {
                return false;
            }
        }
        return false;
    }

    async ensureModelExists(modelConfig) {
        try {
            return await this.modelDownloader.ensureModelExists(modelConfig);
        } catch (error) {
            console.error('[PoseDetector] Model check/download failed:', error);
            throw error;
        }
    }

    async initializeHolisticDetector(vision, delegate, canvas) {
        try {
            const config = this.modelConfigs.HOLISTIC;

            // Ensure model exists locally before creating detector
            const modelPath = await this.ensureModelExists(config);

            const detectorOptions = {
                baseOptions: {
                    modelAssetPath: modelPath,
                    delegate: delegate
                },
                ...config.options
            };

            if (this.holisticDetector) {
                await this.holisticDetector.close();
                this.holisticDetector = null;
            }

            console.log('[PoseDetector] Creating holistic detector with path:', modelPath);
            const holisticDetector = await HolisticLandmarker.createFromOptions(
                vision,
                detectorOptions
            );

            return holisticDetector;
        } catch (error) {
            console.error('[PoseDetector] Holistic detector initialization failed:', error);
            throw error;
        }
    }

    async initializeSeparateDetectors(vision, delegate, canvas) {
        // This method is now split into specialized methods for pose and hand detectors
        if (this.detectionMode === DetectionMode.POSE_ONLY ||
            this.detectionMode === DetectionMode.POSE_AND_HANDS) {
            await this.initializePoseDetector(vision, delegate);
        }

        if (this.detectionMode === DetectionMode.HANDS_ONLY ||
            this.detectionMode === DetectionMode.POSE_AND_HANDS) {
            await this.initializeHandDetector(vision, delegate);
        }
    }

    async initializePoseDetector(vision, delegate) {
        try {
            console.log('[PoseDetector] Initializing pose detector...');

            // Ensure pose model exists
            const poseConfig = this.modelConfigs.POSE;
            const poseModelPath = await this.ensureModelExists(poseConfig);

            // Create pose detector
            this.poseDetector = await PoseLandmarker.createFromOptions(
                vision,
                {
                    baseOptions: {
                        modelAssetPath: poseModelPath,
                        delegate: delegate
                    },
                    ...poseConfig.options
                }
            );

            console.log('[PoseDetector] Pose detector initialized successfully');
        } catch (error) {
            console.error('[PoseDetector] Pose detector initialization failed:', error);
            throw error;
        }
    }

    async initializeHandDetector(vision, delegate) {
        try {
            console.log('[PoseDetector] Initializing hand detector...');

            // Ensure hand model exists
            const handConfig = this.modelConfigs.HAND;
            const handModelPath = await this.ensureModelExists(handConfig);

            // Create hand detector
            this.handDetector = await HandLandmarker.createFromOptions(
                vision,
                {
                    baseOptions: {
                        modelAssetPath: handModelPath,
                        delegate: delegate
                    },
                    ...handConfig.options
                }
            );

            console.log('[PoseDetector] Hand detector initialized successfully');
        } catch (error) {
            console.error('[PoseDetector] Hand detector initialization failed:', error);
            throw error;
        }
    }



    // Register a visualizer for debug visualization
    setVisualizer(visualizer) {
        this.visualizer = visualizer;
    }

    // Enable/disable debug mode
    setDebugMode(enabled) {
        this.debug = enabled;

        if (this.visualizer) {
            this.visualizer.setDebugMode(enabled);
        }
    }

    setDebugMode(enabled) {
        this.debugVisualization.enabled = enabled;
        console.log(`[PoseDetector] Debug visualization ${enabled ? 'enabled' : 'disabled'}`);
    }

}

// Export the DetectionMode enum so it can be used by consumers
export { DetectionMode };
export default PoseDetector;