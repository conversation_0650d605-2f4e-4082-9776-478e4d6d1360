import { CONFIG } from '../config.js';

export class SettingsPanel {
    constructor(onSettingsChange) {
        this.panel = this.createPanel();
        this.onSettingsChange = onSettingsChange;
        this.settings = this.createSettings();
        this.initializeUI();
    }

    createPanel() {
        const panel = document.createElement('div');
        panel.className = 'settings-panel';
        panel.innerHTML = `
            <div class="settings-header">
                <h3>Settings</h3>
                <button class="settings-toggle">×</button>
            </div>
            <div class="settings-content"></div>
        `;
        document.body.appendChild(panel);

        // Add styles
        const style = document.createElement('style');
        style.textContent = `
            .settings-panel {
                position: fixed;
                top: 20px;
                right: 20px;
                background: rgba(0, 0, 0, 0.8);
                color: white;
                padding: 15px;
                border-radius: 8px;
                font-family: Arial, sans-serif;
                min-width: 250px;
                z-index: 1000;
            }
            .settings-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 15px;
            }
            .settings-toggle {
                background: none;
                border: none;
                color: white;
                font-size: 20px;
                cursor: pointer;
            }
            .setting-item {
                margin-bottom: 10px;
            }
            .setting-label {
                display: block;
                margin-bottom: 5px;
            }
            .setting-value {
                display: inline-block;
                margin-left: 10px;
                min-width: 40px;
            }
            input[type="range"] {
                width: 100%;
            }
            input[type="checkbox"] {
                margin-right: 10px;
            }
        `;
        document.head.appendChild(style);

        return panel;
    }

    createSettings() {
        return {
            'Gesture Detection': {
                punchVelocityThreshold: {
                    value: CONFIG.gestures.punch.velocityThreshold,
                    min: 0.05,
                    max: 0.3,
                    step: 0.01,
                    type: 'range'
                },
                punchDistanceThreshold: {
                    value: CONFIG.gestures.punch.distanceThreshold,
                    min: 0.05,
                    max: 0.2,
                    step: 0.01,
                    type: 'range'
                }
            },
            'Visual Effects': {
                particleCount: {
                    value: 1000,
                    min: 100,
                    max: 5000,
                    step: 100,
                    type: 'range'
                },
                showHandMesh: {
                    value: true,
                    type: 'checkbox'
                }
            },
            'Audio': {
                volume: {
                    value: 1.0,
                    min: 0,
                    max: 1,
                    step: 0.1,
                    type: 'range'
                },
                enabled: {
                    value: true,
                    type: 'checkbox'
                }
            }
        };
    }

    initializeUI() {
        const content = this.panel.querySelector('.settings-content');

        for (const [category, settings] of Object.entries(this.settings)) {
            const categoryEl = document.createElement('div');
            categoryEl.className = 'settings-category';
            categoryEl.innerHTML = `<h4>${category}</h4>`;

            for (const [name, setting] of Object.entries(settings)) {
                const item = document.createElement('div');
                item.className = 'setting-item';

                const label = document.createElement('label');
                label.className = 'setting-label';
                label.textContent = this.formatLabel(name);

                const input = document.createElement('input');
                input.type = setting.type;

                if (setting.type === 'range') {
                    input.min = setting.min;
                    input.max = setting.max;
                    input.step = setting.step;
                    input.value = setting.value;

                    const value = document.createElement('span');
                    value.className = 'setting-value';
                    value.textContent = setting.value;

                    input.addEventListener('input', () => {
                        setting.value = parseFloat(input.value);
                        value.textContent = input.value;
                        this.onSettingsChange(this.getSettings());
                    });

                    item.appendChild(label);
                    item.appendChild(input);
                    item.appendChild(value);
                } else if (setting.type === 'checkbox') {
                    input.checked = setting.value;
                    input.addEventListener('change', () => {
                        setting.value = input.checked;
                        this.onSettingsChange(this.getSettings());
                    });

                    label.insertBefore(input, label.firstChild);
                }

                categoryEl.appendChild(item);
            }

            content.appendChild(categoryEl);
        }

        // Add toggle functionality
        const toggle = this.panel.querySelector('.settings-toggle');
        toggle.addEventListener('click', () => {
            const content = this.panel.querySelector('.settings-content');
            content.style.display = content.style.display === 'none' ? 'block' : 'none';
        });
    }

    formatLabel(str) {
        return str.replace(/([A-Z])/g, ' $1')
            .replace(/^./, str => str.toUpperCase());
    }

    getSettings() {
        const result = {};
        for (const [category, settings] of Object.entries(this.settings)) {
            result[category] = {};
            for (const [name, setting] of Object.entries(settings)) {
                result[category][name] = setting.value;
            }
        }
        return result;
    }
} 