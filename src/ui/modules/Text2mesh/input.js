import { Button, Modal } from '../../components';
import { useState } from 'react';

export function Input({ onGenerate, isLoading = false }) {
    const [isOpen, setIsOpen] = useState(false);
    const [promptText, setPromptText] = useState('');

    const handleSubmit = () => {
        if (promptText.trim()) {
            onGenerate(promptText);
            setIsOpen(false);
            setPromptText('');
        }
    };

    const handleKeyDown = (e) => {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            handleSubmit();
        }
    };

    return (
        <>
            <Button 
                variant="primary"
                onClick={() => setIsOpen(true)}
                icon="magic"
                className="text2mesh-button"
            >
                Generate 3D Model
            </Button>

            <Modal
                isOpen={isOpen}
                onClose={() => setIsOpen(false)}
                title="Generate 3D Model"
                actions={[
                    { 
                        label: 'Cancel', 
                        onClick: () => setIsOpen(false),
                        variant: 'secondary'
                    },
                    { 
                        label: 'Generate',
                        onClick: handleSubmit,
                        variant: 'primary',
                        disabled: !promptText.trim() || isLoading,
                        loading: isLoading
                    }
                ]}
            >
                <div className="text2mesh-input">
                    <label htmlFor="prompt-text">Enter description for new 3D model:</label>
                    <textarea
                        id="prompt-text"
                        value={promptText}
                        onChange={(e) => setPromptText(e.target.value)}
                        onKeyDown={handleKeyDown}
                        placeholder="Describe the 3D model you want to generate..."
                        rows={4}
                        disabled={isLoading}
                    />
                    {isLoading && (
                        <div className="loading-message">
                            Generating your 3D model...
                        </div>
                    )}
                </div>
            </Modal>
        </>
    );
} 