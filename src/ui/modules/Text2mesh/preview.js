export function Preview({ imageUrl, videoUrl }) {
    return (
        <div className="text2mesh-preview">
            <div className="preview-section">
                <h3>Generated Image</h3>
                <img 
                    src={imageUrl} 
                    alt="Generated model preview"
                    className="preview-image"
                />
            </div>
            <div className="preview-section">
                <h3>Generated Video</h3>
                <video 
                    src={videoUrl}
                    controls
                    loop
                    muted
                    className="preview-video"
                />
            </div>
        </div>
    );
} 