.text2mesh-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.text2mesh-button {
    align-self: flex-end;
}

.text2mesh-input {
    padding: 20px;
}

.text2mesh-input label {
    display: block;
    margin-bottom: 10px;
    font-weight: 500;
    color: var(--text-primary);
}

.text2mesh-input textarea {
    width: 100%;
    padding: 12px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    resize: vertical;
    font-family: inherit;
    margin-bottom: 16px;
    background: var(--input-background);
    color: var(--text-primary);
}

.text2mesh-input textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px var(--primary-color-light);
}

.text2mesh-input .loading-message {
    color: var(--text-secondary);
    font-size: 14px;
    text-align: center;
    margin-top: 12px;
}

.text2mesh-preview {
    background: var(--surface-color);
    border-radius: 8px;
    padding: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    max-width: 400px;
}

.preview-section {
    margin-bottom: 20px;
}

.preview-section h3 {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 12px;
    color: var(--text-primary);
}

.preview-image,
.preview-video {
    width: 100%;
    border-radius: 4px;
    background: var(--background-color);
}

/* Theme variables */
:root {
    --primary-color: #007AFF;
    --primary-color-light: rgba(0, 122, 255, 0.1);
    --border-color: #E2E8F0;
    --text-primary: #1A202C;
    --text-secondary: #718096;
    --background-color: #FFFFFF;
    --surface-color: #F7FAFC;
    --input-background: #FFFFFF;
}

/* Dark theme */
@media (prefers-color-scheme: dark) {
    :root {
        --primary-color: #0A84FF;
        --primary-color-light: rgba(10, 132, 255, 0.1);
        --border-color: #2D3748;
        --text-primary: #FFFFFF;
        --text-secondary: #A0AEC0;
        --background-color: #1A202C;
        --surface-color: #2D3748;
        --input-background: #2D3748;
    }
} 