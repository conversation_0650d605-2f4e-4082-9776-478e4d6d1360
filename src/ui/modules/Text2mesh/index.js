import { Input } from './input';
import { Preview } from './preview';
// Server API removed - using agent-based architecture
// import { api } from '../../../server/api';
import { useState } from 'react';

export function Text2mesh({ app }) {
    const [isLoading, setIsLoading] = useState(false);
    const [previewData, setPreviewData] = useState(null);

    const handleGenerate = async (promptText) => {
        setIsLoading(true);
        try {
            const result = await api.textTo3D({ promptText });
            if (result.success) {
                const { imageResult, videoResult, meshResult } = result.data;
                setPreviewData({ imageResult, videoResult });

                // Load the 3D model into the scene
                await app.loadAssets(
                    '/assets/backgrounds/royal_esplanade_1k.hdr',
                    meshResult
                );
                console.log('New 3D model loaded successfully');
            }
        } catch (error) {
            console.error('Failed to generate 3D model:', error);
        } finally {
            setIsLoading(false);
        }
    };

    return (
        <div className="text2mesh-container">
            <Input
                onGenerate={handleGenerate}
                isLoading={isLoading}
            />
            {previewData && (
                <Preview
                    imageUrl={previewData.imageResult}
                    videoUrl={previewData.videoResult}
                />
            )}
        </div>
    );
} 