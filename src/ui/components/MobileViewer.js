/**
 * MobileViewer - A component to mirror the host's 3D scene on a mobile device
 *
 * This component creates a lightweight Three.js scene that mirrors the host's scene
 * and synchronizes camera movements and UI elements.
 */

// Using global THREE object loaded from script tags in index.html
// connectionManager removed - mobile functionality disabled
import mobileDebugger from '../../utils/mobileDebugger.js';
import { getDownloadServerPort } from '../../utils/portManager.js';

export default class MobileViewer {
    /**
     * Create a new MobileViewer
     * @param {Object} options - Configuration options
     * @param {HTMLElement} options.container - The container element
     */
    constructor(options = {}) {
        this.options = {
            container: document.body,
            ...options
        };

        this.container = this.options.container;
        this.scene = null;
        this.camera = null;
        this.renderer = null;
        this.controls = null;
        this.currentMesh = null;
        this.isInitialized = false;
        this.animationFrameId = null;
        this._hasRendered = false;

        // Bind methods
        this._onWindowResize = this._onWindowResize.bind(this);
        this._animate = this._animate.bind(this);
    }

    /**
     * Initialize the viewer
     * @returns {Promise<boolean>} - Whether initialization was successful
     */
    async initialize() {
        try {
            // Create the scene
            this.scene = new THREE.Scene();
            this.scene.background = new THREE.Color(0x000000);

            // Create the camera
            const aspect = this.container.clientWidth / this.container.clientHeight;
            this.camera = new THREE.PerspectiveCamera(45, aspect, 0.1, 1000);
            this.camera.position.set(0, 0, 5);

            // Create the renderer
            this.renderer = new THREE.WebGLRenderer({ antialias: true, alpha: true });
            this.renderer.setSize(this.container.clientWidth, this.container.clientHeight);
            this.renderer.setPixelRatio(window.devicePixelRatio);
            this.renderer.outputEncoding = THREE.sRGBEncoding;

            // Add the renderer to the container
            this.container.appendChild(this.renderer.domElement);

            // Create the controls - use the global THREE.OrbitControls if available
            try {
                // Try different ways OrbitControls might be available
                if (window.THREE && THREE.OrbitControls) {
                    // Direct property on THREE
                    this.controls = new THREE.OrbitControls(this.camera, this.renderer.domElement);
                } else if (window.OrbitControls) {
                    // Global OrbitControls
                    this.controls = new OrbitControls(this.camera, this.renderer.domElement);
                } else if (window.THREE && window.THREE.examples && window.THREE.examples.jsm && window.THREE.examples.jsm.controls && window.THREE.examples.jsm.controls.OrbitControls) {
                    // Module path
                    this.controls = new THREE.examples.jsm.controls.OrbitControls(this.camera, this.renderer.domElement);
                } else {
                    // Last resort - try to find it in any property of THREE
                    let found = false;
                    for (const key in THREE) {
                        if (key.includes('OrbitControls')) {
                            this.controls = new THREE[key](this.camera, this.renderer.domElement);
                            found = true;
                            break;
                        }
                    }

                    if (!found) {
                        throw new Error('OrbitControls not found');
                    }
                }
            } catch (error) {
                console.error('[MobileViewer] OrbitControls not found:', error);
                throw new Error('OrbitControls not found. Make sure Three.js and its extensions are properly loaded.');
            }

            this.controls.enableDamping = true;
            this.controls.dampingFactor = 0.05;

            // Add lights
            const ambientLight = new THREE.AmbientLight(0xffffff, 0.5);
            this.scene.add(ambientLight);

            const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
            directionalLight.position.set(1, 1, 1);
            this.scene.add(directionalLight);

            // Add window resize listener
            window.addEventListener('resize', this._onWindowResize);

            // Set up message handlers
            this._setupMessageHandlers();

            // Start animation loop
            this._animate();

            this.isInitialized = true;
            console.log('[MobileViewer] Initialized successfully');
            mobileDebugger.info('MobileViewer initialized successfully');

            return true;
        } catch (error) {
            console.error('[MobileViewer] Error initializing:', error);
            mobileDebugger.error('Error initializing MobileViewer:', error.message);
            mobileDebugger.error('Error stack:', error.stack);
            return false;
        }
    }

    /**
     * Set up message handlers for synchronization
     * @private
     */
    _setupMessageHandlers() {
        // Mobile connection functionality disabled - using realtime mode
        console.log('[MobileViewer] Message handlers disabled - mobile functionality removed');
    }

    /**
     * Update the camera based on data from the host
     * @param {Object} data - Camera data
     * @private
     */
    _updateCamera(data) {
        if (!this.camera || !data) return;

        // Update camera position
        if (data.position) {
            this.camera.position.set(
                data.position.x,
                data.position.y,
                data.position.z
            );
        }

        // Update camera rotation
        if (data.rotation) {
            this.camera.rotation.set(
                data.rotation.x,
                data.rotation.y,
                data.rotation.z
            );
        }

        // Update controls target
        if (data.target && this.controls) {
            this.controls.target.set(
                data.target.x,
                data.target.y,
                data.target.z
            );
            this.controls.update();
        }
    }

    /**
     * Update the camera based on data from the UI state
     * @param {object} cameraData - The camera data from UI state
     */
    updateCameraFromHost(cameraData) {
        if (!cameraData) {
            console.warn('[MobileViewer] Cannot update camera: no camera data provided');
            return;
        }

        console.log('[MobileViewer] Updating camera from host data:', cameraData);

        if (!this.camera || !this.controls) {
            console.warn('[MobileViewer] Cannot update camera: camera or controls not initialized');
            return;
        }

        // Update camera position if provided
        if (cameraData.position) {
            this.camera.position.set(
                cameraData.position.x,
                cameraData.position.y,
                cameraData.position.z
            );
        }

        // Update camera rotation if provided
        if (cameraData.rotation) {
            this.camera.rotation.set(
                cameraData.rotation.x,
                cameraData.rotation.y,
                cameraData.rotation.z
            );
        }

        // Update the controls
        if (this.controls) {
            this.controls.update();
        }

        // Render a frame to show the changes
        this._render();
    }

    /**
     * Update the mesh based on data from the host
     * @param {Object} data - Mesh data
     * @private
     */
    _updateMesh(data) {
        console.log('[MobileViewer] Updating mesh with data:', data);
        mobileDebugger.info('Updating mesh');
        mobileDebugger.debug('Mesh data:', JSON.stringify(data));

        if (!this.scene) {
            console.log('[MobileViewer] No scene, cannot update mesh');
            mobileDebugger.error('No scene available, cannot update mesh');
            return;
        }

        if (!data) {
            console.log('[MobileViewer] No data, cannot update mesh');
            mobileDebugger.error('No mesh data provided');
            return;
        }

        // If this is a test mesh with a specific ID, create a test cube
        if (data.meshId === 'test-cube') {
            console.log('[MobileViewer] Creating test cube');
            this._createTestCube();
            return;
        }

        // Remove current mesh if it exists
        if (this.currentMesh) {
            console.log('[MobileViewer] Removing current mesh');
            this.scene.remove(this.currentMesh);
            this.currentMesh = null;
        }

        // Check if we have a mesh to load
        if (!data.hasMesh) {
            console.log('[MobileViewer] No mesh to load');
            return;
        }

        // Get the URL from the data
        let url = data.meshUrl || (data.meshId ? `/assets/meshes/${data.meshId}` : null);
        console.log('[MobileViewer] Original Mesh URL:', url);

        // If the URL doesn't start with http or https, try to construct a full URL
        if (url && !url.startsWith('http://') && !url.startsWith('https://')) {
            const protocol = window.location.protocol;
            const hostname = window.location.hostname || 'localhost';

            // Use the port manager to get the correct download server port
            // This is important because the WebSocket server runs on port 3000
            // but the assets are served from the download server on a dynamic port
            const port = getDownloadServerPort();

            url = `${protocol}//${hostname}:${port}${url.startsWith('/') ? '' : '/'}${url}`;
            console.log('[MobileViewer] Constructed full URL:', url);
        }

        // For test-cube, use the direct URL
        if (data.meshId === 'test-cube') {
            url = 'https://threejs.org/examples/models/gltf/cube/cube.gltf';
            console.log('[MobileViewer] Using test cube URL:', url);
        }

        // Load the new mesh if a URL is provided
        if (url) {
            console.log('[MobileViewer] Loading mesh from URL:', url);
            // Use the global THREE.GLTFLoader if available
            let loader;
            try {
                // Try different ways GLTFLoader might be available
                if (window.THREE && THREE.GLTFLoader) {
                    // Direct property on THREE
                    loader = new THREE.GLTFLoader();
                } else if (window.GLTFLoader) {
                    // Global GLTFLoader
                    loader = new GLTFLoader();
                } else if (window.THREE && window.THREE.examples && window.THREE.examples.jsm && window.THREE.examples.jsm.loaders && window.THREE.examples.jsm.loaders.GLTFLoader) {
                    // Module path
                    loader = new THREE.examples.jsm.loaders.GLTFLoader();
                } else {
                    // Last resort - try to find it in any property of THREE
                    let found = false;
                    for (const key in THREE) {
                        if (key.includes('GLTFLoader')) {
                            loader = new THREE[key]();
                            found = true;
                            break;
                        }
                    }

                    if (!found) {
                        throw new Error('GLTFLoader not found');
                    }
                }
            } catch (error) {
                console.error('[MobileViewer] GLTFLoader not found:', error);
                return;
            }

            // Show loading indicator
            this._showLoadingIndicator();

            console.log('[MobileViewer] Starting to load mesh with loader:', loader);
            // Log the loader being used
            console.log('[MobileViewer] Using loader:', loader.constructor.name || 'Unknown loader type');

            // Add a timeout to detect if loading takes too long
            const loadTimeout = setTimeout(() => {
                console.warn('[MobileViewer] Mesh loading is taking longer than expected. URL may be inaccessible:', url);
                this._showErrorMessage('Mesh loading is taking longer than expected. The URL may be inaccessible.');
            }, 10000); // 10 seconds timeout

            loader.load(
                url,
                (gltf) => {
                    // Clear the timeout since loading succeeded
                    clearTimeout(loadTimeout);
                    console.log('[MobileViewer] GLTF loaded successfully:', gltf);

                    // Hide loading indicator
                    this._hideLoadingIndicator();

                    // Add the mesh to the scene
                    this.currentMesh = gltf.scene;
                    console.log('[MobileViewer] Adding model to scene:', this.currentMesh);
                    this.scene.add(this.currentMesh);

                    // Center the mesh
                    const box = new THREE.Box3().setFromObject(this.currentMesh);
                    const center = box.getCenter(new THREE.Vector3());
                    const size = box.getSize(new THREE.Vector3());
                    console.log('[MobileViewer] Mesh size:', size);

                    // Position the mesh at the center
                    this.currentMesh.position.x = -center.x;
                    this.currentMesh.position.y = -center.y;
                    this.currentMesh.position.z = -center.z;

                    // Position the camera to view the entire mesh
                    const maxDim = Math.max(size.x, size.y, size.z);
                    const fov = this.camera.fov * (Math.PI / 180);
                    let cameraZ = Math.abs(maxDim / 2 / Math.tan(fov / 2));
                    cameraZ *= 1.5; // Add some margin

                    this.camera.position.z = cameraZ;
                    console.log('[MobileViewer] Camera positioned at z:', cameraZ);

                    // Update the controls
                    this.controls.target.set(0, 0, 0);
                    this.controls.update();

                    console.log('[MobileViewer] Mesh loaded successfully');

                    // Force a render to show the model
                    this.renderer.render(this.scene, this.camera);
                },
                (xhr) => {
                    // Update loading progress
                    const progress = (xhr.loaded / xhr.total) * 100;
                    console.log(`[MobileViewer] Loading progress: ${progress.toFixed(2)}%`);
                    this._updateLoadingProgress(progress);
                },
                (error) => {
                    // Hide loading indicator
                    this._hideLoadingIndicator();
                    console.error('[MobileViewer] Error loading mesh:', error);
                    mobileDebugger.error('Error loading mesh:', error.message || 'Unknown error');

                    // Log the URL that failed
                    mobileDebugger.error('Failed URL:', url);

                    // Try to get more details about the error
                    if (error.target && error.target.status) {
                        mobileDebugger.error('HTTP Status:', error.target.status);
                    }

                    // Show error message
                    this._showErrorMessage(`Failed to load mesh: ${error.message || 'Unknown error'}`);

                    // Try to create a fallback cube
                    console.log('[MobileViewer] Creating fallback cube due to load error');
                    mobileDebugger.info('Creating fallback test cube');
                    this._createTestCube();
                }
            );
        }
    }

    /**
     * Update the UI based on data from the host
     * @param {Object} data - UI data
     * @private
     */
    _updateUI(data) {
        console.log('[MobileViewer] UI update received:', data);

        // Check if we have a MobileController instance
        if (window.mobileController && typeof window.mobileController.updateUIState === 'function') {
            console.log('[MobileViewer] Forwarding UI update to MobileController');
            window.mobileController.updateUIState(data);
        } else {
            console.log('[MobileViewer] No MobileController found to handle UI update');

            // Dispatch a custom event for the MobileController to handle
            const event = new CustomEvent('uiUpdate', { detail: data });
            window.dispatchEvent(event);
        }
    }

    /**
     * Show a loading indicator
     * @private
     */
    _showLoadingIndicator() {
        // Remove any existing indicator
        this._hideLoadingIndicator();

        // Create a loading indicator
        const indicator = document.createElement('div');
        indicator.className = 'loading-indicator';
        indicator.style.position = 'absolute';
        indicator.style.top = '50%';
        indicator.style.left = '50%';
        indicator.style.transform = 'translate(-50%, -50%)';
        indicator.style.backgroundColor = 'rgba(0, 0, 0, 0.8)';
        indicator.style.color = '#fff';
        indicator.style.padding = '20px';
        indicator.style.borderRadius = '10px';
        indicator.style.textAlign = 'center';
        indicator.style.zIndex = '100';

        // Add a spinner
        const spinner = document.createElement('div');
        spinner.style.border = '4px solid rgba(255, 255, 255, 0.3)';
        spinner.style.borderTop = '4px solid #ffffff';
        spinner.style.borderRadius = '50%';
        spinner.style.width = '30px';
        spinner.style.height = '30px';
        spinner.style.animation = 'spin 1s linear infinite';
        spinner.style.margin = '0 auto 15px auto';

        // Add keyframes for the spinner if they don't exist
        if (!document.querySelector('style[data-id="spin-keyframes"]')) {
            const style = document.createElement('style');
            style.dataset.id = 'spin-keyframes';
            style.textContent = `
                @keyframes spin {
                    0% { transform: rotate(0deg); }
                    100% { transform: rotate(360deg); }
                }
            `;
            document.head.appendChild(style);
        }

        // Add progress bar
        const progressContainer = document.createElement('div');
        progressContainer.style.width = '100%';
        progressContainer.style.height = '10px';
        progressContainer.style.backgroundColor = 'rgba(255, 255, 255, 0.2)';
        progressContainer.style.borderRadius = '5px';
        progressContainer.style.marginTop = '10px';
        progressContainer.style.overflow = 'hidden';

        const progressBar = document.createElement('div');
        progressBar.className = 'progress-bar';
        progressBar.style.width = '0%';
        progressBar.style.height = '100%';
        progressBar.style.backgroundColor = '#4CAF50';
        progressBar.style.transition = 'width 0.3s ease-in-out';

        progressContainer.appendChild(progressBar);

        // Add text
        const text = document.createElement('div');
        text.textContent = 'Loading mesh...';
        text.style.marginBottom = '10px';

        // Add elements to the indicator
        indicator.appendChild(spinner);
        indicator.appendChild(text);
        indicator.appendChild(progressContainer);

        // Add the indicator to the container
        this.container.appendChild(indicator);
    }

    /**
     * Update the loading progress
     * @param {number} progress - Progress percentage (0-100)
     * @private
     */
    _updateLoadingProgress(progress) {
        const progressBar = document.querySelector('.loading-indicator .progress-bar');
        if (progressBar) {
            progressBar.style.width = `${progress}%`;
        }
    }

    /**
     * Hide the loading indicator
     * @private
     */
    _hideLoadingIndicator() {
        const indicator = document.querySelector('.loading-indicator');
        if (indicator && indicator.parentNode) {
            indicator.parentNode.removeChild(indicator);
        }
    }

    /**
     * Show an error message
     * @param {string} message - The error message to display
     * @private
     */
    _showErrorMessage(message) {
        // Create error message element if it doesn't exist
        let errorElement = document.querySelector('.error-message');
        if (!errorElement) {
            errorElement = document.createElement('div');
            errorElement.className = 'error-message';
            errorElement.style.position = 'absolute';
            errorElement.style.bottom = '20px';
            errorElement.style.left = '20px';
            errorElement.style.right = '20px';
            errorElement.style.backgroundColor = 'rgba(255, 0, 0, 0.7)';
            errorElement.style.color = 'white';
            errorElement.style.padding = '10px';
            errorElement.style.borderRadius = '5px';
            errorElement.style.zIndex = '1000';
            errorElement.style.textAlign = 'center';
            document.body.appendChild(errorElement);
        } else {
            errorElement.style.display = 'block';
        }

        // Set the error message
        errorElement.textContent = message;

        // Auto-hide after 5 seconds
        setTimeout(() => {
            errorElement.style.display = 'none';
        }, 5000);
    }

    /**
     * Handle window resize
     * @private
     */
    _onWindowResize() {
        if (!this.camera || !this.renderer) return;

        // Update camera aspect ratio
        this.camera.aspect = this.container.clientWidth / this.container.clientHeight;
        this.camera.updateProjectionMatrix();

        // Update renderer size
        this.renderer.setSize(this.container.clientWidth, this.container.clientHeight);
    }

    /**
     * Create a test cube for debugging
     * @private
     */
    _createTestCube() {
        console.log('[MobileViewer] Creating test cube');

        // Remove current mesh if it exists
        if (this.currentMesh) {
            console.log('[MobileViewer] Removing current mesh');
            this.scene.remove(this.currentMesh);
            this.currentMesh = null;
        }

        try {
            // Create a simple cube
            const geometry = new THREE.BoxGeometry(1, 1, 1);
            const material = new THREE.MeshBasicMaterial({ color: 0x00ff00, wireframe: true });
            const cube = new THREE.Mesh(geometry, material);

            console.log('[MobileViewer] Adding cube to scene');
            this.scene.add(cube);
            this.currentMesh = cube;

            // Position the camera
            this.camera.position.z = 5;

            // Update the controls
            this.controls.target.set(0, 0, 0);
            this.controls.update();

            console.log('[MobileViewer] Test cube created successfully');

            // Force a render to show the cube
            this._render();

            // Add animation to rotate the cube
            const animate = () => {
                requestAnimationFrame(animate);
                cube.rotation.x += 0.01;
                cube.rotation.y += 0.01;
                this._render();
            };
            animate();

            return true;
        } catch (error) {
            console.error('[MobileViewer] Error creating test cube:', error);
            return false;
        }
    }

    /**
     * Render a single frame
     * @private
     */
    _render() {
        if (this.renderer && this.scene && this.camera) {
            // Only log the first time
            if (!this._hasRendered) {
                console.log('[MobileViewer] Rendering scene');
                this._hasRendered = true;
            }
            this.renderer.render(this.scene, this.camera);
        } else {
            console.log('[MobileViewer] Cannot render - missing renderer, scene, or camera');
        }
    }

    /**
     * Animation loop
     * @private
     */
    _animate() {
        this.animationFrameId = requestAnimationFrame(this._animate);

        if (this.controls) {
            this.controls.update();
        }

        this._render();
    }

    /**
     * Dispose of the viewer and clean up resources
     */
    dispose() {
        // Stop animation loop
        if (this.animationFrameId) {
            cancelAnimationFrame(this.animationFrameId);
        }

        // Remove event listeners
        window.removeEventListener('resize', this._onWindowResize);

        // Dispose of Three.js objects
        if (this.controls) {
            this.controls.dispose();
        }

        if (this.renderer) {
            this.renderer.dispose();

            // Remove the canvas from the DOM
            if (this.renderer.domElement && this.renderer.domElement.parentNode) {
                this.renderer.domElement.parentNode.removeChild(this.renderer.domElement);
            }
        }

        // Clear references
        this.scene = null;
        this.camera = null;
        this.renderer = null;
        this.controls = null;
        this.currentMesh = null;

        console.log('[MobileViewer] Disposed successfully');
    }
}
