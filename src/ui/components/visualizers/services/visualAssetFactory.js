import { MeshBuilder, StandardMaterial, Color3, DynamicTexture } from '@babylonjs/core';

export class VisualAssetFactory {
    constructor(scene) {
        this.scene = scene;
        this.meshPool = new Map();
        this.materials = this.initializeMaterials();

        // Add canvas2D specific assets
        this.canvasAssets = {
            colors: {
                pose: 'rgba(245, 117, 66, 0.8)',
                hand: 'rgba(102, 204, 255, 0.8)',
                connector: 'rgba(255, 255, 255, 0.8)'
            },
            lineWidths: {
                connection: 2,
                point: 4
            }
        };
    }

    initializeMaterials() {
        const materials = {
            ikPoint: new StandardMaterial("ikPointMat", this.scene),
            ikLine: new StandardMaterial("ikLineMat", this.scene),
            label: new StandardMaterial("labelMat", this.scene)
        };

        // Configure materials
        materials.ikPoint.emissiveColor = new Color3(1, 0, 0);
        materials.ikPoint.diffuseColor = new Color3(1, 0, 0);
        materials.ikPoint.specularColor = new Color3(0, 0, 0);
        materials.ikPoint.alpha = 1;

        materials.ikLine.emissiveColor = new Color3(1, 1, 1);
        materials.ikLine.diffuseColor = new Color3(1, 1, 1);
        materials.ikLine.specularColor = new Color3(0, 0, 0);
        materials.ikLine.alpha = 1;

        return materials;
    }

    createChainMaterial(chainName, color) {
        const material = new StandardMaterial(`ikDebug_${chainName}`, this.scene);
        material.emissiveColor = color;
        material.diffuseColor = color;
        material.specularColor = new Color3(0, 0, 0);
        material.wireframe = false;
        material.backFaceCulling = false;
        material.useLogarithmicDepth = true;
        material.alpha = 1;
        return material;
    }

    createVisualizationSphere(sphereKey, size = 0.05) {
        // Ensure sphereKey is a string
        const safeKey = String(sphereKey || '').trim() || 'unnamed';
        
        const sphere = MeshBuilder.CreateSphere(
            `ik_${safeKey}`,
            { diameter: size },
            this.scene
        );

        const material = this.materials.ikPoint.clone(`ikMat_${safeKey}`);
        material.emissiveColor = new Color3(1, 0, 0);
        material.alpha = 1;
        sphere.material = material;
        sphere.renderingGroupId = 2;

        // Create label plane with safe key
        const labelPlane = this.createLabelPlane(safeKey, sphere);

        this.meshPool.set(safeKey, sphere);
        return sphere;
    }

    createLabelPlane(text, parent, options = { width: 0.2, height: 0.1 }) {
        // Ensure text is a string
        const safeText = String(text || '').trim() || 'unnamed';
        
        const labelPlane = MeshBuilder.CreatePlane(
            `label_${safeText}`,
            options,
            this.scene
        );

        const texture = new DynamicTexture(
            `texture_${safeText}`,
            { width: 512, height: 256 },
            this.scene
        );

        // Draw validated text
        texture.drawText(
            safeText,
            null,
            100,
            "bold 36px Arial",
            "white",
            "transparent"
        );

        const material = new StandardMaterial(`labelMat_${safeText}`, this.scene);
        material.diffuseTexture = texture;
        material.specularColor = new Color3(0, 0, 0);
        material.emissiveColor = new Color3(1, 1, 1);
        material.backFaceCulling = false;
        material.useAlphaFromDiffuseTexture = true;

        labelPlane.material = material;
        labelPlane.renderingGroupId = 2;
        labelPlane.parent = parent;

        // Adjust position relative to parent mesh size
        const parentBounds = parent.getBoundingInfo().boundingBox;
        const parentHeight = parentBounds.maximumWorld.y - parentBounds.minimumWorld.y;
        labelPlane.position.y = parentHeight * 2; // Position above parent
        
        // Add slight offset in x and z to prevent overlapping
        labelPlane.position.x = parentHeight * 0.5;
        labelPlane.position.z = parentHeight * 0.1;

        // Ensure label always faces camera
        labelPlane.billboardMode = 7;

        return labelPlane;
    }

    createDebugLine(chainName, points) {
        // Ensure chainName is a string
        const safeName = String(chainName || '').trim() || 'unnamed';
        
        const lineMesh = MeshBuilder.CreateLines(`ikDebug_${safeName}`, {
            points,
            updatable: true
        }, this.scene);

        lineMesh.enableEdgesRendering();
        lineMesh.edgesWidth = 4.0;
        lineMesh.renderingGroupId = 2;

        return lineMesh;
    }

    updateDebugLine(lineMesh, points) {
        if (!points || points.length < 2) return;

        MeshBuilder.CreateLines(null, {
            points,
            instance: lineMesh
        });

        lineMesh.isVisible = true;
        lineMesh.computeWorldMatrix(true);
    }

    dispose() {
        this.meshPool.forEach(mesh => mesh?.dispose());
        this.meshPool.clear();
        Object.values(this.materials).forEach(mat => mat?.dispose());
    }

    getMesh(key) {
        return this.meshPool.get(key);
    }

    storeMesh(key, mesh) {
        this.meshPool.set(key, mesh);
    }

    // Add Canvas2D specific methods
    getCanvasStyle(type) {
        return this.canvasAssets.colors[type];
    }

    getLineWidth(type) {
        return this.canvasAssets.lineWidths[type];
    }

    // Add new 3D visualization methods
    createVisualizationPoint(key, options = {}) {
        const sphere = this.createVisualizationSphere(key, options.size);
        const label = options.withLabel ? 
            this.createLabelPlane(key, sphere, options.labelSize) : null;
        
        return { sphere, label };
    }

    // Add helper method for safe string conversion
    _sanitizeKey(key) {
        if (key === null || key === undefined) return 'unnamed';
        return String(key).trim() || 'unnamed';
    }
}