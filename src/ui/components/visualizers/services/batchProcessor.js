export class BatchProcessor {
    constructor(options = {}) {
        this.maxBatchSize = options.maxBatchSize || 30;
        this.batchInterval = options.batchInterval || 16.67; // Target 60fps
        this.lastBatchTime = 0;
        this.isProcessing = false;
        this.queue = null;
        this.resultBuffer = {
            pose: new Map(),
            hands: {
                left: new Map(),
                right: new Map()
            }
        };
        this.thresholds = {
            position: 0.01,  // 1cm minimum change
            rotation: 0.05,  // ~3 degrees minimum change
            confidence: 0.1  // 10% confidence change threshold
        };
        this.lastUpdate = 0;
        this.updateInterval = options.updateInterval || 16.67; // 60fps by default

        // Add mesh pool management
        this.meshPools = {
            spheres: [],
            lines: [],
            labels: []
        };

        // Add visualization batches
        this.visualizationBatches = {
            landmarks: [],
            handPoses: { left: [], right: [] },
            ikChains: new Map(),
            timestamp: 0
        };

        // Add frame tracking
        this.frameStats = {
            lastFrameTime: 0,
            frameCount: 0,
            fps: 0
        };
    }

    shouldUpdate() {
        const now = performance.now();
        const shouldUpdate = now - this.lastUpdate >= this.updateInterval;

        if (shouldUpdate) {
            this.lastUpdate = now;
        }

        return shouldUpdate;
    }

    queueUpdate(type, data) {
        const now = performance.now();
        if (now - this.lastBatchTime < this.batchInterval) {
            return false;
        }

        this.queue = {
            type,
            data,
            timestamp: now
        };

        this.lastBatchTime = now;
        return true;
    }

    processQueue(processor) {
        if (this.isProcessing || !this.queue) return;

        try {
            this.isProcessing = true;
            const { type, data } = this.queue;
            switch (type) {
                case 'combined':
                    this.processCombinedData(data, processor);
                    break;
                case 'ik':
                    processor.processIKData?.(data);
                    break;
                case 'hands':
                    processor.processHandsData?.(data);
                    break;
            }
        } catch (error) {
            console.error('[BatchProcessor] Processing error:', error);
        } finally {
            this.isProcessing = false;
            this.queue = null;
        }
    }

    processCombinedData(data, processor) {
        const processedData = {
            landmarks: null,
            hands: null,
            pose: null
        };

        if (data.landmarks) {
            processedData.landmarks = this.processLandmarks(data.landmarks);
        }

        if (data.hands) {
            processedData.hands = this.processHandData(data.hands);
        }

        if (data.pose) {
            processedData.pose = this.processPoseData(data.pose);
        }

        // Notify processors with processed data
        if (processor.onProcessedData) {
            processor.onProcessedData(processedData);
        }

        return processedData;
    }

    processLandmarks(landmarks, keyIndices = null) {
        if (!Array.isArray(landmarks) || !landmarks[0]) return null;

        const landmarkData = landmarks[0];
        const significantChanges = new Map();
        let changeCount = 0;

        // If no specific indices provided, process all landmarks
        const indicesToProcess = keyIndices || [...Array(landmarkData.length).keys()];

        indicesToProcess.forEach(idx => {
            const landmark = landmarkData[idx];
            if (!landmark || landmark.visibility < 0.5) return;

            const now = performance.now();
            const filtered = {
                x: this.filters?.x.get(idx)?.filter(landmark.x, now) || landmark.x,
                y: this.filters?.y.get(idx)?.filter(landmark.y, now) || landmark.y,
                z: this.filters?.z.get(idx)?.filter(landmark.z || 0, now) || landmark.z,
                visibility: landmark.visibility
            };

            const prevLandmark = this.resultBuffer.pose.get(idx);
            if (!prevLandmark || this.hasSignificantChange(filtered, prevLandmark)) {
                significantChanges.set(idx, filtered);
                this.resultBuffer.pose.set(idx, { ...filtered });
                changeCount++;
            }
        });

        return changeCount > 0 ? significantChanges : null;
    }

    hasSignificantChange(current, previous) {
        if (!previous) return true;

        return Math.abs(current.x - previous.x) > this.thresholds.position ||
            Math.abs(current.y - previous.y) > this.thresholds.position ||
            Math.abs(current.z - previous.z) > this.thresholds.position ||
            Math.abs((current.confidence || 1) - (previous.confidence || 1)) > this.thresholds.confidence;
    }

    reset() {
        this.queue = null;
        this.isProcessing = false;
        this.resultBuffer.pose.clear();
        this.resultBuffer.hands.left.clear();
        this.resultBuffer.hands.right.clear();
        this.lastUpdate = 0;
        this.lastBatchTime = 0;
    }

    // Add pool management methods
    getMeshFromPool(type, createFn) {
        const pool = this.meshPools[type];
        let mesh = pool.find(m => !m.isActive);

        if (!mesh) {
            mesh = createFn();
            mesh.isActive = false;
            pool.push(mesh);
        }

        mesh.isActive = true;
        return mesh;
    }

    releaseMeshToPool(mesh) {
        if (mesh) {
            mesh.isActive = false;
        }
    }

    // Add batch management methods
    addToBatch(type, data) {
        switch (type) {
            case 'landmarks':
                this.visualizationBatches.landmarks.push(data);
                break;
            case 'handPoses':
                ['left', 'right'].forEach(side => {
                    if (data[side]?.landmarks) {
                        if (!this.visualizationBatches.handPoses[side]) {
                            this.visualizationBatches.handPoses[side] = [];
                        }
                        this.visualizationBatches.handPoses[side].push(data[side].landmarks);
                    }
                });
                break;
            case 'ikChain':
                if (!data || !data.chainName || !data.points) return;

                if (!this.visualizationBatches.ikChains.has(data.chainName)) {
                    this.visualizationBatches.ikChains.set(data.chainName, []);
                }
                this.visualizationBatches.ikChains.get(data.chainName).push(data.points);
                break;
        }
    }

    getBatchedResults() {
        return {
            landmarks: this.averageDataPoints(this.visualizationBatches.landmarks),
            handPoses: {
                left: this.averageDataPoints(this.visualizationBatches.handPoses.left),
                right: this.averageDataPoints(this.visualizationBatches.handPoses.right)
            },
            ikChains: this.averageIKChains(this.visualizationBatches.ikChains)
        };
    }

    averageIKChains(chainMap) {
        const result = new Map();

        chainMap.forEach((points, chainName) => {
            if (!points || points.length === 0) return;

            // Each point is an array of Vector3-like objects
            const avgPoints = points[0].map((_, jointIndex) => {
                const sum = { x: 0, y: 0, z: 0, count: 0 };

                points.forEach(chain => {
                    const point = chain[jointIndex];
                    if (point && typeof point.x === 'number') {
                        sum.x += point.x;
                        sum.y += point.y;
                        sum.z += point.z;
                        sum.count++;
                    }
                });

                return sum.count > 0 ? {
                    x: sum.x / sum.count,
                    y: sum.y / sum.count,
                    z: sum.z / sum.count
                } : null;
            }).filter(Boolean); // Remove any null points

            if (avgPoints.length > 0) {
                result.set(chainName, avgPoints);
            }
        });

        return result;
    }

    clearBatches() {
        this.visualizationBatches = {
            landmarks: [],
            handPoses: { left: [], right: [] },
            ikChains: new Map(),
            timestamp: performance.now()
        };
    }
}