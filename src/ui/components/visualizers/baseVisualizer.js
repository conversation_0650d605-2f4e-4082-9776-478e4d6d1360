export class BaseVisualizer {
    // MediaPipe pose connections as pairs of landmark indices
    static POSE_CONNECTIONS = [
        // Upper body
        [11, 12], // Shoulders
        [11, 13], [13, 15], // Left arm
        [12, 14], [14, 16], // Right arm
        [11, 23], [12, 24], // Torso

        // Lower body
        [23, 24], // Hips
        [23, 25], [25, 27], [27, 29], [29, 31], // Left leg
        [24, 26], [26, 28], [28, 30], [30, 32], // Right leg

        // Head and neck
        [11, 0], [12, 0], // Neck to nose
        [0, 9], [0, 10], // Eyes
        [9, 10], // Eye connection
    ];

    static HAND_CONNECTIONS = [
        [0, 1], [1, 2], [2, 3], [3, 4],           // thumb
        [0, 5], [5, 6], [6, 7], [7, 8],           // index
        [0, 9], [9, 10], [10, 11], [11, 12],      // middle
        [0, 13], [13, 14], [14, 15], [15, 16],    // ring
        [0, 17], [17, 18], [18, 19], [19, 20],    // pinky
        [0, 17], [0, 13], [0, 9], [0, 5]          // palm
    ];

    constructor(debug = false) {
        this.debug = debug;
        this.enabled = debug;
        this.initialized = false;
        this.performanceConfig = {
            enabled: debug,
            updateInterval: 100,      // 100ms between updates
            drawInterval: 1000 / 30,  // 30fps max
            frameThrottle: 5,         // Process every 5th frame
            lastUpdateTime: 0,
            frameCount: 0,
            cache: new Map()
        };
    }

    enable() {
        this.enabled = true;
        this.debug = true;
    }

    disable() {
        this.enabled = false;
        this.debug = false;
    }

    shouldUpdate() {
        const now = performance.now();
        this.performanceConfig.frameCount++;

        // Skip if within throttle interval
        if (now - this.performanceConfig.lastUpdateTime < this.performanceConfig.updateInterval) {
            return false;
        }

        // Skip if not on target frame
        if (this.performanceConfig.frameCount % this.performanceConfig.frameThrottle !== 0) {
            return false;
        }

        this.performanceConfig.lastUpdateTime = now;
        return true;
    }

    clearCache() {
        this.performanceConfig.cache.clear();
    }
}
