import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ector3, <PERSON><PERSON>ing<PERSON>n<PERSON>, BoneIKController, BoundingSphere } from '@babylonjs/core';
import { BaseVisualizer } from './baseVisualizer.js';
import { PerformanceSettings } from '../../core/performanceSettings.js';
import BoneMapper from '../../characters/boneMapper.js';
import { MEDIAPIPE_LANDMARKS } from '../../configs/mediapipeLandmarks.js';
// import { VisualAssetFactory } from './services/visualAssetFactory.js';
import { BatchProcessor } from './services/batchProcessor.js';

export class Scene3DVisualizer extends BaseVisualizer {
    constructor(scene, debug = false) {
        super(debug);
        this.scene = scene;

        // Initialize state variables first
        this.initialized = false;
        this.initializationPromise = null;
        this.ikPoints = [];  // Move initialization here
        this.ikLines = [];   // Move initialization here
        this.debugMeshes = new Map();
        this.scaleFactor = { x: 1, y: 1, z: 1 };
        this.debug = debug;
        this.enabled = debug;
        this.currentLandmarks = null;

        // Add object pools
        this.meshPools = {
            spheres: [],
            lines: []
        };

        // Add batch processor
        this.visualizationBatch = {
            ikChains: new Map(),
            handPoses: new Map(),
            landmarks: [],
            timestamp: performance.now(),
            dirty: false
        };

        // Add frustum optimization
        this.lastCameraPosition = null;
        this.visibilityCache = new Map();

        // Initialize with SceneManager's metrics if available
        this.characterMetrics = scene.sceneManager?.getCharacterMetrics();
        if (this.characterMetrics?.bounds) {
            console.log('[Scene3DVisualizer] Initialized with existing metrics');
            this.initialized = true;
        }

        // Initialize services after state variables
        // this.assetFactory = new VisualAssetFactory(scene);
        this.batchProcessor = new BatchProcessor(PerformanceSettings);

        // Setup other components
        this.setupScene();
        // this.initializeMaterials();

        // Log initialization state
        console.log('[Scene3DVisualizer] Construction complete:', {
            initialized: this.initialized,
            hasMetrics: !!this.characterMetrics?.bounds,
            ikPointsLength: this.ikPoints.length,
            debugMeshesSize: this.debugMeshes.size
        });
    }

    setupScene() {
        // Basic scene setup
        if (!this.scene.sceneManager) {
            console.warn('[Scene3DVisualizer] No SceneManager available');
        }

        this.worldBounds = this.scene.sceneManager?.getVisualizationBounds() || {
            x: { min: -2, max: 2 },
            y: { min: 0, max: 2 },
            z: { min: -1, max: 1 }
        };
    }

    async waitForCharacterLoading() {
        return new Promise(resolve => {
            const checkCharacter = () => {
                const player = this.scene.getMeshByName('character_player_root');
                if (player) {
                    this.initializeWithCharacter(player);
                    resolve();
                } else {
                    setTimeout(checkCharacter, 100);
                }
            };
            checkCharacter();
        });
    }

    initializeWithCharacter() {
        const playerMesh = this.scene.getMeshByName('character_player_root');
        if (!playerMesh) {
            console.warn('[Scene3DVisualizer] Player character mesh not found');
            return;
        }

        this.update3DVisualizationScale(playerMesh);
        // ...rest of existing code...
    }

    _warmupCoordinateService() {
        // 确保角色加载完成后进行预热
        const player = this.scene.characterManager?.getCharacter('player');
        if (player) {
            const metrics = this.scene.sceneManager?.getCharacterMetrics();
            if (metrics) {
                // 进行一次测试转换以触发任何潜在问题
                try {
                    const testPoint = { x: 0, y: 1, z: 0 };
                    this.scene.sceneManager.transformWorldToVisual(
                        testPoint,
                        metrics,
                        this.scaleFactor
                    );
                } catch (error) {
                    console.warn('[Scene3DVisualizer] Coordinate service warmup failed:', error);
                }
            }
        }
    }

    // initializeMaterials() {
    //     // Delegate to asset factory
    //     this.assetFactory.initializeMaterials();
    // }

    update3DVisualizationScale(character) {
        if (!character) {
            console.warn('[Scene3DVisualizer] No character provided for scale update');
            return;
        }

        // Get and validate metrics
        const metrics = this.scene.sceneManager?.getCharacterMetrics();
        if (!metrics?.bounds) {
            console.warn('[Scene3DVisualizer] Cannot update visualization scale: Invalid metrics');
            return;
        }

        // Update internal state
        this.characterMetrics = metrics;
        this.worldBounds = {
            x: { min: metrics.bounds.min.x, max: metrics.bounds.max.x },
            y: { min: metrics.bounds.min.y, max: metrics.bounds.max.y },
            z: { min: metrics.bounds.min.z, max: metrics.bounds.max.z }
        };

        // Calculate scale factor
        const dims = metrics.dimensions;
        const minDimension = 0.1;
        const safeDims = {
            width: Math.max(dims.width || minDimension, minDimension),
            height: Math.max(dims.height || minDimension, minDimension),
            depth: Math.max(dims.depth || minDimension, minDimension)
        };

        const scaleMultiplier = safeDims.height < 1.7 ? 1.5 :
            safeDims.height > 2.0 ? 0.8 : 1.0;

        this.scaleFactor = {
            x: safeDims.width * scaleMultiplier,
            y: safeDims.height * scaleMultiplier,
            z: safeDims.depth * scaleMultiplier
        };

        // Only update visualization if we have active elements
        // if (Array.isArray(this.ikPoints) && this.ikPoints.length > 0 || 
        //     Array.isArray(this.ikLines) && this.ikLines.length > 0) {
        //     this.updateVisualization();
        // }

        this.initialized = true;
    }

    // initializeAfterSceneReady() {
    //     const characterManager = this.scene.characterManager;
    //     if (!characterManager) {
    //         console.warn('[Scene3DVisualizer] No CharacterManager available');
    //         return;
    //     }

    //     const playerCharacter = characterManager.getCharacter('player');
    //     if (!playerCharacter) {
    //         console.warn('[Scene3DVisualizer] No player character found');
    //         return;
    //     }

    //     const boundingInfo = playerCharacter.getBoundingInfo();
    //     if (!boundingInfo) {
    //         console.warn('[Scene3DVisualizer] No bounding info for player character');
    //         return;
    //     }

    //     const min = boundingInfo.boundingBox.minimumWorld;
    //     const max = boundingInfo.boundingBox.maximumWorld;

    //     this.worldBounds = {
    //         x: { min: min.x, max: max.x },
    //         y: { min: min.y, max: max.y },
    //         z: { min: min.z, max: max.z }
    //     };

    //     const ranges = {
    //         x: this.worldBounds.x.max - this.worldBounds.x.min,
    //         y: this.worldBounds.y.max - this.worldBounds.y.min,
    //         z: this.worldBounds.z.max - this.worldBounds.z.min
    //     };

    //     this.scaleFactor = {
    //         x: ranges.x / 2,
    //         y: ranges.y,
    //         z: ranges.z
    //     };

    //     // 预热坐标服务
    //     this._warmupCoordinateService();
    // }

    updateFromResults(results) {
        // if (!this.enabled || !this.initialized) {
        //     console.debug('[Scene3DVisualizer] Not ready:', {
        //         enabled: this.enabled,
        //         initialized: this.initialized
        //     });
        //     return;
        // }

        // try {
        //     const now = performance.now();
        //     if (now - this.visualizationBatch.timestamp < this.batchProcessor.updateInterval) {
        //         this.addToBatch(results);
        //         return;
        //     }

        //     // Process current batch and add new data
        //     this.processBatch();
        //     this.addToBatch(results);

        // } catch (error) {
        //     console.error('[Scene3DVisualizer] Update error:', error);
        // }
    }

    addToBatch(results) {
        // Add validation
        if (!results) return;
        if (results.landmarks?.[0]) {
            const processedResults = this.processLandmarksForIK(results.landmarks[0]);
            if (processedResults) {
                Object.entries(processedResults).forEach(([chain, points]) => {
                    if (!this.visualizationBatch.ikChains.has(chain)) {
                        this.visualizationBatch.ikChains.set(chain, []);
                    }
                    this.visualizationBatch.ikChains.get(chain).push(points);
                });
            }
        }

        this.visualizationBatch.dirty = true;
    }

    processBatch() {
        if (!this.visualizationBatch.dirty) return;

        try {
            const camera = this.scene.activeCamera;

            this.visualizationBatch.ikChains.forEach((pointsBatch, chainName) => {
                if (!pointsBatch?.length) return;

                try {
                    const averagePoints = this.averagePoints(pointsBatch);
                    if (!averagePoints?.length >= 2) return;

                    // Calculate chain center and radius
                    const center = averagePoints.reduce((acc, pt) =>
                        acc.add(pt), Vector3.Zero()
                    ).scale(1 / averagePoints.length);

                    const radius = averagePoints.reduce((max, pt) =>
                        Math.max(max, Vector3.Distance(center, pt)), 0
                    ) * 1.1; // Add 10% margin

                    // Create a temporary sphere for frustum checking
                    const tempSphere = MeshBuilder.CreateSphere(
                        `temp_${chainName}`,
                        {
                            diameter: radius * 2,
                            segments: 4
                        },
                        this.scene
                    );
                    tempSphere.position = center;
                    tempSphere.isVisible = false;

                    // Check visibility using the temporary sphere
                    // const isVisible = !camera || camera.isInFrustum(tempSphere);

                    // Clean up
                    // tempSphere.dispose();

                    // if (isVisible) {
                    //     this.visualizeIKChain(chainName, averagePoints);
                    // }

                } catch (error) {
                    console.debug(`[Scene3DVisualizer] Skipping chain ${chainName} due to error:`, error);
                }
            });

            this.resetBatch();
            this.updateMeshVisibility();

        } catch (error) {
            console.error('[Scene3DVisualizer] Batch processing error:', error);
            this.resetBatch();
        }
    }

    resetBatch() {
        // Clear current batch data
        this.visualizationBatch = {
            ikChains: new Map(),
            handPoses: new Map(),
            landmarks: [],
            timestamp: performance.now(),
            dirty: false
        };
    }

    processLandmarksForIK(landmarks) {
        if (!this.initialized || !Array.isArray(landmarks)) {
            return null;
        }

        const landmarkData = Array.isArray(landmarks[0]) ? landmarks[0] : landmarks;
        if (!Array.isArray(landmarkData)) {
            console.warn('[Scene3DVisualizer] Invalid landmark data format');
            return null;
        }

        this.currentLandmarks = landmarkData;
        const ikResults = {};
        const sceneManager = this.scene.sceneManager;
        const player = this.scene.characterManager?.getCharacter('player');
        const camera = this.scene.activeCamera;

        // Process pose landmarks with visibility check
        Object.entries(BoneMapper.CHAIN_DEFINITIONS).forEach(([chainName, config]) => {
            const points = config.landmarks
                .map(idx => {
                    const lm = landmarkData[idx];
                    if (!lm || typeof lm.visibility !== 'number' || lm.visibility < 0.5) {
                        return null;
                    }
                    const scaledPoint = sceneManager.scaleLandmark(lm, player);
                    if (!scaledPoint) return null;

                    const position = new Vector3(scaledPoint.x, scaledPoint.y, scaledPoint.z);
                    return this.isPointInView(position, camera) ? scaledPoint : null;
                })
                .filter(Boolean);

            if (points.length >= 2) {
                ikResults[chainName] = points;
            } else {
                console.debug(`[Scene3DVisualizer] Chain ${chainName} has insufficient visible points`);
            }
        });

        // Process hand landmarks if available
        if (landmarks.handPoses) {
            ['left', 'right'].forEach(side => {
                const handData = landmarks.handPoses[side];
                if (!handData?.landmarks?.length) return;

                const handPoints = handData.landmarks
                    .map(lm => sceneManager.scaleLandmark(lm, player))
                    .filter(Boolean);

                if (handPoints.length > 0) {
                    ikResults[`${side}Hand`] = handPoints;
                }
            });
        }

        return Object.keys(ikResults).length > 0 ? ikResults : null;
    }

    processHandLandmarks(handPoses) {
        if (!handPoses) return null;

        const results = { left: null, right: null };

        ['left', 'right'].forEach(side => {
            const handData = handPoses[side];
            if (!handData?.fingerData?.joints) return;

            const processedFingers = {};
            Object.entries(handData.fingerData.joints).forEach(([finger, joints]) => {
                const points = joints
                    .map(joint => joint.position && this.scaleLandmark({
                        x: joint.position.x,
                        y: joint.position.y,
                        z: joint.position.z,
                        visibility: 1
                    }))
                    .filter(Boolean);

                if (points.length > 0) {
                    processedFingers[finger] = points;
                }
            });

            if (Object.keys(processedFingers).length > 0) {
                results[side] = {
                    fingerData: {
                        joints: processedFingers,
                        connections: handData.fingerData.connections
                    }
                };
            }
        });

        return results.left || results.right ? results : null;
    }

    averagePoints(pointsBatch) {
        if (!Array.isArray(pointsBatch) || pointsBatch.length === 0) {
            console.debug('[Scene3DVisualizer] Empty or invalid pointsBatch');
            return [];
        }

        try {
            // Filter and validate batches first
            const validBatches = pointsBatch.filter(batch =>
                Array.isArray(batch) &&
                batch.length > 0
            );

            if (validBatches.length === 0) {
                console.debug('[Scene3DVisualizer] No valid batches found');
                return [];
            }

            // Get the length from the first valid batch
            const pointCount = validBatches[0].length;

            // Initialize sums array
            const sums = new Array(pointCount).fill(null).map(() => ({
                x: 0, y: 0, z: 0, count: 0
            }));

            // Process each batch
            validBatches.forEach(batch => {
                // Ensure batch length matches expected pointCount
                if (batch.length !== pointCount) {
                    console.debug('[Scene3DVisualizer] Skipping batch with mismatched length');
                    return;
                }

                batch.forEach((point, index) => {
                    // Validate point has required properties
                    if (point &&
                        typeof point.x === 'number' &&
                        typeof point.y === 'number' &&
                        typeof point.z === 'number' &&
                        !isNaN(point.x) && !isNaN(point.y) && !isNaN(point.z)) {

                        sums[index].x += point.x;
                        sums[index].y += point.y;
                        sums[index].z += point.z;
                        sums[index].count++;
                    } else {
                        console.debug('[Scene3DVisualizer] Skipping invalid point:', point);
                    }
                });
            });

            // Convert sums to Vector3 points, filtering out any points with no valid samples
            const result = sums
                .filter(sum => sum.count > 0)
                .map(sum => new Vector3(
                    sum.x / sum.count,
                    sum.y / sum.count,
                    sum.z / sum.count
                ));

            if (result.length === 0) {
                console.debug('[Scene3DVisualizer] No valid points after averaging');
            }

            return result;

        } catch (error) {
            console.warn('[Scene3DVisualizer] Error averaging points:', error);
            return [];
        }
    }

    processPoseLandmarks(landmarks) {
        return this.batchProcessor.processLandmarks(
            [landmarks],
            Object.values(MEDIAPIPE_LANDMARKS)
        );
    }

    // updateVisualization() {
    //     if (this.batchProcessor.queue) {
    //         const queue = this.batchProcessor.queue;
    //         if (queue.type === 'combined') {
    //             if (queue.data.ik) {
    //                 this.visualizeIK(queue.data.ik);
    //             }
    //             if (queue.data.hands) {
    //                 this.visualizeHands(queue.data.hands);
    //             }
    //         }
    //     }
    // }

    // visualizeIK(ikResults) {
    //     if (!this.enabled || !ikResults || !this.initialized) {
    //         return;
    //     }

    //     try {
    //         this.clearIKVisualizations();

    //         Object.entries(ikResults).forEach(([chainName, joints]) => {
    //             if (!joints || !Array.isArray(joints) || joints.length < 2) return;

    //             const positions = [];
    //             joints.forEach((joint, idx) => {
    //                 if (!joint) return;

    //                 try {
    //                     const visualPos = this.scene.sceneManager.transformWorldToVisual(
    //                         joint,
    //                         this.characterMetrics,
    //                         this.scaleFactor
    //                     );

    //                     const sphereKey = `${chainName}_${idx}`;
    //                     const sphere = this.getMeshFromPool('spheres', () => this.assetFactory.createVisualizationSphere(sphereKey));

    //                     if (sphere) {
    //                         sphere.position = new Vector3(visualPos.x, visualPos.y, visualPos.z);
    //                         sphere.isVisible = true;
    //                         positions.push(sphere.position);
    //                         this.ikPoints.push(sphere);
    //                     }
    //                 } catch (error) {
    //                     console.warn(`[Scene3DVisualizer] Failed to visualize joint ${idx} in chain ${chainName}:`, error);
    //                 }
    //             });

    //             if (positions.length >= 2) {
    //                 const lineMesh = this.getMeshFromPool('lines', () => this.assetFactory.createDebugLine(chainName, positions));
    //                 if (lineMesh) {
    //                     this.ikLines.push(lineMesh);
    //                 }
    //             }
    //         });
    //     } catch (error) {
    //         console.error('[Scene3DVisualizer] Error in visualizeIK:', error);
    //     }
    // }

    // visualizeHands(handPoses) {
    //     if (!this.enabled || !handPoses) return;

    //     ['left', 'right'].forEach(side => {
    //         const handData = handPoses[side];
    //         if (!handData?.fingerData?.joints) return;

    //         Object.entries(handData.fingerData.joints).forEach(([finger, joints]) => {
    //             const chainName = `${side}Hand_${finger}`;
    //             const points = joints.map(j => j.position);

    //             if (handData.worldPositions?.[0]) {
    //                 points.unshift(handData.worldPositions[0]);
    //             }

    //             // this.updateDebugLine(chainName, points);
    //         });
    //     });
    // }

    // updateDebugLine(chainName, points) {
    //     if (!points || points.length < 2) return;

    //     const safeChainName = String(chainName || '').trim() || 'unnamed';
    //     const lineMesh = this.getMeshFromPool('lines', () => 
    //         this.assetFactory.createDebugLine(safeChainName, points)
    //     );

    //     if (lineMesh) {
    //         this.debugMeshes.set(safeChainName, lineMesh);
    //     }
    // }

    visualizeIKChain(chainName, points) {
        if (!this.enabled || !points || points.length < 2) return;

        try {
            const positions = [];
            const safeChainName = String(chainName || '').trim() || 'unnamed';

            points.forEach((joint, idx) => {
                if (!joint) return;

                try {
                    const visualPos = this.scene.sceneManager.transformWorldToVisual(
                        joint,
                        this.characterMetrics,
                        this.scaleFactor
                    );

                    const sphereKey = `${safeChainName}_${idx}`;
                    const sphere = this.getMeshFromPool('spheres', () =>
                        this.assetFactory.createVisualizationSphere(sphereKey)
                    );

                    if (sphere) {
                        sphere.position = new Vector3(visualPos.x, visualPos.y, visualPos.z);
                        sphere.isVisible = true;
                        positions.push(sphere.position);
                        this.ikPoints.push(sphere);
                    }
                } catch (error) {
                    console.warn(`[Scene3DVisualizer] Failed to visualize joint ${idx} in chain ${safeChainName}:`, error);
                }
            });

            if (positions.length >= 2) {
                const lineMesh = this.getMeshFromPool('lines', () =>
                    this.assetFactory.createDebugLine(safeChainName, positions)
                );
                if (lineMesh) {
                    this.ikLines.push(lineMesh);
                }
            }
        } catch (error) {
            console.error('[Scene3DVisualizer] Error in visualizeIKChain:', error);
        }
    }

    enable() {
        super.enable();
        this.showVisualElements(true);
    }

    disable() {
        super.disable();
        this.showVisualElements(false);
    }

    showVisualElements(show) {
        this.ikPoints.forEach(element => {
            if (element.setEnabled) {
                element.setEnabled(show);
            } else if (element.isVisible !== undefined) {
                element.isVisible = show;
            }
        });

        this.ikLines.forEach(mesh => {
            if (mesh) mesh.isVisible = show;
        });

        this.debugMeshes.forEach(mesh => {
            if (mesh) mesh.isVisible = show;
        });
    }

    clearIKVisualizations() {
        try {
            if (Array.isArray(this.ikPoints)) {
                this.ikPoints.forEach(mesh => mesh.isActive = false);
                this.ikPoints = [];
            } else {
                this.ikPoints = [];
            }

            if (Array.isArray(this.ikLines)) {
                this.ikLines.forEach(line => line.isActive = false);
                this.ikLines = [];
            } else {
                this.ikLines = [];
            }
        } catch (error) {
            console.error('[Scene3DVisualizer] Error clearing IK visualizations:', error);
            // Reset arrays even if disposal fails
            this.ikPoints = [];
            this.ikLines = [];
        }
    }

    getMeshFromPool(type, createFn) {
        const pool = this.meshPools[type];
        if (!pool) {
            console.warn('[Scene3DVisualizer] No pool found for type:', type);
            return null;
        }

        let mesh = pool.find(m => !m.isActive);
        if (!mesh) {
            try {
                mesh = createFn();
                if (mesh) {
                    mesh.isActive = false;
                    pool.push(mesh);
                }
            } catch (error) {
                console.error('[Scene3DVisualizer] Error creating mesh:', error);
                return null;
            }
        }

        if (mesh) {
            mesh.isActive = true;
        }
        return mesh;
    }

    dispose() {
        // Dispose all pooled meshes
        Object.values(this.meshPools).forEach(pool => {
            pool.forEach(mesh => mesh.dispose());
            pool.length = 0;
        });

        this.clearIKVisualizations();
        this.assetFactory.dispose();
        this.debugMeshes.forEach(mesh => mesh?.dispose());
        this.debugMeshes.clear();
    }

    updateMeshVisibility() {
        if (!this.scene?.activeCamera) return;

        const camera = this.scene.activeCamera;
        const cameraPosition = camera.position;

        // Only update if camera has moved significantly
        if (this.lastCameraPosition &&
            Vector3.Distance(this.lastCameraPosition, cameraPosition) < 0.1) {
            return;
        }

        this.lastCameraPosition = cameraPosition.clone();

        // Update visibility for all active meshes
        const updateMeshes = (meshes) => {
            meshes.filter(mesh => mesh?.isActive)
                .forEach(mesh => {
                    if (mesh) {
                        const shouldBeVisible = camera.isInFrustum(mesh);
                        if (mesh.isVisible !== shouldBeVisible) {
                            mesh.isVisible = shouldBeVisible;
                        }
                    }
                });
        };

        // Update IK points
        if (this.ikPoints?.length) {
            updateMeshes(this.ikPoints);
        }

        // Update IK lines
        if (this.ikLines?.length) {
            updateMeshes(this.ikLines);
        }

        // Update debug meshes
        if (this.debugMeshes?.size) {
            updateMeshes([...this.debugMeshes.values()]);
        }
    }
    /**
     * Creates a bounding sphere from the given min and max vectors.
     * @param {Vector3} min - The minimum vector of the bounding box.
     * @param {Vector3} max - The maximum vector of the bounding box.
     * @returns {BoundingSphere} - A new bounding sphere.
     */
    createBoundingSphereFromMinMax(min, max) {
        // Ensure the inputs are proper Vector3 instances.
        const safeMin = (min instanceof Vector3) ? min.clone() : new Vector3(min.x, min.y, min.z);
        const safeMax = (max instanceof Vector3) ? max.clone() : new Vector3(max.x, max.y, max.z);

        // Compute the center as the midpoint between safeMin and safeMax.
        const center = new Vector3(
            (safeMin.x + safeMax.x) / 2,
            (safeMin.y + safeMax.y) / 2,
            (safeMin.z + safeMax.z) / 2
        );

        // Compute the radius as half the distance between safeMin and safeMax.
        const radius = Vector3.Distance(safeMin, safeMax) / 2;

        // Return an object with the minimal properties expected by isInFrustum.
        return {
            center,           // The center of the sphere.
            radius,           // The computed radius.
            centerWorld: center, // Some frustum checks may use centerWorld.
            // Dummy method so that any internal call to reConstruct doesn't crash.
            reConstruct: () => { }
        };
    }
    /**
     * Creates bounding objects (a BoundingInfo and BoundingSphere) from a center point and a radius.
     * @param {Vector3} center - The center point for the bounding objects.
     * @param {number} radius - The radius to use for the bounding sphere.
     * @returns {{boundingInfo: BoundingInfo}} - An object containing the BoundingInfo.
     */
    createBoundingObjects(center, radius) {
        // Ensure the center is a proper Vector3.
        const safeCenter = center instanceof Vector3 ? center.clone() : new Vector3(center.x, center.y, center.z);

        // Create min and max bounds.
        const min = new Vector3(safeCenter.x - radius, safeCenter.y - radius, safeCenter.z - radius);
        const max = new Vector3(safeCenter.x + radius, safeCenter.y + radius, safeCenter.z + radius);

        // Here, we assume you have your own BoundingInfo-like object.
        // Instead of calling new BoundingSphere(safeCenter, radius),
        // we use our custom function.
        const customBoundingSphere = this.createBoundingSphereFromMinMax(min, max);

        // If you have an existing BoundingInfo instance (from legacy code), you can set its boundingSphere:
        // (This example assumes you’re not using the full Babylon BoundingInfo constructor)
        const boundingInfo = {
            boundingBox: { minimumWorld: min, maximumWorld: max },
            boundingSphere: customBoundingSphere
        };

        return { boundingInfo };
    }
    /**
     * Computes the eight corners of an axis-aligned bounding box defined by minimum and maximum vectors.
     * @param {Object} bbox - An object with properties `minimumWorld` and `maximumWorld` (both Vector3).
     * @returns {Vector3[]} An array of 8 Vector3 objects representing the corners.
     */
    getCornersFromBoundingBox(bbox) {
        const min = bbox.minimumWorld;
        const max = bbox.maximumWorld;
        return [
            new Vector3(min.x, min.y, min.z),
            new Vector3(max.x, min.y, min.z),
            new Vector3(min.x, max.y, min.z),
            new Vector3(max.x, max.y, min.z),
            new Vector3(min.x, min.y, max.z),
            new Vector3(max.x, min.y, max.z),
            new Vector3(min.x, max.y, max.z),
            new Vector3(max.x, max.y, max.z)
        ];
    }
    /**
     * Checks if at least a given percentage (threshold) of the bounding box is inside the camera frustum.
     * @param {BABYLON.Camera} camera - The camera to test against.
     * @param {Object} boundingInfo - An object with a `boundingBox` property that contains at least `minimumWorld` and `maximumWorld`.
     * @param {number} threshold - A number between 0 and 1 representing the percentage of corners that must be inside.
     * @returns {boolean} True if the fraction of corners inside is at least the threshold.
     */
    isMostlyInFrustum(camera, boundingInfo, threshold = 0.5) {
        if (!camera || !boundingInfo) return true;

        try {
            // Babylon.js cameras have isInFrustum method that works with meshes and bounding info
            const dummyMesh = {
                getBoundingInfo: () => boundingInfo,
                absolutePosition: boundingInfo.boundingBox.centerWorld || Vector3.Zero()
            };
            return camera.isInFrustum(dummyMesh);
        } catch (error) {
            console.warn('[Scene3DVisualizer] Frustum check error:', error);
            return true; // Default to visible on error
        }
    }

    isPointInView(point, camera) {
        if (!camera || !point) return true;

        try {
            const pointVector = point instanceof Vector3 ? point : new Vector3(point.x, point.y, point.z);

            // Create a small invisible sphere at the point's position
            const sphereMesh = MeshBuilder.CreateSphere(
                "tempSphere",
                {
                    diameter: 0.1,
                    segments: 4 // Low poly since it's just for frustum checking
                },
                this.scene
            );

            // Position the sphere at the point location
            sphereMesh.position = pointVector;
            sphereMesh.isVisible = false;

            // Check if the sphere is in frustum
            const isVisible = camera.isInFrustum(sphereMesh);

            // Clean up the temporary mesh
            sphereMesh.dispose();

            return isVisible;
        } catch (error) {
            console.warn('[Scene3DVisualizer] Point visibility check failed:', error);
            return true; // Default to visible on error
        }
    }

    onProcessedData(processedData) {
        if (!this.enabled || !this.initialized) return;

        if (processedData.landmarks) {
            this.visualizeLandmarks(processedData.landmarks);
        }

        if (processedData.hands) {
            this.visualizeHands(processedData.hands);
        }
    }

    visualizeLandmarks(landmarks) {
        // Remove landmark processing logic and just handle visualization
        // Use already processed landmarks directly
        this.addToBatch({ type: 'landmarks', data: landmarks });
    }
}
