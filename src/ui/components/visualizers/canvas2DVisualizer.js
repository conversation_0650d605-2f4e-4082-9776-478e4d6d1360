import { BaseVisualizer } from './baseVisualizer.js';
import * as mpDraw from '@mediapipe/drawing_utils';
import BoneMapper from './boneMapper.js';

export class Canvas2DVisualizer extends BaseVisualizer {
    constructor(debug = false) {
        super(debug);
        this.canvas = null;
        this.ctx = null;
        this.enabled = debug;

        // Update drawing configurations with new color scheme
        this.drawingConfig = {
            landmarkConfig: {
                color: 'rgba(255, 138, 138, 0.8)',  // light red for pose landmarks
                lineWidth: 2,
                radius: 4
            },
            connectorConfig: {
                color: 'rgba(255, 255, 255, 0.8)',  // white for connections
                lineWidth: 2,
            },
            handConfig: {
                landmarks: {
                    left: {
                        color: 'rgba(255, 138, 138, 0.8)',  // light red for hand landmarks
                        lineWidth: 2,
                        radius: 3
                    },
                    right: {
                        color: 'rgba(255, 138, 138, 0.8)',  // light red for hand landmarks
                        lineWidth: 2,
                        radius: 3
                    }
                },
                connections: {
                    color: 'rgba(255, 255, 255, 0.8)',  // white for hand connections
                    lineWidth: 2,
                }
            }
        };

        this.readyState = {
            initialized: false,
            canvas: null,
            context: null
        };

        // Add debug visualization settings
        this.debugVisualization = {
            enabled: false,
            drawHandLandmarks: true,
            drawConnections: true,
            drawPoseLandmarks: false,  // Set to false by default
            colors: {
                landmarks: 'rgba(0, 255, 0, 0.8)',
                connections: 'rgba(255, 255, 255, 0.5)',
                leftHand: 'rgba(0, 255, 0, 0.8)',
                rightHand: 'rgba(0, 255, 0, 0.8)'  // Set same color for both hands
            }
        };
    }

    setCanvas(canvas) {
        if (!canvas || !(canvas instanceof HTMLCanvasElement)) {
            console.warn('[Canvas2DVisualizer] Invalid canvas provided');
            this.reset();
            return false;
        }

        try {
            const wasEnabled = this.enabled;
            const oldCanvas = this.canvas;

            // Keep track of old dimensions if they exist
            const oldDimensions = oldCanvas ? {
                width: oldCanvas.width,
                height: oldCanvas.height
            } : null;

            // Only reset if we have a different canvas
            if (oldCanvas && oldCanvas !== canvas) {
                this.reset();
            }

            // Ensure canvas has proper dimensions
            if (!canvas.width || !canvas.height) {
                canvas.width = canvas.clientWidth || 640;
                canvas.height = canvas.clientHeight || 480;
                console.log('[Canvas2DVisualizer] Set canvas dimensions:', {
                    width: canvas.width,
                    height: canvas.height
                });
            }

            this.canvas = canvas;
            // Create context once with all necessary options
            this.ctx = canvas.getContext('2d', {
                willReadFrequently: false,
                alpha: true,
                desynchronized: true, // Optimize for video streaming
                antialias: true // Add antialiasing for smoother lines
            });

            if (!this.ctx) {
                throw new Error('Failed to get 2D context');
            }

            // Store initial context state
            this.initialContextState = {
                globalAlpha: this.ctx.globalAlpha,
                globalCompositeOperation: this.ctx.globalCompositeOperation,
                imageSmoothingEnabled: this.ctx.imageSmoothingEnabled,
                imageSmoothingQuality: this.ctx.imageSmoothingQuality
            };

            // If we had previous dimensions, try to maintain them
            if (oldDimensions && oldDimensions.width > 0 && oldDimensions.height > 0) {
                this.canvas.width = oldDimensions.width;
                this.canvas.height = oldDimensions.height;
            }

            // Restore enabled state if it was enabled before
            if (wasEnabled) {
                this.enabled = true;
                this.canvas.style.display = 'block';
            } else {
                this.canvas.style.display = 'none';
            }

            // Add resize observer to handle canvas resizing
            this.setupResizeObserver(canvas);

            return new Promise((resolve) => {
                const checkCanvas = () => {
                    if (canvas.width > 0 && canvas.height > 0) {
                        this.readyState = {
                            initialized: true,
                            canvas: canvas,
                            context: this.ctx,
                            dimensions: {
                                width: canvas.width,
                                height: canvas.height
                            }
                        };
                        this.canvasReady = true;

                        // Re-enable if it was enabled before
                        if (wasEnabled) {
                            this.enable();
                        }

                        console.log('[Canvas2DVisualizer] Canvas ready:', {
                            width: canvas.width,
                            height: canvas.height,
                            hasContext: !!this.ctx,
                            enabled: this.enabled,
                            display: this.canvas.style.display,
                            wasEnabled
                        });
                        resolve(true);
                    } else {
                        requestAnimationFrame(checkCanvas);
                    }
                };
                checkCanvas();
            });
        } catch (error) {
            console.error('[Canvas2DVisualizer] Canvas setup error:', error);
            this.reset();
            return Promise.reject(error);
        }
    }

    // Add method to handle canvas resizing
    setupResizeObserver(canvas) {
        // Clean up previous observer if exists
        if (this.resizeObserver) {
            this.resizeObserver.disconnect();
            this.resizeObserver = null;
        }

        // Create new observer
        this.resizeObserver = new ResizeObserver(entries => {
            for (const entry of entries) {
                const { width, height } = entry.contentRect;

                // Only update if dimensions actually changed
                if (this.canvas.width !== width || this.canvas.height !== height) {
                    console.log('[Canvas2DVisualizer] Canvas resized:', { width, height });

                    // Update canvas dimensions
                    this.canvas.width = width || canvas.clientWidth;
                    this.canvas.height = height || canvas.clientHeight;

                    // Update readyState dimensions
                    if (this.readyState) {
                        this.readyState.dimensions = {
                            width: this.canvas.width,
                            height: this.canvas.height
                        };
                    }
                }
            }
        });

        // Start observing
        this.resizeObserver.observe(canvas);
    }

    enable() {
        if (this.enabled && this.canvasReady) {
            console.log('[Canvas2DVisualizer] Already enabled and ready');
            return;
        }

        console.log('[Canvas2DVisualizer] Enabling visualizer');
        this.enabled = true;
        this.debug = true;

        if (this.canvas) {
            // Don't recreate context, just ensure it's properly configured
            if (this.ctx && this.initialContextState) {
                Object.assign(this.ctx, this.initialContextState);
            }

            this.canvasReady = true;
            this.canvas.style.display = 'block';

            // Update readyState without changing context
            this.readyState.initialized = true;
            this.readyState.canvas = this.canvas;
            this.readyState.context = this.ctx;
        }

        console.log('[Canvas2DVisualizer] Enabled state:', {
            enabled: this.enabled,
            canvasReady: this.canvasReady,
            hasContext: !!this.ctx,
            display: this.canvas?.style.display
        });
    }

    disable() {
        console.log('[Canvas2DVisualizer] Disabling visualizer');
        this.enabled = false;

        if (this.canvas) {
            this.canvas.style.display = 'none';
            if (this.ctx) {
                this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
            }
        }

        // Clear pending draws
        this.drawQueue = [];
        this.isProcessing = false;
        this.canvasReady = false;

        console.log('[Canvas2DVisualizer] Disabled and cleared state');
    }

    isReady() {
        // console.log('[Canvas2DVisualizer] Checking readiness:', {
        //     enabled: this.enabled,
        //     ctx: this.ctx,
        //     canvas: this.canvas,
        //     readyState: this.readyState.initialized
        // });
        return this.enabled && this.ctx && this.canvas && this.readyState.initialized;
    }

    drawResults(results) {
        if (!results || !this.isReady() || !this.enabled || !this.canvasReady) {
            return;
        }

        try {
            this.ctx.save();
            this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
            this.ctx.globalCompositeOperation = 'source-over';

            // Draw pose landmarks and connections first
            if (results.landmarks?.[0]) {
                mpDraw.drawConnectors(
                    this.ctx,
                    results.landmarks[0],
                    BoneMapper.POSE_CONNECTIONS,
                    this.drawingConfig.connectorConfig
                );

                mpDraw.drawLandmarks(
                    this.ctx,
                    results.landmarks[0],
                    this.drawingConfig.landmarkConfig
                );
            }
            this.drawLandmarkLabels(results.landmarks?.[0] || []);

            // Draw hands with separate handling for each side
            if (results.handPoses) {
                ['left', 'right'].forEach(side => {
                    if (!results.handPoses[side]?.landmarks?.length) return;

                    const landmarks = results.handPoses[side].landmarks;
                    const handConfig = this.drawingConfig.handConfig;
                    const sideConfig = handConfig.landmarks[side];

                    // Draw connections using BoneMapper.HAND_CONNECTIONS
                    mpDraw.drawConnectors(
                        this.ctx,
                        landmarks,
                        BoneMapper.HAND_CONNECTIONS,
                        {
                            color: handConfig.connections.color,
                            lineWidth: handConfig.connections.lineWidth
                        }
                    );

                    // Draw landmarks
                    mpDraw.drawLandmarks(
                        this.ctx,
                        landmarks,
                        {
                            color: sideConfig.color,
                            lineWidth: sideConfig.lineWidth,
                            radius: sideConfig.radius
                        }
                    );
                });
            }

            this.ctx.restore();
        } catch (error) {
            console.error('[Canvas2DVisualizer] Error drawing results:', error);
        }
    }

    // Add methods for debug visualization from PoseDetector
    setDebugMode(enabled) {
        this.debugVisualization.enabled = enabled;
        this.enabled = enabled;
        console.log(`[Canvas2DVisualizer] Debug visualization ${enabled ? 'enabled' : 'disabled'}`);

        if (this.canvas) {
            this.canvas.style.display = enabled ? 'block' : 'none';
        }
    }

    drawDebugVisualization(results, videoElement) {
        if (!this.debugVisualization.enabled || !this.isReady() || !results) {
            return;
        }

        // Ensure canvas dimensions match video
        if (videoElement &&
            (this.canvas.width !== videoElement.videoWidth ||
                this.canvas.height !== videoElement.videoHeight)) {
            this.canvas.width = videoElement.videoWidth;
            this.canvas.height = videoElement.videoHeight;
        }

        // Clear canvas
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);

        // Draw pose landmarks if available
        if (this.debugVisualization.drawPoseLandmarks && results.landmarks?.[0]) {
            mpDraw.drawConnectors(
                this.ctx,
                results.landmarks[0],
                BoneMapper.POSE_CONNECTIONS,
                {
                    color: this.debugVisualization.colors.connections,
                    lineWidth: 2
                }
            );

            mpDraw.drawLandmarks(
                this.ctx,
                results.landmarks[0],
                {
                    color: this.debugVisualization.colors.landmarks,
                    lineWidth: 2,
                    radius: 4
                }
            );
        }

        // Draw hands if available
        if (this.debugVisualization.drawHandLandmarks && results.handPoses) {
            // Draw left hand
            if (results.handPoses.left?.landmarks) {
                mpDraw.drawConnectors(
                    this.ctx,
                    results.handPoses.left.landmarks,
                    BoneMapper.HAND_CONNECTIONS,
                    {
                        color: this.debugVisualization.colors.connections,
                        lineWidth: 2
                    }
                );

                mpDraw.drawLandmarks(
                    this.ctx,
                    results.handPoses.left.landmarks,
                    {
                        color: this.debugVisualization.colors.leftHand,
                        lineWidth: 2,
                        radius: 3
                    }
                );
            }

            // Draw right hand
            if (results.handPoses.right?.landmarks) {
                mpDraw.drawConnectors(
                    this.ctx,
                    results.handPoses.right.landmarks,
                    BoneMapper.HAND_CONNECTIONS,
                    {
                        color: this.debugVisualization.colors.connections,
                        lineWidth: 2
                    }
                );

                mpDraw.drawLandmarks(
                    this.ctx,
                    results.handPoses.right.landmarks,
                    {
                        color: this.debugVisualization.colors.rightHand,
                        lineWidth: 2,
                        radius: 3
                    }
                );
            }
        }
    }


    // Update drawLandmarkLabels to handle normalized coordinates
    drawLandmarkLabels(landmarks, prefix = '') {
        this.ctx.save();
        this.ctx.fillStyle = 'white';
        this.ctx.font = '10px Arial';
        this.ctx.globalCompositeOperation = 'source-over';

        landmarks.forEach((point, index) => {
            if (point?.visibility > 0.5) {
                const x = point.x * this.canvas.width;
                const y = point.y * this.canvas.height;
                this.ctx.fillText(index.toString(), x + 5, y - 5);
            }
        });

        this.ctx.restore();
    }

    reset() {
        // Add guard against multiple resets
        if (this.isResetting) {
            console.log('[Canvas2DVisualizer] Reset already in progress, skipping');
            return;
        }

        this.isResetting = true;
        console.log('[Canvas2DVisualizer] Resetting state');

        try {
            // Clean up resize observer
            if (this.resizeObserver) {
                this.resizeObserver.disconnect();
                this.resizeObserver = null;
            }

            if (this.canvas) {
                this.canvas.style.display = 'none';
                if (this.ctx) {
                    this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
                }
            }

            // Reset all state
            this.enabled = false;
            this.canvasReady = false;
            this.isProcessing = false;
            this.drawQueue = [];
            this.readyState = {
                initialized: false,
                canvas: null,
                context: null
            };

            console.log('[Canvas2DVisualizer] Reset complete');
        } finally {
            this.isResetting = false;
        }
    }

    dispose() {
        if (this.resizeObserver) {
            this.resizeObserver.disconnect();
            this.resizeObserver = null;
        }
        this.reset();
    }
}
