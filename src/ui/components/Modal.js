export class Modal {
    constructor({
        title = '',
        isOpen = false,
        onClose = () => { },
        actions = [],
        closeOnBackdropClick = true
    } = {}) {
        this.options = { title, isOpen, onClose, actions, closeOnBackdropClick };
        this.element = this.createModal();
        this.isOpen = isOpen;
        this.onClose = onClose;

        // 初始化“全局拖拽”
        this.setupGlobalDrag();
    }

    createModal() {
        const modal = document.createElement('div');
        modal.className = 'modal camera-modal';
        modal.style.display = this.options.isOpen ? 'flex' : 'none';

        const content = document.createElement('div');
        content.className = 'modal-content';
        content.style.cursor = 'grab';

        const body = document.createElement('div');
        body.className = 'modal-body';
        content.appendChild(body);

        const closeButton = document.createElement('button');
        closeButton.className = 'modal-close';
        closeButton.innerHTML = '×';
        closeButton.onclick = (e) => {
            e.stopPropagation();
            this.close();
            this.options.onClose();
        };
        content.appendChild(closeButton);

        modal.appendChild(content);

        // 点击遮罩关闭
        if (this.options.closeOnBackdropClick) {
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    this.close();
                    this.options.onClose();
                }
            });
        }

        return modal;
    }

    setupGlobalDrag() {
        requestAnimationFrame(() => {
            const modalContent = this.element.querySelector('.modal-content');
            if (!modalContent) {
                console.warn('Modal content not found, retrying...');
                setTimeout(() => this.setupGlobalDrag(), 100);
                return;
            }

            let startX = 0, startY = 0;
            let isDragging = false;

            const onMouseDown = (e) => {
                if (e.target.closest('.modal-close')) return;

                isDragging = true;
                startX = e.clientX - modalContent.offsetLeft;
                startY = e.clientY - modalContent.offsetTop;
                modalContent.style.cursor = 'grabbing';
            };

            const onMouseMove = (e) => {
                if (!isDragging) return;
                e.preventDefault();

                const newX = e.clientX - startX;
                const newY = e.clientY - startY;

                const maxX = window.innerWidth - modalContent.offsetWidth;
                const maxY = window.innerHeight - modalContent.offsetHeight;

                modalContent.style.left = `${Math.max(0, Math.min(maxX, newX))}px`;
                modalContent.style.top = `${Math.max(0, Math.min(maxY, newY))}px`;
            };

            const onMouseUp = () => {
                if (isDragging) {
                    isDragging = false;
                    modalContent.style.cursor = 'grab';
                }
            };

            modalContent.addEventListener('mousedown', onMouseDown);
            document.addEventListener('mousemove', onMouseMove);
            document.addEventListener('mouseup', onMouseUp);
        });
    }

    // 其他与业务相关的方法不变
    setContent(content) {
        const body = this.element.querySelector('.modal-body');
        if (typeof content === 'string') {
            body.innerHTML = content;
        } else if (content instanceof Node) {
            body.innerHTML = '';
            body.appendChild(content);
        }
        return this;
    }

    // 以示例的 Modal 为例
    open() {
        this.isOpen = true;
        // 先移除可能存在的 .closing，显示元素
        this.element.classList.remove('closing');
        this.element.style.display = 'block';

        // 强制一次 reflow，让浏览器知道现在的 display
        // 然后再加 .show 执行动画
        this.element.offsetHeight;
        this.element.classList.add('show');
    }

    close() {
        if (!this.isOpen) return;
        this.isOpen = false;

        // 切换到 .closing，触发淡出动画
        this.element.classList.remove('show');
        this.element.classList.add('closing');

        // 等动画结束后再真正隐藏
        setTimeout(() => {
            if (!this.isOpen) {
                this.element.style.display = 'none';
                this.element.classList.remove('closing');
            }
        }, 300); // 与 CSS 动画时长保持一致
    }

    setActions(actions) {
        let footer = this.element.querySelector('.modal-footer');

        if (!footer && actions.length > 0) {
            footer = document.createElement('div');
            footer.className = 'modal-footer';
            this.element.querySelector('.modal-content').appendChild(footer);
        } else if (footer && actions.length === 0) {
            footer.remove();
            return this;
        }

        footer.innerHTML = '';
        actions.forEach(action => {
            const button = document.createElement('button');
            button.className = `modal-button ${action.variant || ''}`;
            button.textContent = action.label;
            button.onclick = action.onClick;
            footer.appendChild(button);
        });

        return this;
    }

    mount(container) {
        container.appendChild(this.element);
        return this;
    }
}