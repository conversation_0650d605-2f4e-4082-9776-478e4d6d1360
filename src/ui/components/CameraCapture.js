/**
 * CameraCapture Component
 * 
 * Extends the CameraViewer with photo capture functionality
 * for use with the tripo-doll API.
 */

import { CameraViewer } from './cameraViewer.js';

export class CameraCapture extends CameraViewer {
    constructor(app, props = {}) {
        // Call parent constructor with provided props
        super(app, props);

        // Additional properties for photo capture
        this.captureCallbacks = new Set();
        this.capturedPhotos = [];
        this.maxPhotos = props.maxPhotos || 5;
        this.photoQuality = props.photoQuality || 0.9;
        this.photoFormat = props.photoFormat || 'image/jpeg';
        this.photoWidth = props.photoWidth || 640;
        this.photoHeight = props.photoHeight || 480;
        
        // Path for saving photos
        this.savePath = props.savePath || 'assets/images';
    }

    /**
     * Initialize the camera capture component
     * @param {HTMLVideoElement} videoElement - Video element to use
     * @returns {HTMLCanvasElement} - Canvas element for drawing
     */
    async initialize(videoElement) {
        // Call parent initialize method
        const canvas = await super.initialize(videoElement);
        
        // Add capture button to popup window
        this.addCaptureButton();
        
        return canvas;
    }

    /**
     * Add a capture button to the camera popup window
     */
    addCaptureButton() {
        // Create capture button style
        const style = document.createElement('style');
        style.textContent = `
            .camera-capture-button {
                position: absolute;
                bottom: 20px;
                left: 50%;
                transform: translateX(-50%);
                width: 60px;
                height: 60px;
                border-radius: 50%;
                background-color: #f44336;
                border: 4px solid white;
                cursor: pointer;
                box-shadow: 0 2px 5px rgba(0,0,0,0.3);
                transition: all 0.2s ease;
                z-index: 1000;
            }
            .camera-capture-button:hover {
                background-color: #d32f2f;
                transform: translateX(-50%) scale(1.05);
            }
            .camera-capture-button:active {
                background-color: #b71c1c;
                transform: translateX(-50%) scale(0.95);
            }
            .camera-capture-flash {
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background-color: white;
                opacity: 0;
                pointer-events: none;
                z-index: 999;
                transition: opacity 0.1s ease;
            }
            .camera-capture-flash.active {
                opacity: 1;
                animation: flash 0.5s ease-out;
            }
            @keyframes flash {
                0% { opacity: 1; }
                100% { opacity: 0; }
            }
            .camera-capture-preview {
                position: absolute;
                bottom: 20px;
                right: 20px;
                width: 80px;
                height: 80px;
                border-radius: 8px;
                border: 2px solid white;
                overflow: hidden;
                box-shadow: 0 2px 5px rgba(0,0,0,0.3);
                z-index: 1000;
                background-color: #f5f5f5;
                display: flex;
                align-items: center;
                justify-content: center;
            }
            .camera-capture-preview img {
                width: 100%;
                height: 100%;
                object-fit: cover;
            }
        `;

        // Add the capture button to the popup window when it's opened
        const originalOpenPopupWindow = this.openPopupWindow;
        this.openPopupWindow = (...args) => {
            const popupWindow = originalOpenPopupWindow.apply(this, args);
            
            if (popupWindow) {
                // Add style to popup window
                popupWindow.document.head.appendChild(style.cloneNode(true));
                
                // Create flash element
                const flash = document.createElement('div');
                flash.className = 'camera-capture-flash';
                popupWindow.document.body.appendChild(flash);
                
                // Create capture button
                const captureButton = document.createElement('button');
                captureButton.className = 'camera-capture-button';
                captureButton.title = 'Take Photo';
                
                // Create preview container
                const previewContainer = document.createElement('div');
                previewContainer.className = 'camera-capture-preview';
                previewContainer.style.display = 'none';
                
                // Add click handler to capture button
                captureButton.addEventListener('click', () => {
                    this.capturePhoto(flash, previewContainer);
                });
                
                // Add elements to popup
                popupWindow.document.body.appendChild(captureButton);
                popupWindow.document.body.appendChild(previewContainer);
            }
            
            return popupWindow;
        };
    }

    /**
     * Capture a photo from the video stream
     * @param {HTMLElement} flashElement - Element to use for flash effect
     * @param {HTMLElement} previewContainer - Container for preview image
     */
    capturePhoto(flashElement, previewContainer) {
        if (!this.videoElement || !this.videoElement.readyState === 4) {
            console.warn('[CameraCapture] Video not ready for capture');
            return;
        }
        
        try {
            // Create a temporary canvas for the capture
            const captureCanvas = document.createElement('canvas');
            captureCanvas.width = this.photoWidth;
            captureCanvas.height = this.photoHeight;
            const ctx = captureCanvas.getContext('2d');
            
            // Draw the current video frame to the canvas
            ctx.drawImage(this.videoElement, 0, 0, captureCanvas.width, captureCanvas.height);
            
            // Convert to data URL
            const dataUrl = captureCanvas.toDataURL(this.photoFormat, this.photoQuality);
            
            // Add to captured photos array
            this.capturedPhotos.push(dataUrl);
            if (this.capturedPhotos.length > this.maxPhotos) {
                this.capturedPhotos.shift(); // Remove oldest photo if we exceed max
            }
            
            // Show flash effect
            if (flashElement) {
                flashElement.classList.add('active');
                setTimeout(() => {
                    flashElement.classList.remove('active');
                }, 500);
            }
            
            // Update preview
            if (previewContainer) {
                previewContainer.style.display = 'block';
                previewContainer.innerHTML = '';
                const img = document.createElement('img');
                img.src = dataUrl;
                previewContainer.appendChild(img);
            }
            
            // Save the photo to the server
            this.savePhotoToServer(dataUrl);
            
            // Notify callbacks
            this.captureCallbacks.forEach(callback => {
                try {
                    callback(dataUrl);
                } catch (error) {
                    console.error('[CameraCapture] Error in capture callback:', error);
                }
            });
            
            console.log('[CameraCapture] Photo captured successfully');
            return dataUrl;
        } catch (error) {
            console.error('[CameraCapture] Error capturing photo:', error);
            return null;
        }
    }

    /**
     * Save the captured photo to the server
     * @param {string} dataUrl - Data URL of the captured photo
     */
    async savePhotoToServer(dataUrl) {
        try {
            // Convert data URL to blob
            const response = await fetch(dataUrl);
            const blob = await response.blob();
            
            // Create form data
            const formData = new FormData();
            const filename = `capture_${Date.now()}.jpg`;
            formData.append('file', blob, filename);
            formData.append('path', this.savePath);
            formData.append('name', filename);
            
            // Send to server
            const uploadResponse = await fetch('/upload', {
                method: 'POST',
                body: formData
            });
            
            const result = await uploadResponse.json();
            
            if (result.success) {
                console.log('[CameraCapture] Photo saved to server:', result);
                return `/assets/images/${filename}`;
            } else {
                console.error('[CameraCapture] Failed to save photo:', result.error);
                return null;
            }
        } catch (error) {
            console.error('[CameraCapture] Error saving photo to server:', error);
            return null;
        }
    }

    /**
     * Register a callback for when a photo is captured
     * @param {Function} callback - Function to call with the data URL of the captured photo
     * @returns {Function} - Function to call to unregister the callback
     */
    onPhotoCapture(callback) {
        if (typeof callback === 'function') {
            this.captureCallbacks.add(callback);
            return () => this.captureCallbacks.delete(callback);
        }
        return null;
    }

    /**
     * Get the most recently captured photo
     * @returns {string|null} - Data URL of the most recent photo or null if none
     */
    getLatestPhoto() {
        return this.capturedPhotos.length > 0 ? this.capturedPhotos[this.capturedPhotos.length - 1] : null;
    }
}
