export class Slider {
    constructor({ min = 0, max = 100, value = 50, step = 1, labels = [] } = {}) {
        this.element = this.createSlider({ min, max, value, step, labels });
        this.onChangeCallback = null;
    }

    createSlider({ min, max, value, step, labels }) {
        const container = document.createElement('div');
        container.className = 'slider-container';

        // Create slider input
        const input = document.createElement('input');
        input.type = 'range';
        input.className = 'slider';
        input.min = min;
        input.max = max;
        input.value = value;
        input.step = step;

        // Create value display
        const valueDisplay = document.createElement('span');
        valueDisplay.className = 'slider-value';
        valueDisplay.textContent = value;

        // Add labels if provided
        if (labels.length > 0) {
            const labelContainer = document.createElement('div');
            labelContainer.className = 'slider-labels';
            
            labels.forEach((label, index) => {
                const labelElement = document.createElement('span');
                labelElement.className = 'slider-label';
                labelElement.textContent = label;
                labelElement.style.left = `${(index / (labels.length - 1)) * 100}%`;
                labelContainer.appendChild(labelElement);
            });

            container.appendChild(labelContainer);
        }

        // Add event listener
        input.addEventListener('input', () => {
            valueDisplay.textContent = input.value;
            if (this.onChangeCallback) {
                this.onChangeCallback(parseFloat(input.value));
            }
        });

        // Add styles
        if (!document.getElementById('slider-styles')) {
            const style = document.createElement('style');
            style.id = 'slider-styles';
            style.textContent = `
                .slider-container {
                    position: relative;
                    width: 100%;
                    padding: 10px 0;
                }

                .slider {
                    -webkit-appearance: none;
                    width: 100%;
                    height: 4px;
                    border-radius: 2px;
                    background: #ddd;
                    outline: none;
                    margin: 10px 0;
                }

                .slider::-webkit-slider-thumb {
                    -webkit-appearance: none;
                    appearance: none;
                    width: 16px;
                    height: 16px;
                    border-radius: 50%;
                    background: #007AFF;
                    cursor: pointer;
                    transition: all 0.2s;
                }

                .slider::-webkit-slider-thumb:hover {
                    transform: scale(1.2);
                }

                .slider::-moz-range-thumb {
                    width: 16px;
                    height: 16px;
                    border-radius: 50%;
                    background: #007AFF;
                    cursor: pointer;
                    transition: all 0.2s;
                    border: none;
                }

                .slider::-moz-range-thumb:hover {
                    transform: scale(1.2);
                }

                .slider-value {
                    display: inline-block;
                    margin-left: 10px;
                    min-width: 40px;
                    font-family: Arial, sans-serif;
                }

                .slider-labels {
                    position: relative;
                    width: 100%;
                    height: 20px;
                    margin-top: 5px;
                }

                .slider-label {
                    position: absolute;
                    transform: translateX(-50%);
                    font-size: 12px;
                    color: #666;
                    font-family: Arial, sans-serif;
                }
            `;
            document.head.appendChild(style);
        }

        container.appendChild(input);
        container.appendChild(valueDisplay);

        return container;
    }

    onChange(callback) {
        this.onChangeCallback = callback;
        return this;
    }

    setValue(value) {
        const input = this.element.querySelector('input');
        const valueDisplay = this.element.querySelector('.slider-value');
        input.value = value;
        valueDisplay.textContent = value;
        if (this.onChangeCallback) {
            this.onChangeCallback(parseFloat(value));
        }
        return this;
    }

    getValue() {
        const input = this.element.querySelector('input');
        return parseFloat(input.value);
    }

    setDisabled(disabled) {
        const input = this.element.querySelector('input');
        input.disabled = disabled;
        return this;
    }

    mount(container) {
        container.appendChild(this.element);
        return this;
    }
} 