export class Button {
    constructor({ variant = 'primary', size = 'medium', disabled = false, loading = false, icon = null } = {}) {
        this.element = this.createButton({ variant, size, disabled, loading, icon });
    }

    createButton({ variant, size, disabled, loading, icon }) {
        const button = document.createElement('button');
        button.className = `btn btn-${variant} btn-${size}`;
        button.disabled = disabled || loading;

        // Add loading state
        if (loading) {
            button.classList.add('loading');
            const spinner = document.createElement('span');
            spinner.className = 'spinner';
            button.appendChild(spinner);
        }

        // Add icon if provided
        if (icon) {
            const iconElement = document.createElement('span');
            iconElement.className = 'btn-icon';
            iconElement.innerHTML = icon;
            button.appendChild(iconElement);
        }

        // Add styles
        if (!document.getElementById('button-styles')) {
            const style = document.createElement('style');
            style.id = 'button-styles';
            style.textContent = `
                .btn {
                    display: inline-flex;
                    align-items: center;
                    justify-content: center;
                    padding: 8px 16px;
                    border-radius: 4px;
                    font-family: Arial, sans-serif;
                    font-weight: 500;
                    cursor: pointer;
                    transition: all 0.2s;
                    border: none;
                    outline: none;
                }

                .btn:disabled {
                    opacity: 0.6;
                    cursor: not-allowed;
                }

                .btn-primary {
                    background: #007AFF;
                    color: white;
                }

                .btn-primary:hover:not(:disabled) {
                    background: #0056b3;
                }

                .btn-secondary {
                    background: #5856D6;
                    color: white;
                }

                .btn-secondary:hover:not(:disabled) {
                    background: #4240a8;
                }

                .btn-ghost {
                    background: transparent;
                    color: #007AFF;
                    border: 1px solid #007AFF;
                }

                .btn-ghost:hover:not(:disabled) {
                    background: rgba(0, 122, 255, 0.1);
                }

                .btn-small {
                    padding: 4px 8px;
                    font-size: 12px;
                }

                .btn-medium {
                    padding: 8px 16px;
                    font-size: 14px;
                }

                .btn-large {
                    padding: 12px 24px;
                    font-size: 16px;
                }

                .loading {
                    position: relative;
                    color: transparent !important;
                }

                .spinner {
                    position: absolute;
                    width: 16px;
                    height: 16px;
                    border: 2px solid rgba(255, 255, 255, 0.3);
                    border-radius: 50%;
                    border-top-color: white;
                    animation: spin 1s linear infinite;
                }

                @keyframes spin {
                    to { transform: rotate(360deg); }
                }

                .btn-icon {
                    margin-right: 8px;
                }
            `;
            document.head.appendChild(style);
        }

        return button;
    }

    onClick(callback) {
        this.element.addEventListener('click', callback);
        return this;
    }

    setText(text) {
        // Create or update text node
        if (this.element.childNodes.length > 0) {
            // Find text node
            const textNode = Array.from(this.element.childNodes)
                .find(node => node.nodeType === Node.TEXT_NODE);
            if (textNode) {
                textNode.textContent = text;
            } else {
                this.element.appendChild(document.createTextNode(text));
            }
        } else {
            this.element.appendChild(document.createTextNode(text));
        }
        return this;
    }

    setDisabled(disabled) {
        this.element.disabled = disabled;
        return this;
    }

    setLoading(loading) {
        if (loading) {
            this.element.classList.add('loading');
            if (!this.element.querySelector('.spinner')) {
                const spinner = document.createElement('span');
                spinner.className = 'spinner';
                this.element.appendChild(spinner);
            }
        } else {
            this.element.classList.remove('loading');
            const spinner = this.element.querySelector('.spinner');
            if (spinner) {
                spinner.remove();
            }
        }
        this.element.disabled = loading;
        return this;
    }

    mount(container) {
        container.appendChild(this.element);
        return this;
    }
} 