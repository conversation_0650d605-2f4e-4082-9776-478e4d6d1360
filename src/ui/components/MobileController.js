/**
 * Mobile Controller Component
 * Provides a touch-friendly interface for controlling the Looking Glass display from a mobile device
 */

// connectionManager removed - using realtime mode directly
import MobileViewer from './MobileViewer.js';
import { MediaStreamManager } from '../../media/compat';

export class MobileController {
    constructor(options = {}) {
        this.options = {
            container: document.body,
            ...options
        };

        this.container = this.options.container;
        this.isConnected = false;
        this.sessionId = null;

        // Create the mobile viewer for 3D rendering
        this.viewer = null;

        // Media streaming manager
        this.mediaManager = null;

        // Bind methods
        this._handleOrientationChange = this._handleOrientationChange.bind(this);
        this._handleTouchStart = this._handleTouchStart.bind(this);
        this._handleTouchMove = this._handleTouchMove.bind(this);
        this._handleTouchEnd = this._handleTouchEnd.bind(this);
        this._handlePinchZoom = this._handlePinchZoom.bind(this);

        // Touch tracking variables
        this.touchStartX = 0;
        this.touchStartY = 0;
        this.lastTouchX = 0;
        this.lastTouchY = 0;
        this.pinchDistance = 0;
        this.isTouching = false;
    }

    /**
     * Initialize the mobile controller
     * @param {string} sessionId - The session ID to connect with
     */
    async initialize(sessionId) {
        this.sessionId = sessionId;

        try {
            // Store the session ID in localStorage for reconnection
            localStorage.setItem('mobileControllerSessionId', sessionId);

            // Connect to the WebSocket server
            console.log('[MobileController] Connecting to WebSocket server as controller with session ID:', sessionId);

            try {
                const connected = await connectionManager.connect('controller', sessionId);

                if (!connected) {
                    console.error('[MobileController] Failed to connect to WebSocket server');
                    throw new Error('Failed to connect to WebSocket server');
                }

                // Log connection details
                console.log('[MobileController] WebSocket connection successful');
                console.log('[MobileController] WebSocket URL:', connectionManager.socket?.url || 'Unknown');
                console.log('[MobileController] WebSocket state:', connectionManager.socket?.readyState || 'Unknown');
            } catch (error) {
                console.error('[MobileController] WebSocket connection error:', error.message);
                console.error('[MobileController] Error stack:', error.stack);
                throw error;
            }

            console.log('[MobileController] Successfully connected to WebSocket server');
            this.isConnected = true;

            // Set up event listeners
            this._setupEventListeners();

            // Create the UI
            this._createUI();

            // Initialize the mobile viewer
            this.viewer = new MobileViewer({
                container: this.viewContainer
            });
            await this.viewer.initialize();

            // Get the download server port from the environment or use default
            const downloadServerPort = typeof window !== 'undefined' &&
                window.location &&
                window.location.origin ?
                window.location.origin.replace(/:\d+/, `:${import.meta.env.VITE_DOWNLOAD_SERVER_PORT || '2994'}`) :
                'http://localhost:2994';

            // Initialize media streaming
            this.mediaManager = new MediaStreamManager({
                llmServerEndpoint: `${downloadServerPort}/api/llm/process-media`,
                onStreamStart: () => {
                    console.log('[MobileController] Media streaming started');
                    connectionManager.send('streamingStarted', { type: 'audio-video' });
                },
                onStreamStop: () => {
                    console.log('[MobileController] Media streaming stopped');
                    connectionManager.send('streamingStopped', {});
                },
                onStreamError: (error) => {
                    console.error('[MobileController] Media streaming error:', error);
                },
                onResponse: (data) => {
                    console.log('[MobileController] Received LLM response:', data);
                }
            });

            const mediaInitialized = await this.mediaManager.initialize();

            if (!mediaInitialized) {
                console.warn('[MobileController] Media devices initialization failed');
                // Continue anyway, as this is not critical
            }

            // Set up message handlers
            this._setupMessageHandlers();

            // Make the controller instance globally available for ConnectionManager
            window.mobileController = this;

            // Update the connection status
            this._updateConnectionStatus(true);

            return true;
        } catch (error) {
            console.error('[MobileController] Error initializing:', error);
            this._updateConnectionStatus(false);
            return false;
        }
    }

    /**
     * Set up WebSocket message handlers
     * @private
     */
    _setupMessageHandlers() {
        // Set up connection status handlers
        connectionManager.on('disconnect', () => {
            this.isConnected = false;
            this._updateConnectionStatus(false);
            console.log('[MobileController] Disconnected from server');

            // Attempt to reconnect after a short delay
            setTimeout(() => {
                if (!this.isConnected) {
                    console.log('[MobileController] Attempting to reconnect...');
                    this._attemptReconnect();
                }
            }, 3000); // Wait 3 seconds before attempting to reconnect
        });

        connectionManager.on('connected', () => {
            this.isConnected = true;
            this._updateConnectionStatus(true);
            console.log('[MobileController] Connected to server, requesting initial state');

            // Request initial UI state from the display
            console.log('[MobileController] Requesting UI state');
            connectionManager.send('requestUIState', {});

            // Request current mesh from the display
            console.log('[MobileController] Requesting mesh info');
            connectionManager.send('requestMesh', {});

            // Request camera state from the display
            console.log('[MobileController] Requesting camera state');
            connectionManager.send('requestCameraState', {});
        });

        // Add explicit handlers for each message type with debug logging
        connectionManager.on('uiState', (data) => {
            console.log('[MobileController] Received UI state:', data);
            this.updateUIState(data);
        });

        connectionManager.on('meshLoaded', (data) => {
            console.log('[MobileController] Received mesh info:', data);
            this.updateMesh(data);
        });

        connectionManager.on('cameraUpdate', (data) => {
            console.log('[MobileController] Received camera update:', data);
            if (this.viewer) {
                this.viewer._updateCamera(data);
            } else {
                console.warn('[MobileController] No viewer available to update camera');
            }
        });
    }

    /**
     * Set up event listeners for touch and orientation
     * @private
     */
    _setupEventListeners() {
        // Add touch event listeners
        this.container.addEventListener('touchstart', this._handleTouchStart, { passive: false });
        this.container.addEventListener('touchmove', this._handleTouchMove, { passive: false });
        this.container.addEventListener('touchend', this._handleTouchEnd, { passive: false });

        // Add orientation event listener
        window.addEventListener('deviceorientation', this._handleOrientationChange);
    }

    /**
     * Create the mobile UI
     * @private
     */
    _createUI() {
        // Create the main container
        this.uiContainer = document.createElement('div');
        this.uiContainer.className = 'mobile-controller';
        this.uiContainer.style.width = '100%';
        this.uiContainer.style.height = '100%';
        this.uiContainer.style.position = 'fixed';
        this.uiContainer.style.top = '0';
        this.uiContainer.style.left = '0';
        this.uiContainer.style.backgroundColor = '#000';
        this.uiContainer.style.color = '#fff';
        this.uiContainer.style.fontFamily = 'Arial, sans-serif';
        this.uiContainer.style.overflow = 'hidden';
        this.uiContainer.style.touchAction = 'none'; // Prevent browser handling of touch events

        // Add a debug button
        this._addDebugButton();

        // Add media controls
        this._addMediaControls();

        // Create a container for the 3D view (will be used later for mesh display)
        this.viewContainer = document.createElement('div');
        this.viewContainer.className = 'view-container';
        this.viewContainer.style.width = '100%';
        this.viewContainer.style.height = '100%';
        this.viewContainer.style.position = 'absolute';
        this.viewContainer.style.top = '0';
        this.viewContainer.style.left = '0';
        this.viewContainer.style.zIndex = '1'; // Below UI elements

        // Create the status bar
        this.statusBar = document.createElement('div');
        this.statusBar.className = 'status-bar';
        this.statusBar.style.position = 'fixed';
        this.statusBar.style.top = '0';
        this.statusBar.style.left = '0';
        this.statusBar.style.width = '100%';
        this.statusBar.style.padding = '10px';
        this.statusBar.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';
        this.statusBar.style.color = '#fff';
        this.statusBar.style.textAlign = 'center';
        this.statusBar.style.zIndex = '1000';
        this.statusBar.style.fontSize = '14px';

        // Create the touch area
        this.touchArea = document.createElement('div');
        this.touchArea.className = 'touch-area';
        this.touchArea.style.position = 'fixed';
        this.touchArea.style.top = '0';
        this.touchArea.style.left = '0';
        this.touchArea.style.width = '100%';
        this.touchArea.style.height = '100%';
        this.touchArea.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
        this.touchArea.style.display = 'flex';
        this.touchArea.style.justifyContent = 'center';
        this.touchArea.style.alignItems = 'center';
        this.touchArea.style.color = '#fff';
        this.touchArea.style.fontSize = '18px';
        this.touchArea.style.transition = 'opacity 0.5s ease-in-out';
        this.touchArea.textContent = '';

        // Add tap handler to hide all instructions
        this.touchArea.addEventListener('click', () => {
            this._hideAllInstructions();
        });

        // Create the instructions
        this.instructions = document.createElement('div');
        this.instructions.className = 'instructions';
        this.instructions.style.position = 'fixed';
        this.instructions.style.bottom = '20px';
        this.instructions.style.left = '0';
        this.instructions.style.width = '100%';
        this.instructions.style.padding = '10px';
        this.instructions.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';
        this.instructions.style.color = '#fff';
        this.instructions.style.textAlign = 'center';
        this.instructions.style.zIndex = '1000';
        this.instructions.style.fontSize = '14px';
        this.instructions.style.transition = 'opacity 0.5s ease-in-out';
        this.instructions.innerHTML = 'Drag: Rotate<br>Pinch: Zoom<br>Double Tap: Reset View';

        // Create a temporary instruction overlay
        this.instructionOverlay = document.createElement('div');
        this.instructionOverlay.className = 'instruction-overlay';
        this.instructionOverlay.style.position = 'fixed';
        this.instructionOverlay.style.top = '0';
        this.instructionOverlay.style.left = '0';
        this.instructionOverlay.style.width = '100%';
        this.instructionOverlay.style.height = '100%';
        this.instructionOverlay.style.display = 'flex';
        this.instructionOverlay.style.flexDirection = 'column';
        this.instructionOverlay.style.justifyContent = 'center';
        this.instructionOverlay.style.alignItems = 'center';
        this.instructionOverlay.style.backgroundColor = 'rgba(0, 0, 0, 0.8)';
        this.instructionOverlay.style.color = '#fff';
        this.instructionOverlay.style.zIndex = '2000';
        this.instructionOverlay.style.transition = 'opacity 0.5s ease-in-out';

        // Create the instruction text
        const instructionTitle = document.createElement('h2');
        instructionTitle.textContent = 'Mobile Control';
        instructionTitle.style.marginBottom = '20px';
        instructionTitle.style.fontSize = '24px';

        const instructionText = document.createElement('div');
        instructionText.style.fontSize = '18px';
        instructionText.style.lineHeight = '1.6';
        instructionText.style.textAlign = 'center';
        instructionText.style.padding = '0 20px';
        instructionText.innerHTML = '<div style="margin-bottom: 15px;"><strong>Drag</strong>: Rotate the model</div>' +
            '<div style="margin-bottom: 15px;"><strong>Pinch</strong>: Zoom in/out</div>' +
            '<div><strong>Double Tap</strong>: Reset view</div>';

        const tapToDismiss = document.createElement('div');
        tapToDismiss.textContent = 'Tap anywhere to continue';
        tapToDismiss.style.marginTop = '40px';
        tapToDismiss.style.fontSize = '14px';
        tapToDismiss.style.opacity = '0.7';

        this.instructionOverlay.appendChild(instructionTitle);
        this.instructionOverlay.appendChild(instructionText);
        this.instructionOverlay.appendChild(tapToDismiss);

        // Add tap to dismiss
        this.instructionOverlay.addEventListener('click', () => {
            this.instructionOverlay.style.opacity = '0';
            setTimeout(() => {
                this.instructionOverlay.style.display = 'none';
            }, 500);
        });

        // Add elements to the container
        this.uiContainer.appendChild(this.viewContainer); // Add the view container first (lowest z-index)
        this.uiContainer.appendChild(this.touchArea);
        this.uiContainer.appendChild(this.statusBar); // Status bar on top
        this.uiContainer.appendChild(this.instructions);
        this.uiContainer.appendChild(this.instructionOverlay);

        // Add the container to the document
        this.container.appendChild(this.uiContainer);

        // Auto-hide instructions after a delay
        setTimeout(() => {
            this.instructions.style.opacity = '0';
        }, 5000); // Hide after 5 seconds

        // Auto-hide instruction overlay after a delay if user hasn't tapped
        setTimeout(() => {
            if (this.instructionOverlay.style.display !== 'none') {
                this.instructionOverlay.style.opacity = '0';
                setTimeout(() => {
                    this.instructionOverlay.style.display = 'none';
                }, 500);
            }
        }, 8000); // Hide after 8 seconds if not dismissed
    }

    /**
     * Attempt to reconnect to the WebSocket server
     * @private
     */
    async _attemptReconnect() {
        try {
            // Get the session ID from localStorage or use the current one
            const savedSessionId = localStorage.getItem('mobileControllerSessionId') || this.sessionId;

            if (!savedSessionId) {
                console.error('[MobileController] No session ID available for reconnection');
                return false;
            }

            console.log(`[MobileController] Attempting to reconnect with session ID: ${savedSessionId}`);

            // Update status bar
            if (this.statusBar) {
                this.statusBar.textContent = 'Reconnecting to Looking Glass Display...';
                this.statusBar.style.backgroundColor = 'rgba(255, 152, 0, 0.7)';
            }

            // Disconnect first to ensure clean state
            connectionManager.disconnect();

            // Wait a moment
            await new Promise(resolve => setTimeout(resolve, 500));

            // Reconnect
            const connected = await connectionManager.connect('controller', savedSessionId);
            console.log('[MobileController] Reconnection result:', connected);

            return connected;
        } catch (error) {
            console.error('[MobileController] Reconnection error:', error);
            return false;
        }
    }

    /**
     * Update the connection status display
     * @private
     * @param {boolean} connected - Whether the controller is connected
     */
    _updateConnectionStatus(connected) {
        console.log(`[MobileController] Updating connection status: ${connected}`);

        // Update the connection state
        this.isConnected = connected;

        if (!this.statusBar) {
            console.log('[MobileController] No status bar to update');
            return;
        }

        if (connected) {
            this.statusBar.textContent = 'Connected to Looking Glass Display';
            this.statusBar.style.backgroundColor = 'rgba(0, 128, 0, 0.7)';

            // Request initial state from the display
            console.log('[MobileController] Connected, requesting initial state');
            setTimeout(() => {
                // Request UI state
                console.log('[MobileController] Requesting UI state');
                connectionManager.send('requestUIState', {});

                // Request mesh info
                console.log('[MobileController] Requesting mesh info');
                connectionManager.send('requestMesh', {});

                // Request camera state
                console.log('[MobileController] Requesting camera state');
                connectionManager.send('requestCameraState', {});
            }, 500);
        } else {
            this.statusBar.textContent = 'Disconnected from Looking Glass Display';
            this.statusBar.style.backgroundColor = 'rgba(255, 0, 0, 0.7)';
        }
    }

    /**
     * Handle touch start events
     * @private
     * @param {TouchEvent} event - The touch event
     */
    _handleTouchStart(event) {
        event.preventDefault();

        // Check for double tap to reset view
        const now = new Date().getTime();
        const timeSince = now - (this.lastTap || 0);

        if (timeSince < 300 && timeSince > 0) {
            // Double tap detected
            connectionManager.send('resetView', {});
            this.lastTap = 0;
            return;
        }

        this.lastTap = now;

        // Handle touch start
        if (event.touches.length === 1) {
            // Single touch - rotation
            const touch = event.touches[0];
            this.touchStartX = touch.clientX;
            this.touchStartY = touch.clientY;
            this.lastTouchX = touch.clientX;
            this.lastTouchY = touch.clientY;
            this.isTouching = true;
        } else if (event.touches.length === 2) {
            // Two touches - pinch zoom
            const touch1 = event.touches[0];
            const touch2 = event.touches[1];
            this.pinchDistance = Math.hypot(
                touch2.clientX - touch1.clientX,
                touch2.clientY - touch1.clientY
            );
        }
    }

    /**
     * Handle touch move events
     * @private
     * @param {TouchEvent} event - The touch event
     */
    _handleTouchMove(event) {
        event.preventDefault();

        if (!this.isConnected) return;

        if (event.touches.length === 1 && this.isTouching) {
            // Single touch - rotation
            const touch = event.touches[0];
            const deltaX = touch.clientX - this.lastTouchX;
            const deltaY = touch.clientY - this.lastTouchY;

            // Only send if there's significant movement
            if (Math.abs(deltaX) > 1 || Math.abs(deltaY) > 1) {
                console.log(`[MobileController] Sending rotation: deltaX=${deltaX * 0.01}, deltaY=${deltaY * 0.01}`);

                // Send rotation message
                connectionManager.send('rotate', {
                    deltaX: deltaX * 0.01,
                    deltaY: deltaY * 0.01
                });
            }

            this.lastTouchX = touch.clientX;
            this.lastTouchY = touch.clientY;
        } else if (event.touches.length === 2) {
            // Handle pinch zoom
            this._handlePinchZoom(event);
        }
    }

    /**
     * Handle touch end events
     * @private
     * @param {TouchEvent} event - The touch event
     */
    _handleTouchEnd(event) {
        event.preventDefault();
        this.isTouching = false;
    }

    /**
     * Handle pinch zoom gestures
     * @private
     * @param {TouchEvent} event - The touch event
     */
    _handlePinchZoom(event) {
        const touch1 = event.touches[0];
        const touch2 = event.touches[1];
        const currentDistance = Math.hypot(
            touch2.clientX - touch1.clientX,
            touch2.clientY - touch1.clientY
        );

        if (this.pinchDistance > 0) {
            const scale = currentDistance / this.pinchDistance;

            // Only send if there's significant change
            if (Math.abs(scale - 1) > 0.01) {
                console.log(`[MobileController] Sending zoom: scale=${scale}`);

                // Send zoom message
                connectionManager.send('zoom', {
                    // Use a more direct approach for zoom
                    scale: scale > 1 ? 0.95 : 1.05 // Invert for more natural feel
                });
            }
        }

        this.pinchDistance = currentDistance;
    }

    /**
     * Handle device orientation changes
     * @private
     * @param {DeviceOrientationEvent} event - The orientation event
     */
    _handleOrientationChange(event) {
        // Only send orientation updates if connected and enabled
        if (!this.isConnected || !this.options.useOrientation) return;

        // Send orientation data
        connectionManager.send('orientation', {
            alpha: event.alpha,
            beta: event.beta,
            gamma: event.gamma
        });
    }

    /**
     * Update the UI state based on data from the display
     * @param {object} data - The UI state data
     */
    updateUIState(data) {
        console.log('[MobileController] Updating UI state:', JSON.stringify(data));

        // Update status message if provided
        if (data.statusMessage) {
            this.statusBar.textContent = data.statusMessage;
        }

        // Update any other UI elements based on the state
        if (data.isGenerating) {
            this.statusBar.style.backgroundColor = 'rgba(255, 152, 0, 0.7)';
            this.statusBar.textContent = 'Generating 3D model...';

            // Show a generating indicator
            this._showGeneratingIndicator();
        } else if (data.hasModel) {
            // If we have a model but not generating, show green status
            this.statusBar.style.backgroundColor = 'rgba(0, 128, 0, 0.7)';
            if (!data.statusMessage) {
                this.statusBar.textContent = 'Model loaded';
            }
        }

        // Update camera if provided
        if (data.camera && this.viewer) {
            console.log('[MobileController] Updating camera from UI state:', data.camera);
            this.viewer.updateCameraFromHost(data.camera);
        }

        // Update scene info if provided
        if (data.currentScene) {
            console.log('[MobileController] Scene info:', data.currentScene);
            // You can display scene info in the UI if needed
        }

        // If we have a model, update the background to be more transparent
        if (data.hasModel) {
            this.touchArea.style.backgroundColor = 'rgba(0, 0, 0, 0.2)';

            // Hide the instructions if they're still visible
            if (this.instructions.style.opacity !== '0') {
                this.instructions.style.opacity = '0';
            }

            // Hide the instruction overlay if it's still visible
            if (this.instructionOverlay.style.display !== 'none') {
                this.instructionOverlay.style.opacity = '0';
                setTimeout(() => {
                    this.instructionOverlay.style.display = 'none';
                }, 500);
            }
        }

        // Update mesh selector if available
        if (data.meshSelector) {
            console.log('[MobileController] Updating mesh selector with data:', data.meshSelector);
            this._updateMeshSelector(data.meshSelector);
        } else {
            console.log('[MobileController] No mesh selector data available');
        }

        // Update actions menu if available
        if (data.actionsMenu) {
            this._updateActionsMenu(data.actionsMenu);
        }

        // Update VR button visibility if available
        if (data.vrAvailable !== undefined) {
            this._updateVRButton(data.vrAvailable);
        }
    }

    /**
     * Hide all instruction elements
     * @private
     */
    _hideAllInstructions() {
        // Hide the instructions
        if (this.instructions) {
            this.instructions.style.opacity = '0';
        }

        // Hide the instruction overlay
        if (this.instructionOverlay) {
            this.instructionOverlay.style.opacity = '0';
            setTimeout(() => {
                this.instructionOverlay.style.display = 'none';
            }, 500);
        }
    }

    /**
     * Show a generating indicator
     * @private
     */
    _showGeneratingIndicator() {
        // Remove any existing indicator
        const existingIndicator = this.viewContainer.querySelector('.generating-indicator');
        if (existingIndicator) {
            existingIndicator.parentNode.removeChild(existingIndicator);
        }

        // Create a generating indicator
        const indicator = document.createElement('div');
        indicator.className = 'generating-indicator';
        indicator.style.position = 'absolute';
        indicator.style.top = '50%';
        indicator.style.left = '50%';
        indicator.style.transform = 'translate(-50%, -50%)';
        indicator.style.backgroundColor = 'rgba(0, 0, 0, 0.8)';
        indicator.style.color = '#fff';
        indicator.style.padding = '20px';
        indicator.style.borderRadius = '10px';
        indicator.style.textAlign = 'center';
        indicator.style.zIndex = '100';

        // Add a spinner
        const spinner = document.createElement('div');
        spinner.style.border = '4px solid rgba(255, 255, 255, 0.3)';
        spinner.style.borderTop = '4px solid #ffffff';
        spinner.style.borderRadius = '50%';
        spinner.style.width = '30px';
        spinner.style.height = '30px';
        spinner.style.animation = 'spin 1s linear infinite';
        spinner.style.margin = '0 auto 15px auto';

        // Add keyframes for the spinner
        const style = document.createElement('style');
        style.textContent = `
            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }
        `;
        document.head.appendChild(style);

        // Add text
        const text = document.createElement('div');
        text.textContent = 'Generating 3D model...';

        // Add elements to the indicator
        indicator.appendChild(spinner);
        indicator.appendChild(text);

        // Add the indicator to the view container
        this.viewContainer.appendChild(indicator);
    }

    /**
     * Update the mesh display based on data from the display
     * @param {object} data - The mesh data
     */
    updateMesh(data) {
        console.log('[MobileController] Updating mesh:', data);

        // Check if we have a viewer
        if (!this.viewer) {
            console.error('[MobileController] No viewer available to update mesh');
            return;
        }

        // Check if the data is valid
        if (!data) {
            console.error('[MobileController] Invalid mesh data');
            return;
        }

        // If we have mesh data, update the UI to show it
        if (data.hasMesh) {
            // Update status bar
            this.statusBar.textContent = 'Mesh loaded: ' + (data.meshName || 'Unknown');
            this.statusBar.style.backgroundColor = 'rgba(0, 128, 0, 0.7)';

            // Show that we have a mesh loaded
            this.touchArea.style.backgroundColor = 'rgba(0, 0, 0, 0.2)';

            // Hide all instructions
            this._hideAllInstructions();

            // If we have a viewer, update the mesh
            if (this.viewer) {
                console.log('[MobileController] Forwarding mesh data to viewer');
                // Forward the data directly to the viewer
                this.viewer._updateMesh(data);
            } else {
                console.log('[MobileController] No viewer found to update mesh');
            }

            // Create a message to show the mesh is synchronized
            const meshLoadedMsg = document.createElement('div');
            meshLoadedMsg.style.position = 'absolute';
            meshLoadedMsg.style.top = '50%';
            meshLoadedMsg.style.left = '50%';
            meshLoadedMsg.style.transform = 'translate(-50%, -50%)';
            meshLoadedMsg.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';
            meshLoadedMsg.style.color = '#fff';
            meshLoadedMsg.style.padding = '15px 20px';
            meshLoadedMsg.style.borderRadius = '8px';
            meshLoadedMsg.style.fontSize = '16px';
            meshLoadedMsg.style.textAlign = 'center';
            meshLoadedMsg.style.zIndex = '100';
            meshLoadedMsg.style.transition = 'opacity 0.5s ease-in-out';
            meshLoadedMsg.textContent = `Mesh "${data.meshName || 'Unknown'}" synchronized`;

            // Add to the view container
            this.viewContainer.appendChild(meshLoadedMsg);

            // Fade out after a few seconds
            setTimeout(() => {
                meshLoadedMsg.style.opacity = '0';
                setTimeout(() => {
                    if (meshLoadedMsg.parentNode) {
                        meshLoadedMsg.parentNode.removeChild(meshLoadedMsg);
                    }
                }, 500);
            }, 3000);
        }
    }

    /**
     * Update the mesh selector UI based on data from the display
     * @param {object} data - The mesh selector data
     * @private
     */
    _updateMeshSelector(data) {
        console.log('[MobileController] Updating mesh selector with data:', JSON.stringify(data));

        // Check if the data is valid
        if (!data || !data.meshes) {
            console.warn('[MobileController] Invalid mesh selector data:', data);
            return;
        }

        // Check if we already have a mesh selector
        let meshSelector = this.uiContainer.querySelector('.mobile-mesh-selector');
        console.log('[MobileController] Existing mesh selector element:', meshSelector ? 'Found' : 'Not found');

        // Log the meshes array for debugging
        if (Array.isArray(data.meshes)) {
            console.log(`[MobileController] Received ${data.meshes.length} meshes:`, data.meshes);
        } else {
            console.warn('[MobileController] Meshes is not an array:', data.meshes);
            // Try to convert to array if it's an object
            if (typeof data.meshes === 'object' && data.meshes !== null) {
                data.meshes = Object.values(data.meshes);
                console.log('[MobileController] Converted meshes to array:', data.meshes);
            }
        }

        // If we don't have a mesh selector and there are meshes, create one
        if (!meshSelector && data.meshes && data.meshes.length > 0) {
            meshSelector = document.createElement('div');
            meshSelector.className = 'mobile-mesh-selector';
            meshSelector.style.position = 'absolute';
            meshSelector.style.bottom = '20px';
            meshSelector.style.left = '50%';
            meshSelector.style.transform = 'translateX(-50%)';
            meshSelector.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';
            meshSelector.style.borderRadius = '10px';
            meshSelector.style.padding = '10px';
            meshSelector.style.display = 'flex';
            meshSelector.style.flexDirection = 'row';
            meshSelector.style.gap = '10px';
            meshSelector.style.overflowX = 'auto';
            meshSelector.style.maxWidth = '90%';
            meshSelector.style.zIndex = '100';
            meshSelector.style.transition = 'opacity 0.3s ease-in-out';

            // Add to the UI container
            this.uiContainer.appendChild(meshSelector);
        }

        // If we have a mesh selector and there are meshes, update it
        if (meshSelector && data.meshes && data.meshes.length > 0) {
            // Clear existing items
            meshSelector.innerHTML = '';

            // Log the meshes we're adding
            console.log(`[MobileController] Adding ${data.meshes.length} meshes to selector`);

            // Add mesh items
            data.meshes.forEach(mesh => {
                if (!mesh || !mesh.id) {
                    console.warn('[MobileController] Invalid mesh data:', mesh);
                    return;
                }

                console.log(`[MobileController] Adding mesh to selector: ${mesh.id} (${mesh.name || 'unnamed'})`);

                const meshItem = document.createElement('div');
                meshItem.className = 'mesh-item';
                meshItem.dataset.meshId = mesh.id;
                meshItem.style.width = '60px';
                meshItem.style.height = '60px';
                meshItem.style.backgroundColor = mesh.id === data.selectedMesh ? 'rgba(0, 120, 255, 0.7)' : 'rgba(50, 50, 50, 0.7)';
                meshItem.style.borderRadius = '8px';
                meshItem.style.display = 'flex';
                meshItem.style.alignItems = 'center';
                meshItem.style.justifyContent = 'center';
                meshItem.style.cursor = 'pointer';
                meshItem.style.flexShrink = '0';
                meshItem.style.transition = 'background-color 0.2s ease-in-out';

                // Add mesh name or icon
                const meshName = document.createElement('div');
                meshName.textContent = mesh.name || mesh.id;
                meshName.style.color = '#fff';
                meshName.style.fontSize = '12px';
                meshName.style.textAlign = 'center';
                meshName.style.padding = '5px';
                meshName.style.wordBreak = 'break-word';
                meshName.style.overflow = 'hidden';
                meshName.style.textOverflow = 'ellipsis';
                meshName.style.display = 'block';
                meshName.style.maxHeight = '2.4em';
                meshName.style.lineHeight = '1.2em';

                meshItem.appendChild(meshName);

                // Add click handler to select mesh
                meshItem.addEventListener('click', () => {
                    console.log(`[MobileController] Mesh selected: ${mesh.id}`);
                    // Send mesh selection message
                    connectionManager.send('selectMesh', {
                        meshId: mesh.id
                    });

                    // Also update the UI to show this mesh is selected
                    const allMeshItems = meshSelector.querySelectorAll('.mesh-item');
                    allMeshItems.forEach(item => {
                        item.style.backgroundColor = item.dataset.meshId === mesh.id ?
                            'rgba(0, 120, 255, 0.7)' : 'rgba(50, 50, 50, 0.7)';
                    });
                });

                meshSelector.appendChild(meshItem);
            });

            // Show or hide based on visibility state
            meshSelector.style.opacity = data.visible ? '1' : '0';
            meshSelector.style.pointerEvents = data.visible ? 'auto' : 'none';
        } else if (meshSelector && (!data.meshes || data.meshes.length === 0)) {
            console.log('[MobileController] No meshes available, hiding mesh selector');
            meshSelector.style.opacity = '0';
            meshSelector.style.pointerEvents = 'none';
        }
    }

    /**
     * Update the actions menu UI based on data from the display
     * @param {object} data - The actions menu data
     * @private
     */
    _updateActionsMenu(data) {
        // Check if we already have an actions menu button
        let actionsButton = this.uiContainer.querySelector('.mobile-actions-button');

        // If we don't have an actions button, create one
        if (!actionsButton) {
            actionsButton = document.createElement('button');
            actionsButton.className = 'mobile-actions-button';
            actionsButton.style.position = 'absolute';
            actionsButton.style.top = '20px';
            actionsButton.style.left = '20px';
            actionsButton.style.width = '42px';
            actionsButton.style.height = '42px';
            actionsButton.style.borderRadius = '50%';
            actionsButton.style.backgroundColor = 'rgba(0, 120, 255, 0.7)';
            actionsButton.style.border = 'none';
            actionsButton.style.color = 'white';
            actionsButton.style.fontSize = '20px';
            actionsButton.style.display = 'flex';
            actionsButton.style.alignItems = 'center';
            actionsButton.style.justifyContent = 'center';
            actionsButton.style.cursor = 'pointer';
            actionsButton.style.zIndex = '100';
            actionsButton.style.transition = 'background-color 0.3s ease';
            actionsButton.style.boxShadow = '0 2px 10px rgba(0, 0, 0, 0.2)';
            actionsButton.innerHTML = '<span>⚙️</span>';

            // Add click handler to toggle actions menu
            actionsButton.addEventListener('click', () => {
                // Send toggle actions menu message
                connectionManager.send('toggleActionsMenu', {});
            });

            // Add to the UI container
            this.uiContainer.appendChild(actionsButton);
        }

        // Update button state based on menu visibility
        if (data.visible) {
            actionsButton.style.backgroundColor = 'rgba(0, 120, 255, 0.9)';
        } else {
            actionsButton.style.backgroundColor = 'rgba(0, 120, 255, 0.7)';
        }
    }

    /**
     * Update the VR button UI based on data from the display
     * @param {boolean} available - Whether VR is available
     * @private
     */
    _updateVRButton(available) {
        // Check if we already have a VR button
        let vrButton = this.uiContainer.querySelector('.mobile-vr-button');

        // If VR is available and we don't have a button, create one
        if (available && !vrButton) {
            vrButton = document.createElement('button');
            vrButton.className = 'mobile-vr-button';
            vrButton.style.position = 'absolute';
            vrButton.style.top = '20px';
            vrButton.style.right = '20px';
            vrButton.style.width = '42px';
            vrButton.style.height = '42px';
            vrButton.style.borderRadius = '50%';
            vrButton.style.backgroundColor = 'rgba(0, 120, 255, 0.7)';
            vrButton.style.border = 'none';
            vrButton.style.color = 'white';
            vrButton.style.fontSize = '20px';
            vrButton.style.display = 'flex';
            vrButton.style.alignItems = 'center';
            vrButton.style.justifyContent = 'center';
            vrButton.style.cursor = 'pointer';
            vrButton.style.zIndex = '100';
            vrButton.style.transition = 'background-color 0.3s ease';
            vrButton.style.boxShadow = '0 2px 10px rgba(0, 0, 0, 0.2)';
            vrButton.innerHTML = '<span>🥽</span>';

            // Add click handler to toggle VR
            vrButton.addEventListener('click', () => {
                // Send toggle VR message
                connectionManager.send('toggleVR', {});
            });

            // Add to the UI container
            this.uiContainer.appendChild(vrButton);
        }

        // If VR is not available and we have a button, remove it
        if (!available && vrButton) {
            vrButton.parentNode.removeChild(vrButton);
        }
    }

    /**
     * Add a debug button to the UI
     * @private
     */
    _addDebugButton() {
        // Skip adding debug button on non-mobile devices
        if (!this._isMobileDevice()) return;

        // Create a debug button
        const debugButton = document.createElement('button');
        debugButton.className = 'debug-button';
        debugButton.style.position = 'fixed';
        debugButton.style.bottom = '80px';
        debugButton.style.right = '20px';
        debugButton.style.width = '60px';
        debugButton.style.height = '60px';
        debugButton.style.borderRadius = '50%';
        debugButton.style.backgroundColor = 'rgba(255, 0, 0, 0.7)';
        debugButton.style.border = 'none';
        debugButton.style.color = 'white';
        debugButton.style.fontSize = '12px';
        debugButton.style.fontWeight = 'bold';
        debugButton.style.display = 'flex';
        debugButton.style.alignItems = 'center';
        debugButton.style.justifyContent = 'center';
        debugButton.style.cursor = 'pointer';
        debugButton.style.zIndex = '1000';
        debugButton.style.boxShadow = '0 2px 10px rgba(0, 0, 0, 0.3)';
        debugButton.textContent = 'DEBUG';

        // Add click handler
        debugButton.addEventListener('click', () => {
            this._showDebugInfo();
        });

        // Add to the document body (not the container, so it's always visible)
        document.body.appendChild(debugButton);
        this.debugButton = debugButton;
    }

    /**
     * Add media controls to the UI
     * @private
     */
    _addMediaControls() {
        // Create media control container
        const mediaControls = document.createElement('div');
        mediaControls.className = 'media-controls';
        mediaControls.style.position = 'fixed';
        mediaControls.style.bottom = '20px';
        mediaControls.style.left = '20px';
        mediaControls.style.zIndex = '1000';
        mediaControls.style.display = 'flex';
        mediaControls.style.gap = '10px';

        // Create microphone button
        const micButton = document.createElement('button');
        micButton.className = 'mic-button';
        micButton.innerHTML = '🎤';
        micButton.style.width = '50px';
        micButton.style.height = '50px';
        micButton.style.borderRadius = '50%';
        micButton.style.backgroundColor = '#fff';
        micButton.style.border = '2px solid #333';
        micButton.style.fontSize = '24px';
        micButton.style.display = 'flex';
        micButton.style.justifyContent = 'center';
        micButton.style.alignItems = 'center';
        micButton.style.cursor = 'pointer';

        // Create camera button
        const cameraButton = document.createElement('button');
        cameraButton.className = 'camera-button';
        cameraButton.innerHTML = '📷';
        cameraButton.style.width = '50px';
        cameraButton.style.height = '50px';
        cameraButton.style.borderRadius = '50%';
        cameraButton.style.backgroundColor = '#fff';
        cameraButton.style.border = '2px solid #333';
        cameraButton.style.fontSize = '24px';
        cameraButton.style.display = 'flex';
        cameraButton.style.justifyContent = 'center';
        cameraButton.style.alignItems = 'center';
        cameraButton.style.cursor = 'pointer';
        cameraButton.style.marginRight = '10px';

        // Add event listeners
        let isStreaming = false;
        let isListening = false;

        // Handle startListening messages from the host
        connectionManager.on('startListening', () => {
            console.log('[MobileController] Received startListening command from host');
            if (!isStreaming) {
                // Start streaming
                if (this.mediaManager && this.mediaManager.startStreaming()) {
                    isStreaming = true;
                    isListening = true;
                    micButton.style.backgroundColor = '#ff4444';
                    cameraButton.disabled = true;
                    cameraButton.style.opacity = '0.5';

                    // Create video preview
                    this.videoPreview = this.mediaManager.createVideoPreview(this.container);

                    // Show status in the status bar
                    if (this.statusBar) {
                        this.statusBar.textContent = 'Listening...';
                        this.statusBar.style.backgroundColor = 'rgba(255, 0, 0, 0.7)';
                    }
                }
            }
        });

        // Handle stopListening messages from the host
        connectionManager.on('stopListening', () => {
            console.log('[MobileController] Received stopListening command from host');
            if (isStreaming) {
                // Stop streaming
                if (this.mediaManager) {
                    this.mediaManager.stopStreaming();
                    isStreaming = false;
                    isListening = false;
                    micButton.style.backgroundColor = '#fff';
                    cameraButton.disabled = false;
                    cameraButton.style.opacity = '1';

                    // Remove video preview
                    if (this.videoPreview && this.videoPreview.parentNode) {
                        this.videoPreview.parentNode.removeChild(this.videoPreview);
                        this.videoPreview = null;
                    }

                    // Update status in the status bar
                    if (this.statusBar) {
                        this.statusBar.textContent = 'Connected to Looking Glass Display';
                        this.statusBar.style.backgroundColor = 'rgba(0, 128, 0, 0.7)';
                    }
                }
            }
        });

        micButton.addEventListener('click', () => {
            if (!isStreaming) {
                // Start streaming
                if (this.mediaManager && this.mediaManager.startStreaming()) {
                    isStreaming = true;
                    isListening = true;
                    micButton.style.backgroundColor = '#ff4444';
                    cameraButton.disabled = true;
                    cameraButton.style.opacity = '0.5';

                    // Create video preview
                    this.videoPreview = this.mediaManager.createVideoPreview(this.container);

                    // Notify the host that we're listening
                    connectionManager.send('mobileListening', { isListening: true });

                    // Show status in the status bar
                    if (this.statusBar) {
                        this.statusBar.textContent = 'Listening...';
                        this.statusBar.style.backgroundColor = 'rgba(255, 0, 0, 0.7)';
                    }
                }
            } else {
                // Stop streaming
                if (this.mediaManager) {
                    this.mediaManager.stopStreaming();
                    isStreaming = false;
                    isListening = false;
                    micButton.style.backgroundColor = '#fff';
                    cameraButton.disabled = false;
                    cameraButton.style.opacity = '1';

                    // Remove video preview
                    if (this.videoPreview && this.videoPreview.parentNode) {
                        this.videoPreview.parentNode.removeChild(this.videoPreview);
                        this.videoPreview = null;
                    }

                    // Notify the host that we've stopped listening
                    connectionManager.send('mobileListening', { isListening: false });

                    // Update status in the status bar
                    if (this.statusBar) {
                        this.statusBar.textContent = 'Connected to Looking Glass Display';
                        this.statusBar.style.backgroundColor = 'rgba(0, 128, 0, 0.7)';
                    }
                }
            }
        });

        // Add buttons to container
        mediaControls.appendChild(cameraButton);
        mediaControls.appendChild(micButton);

        // Add camera button click handler
        cameraButton.addEventListener('click', async () => {
            console.log('[MobileController] Camera button clicked');

            try {
                // Import centralized camera manager
                const { CameraManager } = await import('../../ui/components/CameraManager.js');

                // Create or get existing camera manager instance
                if (!this.cameraManager) {
                    this.cameraManager = new CameraManager();
                    console.log('[MobileController] Created new centralized camera manager');
                }

                // Start camera and show in popup mode for mobile
                await this.cameraManager.startCamera();
                await this.cameraManager.showCamera('popup');

                console.log('[MobileController] Camera opened using centralized system');

            } catch (error) {
                console.error('[MobileController] Failed to open camera:', error);

                // Show error notification if status bar is available
                if (this.statusBar) {
                    const originalText = this.statusBar.textContent;
                    const originalBg = this.statusBar.style.backgroundColor;
                    this.statusBar.textContent = 'Camera failed to open';
                    this.statusBar.style.backgroundColor = 'rgba(255, 0, 0, 0.7)';

                    // Reset after 3 seconds
                    setTimeout(() => {
                        this.statusBar.textContent = originalText;
                        this.statusBar.style.backgroundColor = originalBg;
                    }, 3000);
                }
            }
        });

        // Add container to UI
        this.container.appendChild(mediaControls);
    }

    /**
     * Show debug information
     * @private
     */
    _showDebugInfo() {
        console.log('[MobileController] Showing debug info');

        // Create a debug overlay
        const debugOverlay = document.createElement('div');
        debugOverlay.className = 'debug-overlay';
        debugOverlay.style.position = 'fixed';
        debugOverlay.style.top = '0';
        debugOverlay.style.left = '0';
        debugOverlay.style.width = '100%';
        debugOverlay.style.height = '100%';
        debugOverlay.style.backgroundColor = 'rgba(0, 0, 0, 0.9)';
        debugOverlay.style.color = '#fff';
        debugOverlay.style.padding = '20px';
        debugOverlay.style.boxSizing = 'border-box';
        debugOverlay.style.overflow = 'auto';
        debugOverlay.style.zIndex = '2000';
        debugOverlay.style.fontFamily = 'monospace';
        debugOverlay.style.fontSize = '12px';

        // Add connection status
        const connectionStatus = document.createElement('div');
        connectionStatus.style.marginBottom = '20px';
        connectionStatus.innerHTML = `<strong>Connection Status:</strong> ${this.isConnected ? 'Connected' : 'Disconnected'}<br>`;
        connectionStatus.innerHTML += `<strong>Session ID:</strong> ${this.sessionId || 'None'}<br>`;
        connectionStatus.innerHTML += `<strong>Saved Session ID:</strong> ${localStorage.getItem('mobileControllerSessionId') || 'None'}<br>`;
        connectionStatus.innerHTML += `<strong>WebSocket State:</strong> ${connectionManager.socket ? connectionManager.socket.readyState : 'No Socket'}<br>`;
        connectionStatus.innerHTML += `<strong>WebSocket URL:</strong> ${connectionManager.socket ? connectionManager.socket.url : 'No Socket'}<br>`;

        // Add session info from URL
        const urlParams = new URLSearchParams(window.location.search);
        const urlSessionId = urlParams.get('session');
        connectionStatus.innerHTML += `<strong>URL Session ID:</strong> ${urlSessionId || 'None'}<br>`;

        // Add viewer status
        const viewerStatus = document.createElement('div');
        viewerStatus.style.marginBottom = '20px';
        viewerStatus.innerHTML = `<strong>Viewer Initialized:</strong> ${this.viewer ? 'Yes' : 'No'}<br>`;
        if (this.viewer) {
            viewerStatus.innerHTML += `<strong>Scene Objects:</strong> ${this.viewer.scene ? 'Yes' : 'No'}<br>`;
            viewerStatus.innerHTML += `<strong>Current Mesh:</strong> ${this.viewer.currentMesh ? 'Yes' : 'No'}<br>`;
        }

        // Add UI status
        const uiStatus = document.createElement('div');
        uiStatus.style.marginBottom = '20px';
        uiStatus.innerHTML = `<strong>UI Container:</strong> ${this.uiContainer ? 'Created' : 'Not created'}<br>`;
        uiStatus.innerHTML += `<strong>View Container:</strong> ${this.viewContainer ? 'Created' : 'Not created'}<br>`;
        uiStatus.innerHTML += `<strong>Status Bar:</strong> ${this.statusBar ? 'Created' : 'Not created'}<br>`;
        if (this.statusBar) {
            uiStatus.innerHTML += `<strong>Status Text:</strong> ${this.statusBar.textContent}<br>`;
        }

        // Add action buttons
        const actionButtons = document.createElement('div');
        actionButtons.style.marginTop = '30px';
        actionButtons.style.display = 'flex';
        actionButtons.style.flexDirection = 'column';
        actionButtons.style.gap = '10px';

        // Add reconnect button
        const reconnectButton = document.createElement('button');
        reconnectButton.textContent = 'Reconnect WebSocket';
        reconnectButton.style.padding = '10px';
        reconnectButton.style.backgroundColor = '#9C27B0';
        reconnectButton.style.border = 'none';
        reconnectButton.style.borderRadius = '5px';
        reconnectButton.style.color = 'white';
        reconnectButton.style.cursor = 'pointer';
        reconnectButton.addEventListener('click', async () => {
            console.log('[MobileController] Manually reconnecting WebSocket');
            try {
                // Disconnect first
                connectionManager.disconnect();

                // Wait a moment
                await new Promise(resolve => setTimeout(resolve, 500));

                // Reconnect
                const connected = await connectionManager.connect('controller', this.sessionId);
                console.log('[MobileController] Reconnection result:', connected);

                // Update the debug panel
                this._showDebugInfo();
            } catch (error) {
                console.error('[MobileController] Reconnection error:', error);
                alert('Reconnection failed: ' + error.message);
            }
        });

        // Add request UI state button
        const requestUIButton = document.createElement('button');
        requestUIButton.textContent = 'Request UI State';
        requestUIButton.style.padding = '10px';
        requestUIButton.style.backgroundColor = '#4CAF50';
        requestUIButton.style.border = 'none';
        requestUIButton.style.borderRadius = '5px';
        requestUIButton.style.color = 'white';
        requestUIButton.style.cursor = 'pointer';
        requestUIButton.addEventListener('click', () => {
            console.log('[MobileController] Manually requesting UI state');
            connectionManager.send('requestUIState', {});
        });

        // Add request mesh button
        const requestMeshButton = document.createElement('button');
        requestMeshButton.textContent = 'Request Mesh Info';
        requestMeshButton.style.padding = '10px';
        requestMeshButton.style.backgroundColor = '#2196F3';
        requestMeshButton.style.border = 'none';
        requestMeshButton.style.borderRadius = '5px';
        requestMeshButton.style.color = 'white';
        requestMeshButton.style.cursor = 'pointer';
        requestMeshButton.addEventListener('click', () => {
            console.log('[MobileController] Manually requesting mesh info');
            connectionManager.send('requestMesh', {});
        });

        // Add request camera state button
        const requestCameraButton = document.createElement('button');
        requestCameraButton.textContent = 'Request Camera State';
        requestCameraButton.style.padding = '10px';
        requestCameraButton.style.backgroundColor = '#FF9800';
        requestCameraButton.style.border = 'none';
        requestCameraButton.style.borderRadius = '5px';
        requestCameraButton.style.color = 'white';
        requestCameraButton.style.cursor = 'pointer';
        requestCameraButton.addEventListener('click', () => {
            console.log('[MobileController] Manually requesting camera state');
            connectionManager.send('requestCameraState', {});
        });

        // Add load test mesh button
        const loadTestMeshButton = document.createElement('button');
        loadTestMeshButton.textContent = 'Load Test Mesh';
        loadTestMeshButton.style.padding = '10px';
        loadTestMeshButton.style.backgroundColor = '#E91E63';
        loadTestMeshButton.style.border = 'none';
        loadTestMeshButton.style.borderRadius = '5px';
        loadTestMeshButton.style.color = 'white';
        loadTestMeshButton.style.cursor = 'pointer';
        loadTestMeshButton.addEventListener('click', () => {
            console.log('[MobileController] Manually loading test mesh');

            // Create a test mesh data object
            const testMeshData = {
                hasMesh: true,
                meshId: 'test-cube',
                meshName: 'Test Cube',
                meshType: 'Mesh',
                meshUrl: 'https://threejs.org/examples/models/gltf/cube/cube.gltf'
            };

            // Update the mesh
            this.updateMesh(testMeshData);
        });

        // Add close button
        const closeButton = document.createElement('button');
        closeButton.textContent = 'Close Debug Panel';
        closeButton.style.padding = '10px';
        closeButton.style.backgroundColor = '#f44336';
        closeButton.style.border = 'none';
        closeButton.style.borderRadius = '5px';
        closeButton.style.color = 'white';
        closeButton.style.cursor = 'pointer';
        closeButton.style.marginTop = '20px';
        closeButton.addEventListener('click', () => {
            document.body.removeChild(debugOverlay);
        });

        // Add all elements to the overlay
        debugOverlay.appendChild(connectionStatus);
        debugOverlay.appendChild(viewerStatus);
        debugOverlay.appendChild(uiStatus);
        actionButtons.appendChild(reconnectButton);
        actionButtons.appendChild(requestUIButton);
        actionButtons.appendChild(requestMeshButton);
        actionButtons.appendChild(requestCameraButton);
        actionButtons.appendChild(loadTestMeshButton);
        actionButtons.appendChild(closeButton);
        debugOverlay.appendChild(actionButtons);

        // Add to the document body
        document.body.appendChild(debugOverlay);
    }

    /**
     * Check if the current device is a mobile device
     * @private
     * @returns {boolean} True if the device is a mobile device
     */
    _isMobileDevice() {
        // Check if this is a mobile device using user agent
        const userAgent = navigator.userAgent || window.opera;
        const isMobile = /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(userAgent.toLowerCase());

        // Also check screen size as a fallback
        const isSmallScreen = window.innerWidth <= 768;

        // Check if we're explicitly in controller mode
        const isController = window.location.pathname.includes('/controller') ||
            window.location.search.includes('controller') ||
            document.body.classList.contains('controller-mode');

        return isMobile || isSmallScreen || isController;
    }

    /**
     * Dispose of the controller and clean up event listeners
     */
    dispose() {
        // Remove event listeners
        this.container.removeEventListener('touchstart', this._handleTouchStart);
        this.container.removeEventListener('touchmove', this._handleTouchMove);
        this.container.removeEventListener('touchend', this._handleTouchEnd);
        window.removeEventListener('deviceorientation', this._handleOrientationChange);

        // Dispose of the viewer
        if (this.viewer) {
            this.viewer.dispose();
            this.viewer = null;
        }

        // Dispose of the media manager
        if (this.mediaManager) {
            this.mediaManager.dispose();
            this.mediaManager = null;
        }

        // Dispose of the centralized camera manager
        if (this.cameraManager) {
            this.cameraManager.cleanup();
            this.cameraManager = null;
        }

        // Remove video preview if it exists
        if (this.videoPreview && this.videoPreview.parentNode) {
            this.videoPreview.parentNode.removeChild(this.videoPreview);
            this.videoPreview = null;
        }

        // Remove global reference
        if (window.mobileController === this) {
            delete window.mobileController;
        }

        // Disconnect from the server
        connectionManager.disconnect();

        // Remove UI elements
        if (this.uiContainer && this.uiContainer.parentNode) {
            this.uiContainer.parentNode.removeChild(this.uiContainer);
        }

        // Remove modal if it exists
        if (this.ipModal && this.ipModal.parentNode) {
            this.ipModal.parentNode.removeChild(this.ipModal);
        }

        // Remove debug button if it exists
        if (this.debugButton && this.debugButton.parentNode) {
            this.debugButton.parentNode.removeChild(this.debugButton);
        }

        // Remove any debug overlays
        const debugOverlay = document.querySelector('.debug-overlay');
        if (debugOverlay && debugOverlay.parentNode) {
            debugOverlay.parentNode.removeChild(debugOverlay);
        }
    }
}
