import { Camera } from '../../recognition/camera.js';
import { Modal } from '../components/Modal.js';

export class CameraViewer {
    constructor(app, props = {}) {
        this.app = app;
        this.props = {
            width: props.width || 640,
            height: props.height || 480,
            visible: props.visible || false,
            position: props.position || 'bottom-right',
            modalConfig: props.modalConfig || {
                maxWidth: '90vw',
                maxHeight: '90vh',
                minWidth: '320px',
                minHeight: '180px'
            },
            ...props
        };

        this.container = app.container;
        if (!this.container) {
            console.error('No container provided to CameraViewer');
            this.container = document.body; // Fallback to body
        }

        this.canvas = null;
        this.camera = props.camera || new Camera();
        this.videoElement = null;
        this.videoEventsBound = false;

        // Initialize modal for embedded view
        this.modal = new Modal({
            isOpen: false,
            closeOnBackdropClick: false,
            onClose: () => this.closeViewer(),
            className: 'camera-modal',
            ...this.props.modalConfig
        });

        // Setup dragging state
        this.isDragging = false;
        this.dragOffset = { x: 0, y: 0 };
        this.lastSize = {
            width: `${this.props.width}px`,
            height: `${this.props.height}px`,
            left: '20px',
            top: '20px'
        };

        // State tracking
        this.isStreamActive = false;
        this.popupWindow = null;
        this.popupClosed = true;
        this.streamManager = {
            active: false,
            stream: null,
            lastVideoTrack: null
        };

        // Callbacks
        this.onDeviceCheck = null;
        this.onViewerOpen = null;
        this.onViewerClose = null;

        // Add callback collections for video frames
        this.videoFrameCallbacks = new Set();

        // Add resize observer
        this.resizeObserver = null;

        // Add preference for popup mode
        this.preferPopup = props.preferPopup || false;
    }

    async initialize(videoElement) {
        if (!videoElement) {
            console.warn('[CameraViewer] No video element provided');
            return null;
        }

        // Store video element reference
        this.videoElement = videoElement;

        // Create video container if it doesn't exist
        if (!this.videoContainer) {
            this.videoContainer = document.createElement('div');
            this.videoContainer.className = 'camera-viewer-container';

            // Create wrapper
            const wrapper = document.createElement('div');
            wrapper.className = 'camera-wrapper';
            wrapper.style.position = 'relative';
            wrapper.style.width = '100%';
            wrapper.style.height = '100%';
            wrapper.style.display = 'flex';
            wrapper.style.justifyContent = 'center';
            wrapper.style.alignItems = 'center';
            wrapper.style.overflow = 'hidden';

            // Create canvas with proper properties
            const canvas = document.createElement('canvas');
            canvas.style.position = 'absolute';
            canvas.style.top = '0';
            canvas.style.left = '0';
            canvas.style.width = '100%';
            canvas.style.height = '100%';
            canvas.style.pointerEvents = 'none';

            // Set initial dimensions matching video
            if (this.videoElement) {
                canvas.width = this.videoElement.videoWidth || this.props.width;
                canvas.height = this.videoElement.videoHeight || this.props.height;
            }
            this.canvas = canvas;
            wrapper.appendChild(canvas);

            // Add controls for popup window
            const controls = document.createElement('div');
            controls.className = 'camera-controls';
            controls.style.cssText = `
                position: absolute;
                top: 10px;
                right: 10px;
                display: flex;
                gap: 5px;
                z-index: 1000;
            `;

            // Add popup button
            const popupButton = document.createElement('button');
            popupButton.className = 'camera-popup-button';
            popupButton.innerHTML = `
                <svg viewBox="0 0 24 24" width="18" height="18">
                    <path fill="currentColor" d="M19 19H5V5h7V3H5a2 2 0 00-2 2v14a2 2 0 002 2h14a2 2 0 002-2v-7h-2v7zM14 3v2h3.59l-7.83 7.83 1.41 1.41L19 6.41V10h2V3h-7z"/>
                </svg>
            `;
            popupButton.style.cssText = `
                width: 30px;
                height: 30px;
                background: rgba(0,0,0,0.5);
                border: none;
                border-radius: 50%;
                color: white;
                cursor: pointer;
                display: flex;
                align-items: center;
                justify-content: center;
            `;
            popupButton.addEventListener('click', () => this.openPopupWindow());
            controls.appendChild(popupButton);
            wrapper.appendChild(controls);

            // Add video element to wrapper
            if (this.videoElement) {
                this.videoElement.style.width = '100%';
                this.videoElement.style.height = '100%';
                this.videoElement.style.objectFit = 'cover';
                wrapper.appendChild(this.videoElement);
            }

            this.videoContainer.appendChild(wrapper);

            // Set modal content
            this.modal.setContent(this.videoContainer);

            // Mount modal to document body if not already mounted
            if (!document.body.contains(this.modal.element)) {
                document.body.appendChild(this.modal.element);
            }

            // Make modal draggable
            this.setupDraggableModal();
        }

        // Setup video metadata handler
        if (this.videoElement && !this.videoEventsBound) {
            this.videoElement.addEventListener('loadedmetadata', () => {
                console.log('[CameraViewer] Video metadata loaded:', {
                    videoWidth: this.videoElement.videoWidth,
                    videoHeight: this.videoElement.videoHeight,
                    readyState: this.videoElement.readyState
                });

                if (this.canvas) {
                    this.canvas.width = this.videoElement.videoWidth || this.props.width;
                    this.canvas.height = this.videoElement.videoHeight || this.props.height;
                }
            });

            this.videoEventsBound = true;
        }

        // If preferPopup is true, automatically open popup when initialized
        if (this.preferPopup && videoElement && videoElement.readyState >= 2) {
            setTimeout(() => this.openPopupWindow(), 500);
        }

        return this.canvas;
    }

    async openViewer() {
        console.log('[CameraViewer] Opening viewer - checking popup preference');

        // If preferPopup is true, directly open popup window instead of modal
        if (this.preferPopup) {
            this.openPopupWindow();
            return;
        }

        console.log('[CameraViewer] Opening viewer');
        try {
            // Ensure canvas is initialized
            if (!this.canvas) {
                await this.initialize(this.videoElement);
            }

            if (!this.canvas || !this.videoElement) {
                throw new Error('[CameraViewer] Canvas or video element not properly initialized');
            }

            // Reset modal content position and size
            const modalContent = this.modal.element.querySelector('.modal-content');
            Object.assign(modalContent.style, this.lastSize);

            // Ensure video has active stream
            if (this.streamManager.stream) {
                console.log('[CameraViewer] Reconnecting existing stream');
                this.videoElement.srcObject = this.streamManager.stream;
                try {
                    await this.videoElement.play();
                    console.log('[CameraViewer] Video playback resumed');
                } catch (err) {
                    console.error('[CameraViewer] Error resuming video:', err);
                }
            }

            // Always ensure video is in wrapper and reset the styles used for hiding
            this.videoElement.style.position = '';
            this.videoElement.style.left = '';
            this.videoElement.style.visibility = '';

            // Create and store ResizeObserver for canvas scaling
            this.setupResizeObserver();

            // Open the modal and trigger callbacks
            this.modal.open();
            console.log('[CameraViewer] Modal opened, triggering callbacks');

            // Execute onViewerOpen callback with canvas
            if (this.onViewerOpen) {
                console.log('[CameraViewer] Executing onViewerOpen callback');
                this.onViewerOpen(this.canvas);
            }
        } catch (error) {
            console.error('[CameraViewer] Error opening camera:', error);
            alert('Failed to open camera. Please check camera permissions.');
        }
    }

    setupResizeObserver() {
        // Clean up previous observer if exists
        if (this.resizeObserver) {
            this.resizeObserver.disconnect();
            this.resizeObserver = null;
        }

        // Create new observer for the video container
        this.resizeObserver = new ResizeObserver(entries => {
            for (const entry of entries) {
                const { width, height } = entry.contentRect;
                console.log('[CameraViewer] Container resized:', { width, height });

                // Only update canvas if video and canvas exist
                if (this.canvas && this.videoElement && this.videoElement.readyState >= 2) {
                    const videoWidth = this.videoElement.videoWidth || width;
                    const videoHeight = this.videoElement.videoHeight || height;

                    if (this.canvas.width !== videoWidth || this.canvas.height !== videoHeight) {
                        this.canvas.width = videoWidth;
                        this.canvas.height = videoHeight;
                        console.log('[CameraViewer] Canvas dimensions updated:', {
                            width: this.canvas.width,
                            height: this.canvas.height
                        });
                    }
                }
            }
        });

        // Start observing
        if (this.videoContainer) {
            this.resizeObserver.observe(this.videoContainer);
        }
    }

    closeViewer() {
        // Cleanup ResizeObserver first
        if (this.resizeObserver) {
            this.resizeObserver.disconnect();
            this.resizeObserver = null;
        }

        console.log('[CameraViewer] Closing viewer, stream active:', this.streamManager.active);

        // Save current modal size and position
        const modalContent = this.modal.element.querySelector('.modal-content');
        this.lastSize = {
            width: modalContent.style.width,
            height: modalContent.style.height,
            left: modalContent.style.left,
            top: modalContent.style.top
        };

        // IMPORTANT: Keep video element connected but make it invisible
        // instead of removing it from DOM
        if (this.videoElement && this.videoElement.parentNode) {
            // Store stream reference if not already stored
            if (this.videoElement.srcObject && !this.streamManager.stream) {
                this.streamManager.stream = this.videoElement.srcObject;
                this.streamManager.active = true;
                const videoTrack = this.streamManager.stream.getVideoTracks()[0];
                if (videoTrack) {
                    this.streamManager.lastVideoTrack = videoTrack;
                }
            }

            // Instead of removing from DOM, make it invisible
            this.videoElement.style.position = 'absolute';
            this.videoElement.style.left = '-9999px';
            this.videoElement.style.visibility = 'hidden';

            // Keep video playing in background
            if (this.videoElement.paused) {
                this.videoElement.play().catch(err =>
                    console.warn('[CameraViewer] Could not keep video playing:', err)
                );
            }
        }

        this.modal.close();

        // Notify callback after ensuring state is saved
        if (this.onViewerClose) {
            this.onViewerClose();
        }

        console.log('[CameraViewer] Viewer closed, stream state:', {
            active: this.streamManager.active,
            hasStream: !!this.streamManager.stream,
            hasVideoTrack: !!this.streamManager.lastVideoTrack
        });
    }

    openPopupWindow() {
        // Close existing popup if it exists
        if (this.popupWindow && !this.popupWindow.closed) {
            this.popupWindow.close();
        }

        // Calculate a reasonable size for the popup
        const width = Math.max(640, this.props.width * 1.5);
        const height = Math.max(480, this.props.height * 1.5);

        // Open a new popup window
        this.popupWindow = window.open('', 'Camera Viewer',
            `width=${width},height=${height},resizable=yes,scrollbars=no,status=no`);

        if (!this.popupWindow) {
            console.error('[CameraViewer] Failed to open popup window (likely blocked by browser)');
            alert('Popup window was blocked. Please allow popups for this site.');
            return;
        }

        // Set up popup window content
        this.popupWindow.document.title = 'Camera Viewer';
        this.popupWindow.document.body.style.margin = '0';
        this.popupWindow.document.body.style.padding = '0';
        this.popupWindow.document.body.style.overflow = 'hidden';
        this.popupWindow.document.body.style.backgroundColor = '#000';

        // Add HTML content to popup
        this.popupWindow.document.body.innerHTML = `
            <div class="camera-popup">
                <video class="camera-popup-video" autoplay playsinline muted></video>
                <canvas class="camera-popup-canvas"></canvas>
                <div class="camera-popup-controls">
                    <button class="camera-popup-button debug-toggle">Toggle Debug</button>
                    <button class="camera-popup-button close-button">Close</button>
                </div>
                <div class="camera-popup-status">Initializing camera...</div>
            </div>
        `;

        // Add styles to popup
        const popupStyle = this.popupWindow.document.createElement('style');
        popupStyle.textContent = `
            body, html {
                margin: 0;
                padding: 0;
                width: 100%;
                height: 100%;
                overflow: hidden;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            }
            
            .camera-popup {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: #000;
            }
            
            .camera-popup-video {
                width: 100%;
                height: 100%;
                object-fit: contain;
            }
            
            .camera-popup-canvas {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                pointer-events: none;
            }
            
            .camera-popup-controls {
                position: absolute;
                top: 10px;
                right: 10px;
                display: flex;
                gap: 10px;
                z-index: 1000;
            }
            
            .camera-popup-button {
                padding: 8px 12px;
                background: rgba(0,0,0,0.7);
                color: white;
                border: none;
                border-radius: 4px;
                cursor: pointer;
                font-size: 14px;
            }
            
            .camera-popup-button:hover {
                background: rgba(0,0,0,0.9);
            }
            
            .camera-popup-status {
                position: absolute;
                bottom: 20px;
                left: 50%;
                transform: translateX(-50%);
                color: white;
                background: rgba(0,0,0,0.5);
                padding: 5px 10px;
                border-radius: 4px;
                font-size: 14px;
            }
        `;
        this.popupWindow.document.head.appendChild(popupStyle);

        // Get references to popup elements
        const popupVideo = this.popupWindow.document.querySelector('.camera-popup-video');
        const popupCanvas = this.popupWindow.document.querySelector('.camera-popup-canvas');
        const debugToggle = this.popupWindow.document.querySelector('.debug-toggle');
        const closeButton = this.popupWindow.document.querySelector('.close-button');
        const statusElement = this.popupWindow.document.querySelector('.camera-popup-status');

        // Add close button handler
        closeButton.addEventListener('click', () => {
            this.popupWindow.close();
        });

        // Helper function to update status
        const updateStatus = (message) => {
            if (statusElement && !this.popupWindow.closed) {
                statusElement.textContent = message;
            }
        };

        // Try to get active stream from various sources
        let sourceStream = null;

        if (this.streamManager && this.streamManager.active && this.streamManager.stream) {
            sourceStream = this.streamManager.stream;
            console.log('[CameraViewer] Using existing active stream from streamManager');
        } else if (this.videoElement && this.videoElement.srcObject) {
            sourceStream = this.videoElement.srcObject;
            console.log('[CameraViewer] Using existing stream from videoElement');
        }

        // If we have a stream, connect it to the popup video
        if (sourceStream) {
            try {
                updateStatus('Connecting to camera...');
                // Try to clone the stream for the popup
                try {
                    const clonedStream = sourceStream.clone();
                    popupVideo.srcObject = clonedStream;
                } catch (cloneErr) {
                    console.warn('[CameraViewer] Failed to clone stream:', cloneErr);
                    popupVideo.srcObject = sourceStream;
                }

                // Play the video
                popupVideo.play()
                    .then(() => {
                        console.log('[CameraViewer] Popup video started playing');
                        updateStatus('Camera connected');

                        // Store stream reference in streamManager
                        if (!this.streamManager.active) {
                            this.streamManager.stream = sourceStream;
                            this.streamManager.active = true;
                        }

                        // Hide status after a delay
                        setTimeout(() => {
                            if (statusElement && !this.popupWindow.closed) {
                                statusElement.style.opacity = '0';
                                statusElement.style.transition = 'opacity 0.5s';
                            }
                        }, 2000);
                    })
                    .catch(err => {
                        console.error('[CameraViewer] Popup video play error:', err);
                        updateStatus('Error starting video playback');
                    });

                // Set initial canvas dimensions
                popupCanvas.width = this.videoElement.videoWidth || 640;
                popupCanvas.height = this.videoElement.videoHeight || 480;
            } catch (error) {
                console.error('[CameraViewer] Failed to set popup video source:', error);
                updateStatus('Error connecting to camera: ' + error.message);
            }
        } else {
            // No stream available - show status
            updateStatus('Camera not available. Please open camera first.');
            console.warn('[CameraViewer] No stream available for popup');
        }

        // Add debug toggle functionality
        let debugMode = false;
        debugToggle.addEventListener('click', () => {
            debugMode = !debugMode;
            debugToggle.textContent = debugMode ? 'Debug: ON' : 'Debug: OFF';

            // Toggle debug visualization in app
            if (this.app && this.app.toggleDebugMode) {
                this.app.toggleDebugMode();
            } else if (this.app && this.app.gestureController) {
                this.app.gestureController.toggleDebugMode();
            }

            // Show canvas when debug is on
            if (popupCanvas) {
                popupCanvas.style.display = debugMode ? 'block' : 'none';
            }
        });

        // Handle popup close
        this.popupWindow.addEventListener('beforeunload', () => {
            this.popupClosed = true;
            console.log('[CameraViewer] Popup window closed');

            // Start video frame loop if it's not running
            this.startVideoFrameLoop();
        });

        this.popupClosed = false;
        console.log('[CameraViewer] Popup window opened');

        // Start processing video frames
        this.startVideoFrameLoop();

        return this.popupWindow;
    }

    /**
     * Start the camera by requesting user permission and connecting video stream
     * @returns {Promise<MediaStream>} Promise resolving to the media stream
     */
    async startCamera() {
        try {
            console.log('[CameraViewer] Starting camera...');

            // Check if we already have an active stream
            if (this.streamManager.active && this.streamManager.stream) {
                console.log('[CameraViewer] Reusing existing stream');
                return this.streamManager.stream;
            }

            // Request camera access
            const stream = await navigator.mediaDevices.getUserMedia({
                video: {
                    width: { ideal: 640 },
                    height: { ideal: 480 },
                    facingMode: 'user'
                },
                audio: false
            });

            console.log('[CameraViewer] Camera access granted');

            // Connect stream to video element
            if (this.videoElement) {
                this.videoElement.srcObject = stream;

                // Play video even if hidden
                try {
                    await this.videoElement.play();
                    console.log('[CameraViewer] Video playback started');
                } catch (playError) {
                    console.warn('[CameraViewer] Video play error:', playError);
                }
            }

            // Store stream in manager
            this.streamManager.stream = stream;
            this.streamManager.active = true;

            // Start video frame loop
            this.startVideoFrameLoop();

            return stream;
        } catch (error) {
            console.error('[CameraViewer] Error starting camera:', error);
            this.streamManager.active = false;
            this.streamManager.stream = null;
            throw error;
        }
    }

    setupDraggableModal() {
        console.log('[CameraViewer] Setting up draggable modal');
        const modalContent = this.modal.element.querySelector('.modal-content');
        // Use modal header if it exists; otherwise, fallback to modal content
        const dragElement = this.modal.element.querySelector('.modal-header') || modalContent;
        let startX, startY;

        const dragStart = (e) => {
            if (e.target.closest('.modal-close')) return;

            console.log('[CameraViewer] Drag start');
            this.isDragging = true;
            startX = e.clientX - modalContent.offsetLeft;
            startY = e.clientY - modalContent.offsetTop;
            dragElement.style.cursor = 'grabbing';
        };

        const drag = (e) => {
            if (!this.isDragging) return;
            e.preventDefault();

            const newX = e.clientX - startX;
            const newY = e.clientY - startY;

            // Constrain to viewport
            const maxX = window.innerWidth - modalContent.offsetWidth;
            const maxY = window.innerHeight - modalContent.offsetHeight;

            modalContent.style.left = `${Math.max(0, Math.min(maxX, newX))}px`;
            modalContent.style.top = `${Math.max(0, Math.min(maxY, newY))}px`;
        };

        const dragEnd = () => {
            if (this.isDragging) {
                console.log('[CameraViewer] Drag end');
                this.isDragging = false;
                dragElement.style.cursor = 'grab';
            }
        };

        dragElement.addEventListener('mousedown', dragStart);
        window.addEventListener('mousemove', drag);
        window.addEventListener('mouseup', dragEnd);
    }

    setViewerCallbacks(onOpen, onClose) {
        this.onViewerOpen = onOpen;
        this.onViewerClose = onClose;
    }

    setDebugOverlay(enabled) {
        console.log('[CameraViewer] Setting debug overlay:', enabled);
        if (!this.canvas) {
            console.warn('[CameraViewer] Canvas not initialized for debugging overlay. Please initialize first.');
            return;
        }

        // Update canvas style based on debug state
        if (this.canvas) {
            if (enabled) {
                this.canvas.style.opacity = '1';
                this.canvas.style.visibility = 'visible';
            } else {
                this.canvas.style.opacity = '0';
                this.canvas.style.visibility = 'hidden';
            }
        }

        // Update debug state
        this.debugOverlayEnabled = enabled;
    }

    // Video frame processing methods from the simpler implementation
    startVideoFrameLoop() {
        if (this.videoFrameLoopId) {
            cancelAnimationFrame(this.videoFrameLoopId);
        }

        const processFrame = () => {
            if (!this.streamManager.active || !this.videoElement) {
                return;
            }

            const timestamp = performance.now();

            // Notify all registered callbacks
            if (this.videoElement.readyState === 4) {
                this.videoFrameCallbacks.forEach(callback => {
                    try {
                        callback(this.videoElement, timestamp);
                    } catch (error) {
                        console.error('[CameraViewer] Error in video frame callback:', error);
                    }
                });
            }

            // Continue the loop
            this.videoFrameLoopId = requestAnimationFrame(processFrame);
        };

        this.videoFrameLoopId = requestAnimationFrame(processFrame);
    }

    /**
     * Register a callback for video frames
     * @param {Function} callback Function to call for each video frame
     * @returns {Function} Function to call to unregister the callback
     */
    onVideoFrame(callback) {
        if (typeof callback === 'function') {
            this.videoFrameCallbacks.add(callback);
            return () => this.videoFrameCallbacks.delete(callback);
        }
        return null;
    }

    dispose() {
        // Clean up resize observer
        if (this.resizeObserver) {
            this.resizeObserver.disconnect();
            this.resizeObserver = null;
        }

        // Stop video frame loop if active
        if (this.videoFrameLoopId) {
            cancelAnimationFrame(this.videoFrameLoopId);
            this.videoFrameLoopId = null;
        }

        // Clean up stream
        if (this.streamManager.stream) {
            const tracks = this.streamManager.stream.getTracks();
            tracks.forEach(track => track.stop());
            this.streamManager.active = false;
            this.streamManager.stream = null;
        }

        // Clean up video events
        this.videoEventsBound = false;

        // Close popup if open
        if (this.popupWindow && !this.popupWindow.closed) {
            this.popupWindow.close();
            this.popupWindow = null;
        }

        // Clear all callbacks
        this.videoFrameCallbacks.clear();
    }

    /**
     * Returns the canvas element for direct access
     */
    get_canvas_element() {
        if (!this.canvas) {
            throw new Error('[CameraViewer] Canvas element not found or not initialized');
        }
        return this.canvas;
    }
}