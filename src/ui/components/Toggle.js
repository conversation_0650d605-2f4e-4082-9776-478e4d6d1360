export class Toggle {
    constructor({ checked = false, label = '', activeColor = '#007AFF', size = 'medium' } = {}) {
        this.element = this.createToggle({ checked, label, activeColor, size });
        this.onChangeCallback = null;
    }

    createToggle({ checked, label, activeColor, size }) {
        const container = document.createElement('label');
        container.className = `toggle-container toggle-${size}`;

        // Create toggle input
        const input = document.createElement('input');
        input.type = 'checkbox';
        input.className = 'toggle-input';
        input.checked = checked;

        // Create toggle switch
        const switch_ = document.createElement('span');
        switch_.className = 'toggle-switch';

        // Create label text if provided
        if (label) {
            const labelText = document.createElement('span');
            labelText.className = 'toggle-label';
            labelText.textContent = label;
            container.appendChild(labelText);
        }

        // Add event listener
        input.addEventListener('change', () => {
            if (this.onChangeCallback) {
                this.onChangeCallback(input.checked);
            }
        });

        // Add styles
        if (!document.getElementById('toggle-styles')) {
            const style = document.createElement('style');
            style.id = 'toggle-styles';
            style.textContent = `
                .toggle-container {
                    display: inline-flex;
                    align-items: center;
                    cursor: pointer;
                    font-family: Arial, sans-serif;
                }

                .toggle-input {
                    display: none;
                }

                .toggle-switch {
                    position: relative;
                    display: inline-block;
                    width: 36px;
                    height: 20px;
                    background-color: #ccc;
                    border-radius: 20px;
                    transition: all 0.3s;
                }

                .toggle-switch::after {
                    content: '';
                    position: absolute;
                    width: 16px;
                    height: 16px;
                    border-radius: 50%;
                    background-color: white;
                    top: 2px;
                    left: 2px;
                    transition: all 0.3s;
                }

                .toggle-input:checked + .toggle-switch {
                    background-color: ${activeColor};
                }

                .toggle-input:checked + .toggle-switch::after {
                    transform: translateX(16px);
                }

                .toggle-input:disabled + .toggle-switch {
                    opacity: 0.6;
                    cursor: not-allowed;
                }

                .toggle-label {
                    margin-left: 8px;
                    font-size: 14px;
                    user-select: none;
                }

                /* Sizes */
                .toggle-small .toggle-switch {
                    width: 28px;
                    height: 16px;
                }

                .toggle-small .toggle-switch::after {
                    width: 12px;
                    height: 12px;
                }

                .toggle-small .toggle-input:checked + .toggle-switch::after {
                    transform: translateX(12px);
                }

                .toggle-large .toggle-switch {
                    width: 48px;
                    height: 26px;
                }

                .toggle-large .toggle-switch::after {
                    width: 22px;
                    height: 22px;
                }

                .toggle-large .toggle-input:checked + .toggle-switch::after {
                    transform: translateX(22px);
                }

                .toggle-small .toggle-label {
                    font-size: 12px;
                }

                .toggle-large .toggle-label {
                    font-size: 16px;
                }
            `;
            document.head.appendChild(style);
        }

        container.appendChild(input);
        container.appendChild(switch_);

        return container;
    }

    onChange(callback) {
        this.onChangeCallback = callback;
        return this;
    }

    setChecked(checked) {
        const input = this.element.querySelector('input');
        input.checked = checked;
        if (this.onChangeCallback) {
            this.onChangeCallback(checked);
        }
        return this;
    }

    getChecked() {
        const input = this.element.querySelector('input');
        return input.checked;
    }

    setDisabled(disabled) {
        const input = this.element.querySelector('input');
        input.disabled = disabled;
        this.element.style.cursor = disabled ? 'not-allowed' : 'pointer';
        return this;
    }

    setLabel(label) {
        let labelElement = this.element.querySelector('.toggle-label');
        if (!labelElement && label) {
            labelElement = document.createElement('span');
            labelElement.className = 'toggle-label';
            this.element.appendChild(labelElement);
        }
        if (labelElement) {
            labelElement.textContent = label;
        }
        return this;
    }

    mount(container) {
        container.appendChild(this.element);
        return this;
    }
} 