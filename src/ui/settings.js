import { CONFIG } from '../config.js';

export class Settings {
    constructor() {
        this.panel = this.createPanel();
        this.visible = false;
    }

    createPanel() {
        const panel = document.createElement('div');
        panel.id = 'settings-panel';
        panel.style.display = 'none';

        this.addSettings(panel);
        document.body.appendChild(panel);

        return panel;
    }

    addSettings(panel) {
        // Hand tracking settings
        this.addSlider(panel, 'Model Complexity', 'modelComplexity',
            CONFIG.HAND_TRACKING.modelComplexity, 0, 1, 1);

        this.addSlider(panel, 'Detection Confidence', 'detectionConfidence',
            CONFIG.HAND_TRACKING.minDetectionConfidence, 0, 1, 0.1);

        // Debug settings
        this.addCheckbox(panel, 'Show Hand Mesh', 'showHandMesh',
            CONFIG.DEBUG.SHOW_HAND_MESH);

        this.addCheckbox(panel, 'Show Landmarks', 'showLandmarks',
            CONFIG.DEBUG.SHOW_LANDMARKS);

        // Audio settings
        this.addCheckbox(panel, 'Spatial Audio', 'spatialAudio',
            CONFIG.AUDIO.SPATIAL_AUDIO_ENABLED);
    }

    addSlider(panel, label, id, value, min, max, step) {
        const container = document.createElement('div');
        container.className = 'setting-item';

        const labelElement = document.createElement('label');
        labelElement.textContent = label;
        labelElement.htmlFor = id;

        const slider = document.createElement('input');
        slider.type = 'range';
        slider.id = id;
        slider.min = min;
        slider.max = max;
        slider.step = step;
        slider.value = value;

        container.appendChild(labelElement);
        container.appendChild(slider);
        panel.appendChild(container);

        return slider;
    }

    addCheckbox(panel, label, id, checked) {
        const container = document.createElement('div');
        container.className = 'setting-item';

        const checkbox = document.createElement('input');
        checkbox.type = 'checkbox';
        checkbox.id = id;
        checkbox.checked = checked;

        const labelElement = document.createElement('label');
        labelElement.textContent = label;
        labelElement.htmlFor = id;

        container.appendChild(checkbox);
        container.appendChild(labelElement);
        panel.appendChild(container);

        return checkbox;
    }

    toggle() {
        this.visible = !this.visible;
        this.panel.style.display = this.visible ? 'block' : 'none';
    }
} 