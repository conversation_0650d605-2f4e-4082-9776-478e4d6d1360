import * as THREE from 'three';

export class HandVisualizer {
    constructor(scene) {
        this.scene = scene;
        this.handMesh = null;
        this.joints = [];
        this.connections = [];
        this.initializeHandMesh();
    }

    initializeHandMesh() {
        // Create spheres for joints
        const jointGeometry = new THREE.SphereGeometry(0.03, 8, 8);
        const jointMaterial = new THREE.MeshPhongMaterial({
            color: 0x00ff00,
            transparent: true,
            opacity: 0.7
        });

        // Create 21 joints (MediaPipe hand landmarks)
        for (let i = 0; i < 21; i++) {
            const joint = new THREE.Mesh(jointGeometry, jointMaterial.clone());
            this.joints.push(joint);
            this.scene.add(joint);
        }

        // Create connections between joints
        const connectionGeometry = new THREE.CylinderGeometry(0.01, 0.01, 1, 8);
        connectionGeometry.rotateX(Math.PI / 2);
        const connectionMaterial = new THREE.MeshPhongMaterial({
            color: 0x00ff00,
            transparent: true,
            opacity: 0.5
        });

        // Define hand connections
        const connections = [
            [0, 1], [1, 2], [2, 3], [3, 4],           // Thumb
            [0, 5], [5, 6], [6, 7], [7, 8],           // Index
            [0, 9], [9, 10], [10, 11], [11, 12],      // Middle
            [0, 13], [13, 14], [14, 15], [15, 16],    // Ring
            [0, 17], [17, 18], [18, 19], [19, 20]     // Pinky
        ];

        connections.forEach(([start, end]) => {
            const connection = new THREE.Mesh(connectionGeometry, connectionMaterial.clone());
            this.connections.push({
                mesh: connection,
                start,
                end
            });
            this.scene.add(connection);
        });
    }

    updateHandVisualization(landmarks) {
        if (!landmarks) return;

        // Update joint positions
        landmarks.forEach((landmark, i) => {
            const joint = this.joints[i];
            joint.position.set(
                landmark.x * 2 - 1,
                -(landmark.y * 2 - 1),
                -landmark.z
            );
        });

        // Update connections
        this.connections.forEach(connection => {
            const start = this.joints[connection.start].position;
            const end = this.joints[connection.end].position;

            // Position
            connection.mesh.position.copy(start);

            // Orientation
            const direction = end.clone().sub(start);
            const length = direction.length();

            connection.mesh.scale.set(1, length, 1);
            connection.mesh.lookAt(end);
        });
    }

    setVisibility(visible) {
        this.joints.forEach(joint => {
            joint.visible = visible;
        });
        this.connections.forEach(connection => {
            connection.mesh.visible = visible;
        });
    }

    setColor(color) {
        this.joints.forEach(joint => {
            joint.material.color.set(color);
        });
        this.connections.forEach(connection => {
            connection.mesh.material.color.set(color);
        });
    }
} 