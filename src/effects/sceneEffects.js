import * as THREE from 'three';

/**
 * Base class for all effects
 */
export class BaseEffect {
    constructor() {
        this.isActive = true;
    }

    update(object, time) {
        // Base update method to be overridden by child classes
    }

    enable() {
        this.isActive = true;
    }

    disable() {
        this.isActive = false;
    }

    toggle() {
        this.isActive = !this.isActive;
    }
}

export class SceneEffects {
    constructor(scene) {
        this.scene = scene;
        this.effects = new Map();
        this.particles = [];
        this.initEffects();
    }

    initEffects() {
        // Punch impact effect
        const impactGeometry = new THREE.SphereGeometry(0.1, 8, 8);
        const impactMaterial = new THREE.MeshBasicMaterial({
            color: 0xffff00,
            transparent: true,
            opacity: 0.8
        });
        this.effects.set('impact', {
            geometry: impactGeometry,
            material: impactMaterial,
            pool: []
        });

        // Initialize particle system
        this.initParticleSystem();
    }

    initParticleSystem() {
        const particleGeometry = new THREE.BufferGeometry();
        const particleCount = 1000;

        const positions = new Float32Array(particleCount * 3);
        const velocities = new Float32Array(particleCount * 3);
        const colors = new Float32Array(particleCount * 3);

        for (let i = 0; i < particleCount; i++) {
            positions[i * 3] = 0;
            positions[i * 3 + 1] = 0;
            positions[i * 3 + 2] = 0;

            velocities[i * 3] = (Math.random() - 0.5) * 0.3;
            velocities[i * 3 + 1] = Math.random() * 0.2;
            velocities[i * 3 + 2] = (Math.random() - 0.5) * 0.3;

            colors[i * 3] = Math.random();
            colors[i * 3 + 1] = Math.random();
            colors[i * 3 + 2] = Math.random();
        }

        particleGeometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
        particleGeometry.setAttribute('velocity', new THREE.BufferAttribute(velocities, 3));
        particleGeometry.setAttribute('color', new THREE.BufferAttribute(colors, 3));

        const particleMaterial = new THREE.PointsMaterial({
            size: 0.05,
            vertexColors: true,
            transparent: true,
            opacity: 0.8
        });

        this.particleSystem = new THREE.Points(particleGeometry, particleMaterial);
        this.scene.add(this.particleSystem);
    }

    createPunchImpact(position) {
        const impact = this.getEffectFromPool('impact');
        impact.position.copy(position);
        impact.scale.set(0.1, 0.1, 0.1);
        impact.material.opacity = 1;

        this.scene.add(impact);

        // Animate impact
        const animate = () => {
            impact.scale.multiplyScalar(1.2);
            impact.material.opacity *= 0.9;

            if (impact.material.opacity > 0.01) {
                requestAnimationFrame(animate);
            } else {
                this.scene.remove(impact);
                this.returnEffectToPool('impact', impact);
            }
        };

        animate();
        this.emitParticles(position, 20);
    }

    emitParticles(position, count) {
        const positions = this.particleSystem.geometry.attributes.position;
        const velocities = this.particleSystem.geometry.attributes.velocity;
        const colors = this.particleSystem.geometry.attributes.color;

        for (let i = 0; i < count; i++) {
            const index = Math.floor(Math.random() * positions.count) * 3;

            positions.array[index] = position.x;
            positions.array[index + 1] = position.y;
            positions.array[index + 2] = position.z;

            velocities.array[index] = (Math.random() - 0.5) * 0.3;
            velocities.array[index + 1] = Math.random() * 0.2;
            velocities.array[index + 2] = (Math.random() - 0.5) * 0.3;
        }

        positions.needsUpdate = true;
        velocities.needsUpdate = true;
    }

    updateParticles() {
        const positions = this.particleSystem.geometry.attributes.position;
        const velocities = this.particleSystem.geometry.attributes.velocity;

        for (let i = 0; i < positions.count; i++) {
            const idx = i * 3;

            positions.array[idx] += velocities.array[idx];
            positions.array[idx + 1] += velocities.array[idx + 1];
            positions.array[idx + 2] += velocities.array[idx + 2];

            // Apply gravity
            velocities.array[idx + 1] -= 0.01;
        }

        positions.needsUpdate = true;
    }

    getEffectFromPool(type) {
        const effect = this.effects.get(type);
        if (effect.pool.length > 0) {
            return effect.pool.pop();
        }
        return new THREE.Mesh(effect.geometry, effect.material.clone());
    }

    returnEffectToPool(type, mesh) {
        const effect = this.effects.get(type);
        effect.pool.push(mesh);
    }
} 