import * as THREE from 'three';
import { BaseEffect } from './sceneEffects';

/**
 * Floating effect for models
 * Creates a smooth floating animation with configurable rotation and position
 */
export class FloatingEffect extends BaseEffect {
    constructor(params = {}) {
        super();
        this.params = {
            rotation: {
                enabled: {
                    x: params.rotation?.enabled?.x ?? false,
                    y: params.rotation?.enabled?.y ?? true,
                    z: params.rotation?.enabled?.z ?? true
                },
                speed: {
                    x: params.rotation?.speed?.x ?? 4,
                    y: params.rotation?.speed?.y ?? 4,
                    z: params.rotation?.speed?.z ?? 1.5
                },
                amplitude: {
                    x: params.rotation?.amplitude?.x ?? 8,
                    y: params.rotation?.amplitude?.y ?? 8,
                    z: params.rotation?.amplitude?.z ?? 20
                },
                offset: {
                    x: params.rotation?.offset?.x ?? -Math.PI / 1.75,
                    y: params.rotation?.offset?.y ?? 0,
                    z: params.rotation?.offset?.z ?? 1
                }
            },
            position: {
                enabled: {
                    x: params.position?.enabled?.x ?? false,
                    y: params.position?.enabled?.y ?? true,
                    z: params.position?.enabled?.z ?? false
                },
                speed: {
                    x: params.position?.speed?.x ?? 1.5,
                    y: params.position?.speed?.y ?? 1.5,
                    z: params.position?.speed?.z ?? 1.5
                },
                amplitude: {
                    x: params.position?.amplitude?.x ?? 10,
                    y: params.position?.amplitude?.y ?? 10,
                    z: params.position?.amplitude?.z ?? 10
                }
            }
        };
    }

    update(object, time) {
        const { rotation, position } = this.params;

        // Apply rotation animations
        if (rotation.enabled.x) {
            object.rotation.x = rotation.offset.x + 
                Math.cos(time / rotation.speed.x) / rotation.amplitude.x;
        }
        if (rotation.enabled.y) {
            object.rotation.y = rotation.offset.y + 
                Math.sin(time / rotation.speed.y) / rotation.amplitude.y;
        }
        if (rotation.enabled.z) {
            object.rotation.z = rotation.offset.z + 
                Math.sin(time / rotation.speed.z) / rotation.amplitude.z;
        }

        // Apply position animations
        if (position.enabled.x) {
            object.position.x = (1 + Math.sin(time / position.speed.x)) / position.amplitude.x;
        }
        if (position.enabled.y) {
            object.position.y = (1 + Math.sin(time / position.speed.y)) / position.amplitude.y;
        }
        if (position.enabled.z) {
            object.position.z = (1 + Math.sin(time / position.speed.z)) / position.amplitude.z;
        }
    }
}

/**
 * Trackball effect for Looking Glass display
 * Controls the view rotation for holographic display
 */
export class TrackballEffect extends BaseEffect {
    constructor(params = {}) {
        super();
        this.params = {
            enabled: {
                x: params.enabled?.x ?? true,
                y: params.enabled?.y ?? false
            },
            speed: {
                x: params.speed?.x ?? 4,
                y: params.speed?.y ?? 4
            },
            amplitude: {
                x: params.amplitude?.x ?? 3,
                y: params.amplitude?.y ?? 3
            }
        };
    }

    update(lookingGlassConfig, time) {
        if (this.params.enabled.x) {
            lookingGlassConfig.trackballX = 
                Math.sin(time / this.params.speed.x) / this.params.amplitude.x;
        }
        if (this.params.enabled.y) {
            lookingGlassConfig.trackballY = 
                Math.sin(time / this.params.speed.y) / this.params.amplitude.y;
        }
    }
} 