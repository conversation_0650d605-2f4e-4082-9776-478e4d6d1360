import * as THREE from 'three';

export class ParticleSystem {
    constructor() {
        this.particleCount = 1000;
        this.particles = [];
        this.initParticleSystem();
    }

    initParticleSystem() {
        const geometry = new THREE.BufferGeometry();
        const positions = new Float32Array(this.particleCount * 3);
        const colors = new Float32Array(this.particleCount * 3);

        for (let i = 0; i < this.particleCount; i++) {
            positions[i * 3] = 0;
            positions[i * 3 + 1] = 0;
            positions[i * 3 + 2] = 0;

            colors[i * 3] = Math.random();
            colors[i * 3 + 1] = Math.random();
            colors[i * 3 + 2] = Math.random();
        }

        geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
        geometry.setAttribute('color', new THREE.BufferAttribute(colors, 3));

        const material = new THREE.PointsMaterial({
            size: 0.05,
            vertexColors: true,
            transparent: true,
            opacity: 0.8,
            blending: THREE.AdditiveBlending
        });

        this.points = new THREE.Points(geometry, material);
        this.particles = Array(this.particleCount).fill().map(() => ({
            velocity: new THREE.Vector3(),
            active: false
        }));
    }

    emitPunchEffect(position) {
        const numParticles = 20;
        for (let i = 0; i < numParticles; i++) {
            const index = this.findInactiveParticle();
            if (index === -1) continue;

            const positions = this.points.geometry.attributes.position;
            positions.array[index * 3] = position.x;
            positions.array[index * 3 + 1] = position.y;
            positions.array[index * 3 + 2] = position.z;

            this.particles[index].active = true;
            this.particles[index].velocity.set(
                (Math.random() - 0.5) * 0.2,
                Math.random() * 0.2,
                (Math.random() - 0.5) * 0.2
            );
        }
        this.points.geometry.attributes.position.needsUpdate = true;
    }

    findInactiveParticle() {
        return this.particles.findIndex(p => !p.active);
    }

    update() {
        const positions = this.points.geometry.attributes.position;

        for (let i = 0; i < this.particleCount; i++) {
            if (!this.particles[i].active) continue;

            const idx = i * 3;
            positions.array[idx] += this.particles[i].velocity.x;
            positions.array[idx + 1] += this.particles[i].velocity.y;
            positions.array[idx + 2] += this.particles[i].velocity.z;

            // Apply gravity
            this.particles[i].velocity.y -= 0.01;

            // Deactivate if particle falls below a certain point
            if (positions.array[idx + 1] < -2) {
                this.particles[i].active = false;
                positions.array[idx] = 0;
                positions.array[idx + 1] = 0;
                positions.array[idx + 2] = 0;
            }
        }

        positions.needsUpdate = true;
    }

    getObject() {
        return this.points;
    }
} 