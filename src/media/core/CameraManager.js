/**
 * CameraManager
 *
 * A unified camera management system that handles:
 * - Single stream management across all components
 * - Corner and popup camera view modes with smooth animations
 * - Stream sharing between gesture control and capture functionalities
 * - Efficient streaming for video sampling and recording
 */

import { MediaCaptureManager } from '../capture/MediaCaptureManager.ts';

export class CameraManager {
    constructor(container, options = {}) {
        this.container = container;
        this.options = {
            defaultMode: options.defaultMode || 'corner', // 'corner' or 'popup'
            cornerPosition: options.cornerPosition || 'bottom-right',
            cornerSize: {
                width: options.cornerSize?.width || 320,
                height: options.cornerSize?.height || 240
            },
            popupSize: {
                width: options.popupSize?.width || 640,
                height: options.popupSize?.height || 480
            },
            animationDuration: options.animationDuration || 300,
            streamQuality: {
                video: {
                    width: { ideal: 640 },
                    height: { ideal: 480 },
                    frameRate: { ideal: 30 },
                    facingMode: 'user'
                },
                audio: false
            },
            ...options
        };

        // Core state
        this.isInitialized = false;
        this.isActive = false;
        this.currentMode = this.options.defaultMode;
        this.mediaStream = null;
        this.videoElement = null;

        // UI elements
        this.cornerContainer = null;
        this.popupWindow = null;
        this.canvasElement = null;

        // Animation system
        this.animationFrameId = null;
        this.transitionInProgress = false;

        // Drag state
        this.isDragging = false;
        this.dragOffset = { x: 0, y: 0 };
        this.dragPosition = null; // Will store custom drag position

        // Callbacks for different components
        this.gestureCallbacks = new Set();
        this.captureCallbacks = new Set();
        this.frameCallbacks = new Set();

        // Media capture manager for advanced features
        this.mediaCaptureManager = new MediaCaptureManager({
            video: this.options.streamQuality.video,
            audio: this.options.streamQuality.audio,
            onCaptureStart: () => this._notifyCallbacks('streamStart'),
            onCaptureStop: () => this._notifyCallbacks('streamStop'),
            onFrame: (frameInfo) => this._notifyFrameCallbacks(frameInfo)
        });

        // Bind methods
        this._handleVisibilityChange = this._handleVisibilityChange.bind(this);
        this._handleResize = this._handleResize.bind(this);
    }

    /**
     * Initialize the camera manager
     */
    async initialize() {
        if (this.isInitialized) {
            console.log('[CameraManager] Already initialized');
            return true;
        }

        try {
            console.log('[CameraManager] Initializing...');

            // Initialize media capture manager
            const initSuccess = await this.mediaCaptureManager.initialize('video');
            if (!initSuccess) {
                throw new Error('Failed to initialize media capture');
            }

            // Set up event listeners
            document.addEventListener('visibilitychange', this._handleVisibilityChange);
            window.addEventListener('resize', this._handleResize);

            this.isInitialized = true;
            console.log('[CameraManager] Initialized successfully');
            return true;
        } catch (error) {
            console.error('[CameraManager] Initialization failed:', error);
            return false;
        }
    }

    /**
     * Start camera stream
     */
    async startCamera() {
        if (this.isActive) {
            console.log('[CameraManager] Camera already active');
            return this.mediaStream;
        }

        try {
            // Start media capture
            const captureStarted = await this.mediaCaptureManager.startCapture('video');
            if (!captureStarted) {
                throw new Error('Failed to start media capture');
            }

            // Get the media stream from the capture manager
            this.mediaStream = this.mediaCaptureManager.getMediaStream();
            if (!this.mediaStream) {
                throw new Error('No media stream available from capture manager');
            }

            // Get the video element from the capture manager (it's already set up)
            this.videoElement = this.mediaCaptureManager.getVideoElement();
            if (!this.videoElement) {
                throw new Error('No video element available from capture manager');
            }

            this.isActive = true;

            // Notify callbacks
            this._notifyCallbacks('streamStart', { stream: this.mediaStream });

            console.log('[CameraManager] Camera started successfully');
            return this.mediaStream;
        } catch (error) {
            console.error('[CameraManager] Failed to start camera:', error);
            this.isActive = false;
            throw error;
        }
    }

    /**
     * Stop camera stream
     */
    stopCamera() {
        if (!this.isActive) {
            return;
        }

        try {
            // Stop media capture
            this.mediaCaptureManager.stopCapture();

            // Clean up video element
            if (this.videoElement) {
                this.videoElement.srcObject = null;
            }

            // Stop all tracks
            if (this.mediaStream) {
                this.mediaStream.getTracks().forEach(track => track.stop());
                this.mediaStream = null;
            }

            this.isActive = false;

            // Notify callbacks
            this._notifyCallbacks('streamStop');

            console.log('[CameraManager] Camera stopped');
        } catch (error) {
            console.error('[CameraManager] Error stopping camera:', error);
        }
    }

    /**
     * Check if camera is currently active
     */
    isStreamActive() {
        return this.isActive && this.mediaStream && this.mediaStream.active;
    }

    /**
     * Check if camera manager is active (alternative method name)
     */
    isCameraActive() {
        return this.isActive;
    }

    /**
     * Get current stream status
     */
    getStreamStatus() {
        return {
            isActive: this.isActive,
            hasStream: !!this.mediaStream,
            streamActive: this.mediaStream?.active || false,
            currentMode: this.currentMode,
            isInitialized: this.isInitialized
        };
    }

    /**
     * Show camera in the specified mode with animation
     */
    async showCamera(mode = null) {
        const targetMode = mode || this.currentMode;

        if (!this.isActive) {
            await this.startCamera();
        }

        if (this.transitionInProgress) {
            console.log('[CameraManager] Transition in progress, waiting...');
            return;
        }

        try {
            this.transitionInProgress = true;

            if (targetMode === 'corner') {
                await this._showCornerView();
            } else if (targetMode === 'popup') {
                await this._showPopupView();
            }

            this.currentMode = targetMode;
            this._notifyCallbacks('viewShow', { mode: targetMode });
        } finally {
            this.transitionInProgress = false;
        }
    }

    /**
     * Hide camera with animation
     */
    async hideCamera() {
        if (this.transitionInProgress) {
            return;
        }

        try {
            this.transitionInProgress = true;

            if (this.cornerContainer) {
                await this._hideCornerView();
            }

            if (this.popupWindow && !this.popupWindow.closed) {
                this.popupWindow.close();
                this.popupWindow = null;
            }

            this.currentMode = 'none';
            this._notifyCallbacks('viewHide');
        } finally {
            this.transitionInProgress = false;
        }
    }

    /**
     * Toggle between corner and popup modes
     */
    async toggleMode() {
        const newMode = this.currentMode === 'corner' ? 'popup' : 'corner';

        // Hide current view
        await this.hideCamera();

        // Show in new mode
        await this.showCamera(newMode);
    }

    /**
     * Capture a photo from the current stream
     */
    async capturePhoto(options = {}) {
        if (!this.isActive || !this.videoElement) {
            throw new Error('Camera not active or video element not available');
        }

        try {
            const frameDataUrl = await this.mediaCaptureManager.captureFrame();
            if (!frameDataUrl) {
                throw new Error('Failed to capture frame');
            }

            // Notify capture callbacks
            this._notifyCallbacks('photoCapture', {
                dataUrl: frameDataUrl,
                options
            });

            return frameDataUrl;
        } catch (error) {
            console.error('[CameraManager] Photo capture failed:', error);
            throw error;
        }
    }

    /**
     * Get the current video element for external use
     */
    getVideoElement() {
        return this.videoElement;
    }

    /**
     * Get the current media stream for external use
     */
    getMediaStream() {
        return this.mediaStream;
    }

    /**
     * Register callbacks for gesture control
     */
    registerGestureCallbacks(callbacks) {
        this.gestureCallbacks.add(callbacks);
        return () => this.gestureCallbacks.delete(callbacks);
    }

    /**
     * Register callbacks for capture functionality
     */
    registerCaptureCallbacks(callbacks) {
        this.captureCallbacks.add(callbacks);
        return () => this.captureCallbacks.delete(callbacks);
    }

    /**
     * Register callbacks for video frame processing
     */
    registerFrameCallbacks(callback) {
        this.frameCallbacks.add(callback);
        return () => this.frameCallbacks.delete(callback);
    }

    /**
     * Extract frames from video data for multimodal processing
     * @param {any} videoData - Video data (Blob, File, ArrayBuffer, etc.)
     * @param {number} fps - Frames per second to extract (default: 2)
     * @returns {Promise<string[]>} Array of base64 encoded frame data URLs
     */
    async extractFramesFromVideo(videoData, fps = 2) {
        try {
            // Import the shared implementation from videoUtils
            const { extractFrames } = await import('../utils/videoUtils');

            // Use the shared implementation
            return await extractFrames(videoData, fps);
        } catch (error) {
            console.error('[CameraManager] Error extracting frames from video:', error);
            throw error;
        }
    }

    /**
     * Capture current video frames from the active camera stream
     * @param {number} maxFrames - Maximum number of frames to capture (default: 3)
     * @returns {Promise<string[]>} Array of base64 encoded frame data URLs
     */
    async captureCurrentFrames(maxFrames = 3) {
        try {
            if (!this.isCameraActive()) {
                console.warn('[CameraManager] Cannot capture frames - camera not active');
                return [];
            }

            const videoElement = this.getVideoElement();
            if (!videoElement || videoElement.videoWidth === 0 || videoElement.videoHeight === 0) {
                console.warn('[CameraManager] Cannot capture frames - video element not ready');
                return [];
            }

            const frames = [];
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');

            canvas.width = videoElement.videoWidth;
            canvas.height = videoElement.videoHeight;

            // Capture frames (for live stream, we can only capture current frame)
            for (let i = 0; i < maxFrames; i++) {
                // Draw current video frame to canvas
                ctx.drawImage(videoElement, 0, 0, canvas.width, canvas.height);

                // Convert canvas to base64 data URL
                const dataUrl = canvas.toDataURL('image/jpeg', 0.8);
                frames.push(dataUrl);

                // Small delay between captures for different frames
                if (i < maxFrames - 1) {
                    await new Promise(resolve => setTimeout(resolve, 100));
                }
            }

            console.log(`[CameraManager] Captured ${frames.length} frames from active camera stream`);
            return frames;
        } catch (error) {
            console.error('[CameraManager] Error capturing current frames:', error);
            return [];
        }
    }

    /**
     * Create corner view with animations
     * @private
     */
    async _showCornerView() {
        if (this.cornerContainer) {
            return; // Already showing
        }

        // Create corner container
        this.cornerContainer = document.createElement('div');
        this.cornerContainer.className = 'camera-corner-view';
        this.cornerContainer.style.cssText = this._getCornerStyles();

        // Create video element for corner view
        const cornerVideo = document.createElement('video');
        cornerVideo.autoplay = true;
        cornerVideo.playsInline = true;
        cornerVideo.muted = true;
        cornerVideo.style.cssText = `
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 12px;
        `;

        // Clone stream for corner view
        if (this.mediaStream) {
            cornerVideo.srcObject = this.mediaStream.clone();
        }

        // Add controls
        const controls = this._createCornerControls();

        this.cornerContainer.appendChild(cornerVideo);
        this.cornerContainer.appendChild(controls);
        this.container.appendChild(this.cornerContainer);

        // Add drag functionality
        this._setupDragFunctionality();

        // Animate in
        return this._animateCornerIn();
    }

    /**
     * Hide corner view with animation
     * @private
     */
    async _hideCornerView() {
        if (!this.cornerContainer) {
            return;
        }

        await this._animateCornerOut();

        if (this.cornerContainer && this.cornerContainer.parentNode) {
            this.cornerContainer.parentNode.removeChild(this.cornerContainer);
        }

        // Reset drag state when hiding
        this.cornerContainer = null;
        this.isDragging = false;
        // Note: We keep dragPosition to remember user's preferred position
    }

    /**
     * Show popup view
     * @private
     */
    async _showPopupView() {
        if (this.popupWindow && !this.popupWindow.closed) {
            this.popupWindow.focus();
            return;
        }

        const { width, height } = this.options.popupSize;

        this.popupWindow = window.open('', 'CameraPopup',
            `width=${width},height=${height},resizable=yes,scrollbars=no,status=no`);

        if (!this.popupWindow) {
            throw new Error('Failed to open popup window (likely blocked)');
        }

        // Set up popup content
        this._setupPopupContent();
    }

    /**
     * Get corner view styles
     * @private
     */
    _getCornerStyles() {
        const { cornerPosition, cornerSize, animationDuration } = this.options;
        const { width, height } = cornerSize;

        let positionStyles = '';

        // Use drag position if available, otherwise use corner position
        if (this.dragPosition) {
            positionStyles = `top: ${this.dragPosition.y}px; left: ${this.dragPosition.x}px;`;
        } else {
            switch (cornerPosition) {
                case 'top-left':
                    positionStyles = 'top: 20px; left: 20px;';
                    break;
                case 'top-right':
                    positionStyles = 'top: 20px; right: 20px;';
                    break;
                case 'bottom-left':
                    positionStyles = 'bottom: 20px; left: 20px;';
                    break;
                case 'bottom-right':
                default:
                    positionStyles = 'bottom: 20px; right: 20px;';
                    break;
            }
        }

        return `
            position: fixed;
            ${positionStyles}
            width: ${width}px;
            height: ${height}px;
            background: #000;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
            z-index: 1000;
            overflow: hidden;
            opacity: 0;
            transform: scale(0.8) translateY(20px);
            transition: all ${animationDuration}ms cubic-bezier(0.175, 0.885, 0.32, 1.275);
            cursor: move;
        `;
    }

    /**
     * Create corner controls
     * @private
     */
    _createCornerControls() {
        const controls = document.createElement('div');
        controls.className = 'camera-corner-controls';
        controls.style.cssText = `
            position: absolute;
            top: 8px;
            right: 8px;
            display: flex;
            gap: 4px;
            z-index: 1001;
        `;

        // Expand to popup button
        const expandButton = document.createElement('button');
        expandButton.innerHTML = '⤢';
        expandButton.title = 'Expand to popup';
        expandButton.style.cssText = this._getControlButtonStyles();
        expandButton.addEventListener('click', () => this.toggleMode());

        // Close button
        const closeButton = document.createElement('button');
        closeButton.innerHTML = '×';
        closeButton.title = 'Close camera';
        closeButton.style.cssText = this._getControlButtonStyles();
        closeButton.addEventListener('click', () => this.hideCamera());

        controls.appendChild(expandButton);
        controls.appendChild(closeButton);

        return controls;
    }

    /**
     * Get control button styles
     * @private
     */
    _getControlButtonStyles() {
        return `
            width: 24px;
            height: 24px;
            border: none;
            border-radius: 50%;
            background: rgba(0,0,0,0.7);
            color: white;
            font-size: 12px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background-color 0.2s ease;
        `;
    }

    /**
     * Animate corner view in
     * @private
     */
    _animateCornerIn() {
        return new Promise(resolve => {
            requestAnimationFrame(() => {
                this.cornerContainer.style.opacity = '1';
                this.cornerContainer.style.transform = 'scale(1) translateY(0)';
                setTimeout(resolve, this.options.animationDuration);
            });
        });
    }

    /**
     * Animate corner view out
     * @private
     */
    _animateCornerOut() {
        return new Promise(resolve => {
            this.cornerContainer.style.opacity = '0';
            this.cornerContainer.style.transform = 'scale(0.8) translateY(20px)';
            setTimeout(resolve, this.options.animationDuration);
        });
    }

    /**
     * Setup drag functionality for the corner view
     * @private
     */
    _setupDragFunctionality() {
        if (!this.cornerContainer) return;

        let startX, startY, initialLeft, initialTop;

        const handleMouseDown = (e) => {
            // Only allow dragging on the video area, not on controls
            if (e.target.closest('.camera-corner-controls')) {
                return;
            }

            e.preventDefault();
            this.isDragging = true;

            // Get initial mouse position
            startX = e.clientX;
            startY = e.clientY;

            // Get initial container position
            const rect = this.cornerContainer.getBoundingClientRect();
            initialLeft = rect.left;
            initialTop = rect.top;

            // Add visual feedback
            this.cornerContainer.style.cursor = 'grabbing';
            this.cornerContainer.style.transition = 'none'; // Disable transitions during drag
            this.cornerContainer.style.boxShadow = '0 8px 32px rgba(0,0,0,0.5)';

            // Add document event listeners
            document.addEventListener('mousemove', handleMouseMove);
            document.addEventListener('mouseup', handleMouseUp);
        };

        const handleMouseMove = (e) => {
            if (!this.isDragging) return;

            e.preventDefault();

            // Calculate movement delta
            const deltaX = e.clientX - startX;
            const deltaY = e.clientY - startY;

            // Calculate new position
            const newLeft = initialLeft + deltaX;
            const newTop = initialTop + deltaY;

            // Constrain to viewport
            const containerRect = this.cornerContainer.getBoundingClientRect();
            const maxLeft = window.innerWidth - containerRect.width;
            const maxTop = window.innerHeight - containerRect.height;

            const constrainedLeft = Math.max(0, Math.min(maxLeft, newLeft));
            const constrainedTop = Math.max(0, Math.min(maxTop, newTop));

            // Update position
            this.cornerContainer.style.left = `${constrainedLeft}px`;
            this.cornerContainer.style.top = `${constrainedTop}px`;
            this.cornerContainer.style.right = 'auto';
            this.cornerContainer.style.bottom = 'auto';
        };

        const handleMouseUp = (e) => {
            if (!this.isDragging) return;

            this.isDragging = false;

            // Store final position
            const rect = this.cornerContainer.getBoundingClientRect();
            this.dragPosition = {
                x: rect.left,
                y: rect.top
            };

            // Restore visual state
            this.cornerContainer.style.cursor = 'move';
            this.cornerContainer.style.transition = `all ${this.options.animationDuration}ms cubic-bezier(0.175, 0.885, 0.32, 1.275)`;
            this.cornerContainer.style.boxShadow = '0 4px 20px rgba(0,0,0,0.3)';

            // Remove document event listeners
            document.removeEventListener('mousemove', handleMouseMove);
            document.removeEventListener('mouseup', handleMouseUp);

            console.log('[CameraManager] Camera position saved:', this.dragPosition);
        };

        // Add mousedown listener to container
        this.cornerContainer.addEventListener('mousedown', handleMouseDown);

        // Add touch support for mobile
        this._setupTouchDrag();
    }

    /**
     * Setup touch drag functionality for mobile devices
     * @private
     */
    _setupTouchDrag() {
        if (!this.cornerContainer) return;

        let startX, startY, initialLeft, initialTop;

        const handleTouchStart = (e) => {
            // Only allow dragging on the video area, not on controls
            if (e.target.closest('.camera-corner-controls')) {
                return;
            }

            e.preventDefault();
            this.isDragging = true;

            const touch = e.touches[0];
            startX = touch.clientX;
            startY = touch.clientY;

            const rect = this.cornerContainer.getBoundingClientRect();
            initialLeft = rect.left;
            initialTop = rect.top;

            // Add visual feedback
            this.cornerContainer.style.cursor = 'grabbing';
            this.cornerContainer.style.transition = 'none';
            this.cornerContainer.style.boxShadow = '0 8px 32px rgba(0,0,0,0.5)';
        };

        const handleTouchMove = (e) => {
            if (!this.isDragging) return;

            e.preventDefault();

            const touch = e.touches[0];
            const deltaX = touch.clientX - startX;
            const deltaY = touch.clientY - startY;

            const newLeft = initialLeft + deltaX;
            const newTop = initialTop + deltaY;

            // Constrain to viewport
            const containerRect = this.cornerContainer.getBoundingClientRect();
            const maxLeft = window.innerWidth - containerRect.width;
            const maxTop = window.innerHeight - containerRect.height;

            const constrainedLeft = Math.max(0, Math.min(maxLeft, newLeft));
            const constrainedTop = Math.max(0, Math.min(maxTop, newTop));

            // Update position
            this.cornerContainer.style.left = `${constrainedLeft}px`;
            this.cornerContainer.style.top = `${constrainedTop}px`;
            this.cornerContainer.style.right = 'auto';
            this.cornerContainer.style.bottom = 'auto';
        };

        const handleTouchEnd = (e) => {
            if (!this.isDragging) return;

            this.isDragging = false;

            // Store final position
            const rect = this.cornerContainer.getBoundingClientRect();
            this.dragPosition = {
                x: rect.left,
                y: rect.top
            };

            // Restore visual state
            this.cornerContainer.style.cursor = 'move';
            this.cornerContainer.style.transition = `all ${this.options.animationDuration}ms cubic-bezier(0.175, 0.885, 0.32, 1.275)`;
            this.cornerContainer.style.boxShadow = '0 4px 20px rgba(0,0,0,0.3)';

            console.log('[CameraManager] Camera position saved (touch):', this.dragPosition);
        };

        // Add touch listeners
        this.cornerContainer.addEventListener('touchstart', handleTouchStart, { passive: false });
        this.cornerContainer.addEventListener('touchmove', handleTouchMove, { passive: false });
        this.cornerContainer.addEventListener('touchend', handleTouchEnd, { passive: false });
    }

    /**
     * Set up popup window content
     * @private
     */
    _setupPopupContent() {
        this.popupWindow.document.title = 'Camera View';
        this.popupWindow.document.body.style.cssText = `
            margin: 0;
            padding: 0;
            background: #000;
            overflow: hidden;
        `;

        // Add video element
        const popupVideo = document.createElement('video');
        popupVideo.autoplay = true;
        popupVideo.playsInline = true;
        popupVideo.muted = true;
        popupVideo.style.cssText = `
            width: 100%;
            height: 100%;
            object-fit: contain;
        `;

        // Add canvas for overlays
        const popupCanvas = document.createElement('canvas');
        popupCanvas.style.cssText = `
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
        `;

        // Connect stream
        if (this.mediaStream) {
            popupVideo.srcObject = this.mediaStream.clone();
        }

        this.popupWindow.document.body.appendChild(popupVideo);
        this.popupWindow.document.body.appendChild(popupCanvas);

        // Handle window close
        this.popupWindow.addEventListener('beforeunload', () => {
            this.popupWindow = null;
            this._notifyCallbacks('popupClose');
        });
    }

    /**
     * Notify all registered callbacks
     * @private
     */
    _notifyCallbacks(event, data = {}) {
        const allCallbacks = new Set([
            ...this.gestureCallbacks,
            ...this.captureCallbacks
        ]);

        allCallbacks.forEach(callbackSet => {
            if (callbackSet[event] && typeof callbackSet[event] === 'function') {
                try {
                    callbackSet[event](data);
                } catch (error) {
                    console.error(`[CameraManager] Error in ${event} callback:`, error);
                }
            }
        });
    }

    /**
     * Notify frame processing callbacks
     * @private
     */
    _notifyFrameCallbacks(frameInfo) {
        this.frameCallbacks.forEach(callback => {
            try {
                callback(this.videoElement, frameInfo);
            } catch (error) {
                console.error('[CameraManager] Error in frame callback:', error);
            }
        });
    }

    /**
     * Handle visibility change
     * @private
     */
    _handleVisibilityChange() {
        if (document.hidden && this.isActive) {
            console.log('[CameraManager] Page hidden, pausing camera');
            // Optionally pause camera when page is hidden
        } else if (!document.hidden && this.isActive) {
            console.log('[CameraManager] Page visible, resuming camera');
            // Optionally resume camera when page is visible
        }
    }

    /**
     * Handle window resize
     * @private
     */
    _handleResize() {
        if (this.cornerContainer) {
            // Adjust corner position on resize if needed
        }
    }

    /**
     * Dispose and clean up all resources
     */
    dispose() {
        console.log('[CameraManager] Disposing...');

        // Stop camera
        this.stopCamera();

        // Clean up event listeners
        document.removeEventListener('visibilitychange', this._handleVisibilityChange);
        window.removeEventListener('resize', this._handleResize);

        // Dispose media capture manager
        if (this.mediaCaptureManager) {
            this.mediaCaptureManager.dispose();
        }

        // Clean up UI elements
        if (this.cornerContainer && this.cornerContainer.parentNode) {
            this.cornerContainer.parentNode.removeChild(this.cornerContainer);
        }

        if (this.popupWindow && !this.popupWindow.closed) {
            this.popupWindow.close();
        }

        // Clear all callbacks
        this.gestureCallbacks.clear();
        this.captureCallbacks.clear();
        this.frameCallbacks.clear();

        this.isInitialized = false;
    }

    /**
     * Reset camera position to default corner position
     */
    resetPosition() {
        this.dragPosition = null;
        if (this.cornerContainer) {
            // Re-apply styles with default position
            this.cornerContainer.style.cssText = this._getCornerStyles();
            console.log('[CameraManager] Camera position reset to default');
        }
    }
}
