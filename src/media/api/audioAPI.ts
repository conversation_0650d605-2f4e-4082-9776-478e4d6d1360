/**
 * Audio API utilities
 * Handles interactions with audio processing APIs
 */

import { convertFloat32ToWav } from '../utils/audioUtils.js';
import { createLogger } from '../../utils/logger.js';

// Create a logger for this module
const logger = createLogger('audioAPI');

// Default audio processing options
const DEFAULT_AUDIO_OPTIONS = {
  sampleRate: 16000,
  numChannels: 1,
  bitDepth: 16
};

/**
 * Process audio data for API consumption
 * @param audioData Audio data as Float32Array
 * @param options Processing options
 * @returns Promise with processed audio data as base64
 */
export async function processAudioForAPI(
  audioData: Float32Array,
  options: {
    sampleRate?: number;
    numChannels?: number;
    bitDepth?: number;
  } = {}
): Promise<string> {
  try {
    // Merge options with defaults
    const mergedOptions = {
      ...DEFAULT_AUDIO_OPTIONS,
      ...options
    };

    // Convert Float32Array to WAV format
    const wavBlob = convertFloat32ToWav(audioData, mergedOptions);

    // Convert to base64
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onloadend = () => {
        const base64data = reader.result as string;
        // Extract the base64 part (remove the data URL prefix)
        const base64 = base64data.split(',')[1];
        resolve(base64);
      };
      reader.onerror = reject;
      reader.readAsDataURL(wavBlob);
    });
  } catch (error) {
    logger.error('Error processing audio for API:', error);
    throw error;
  }
}

// Default API request options
const DEFAULT_API_OPTIONS = {
  stream: true,
  enableAudio: true,
  additionalText: 'Please transcribe and respond to this audio.'
};

/**
 * Create a session ID
 * @returns A unique session ID
 */
function createSessionId(): string {
  return `session-${Date.now()}-${Math.random().toString(36).substring(2, 8)}`;
}

/**
 * Convert base64 string to Blob
 * @param base64 Base64 string
 * @param mimeType MIME type for the blob
 * @returns Blob object
 */
function base64ToBlob(base64: string, mimeType: string = 'audio/wav'): Blob {
  const byteCharacters = atob(base64);
  const byteNumbers = new Array(byteCharacters.length);

  for (let i = 0; i < byteCharacters.length; i++) {
    byteNumbers[i] = byteCharacters.charCodeAt(i);
  }

  const byteArray = new Uint8Array(byteNumbers);
  return new Blob([byteArray], { type: mimeType });
}

/**
 * Send audio to an API for processing
 * @param audioData Audio data as Float32Array
 * @param options Processing options
 * @returns Promise with API response
 */
export async function sendAudioToAPI(
  audioData: Float32Array,
  options: {
    endpoint?: string;
    systemPrompt?: string;
    additionalText?: string;
    sessionId?: string;
    language?: string;
    stream?: boolean;
    onProgress?: (data: any) => void;
    onComplete?: (data: any) => void;
    provider?: string;
    skipTTS?: boolean; // New option to skip TTS in LLMStreamProcessor
  } = {}
): Promise<any> {
  try {
    // Process audio to base64
    const base64Audio = await processAudioForAPI(audioData);

    // Generate a session ID if not provided
    const sessionId = options.sessionId || createSessionId();

    // Import the LLM service directly
    // This works in both browser and server environments
    // Server LLM service removed - using agent-based architecture
    // const { getLLMService } = await import('../../server/llm');
    // const llmService = await getLLMService();
    console.warn('LLM service functionality moved to agent system');

    // Return placeholder response - functionality moved to agent system
    return {
      text: '[Audio processing moved to agent system - use TalkingAvatar with agent mode]',
      success: false,
      error: 'Legacy audio API disabled - use agent mode for audio processing',
      sessionId: sessionId
    };
  } catch (error) {
    logger.error('Error in sendAudioToAPI:', error);
    throw error;
  }
}
