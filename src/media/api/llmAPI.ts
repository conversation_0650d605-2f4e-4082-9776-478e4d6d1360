/**
 * LLM API Service - Proxies all LLM calls to server /llm route
 * 
 * This service ensures all LLM calls go through the server instead of running models directly in the browser.
 * This is critical for:
 * - API key security (server-side only)
 * - CORS compliance
 * - Audio processing capabilities
 * - Tool calling functionality
 */

import { createLogger } from '@/utils/logger.js';
import { getDownloadServerUrl } from '@/utils/portManager.js';
import { ALIYUN_AUDIO_CONFIG } from '@/agent/models/aliyun/config/AliyunConfig.js';

interface LLMMessage {
    role: string;
    content: string | any[];
    input_audio?: any;
    extraModalities?: any;
    constructor?: { name: string };
    _getType?: () => string;
}

interface LLMOptions {
    provider?: string;
    model?: string;
    modalities?: string[];
    audioConfig?: { voice: string; format: string };
    tools?: any[];
    tool_choice?: string;
    toolChoice?: string;
    temperature?: number;
    max_tokens?: number;
    stream?: boolean;
    stream_options?: { include_usage: boolean };
    language?: string;
    gender?: string | null;
    mood?: string;
}

interface ServerResponse {
    content?: string;
    audio?: string;
    metadata?: {
        usage?: any;
    };
}

class LLMAPIService {
    private logger: any;
    private getBaseUrl: () => string;

    constructor() {
        this.logger = createLogger('LLMAPIService');
        // Use dynamic getter for base URL
        this.getBaseUrl = () => getDownloadServerUrl();
    }

    /**
     * Invoke LLM with messages and options
     */
    async invoke(messages: LLMMessage[], options: LLMOptions = {}): Promise<any> {
        this.logger.debug('[LLMAPIService] invoke called with:', {
            messagesCount: messages.length,
            optionsKeys: Object.keys(options),
            hasAudio: messages.some(m => m.input_audio),
            hasTools: !!(options.tools && options.tools.length > 0)
        });

        // Format messages for the API
        const formattedMessages = this._formatMessages(messages);

        // Prepare the request payload
        const payload = {
            provider: options.provider || 'aliyun',
            model: options.model || 'qwen-omni-turbo-realtime',
            messages: formattedMessages,
            modalities: options.modalities || ['text'],
            audioConfig: options.audioConfig || { voice: ALIYUN_AUDIO_CONFIG.defaultVoice, format: 'wav' },
            tools: options.tools || [],
            tool_choice: options.tool_choice || options.toolChoice || 'auto',
            temperature: options.temperature || 0.7,
            max_tokens: options.max_tokens,
            stream: options.stream || false,
            // Pass through any additional options
            ...options
        };

        // Use the correct /api/llm endpoint that is registered on the server
        const apiUrl = `${this.getBaseUrl()}/api/llm`;

        this.logger.debug('[LLMAPIService] Sending request to server:', {
            url: apiUrl,
            payloadKeys: Object.keys(payload),
            messagesCount: formattedMessages.length,
            provider: payload.provider,
            model: payload.model
        });

        try {
            const response = await fetch(apiUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(payload)
            });

            if (!response.ok) {
                const errorText = await response.text();
                throw new Error(`LLM API request failed: ${response.status} ${response.statusText}. ${errorText}`);
            }

            const result = await response.json();

            this.logger.debug('[LLMAPIService] Received response:', {
                hasContent: !!result.content,
                hasAudio: !!result.audio,
                hasMetadata: !!result.metadata
            });

            return result;
        } catch (error) {
            this.logger.error('[LLMAPIService] API request failed:', error);
            throw error;
        }
    }

    /**
     * Format messages for server API
     */
    private _formatMessages(messages: LLMMessage[]): any[] {
        return messages.map(msg => {
            let role = 'user';
            let content: string | any[] = '';

            // Handle LangChain message types
            if (msg.constructor?.name === 'HumanMessage' || msg._getType?.() === 'human') {
                role = 'user';
            } else if (msg.constructor?.name === 'AIMessage' || msg._getType?.() === 'ai') {
                role = 'assistant';
            } else if (msg.constructor?.name === 'SystemMessage' || msg._getType?.() === 'system') {
                role = 'system';
            } else if ((msg as any).role) {
                role = (msg as any).role;
            }

            // Handle content
            if (typeof msg.content === 'string') {
                content = msg.content;
            } else if (Array.isArray(msg.content)) {
                content = msg.content;
            } else {
                content = msg.content || '';
            }

            const formattedMsg: any = { role, content };

            // Preserve audio data
            if (msg.input_audio) {
                formattedMsg.input_audio = msg.input_audio;
            }

            // Preserve extra modalities
            if (msg.extraModalities) {
                formattedMsg.extraModalities = msg.extraModalities;
            }

            return formattedMsg;
        });
    }

    /**
     * Format server response to LangChain-compatible format
     */
    private _formatResponse(serverResponse: ServerResponse): any {
        const generation = {
            text: serverResponse.content || '',
            audio: serverResponse.audio || '',
            message: {
                lc: 1,
                type: 'constructor',
                id: ['langchain_core', 'messages', 'AIMessage'],
                kwargs: {
                    content: serverResponse.content || '',
                    audio: serverResponse.audio || ''
                }
            },
            usage: serverResponse.metadata?.usage || null
        };

        return {
            generations: [generation]
        };
    }

    /**
     * Test connection to server
     */
    async testConnection(): Promise<boolean> {
        try {
            const response = await fetch(`${this.getBaseUrl()}/api/llm`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    provider: 'aliyun',
                    model: 'qwen-omni-turbo-realtime',
                    messages: [{ role: 'user', content: 'test' }]
                })
            });

            return response.ok;
        } catch (error) {
            this.logger.error('[LLMAPIService] Connection test failed:', error);
            return false;
        }
    }
}

// Export singleton instance
export const llmAPI = new LLMAPIService();
export default llmAPI; 