/**
 * Video Processing Module
 * Centralizes all video-related functionality including frame extraction, validation, and compression
 */

import { createLogger } from '../../utils/logger.js';

// Create dedicated logger for video modality
const logger = createLogger('VideoModality');

/**
 * Video format constants
 */
export const VideoFormat = {
    MP4: 'mp4',
    WEBM: 'webm',
    AVI: 'avi',
    MOV: 'mov',
    UNKNOWN: 'unknown'
} as const;

export type VideoFormatType = typeof VideoFormat[keyof typeof VideoFormat];

/**
 * Video processing configuration
 */
export interface VideoConfig {
    maxFrames?: number;
    frameRate?: number;
    quality?: number;
    maxFileSize?: number;
    format?: string;
}

/**
 * Default video processing options
 */
export const DEFAULT_VIDEO_CONFIG: VideoConfig = {
    maxFrames: 30,
    frameRate: 2, // 2fps for real-time APIs as per Aliyun docs
    quality: 0.8,
    maxFileSize: 500000, // 500KB as per Aliyun docs
    format: 'jpeg'
};

/**
 * Frame extraction result
 */
export interface FrameExtractionResult {
    success: boolean;
    frames?: string[]; // Base64 encoded frames
    error?: string;
    metadata?: {
        totalFrames: number;
        extractedFrames: number;
        processingTimeMs: number;
    };
}

/**
 * Video validation result
 */
export interface VideoValidationResult {
    isValid: boolean;
    format?: VideoFormatType;
    error?: string;
    metadata?: {
        duration?: number;
        width?: number;
        height?: number;
        size?: number;
    };
}

/**
 * Detect video format from file extension or MIME type
 */
export function detectVideoFormat(input: string | File): VideoFormatType {
    let mimeType = '';
    let extension = '';

    if (typeof input === 'string') {
        extension = input.toLowerCase().split('.').pop() || '';
    } else if (input instanceof File) {
        mimeType = input.type.toLowerCase();
        extension = input.name.toLowerCase().split('.').pop() || '';
    }

    // Check MIME type first
    if (mimeType.includes('mp4')) return VideoFormat.MP4;
    if (mimeType.includes('webm')) return VideoFormat.WEBM;
    if (mimeType.includes('avi')) return VideoFormat.AVI;
    if (mimeType.includes('mov') || mimeType.includes('quicktime')) return VideoFormat.MOV;

    // Check extension
    switch (extension) {
        case 'mp4': return VideoFormat.MP4;
        case 'webm': return VideoFormat.WEBM;
        case 'avi': return VideoFormat.AVI;
        case 'mov': return VideoFormat.MOV;
        default: return VideoFormat.UNKNOWN;
    }
}

/**
 * Validate video input
 */
export function validateVideoInput(videoInput: any): VideoValidationResult {
    if (!videoInput) {
        return {
            isValid: false,
            error: 'Video input is null or undefined'
        };
    }

    if (videoInput instanceof File) {
        const format = detectVideoFormat(videoInput);
        return {
            isValid: format !== VideoFormat.UNKNOWN,
            format,
            error: format === VideoFormat.UNKNOWN ? 'Unsupported video format' : undefined,
            metadata: {
                size: videoInput.size
            }
        };
    }

    if (typeof videoInput === 'string') {
        // Assume it's a URL or base64
        return {
            isValid: videoInput.length > 0,
            format: detectVideoFormat(videoInput),
            error: videoInput.length === 0 ? 'Empty video string' : undefined
        };
    }

    return {
        isValid: false,
        error: `Unsupported video input type: ${typeof videoInput}`
    };
}

/**
 * Extract frames from video file (browser environment)
 */
export async function extractFramesInBrowser(
    videoFile: File | string,
    config: VideoConfig = {}
): Promise<FrameExtractionResult> {
    const startTime = Date.now();
    const {
        maxFrames = DEFAULT_VIDEO_CONFIG.maxFrames!,
        frameRate = DEFAULT_VIDEO_CONFIG.frameRate!,
        quality = DEFAULT_VIDEO_CONFIG.quality!
    } = config;

    try {
        logger.debug('🎬 Starting frame extraction in browser', {
            maxFrames,
            frameRate,
            quality
        });

        // Create video element
        const video = document.createElement('video');
        video.crossOrigin = 'anonymous';
        video.muted = true;

        // Create canvas for frame capture
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');

        if (!ctx) {
            throw new Error('Failed to get 2D canvas context');
        }

        return new Promise((resolve) => {
            const frames: string[] = [];
            let currentFrame = 0;

            video.onloadedmetadata = () => {
                canvas.width = video.videoWidth;
                canvas.height = video.videoHeight;

                const duration = video.duration;
                const frameInterval = 1 / frameRate;
                const totalPossibleFrames = Math.floor(duration / frameInterval);
                const framesToExtract = Math.min(maxFrames, totalPossibleFrames);

                logger.debug('📊 Video metadata loaded', {
                    duration,
                    width: video.videoWidth,
                    height: video.videoHeight,
                    framesToExtract
                });

                extractNextFrame();
            };

            const extractNextFrame = () => {
                if (currentFrame >= maxFrames) {
                    finishExtraction();
                    return;
                }

                const timePosition = currentFrame / frameRate;
                video.currentTime = timePosition;
            };

            video.onseeked = () => {
                // Draw current frame to canvas
                ctx.drawImage(video, 0, 0, canvas.width, canvas.height);

                // Convert to base64 JPEG
                const frameData = canvas.toDataURL('image/jpeg', quality);
                const base64Data = frameData.split(',')[1];

                frames.push(base64Data);
                currentFrame++;

                // Extract next frame
                setTimeout(extractNextFrame, 50); // Small delay to ensure seek completes
            };

            const finishExtraction = () => {
                const processingTime = Date.now() - startTime;

                logger.debug('✅ Frame extraction completed', {
                    extractedFrames: frames.length,
                    processingTimeMs: processingTime
                });

                resolve({
                    success: true,
                    frames,
                    metadata: {
                        totalFrames: maxFrames,
                        extractedFrames: frames.length,
                        processingTimeMs: processingTime
                    }
                });
            };

            video.onerror = (error) => {
                const processingTime = Date.now() - startTime;
                resolve({
                    success: false,
                    error: `Video loading failed: ${error}`,
                    metadata: {
                        totalFrames: 0,
                        extractedFrames: 0,
                        processingTimeMs: processingTime
                    }
                });
            };

            // Start loading video
            if (typeof videoFile === 'string') {
                video.src = videoFile;
            } else {
                video.src = URL.createObjectURL(videoFile);
            }
        });

    } catch (error) {
        const processingTime = Date.now() - startTime;
        logger.error('❌ Frame extraction failed', error);

        return {
            success: false,
            error: `Frame extraction failed: ${error instanceof Error ? error.message : String(error)}`,
            metadata: {
                totalFrames: 0,
                extractedFrames: 0,
                processingTimeMs: processingTime
            }
        };
    }
}

/**
 * Compress video frame for real-time transmission
 */
export async function compressVideoFrame(
    frameData: string,
    config: VideoConfig = {}
): Promise<{ success: boolean; compressedFrame?: string; error?: string }> {
    const {
        quality = DEFAULT_VIDEO_CONFIG.quality!,
        maxFileSize = DEFAULT_VIDEO_CONFIG.maxFileSize!
    } = config;

    try {
        // Create a canvas to recompress the image
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');

        if (!ctx) {
            throw new Error('Failed to get 2D canvas context');
        }

        const img = new Image();

        return new Promise((resolve) => {
            img.onload = () => {
                canvas.width = img.width;
                canvas.height = img.height;

                // Draw and recompress
                ctx.drawImage(img, 0, 0);

                let compressedFrame = canvas.toDataURL('image/jpeg', quality).split(',')[1];

                // Check size and reduce quality if needed
                let currentQuality = quality;
                while (compressedFrame.length * 0.75 > maxFileSize && currentQuality > 0.1) {
                    currentQuality -= 0.1;
                    compressedFrame = canvas.toDataURL('image/jpeg', currentQuality).split(',')[1];
                }

                resolve({
                    success: true,
                    compressedFrame
                });
            };

            img.onerror = () => {
                resolve({
                    success: false,
                    error: 'Failed to load image for compression'
                });
            };

            img.src = `data:image/jpeg;base64,${frameData}`;
        });

    } catch (error) {
        return Promise.resolve({
            success: false,
            error: `Frame compression failed: ${error instanceof Error ? error.message : String(error)}`
        });
    }
}

/**
 * Process video for real-time streaming (extracts frames at optimal rate)
 */
export async function processVideoForRealtime(
    videoInput: File | string,
    config: VideoConfig = {}
): Promise<FrameExtractionResult> {
    const validation = validateVideoInput(videoInput);

    if (!validation.isValid) {
        return {
            success: false,
            error: validation.error,
            metadata: {
                totalFrames: 0,
                extractedFrames: 0,
                processingTimeMs: 0
            }
        };
    }

    // Use Aliyun-optimized settings for real-time
    const realtimeConfig: VideoConfig = {
        ...DEFAULT_VIDEO_CONFIG,
        ...config,
        frameRate: 2, // 2fps as recommended by Aliyun
        maxFileSize: 500000 // 500KB limit
    };

    return extractFramesInBrowser(videoInput as File | string, realtimeConfig);
}

/**
 * Create video thumbnail
 */
export async function createVideoThumbnail(
    videoInput: File | string,
    timePosition: number = 1.0
): Promise<{ success: boolean; thumbnail?: string; error?: string }> {
    try {
        const video = document.createElement('video');
        video.crossOrigin = 'anonymous';
        video.muted = true;

        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');

        if (!ctx) {
            throw new Error('Failed to get 2D canvas context');
        }

        return new Promise((resolve) => {
            video.onloadedmetadata = () => {
                canvas.width = video.videoWidth;
                canvas.height = video.videoHeight;
                video.currentTime = Math.min(timePosition, video.duration);
            };

            video.onseeked = () => {
                ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
                const thumbnail = canvas.toDataURL('image/jpeg', 0.8).split(',')[1];

                resolve({
                    success: true,
                    thumbnail
                });
            };

            video.onerror = (error) => {
                resolve({
                    success: false,
                    error: `Video loading failed: ${error}`
                });
            };

            // Start loading
            if (typeof videoInput === 'string') {
                video.src = videoInput;
            } else {
                video.src = URL.createObjectURL(videoInput);
            }
        });

    } catch (error) {
        return {
            success: false,
            error: `Thumbnail creation failed: ${error instanceof Error ? error.message : String(error)}`
        };
    }
}