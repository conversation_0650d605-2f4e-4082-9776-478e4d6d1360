/**
 * Simplified Audio Processing Module using Open Source Libraries
 * Replaces complex custom audio processing with battle-tested libraries
 */

import toWav from 'audiobuffer-to-wav';
import { createLogger } from '../../utils/logger.js';

const logger = createLogger('AudioProcessor');


/**
 * Convert ArrayBuffer to base64 string
 */
export function arrayBufferToBase64(buffer) {
    const bytes = new Uint8Array(buffer);
    let binary = '';
    for (let i = 0; i < bytes.byteLength; i++) {
        binary += String.fromCharCode(bytes[i]);
    }
    return btoa(binary);
}

/**
 * Convert base64 string to ArrayBuffer
 */
export function base64ToArrayBuffer(base64) {
    const binaryString = atob(base64);
    const bytes = new Uint8Array(binaryString.length);
    for (let i = 0; i < binaryString.length; i++) {
        bytes[i] = binaryString.charCodeAt(i);
    }
    return bytes.buffer;
}

/**
 * Create AudioBuffer from PCM data
 */
export function createAudioBufferFromPCM(pcmData, sampleRate = 24000, channels = 1) {
    if (typeof window === 'undefined' || !window.AudioContext) {
        throw new Error('AudioContext not available');
    }
    
    const audioContext = new (window.AudioContext || window.webkitAudioContext)();
    const audioBuffer = audioContext.createBuffer(channels, pcmData.length / channels, sampleRate);
    
    // Convert PCM data to Float32Array if needed
    let floatData;
    if (pcmData instanceof Int16Array) {
        floatData = new Float32Array(pcmData.length);
        for (let i = 0; i < pcmData.length; i++) {
            floatData[i] = pcmData[i] / 32768.0; // Convert 16-bit to float
        }
    } else if (pcmData instanceof Float32Array) {
        floatData = pcmData;
    } else {
        // Assume raw bytes, convert to 16-bit PCM then to float
        const int16Data = new Int16Array(pcmData.buffer || pcmData);
        floatData = new Float32Array(int16Data.length);
        for (let i = 0; i < int16Data.length; i++) {
            floatData[i] = int16Data[i] / 32768.0;
        }
    }
    
    audioBuffer.copyToChannel(floatData, 0);
    return audioBuffer;
}

/**
 * Process ArrayBuffer audio data and convert to playable blob
 */
export async function processArrayBufferAudio(arrayBuffer, sampleRate = 24000, channels = 1) {
    try {
        logger.debug('Processing ArrayBuffer audio:', {
            size: arrayBuffer.byteLength,
            sampleRate,
            channels
        });

        // Try to decode as audio first (for complete audio files)
        if (typeof window !== 'undefined' && window.AudioContext) {
            const audioContext = new (window.AudioContext || window.webkitAudioContext)();
            
            try {
                // Try direct decoding first (for WAV, MP3, etc.)
                const audioBuffer = await audioContext.decodeAudioData(arrayBuffer.slice());
                const wavBuffer = toWav(audioBuffer);
                logger.debug('Successfully decoded as complete audio file');
                return new Blob([wavBuffer], { type: 'audio/wav' });
            } catch (decodeError) {
                logger.debug('Direct decode failed, treating as raw PCM:', decodeError.message);
                
                // Treat as raw PCM data
                const pcmData = new Uint8Array(arrayBuffer);
                const audioBuffer = createAudioBufferFromPCM(pcmData, sampleRate, channels);
                const wavBuffer = toWav(audioBuffer);
                logger.debug('Successfully converted PCM to WAV');
                return new Blob([wavBuffer], { type: 'audio/wav' });
            }
        } else {
            throw new Error('AudioContext not available');
        }
    } catch (error) {
        logger.error('Failed to process ArrayBuffer audio:', error);
        throw error;
    }
}

/**
 * Process base64 audio data and convert to playable blob
 */
export async function processBase64Audio(base64Audio, sampleRate = 24000, channels = 1) {
    try {
        if (!base64Audio || typeof base64Audio !== 'string') {
            throw new Error('Invalid base64 audio data');
        }
        
        // Clean base64 string
        let cleanBase64 = base64Audio.trim();
        if (cleanBase64.startsWith('data:audio/')) {
            cleanBase64 = cleanBase64.split(',')[1];
        }
        cleanBase64 = cleanBase64.replace(/\s/g, '');
        
        if (!cleanBase64) {
            throw new Error('Empty base64 audio data');
        }
        
        logger.debug('Processing base64 audio:', {
            originalLength: base64Audio.length,
            cleanLength: cleanBase64.length,
            sampleRate,
            channels
        });
        
        const arrayBuffer = base64ToArrayBuffer(cleanBase64);
        return await processArrayBufferAudio(arrayBuffer, sampleRate, channels);
    } catch (error) {
        logger.error('Failed to process base64 audio:', error);
        throw error;
    }
}

/**
 * Play audio blob in browser
 */
export async function playAudioBlob(blob) {
    try {
        if (typeof window === 'undefined' || !window.Audio) {
            logger.warn('Audio playback not available in this environment');
            return { success: false, simulated: true };
        }
        
        const audio = new Audio();
        const url = URL.createObjectURL(blob);
        
        return new Promise((resolve, reject) => {
            audio.addEventListener('loadedmetadata', () => {
                logger.debug('Audio loaded successfully:', {
                    duration: audio.duration,
                    readyState: audio.readyState
                });
            });
            
            audio.addEventListener('ended', () => {
                URL.revokeObjectURL(url);
                resolve({ 
                    success: true, 
                    duration: audio.duration 
                });
            });
            
            audio.addEventListener('error', (e) => {
                URL.revokeObjectURL(url);
                reject(new Error(`Audio playback error: ${e.message || 'Unknown error'}`));
            });
            
            audio.src = url;
            audio.play().catch(reject);
        });
    } catch (error) {
        logger.error('Failed to play audio blob:', error);
        throw error;
    }
}

/**
 * Main audio processor class - simplified version
 */
export class SimpleAudioProcessor {
    constructor(options = {}) {
        this.logger = options.logger || logger;
        this.sampleRate = options.sampleRate || 24000;
        this.channels = options.channels || 1;
    }
    
    /**
     * Process any audio data (ArrayBuffer, base64, or response object)
     */
    async processAudio(audioData) {
        try {
            let blob;
            
            if (audioData instanceof ArrayBuffer) {
                blob = await processArrayBufferAudio(audioData, this.sampleRate, this.channels);
            } else if (typeof audioData === 'string') {
                blob = await processBase64Audio(audioData, this.sampleRate, this.channels);
            } else if (audioData && audioData.audio instanceof ArrayBuffer) {
                blob = await processArrayBufferAudio(audioData.audio, this.sampleRate, this.channels);
            } else if (audioData && typeof audioData.audio === 'string') {
                blob = await processBase64Audio(audioData.audio, this.sampleRate, this.channels);
            } else {
                throw new Error('Unsupported audio data format');
            }
            
            return { success: true, blob, audioLength: blob.size };
        } catch (error) {
            this.logger.error('Audio processing failed:', error);
            return { success: false, error: error.message };
        }
    }
    
    /**
     * Process and play audio data
     */
    async processAndPlay(audioData) {
        try {
            const processResult = await this.processAudio(audioData);
            
            if (!processResult.success) {
                return { audioPlayed: false, error: processResult.error };
            }
            
            const playResult = await playAudioBlob(processResult.blob);
            
            return {
                audioPlayed: playResult.success,
                audioLength: processResult.audioLength,
                duration: playResult.duration,
                simulated: playResult.simulated
            };
        } catch (error) {
            this.logger.error('Audio processing and playback failed:', error);
            return { audioPlayed: false, error: error.message };
        }
    }
}

/**
 * Helper function to replace processLLMResponseAudio
 */
export async function processLLMResponseAudio(response, options = {}) {
    const processor = new SimpleAudioProcessor(options);
    return await processor.processAndPlay(response);
}

/**
 * Helper function to replace playBase64Audio
 */
export async function playBase64Audio(base64Audio, format = 'wav', options = {}) {
    const processor = new SimpleAudioProcessor(options);
    return await processor.processAndPlay(base64Audio);
}

// Export for backward compatibility
export { SimpleAudioProcessor as AudioResponseProcessor };
export default SimpleAudioProcessor;