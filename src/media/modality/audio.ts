/**
 * Audio Processing Module
 * Centralizes all audio-related functionality including realtime processing, format conversion, and utilities
 */

import { createLogger } from '@/utils/logger';

// Create dedicated logger for audio modality
const logger = createLogger('AudioModality');

// Audio format constants
export const AudioFormat = {
    WAV: 'wav',
    MP3: 'mp3',
    PCM: 'pcm',
    OGG: 'ogg',
    WEBM: 'webm'
} as const;

export type AudioFormatType = typeof AudioFormat[keyof typeof AudioFormat];

export interface AudioConfig {
    sampleRate: number;
    numChannels: number;
    bitDepth: number;
    chunkDurationMs?: number;
}

// Default audio configuration based on <PERSON><PERSON> requirements (16-bit 24kHz mono PCM)
// CRITICAL: Matches Python working implementation exactly (RATE = 24000)
// Reference: debug/vad_mode.py - RATE = 24000, CHUNK = 3200, asyncio.sleep(0.2)
export const DEFAULT_AUDIO_CONFIG: AudioConfig = {
    sampleRate: 24000,    // 24kHz sample rate (matches Python working implementation)
    numChannels: 1,       // Mono audio (required by <PERSON><PERSON>)
    bitDepth: 16,         // 16-bit PCM (required by <PERSON><PERSON>)
    chunkDurationMs: 200  // 200ms chunks = 5 chunks/sec (matches Python rate limiting)
};

/**
 * Audio processing result interface
 */
export interface AudioProcessingResult {
    success: boolean;
    base64Audio?: string;
    error?: string;
    metadata?: {
        inputType: string;
        inputSize: number;
        outputSize: number;
        processingTimeMs: number;
    };
}

/**
 * Audio validation result interface
 */
export interface AudioValidationResult {
    isValid: boolean;
    type?: string;
    size?: number;
    error?: string;
}

/**
 * Detect audio format from header bytes
 */
export function detectAudioFormat(data: Uint8Array): AudioFormatType {
    if (!data || data.length < 4) {
        return AudioFormat.WAV; // Default to WAV if no data or too short
    }

    // Check for MP3 format (ID3 tag)
    if (data[0] === 0x49 && data[1] === 0x44 && data[2] === 0x33) {
        return AudioFormat.MP3;
    }

    // Check for MP3 format (MPEG frame sync)
    if (data[0] === 0xFF && (data[1] & 0xE0) === 0xE0) {
        return AudioFormat.MP3;
    }

    // Check for WAV format (RIFF header)
    if (data[0] === 0x52 && data[1] === 0x49 && data[2] === 0x46 && data[3] === 0x46) {
        return AudioFormat.WAV;
    }

    // Check for OGG format (OggS header)
    if (data[0] === 0x4F && data[1] === 0x67 && data[2] === 0x67 && data[3] === 0x53) {
        return AudioFormat.OGG;
    }

    return AudioFormat.WAV; // Default to WAV if unknown format
}

/**
 * Convert Float32Array to WAV format
 */
export function convertFloat32ToWav(
    samples: Float32Array,
    config: Partial<AudioConfig> = {}
): Blob {
    // Validate input
    if (!samples || !(samples instanceof Float32Array)) {
        const typeName = (samples as any)?.constructor?.name || typeof samples;
        throw new Error(`Invalid input: expected Float32Array, got ${typeName}`);
    }

    // Allow empty arrays - they will produce a minimal WAV file

    // Use centralized config for defaults
    const sampleRate = config.sampleRate ?? DEFAULT_AUDIO_CONFIG.sampleRate;
    const numChannels = config.numChannels ?? DEFAULT_AUDIO_CONFIG.numChannels;
    const bitDepth = config.bitDepth ?? DEFAULT_AUDIO_CONFIG.bitDepth;

    const bytesPerSample = bitDepth / 8;
    const blockAlign = numChannels * bytesPerSample;
    const dataLength = samples.length * bytesPerSample;
    const buffer = new ArrayBuffer(44 + dataLength);
    const view = new DataView(buffer);

    // Write WAV header
    writeString(view, 0, 'RIFF');
    view.setUint32(4, 36 + dataLength, true);
    writeString(view, 8, 'WAVE');
    writeString(view, 12, 'fmt ');
    view.setUint32(16, 16, true);
    view.setUint16(20, 1, true);
    view.setUint16(22, numChannels, true);
    view.setUint32(24, sampleRate, true);
    view.setUint32(28, sampleRate * blockAlign, true);
    view.setUint16(32, blockAlign, true);
    view.setUint16(34, bitDepth, true);
    writeString(view, 36, 'data');
    view.setUint32(40, dataLength, true);

    // Write audio data
    if (bitDepth === 16) {
        floatTo16BitPCM(view, 44, samples);
    } else if (bitDepth === 8) {
        floatTo8BitPCM(view, 44, samples);
    } else if (bitDepth === 32) {
        floatTo32BitPCM(view, 44, samples);
    }

    return new Blob([buffer], { type: 'audio/wav' });
}

/**
 * Validate audio data before processing
 */
export function validateAudioData(audioData: any): AudioValidationResult {
    logger.debug('Validating audio data', {
        type: typeof audioData,
        isNull: audioData === null,
        isUndefined: audioData === undefined
    });

    if (!audioData) {
        return {
            isValid: false,
            error: 'Audio data is null or undefined'
        };
    }

    if (typeof audioData === 'string') {
        return {
            isValid: audioData.length > 0,
            type: 'base64_string',
            size: audioData.length,
            error: audioData.length === 0 ? 'String is empty' : undefined
        };
    }

    if (audioData instanceof ArrayBuffer) {
        return {
            isValid: audioData.byteLength > 0,
            type: 'ArrayBuffer',
            size: audioData.byteLength,
            error: audioData.byteLength === 0 ? 'ArrayBuffer is empty' : undefined
        };
    }

    if (audioData instanceof Float32Array || audioData instanceof Int16Array) {
        return {
            isValid: audioData.length > 0,
            type: audioData.constructor.name,
            size: audioData.length,
            error: audioData.length === 0 ? 'Audio array is empty' : undefined
        };
    }

    return {
        isValid: false,
        error: `Unsupported audio data type: ${typeof audioData}`
    };
}

/**
 * Process audio data for real-time streaming APIs (consolidated from realtimeAudioProcessor)
 */
export async function processRealtimeAudio(
    audioData: ArrayBuffer | Float32Array | Int16Array | string,
    options: Partial<AudioConfig & { enableDebugLogging?: boolean }> = {}
): Promise<AudioProcessingResult> {
    const startTime = Date.now();

    const {
        sampleRate = DEFAULT_AUDIO_CONFIG.sampleRate,
        numChannels = DEFAULT_AUDIO_CONFIG.numChannels,
        bitDepth = DEFAULT_AUDIO_CONFIG.bitDepth,
        enableDebugLogging = false
    } = options;

    try {
        // Validate input
        const validation = validateAudioData(audioData);
        if (!validation.isValid) {
            return {
                success: false,
                error: validation.error
            };
        }

        if (enableDebugLogging) {
            logger.debug('🔄 Processing realtime audio data', {
                dataType: validation.type,
                dataSize: validation.size,
                config: { sampleRate, numChannels, bitDepth }
            });
        }

        // If already base64 string, return as-is
        if (typeof audioData === 'string') {
            const processingTime = Date.now() - startTime;
            return {
                success: true,
                base64Audio: audioData,
                metadata: {
                    inputType: 'base64_string',
                    inputSize: audioData.length,
                    outputSize: audioData.length,
                    processingTimeMs: processingTime
                }
            };
        }

        // Convert to Float32Array
        let float32Audio: Float32Array;

        if (audioData instanceof ArrayBuffer) {
            const int16Data = new Int16Array(audioData);
            float32Audio = new Float32Array(int16Data.length);

            for (let i = 0; i < int16Data.length; i++) {
                float32Audio[i] = int16Data[i] / (int16Data[i] < 0 ? 0x8000 : 0x7FFF);
            }

            if (enableDebugLogging) {
                logger.debug('🔄 Converted ArrayBuffer to Float32Array', {
                    originalLength: int16Data.length,
                    convertedLength: float32Audio.length
                });
            }
        } else if (audioData instanceof Float32Array) {
            float32Audio = audioData;
        } else if (audioData instanceof Int16Array) {
            float32Audio = new Float32Array(audioData.length);
            for (let i = 0; i < audioData.length; i++) {
                float32Audio[i] = audioData[i] / (audioData[i] < 0 ? 0x8000 : 0x7FFF);
            }
        } else {
            throw new Error(`Unsupported audio data type: ${typeof audioData}`);
        }

        // Convert to WAV
        const wavBlob = convertFloat32ToWav(float32Audio, {
            sampleRate,
            numChannels,
            bitDepth
        });

        // Convert to base64
        const arrayBuffer = await wavBlob.arrayBuffer();
        const uint8Array = new Uint8Array(arrayBuffer);
        const base64Audio = btoa(String.fromCharCode.apply(null, Array.from(uint8Array)));

        const processingTime = Date.now() - startTime;

        if (enableDebugLogging) {
            logger.debug('✅ Audio processing completed', {
                wavBlobSize: wavBlob.size,
                base64Length: base64Audio.length,
                processingTimeMs: processingTime
            });
        }

        return {
            success: true,
            base64Audio,
            metadata: {
                inputType: validation.type || 'unknown',
                inputSize: validation.size || 0,
                outputSize: base64Audio.length,
                processingTimeMs: processingTime
            }
        };

    } catch (error) {
        const processingTime = Date.now() - startTime;
        const errorMsg = `Audio processing failed: ${error instanceof Error ? error.message : String(error)}`;

        if (enableDebugLogging) {
            logger.error('❌ Audio processing error', { error: errorMsg, processingTimeMs: processingTime });
        }

        return {
            success: false,
            error: errorMsg,
            metadata: {
                inputType: typeof audioData,
                inputSize: 0,
                outputSize: 0,
                processingTimeMs: processingTime
            }
        };
    }
}

/**
 * Create fallback base64 audio from raw data
 */
export function createFallbackBase64Audio(audioData: ArrayBuffer | Uint8Array): string {
    try {
        const uint8Array = new Uint8Array(audioData instanceof ArrayBuffer ? audioData : audioData.buffer);
        return btoa(String.fromCharCode.apply(null, Array.from(uint8Array)));
    } catch (error) {
        logger.warn('Fallback base64 conversion failed:', error);
        return '';
    }
}

/**
 * Convert base64 string to Blob
 */
export function base64ToBlob(base64: string, mimeType: string = 'audio/wav'): Blob {
    const byteCharacters = atob(base64);
    const byteArrays: Uint8Array[] = [];

    for (let offset = 0; offset < byteCharacters.length; offset += 1024) {
        const slice = byteCharacters.slice(offset, offset + 1024);
        const byteNumbers = new Array(slice.length);

        for (let i = 0; i < slice.length; i++) {
            byteNumbers[i] = slice.charCodeAt(i);
        }

        byteArrays.push(new Uint8Array(byteNumbers));
    }

    return new Blob(byteArrays, { type: mimeType });
}

/**
 * Convert Blob to base64 string
 */
export function blobToBase64(blob: Blob): Promise<string> {
    return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = () => {
            const result = reader.result as string;
            const base64 = result.split(',')[1];
            resolve(base64);
        };
        reader.onerror = reject;
        reader.readAsDataURL(blob);
    });
}

/**
 * Check if an AudioBuffer contains actual audio data
 */
export function checkAudioBuffer(
    buffer: AudioBuffer,
    options: {
        minAmplitude?: number;
        checkMultipleRanges?: boolean;
        logger?: any;
    } = {}
): { hasAudio: boolean; maxValue: number; percentNonZero: number } {
    const {
        minAmplitude = 0.0001,
        checkMultipleRanges = true,
        logger: loggerOverride = logger
    } = options;

    if (!buffer || !buffer.numberOfChannels || buffer.numberOfChannels < 1) {
        return { hasAudio: false, maxValue: 0, percentNonZero: 0 };
    }

    const data = buffer.getChannelData(0);
    let nonZeroCount = 0;
    let maxValue = 0;
    let samplesChecked = 0;

    // Function to check a specific range of the buffer
    const checkRange = (start: number, end: number) => {
        let rangeNonZeroCount = 0;
        let rangeMaxValue = 0;
        const rangeLength = end - start;
        const step = Math.max(1, Math.floor(rangeLength / 333));

        for (let i = start; i < end; i += step) {
            const absValue = Math.abs(data[i]);
            if (absValue > minAmplitude) {
                rangeNonZeroCount++;
            }
            rangeMaxValue = Math.max(rangeMaxValue, absValue);
        }

        return {
            nonZeroCount: rangeNonZeroCount,
            maxValue: rangeMaxValue,
            samplesChecked: Math.ceil(rangeLength / step)
        };
    };

    // Check multiple ranges if enabled and buffer is long enough
    if (checkMultipleRanges && data.length > 3000) {
        const ranges = [
            { start: 0, end: Math.min(1000, data.length) },
            { start: Math.floor(data.length / 2) - 500, end: Math.floor(data.length / 2) + 500 },
            { start: Math.max(0, data.length - 1000), end: data.length }
        ];

        for (const range of ranges) {
            const result = checkRange(range.start, range.end);
            nonZeroCount += result.nonZeroCount;
            maxValue = Math.max(maxValue, result.maxValue);
            samplesChecked += result.samplesChecked;
        }
    } else {
        const step = Math.max(1, Math.floor(data.length / 1000));
        for (let i = 0; i < data.length; i += step) {
            const absValue = Math.abs(data[i]);
            if (absValue > minAmplitude) {
                nonZeroCount++;
            }
            maxValue = Math.max(maxValue, absValue);
        }
        samplesChecked = Math.ceil(data.length / step);
    }

    const percentNonZero = (nonZeroCount / samplesChecked) * 100;
    const hasAudio = percentNonZero >= 1.0 && maxValue >= minAmplitude;

    return { hasAudio, maxValue, percentNonZero };
}

/**
 * Create a fallback tone generator
 */
export function createFallbackTone(options: {
    frequency?: number;
    duration?: number;
    sampleRate?: number;
    amplitude?: number;
    audioContext?: AudioContext;
} = {}): AudioBuffer {
    const frequency = options.frequency || 440;
    const duration = options.duration || 1.0;
    const sampleRate = options.sampleRate || 22050;
    const amplitude = options.amplitude || 0.5;

    let audioContext: AudioContext;
    let shouldCloseContext = false;

    if (options.audioContext) {
        audioContext = options.audioContext;
    } else {
        if (typeof window !== 'undefined' && window.AudioContext) {
            audioContext = new AudioContext();
            shouldCloseContext = true;
        } else {
            throw new Error('AudioContext not available');
        }
    }

    const buffer = audioContext.createBuffer(1, Math.ceil(duration * sampleRate), sampleRate);
    const channelData = buffer.getChannelData(0);

    for (let i = 0; i < channelData.length; i++) {
        channelData[i] = amplitude * Math.sin(2 * Math.PI * frequency * i / sampleRate);
    }

    if (shouldCloseContext) {
        audioContext.close().catch(console.warn);
    }

    return buffer;
}

/**
 * Create an Audio element from a blob
 */
export function createAudioFromBlob(blob: Blob): Promise<HTMLAudioElement> {
    return new Promise((resolve, reject) => {
        const audio = new Audio();
        const url = URL.createObjectURL(blob);

        audio.onloadeddata = () => {
            URL.revokeObjectURL(url);
            resolve(audio);
        };

        audio.onerror = (error) => {
            URL.revokeObjectURL(url);
            reject(error);
        };

        audio.src = url;
    });
}

/**
 * Play audio with promise
 */
export function playAudioWithPromise(audio: HTMLAudioElement): Promise<void> {
    return new Promise((resolve, reject) => {
        const onEnded = () => {
            audio.removeEventListener('ended', onEnded);
            audio.removeEventListener('error', onError);
            resolve();
        };

        const onError = (error: any) => {
            audio.removeEventListener('ended', onEnded);
            audio.removeEventListener('error', onError);
            reject(error);
        };

        audio.addEventListener('ended', onEnded);
        audio.addEventListener('error', onError);

        audio.play().catch(reject);
    });
}

/**
 * Split audio into chunks
 */
export function splitAudioIntoChunks(
    audio: HTMLAudioElement,
    chunkDurationMs: number = DEFAULT_AUDIO_CONFIG.chunkDurationMs!
): Array<{ duration: number; isLast: boolean }> {
    const numChunks = Math.ceil(audio.duration / (chunkDurationMs / 1000));
    const chunks = [];

    for (let i = 0; i < numChunks; i++) {
        const isLast = i === numChunks - 1;
        const chunkDuration = isLast ?
            audio.duration - (i * chunkDurationMs / 1000) :
            chunkDurationMs / 1000;

        chunks.push({
            duration: chunkDuration,
            isLast
        });
    }

    return chunks;
}

/**
 * Convert Base64 encoded string to ArrayBuffer
 */
export function b64ToArrayBuffer(b64String: string): ArrayBuffer {
    const binaryString = atob(b64String);
    const bytes = new Uint8Array(binaryString.length);

    for (let i = 0; i < binaryString.length; i++) {
        bytes[i] = binaryString.charCodeAt(i);
    }

    return bytes.buffer;
}

/**
 * Concatenate an array of ArrayBuffers
 */
export function concatArrayBuffers(buffers: ArrayBuffer[]): ArrayBuffer {
    const totalLength = buffers.reduce((acc, buffer) => acc + buffer.byteLength, 0);
    const result = new Uint8Array(totalLength);
    let offset = 0;

    for (const buffer of buffers) {
        result.set(new Uint8Array(buffer), offset);
        offset += buffer.byteLength;
    }

    return result.buffer;
}

/**
 * Convert PCM buffer to AudioBuffer (signed 16bit little endian)
 */
export function pcmToAudioBuffer(
    buffer: ArrayBuffer,
    sampleRate: number = 22050
): AudioBuffer | null {
    if (typeof window === 'undefined' || !window.AudioContext) {
        console.warn('AudioContext not available in this environment');
        return null;
    }

    try {
        const audioContext = new AudioContext();
        const pcmData = new Int16Array(buffer);
        const audioBuffer = audioContext.createBuffer(1, pcmData.length, sampleRate);
        const channelData = audioBuffer.getChannelData(0);

        // Convert 16-bit PCM to float32 (-1.0 to 1.0)
        for (let i = 0; i < pcmData.length; i++) {
            channelData[i] = pcmData[i] / (pcmData[i] < 0 ? 0x8000 : 0x7FFF);
        }

        audioContext.close().catch(console.warn);
        return audioBuffer;
    } catch (error) {
        console.error('Failed to create AudioBuffer:', error);
        return null;
    }
}

/**
 * Set reverb for audio context
 */
export async function setReverb(
    audioContext: AudioContext,
    reverbNode: ConvolverNode,
    impulseResponse: ArrayBuffer | null = null
): Promise<void> {
    if (!impulseResponse) {
        reverbNode.buffer = null;
        return;
    }

    try {
        const audioBuffer = await audioContext.decodeAudioData(impulseResponse);
        reverbNode.buffer = audioBuffer;
    } catch (error) {
        console.error('Failed to set reverb:', error);
    }
}

/**
 * Set mixer gain levels
 */
export function setMixerGain(
    speechGainNode: GainNode,
    backgroundGainNode: GainNode,
    speechGain: number | null = null,
    backgroundGain: number | null = null
): void {
    if (speechGain !== null) {
        speechGainNode.gain.value = Math.max(0, Math.min(1, speechGain));
    }
    if (backgroundGain !== null) {
        backgroundGainNode.gain.value = Math.max(0, Math.min(1, backgroundGain));
    }
}

/**
 * Extract role name from audio file path
 */
export function extractRoleNameFromAudioFile(
    audioFilePath: string,
    options: { logger?: any } = {}
): string {
    const logger = options.logger || console;

    try {
        // Extract filename from path
        const filename = audioFilePath.split('/').pop() || audioFilePath;

        // Remove extension
        const nameWithoutExt = filename.replace(/\.[^/.]+$/, '');

        // Extract role name (assuming format like "role_timestamp.wav")
        const parts = nameWithoutExt.split('_');

        if (parts.length > 0) {
            return parts[0];
        }

        return nameWithoutExt;
    } catch (error) {
        logger.warn('Failed to extract role name:', error);
        return 'unknown';
    }
}

/**
 * Prepare request payload for TTS services
 */
export function prepareTTSRequestPayload(
    text: string,
    options: {
        voice?: string;
        speed?: number;
        pitch?: number;
        volume?: number;
        format?: string;
        sampleRate?: number;
    } = {}
): { payload: any; headers: Record<string, string> } {
    const {
        voice = 'default',
        speed = 1.0,
        pitch = 1.0,
        volume = 1.0,
        format = 'wav',
        sampleRate = 22050
    } = options;

    const payload = {
        text,
        voice,
        speed: Math.max(0.25, Math.min(4.0, speed)),
        pitch: Math.max(0.5, Math.min(2.0, pitch)),
        volume: Math.max(0.0, Math.min(1.0, volume)),
        format,
        sampleRate
    };

    const headers = {
        'Content-Type': 'application/json',
        'Accept': `audio/${format}`
    };

    return { payload, headers };
}

// Helper functions for WAV encoding
function writeString(view: DataView, offset: number, string: string): void {
    for (let i = 0; i < string.length; i++) {
        view.setUint8(offset + i, string.charCodeAt(i));
    }
}

function floatTo16BitPCM(view: DataView, offset: number, samples: Float32Array): void {
    for (let i = 0; i < samples.length; i++) {
        const sample = Math.max(-1, Math.min(1, samples[i]));
        view.setInt16(offset + i * 2, sample < 0 ? sample * 0x8000 : sample * 0x7FFF, true);
    }
}

function floatTo8BitPCM(view: DataView, offset: number, samples: Float32Array): void {
    for (let i = 0; i < samples.length; i++) {
        const sample = Math.max(-1, Math.min(1, samples[i]));
        view.setUint8(offset + i, (sample + 1) * 128);
    }
}

function floatTo32BitPCM(view: DataView, offset: number, samples: Float32Array): void {
    for (let i = 0; i < samples.length; i++) {
        view.setFloat32(offset + i * 4, samples[i], true);
    }
}

/**
 * Convert Float32Array to Int16Array PCM (for realtime streaming)
 */
export function float32ToInt16PCM(floatBuffer: Float32Array): Int16Array {
    const int16 = new Int16Array(floatBuffer.length);
    for (let i = 0; i < floatBuffer.length; i++) {
        let s = Math.max(-1, Math.min(1, floatBuffer[i]));
        int16[i] = s < 0 ? s * 0x8000 : s * 0x7FFF;
    }
    return int16;
}

/**
 * Upsample a Float32Array audio buffer to 24kHz using linear interpolation.
 * @param input Float32Array input audio samples
 * @param inputSampleRate Input sample rate (e.g., 16000)
 * @returns Float32Array resampled to 24000Hz
 */
export function upsampleTo24kHz(input: Float32Array, inputSampleRate: number): Float32Array {
    if (inputSampleRate === 24000) return input;
    const outputLength = Math.round(input.length * 24000 / inputSampleRate);
    const output = new Float32Array(outputLength);
    for (let i = 0; i < outputLength; i++) {
        const t = i * (input.length - 1) / (outputLength - 1);
        const idx = Math.floor(t);
        const frac = t - idx;
        const s0 = input[idx];
        const s1 = idx + 1 < input.length ? input[idx + 1] : input[idx];
        output[i] = s0 + (s1 - s0) * frac;
    }
    return output;
}

/**
 * Robust base64 to Uint8Array conversion with validation
 */
export function base64ToUint8Array(base64: string): Uint8Array {
    try {
        // Clean and validate base64 string
        if (!base64 || typeof base64 !== 'string') {
            throw new Error('Invalid base64 audio data: must be a non-empty string');
        }

        // Remove any whitespace, line breaks, and data URL prefix if present
        let cleanBase64 = base64.trim();
        if (cleanBase64.startsWith('data:audio/')) {
            cleanBase64 = cleanBase64.split(',')[1];
        }
        cleanBase64 = cleanBase64.replace(/\s/g, ''); // Remove all whitespace

        // Check for empty string after cleaning
        if (!cleanBase64) {
            throw new Error('Invalid base64 audio data: empty string after cleaning');
        }

        // Validate base64 format
        if (!/^[A-Za-z0-9+/]*={0,2}$/.test(cleanBase64)) {
            throw new Error('Invalid base64 format');
        }

        // Convert to Uint8Array
        const binaryString = atob(cleanBase64);
        const bytes = new Uint8Array(binaryString.length);

        for (let i = 0; i < binaryString.length; i++) {
            bytes[i] = binaryString.charCodeAt(i);
        }

        return bytes;
    } catch (error) {
        throw new Error(`Base64 to Uint8Array conversion failed: ${error instanceof Error ? error.message : String(error)}`);
    }
}

/**
 * Detect if base64 data contains a complete WAV file or raw PCM data
 */
export function detectAudioFormatFromBase64(base64: string): {
    isComplete: boolean;
    format: 'complete_wav' | 'raw_pcm' | 'unknown';
    needsHeaders: boolean;
    audioInfo?: {
        sampleRate?: number;
        channels?: number;
        bitDepth?: number;
    };
} {
    try {
        // Validate input
        if (!base64 || typeof base64 !== 'string') {
            logger.warn('Audio format detection: invalid base64 input');
            return {
                isComplete: false,
                format: 'unknown',
                needsHeaders: true
            };
        }

        // Clean base64 string
        let cleanBase64 = base64.trim();
        if (cleanBase64.startsWith('data:audio/')) {
            cleanBase64 = cleanBase64.split(',')[1];
        }
        cleanBase64 = cleanBase64.replace(/\s/g, '');

        // Check for empty string after cleaning
        if (!cleanBase64) {
            logger.warn('Audio format detection: empty base64 string');
            return {
                isComplete: false,
                format: 'unknown',
                needsHeaders: true
            };
        }

        // Decode first 44 bytes (WAV header size) to check format
        const headerBytes = atob(cleanBase64.slice(0, Math.min(60, cleanBase64.length))); // ~44 bytes in base64
        const headerView = new Uint8Array(Math.min(44, headerBytes.length));

        for (let i = 0; i < headerView.length && i < headerBytes.length; i++) {
            headerView[i] = headerBytes.charCodeAt(i);
        }

        // Check for RIFF header (0x52494646 = "RIFF")
        const hasRIFF = headerView.length >= 4 &&
            headerView[0] === 0x52 && headerView[1] === 0x49 &&
            headerView[2] === 0x46 && headerView[3] === 0x46;

        // Check for WAVE format (0x57415645 = "WAVE" at offset 8)
        const hasWAVE = headerView.length >= 12 &&
            headerView[8] === 0x57 && headerView[9] === 0x41 &&
            headerView[10] === 0x56 && headerView[11] === 0x45;

        // Check for fmt chunk (0x666d7420 = "fmt " at offset 12)
        const hasFMT = headerView.length >= 16 &&
            headerView[12] === 0x66 && headerView[13] === 0x6d &&
            headerView[14] === 0x74 && headerView[15] === 0x20;

        const isCompleteWAV = hasRIFF && hasWAVE && hasFMT;

        let audioInfo = {};
        if (isCompleteWAV && headerView.length >= 44) {
            // Extract audio parameters from WAV header
            const view = new DataView(headerView.buffer);
            audioInfo = {
                channels: view.getUint16(22, true),
                sampleRate: view.getUint32(24, true),
                bitDepth: view.getUint16(34, true)
            };
        }

        logger.debug('Audio format detection:', {
            base64Length: base64.length,
            headerPreview: Array.from(headerView.slice(0, 16)).map(b => b.toString(16).padStart(2, '0')).join(' '),
            hasRIFF,
            hasWAVE,
            hasFMT,
            isCompleteWAV,
            audioInfo
        });

        return {
            isComplete: isCompleteWAV,
            format: isCompleteWAV ? 'complete_wav' : 'raw_pcm',
            needsHeaders: !isCompleteWAV,
            audioInfo: Object.keys(audioInfo).length > 0 ? audioInfo : undefined
        };

    } catch (error) {
        logger.warn('Audio format detection failed:', error);
        return {
            isComplete: false,
            format: 'unknown',
            needsHeaders: true
        };
    }
}

/**
 * Convert raw PCM base64 data to complete WAV format with headers
 * Uses the standard configuration for LLM audio responses (24kHz, 16-bit, mono)
 */
export function convertPCMToWAVBlob(base64PCM: string, config: Partial<AudioConfig> = {}): Blob {
    try {
        // Use standard LLM audio configuration
        const {
            sampleRate = 24000,  // Standard for most LLM audio responses
            numChannels = 1,     // Mono audio
            bitDepth = 16        // 16-bit PCM
        } = config;

        // Decode PCM data
        const pcmData = atob(base64PCM);
        const pcmBytes = new Uint8Array(pcmData.length);
        for (let i = 0; i < pcmData.length; i++) {
            pcmBytes[i] = pcmData.charCodeAt(i);
        }

        const bytesPerSample = bitDepth / 8;
        const blockAlign = numChannels * bytesPerSample;
        const dataSize = pcmBytes.length;
        const fileSize = 36 + dataSize;

        // Create WAV header (44 bytes)
        const header = new ArrayBuffer(44);
        const view = new DataView(header);

        // RIFF header
        view.setUint32(0, 0x46464952, true);   // "RIFF" in little-endian
        view.setUint32(4, fileSize, true);     // File size - 8
        view.setUint32(8, 0x45564157, true);   // "WAVE" in little-endian

        // fmt chunk
        view.setUint32(12, 0x20746d66, true);  // "fmt " in little-endian
        view.setUint32(16, 16, true);          // fmt chunk size
        view.setUint16(20, 1, true);           // PCM format
        view.setUint16(22, numChannels, true); // Number of channels
        view.setUint32(24, sampleRate, true);  // Sample rate
        view.setUint32(28, sampleRate * blockAlign, true); // Byte rate
        view.setUint16(32, blockAlign, true);  // Block align
        view.setUint16(34, bitDepth, true);    // Bits per sample

        // data chunk
        view.setUint32(36, 0x61746164, true);  // "data" in little-endian
        view.setUint32(40, dataSize, true);    // Data size

        // Combine header and PCM data
        const wavBuffer = new Uint8Array(44 + dataSize);
        wavBuffer.set(new Uint8Array(header), 0);
        wavBuffer.set(pcmBytes, 44);

        logger.debug('PCM to WAV conversion completed:', {
            pcmSize: dataSize,
            wavSize: wavBuffer.length,
            sampleRate,
            numChannels,
            bitDepth,
            estimatedDuration: dataSize / (sampleRate * blockAlign)
        });

        return new Blob([wavBuffer], { type: 'audio/wav' });

    } catch (error) {
        logger.error('PCM to WAV conversion failed:', error);
        throw new Error(`Failed to convert PCM to WAV: ${error instanceof Error ? error.message : String(error)}`);
    }
}

/**
 * Validate audio blob by checking WAV header structure
 */
export async function validateAudioBlob(audioBlob: Blob): Promise<{
    isValid: boolean;
    audioInfo?: {
        channels: number;
        sampleRate: number;
        bitDepth: number;
        dataSize: number;
        duration: number;
    };
    error?: string;
}> {
    try {
        if (audioBlob.size < 44) {
            return { isValid: false, error: 'Audio blob too small to contain WAV header' };
        }

        const arrayBuffer = await audioBlob.arrayBuffer();
        const view = new DataView(arrayBuffer);

        // Check WAV header components
        const riff = view.getUint32(0, true) === 0x46464952; // "RIFF"
        const wave = view.getUint32(8, true) === 0x45564157; // "WAVE"
        const fmt = view.getUint32(12, true) === 0x20746d66; // "fmt "
        const data = view.getUint32(36, true) === 0x61746164; // "data"

        const isValidWAV = riff && wave && fmt && data;

        if (!isValidWAV) {
            const missing = [];
            if (!riff) missing.push('RIFF header');
            if (!wave) missing.push('WAVE format');
            if (!fmt) missing.push('fmt chunk');
            if (!data) missing.push('data chunk');

            return {
                isValid: false,
                error: `Invalid WAV structure. Missing: ${missing.join(', ')}`
            };
        }

        // Extract audio information
        const audioInfo = {
            channels: view.getUint16(22, true),
            sampleRate: view.getUint32(24, true),
            bitDepth: view.getUint16(34, true),
            dataSize: view.getUint32(40, true),
            duration: 0
        };

        // Calculate duration
        const bytesPerSample = audioInfo.bitDepth / 8;
        const samplesPerSecond = audioInfo.sampleRate * audioInfo.channels;
        audioInfo.duration = audioInfo.dataSize / (samplesPerSecond * bytesPerSample);

        return { isValid: true, audioInfo };

    } catch (error) {
        return {
            isValid: false,
            error: `Audio validation failed: ${error instanceof Error ? error.message : String(error)}`
        };
    }
}

/**
 * Enhanced base64 to Blob conversion with automatic format detection and processing
 */
export function base64ToBlobEnhanced(base64: string, mimeType: string = 'audio/wav'): Blob {
    try {
        // Validate input
        if (!base64 || typeof base64 !== 'string') {
            throw new Error('Invalid base64 audio data: must be a non-empty string');
        }

        // First, detect what kind of audio data we have
        const formatInfo = detectAudioFormatFromBase64(base64);

        if (formatInfo.format === 'unknown') {
            throw new Error('Unable to detect audio format from base64 data');
        }

        if (formatInfo.isComplete) {
            // Already a complete WAV file, convert directly
            logger.debug('Audio is complete WAV, converting directly to blob');
            const uint8Array = base64ToUint8Array(base64);
            return new Blob([uint8Array], { type: mimeType });
        } else if (formatInfo.format === 'raw_pcm') {
            // Raw PCM data, need to add WAV headers
            logger.debug('Audio is raw PCM, adding WAV headers');
            return convertPCMToWAVBlob(base64, {
                sampleRate: formatInfo.audioInfo?.sampleRate || 24000,
                numChannels: formatInfo.audioInfo?.channels || 1,
                bitDepth: formatInfo.audioInfo?.bitDepth || 16
            });
        } else {
            // Unknown format, try direct conversion and hope for the best
            logger.warn('Unknown audio format, attempting direct conversion');
            const uint8Array = base64ToUint8Array(base64);
            return new Blob([uint8Array], { type: mimeType });
        }

    } catch (error) {
        logger.error('Enhanced base64 to blob conversion failed:', error);
        throw new Error(`Failed to convert base64 to audio blob: ${error instanceof Error ? error.message : String(error)}`);
    }
}

/**
 * Real-time Streaming Audio Processor
 * Handles real-time streaming of PCM audio chunks (like Qwen-Omni 方式2)
 */
export class StreamingAudioProcessor {
    private audioContext: AudioContext | null = null;
    private audioQueue: AudioBuffer[] = [];
    private isPlaying: boolean = false;
    private nextBufferTime: number = 0;
    private logger: any;
    private sampleRate: number;
    private channels: number;
    private bitDepth: number;
    private chunkBuffer: number[] = [];

    constructor(options: {
        sampleRate?: number;
        channels?: number;
        bitDepth?: number;
        logger?: any;
    } = {}) {
        this.sampleRate = options.sampleRate || 24000;  // Qwen-Omni default
        this.channels = options.channels || 1;           // Mono
        this.bitDepth = options.bitDepth || 16;          // 16-bit
        this.logger = options.logger || logger;

        if (typeof window !== 'undefined') {
            this.initWebAudio();
        }
    }

    private async initWebAudio() {
        try {
            this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)({
                sampleRate: this.sampleRate
            });
            this.logger.debug('Web Audio initialized:', {
                sampleRate: this.audioContext.sampleRate,
                channels: this.channels,
                bitDepth: this.bitDepth
            });
        } catch (error) {
            this.logger.error('Failed to initialize Web Audio:', error instanceof Error ? error.message : String(error));
        }
    }

    /**
     * Process a single streaming audio chunk in real-time
     * Following Qwen-Omni 方式2 pattern: 边生成边实时播放
     */
    async processStreamingChunk(base64PCMChunk: string): Promise<void> {
        try {
            if (!base64PCMChunk) return;

            this.logger.debug('Processing streaming PCM chunk:', {
                chunkLength: base64PCMChunk.length,
                isPlaying: this.isPlaying
            });

            if (typeof window !== 'undefined' && this.audioContext) {
                // Browser: Use Web Audio API for real-time playback
                await this.playWebAudioChunk(base64PCMChunk);
            } else {
                // Node.js: Would use node-speaker (commented out for browser compatibility)
                this.logger.debug('Node.js environment - streaming audio would use node-speaker');
                // const pcmBuffer = Buffer.from(base64PCMChunk, 'base64');
                // this.speaker.write(pcmBuffer);
            }

        } catch (error) {
            this.logger.error('Error processing streaming chunk:', error);
        }
    }

    private async playWebAudioChunk(base64PCMChunk: string): Promise<void> {
        if (!this.audioContext) return;

        try {
            // Decode PCM data from base64
            const pcmData = atob(base64PCMChunk);
            const pcmBytes = new Uint8Array(pcmData.length);
            for (let i = 0; i < pcmData.length; i++) {
                pcmBytes[i] = pcmData.charCodeAt(i);
            }

            // Convert to 16-bit signed integers
            const int16Data = new Int16Array(pcmBytes.buffer);

            // Convert to Float32Array for Web Audio
            const floatData = new Float32Array(int16Data.length);
            for (let i = 0; i < int16Data.length; i++) {
                floatData[i] = int16Data[i] / (int16Data[i] < 0 ? 0x8000 : 0x7FFF);
            }

            // Create AudioBuffer
            const audioBuffer = this.audioContext.createBuffer(
                this.channels,
                floatData.length / this.channels,
                this.sampleRate
            );

            // Copy data to AudioBuffer
            audioBuffer.getChannelData(0).set(floatData);

            // Schedule playback
            this.scheduleAudioBuffer(audioBuffer);

        } catch (error) {
            this.logger.error('Error playing web audio chunk:', error);
        }
    }

    private scheduleAudioBuffer(audioBuffer: AudioBuffer): void {
        if (!this.audioContext) return;

        const source = this.audioContext.createBufferSource();
        source.buffer = audioBuffer;
        source.connect(this.audioContext.destination);

        // Schedule at the next available time
        const currentTime = this.audioContext.currentTime;
        const scheduledTime = Math.max(currentTime, this.nextBufferTime);

        source.start(scheduledTime);
        this.nextBufferTime = scheduledTime + audioBuffer.duration;

        if (!this.isPlaying) {
            this.isPlaying = true;
            this.logger.debug('Started streaming audio playback');
        }

        // Clean up when audio ends
        source.onended = () => {
            if (this.nextBufferTime <= this.audioContext!.currentTime + 0.1) {
                this.isPlaying = false;
                this.logger.debug('Streaming audio playback ended');
            }
        };
    }

    /**
     * Signal end of streaming
     */
    endStream(): void {
        this.isPlaying = false;
        this.nextBufferTime = 0;
        this.audioQueue = [];
        this.chunkBuffer = [];
        this.logger.debug('Audio streaming ended');
    }

    /**
     * Get streaming status
     */
    getStatus(): { isPlaying: boolean; queueLength: number } {
        return {
            isPlaying: this.isPlaying,
            queueLength: this.audioQueue.length
        };
    }
}

/**
 * Audio Response Processor
 * Handles LangChain/LangGraph audio responses from LLMs
 */
export class AudioResponseProcessor {
    private logger: any;
    private streamingProcessor: StreamingAudioProcessor | null = null;

    constructor(options: { logger?: any } = {}) {
        this.logger = options.logger || logger;
    }

    /**
     * Enable streaming audio processing mode
     * Creates a streaming processor for real-time audio chunk playback
     */
    enableStreaming(options: {
        sampleRate?: number;
        channels?: number;
        bitDepth?: number;
    } = {}): void {
        this.streamingProcessor = new StreamingAudioProcessor({
            sampleRate: options.sampleRate || 24000,
            channels: options.channels || 1,
            bitDepth: options.bitDepth || 16,
            logger: this.logger
        });
        this.logger.info('Streaming audio processing enabled');
    }

    /**
     * Process streaming audio chunk in real-time (Qwen-Omni 方式2)
     * Call this method for each streaming chunk as it arrives
     */
    async processStreamingChunk(base64PCMChunk: string): Promise<void> {
        if (!this.streamingProcessor) {
            this.logger.warn('Streaming not enabled. Call enableStreaming() first.');
            return;
        }

        await this.streamingProcessor.processStreamingChunk(base64PCMChunk);
    }

    /**
     * End streaming audio session
     */
    endStreaming(): void {
        if (this.streamingProcessor) {
            this.streamingProcessor.endStream();
            this.streamingProcessor = null;
            this.logger.info('Streaming audio session ended');
        }
    }

    /**
     * Get streaming status
     */
    getStreamingStatus(): { isStreaming: boolean; isPlaying: boolean; queueLength: number } {
        if (!this.streamingProcessor) {
            return { isStreaming: false, isPlaying: false, queueLength: 0 };
        }

        const status = this.streamingProcessor.getStatus();
        return {
            isStreaming: true,
            isPlaying: status.isPlaying,
            queueLength: status.queueLength
        };
    }

    /**
     * Process LangChain/LangGraph response with audio content
     * Based on OpenAI ChatGPT pattern from docs
     * Now supports both complete audio and streaming audio processing
     */
    async processLLMResponse(response: any, options: {
        enableStreaming?: boolean;
        streamingConfig?: {
            sampleRate?: number;
            channels?: number;
            bitDepth?: number;
        }
    } = {}): Promise<{ audioPlayed: boolean; audioLength?: number; error?: string; isStreaming?: boolean }> {
        try {
            this.logger.debug('Processing LLM response for audio content', {
                hasResponse: !!response,
                responseKeys: response ? Object.keys(response) : null
            });

            let audioData = null;
            let audioFormat = 'wav';

            // Check multiple possible locations for audio data based on LangChain docs
            if (response?.additional_kwargs?.audio) {
                // OpenAI ChatGPT pattern: additional_kwargs.audio
                const audioContent = response.additional_kwargs.audio;
                audioData = audioContent.data;
                this.logger.debug('Found audio in additional_kwargs.audio', {
                    hasData: !!audioData,
                    dataLength: audioData?.length
                });
            } else if (response?.content?.[0]?.type === 'audio') {
                // Content array pattern: content[0] with type audio
                const audioContent = response.content[0];
                audioData = audioContent.data;
                this.logger.debug('Found audio in content[0]', {
                    hasData: !!audioData,
                    dataLength: audioData?.length
                });
            } else if (response?.audio) {
                // Direct audio property
                audioData = response.audio;
                this.logger.debug('Found audio in direct property', {
                    hasData: !!audioData,
                    dataLength: audioData?.length
                });
            } else if (typeof response === 'string' && response.startsWith('UklGR')) {
                // Raw base64 WAV data (starts with 'RIFF' encoded)
                audioData = response;
                this.logger.debug('Response appears to be raw base64 audio data');
            }

            if (audioData) {
                this.logger.info('🔊 Audio content detected in LLM response, playing...');
                await this.playAudioData(audioData, audioFormat);
                return { audioPlayed: true, audioLength: audioData.length };
            } else {
                this.logger.debug('No audio content found in LLM response');
                return { audioPlayed: false };
            }

        } catch (error) {
            this.logger.error('Error processing LLM response audio:', error);
            return { audioPlayed: false, error: error instanceof Error ? error.message : String(error) };
        }
    }

    /**
     * Play base64 audio data with enhanced format detection and error handling
     */
    async playAudioData(base64Audio: string, format: string = 'wav'): Promise<{ success: boolean; duration?: number; simulated?: boolean }> {
        try {
            if (typeof window !== 'undefined' && window.Audio) {
                // Browser environment
                this.logger.debug('Playing audio in browser environment');

                // Detect and log audio format information
                const formatInfo = detectAudioFormatFromBase64(base64Audio);
                this.logger.debug('Audio format analysis:', {
                    originalLength: base64Audio.length,
                    format: formatInfo.format,
                    isComplete: formatInfo.isComplete,
                    needsHeaders: formatInfo.needsHeaders,
                    audioInfo: formatInfo.audioInfo,
                    preview: base64Audio.slice(0, 50) + '...'
                });

                // Create blob using enhanced conversion with format detection
                const blob = base64ToBlobEnhanced(base64Audio, `audio/${format}`);

                if (!blob || blob.size === 0) {
                    throw new Error('Failed to create audio blob from base64 data');
                }

                // Validate the audio blob structure
                const validation = await validateAudioBlob(blob);
                if (!validation.isValid) {
                    this.logger.warn('Audio blob validation failed:', {
                        error: validation.error,
                        blobSize: blob.size,
                        blobType: blob.type
                    });
                    // Continue anyway - some browsers might be more forgiving
                }

                this.logger.debug('Audio blob created and validated:', {
                    size: blob.size,
                    type: blob.type,
                    validation: validation.isValid ? 'passed' : 'failed',
                    audioInfo: validation.audioInfo
                });

                const audioUrl = URL.createObjectURL(blob);

                // Create and play audio element
                const audio = new Audio(audioUrl);

                return new Promise((resolve, reject) => {
                    // Enhanced error handling with specific error codes
                    audio.onerror = (event) => {
                        URL.revokeObjectURL(audioUrl);
                        const error = audio.error;
                        let errorMessage = 'Unknown audio error';
                        let errorCode = 'UNKNOWN';

                        if (error) {
                            switch (error.code) {
                                case error.MEDIA_ERR_ABORTED:
                                    errorMessage = 'Audio playback aborted by user or system';
                                    errorCode = 'ABORTED';
                                    break;
                                case error.MEDIA_ERR_NETWORK:
                                    errorMessage = 'Network error during audio playback';
                                    errorCode = 'NETWORK';
                                    break;
                                case error.MEDIA_ERR_DECODE:
                                    errorMessage = 'Audio decode error - invalid format or corrupted data';
                                    errorCode = 'DECODE';
                                    break;
                                case error.MEDIA_ERR_SRC_NOT_SUPPORTED:
                                    errorMessage = 'Audio format not supported by browser';
                                    errorCode = 'NOT_SUPPORTED';
                                    break;
                                default:
                                    errorMessage = `Audio error code: ${error.code}`;
                                    errorCode = `CODE_${error.code}`;
                            }
                        }

                        this.logger.error('Audio playback failed:', {
                            errorCode,
                            errorMessage,
                            browserError: error?.code,
                            blobSize: blob.size,
                            blobType: blob.type,
                            audioSrc: audio.src,
                            formatInfo,
                            validation: validation.isValid
                        });

                        reject(new Error(`Audio playback failed (${errorCode}): ${errorMessage}`));
                    };

                    audio.onended = () => {
                        URL.revokeObjectURL(audioUrl);
                        this.logger.info('✅ Audio playback completed successfully');
                        resolve({ success: true, duration: audio.duration });
                    };

                    // Enhanced loading event handling
                    audio.onloadeddata = () => {
                        this.logger.debug('Audio loaded successfully:', {
                            duration: audio.duration,
                            readyState: audio.readyState,
                            networkState: audio.networkState
                        });
                    };

                    audio.oncanplaythrough = () => {
                        this.logger.debug('Audio can play through without buffering');
                    };

                    // Start playback with better error handling
                    audio.play().catch((playError) => {
                        URL.revokeObjectURL(audioUrl);
                        this.logger.error('Audio play() method failed:', {
                            error: playError.message,
                            errorName: playError.name,
                            blobInfo: {
                                size: blob.size,
                                type: blob.type
                            },
                            formatInfo
                        });
                        reject(new Error(`Audio play failed: ${playError.message}`));
                    });
                });

            } else {
                // Node.js environment - simulate playback
                this.logger.info('[Node.js] Audio playback simulated', {
                    format,
                    dataLength: base64Audio.length
                });
                return { success: true, simulated: true };
            }
        } catch (error) {
            this.logger.error('Error in audio playback process:', {
                error: error instanceof Error ? error.message : String(error),
                errorType: error instanceof Error ? error.constructor.name : typeof error,
                base64Length: base64Audio?.length || 0
            });
            throw error;
        }
    }
}

/**
 * Create a global audio processor instance
 */
export function createAudioProcessor(options: { logger?: any } = {}): AudioResponseProcessor {
    return new AudioResponseProcessor(options);
}

/**
 * Helper function to automatically process LLM responses with audio
 * Can be called directly from LangGraph workflows
 * Now uses simplified audio processor with open-source libraries
 */
export async function processLLMResponseAudio(response: any, options: { logger?: any } = {}): Promise<{ audioPlayed: boolean; audioLength?: number; error?: string }> {
    try {
        // Use the new simplified audio processor
        const { processLLMResponseAudio } = await import('./audioProcessor.js');
        return await processLLMResponseAudio(response, options);
    } catch (error) {
        const logger = options.logger || console;
        logger.error('Failed to process LLM response audio:', error);
        return { audioPlayed: false, error: error instanceof Error ? error.message : String(error) };
    }
}

/**
 * Helper function to play base64 audio directly
 * Can be called from anywhere in the codebase
 * Now uses simplified audio processor with open-source libraries
 */
export async function playBase64Audio(base64Audio: string, format: string = 'wav', options: { logger?: any } = {}): Promise<{ success: boolean; duration?: number; simulated?: boolean }> {
    try {
        // Use the new simplified audio processor
        const { playBase64Audio } = await import('./audioProcessor.js');
        return await playBase64Audio(base64Audio, format, options);
    } catch (error) {
        const logger = options.logger || console;
        logger.error('Failed to play base64 audio:', error);
        return { success: false, simulated: false };
    }
}

/**
 * Create a streaming audio processor for real-time audio playback
 * Following Qwen-Omni 方式2 pattern: 边生成边实时播放
 */
export function createStreamingAudioProcessor(options: {
    sampleRate?: number;
    channels?: number;
    bitDepth?: number;
    logger?: any;
} = {}): StreamingAudioProcessor {
    return new StreamingAudioProcessor(options);
}

/**
 * Helper function to process streaming audio chunks in real-time
 * Usage example:
 * 
 * const processor = createStreamingAudioProcessor({ sampleRate: 24000 });
 * 
 * // For each streaming chunk from LLM (like Qwen-Omni):
 * for await (const chunk of completion) {
 *     if (chunk.choices[0].delta.audio?.data) {
 *         await processStreamingAudioChunk(processor, chunk.choices[0].delta.audio.data);
 *     }
 * }
 * 
 * processor.endStream();
 */
export async function processStreamingAudioChunk(
    processor: StreamingAudioProcessor,
    base64PCMChunk: string
): Promise<void> {
    return await processor.processStreamingChunk(base64PCMChunk);
}
