# Media Module

The Media module provides a centralized API for handling all media-related functionality in the application, including audio and video capture, processing, and streaming.

## Module Structure

The Media module is organized into the following submodules:

- **capture**: Media capture functionality (audio/video)
- **modality**: Media type-specific processing (audio/video/image)
- **streaming**: Real-time media streaming services
- **api**: API interfaces for media services
- **utils**: Utility functions for media processing
- **core**: Core media management components

## Key Components

### Media Capture

The `MediaCaptureManager` class in the `capture` submodule provides a unified API for capturing audio and video from device cameras and microphones. It supports different Voice Activity Detection (VAD) modes and can stream PCM16 audio for real-time WebSocket communication.

```javascript
import { MediaCaptureManager } from '@/media/capture/MediaCaptureManager';

const captureManager = new MediaCaptureManager({
  vadMode: 'server',
  audio: { sampleRate: 24000 },
  onAudioData: (audioData) => {
    // Process audio data
  }
});

await captureManager.startCapture('audio');
```

### Real-time Audio Streaming

The `RealtimeAudioManager` class in the `streaming` submodule provides a centralized service for real-time audio streaming with WebSocket connections. It handles audio processing, rate limiting, and session management to prevent issues like the 1011 Internal Server Error.

```javascript
import { RealtimeAudioManager } from '@/media/streaming/RealtimeAudioManager';

const audioManager = new RealtimeAudioManager({
  sampleRate: 24000,
  minIntervalMs: 200
});

// Set up callback for sending audio
audioManager.setSendAudioCallback(async (base64Audio) => {
  // Send audio to WebSocket
  websocket.send(JSON.stringify({
    type: 'input_audio_buffer.append',
    audio: base64Audio
  }));
  return true;
});

// Initialize session
audioManager.initSession();

// Process and send audio
await audioManager.processAndSendAudio(audioData);
```

### Audio Processing

The `audio` module in the `modality` submodule provides functions for processing audio data, including format conversion, validation, and playback.

```javascript
import { processRealtimeAudio, convertFloat32ToWav } from '@/media/modality/audio';

// Process audio for real-time streaming
const result = await processRealtimeAudio(audioData, {
  sampleRate: 24000,
  numChannels: 1,
  bitDepth: 16
});

if (result.success) {
  // Use result.base64Audio
}
```

### Video Processing

The `video` module in the `modality` submodule provides functions for processing video data, including frame extraction, format detection, and validation.

```javascript
import { processVideoForRealtime, extractFramesInBrowser } from '@/media/modality/video';

// Process video for real-time streaming
const result = await processVideoForRealtime(videoData);

// Extract frames from video
const frames = await extractFramesInBrowser(videoElement, {
  frameRate: 5,
  maxFrames: 10
});
```

## Best Practices

### Use Centralized Media Services

Always use the centralized media services provided by this module instead of implementing custom media handling logic in application components. This ensures consistent behavior and easier maintenance.

```javascript
// GOOD: Use centralized media services
import { RealtimeAudioManager } from '@/media/streaming/RealtimeAudioManager';
const audioManager = new RealtimeAudioManager();

// BAD: Implement custom media handling
const customAudioHandler = {
  // Custom implementation
};
```

### Handle Rate Limiting

When streaming audio or video in real-time, always use the built-in rate limiting mechanisms to prevent server overload and errors.

```javascript
// The RealtimeAudioManager handles rate limiting automatically
await audioManager.processAndSendAudio(audioData);
```

### Proper Resource Cleanup

Always dispose of media resources when they are no longer needed to prevent memory leaks and ensure optimal performance.

```javascript
// Clean up resources
audioManager.dispose();
captureManager.dispose();
```

## Integration with Agent System

The Media module integrates with the Agent system through adapters like `TalkingAvatarAdapter`, which use the centralized media services for audio streaming and processing.

```javascript
// In TalkingAvatarAdapter
this.realtimeAudioManager = new RealtimeAudioManager({
  sampleRate: 24000,
  minIntervalMs: 200
});

// Set up audio send callback
this.realtimeAudioManager.setSendAudioCallback(async (base64Audio) => {
  // Send to model's WebSocket
});
```
