import * as vad from '@ricky0123/vad-web';

export interface ClientVADOptions {
    onSpeechStart?: () => void;
    onSpeechEnd?: (audio: Float32Array) => void;
    // Add more vad-web options if needed
}

/**
 * Sets up client-side VAD using vad-web's MicVAD.
 * Returns a Promise that resolves to the MicVAD instance.
 * The returned instance has .start(), .pause(), .reset(), .destroy() methods.
 */
export async function setupClientVAD(
    options: ClientVADOptions = {}
): Promise<any> {
    const MicVAD = (vad as any).MicVAD;
    console.debug('[vadUtils] setupClientVAD called', { options, MicVAD });
    if (!MicVAD) throw new Error('vad-web MicVAD class not found. Check your package version and import.');

    console.debug('[vadUtils] Creating MicVAD instance...');
    const micVad = await MicVAD.new({
        onSpeechStart: () => {
            console.debug('[vadUtils] 🎤 VAD: Speech started');
            options.onSpeechStart?.();
        },
        onSpeechEnd: (audio: Float32Array) => {
            console.debug('[vadUtils] 🔇 VAD: Speech ended, audio length:', audio?.length);
            options.onSpeechEnd?.(audio);
        },
        onVADMisfire: () => {
            console.debug('[vadUtils] ⚠️ VAD: Misfire detected');
        },
        // You can add more vad-web options here if needed
    });

    console.debug('[vadUtils] MicVAD instance created successfully:', micVad);

    // Add additional logging for lifecycle events
    const originalStart = micVad.start;
    const originalStop = micVad.stop;
    const originalPause = micVad.pause;
    const originalDestroy = micVad.destroy;

    micVad.start = function () {
        console.debug('[vadUtils] 🟢 MicVAD.start() called');
        if (typeof originalStart === 'function') {
            return originalStart.call(this);
        } else {
            console.warn('[vadUtils] MicVAD.start: originalStart is not a function!');
        }
    };

    micVad.stop = function () {
        console.debug('[vadUtils] 🛑 MicVAD.stop() called');
        if (typeof originalStop === 'function') {
            return originalStop.call(this);
        } else {
            console.warn('[vadUtils] MicVAD.stop: originalStop is not a function!');
        }
    };

    micVad.pause = function () {
        console.debug('[vadUtils] ⏸️ MicVAD.pause() called');
        if (typeof originalPause === 'function') {
            return originalPause.call(this);
        } else {
            console.warn('[vadUtils] MicVAD.pause: originalPause is not a function!');
        }
    };

    micVad.destroy = function () {
        console.debug('[vadUtils] 💥 MicVAD.destroy() called');
        if (typeof originalDestroy === 'function') {
            return originalDestroy.call(this);
        } else {
            console.warn('[vadUtils] MicVAD.destroy: originalDestroy is not a function!');
        }
    };

    return micVad;
} 