/**
 * Browser Compatibility Utilities
 * Handles browser-specific fixes and polyfills for WebSocket, Audio API, and permissions
 */

import { createLogger } from '../../utils/logger.js';

const logger = createLogger('BrowserCompatibility');

/**
 * Browser Detection Utilities
 */
export const BrowserDetection = {
    isChrome: () => /Chrome/.test(navigator.userAgent) && /Google Inc/.test(navigator.vendor),
    isFirefox: () => /Firefox/.test(navigator.userAgent),
    isSafari: () => /Safari/.test(navigator.userAgent) && !/Chrome/.test(navigator.userAgent),
    isEdge: () => /Edg/.test(navigator.userAgent),
    
    supportsAudioWorklet: () => {
        return typeof AudioWorkletNode !== 'undefined' && 
               typeof AudioContext !== 'undefined' &&
               AudioContext.prototype.audioWorklet !== undefined;
    },
    
    supportsWebRTC: () => {
        return typeof RTCPeerConnection !== 'undefined' ||
               typeof webkitRTCPeerConnection !== 'undefined' ||
               typeof mozRTCPeerConnection !== 'undefined';
    },
    
    supportsGetUserMedia: () => {
        return !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia) ||
               !!(navigator.getUserMedia || navigator.webkitGetUserMedia || navigator.mozGetUserMedia);
    }
};

/**
 * WebSocket Browser Compatibility Wrapper
 * Handles browser-specific WebSocket issues and provides consistent API
 */
export class BrowserWebSocket {
    constructor(url, protocols, options = {}) {
        this.url = url;
        this.protocols = protocols;
        this.options = {
            binaryType: 'blob', // Default to blob for better browser compatibility
            autoReconnect: false,
            reconnectDelay: 1000,
            maxReconnectAttempts: 3,
            ...options
        };
        
        this.socket = null;
        this.reconnectAttempts = 0;
        this.isConnecting = false;
        this.isClosed = false;
        
        // Event handlers
        this.onopen = null;
        this.onmessage = null;
        this.onclose = null;
        this.onerror = null;
        
        logger.debug('BrowserWebSocket created', { url, protocols, options: this.options });
    }
    
    connect() {
        if (this.isConnecting || (this.socket && this.socket.readyState === WebSocket.OPEN)) {
            return Promise.resolve();
        }
        
        return new Promise((resolve, reject) => {
            try {
                this.isConnecting = true;
                this.isClosed = false;
                
                // Create WebSocket with browser-specific handling
                this.socket = new WebSocket(this.url, this.protocols);
                this.socket.binaryType = this.options.binaryType;
                
                // Handle open event
                this.socket.onopen = (event) => {
                    this.isConnecting = false;
                    this.reconnectAttempts = 0;
                    logger.debug('BrowserWebSocket connected');
                    
                    if (this.onopen) this.onopen(event);
                    resolve(event);
                };
                
                // Handle message with browser compatibility
                this.socket.onmessage = (event) => {
                    this.handleMessage(event);
                };
                
                // Handle close with auto-reconnect
                this.socket.onclose = (event) => {
                    this.isConnecting = false;
                    logger.debug('BrowserWebSocket closed', { code: event.code, reason: event.reason });
                    
                    if (!this.isClosed && this.options.autoReconnect && this.reconnectAttempts < this.options.maxReconnectAttempts) {
                        this.scheduleReconnect();
                    }
                    
                    if (this.onclose) this.onclose(event);
                };
                
                // Handle errors
                this.socket.onerror = (event) => {
                    this.isConnecting = false;
                    logger.error('BrowserWebSocket error', event);
                    
                    if (this.onerror) this.onerror(event);
                    if (this.isConnecting) reject(event);
                };
                
            } catch (error) {
                this.isConnecting = false;
                logger.error('Failed to create WebSocket', error);
                reject(error);
            }
        });
    }
    
    /**
     * Handle incoming messages with browser-specific parsing
     */
    async handleMessage(event) {
        try {
            let data = event.data;
            
            // Handle different message types based on browser behavior
            if (data instanceof Blob) {
                // More efficient Blob handling
                if (data.text && typeof data.text === 'function') {
                    // Modern browsers
                    data = await data.text();
                } else {
                    // Fallback for older browsers
                    data = await this.readBlobAsText(data);
                }
            }
            
            // Try to parse as JSON if it's text
            if (typeof data === 'string') {
                try {
                    data = JSON.parse(data);
                } catch (parseError) {
                    // Not JSON, keep as string
                    logger.debug('Message is not JSON, keeping as string');
                }
            }
            
            // Create normalized event
            const normalizedEvent = {
                ...event,
                data: data,
                originalData: event.data,
                timestamp: Date.now()
            };
            
            if (this.onmessage) {
                this.onmessage(normalizedEvent);
            }
            
        } catch (error) {
            logger.error('Error handling WebSocket message', error);
            if (this.onerror) {
                this.onerror(new Error(`Message handling failed: ${error.message}`));
            }
        }
    }
    
    /**
     * Read Blob as text with fallback for older browsers
     */
    readBlobAsText(blob) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = () => resolve(reader.result);
            reader.onerror = () => reject(reader.error);
            reader.readAsText(blob);
        });
    }
    
    /**
     * Send message with browser compatibility
     */
    send(data) {
        if (!this.socket || this.socket.readyState !== WebSocket.OPEN) {
            logger.warn('Cannot send message: WebSocket not open');
            return false;
        }
        
        try {
            // Handle different data types
            if (typeof data === 'object' && !(data instanceof ArrayBuffer) && !(data instanceof Blob)) {
                data = JSON.stringify(data);
            }
            
            this.socket.send(data);
            return true;
        } catch (error) {
            logger.error('Failed to send WebSocket message', error);
            return false;
        }
    }
    
    /**
     * Close WebSocket connection
     */
    close(code = 1000, reason = 'Normal closure') {
        this.isClosed = true;
        this.isConnecting = false;
        
        if (this.socket) {
            this.socket.close(code, reason);
        }
    }
    
    /**
     * Schedule reconnection attempt
     */
    scheduleReconnect() {
        this.reconnectAttempts++;
        const delay = this.options.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1); // Exponential backoff
        
        logger.debug(`Scheduling reconnect attempt ${this.reconnectAttempts}/${this.options.maxReconnectAttempts} in ${delay}ms`);
        
        setTimeout(() => {
            if (!this.isClosed) {
                this.connect().catch(error => {
                    logger.error('Reconnection failed', error);
                });
            }
        }, delay);
    }
    
    /**
     * Get current connection state
     */
    get readyState() {
        return this.socket ? this.socket.readyState : WebSocket.CLOSED;
    }
    
    /**
     * Check if connected
     */
    get connected() {
        return this.socket && this.socket.readyState === WebSocket.OPEN;
    }
}

/**
 * Audio Context Browser Compatibility
 */
export class BrowserAudioContext {
    constructor(options = {}) {
        this.context = null;
        this.options = options;
        
        this.initialize();
    }
    
    initialize() {
        try {
            // Handle browser prefixes
            const AudioContextClass = window.AudioContext || 
                                    window.webkitAudioContext || 
                                    window.mozAudioContext;
            
            if (!AudioContextClass) {
                throw new Error('AudioContext not supported in this browser');
            }
            
            this.context = new AudioContextClass(this.options);
            
            // Handle suspended state (Chrome policy)
            if (this.context.state === 'suspended') {
                logger.debug('AudioContext suspended, will resume on user interaction');
            }
            
            logger.debug('BrowserAudioContext initialized', {
                sampleRate: this.context.sampleRate,
                state: this.context.state,
                supportsAudioWorklet: !!this.context.audioWorklet
            });
            
        } catch (error) {
            logger.error('Failed to create AudioContext', error);
            throw error;
        }
    }
    
    /**
     * Resume audio context (required after user interaction)
     */
    async resume() {
        if (this.context && this.context.state === 'suspended') {
            try {
                await this.context.resume();
                logger.debug('AudioContext resumed');
                return true;
            } catch (error) {
                logger.error('Failed to resume AudioContext', error);
                return false;
            }
        }
        return true;
    }
    
    /**
     * Close audio context
     */
    async close() {
        if (this.context && this.context.state !== 'closed') {
            try {
                await this.context.close();
                logger.debug('AudioContext closed');
            } catch (error) {
                logger.error('Failed to close AudioContext', error);
            }
        }
    }
    
    /**
     * Get the actual AudioContext
     */
    get() {
        return this.context;
    }
}

/**
 * Browser Permission Helper
 */
export const BrowserPermissions = {
    /**
     * Request microphone permission with user-friendly handling
     */
    async requestMicrophone(constraints = { audio: true }) {
        try {
            logger.debug('Requesting microphone permission', constraints);
            
            if (!BrowserDetection.supportsGetUserMedia()) {
                throw new Error('getUserMedia not supported in this browser');
            }
            
            const stream = await navigator.mediaDevices.getUserMedia(constraints);
            logger.debug('Microphone permission granted');
            
            return {
                success: true,
                stream: stream,
                tracks: stream.getAudioTracks()
            };
            
        } catch (error) {
            logger.error('Microphone permission denied or failed', error);
            
            return {
                success: false,
                error: error.name,
                message: this.getPermissionErrorMessage(error),
                userAction: this.getPermissionUserAction(error)
            };
        }
    },
    
    /**
     * Check current microphone permission status
     */
    async checkMicrophonePermission() {
        try {
            if (!navigator.permissions) {
                return { state: 'unknown' };
            }
            
            const permission = await navigator.permissions.query({ name: 'microphone' });
            return { state: permission.state };
            
        } catch (error) {
            logger.warn('Could not check microphone permission', error);
            return { state: 'unknown' };
        }
    },
    
    /**
     * Get user-friendly error message for permission errors
     */
    getPermissionErrorMessage(error) {
        switch (error.name) {
            case 'NotAllowedError':
                return 'Microphone access was denied. Please allow microphone access to use voice features.';
            case 'NotFoundError':
                return 'No microphone found. Please connect a microphone and try again.';
            case 'NotReadableError':
                return 'Microphone is already in use by another application.';
            case 'OverconstrainedError':
                return 'Microphone does not meet the required specifications.';
            case 'SecurityError':
                return 'Microphone access blocked for security reasons. Please use HTTPS.';
            default:
                return 'Failed to access microphone. Please check your browser settings.';
        }
    },
    
    /**
     * Get user action suggestions for permission errors
     */
    getPermissionUserAction(error) {
        switch (error.name) {
            case 'NotAllowedError':
                return 'Click the microphone icon in your browser address bar and allow access.';
            case 'NotFoundError':
                return 'Check that your microphone is connected and recognized by your system.';
            case 'NotReadableError':
                return 'Close other applications that might be using your microphone.';
            case 'SecurityError':
                return 'Make sure you are using HTTPS and try refreshing the page.';
            default:
                return 'Try refreshing the page and check your browser microphone settings.';
        }
    }
};

/**
 * Progressive Enhancement Helper
 */
export const ProgressiveEnhancement = {
    /**
     * Check feature support and provide fallbacks
     */
    getAudioFeatures() {
        return {
            audioContext: !!window.AudioContext || !!window.webkitAudioContext,
            audioWorklet: BrowserDetection.supportsAudioWorklet(),
            scriptProcessor: !!AudioContext.prototype.createScriptProcessor,
            getUserMedia: BrowserDetection.supportsGetUserMedia(),
            webRTC: BrowserDetection.supportsWebRTC(),
            webSockets: !!window.WebSocket
        };
    },
    
    /**
     * Get recommended configuration based on browser capabilities
     */
    getRecommendedConfig() {
        const features = this.getAudioFeatures();
        
        return {
            // Audio processing
            useAudioWorklet: features.audioWorklet,
            useScriptProcessor: !features.audioWorklet && features.scriptProcessor,
            
            // Sample rates
            preferredSampleRate: 24000,
            fallbackSampleRate: 16000,
            
            // Chunk sizes
            audioChunkSize: features.audioWorklet ? 4096 : 2048,
            
            // Timing
            minChunkInterval: 250, // Conservative for better compatibility
            
            // Browser-specific settings
            browserSpecific: {
                chrome: { useAudioWorklet: true, chunkSize: 4096 },
                firefox: { useAudioWorklet: false, chunkSize: 2048 },
                safari: { useAudioWorklet: false, chunkSize: 1024 },
                edge: { useAudioWorklet: true, chunkSize: 4096 }
            }
        };
    }
};

export default {
    BrowserDetection,
    BrowserWebSocket,
    BrowserAudioContext,
    BrowserPermissions,
    ProgressiveEnhancement
};