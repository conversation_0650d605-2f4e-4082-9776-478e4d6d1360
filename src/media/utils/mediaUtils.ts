/**
 * Shared utility functions for media processing
 */

/**
 * Convert a Blob to base64 string
 * @param blob - The blob to convert
 * @returns Base64 string
 */
export async function blobToBase64(blob: Blob): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => {
      if (reader.result) {
        // Extract the base64 part from the data URL
        const base64 = reader.result.toString().split(',')[1];
        resolve(base64);
      } else {
        reject(new Error('FileReader result is null'));
      }
    };
    reader.onerror = reject;
    reader.readAsDataURL(blob);
  });
}

/**
 * Convert base64 to Blob
 * @param base64 Base64 string
 * @param contentType Content type
 * @returns Blob
 */
export function base64ToBlob(base64: string, contentType: string): Blob {
  const byteCharacters = atob(base64);
  const byteArrays: Uint8Array[] = [];

  for (let offset = 0; offset < byteCharacters.length; offset += 1024) {
    const slice = byteCharacters.slice(offset, offset + 1024);

    const byteNumbers = new Array(slice.length);
    for (let i = 0; i < slice.length; i++) {
      byteNumbers[i] = slice.charCodeAt(i);
    }

    const byteArray = new Uint8Array(byteNumbers);
    byteArrays.push(byteArray);
  }

  return new Blob(byteArrays, { type: contentType });
}

/**
 * Format media data for vLLM
 * @param mediaData Media data as Blob, ArrayBuffer, or base64 string
 * @param mediaType Media type ('audio' or 'video')
 * @returns Formatted data URL
 */
export async function formatMediaForVLLM(mediaData: Blob | ArrayBuffer | string, mediaType: string): Promise<string> {
  // If it's already a string, assume it's a base64 string
  if (typeof mediaData === 'string') {
    // If it's already a data URL, return it
    if (mediaData.startsWith('data:')) {
      return mediaData;
    }
    // Otherwise, format it as a data URL
    return `data:${mediaType === 'audio' ? 'audio/wav' : 'image/jpeg'};base64,${mediaData}`;
  }

  // If it's a Blob, convert to base64
  if (mediaData instanceof Blob) {
    const base64 = await blobToBase64(mediaData);
    return `data:${mediaType === 'audio' ? 'audio/wav' : 'image/jpeg'};base64,${base64}`;
  }

  // If it's an ArrayBuffer, convert to base64
  const blob = new Blob([mediaData], {
    type: mediaType === 'audio' ? 'audio/wav' : 'image/jpeg'
  });
  const base64 = await blobToBase64(blob);
  return `data:${mediaType === 'audio' ? 'audio/wav' : 'image/jpeg'};base64,${base64}`;
}
