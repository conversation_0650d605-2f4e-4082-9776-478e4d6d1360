/**
 * DEPRECATED: This file has been CONSOLIDATED into src/media/modality/audio.ts
 * 
 * Please use the new modality structure instead:
 * - import { processRealtimeAudio, validateAudioData, createFallbackBase64Audio } from '../../media/modality/audio.js'
 * 
 * This file is kept temporarily for backward compatibility and will be removed in a future version.
 * All functionality has been moved to the new audio modality module with improved TypeScript support.
 */

console.warn('⚠️ DEPRECATED: realtimeAudioProcessor.js has been consolidated into src/media/modality/audio.ts. Please update your imports.');

/**
 * Realtime audio processing utilities for Aliyun API
 * Handles audio format conversion for realtime streaming
 */

import { createLogger } from '../../utils/logger.js';
import { convertFloat32ToWav } from './audioUtils.js';

const logger = createLogger('RealtimeAudioProcessor');

/**
 * Process audio data for Aliyun realtime API
 * Handles different input types and converts to base64 WAV format
 * @param {ArrayBuffer|Float32Array|Int16Array|string} audioData - Audio data in various formats
 * @param {Object} options - Processing options
 * @returns {Promise<{success: boolean, base64Audio?: string, error?: string}>}
 */
export async function processRealtimeAudio(audioData, options = {}) {
    logger.debug('Processing realtime audio data', {
        inputType: typeof audioData,
        isArrayBuffer: audioData instanceof ArrayBuffer,
        isFloat32Array: audioData instanceof Float32Array,
        isInt16Array: audioData instanceof Int16Array,
        dataLength: audioData.length || audioData.byteLength || 0,
        options
    });

    const {
        sampleRate = 24000,
        numChannels = 1,
        bitDepth = 16,
        enableDebugLogging = false
    } = options;

    try {
        if (enableDebugLogging) {
            logger.debug('🔄 Processing realtime audio data:', {
                dataType: audioData?.constructor?.name || typeof audioData,
                dataLength: audioData?.length || audioData?.byteLength,
                sampleRate,
                numChannels,
                bitDepth
            });
        }

        // If already a base64 string, return as-is
        if (typeof audioData === 'string') {
            if (enableDebugLogging) {
                logger.debug('✅ Audio data is already base64 string');
            }
            return {
                success: true,
                base64Audio: audioData
            };
        }

        // Convert different input types to Float32Array
        let float32Audio;
        if (audioData instanceof ArrayBuffer) {
            // AudioWorklet sends Int16Array.buffer, reconstruct it
            const int16Data = new Int16Array(audioData);
            float32Audio = new Float32Array(int16Data.length);

            if (enableDebugLogging) {
                logger.debug('🔄 Converting ArrayBuffer (Int16) to Float32Array:', {
                    originalLength: int16Data.length,
                    byteLength: audioData.byteLength
                });
            }

            for (let i = 0; i < int16Data.length; i++) {
                // Convert Int16 back to Float32 (-1.0 to 1.0 range)
                float32Audio[i] = int16Data[i] / (int16Data[i] < 0 ? 0x8000 : 0x7FFF);
            }
        } else if (audioData instanceof Float32Array) {
            if (enableDebugLogging) {
                logger.debug('✅ Audio data is already Float32Array');
            }
            float32Audio = audioData;
        } else if (audioData instanceof Int16Array) {
            // Handle Int16Array directly
            float32Audio = new Float32Array(audioData.length);

            if (enableDebugLogging) {
                logger.debug('🔄 Converting Int16Array to Float32Array:', {
                    length: audioData.length
                });
            }

            for (let i = 0; i < audioData.length; i++) {
                float32Audio[i] = audioData[i] / (audioData[i] < 0 ? 0x8000 : 0x7FFF);
            }
        } else {
            const errorMsg = `Unsupported audio data type: ${typeof audioData}`;
            if (enableDebugLogging) {
                logger.error('❌', errorMsg);
            }
            return {
                success: false,
                error: errorMsg
            };
        }

        // Convert to WAV format
        if (enableDebugLogging) {
            logger.debug('🔄 Converting to WAV format:', {
                sampleCount: float32Audio.length,
                sampleRate,
                numChannels,
                bitDepth
            });
        }

        const wavBlob = convertFloat32ToWav(float32Audio, {
            sampleRate,
            numChannels,
            bitDepth
        });

        // Convert blob to base64
        const arrayBuffer = await wavBlob.arrayBuffer();
        const uint8Array = new Uint8Array(arrayBuffer);
        const base64Audio = btoa(String.fromCharCode.apply(null, uint8Array));

        if (enableDebugLogging) {
            logger.debug('✅ Audio processing completed:', {
                wavBlobSize: wavBlob.size,
                base64Length: base64Audio.length,
                expectedSize: 44 + float32Audio.length * (bitDepth / 8)
            });
        }

        return {
            success: true,
            base64Audio
        };

    } catch (error) {
        const errorMsg = `Audio processing failed: ${error.message}`;
        if (enableDebugLogging) {
            logger.error('❌', errorMsg, error);
        }
        return {
            success: false,
            error: errorMsg
        };
    }
}

/**
 * Create fallback base64 audio from raw audio data
 * Used when WAV conversion fails
 * @param {ArrayBuffer|Uint8Array} audioData - Raw audio data
 * @returns {string} Base64 encoded audio
 */
export function createFallbackBase64Audio(audioData) {
    try {
        const uint8Array = new Uint8Array(audioData.buffer || audioData);
        return btoa(String.fromCharCode.apply(null, uint8Array));
    } catch (error) {
        logger.warn('Fallback base64 conversion failed:', error);
        return '';
    }
}

/**
 * Validate audio data before processing
 * @param {any} audioData - Audio data to validate
 * @returns {Object} Validation result
 */
export function validateAudioData(audioData) {
    if (!audioData) {
        return {
            isValid: false,
            error: 'Audio data is null or undefined'
        };
    }

    if (typeof audioData === 'string') {
        return {
            isValid: true,
            type: 'base64_string'
        };
    }

    if (audioData instanceof ArrayBuffer) {
        return {
            isValid: audioData.byteLength > 0,
            type: 'ArrayBuffer',
            size: audioData.byteLength,
            error: audioData.byteLength === 0 ? 'ArrayBuffer is empty' : null
        };
    }

    if (audioData instanceof Float32Array || audioData instanceof Int16Array) {
        return {
            isValid: audioData.length > 0,
            type: audioData.constructor.name,
            size: audioData.length,
            error: audioData.length === 0 ? 'Audio array is empty' : null
        };
    }

    return {
        isValid: false,
        error: `Unsupported audio data type: ${typeof audioData}`
    };
}
