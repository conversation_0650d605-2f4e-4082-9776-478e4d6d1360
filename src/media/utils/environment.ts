/**
 * Environment detection utilities
 * Provides functions to detect the current environment
 */

/**
 * Check if the code is running in a Node.js environment
 * @returns {boolean} True if running in Node.js, false otherwise
 */
export function isNodeEnvironment(): boolean {
  return typeof process !== 'undefined' &&
    typeof process.versions === 'object' &&
    process.versions !== null &&
    typeof process.versions.node === 'string' &&
    // Additional check to ensure we're not in a browser environment
    typeof window === 'undefined' &&
    typeof document === 'undefined';
}

/**
 * Check if the code is running in a browser environment
 * @returns {boolean} True if running in a browser, false otherwise
 */
export function isBrowserEnvironment(): boolean {
  return typeof window !== 'undefined' && typeof document !== 'undefined';
}

/**
 * Get the current environment name
 * @returns {string} 'node', 'browser', or 'unknown'
 */
export function getEnvironmentName(): 'node' | 'browser' | 'unknown' {
  if (isNodeEnvironment()) return 'node';
  if (isBrowserEnvironment()) return 'browser';
  return 'unknown';
}

/**
 * Log the current environment information
 */
export function logEnvironmentInfo(): void {
  const env = getEnvironmentName();
  console.log(`[Environment] Running in ${env} environment`);

  if (env === 'node') {
    console.log(`[Environment] Node.js version: ${process.version}`);
    console.log(`[Environment] Platform: ${process.platform}`);
  } else if (env === 'browser') {
    console.log(`[Environment] User Agent: ${navigator.userAgent}`);
  }
}
