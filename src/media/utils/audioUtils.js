/**
 * DEPRECATED: Core audio functions have been CONSOLIDATED into src/media/modality/audio.ts
 * 
 * Please use the new modality structure instead:
 * - import { convertFloat32ToWav, detectAudioFormat, base64To<PERSON>lob, blob<PERSON>oB<PERSON><PERSON>, checkA<PERSON>o<PERSON><PERSON><PERSON>, createFallbackTone } from '../../media/modality/audio.js'
 * 
 * This file is kept temporarily for backward compatibility and will be removed in a future version.
 * All functionality has been moved to the new audio modality module with improved TypeScript support.
 */

console.warn('⚠️ DEPRECATED: audioUtils.js core functions have been consolidated into src/media/modality/audio.ts. Please update your imports.');

/**
 * Shared audio utilities for the application
 * Works in both browser and server environments
 *
 * IMPORTANT: This is the canonical source for audio utility functions.
 * All audio utility functions should be added here.
 * src/utils/audioUtils.js re-exports from this file for backward compatibility.
 */

import { createLogger } from '../../utils/logger.js';

// Create a logger for this module
const logger = createLogger('AudioUtils');

/**
 * Audio format detection constants
 */
export const AudioFormat = {
  UNKNOWN: 'unknown',
  MP3: 'mp3',
  WAV: 'wav',
  OGG: 'ogg'
};

/**
 * Default audio processing options
 */
export const DEFAULT_AUDIO_OPTIONS = {
  chunkDurationMs: 800,
  sampleRate: 22050,
  numChannels: 1,
  bitDepth: 16
};

/**
 * Detect audio format from header bytes
 * @param {Uint8Array} data - Audio data
 * @returns {string} - Detected format from AudioFormat enum
 */
export function detectAudioFormat(data) {
  if (!data || data.length < 4) {
    return AudioFormat.UNKNOWN;
  }

  // Check for MP3 format (ID3 tag)
  if (data[0] === 0x49 && data[1] === 0x44 && data[2] === 0x33) {
    return AudioFormat.MP3;
  }

  // Check for MP3 format (MPEG frame sync)
  if (data[0] === 0xFF && (data[1] & 0xE0) === 0xE0) {
    return AudioFormat.MP3;
  }

  // Check for WAV format (RIFF header)
  if (data[0] === 0x52 && data[1] === 0x49 && data[2] === 0x46 && data[3] === 0x46) {
    return AudioFormat.WAV;
  }

  // Check for OGG format (OggS header)
  if (data[0] === 0x4F && data[1] === 0x67 && data[2] === 0x67 && data[3] === 0x53) {
    return AudioFormat.OGG;
  }

  return AudioFormat.UNKNOWN;
}

/**
 * Convert base64 string to Blob
 * @param {string} base64 - Base64 string
 * @param {string} mimeType - MIME type for the blob
 * @returns {Blob} - Blob object
 */
export function base64ToBlob(base64, mimeType = 'audio/wav') {
  const byteCharacters = atob(base64);
  const byteArrays = [];

  for (let offset = 0; offset < byteCharacters.length; offset += 1024) {
    const slice = byteCharacters.slice(offset, offset + 1024);

    const byteNumbers = new Array(slice.length);
    for (let i = 0; i < slice.length; i++) {
      byteNumbers[i] = slice.charCodeAt(i);
    }

    const byteArray = new Uint8Array(byteNumbers);
    byteArrays.push(byteArray);
  }

  return new Blob(byteArrays, { type: mimeType });
}

/**
 * Convert a Blob to base64 string
 * @param {Blob} blob - The blob to convert
 * @returns {Promise<string>} - Base64 string
 */
export function blobToBase64(blob) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => {
      if (reader.result) {
        // Extract the base64 part from the data URL
        const base64 = reader.result.toString().split(',')[1];
        resolve(base64);
      } else {
        reject(new Error('FileReader result is null'));
      }
    };
    reader.onerror = reject;
    reader.readAsDataURL(blob);
  });
}

/**
 * Create an Audio element from a blob
 * @param {Blob} blob - Audio blob
 * @returns {Promise<HTMLAudioElement>} - Audio element
 */
export function createAudioFromBlob(blob) {
  return new Promise((resolve, reject) => {
    const url = URL.createObjectURL(blob);
    const audio = new Audio();

    const timeoutId = setTimeout(() => {
      logger.warn('Audio load timeout');
      audio.src = '';
      URL.revokeObjectURL(url);
      reject(new Error('Audio load timeout'));
    }, 5000);

    audio.onloadedmetadata = () => {
      clearTimeout(timeoutId);
      logger.debug(`Audio loaded, duration: ${audio.duration}s`);
      resolve(audio);
    };

    audio.onerror = (e) => {
      clearTimeout(timeoutId);
      logger.error(`Error loading audio: ${e}`);
      URL.revokeObjectURL(url);
      reject(new Error(`Error loading audio: ${e}`));
    };

    audio.src = url;
  });
}

/**
 * Play audio with promise
 * @param {HTMLAudioElement} audio - Audio element
 * @returns {Promise<void>} - Promise that resolves when audio playback ends
 */
export function playAudioWithPromise(audio) {
  return new Promise((resolve, reject) => {
    // Create a promise that resolves when the audio ends
    audio.onended = () => {
      logger.debug('Audio playback ended');
      resolve();
    };

    // Handle errors
    audio.onerror = (error) => {
      logger.error(`Error playing audio: ${error}`);
      reject(error);
    };

    // Start playing the audio
    audio.play().catch(error => {
      logger.error(`Error starting audio playback: ${error}`);
      reject(error);
    });
  });
}

/**
 * Check if an AudioBuffer contains actual audio data
 * @param {AudioBuffer} buffer - Audio buffer to check
 * @param {Object} options - Options for checking audio
 * @param {number} [options.minAmplitude=0.0001] - Minimum amplitude threshold
 * @param {boolean} [options.checkMultipleRanges=true] - Whether to check multiple ranges of the buffer
 * @param {Object} [options.logger=console] - Logger to use
 * @returns {Object} - Object with hasAudio flag, maxValue, and percentNonZero
 */
export function checkAudioBuffer(buffer, options = {}) {
  // Default options
  const {
    minAmplitude = 0.0001,
    checkMultipleRanges = true,
    logger = console
  } = options;

  if (!buffer || !buffer.numberOfChannels || buffer.numberOfChannels < 1) {
    return { hasAudio: false, maxValue: 0, percentNonZero: 0 };
  }

  // Sample the first channel to check for non-zero values
  const data = buffer.getChannelData(0);
  let nonZeroCount = 0;
  let maxValue = 0;
  let samplesChecked = 0;

  // Function to check a specific range of the buffer
  const checkRange = (start, end) => {
    let rangeNonZeroCount = 0;
    let rangeMaxValue = 0;
    const rangeLength = end - start;
    const step = Math.max(1, Math.floor(rangeLength / 333)); // ~333 samples per range

    for (let i = start; i < end; i += step) {
      const absValue = Math.abs(data[i]);
      if (absValue > minAmplitude) {
        rangeNonZeroCount++;
      }
      rangeMaxValue = Math.max(rangeMaxValue, absValue);
    }

    return {
      nonZeroCount: rangeNonZeroCount,
      maxValue: rangeMaxValue,
      samplesChecked: Math.ceil(rangeLength / step)
    };
  };

  // Check multiple ranges if enabled and buffer is long enough
  if (checkMultipleRanges && data.length > 3000) {
    // Define ranges to check (beginning, middle, end)
    const ranges = [
      { start: 0, end: Math.min(1000, data.length) },
      { start: Math.floor(data.length / 2) - 500, end: Math.floor(data.length / 2) + 500 },
      { start: Math.max(0, data.length - 1000), end: data.length }
    ];

    // Check each range
    for (const range of ranges) {
      const result = checkRange(range.start, range.end);
      nonZeroCount += result.nonZeroCount;
      maxValue = Math.max(maxValue, result.maxValue);
      samplesChecked += result.samplesChecked;
    }
  } else {
    // Check the entire buffer with sampling
    const step = Math.max(1, Math.floor(data.length / 1000));
    for (let i = 0; i < data.length; i += step) {
      const absValue = Math.abs(data[i]);
      if (absValue > minAmplitude) {
        nonZeroCount++;
      }
      maxValue = Math.max(maxValue, absValue);
    }
    samplesChecked = Math.ceil(data.length / step);
  }

  const percentNonZero = (nonZeroCount / samplesChecked) * 100;

  // Consider it valid if at least 1% of samples are non-zero and max amplitude is above threshold
  const hasAudio = percentNonZero >= 1.0 && maxValue >= minAmplitude;

  return { hasAudio, maxValue, percentNonZero };
}

/**
 * Create a fallback tone generator
 * @param {Object} options - Configuration options
 * @param {number} [options.frequency=440] - Frequency in Hz (default: 440Hz, A4 note)
 * @param {number} [options.duration=1.0] - Duration in seconds
 * @param {number} [options.sampleRate=22050] - Sample rate in Hz
 * @param {number} [options.amplitude=0.5] - Amplitude of the tone (0.0-1.0)
 * @param {AudioContext} [options.audioContext] - Existing AudioContext to use
 * @returns {AudioBuffer} - Audio buffer with a simple tone
 */
export function createFallbackTone(options = {}) {
  // Extract options with defaults
  const frequency = options.frequency || 440; // A4 note
  const duration = options.duration || 1.0;
  const sampleRate = options.sampleRate || 22050;
  const amplitude = options.amplitude || 0.5;

  // Create or use provided AudioContext
  let audioContext;
  let shouldCloseContext = false;

  if (options.audioContext) {
    audioContext = options.audioContext;
  } else {
    // @ts-ignore - webkitAudioContext is needed for Safari
    audioContext = new (window.AudioContext || window.webkitAudioContext)();
    shouldCloseContext = true;
  }

  // Create buffer
  const buffer = audioContext.createBuffer(1, Math.ceil(duration * sampleRate), sampleRate);
  const channelData = buffer.getChannelData(0);

  // Fill with a simple sine wave
  for (let i = 0; i < channelData.length; i++) {
    const t = i / sampleRate;
    const fadeFactor = Math.min(1, Math.min(t / 0.01, (duration - t) / 0.01)); // 10ms fade in/out
    channelData[i] = Math.sin(2 * Math.PI * frequency * t) * amplitude * fadeFactor;
  }

  // Close the context if we created it
  if (shouldCloseContext) {
    try {
      audioContext.close();
    } catch (e) {
      logger.warn(`Error closing temporary AudioContext: ${e.message}`);
    }
  }

  return buffer;
}

/**
 * Split audio into chunks
 * @param {HTMLAudioElement} audio - Audio element
 * @param {number} chunkDurationMs - Chunk duration in milliseconds
 * @returns {Array<Object>} - Array of chunk objects with duration and isLast properties
 */
export function splitAudioIntoChunks(audio, chunkDurationMs = DEFAULT_AUDIO_OPTIONS.chunkDurationMs) {
  const numChunks = Math.ceil(audio.duration / (chunkDurationMs / 1000));
  const chunks = [];

  for (let i = 0; i < numChunks; i++) {
    const isLast = i === numChunks - 1;
    const chunkDuration = isLast ?
      audio.duration - (i * chunkDurationMs / 1000) :
      chunkDurationMs / 1000;

    chunks.push({
      duration: chunkDuration,
      isLast
    });
  }

  return chunks;
}

/**
 * Convert Float32Array to WAV format
 * @param {Float32Array} samples - Float32Array audio samples
 * @param {Object} options - Conversion options
 * @returns {Blob} - WAV blob
 */
export function convertFloat32ToWav(
  samples,
  options = {}
) {
  // Default options
  const sampleRate = options.sampleRate || DEFAULT_AUDIO_OPTIONS.sampleRate;
  const numChannels = options.numChannels || DEFAULT_AUDIO_OPTIONS.numChannels;
  const bitDepth = options.bitDepth || DEFAULT_AUDIO_OPTIONS.bitDepth;
  const bytesPerSample = bitDepth / 8;
  const blockAlign = numChannels * bytesPerSample;

  // Create buffer with WAV header
  const dataLength = samples.length * bytesPerSample;
  const buffer = new ArrayBuffer(44 + dataLength);
  const view = new DataView(buffer);

  // Write WAV header
  // "RIFF" chunk descriptor
  writeString(view, 0, 'RIFF');
  view.setUint32(4, 36 + dataLength, true);
  writeString(view, 8, 'WAVE');

  // "fmt " sub-chunk
  writeString(view, 12, 'fmt ');
  view.setUint32(16, 16, true); // fmt chunk size
  view.setUint16(20, 1, true); // audio format (1 = PCM)
  view.setUint16(22, numChannels, true);
  view.setUint32(24, sampleRate, true);
  view.setUint32(28, sampleRate * blockAlign, true); // byte rate
  view.setUint16(32, blockAlign, true);
  view.setUint16(34, bitDepth, true);

  // "data" sub-chunk
  writeString(view, 36, 'data');
  view.setUint32(40, dataLength, true);

  // Write audio data
  if (bitDepth === 16) {
    floatTo16BitPCM(view, 44, samples);
  } else if (bitDepth === 8) {
    floatTo8BitPCM(view, 44, samples);
  } else if (bitDepth === 32) {
    floatTo32BitPCM(view, 44, samples);
  } else {
    logger.warn(`Unsupported bit depth: ${bitDepth}, using 16-bit`);
    floatTo16BitPCM(view, 44, samples);
  }

  return new Blob([buffer], { type: 'audio/wav' });
}

// Helper functions for WAV encoding
function writeString(view, offset, string) {
  for (let i = 0; i < string.length; i++) {
    view.setUint8(offset + i, string.charCodeAt(i));
  }
}

function floatTo16BitPCM(view, offset, samples) {
  for (let i = 0; i < samples.length; i++) {
    const s = Math.max(-1, Math.min(1, samples[i]));
    view.setInt16(offset + i * 2, s < 0 ? s * 0x8000 : s * 0x7FFF, true);
  }
}

function floatTo8BitPCM(view, offset, samples) {
  for (let i = 0; i < samples.length; i++) {
    const s = Math.max(-1, Math.min(1, samples[i]));
    view.setUint8(offset + i, (s + 1) * 128);
  }
}

function floatTo32BitPCM(view, offset, samples) {
  for (let i = 0; i < samples.length; i++) {
    const s = Math.max(-1, Math.min(1, samples[i]));
    view.setFloat32(offset + i * 4, s, true);
  }
}

/**
 * Convert a Base64 encoded string to ArrayBuffer.
 * @param {string} b64String Base64 encoded string
 * @return {ArrayBuffer} ArrayBuffer
 */
export function b64ToArrayBuffer(b64String) {
  // Create a lookup table for base64 decoding
  const b64Lookup = createBase64Lookup();

  // Calculate the needed total buffer length
  let bufLen = 3 * b64String.length / 4;
  if (b64String[b64String.length - 1] === '=') {
    bufLen--;
    if (b64String[b64String.length - 2] === '=') {
      bufLen--;
    }
  }

  // Create the ArrayBuffer
  const arrBuf = new ArrayBuffer(bufLen);
  const arr = new Uint8Array(arrBuf);
  let i, p = 0, c1, c2, c3, c4;

  // Populate the buffer
  for (i = 0; i < b64String.length; i += 4) {
    c1 = b64Lookup[b64String.charCodeAt(i)];
    c2 = b64Lookup[b64String.charCodeAt(i + 1)];
    c3 = b64Lookup[b64String.charCodeAt(i + 2)];
    c4 = b64Lookup[b64String.charCodeAt(i + 3)];
    arr[p++] = (c1 << 2) | (c2 >> 4);
    arr[p++] = ((c2 & 15) << 4) | (c3 >> 2);
    arr[p++] = ((c3 & 3) << 6) | (c4 & 63);
  }

  return arrBuf;
}

/**
 * Create a lookup table for base64 decoding
 * @returns {Uint8Array} Lookup table
 */
function createBase64Lookup() {
  const b64Chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';
  const lookup = typeof Uint8Array === 'undefined' ? [] : new Uint8Array(256);
  for (let i = 0; i < b64Chars.length; i++) {
    lookup[b64Chars.charCodeAt(i)] = i;
  }
  return lookup;
}

/**
 * Concatenate an array of ArrayBuffers.
 * @param {ArrayBuffer[]} buffers Array of ArrayBuffers
 * @return {ArrayBuffer} Concatenated ArrayBuffer
 */
export function concatArrayBuffers(buffers) {
  if (buffers.length === 1) return buffers[0];

  let totalLength = 0;
  for (let i = 0; i < buffers.length; i++) {
    totalLength += buffers[i].byteLength;
  }

  const result = new ArrayBuffer(totalLength);
  const resultArray = new Uint8Array(result);

  let offset = 0;
  for (let i = 0; i < buffers.length; i++) {
    resultArray.set(new Uint8Array(buffers[i]), offset);
    offset += buffers[i].byteLength;
  }

  return result;
}

/**
 * Convert PCM buffer to AudioBuffer.
 * NOTE: Only signed 16bit little endian supported.
 * @param {ArrayBuffer} buffer PCM buffer
 * @param {number} sampleRate Sample rate (default: 22050)
 * @return {AudioBuffer} AudioBuffer
 */
export function pcmToAudioBuffer(buffer, sampleRate = 22050) {
  // Create AudioContext if needed
  const audioContext = new (window.AudioContext || window.webkitAudioContext)();

  const arr = new Int16Array(buffer);
  const floats = new Float32Array(arr.length);

  for (let i = 0; i < arr.length; i++) {
    floats[i] = (arr[i] >= 0x8000) ? -(0x10000 - arr[i]) / 0x8000 : arr[i] / 0x7FFF;
  }

  const audio = audioContext.createBuffer(1, floats.length, sampleRate);
  audio.copyToChannel(floats, 0, 0);

  return audio;
}

/**
 * Set reverb for audio context
 * @param {AudioContext} audioContext The audio context
 * @param {AudioNode} reverbNode The convolver node for reverb
 * @param {ArrayBuffer} impulseResponse Impulse response buffer (null for dry)
 * @returns {Promise<void>}
 */
export async function setReverb(audioContext, reverbNode, impulseResponse = null) {
  if (!impulseResponse) {
    // Create a dry impulse (no reverb)
    const sampleRate = audioContext.sampleRate;
    const buffer = audioContext.createBuffer(2, sampleRate * 0.1, sampleRate);
    const channelDataL = buffer.getChannelData(0);
    const channelDataR = buffer.getChannelData(1);
    channelDataL[0] = 1.0;
    channelDataR[0] = 1.0;
    reverbNode.buffer = buffer;
  } else {
    // Decode the provided impulse response
    try {
      const buffer = await audioContext.decodeAudioData(impulseResponse);
      reverbNode.buffer = buffer;
    } catch (error) {
      logger.error('Error setting reverb:', error);
    }
  }
}

/**
 * Set mixer gain levels
 * @param {AudioNode} speechGainNode Speech gain node
 * @param {AudioNode} backgroundGainNode Background gain node
 * @param {number} speechGain Speech gain value (0-1)
 * @param {number} backgroundGain Background gain value (0-1)
 */
export function setMixerGain(speechGainNode, backgroundGainNode, speechGain = null, backgroundGain = null) {
  if (speechGain !== null && speechGainNode) {
    speechGainNode.gain.value = Math.max(0, Math.min(1, speechGain));
  }

  if (backgroundGain !== null && backgroundGainNode) {
    backgroundGainNode.gain.value = Math.max(0, Math.min(1, backgroundGain));
  }
}

/**
 * Process audio chunk with client-side chunking for streaming benefits
 * @param {AudioBuffer|ArrayBuffer} audioData - Complete audio data
 * @param {Object} options - Processing options
 * @returns {Promise<Object>} - Result with audio chunks
 */
export async function processAudioChunk(audioData, options = {}) {
  const defaultOptions = {
    chunkDurationMs: 800,
    audioContext: null,
    onChunk: null,
    onComplete: null,
    onError: console.error
  };

  const mergedOptions = { ...defaultOptions, ...options };
  const audioContext = mergedOptions.audioContext || new (window.AudioContext || window.webkitAudioContext)();

  try {
    let decodedBuffer;

    // Handle different input types
    if (audioData instanceof AudioBuffer) {
      decodedBuffer = audioData;
    } else if (audioData instanceof ArrayBuffer || audioData instanceof Uint8Array) {
      const buffer = audioData instanceof Uint8Array ? audioData.buffer : audioData;
      decodedBuffer = await audioContext.decodeAudioData(buffer.slice(0));
    } else {
      throw new Error('Unsupported audio data type');
    }

    // Calculate chunk parameters
    const samplesPerChunk = Math.floor(decodedBuffer.sampleRate * (mergedOptions.chunkDurationMs / 1000));
    const totalSamples = decodedBuffer.length;
    const numChunks = Math.ceil(totalSamples / samplesPerChunk);

    const chunks = [];

    // Process each chunk
    for (let i = 0; i < numChunks; i++) {
      // Create a new AudioBuffer for this chunk
      const chunkBuffer = audioContext.createBuffer(
        decodedBuffer.numberOfChannels,
        Math.min(samplesPerChunk, totalSamples - i * samplesPerChunk),
        decodedBuffer.sampleRate
      );

      // Copy the data for this chunk
      for (let channel = 0; channel < decodedBuffer.numberOfChannels; channel++) {
        const sourceData = decodedBuffer.getChannelData(channel);
        const chunkData = chunkBuffer.getChannelData(channel);

        for (let j = 0; j < chunkBuffer.length; j++) {
          const sourceIndex = i * samplesPerChunk + j;
          if (sourceIndex < totalSamples) {
            chunkData[j] = sourceData[sourceIndex];
          }
        }
      }

      // Create chunk object
      const chunk = {
        data: chunkBuffer,
        duration: chunkBuffer.duration,
        isLast: i === numChunks - 1
      };

      chunks.push(chunk);

      // Call the onChunk callback if provided
      if (mergedOptions.onChunk) {
        await mergedOptions.onChunk(chunk);
      }
    }

    // Call the onComplete callback if provided
    if (mergedOptions.onComplete) {
      await mergedOptions.onComplete(chunks);
    }

    return { success: true, chunks };
  } catch (error) {
    if (mergedOptions.onError) {
      mergedOptions.onError(error);
    }
    return { success: false, error: error.message };
  }
}

/**
 * Check if an audio file exists and try alternative locations if not found
 * @param {string} audioFilePath - Path to the audio file
 * @param {Object} options - Additional options
 * @param {Function} [options.logger] - Logger function to use
 * @returns {Promise<Object>} - Result with exists flag and resolved path
 */
export async function checkAudioFileExists(audioFilePath, options = {}) {
  if (!audioFilePath) {
    return { exists: false, path: null };
  }

  const log = options.logger || logger;
  let exists = false;
  let resolvedPath = audioFilePath;

  try {
    // Try with the original path
    let response = await fetch(audioFilePath, { method: 'HEAD' });
    exists = response.ok;

    if (!exists) {
      log.warn(`Audio file not found at ${audioFilePath}`);

      // Try with the full URL including host if it doesn't already have it
      if (!audioFilePath.startsWith('http')) {
        const fullUrl = `http://localhost:2994${audioFilePath}`;
        log.info(`Trying with full URL: ${fullUrl}`);

        response = await fetch(fullUrl, { method: 'HEAD' });
        if (response.ok) {
          exists = true;
          resolvedPath = fullUrl;
          log.info(`Found audio file at full URL: ${fullUrl}`);
        }
      }

      // If still not found, check if it's in the favorite folder
      if (!exists && !audioFilePath.includes('__favorite__')) {
        const fileName = audioFilePath.split('/').pop();
        const favoriteFilePath = `/assets/audio/__favorite__/${fileName}`;

        log.info(`Trying favorite folder: ${favoriteFilePath}`);
        response = await fetch(favoriteFilePath, { method: 'HEAD' });
        if (response.ok) {
          exists = true;
          resolvedPath = favoriteFilePath;
          log.info(`Found audio file in favorite folder: ${favoriteFilePath}`);
        }
      }
    }
  } catch (error) {
    log.warn(`Error checking if audio file exists: ${error.message}`);
    // Continue with the assumption that the file might exist
    exists = false;
  }

  return { exists, path: resolvedPath };
}

/**
 * Extract role name from audio file path
 * @param {string} audioFilePath - Path to the audio file
 * @param {Object} options - Additional options
 * @param {Function} [options.logger] - Logger function to use
 * @returns {string} - Extracted role name
 */
export function extractRoleNameFromAudioFile(audioFilePath, options = {}) {
  if (!audioFilePath) {
    return null;
  }

  const log = options.logger || logger;

  // Extract role name from the audio file path
  const pathParts = audioFilePath.split('/');
  const fileName = pathParts[pathParts.length - 1];

  let roleName;

  // Extract the role name, handling different formats:
  // - doll_熊辉_14869285_cloned.mp3 (avatarName_timestamp_cloned.mp3)
  // - 1746751108950_cloned.mp3 (timestamp_cloned.mp3)
  // - roleName.mp3 (direct role name)
  if (fileName.includes('_cloned.')) {
    // Format: avatarName_timestamp_cloned.mp3 or timestamp_cloned.mp3
    roleName = fileName.split('_cloned.')[0] || `cloned_voice_${Date.now()}`;
    log.info(`Extracted role name from cloned file: ${roleName}`);

    // If the filename contains a timestamp (like doll_熊辉_14869285_cloned.mp3),
    // we need to extract just the avatar name part
    const timestampMatch = roleName.match(/(.*?)_(\d{8,})$/);
    if (timestampMatch && timestampMatch[1]) {
      roleName = timestampMatch[1];
      log.info(`Extracted avatar name without timestamp: ${roleName}`);
    }
  } else {
    // Standard format: roleName.mp3
    roleName = fileName.split('.')[0] || `cloned_voice_${Date.now()}`;
    log.info(`Extracted role name from standard file: ${roleName}`);
  }

  // For Chinese names like "doll_熊辉_12557424", we want to use just "doll_熊辉" as the role name
  // This is because the hash suffix is not part of the actual role name
  if (roleName.match(/[\u4e00-\u9fa5]/)) {
    // Check if there's a hash suffix (8 digits) after the Chinese characters
    const match = roleName.match(/(.*[\u4e00-\u9fa5]+)_(\d{8})$/);
    if (match && match[1]) {
      roleName = match[1];
      log.info(`Extracted Chinese role name without hash: ${roleName}`);
    }
  }

  return roleName;
}

/**
 * Prepare request payload for TTS services
 * @param {string} text - Text to speak
 * @param {Object} options - Additional options
 * @returns {Object} - Prepared request payload and headers
 */
export function prepareTTSRequestPayload(text, options = {}) {
  let requestBody;
  let headers = {};

  // Check if this is an add_speaker request (has reference_audio_file)
  if (options.reference_audio_file) {
    // Use FormData for add_speaker requests
    const formData = new FormData();

    // Add common parameters
    formData.append('text', text);
    formData.append('stream', options.stream ? 'true' : 'false');
    formData.append('response_format', options.response_format || 'wav');

    // Add the audio file
    formData.append('reference_audio_file', options.reference_audio_file);

    // Add name if provided
    if (options.name) {
      formData.append('name', options.name);
    }

    // Add reference text if provided
    if (options.reference_text) {
      formData.append('reference_text', options.reference_text);
    }

    // Log FormData contents
    logger.info('FormData contents:');
    for (let pair of formData.entries()) {
      if (pair[0] === 'reference_audio_file' && (pair[1] instanceof Blob || pair[1] instanceof File)) {
        logger.info(`${pair[0]}: [object File]`);
      } else {
        logger.info(`${pair[0]}: ${pair[1]}`);
      }
    }

    requestBody = formData;
    // Don't set Content-Type header for FormData - browser will set it automatically with boundary
  } else {
    // Use JSON for regular voice requests
    requestBody = JSON.stringify({
      text: text,
      stream: options.stream,
      response_format: options.response_format || 'wav',
      name: options.name,
    });
    headers['Content-Type'] = 'application/json';
  }

  return { requestBody, headers };
}
