/**
 * Multimodal Processing Utilities
 * Handles audio, video, and text processing for multimodal AI interactions
 * Supports various formats including requirements for real-time APIs
 */

import { createLogger } from '@/utils/logger';
import { DEFAULT_AUDIO_CONFIG } from '@/media/modality/audio';

// Create a logger for this module
const logger = createLogger('MultimodalUtils');

/**
 * Multimodal input format constants
 */
export const MULTIMODAL_LIMITS = {
    // General limits
    MAX_VIDEO_FRAME_SIZE_KB: 500,
    RECOMMENDED_FRAME_RATE: 2, // frames per second

    // Audio format requirements (based on common real-time API specs)
    AUDIO_FORMAT: {
        SAMPLE_RATE: 24000, // 24kHz
        CHANNELS: 1, // mono
        BIT_DEPTH: 16, // PCM16
    },

    // Video format requirements
    VIDEO_FORMAT: {
        SUPPORTED_FORMATS: ['jpeg', 'jpg'],
        RECOMMENDED_RESOLUTIONS: ['480p', '720p', '1080p'],
        ENCODING: 'base64'
    }
};

/**
 * Normalize input from various sources into a consistent multimodal format
 * @param {string|Float32Array|Object} input - Raw input data
 * @param {Object} options - Processing options
 * @param {Array} options.currentVideoFrames - Current video frames if available
 * @param {boolean} options.enableVideoInput - Whether video input is enabled
 * @param {boolean} options.isVideoStreaming - Whether video streaming is active
 * @returns {Object} Normalized multimodal input
 */
export function normalizeInput(input, options = {}) {
    const {
        currentVideoFrames = null,
        enableVideoInput = false,
        isVideoStreaming = false
    } = options;

    const normalized = {
        text: null,
        audio: null,
        video: null,
        metadata: {
            timestamp: Date.now(),
            inputType: 'unknown',
            isMultimodal: false
        }
    };

    // Handle different input types
    if (typeof input === 'string') {
        normalized.text = input;
        normalized.metadata.inputType = 'text';
    } else if (input && typeof input === 'object') {
        if (input instanceof Float32Array || input instanceof Int16Array ||
            input instanceof Uint8Array || (input.length && typeof input[0] === 'number')) {
            // Audio array input
            normalized.audio = convertAudioFormat(input);
            normalized.metadata.inputType = 'audio';
        } else {
            // Object input (multimodal or existing format)
            normalized.text = input.text || null;
            normalized.audio = input.audio ? convertAudioFormat(input.audio) : null;
            normalized.video = input.video || input.videoFrames || null;
            normalized.metadata = { ...normalized.metadata, ...(input.metadata || {}) };
        }
    }

    // Add current video frames if video streaming is active (default mode: audio + video)
    if (isVideoStreaming && enableVideoInput && currentVideoFrames) {
        normalized.video = prepareVideoFrames(normalized.video || currentVideoFrames);
    }

    // Determine if this is multimodal input
    const hasMultipleModalities = [
        !!normalized.text,
        !!normalized.audio,
        !!normalized.video
    ].filter(Boolean).length > 1;

    normalized.metadata.isMultimodal = hasMultipleModalities;

    // Update input type based on what we have
    if (hasMultipleModalities) {
        normalized.metadata.inputType = 'multimodal';
    } else if (normalized.audio && normalized.video) {
        normalized.metadata.inputType = 'audio_video'; // Default real-time mode
    } else if (normalized.video) {
        normalized.metadata.inputType = 'video';
    } else if (normalized.audio) {
        normalized.metadata.inputType = 'audio';
    }

    logger.debug('🔧 Input normalized for multimodal processing:', {
        inputType: normalized.metadata.inputType,
        isMultimodal: normalized.metadata.isMultimodal,
        hasText: !!normalized.text,
        hasAudio: !!normalized.audio,
        hasVideo: !!normalized.video,
        audioLength: normalized.audio?.length || 0,
        videoFrameCount: normalized.video?.length || 0
    });

    return normalized;
}

/**
 * Convert audio data to standard real-time format: PCM16, 24kHz, mono
 * @param {Float32Array|Int16Array|Uint8Array} audioData - Raw audio data
 * @returns {Float32Array} Converted audio data
 */
export function convertAudioFormat(audioData) {
    if (!audioData) {
        return null;
    }

    // For now, assume input is already in correct format
    // In production, you might need sample rate conversion
    if (audioData instanceof Float32Array) {
        return audioData;
    }

    // Convert other formats to Float32Array
    if (audioData instanceof Int16Array) {
        const float32 = new Float32Array(audioData.length);
        for (let i = 0; i < audioData.length; i++) {
            float32[i] = audioData[i] / 32768.0; // Convert Int16 to Float32
        }
        return float32;
    }

    if (audioData instanceof Uint8Array) {
        const float32 = new Float32Array(audioData.length);
        for (let i = 0; i < audioData.length; i++) {
            float32[i] = (audioData[i] - 128) / 128.0; // Convert Uint8 to Float32
        }
        return float32;
    }

    // Default return as-is for other formats
    return audioData;
}

/**
 * Prepare video frames for real-time multimodal API requirements:
 * - JPEG format, Base64 encoded
 * - Max 500KB per frame
 * - Recommended resolutions (480P/720P/1080P)
 * - 2 frames per second rate
 * @param {string[]|any[]} videoFrames - Raw video frame data
 * @returns {string[]} Prepared video frames
 */
export function prepareVideoFrames(videoFrames) {
    if (!Array.isArray(videoFrames)) {
        return [];
    }

    // For now, assume frames are already Base64 JPEG
    // In production, you might need format conversion and size optimization
    return videoFrames.filter(frame => {
        if (typeof frame === 'string') {
            // Basic size check (Base64 length * 0.75 ≈ byte size)
            const estimatedSize = (frame.length * 0.75) / 1024; // KB
            if (estimatedSize > MULTIMODAL_LIMITS.MAX_VIDEO_FRAME_SIZE_KB) {
                logger.warn(`Video frame size ${estimatedSize.toFixed(1)}KB exceeds ${MULTIMODAL_LIMITS.MAX_VIDEO_FRAME_SIZE_KB}KB limit`);
                // Could implement compression here
                return false; // Skip oversized frames for now
            }
            return true;
        }
        return false;
    });
}

/**
 * Compress video frame if it exceeds size limits
 * @param {string} base64Frame - Base64 encoded frame
 * @param {number} maxSizeKB - Maximum size in KB
 * @returns {Promise<string>} Compressed frame
 */
export async function compressVideoFrame(base64Frame, maxSizeKB = MULTIMODAL_LIMITS.MAX_VIDEO_FRAME_SIZE_KB) {
    if (typeof window === 'undefined') {
        // Node.js environment - would need image processing library
        logger.warn('Video compression not available in Node.js environment');
        return base64Frame;
    }

    return new Promise((resolve) => {
        const img = new Image();
        img.onload = () => {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');

            // Start with original dimensions
            let { width, height } = img;

            // Reduce quality until we meet size requirements
            let quality = 0.9;
            let result = base64Frame;

            while (quality > 0.1) {
                canvas.width = width;
                canvas.height = height;
                ctx.drawImage(img, 0, 0, width, height);

                const compressed = canvas.toDataURL('image/jpeg', quality);
                const sizeKB = (compressed.length * 0.75) / 1024;

                if (sizeKB <= maxSizeKB) {
                    result = compressed.split(',')[1]; // Remove data URL prefix
                    break;
                }

                quality -= 0.1;
            }

            resolve(result);
        };

        img.onerror = () => resolve(base64Frame);
        img.src = `data:image/jpeg;base64,${base64Frame}`;
    });
}

/**
 * Extract frames from video at specified frame rate
 * @param {HTMLVideoElement|Blob} video - Video source
 * @param {number} fps - Frames per second to extract
 * @returns {Promise<string[]>} Array of base64 encoded frames
 */
export async function extractVideoFrames(video, fps = MULTIMODAL_LIMITS.RECOMMENDED_FRAME_RATE) {
    if (typeof window === 'undefined') {
        logger.warn('Video frame extraction not available in Node.js environment');
        return [];
    }

    return new Promise((resolve, reject) => {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        const frames = [];

        if (video instanceof HTMLVideoElement) {
            const interval = 1 / fps;
            let currentTime = 0;

            const captureFrame = () => {
                if (currentTime >= video.duration) {
                    resolve(frames);
                    return;
                }

                video.currentTime = currentTime;
                video.addEventListener('seeked', function onSeeked() {
                    video.removeEventListener('seeked', onSeeked);

                    canvas.width = video.videoWidth;
                    canvas.height = video.videoHeight;
                    ctx.drawImage(video, 0, 0);

                    const frameData = canvas.toDataURL('image/jpeg', 0.8).split(',')[1];
                    frames.push(frameData);

                    currentTime += interval;
                    setTimeout(captureFrame, 10);
                });
            };

            captureFrame();
        } else {
            reject(new Error('Unsupported video source type'));
        }
    });
}

/**
 * Process multimodal content for real-time API
 * @param {Object} normalizedInput - Normalized multimodal input
 * @returns {Object} Processed content ready for API
 */
export function processMultimodal(normalizedInput) {
    const content = {
        text: normalizedInput.text,
        videoFrames: normalizedInput.video,
        audioData: normalizedInput.audio,
        metadata: normalizedInput.metadata
    };

    // Prepare audio for real-time transmission if present
    if (normalizedInput.audio) {
        // Convert Float32Array to base64 PCM16 for real-time APIs
        content.audioBuffer = audioToBase64PCM16(normalizedInput.audio);
    }

    logger.debug('📦 Processed multimodal content:', {
        hasText: !!content.text,
        hasAudio: !!content.audioData,
        hasVideo: !!content.videoFrames?.length,
        audioBufferSize: content.audioBuffer?.length || 0,
        videoFrameCount: content.videoFrames?.length || 0
    });

    return content;
}

/**
 * Convert Float32Array audio to base64 PCM16 format
 * @param {Float32Array} audioData - Audio samples
 * @returns {string} Base64 encoded PCM16 data
 */
function audioToBase64PCM16(audioData) {
    try {
        if (!audioData) return null;

        // Convert to Int16 PCM format
        let int16Data;
        if (audioData instanceof Float32Array) {
            // Convert Float32Array to Int16Array
            int16Data = new Int16Array(audioData.length);
            for (let i = 0; i < audioData.length; i++) {
                // Convert float in range [-1, 1] to int16 in range [-32768, 32767]
                int16Data[i] = Math.max(-32768, Math.min(32767, Math.floor(audioData[i] * 32767)));
            }
        } else if (audioData instanceof ArrayBuffer) {
            // Assume ArrayBuffer is already in Int16 format
            int16Data = new Int16Array(audioData);
        } else {
            throw new Error('Unsupported audio data format');
        }

        // Convert Int16Array to Base64
        const uint8Array = new Uint8Array(int16Data.buffer);
        let binary = '';
        for (let i = 0; i < uint8Array.length; i++) {
            binary += String.fromCharCode(uint8Array[i]);
        }
        return btoa(binary);
    } catch (error) {
        logger.error('Error converting audio to base64 PCM16:', error);
        return null;
    }
}

/**
 * Validate multimodal input meets API requirements
 * @param {Object} normalizedInput - Normalized input to validate
 * @returns {Object} Validation result with errors/warnings
 */
export function validateMultimodalInput(normalizedInput) {
    const result = {
        isValid: true,
        errors: [],
        warnings: []
    };

    // Check video frame requirements
    if (normalizedInput.video?.length) {
        for (let i = 0; i < normalizedInput.video.length; i++) {
            const frame = normalizedInput.video[i];
            if (typeof frame !== 'string') {
                result.errors.push(`Video frame ${i} is not a base64 string`);
                result.isValid = false;
            } else {
                const sizeKB = (frame.length * 0.75) / 1024;
                if (sizeKB > MULTIMODAL_LIMITS.MAX_VIDEO_FRAME_SIZE_KB) {
                    result.warnings.push(`Video frame ${i} size (${sizeKB.toFixed(1)}KB) exceeds recommended limit`);
                }
            }
        }
    }

    // Check audio format
    if (normalizedInput.audio && !(normalizedInput.audio instanceof Float32Array)) {
        result.warnings.push('Audio data is not in Float32Array format - conversion may be needed');
    }

    return result;
}
