/**
 * Utilities for working with vLLM and media
 */

import { blobToBase64, formatMediaForVLLM } from './mediaUtils';

/**
 * Create a vLLM payload with media content
 * @param options Options for creating the payload
 * @returns Formatted payload for vLLM
 */
export function createVLLMMediaPayload(options: {
  mediaData: string; // Base64 or data URL
  mediaType: 'audio' | 'video' | 'image';
  systemPrompt?: string;
  userPrompt?: string;
  model?: string;
  temperature?: number;
  maxTokens?: number;
  stream?: boolean;
}): any {
  // Default system prompt based on media type
  const defaultSystemPrompt = options.mediaType === 'audio'
    ? 'You are a helpful assistant. Transcribe the audio and respond appropriately.'
    : options.mediaType === 'video'
      ? 'You are a helpful assistant. Analyze the video and respond appropriately.'
      : 'You are a helpful assistant. Analyze the image and respond appropriately.';

  // Default user prompt
  const defaultUserPrompt = options.mediaType === 'audio'
    ? 'Please transcribe and respond to this audio: [Audio content not displayed]'
    : options.mediaType === 'video'
      ? 'Please analyze this video and describe what you see: [Video content not displayed]'
      : 'Please analyze this image and describe what you see: [Image content not displayed]';

  // Determine content type based on media type
  const contentType = options.mediaType === 'audio' ? 'audio' : 'image_url';

  // Format the media data as a data URL if it's not already
  const mediaDataUrl = options.mediaData.startsWith('data:')
    ? options.mediaData
    : `data:${options.mediaType === 'audio' ? 'audio/wav' : 'image/jpeg'};base64,${options.mediaData}`;

  // Create the messages array
  const messages = [
    {
      role: 'system',
      content: options.systemPrompt || defaultSystemPrompt
    },
    {
      role: 'user',
      content: [
        {
          type: 'text',
          text: options.userPrompt || defaultUserPrompt
        },
        {
          type: contentType === 'audio' ? 'audio' : 'image_url',
          [contentType === 'audio' ? 'audio' : 'image_url']: contentType === 'audio'
            ? mediaDataUrl
            : { url: mediaDataUrl }
        }
      ]
    }
  ];

  // Create the payload
  return {
    model: options.model || 'Qwen2.5-Omni-7B',
    messages,
    stream: options.stream !== false, // Default to streaming
    temperature: options.temperature || 0.7,
    max_completion_tokens: options.maxTokens || 2048
  };
}
