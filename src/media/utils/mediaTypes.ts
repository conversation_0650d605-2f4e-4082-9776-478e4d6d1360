/**
 * Shared types for media components
 */

// Media Types
export type MediaType = 'audio' | 'video' | 'audio-video';

// Capture Types
export interface CaptureInfo {
  mediaType: MediaType;
  tracks?: MediaStreamTrack[];
}

export interface MediaCaptureOptions {
  // Audio options
  audio?: {
    echoCancellation?: boolean;
    noiseSuppression?: boolean;
    autoGainControl?: boolean;
    sampleRate?: number;
  };

  // Video options
  video?: {
    width?: number;
    height?: number;
    frameRate?: number;
    facingMode?: 'user' | 'environment';
  };

  // Events
  onCaptureStart?: (info: CaptureInfo) => void;
  onCaptureStop?: (info: CaptureInfo) => void;
  onCaptureError?: (error: Error) => void;
  onFrame?: (frameInfo: FrameInfo) => void;
}

export interface FrameInfo {
  frameNumber: number;
  totalFrames: number;
  dataUrl: string;
}

// Processing Types
export interface AudioProcessingOptions {
  sampleRate?: number;
  channels?: number;
  bitDepth?: number;
  format?: 'wav' | 'mp3' | 'ogg';
}

export interface VideoProcessingOptions {
  quality?: number;
  maxWidth?: number;
  maxHeight?: number;
  format?: 'jpeg' | 'png' | 'webp';
}

export interface FrameExtractionOptions {
  frameRate?: number;
  maxFrames?: number;
  quality?: number;
}

export interface NetworkQuality {
  bandwidth: number; // in kbps
  latency: number; // in ms
  quality: 'low' | 'medium' | 'high' | 'unknown';
}

// Streaming Types
export type StreamingProtocol = 'websocket' | 'http';

export interface StreamInfo {
  mediaType: string;
  sessionId: string;
  timestamp: number;
}

export interface StreamingOptions {
  // Connection options
  endpoint: string;
  protocol?: StreamingProtocol;
  sessionId?: string;

  // Streaming options
  chunkSize?: number;
  retryAttempts?: number;
  retryDelay?: number;

  // Events
  onStreamStart?: (info: StreamInfo) => void;
  onStreamStop?: (info: StreamInfo) => void;
  onStreamError?: (error: Error, info?: StreamInfo) => void;
  onResponse?: (response: any) => void;
  onProgress?: (progress: StreamProgress) => void;
}

export interface StreamProgress {
  bytesUploaded: number;
  totalBytes: number;
  percentage: number;
}

// Voice Activity Detection (VAD) Types
export interface VADOptions {
  /** Energy threshold (0-1) for speech detection */
  threshold?: number;

  /** Minimum silence time in ms to trigger stop */
  minSilenceTime?: number;

  /** Maximum speech time in ms */
  maxSpeechTime?: number;

  /** Smoothing time in ms */
  smoothingTime?: number;

  /** Energy smoothing factor (0-1) */
  energyAlpha?: number;
}

export interface VADResult {
  /** Whether speech is currently detected */
  isSpeaking: boolean;

  /** Whether recording should stop */
  shouldStop: boolean;

  /** Current energy level (0-1) */
  energy: number;

  /** Duration of current speech in ms */
  speechDuration: number;

  /** Duration of current silence in ms */
  silenceDuration: number;
}
