/**
 * Media module exports
 * Provides a unified API for media capture, processing, and multimodal interactions
 */

// Export core types
export type {
    MediaType,
    CaptureInfo,
    MediaCaptureOptions,
    FrameInfo,
    AudioProcessingOptions,
    VADOptions,
    VADResult
} from './utils/mediaTypes';

// Export core utilities (legacy - gradually moving to modality structure)
export { formatMediaForVLLM } from './utils/mediaUtils';
export { extractFrames, extractFramesInNode } from './utils/videoUtils';
export { createVLLMMediaPayload } from './utils/vllmUtils';

// Export legacy audio utilities (for backward compatibility)
export {
    AudioFormat,
    DEFAULT_AUDIO_CONFIG as DEFAULT_AUDIO_OPTIONS
} from './modality/audio.js';

// Export NEW MODALITY STRUCTURE - preferred imports
// Audio processing from new modality structure
export {
    processRealtimeAudio,
    validateAudioData,
    createFallbackBase64Audio,
    convertFloat32ToWav,
    detectAudioFormat,
    base64ToBlob,
    blobToBase64,
    checkAudioBuffer,
    createFallbackTone,
    createAudioFromBlob,
    playAudioWithPromise,
    splitAudioIntoChunks,
    b64ToArrayBuffer,
    concatArrayBuffers,
    pcmToAudioBuffer,
    setReverb,
    setMixerGain,
    extractRoleNameFromAudioFile,
    prepareTTSRequestPayload
} from './modality/audio.js';

// Video processing from new modality structure  
export {
    processVideoForRealtime,
    extractFramesInBrowser,
    createVideoThumbnail,
    detectVideoFormat,
    validateVideoInput
} from './modality/video.js';

// Export multimodal processing utilities
export {
    normalizeInput,
    convertAudioFormat,
    prepareVideoFrames,
    compressVideoFrame,
    extractVideoFrames,
    processMultimodal,
    validateMultimodalInput,
    MULTIMODAL_LIMITS
} from './utils/multimodalUtils.js';

// Export capture components
export * from './capture';

// Export streaming components
export * from './streaming';

// Export core camera management
export { CameraManager } from './core/CameraManager.js';

// Export LLM API service for server-side LLM processing
export { llmAPI } from './api/llmAPI.ts';
