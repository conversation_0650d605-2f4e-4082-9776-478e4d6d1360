# Media Streaming Module

This directory contains components for streaming media data, including audio, text, and LLM responses.

## Directory Structure

- `index.ts`: The canonical source for streaming exports. All new components should be exported from here.
- `index.js`: Re-exports components for backward compatibility.
- `StreamingManager.ts`: Main manager for streaming media data.
- `WebSocketStreamingManager.ts`: WebSocket-specific implementation of streaming.
- `StreamingAudioPlayer.js`: Player for streaming audio data.
- `LLMStreamProcessor.js`: Processor for streaming LLM responses.
- `utils.js`: Utility functions for streaming.
- `tts/`: Directory for text-to-speech streaming components.
  - Re-exports TTS services from the talking head module.

## Usage

Import components from the index.ts file:

```typescript
import {
  StreamingManager,
  StreamingAudioPlayer,
  LLMStreamProcessor,
  BaseTTSService,
  GoogleTTSService,
  MicrosoftTTSService,
  ElevenLabsTTSService,
  ServerTTSService,
  SparkTTSService,
  TTSServiceFactory,
  processWithClientSideChunking
} from '@/media/streaming';
```

## Component Responsibilities

- **StreamingManager**: Manages streaming of media data to the backend.
- **WebSocketStreamingManager**: Handles WebSocket-specific streaming.
- **StreamingAudioPlayer**: Plays streaming audio data.

- **LLMStreamProcessor**: Processes LLM responses for streaming.
- **TTSServiceWrapper**: Wraps TTS services for streaming.

## Adding New Components

1. Create your component in the appropriate directory.
2. Export it from `index.ts`.
3. Update this README if necessary.

## Deprecated Components

- `AvatarStreamingManager.js`: Replaced by `StreamingManager.ts`.
- `StreamingFramework.ts`: Replaced by more specific implementations.
- `StreamingAudioProcessor.js`: Functionality moved to `StreamingManager.ts` and `LLMStreamProcessor.js`.
