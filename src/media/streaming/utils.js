/**
 * Streaming utilities
 * Contains utility functions for streaming media
 */

// StreamingAudioPlayer has been removed - agent mode handles audio natively
// StreamingAudioProcessor has been deprecated

/**
 * Process audio data with client-side chunking for streaming benefits
 * @param {ArrayBuffer|AudioBuffer} audioData - Complete audio data as <PERSON><PERSON><PERSON><PERSON>uffer or AudioBuffer
 * @param {string} text - Original text being spoken
 * @param {Object} options - Configuration options
 * @returns {Promise<Object>} - Result object with success status and chunks
 */
export async function processWithClientSideChunking(audioData, text, options = {}) {
    // Default options
    const defaultOptions = {
        chunkDurationMs: 800,
        useNaturalBreaks: true,
        processingDelayMs: 10,
        autoPlay: true,
        // Audio validation options
        audioValidation: {
            enabled: true,
            minNonZeroPercent: 1.0,
            minAmplitude: 0.0001
        },
        forcePlayback: false,
        onSilentChunk: null,
        logger: console // Default to console if no logger provided
    };

    // Merge options
    const mergedOptions = { ...defaultOptions, ...options };
    const logger = mergedOptions.logger || console;

    try {
        // Validate input
        if (!audioData) {
            throw new Error('No audio data provided');
        }

        // Create result object
        const result = {
            success: false,
            chunks: [],
            player: null
        };

        // StreamingAudioPlayer has been removed - agent mode handles audio natively
        let player = null;
        if (mergedOptions.autoPlay) {
            logger.info('Audio playback is now handled natively by agent mode');
        }

        // Create processor options
        const processorOptions = {
            chunkDurationMs: mergedOptions.chunkDurationMs,
            useNaturalBreaks: mergedOptions.useNaturalBreaks,
            processingDelayMs: mergedOptions.processingDelayMs,
            onChunk: async (chunk) => {
                // Add chunk to result
                result.chunks.push(chunk);

                // Play chunk if autoPlay is enabled
                if (mergedOptions.autoPlay && player) {
                    // Pass audio validation options if available
                    const playbackOptions = {
                        audioValidation: mergedOptions.audioValidation,
                        forcePlayback: mergedOptions.forcePlayback
                    };

                    try {
                        // Play the chunk with validation
                        const playResult = await player.playChunk(chunk.data, playbackOptions);

                        // If the chunk was silent and we have a test tone function, use it
                        if (playResult.silent && typeof mergedOptions.onSilentChunk === 'function') {
                            await mergedOptions.onSilentChunk(chunk);
                        }
                    } catch (playError) {
                        logger.warn(`Error playing chunk: ${playError.message}`);
                        // Continue processing even if playback fails
                    }
                }

                // Call custom onChunk callback if provided
                if (typeof mergedOptions.onChunk === 'function') {
                    try {
                        await mergedOptions.onChunk(chunk);
                    } catch (callbackError) {
                        logger.warn(`Error in onChunk callback: ${callbackError.message}`);
                        // Continue processing even if callback fails
                    }
                }
            },
            onComplete: async (chunks) => {
                // Set success flag
                result.success = true;

                // Call custom onComplete callback if provided
                if (typeof mergedOptions.onComplete === 'function') {
                    try {
                        await mergedOptions.onComplete(chunks);
                    } catch (callbackError) {
                        logger.warn(`Error in onComplete callback: ${callbackError.message}`);
                    }
                }
            },
            onError: (error) => {
                logger.error(`Error: ${error}`);

                // Call custom onError callback if provided
                if (typeof mergedOptions.onError === 'function') {
                    try {
                        mergedOptions.onError(error);
                    } catch (callbackError) {
                        logger.error(`Error in onError callback: ${callbackError.message}`);
                    }
                }
            }
        };

        // StreamingAudioProcessor has been deprecated
        // Instead, we'll use a simpler approach to process the audio
        logger.warn('StreamingAudioProcessor has been deprecated. Using fallback processing method.');

        // Create a simple result with the full audio
        result.success = true;
        result.chunks = [{
            data: audioData,
            text: text,
            isLast: true,
            duration: typeof audioData.duration === 'number' ? audioData.duration : 2.0
        }];

        // Call onComplete callback
        if (typeof processorOptions.onComplete === 'function') {
            await processorOptions.onComplete(result.chunks);
        }

        return result;
    } catch (error) {
        logger.error(`Error in processWithClientSideChunking: ${error.message}`);

        // Call custom onError callback if provided
        if (typeof options.onError === 'function') {
            try {
                options.onError(error);
            } catch (callbackError) {
                logger.error(`Error in onError callback: ${callbackError.message}`);
            }
        }

        return {
            success: false,
            error: error.message,
            chunks: []
        };
    }
}
