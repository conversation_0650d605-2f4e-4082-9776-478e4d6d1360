/**
 * Media Streaming Module
 * Centralizes all streaming functionality for audio, video, and real-time data
 */

// Export streaming utilities
export { processWithClientSideChunking } from './utils.js';

// Export audio streaming functionality
export * from './RealtimeAudioManager';

/**
 * @module media/streaming
 * @description
 * This module provides centralized streaming functionality for the application.
 * It handles audio streaming, video streaming, and real-time data transmission.
 * 
 * Key components:
 * - RealtimeAudioManager: Manages real-time audio streaming for WebSocket connections
 * - Utilities for client-side chunking and streaming
 */
