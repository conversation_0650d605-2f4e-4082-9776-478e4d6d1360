/**
 * RealtimeAudioManager
 * Centralizes audio streaming functionality for real-time WebSocket connections
 * Handles audio processing, rate limiting, and session management
 */

import { createLogger, LogLevel } from '@/utils/logger';
import { float32ToInt16PCM } from '@/media/modality/audio';
import { DEFAULT_AUDIO_CONFIG } from '@/media/modality/audio';

/**
 * Configuration options for RealtimeAudioManager
 */
export interface RealtimeAudioConfig {
    /** Sample rate for audio processing (default: 16000 for Aliyun Qwen-Omni) */
    sampleRate?: number;
    /** Number of audio channels (default: 1) */
    numChannels?: number;
    /** Bit depth for audio processing (default: 16) */
    bitDepth?: number;
    /** Minimum interval between audio chunks in milliseconds (default: 200ms = 5 chunks/sec) */
    minIntervalMs?: number;
    /** Enable debug logging (default: false) */
    enableDebugLogging?: boolean;
    /** Callback for handling errors */
    onError?: (error: any) => void;
}

// Default configuration values from centralized config
const DEFAULT_CONFIG = {
    sampleRate: DEFAULT_AUDIO_CONFIG.sampleRate,
    numChannels: DEFAULT_AUDIO_CONFIG.numChannels,
    bitDepth: DEFAULT_AUDIO_CONFIG.bitDepth,
    minIntervalMs: DEFAULT_AUDIO_CONFIG.chunkDurationMs || 200, // Fallback to 200ms if undefined
    enableDebugLogging: false
};

/**
 * Audio rate tracking information
 */
interface AudioRateTracker {
    /** Count of chunks sent in current tracking period */
    chunkCount: number;
    /** Start time of current tracking period */
    startTime: number;
    /** Timestamp of last sent chunk */
    lastSendTime: number;
    /** Minimum interval between chunks in milliseconds */
    minIntervalMs: number;
    /** Total chunks sent since initialization */
    totalChunks: number;
    /** Recent chunks for rate analysis */
    recentChunks: Array<{ timestamp: number; size: number }>;
    /** Maximum number of recent chunks to track */
    maxRecentChunks: number;
    /** Whether the session is ready for audio transmission */
    sessionReady: boolean;
    /** Timestamp when session was created */
    sessionCreatedAt: number | null;
}

/**
 * Session state for audio streaming
 */
export interface AudioSessionState {
    /** Whether the session is active */
    isActive: boolean;
    /** Whether the session is ready for audio transmission */
    isReady: boolean;
    /** Timestamp when session was created */
    createdAt: number | null;
    /** Session ID if available */
    sessionId?: string;
}

/**
 * Manages real-time audio streaming with rate limiting and format conversion
 * Specifically designed for Aliyun Qwen-Omni Realtime API requirements
 */
export class RealtimeAudioManager {
    private logger = createLogger('RealtimeAudioManager');
    private config: Required<RealtimeAudioConfig>;
    private audioRateTracker: AudioRateTracker;
    private audioSendCallback: ((base64Audio: string) => Promise<boolean>) | null = null;

    /**
     * Create a new RealtimeAudioManager
     * @param config Configuration options
     */
    constructor(config: RealtimeAudioConfig = {}) {
        // Use centralized config with any overrides
        this.config = {
            sampleRate: config.sampleRate ?? DEFAULT_CONFIG.sampleRate,
            numChannels: config.numChannels ?? DEFAULT_CONFIG.numChannels,
            bitDepth: config.bitDepth ?? DEFAULT_CONFIG.bitDepth,
            minIntervalMs: config.minIntervalMs ?? DEFAULT_CONFIG.minIntervalMs,
            enableDebugLogging: config.enableDebugLogging ?? DEFAULT_CONFIG.enableDebugLogging,
            onError: config.onError ?? ((error) => console.error('RealtimeAudioManager error:', error))
        };

        // Set up rate tracker for audio chunks
        this.audioRateTracker = {
            chunkCount: 0,
            startTime: Date.now(),
            lastSendTime: 0,
            minIntervalMs: this.config.minIntervalMs,
            totalChunks: 0,
            recentChunks: [],
            maxRecentChunks: 20,
            sessionReady: false,
            sessionCreatedAt: null
        };

        // Set log level based on debug flag
        if (this.config.enableDebugLogging) {
            this.logger.setLogLevel(LogLevel.DEBUG);
            this.logger.debug('RealtimeAudioManager initialized with config:', {
                sampleRate: this.config.sampleRate,
                numChannels: this.config.numChannels,
                bitDepth: this.config.bitDepth,
                minIntervalMs: this.config.minIntervalMs
            });
        } else {
            this.logger.setLogLevel(LogLevel.INFO);
        }
    }

    /**
     * Set the callback function for sending audio data
     * @param callback Function that sends audio data and returns success status
     */
    setSendAudioCallback(callback: (base64Audio: string) => Promise<boolean>): void {
        this.audioSendCallback = callback;
        this.logger.debug('Audio send callback set');
    }

    /**
     * Initialize a new audio streaming session
     * @returns Session state
     */
    initSession(): AudioSessionState {
        // Reset audio rate tracker for new session
        this.audioRateTracker = {
            chunkCount: 0,
            startTime: Date.now(),
            lastSendTime: 0,
            minIntervalMs: this.config.minIntervalMs,
            totalChunks: 0,
            recentChunks: [],
            maxRecentChunks: 20,
            sessionReady: false,
            sessionCreatedAt: Date.now()
        };

        this.logger.debug('📈 Audio rate tracker reset for new session');

        // Set timer for session readiness (2000ms delay - matches server startup time + safety margin)
        setTimeout(() => {
            this.logger.debug('✅ Session ready for audio after initialization delay');
            this.audioRateTracker.sessionReady = true;
        }, 2000);

        return {
            isActive: true,
            isReady: false,
            createdAt: this.audioRateTracker.sessionCreatedAt
        };
    }

    /**
     * Get current session state
     * @returns Session state
     */
    getSessionState(): AudioSessionState {
        return {
            isActive: true,
            isReady: this.audioRateTracker.sessionReady,
            createdAt: this.audioRateTracker.sessionCreatedAt
        };
    }

    /**
     * Process and send audio data with rate limiting
     * @param audioData Audio data to process and send
     * @returns Promise resolving to success status
     */
    async processAndSendAudio(audioData: ArrayBuffer | Float32Array | Int16Array): Promise<boolean> {
        if (!this.audioSendCallback) {
            this.logger.warn('No audio send callback set');
            return false;
        }

        // Check if session is ready for audio
        if (!this.audioRateTracker.sessionReady) {
            const sessionAge = this.audioRateTracker.sessionCreatedAt ?
                Date.now() - this.audioRateTracker.sessionCreatedAt : 0;

            // this.logger.debug(`⏱️ Session not ready for audio yet (age: ${sessionAge}ms), skipping chunk`);
            return false;
        }

        try {
            // Rate limiting check to prevent 1011 errors
            const now = Date.now();
            const timeSinceLastSend = now - this.audioRateTracker.lastSendTime;

            // Add a check for first chunk to ensure extra delay after session creation
            if (this.audioRateTracker.totalChunks === 0) {
                // For the very first chunk after session creation, add extra safety delay
                const sessionAge = now - (this.audioRateTracker.sessionCreatedAt || now);
                if (sessionAge < 2500) { // Wait at least 2.5 seconds for first chunk (enhanced safety)
                    const extraWait = 2500 - sessionAge;
                    this.logger.debug(`⏱️ Adding extra ${extraWait}ms delay for first audio chunk`);
                    await new Promise(resolve => setTimeout(resolve, extraWait));
                }
            }

            // Track audio chunk for rate monitoring
            this.audioRateTracker.totalChunks++;
            this.audioRateTracker.chunkCount++;

            // Add to recent chunks for rate analysis
            this.audioRateTracker.recentChunks.push({
                timestamp: now,
                size: audioData instanceof ArrayBuffer ? audioData.byteLength :
                    (audioData instanceof Float32Array || audioData instanceof Int16Array ?
                        audioData.length * (this.config.bitDepth / 8) : 0)
            });

            // Keep only the most recent chunks
            if (this.audioRateTracker.recentChunks.length > this.audioRateTracker.maxRecentChunks) {
                this.audioRateTracker.recentChunks.shift();
            }

            // Calculate current rate (chunks per second)
            const elapsedSec = (now - this.audioRateTracker.startTime) / 1000;
            const currentRate = elapsedSec > 0 ? this.audioRateTracker.chunkCount / elapsedSec : 0;

            // Strict rate limiting - enforce minimum interval strictly
            if (timeSinceLastSend < this.audioRateTracker.minIntervalMs) {
                const waitTime = this.audioRateTracker.minIntervalMs - timeSinceLastSend;
                // this.logger.debug(`⏱️ Rate limiting applied - waiting ${waitTime}ms before sending next chunk`);
                await new Promise(resolve => setTimeout(resolve, waitTime));
            }

            // Convert audio data to base64 if needed
            let base64Audio = '';

            if (audioData instanceof ArrayBuffer) {
                const uint8Array = new Uint8Array(audioData);
                base64Audio = this._arrayBufferToBase64(uint8Array);
            } else if (audioData instanceof Float32Array || audioData instanceof Int16Array) {
                // Convert to PCM16 if needed
                const pcm16 = audioData instanceof Float32Array ?
                    this._float32ToPCM16(audioData) :
                    audioData;

                // Convert to base64
                const buffer = pcm16.buffer.slice(
                    pcm16.byteOffset,
                    pcm16.byteOffset + pcm16.byteLength
                );
                const uint8Array = new Uint8Array(buffer);
                base64Audio = this._arrayBufferToBase64(uint8Array);
            }

            // Log detailed metrics every 10th chunk
            if (this.audioRateTracker.totalChunks % 10 === 0) {
                this.logger.debug(`🔊 SENDING RAW PCM16 CHUNK #${this.audioRateTracker.totalChunks}: ${base64Audio.length} bytes (raw PCM16), session age: ${now - (this.audioRateTracker.sessionCreatedAt || 0)}ms, interval: ${timeSinceLastSend}ms`);
            }

            // Update last send time before sending
            this.audioRateTracker.lastSendTime = now;

            // Send through callback
            return await this.audioSendCallback(base64Audio);

        } catch (error) {
            this.logger.error('Error processing audio data:', error);
            if (this.config.onError) {
                this.config.onError(error);
            }
            return false;
        }
    }

    /**
     * Reset the audio streaming session
     */
    resetSession(): void {
        this.audioRateTracker = {
            chunkCount: 0,
            startTime: Date.now(),
            lastSendTime: 0,
            minIntervalMs: this.config.minIntervalMs,
            totalChunks: 0,
            recentChunks: [],
            maxRecentChunks: 20,
            sessionReady: false,
            sessionCreatedAt: null
        };

        this.logger.debug('Audio session reset');
    }

    /**
     * Clean up resources
     */
    dispose(): void {
        this.audioSendCallback = null;
        this.resetSession();
        this.logger.debug('RealtimeAudioManager disposed');
    }

    private _arrayBufferToBase64(uint8Array: Uint8Array): string {
        let binaryString = '';
        for (let i = 0; i < uint8Array.length; i++) {
            binaryString += String.fromCharCode(uint8Array[i]);
        }
        return btoa(binaryString);
    }

    private _float32ToPCM16(float32Array: Float32Array): Int16Array {
        const int16Array = new Int16Array(float32Array.length);
        for (let i = 0; i < float32Array.length; i++) {
            const sample = Math.max(-1, Math.min(1, float32Array[i]));
            int16Array[i] = sample < 0 ? sample * 0x8000 : sample * 0x7FFF;
        }
        return int16Array;
    }
} 