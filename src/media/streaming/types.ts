/**
 * Type definitions for streaming functionality
 */

/**
 * Streaming transport options
 */
export enum StreamingTransport {
  WebSocket = 'websocket',
  HTTP = 'http'
}

/**
 * Chunking strategy options
 */
export enum ChunkingStrategy {
  FIXED_SIZE = 'fixed_size',
  ADAPTIVE = 'adaptive',
  TIME_BASED = 'time_based',
  NONE = 'none'
}

/**
 * Chunking options for streaming
 */
export interface ChunkingOptions {
  /** Chunking strategy to use */
  strategy: ChunkingStrategy;
  
  /** Minimum chunk size in bytes (for adaptive strategy) */
  minChunkSize?: number;
  
  /** Maximum chunk size in bytes */
  maxChunkSize?: number;
  
  /** Fixed chunk size in bytes (for fixed size strategy) */
  chunkSize?: number;
  
  /** Chunk duration in milliseconds (for time-based strategy) */
  chunkDuration?: number;
}

/**
 * Streaming options
 */
export interface StreamingOptions {
  /** Streaming endpoint URL */
  endpoint: string;
  
  /** Transport method to use */
  transport?: StreamingTransport;
  
  /** Session ID for the streaming session */
  sessionId?: string;
  
  /** Chunking options */
  chunking?: ChunkingOptions;
  
  /** Number of retry attempts on failure */
  retryAttempts?: number;
  
  /** Delay between retry attempts in milliseconds */
  retryDelay?: number;
  
  /** Callback when streaming starts */
  onStreamStart?: (info: { mediaType?: string }) => void;
  
  /** Callback when streaming stops */
  onStreamStop?: (info: { mediaType?: string }) => void;
  
  /** Callback when streaming encounters an error */
  onStreamError?: (error: Error) => void;
  
  /** Callback when a response is received */
  onResponse?: (response: StreamingResponse) => void;
  
  /** Callback for progress updates */
  onProgress?: (progress: { bytesUploaded: number; totalBytes: number; percentage: number }) => void;
}

/**
 * Streaming response from the server
 */
export interface StreamingResponse {
  /** Response type */
  type: string;
  
  /** Response data */
  data: any;
  
  /** Session ID */
  sessionId?: string;
  
  /** Timestamp */
  timestamp?: number;
  
  /** Error message if applicable */
  error?: string;
}
