/**
 * MediaCaptureManager
 * Handles media capture from device cameras and microphones
 * Single ground truth API for starting/stopping media capture with proper VAD handling
 * Enhanced with PCM16 streaming for Aliyun Qwen-Omni realtime WebSocket integration
 */

import { convertFloat32ToWav } from '../utils/audioUtils.js';
import { setupClientVAD, ClientVADOptions } from '../utils/vadUtils';
import { createLogger, LogLevel } from '@/utils/logger';
import { DEFAULT_AUDIO_CONFIG } from '@/media/modality/audio';
import { getMediaCaptureAudioConfig, validateAudioConfig, ALIYUN_AUDIO_CONFIG } from '@/agent/models/aliyun/config/AliyunConfig.js';

// Type definitions
export type MediaType = 'audio' | 'video' | 'audio-video';
export type FrameInfo = {
  dataUrl: string;
  timestamp: number;
  width: number;
  height: number;
};
export type VADMode = 'server' | 'client' | 'none';
export type VADOptions = {
  threshold?: number;
  smoothing?: number;
  minSilenceMs?: number;
  minSpeechMs?: number;
};

export type MediaCaptureOptions = {
  audio?: any;
  video?: any;
  onCaptureStart?: () => void;
  onCaptureStop?: () => void;
  onCaptureError?: (error: Error) => void;
  onFrame?: (frameInfo: FrameInfo) => void;
  maxFrames?: number;
  captureRateMs?: number;
  frameQuality?: number;
  vadMode?: VADMode;
  onSpeechStart?: () => void;
  onSpeechEnd?: (audio?: Float32Array) => void;
  onAudioData?: (pcm16: ArrayBuffer) => void; // For streaming PCM16 audio to WebSocket
  // NEW: Allow passing Aliyun-specific config overrides
  aliyunConfig?: {
    sampleRate?: number;
    bitDepth?: number;
    channels?: number;
    vadThreshold?: number;
    silenceDurationMs?: number;
    minIntervalMs?: number;
    enableDebugLogging?: boolean;
  };
  [key: string]: any;
};

/**
 * MediaCaptureManager
 * Manages media capture (audio/video) with VAD and frame extraction
 */
export class MediaCaptureManager {
  // Logger
  private logger = createLogger('MediaCaptureManager');

  // Media capture state
  private mediaStream: MediaStream | null = null;
  private videoElement: HTMLVideoElement | null = null;
  private canvasElement: HTMLCanvasElement | null = null;
  private canvasContext: CanvasRenderingContext2D | null = null;
  private isCapturing: boolean = false;
  private currentMediaType: MediaType = 'audio-video';

  // Audio processing
  private audioContext: AudioContext | null = null;
  private audioAnalyser: AnalyserNode | null = null;
  private audioSource: MediaStreamAudioSourceNode | null = null;
  private audioScriptProcessor: ScriptProcessorNode | null = null;
  private sessionStartTime: number | null = null;
  private vadEnabled: boolean = false;
  private vadCheckIntervalId: number | null = null;

  // Audio streaming - using ScriptProcessor for raw PCM16 like Python implementation
  private isAudioStreaming: boolean = false;
  private lastAudioSendTime: number = 0;
  private minAudioIntervalMs: number = 200; // Match Python: 200ms intervals
  private targetSampleRate: number = DEFAULT_AUDIO_CONFIG.sampleRate;
  private targetBitDepth: number = DEFAULT_AUDIO_CONFIG.bitDepth;
  private targetChannels: number = DEFAULT_AUDIO_CONFIG.numChannels;

  // Video frame extraction
  private animationFrameId: number | null = null;
  private lastCaptureTime: number = 0;
  private frameCount: number = 0;
  private maxFrames: number = 10;
  private captureRateMs: number = 500; // 2 FPS by default
  private frameQuality: number = 0.8;

  // VAD state
  private options: MediaCaptureOptions;
  private vadInstance: any = null;
  private vadMode: VADMode = 'none';

  // Callbacks
  private onSpeechStart?: () => void;
  private onSpeechEnd?: (audio?: Float32Array) => void;
  private onAudioData?: (pcm16: ArrayBuffer) => void; // PCM16 streaming callback

  /**
   * Create a new MediaCaptureManager
   * @param options Configuration options
   */
  constructor(options: MediaCaptureOptions = {}) {
    // Get centralized Aliyun audio configuration
    const aliyunAudioConfig = getMediaCaptureAudioConfig(options.aliyunConfig || {}) as any;
    
    // Merge with user options, prioritizing Aliyun config for audio
    this.options = {
      audio: options.audio || aliyunAudioConfig.audio,
      video: options.video || false,
      maxFrames: options.maxFrames || 10,
      captureRateMs: options.captureRateMs || 500,
      frameQuality: options.frameQuality || 0.8,
      vadMode: options.vadMode || aliyunAudioConfig.vadMode,
      vadConfig: options.vadConfig || aliyunAudioConfig.vadConfig,
      ...options
    };

    // Apply Aliyun target configurations
    this.targetSampleRate = aliyunAudioConfig.targetSampleRate;
    this.targetBitDepth = aliyunAudioConfig.targetBitDepth;
    this.targetChannels = aliyunAudioConfig.targetChannels;

    // Set up VAD mode
    this.vadMode = this.options.vadMode || 'none';

    // Set up callbacks
    this.onSpeechStart = this.options.onSpeechStart;
    this.onSpeechEnd = this.options.onSpeechEnd;
    this.onAudioData = this.options.onAudioData;

    // Configure max frames
    if (this.options.maxFrames) {
      this.maxFrames = this.options.maxFrames;
    }

    // Configure capture rate
    if (this.options.captureRateMs) {
      this.captureRateMs = this.options.captureRateMs;
    }

    // Configure frame quality
    if (this.options.frameQuality) {
      this.frameQuality = Math.min(Math.max(this.options.frameQuality, 0.1), 1.0);
    }

    // Validate configuration against Aliyun requirements
    const validation = validateAudioConfig({
      sampleRate: this.targetSampleRate,
      bitDepth: this.targetBitDepth,
      channels: this.targetChannels,
      vadThreshold: this.options.vadConfig?.threshold
    }) as any;

    if (!validation.isValid) {
      this.logger.warn('Audio configuration validation failed:', validation.errors);
    }

    if (validation.warnings.length > 0) {
      this.logger.warn('Audio configuration warnings:', validation.warnings);
    }

    this.logger.debug('MediaCaptureManager initialized with Aliyun-compatible config:', {
      audio: !!this.options.audio,
      video: !!this.options.video,
      vadMode: this.vadMode,
      targetSampleRate: this.targetSampleRate,
      targetBitDepth: this.targetBitDepth,
      targetChannels: this.targetChannels,
      aliyunCompatible: validation.isValid,
      configSource: 'ALIYUN_AUDIO_CONFIG'
    });
  }

  /**
   * Single ground truth API to start media capture
   * This is the primary method to start capturing media
   * @param mediaType Type of media to capture ('audio', 'video', 'audio-video')
   * @returns Promise<boolean> Success status
   */
  async startCapture(mediaType: MediaType = 'audio-video'): Promise<boolean> {
    if (this.isCapturing) {
      console.log('[MediaCaptureManager] Already capturing, skipping startCapture');
      return true;
    }

    try {
      console.log('[MediaCaptureManager] Starting capture for mediaType:', mediaType, 'vadMode:', this.vadMode);

      // Store the media type
      this.currentMediaType = mediaType;

      // Initialize media devices
      const initialized = await this._initializeMediaDevices(mediaType);
      if (!initialized) {
        throw new Error(`Failed to initialize ${mediaType} devices`);
      }

      // Start frame capture for video
      if (mediaType === 'video' || mediaType === 'audio-video') {
        this._startFrameCapture();
      }

      // Track session start time for debugging
      this.sessionStartTime = Date.now();

      // Setup audio processing based on VAD mode
      if (mediaType === 'audio' || mediaType === 'audio-video') {
        await this._setupAudioProcessingBasedOnMode();
      }

      this.isCapturing = true;
      this.options.onCaptureStart?.();

      console.log('[MediaCaptureManager] Capture started successfully');
      return true;

    } catch (error) {
      console.error('[MediaCaptureManager] Error starting capture:', error);
      this.options.onCaptureError?.(error instanceof Error ? error : new Error(String(error)));
      return false;
    }
  }

  /**
   * Initialize media devices (private helper)
   * @param mediaType Type of media to initialize
   * @returns Success status
   */
  private async _initializeMediaDevices(mediaType: MediaType): Promise<boolean> {
    try {
      // Configure constraints based on media type
      const constraints: MediaStreamConstraints = {};

      if (mediaType !== 'video') {
        constraints.audio = this.options.audio;
      }

      if (mediaType !== 'audio') {
        constraints.video = this.options.video;
      }

      console.log('[MediaCaptureManager] Requesting media with constraints:', constraints);

      // Request camera and/or microphone access
      this.mediaStream = await navigator.mediaDevices.getUserMedia(constraints);

      // Validate that we got the expected tracks
      if (mediaType !== 'video' && this.mediaStream.getAudioTracks().length === 0) {
        throw new Error('No audio track available in media stream');
      }

      if (mediaType !== 'audio' && this.mediaStream.getVideoTracks().length === 0) {
        throw new Error('No video track available in media stream');
      }

      console.log('[MediaCaptureManager] Media devices initialized successfully', {
        audioTracks: this.mediaStream.getAudioTracks().length,
        videoTracks: this.mediaStream.getVideoTracks().length
      });

      // If we're using video, set up the video and canvas elements for frame extraction
      if (mediaType === 'video' || mediaType === 'audio-video') {
        await this._setupVideoFrameExtraction();
      }

      return true;
    } catch (error) {
      console.error('[MediaCaptureManager] Error initializing media devices:', error);
      return false;
    }
  }

  /**
   * Setup audio processing based on the configured mode (private helper)
   * NEW: Enhanced for PCM16 streaming in server VAD mode
   */
  private async _setupAudioProcessingBasedOnMode(): Promise<void> {
    switch (this.vadMode) {
      case 'client':
        console.log('[MediaCaptureManager] Setting up client VAD');
        await this._setupClientVAD();
        break;
      case 'server':
        console.log('[MediaCaptureManager] Setting up server VAD mode with PCM16 streaming');

        // CRITICAL FIX: Initialize audioContext and audioSource before attempting to use them
        if (!this.audioContext) {
          console.log('[MediaCaptureManager] Creating new AudioContext');
          this.audioContext = new AudioContext({
            sampleRate: this.targetSampleRate // Use 16kHz for Aliyun API compatibility
          });
        }

        // CRITICAL FIX: Create audioSource if not already created
        if (!this.audioSource && this.mediaStream) {
          console.log('[MediaCaptureManager] Creating new MediaStreamAudioSourceNode');
          this.audioSource = this.audioContext.createMediaStreamSource(this.mediaStream);
        }

        if (!this.audioContext || !this.audioSource) {
          throw new Error('Failed to initialize audio context or source');
        }

        await this._setupServerVADWithStreaming();
        break;
      case 'none':
        console.log('[MediaCaptureManager] No VAD mode - skipping VAD setup');
        break;
      default:
        console.warn('[MediaCaptureManager] Unknown VAD mode:', this.vadMode);
    }
  }

  /**
   * NEW: Setup server VAD mode with PCM16 streaming for Aliyun Qwen-Omni realtime
   * In server VAD mode, we continuously stream PCM16 audio to the WebSocket
   * and let the server handle voice activity detection
   */
  private async _setupServerVADWithStreaming(): Promise<void> {
    if (!this.audioContext || !this.audioSource) {
      throw new Error('Audio context or source not available');
    }

    try {
      console.log('[MediaCaptureManager] Setting up server VAD with PCM16 streaming');

      // Use ScriptProcessor for precise PCM16 control matching Python implementation
      this._setupScriptProcessorStreaming();
      console.log('[MediaCaptureManager] Using ScriptProcessor for PCM16 streaming (matching Python implementation)');

      this.isAudioStreaming = true;
      console.log('[MediaCaptureManager] Server VAD with PCM16 streaming setup complete');

    } catch (error) {
      console.error('[MediaCaptureManager] Error setting up server VAD streaming:', error);
      throw error;
    }
  }


  /**
   * Setup ScriptProcessor for raw PCM16 streaming
   * CRITICAL: Matches the Python working implementation exactly at 24kHz
   * Python: RATE = 24000, CHUNK = 3200, 200ms intervals
   * JS: Use 4096 buffer (nearest power-of-2 to 3200) at 24kHz with 200ms rate limiting
   */
  private _setupScriptProcessorStreaming(): void {
    if (!this.audioContext || !this.audioSource) {
      throw new Error('Audio context or source not available');
    }

    // CRITICAL FIX: Match Python CHUNK = 3200 exactly
    // Python: CHUNK = 3200 samples at 24kHz → 6400 bytes PCM16 → ~8533 bytes base64 (WORKS)
    // JS: Must use 4096 (nearest power of 2 ≥ 3200) but truncate to 3200 samples
    // This ensures exact compatibility with Python working implementation
    this.audioScriptProcessor = this.audioContext.createScriptProcessor(4096, 1, 1);

    // Add rate limiting to match Python implementation exactly
    this.lastAudioSendTime = Date.now();

    // Process audio data and convert to PCM16
    this.audioScriptProcessor.onaudioprocess = (event) => {
      if (!this.isAudioStreaming || !this.onAudioData) return;

      const now = Date.now();
      const timeSinceLastSend = now - this.lastAudioSendTime;

      // CRITICAL: Rate limit exactly like Python - 200ms intervals
      // Python: await asyncio.sleep(0.2) between chunks
      if (timeSinceLastSend < this.minAudioIntervalMs) {
        return; // Skip chunk to maintain exact rate limiting
      }

      try {
        const inputBuffer = event.inputBuffer;
        const inputData = inputBuffer.getChannelData(0); // Get mono channel

        // CRITICAL: Truncate to exactly 3200 samples (Python CHUNK size)
        // ScriptProcessor gives us 4096 samples but we need exactly 3200 like Python
        const targetSamples = 3200; // Matches Python CHUNK exactly
        const samplesToUse = Math.min(inputData.length, targetSamples);
        
        // Convert Float32Array to Int16 (PCM16 format) - exactly 3200 samples
        const pcm16Buffer = new Int16Array(samplesToUse);
        for (let i = 0; i < samplesToUse; i++) {
          // Clamp and convert float32 [-1, 1] to int16 [-32768, 32767]
          const clampedValue = Math.max(-1, Math.min(1, inputData[i]));
          pcm16Buffer[i] = Math.round(clampedValue * 0x7FFF);
        }

        // Create ArrayBuffer from PCM16 data
        const arrayBuffer = pcm16Buffer.buffer.slice(
          pcm16Buffer.byteOffset,
          pcm16Buffer.byteOffset + pcm16Buffer.byteLength
        );

        this.lastAudioSendTime = now;
        this.onAudioData(arrayBuffer);

      } catch (error) {
        console.error('[MediaCaptureManager] Error processing PCM16 audio:', error);
      }
    };

    // Connect audio pipeline
    this.audioSource.connect(this.audioScriptProcessor);
    this.audioScriptProcessor.connect(this.audioContext.destination);

    console.log('[MediaCaptureManager] ScriptProcessor PCM16 streaming pipeline established');
  }



  /**
   * NEW: Convert Float32Array audio to PCM16 format for Aliyun Qwen-Omni
   * @param float32Data Input audio data in Float32 format
   * @returns PCM16 data as Int16Array
   */
  private _convertFloat32ToPCM16(float32Data: Float32Array): Int16Array {
    const length = float32Data.length;
    const pcm16 = new Int16Array(length);

    for (let i = 0; i < length; i++) {
      // Clamp to [-1, 1] range
      let sample = Math.max(-1, Math.min(1, float32Data[i]));

      // Convert to 16-bit PCM
      pcm16[i] = sample < 0 ? sample * 0x8000 : sample * 0x7FFF;
    }

    return pcm16;
  }

  /**
   * Setup client-side Voice Activity Detection using @ricky0123/vad-web
   * Only used when vadMode === 'client'
   */
  private async _setupClientVAD(): Promise<void> {
    if (!this.mediaStream) {
      console.error('[MediaCaptureManager] No media stream available for client VAD');
      return;
    }

    if (this.vadInstance) {
      console.log('[MediaCaptureManager] Client VAD already set up');
      return;
    }

    try {
      if (!this.audioContext) {
        this.audioContext = new AudioContext();
      }

      console.log('[MediaCaptureManager] Setting up client VAD with callbacks');

      // Setup client VAD using vadUtils
      this.vadInstance = await setupClientVAD({
        onSpeechStart: () => {
          console.log('[MediaCaptureManager] Client VAD: Speech started');
          this.onSpeechStart?.();
        },
        onSpeechEnd: (audio: Float32Array) => {
          console.log('[MediaCaptureManager] Client VAD: Speech ended');
          this.onSpeechEnd?.(audio);
        },
      });

      console.log('[MediaCaptureManager] Starting client VAD instance');
      this.vadInstance.start();
      console.log('[MediaCaptureManager] Client VAD setup complete and started');

    } catch (error) {
      console.error('[MediaCaptureManager] Error setting up client VAD:', error);
    }
  }

  /**
   * Stop capturing media
   */
  stopCapture(): void {
    if (!this.isCapturing) {
      console.log('[MediaCaptureManager] Not capturing, skipping stopCapture');
      return;
    }

    console.log('[MediaCaptureManager] Stopping capture...');

    try {
      // Stop audio streaming first
      this._stopAudioStreaming();

      // Stop frame capture if active
      this._stopFrameCapture();

      // Stop VAD if active
      this._stopVAD();

      this.isCapturing = false;
      this.options.onCaptureStop?.();

      console.log('[MediaCaptureManager] Capture stopped successfully');
    } catch (error) {
      console.error('[MediaCaptureManager] Error stopping capture:', error);
      this.options.onCaptureError?.(error instanceof Error ? error : new Error(String(error)));
    }
  }

  /**
   * NEW: Stop audio streaming for server VAD mode
   */
  private _stopAudioStreaming(): void {
    console.log('[MediaCaptureManager] Stopping audio streaming...');

    this.isAudioStreaming = false;

    // No AudioWorklet to clean up (using ScriptProcessor instead)

    // Clean up ScriptProcessor
    if (this.audioScriptProcessor) {
      try {
        this.audioScriptProcessor.onaudioprocess = null;
        this.audioScriptProcessor.disconnect();
        this.audioScriptProcessor = null;
        console.log('[MediaCaptureManager] ScriptProcessor cleaned up');
      } catch (error) {
        console.warn('[MediaCaptureManager] Error cleaning up ScriptProcessor:', error);
      }
    }

    // Clean up audio source
    if (this.audioSource) {
      try {
        this.audioSource.disconnect();
        this.audioSource = null;
        console.log('[MediaCaptureManager] Audio source cleaned up');
      } catch (error) {
        console.warn('[MediaCaptureManager] Error cleaning up audio source:', error);
      }
    }

    console.log('[MediaCaptureManager] Audio streaming stopped');
  }

  /**
   * Stop VAD (both client and server modes)
   */
  private _stopVAD(): void {
    console.log('[MediaCaptureManager] Stopping VAD...');

    // Stop client VAD instance if exists
    if (this.vadInstance) {
      console.log('[MediaCaptureManager] Stopping client VAD instance');
      try {
        this.vadInstance.stop();
        this.vadInstance = null;
        console.log('[MediaCaptureManager] Client VAD instance stopped');
      } catch (error) {
        console.warn('[MediaCaptureManager] Error stopping client VAD instance:', error);
      }
    }

    // Clean up legacy VAD components
    this._teardownLegacyVAD();
  }

  /**
   * Public method to stop VAD (for external access)
   */
  public async stopVAD(): Promise<void> {
    console.log('[MediaCaptureManager] Public stopVAD called');
    this._stopVAD();
  }

  /**
   * Get the audio track from the media stream
   * @returns Audio track or null if not available
   */
  getAudioTrack(): MediaStreamTrack | null {
    if (!this.mediaStream) return null;

    const audioTracks = this.mediaStream.getAudioTracks();
    return audioTracks.length > 0 ? audioTracks[0] : null;
  }

  /**
   * Get the video track from the media stream
   * @returns Video track or null if not available
   */
  getVideoTrack(): MediaStreamTrack | null {
    if (!this.mediaStream) return null;

    const videoTracks = this.mediaStream.getVideoTracks();
    return videoTracks.length > 0 ? videoTracks[0] : null;
  }

  /**
   * Get the media stream
   * @returns Media stream or null if not available
   */
  getMediaStream(): MediaStream | null {
    return this.mediaStream;
  }

  /**
   * NEW: Check if audio streaming is active
   * @returns Boolean indicating if PCM16 audio is being streamed
   */
  isAudioStreamingActive(): boolean {
    return this.isAudioStreaming && !!this.mediaStream && this.mediaStream.getAudioTracks().length > 0;
  }

  /**
   * Get the video element
   * @returns Video element or null if not available
   */
  getVideoElement(): HTMLVideoElement | null {
    return this.videoElement;
  }

  /**
   * Create a video preview element
   * @param container Container to append the preview to
   * @returns Video element or null if media stream is not available
   */
  createVideoPreview(container: HTMLElement): HTMLVideoElement | null {
    if (!this.mediaStream) {
      console.error('[MediaCaptureManager] Cannot create preview: no media stream');
      return null;
    }

    // Create video element to display local preview
    const videoPreview = document.createElement('video');
    videoPreview.srcObject = this.mediaStream;
    videoPreview.autoplay = true;
    videoPreview.muted = true; // Mute to prevent feedback
    videoPreview.style.width = '100px';
    videoPreview.style.height = '100px';
    videoPreview.style.position = 'absolute';
    videoPreview.style.bottom = '10px';
    videoPreview.style.right = '10px';
    videoPreview.style.borderRadius = '50%';
    videoPreview.style.objectFit = 'cover';
    videoPreview.style.border = '2px solid #fff';
    videoPreview.style.boxShadow = '0 2px 10px rgba(0,0,0,0.2)';

    if (container) {
      container.appendChild(videoPreview);
    }

    return videoPreview;
  }

  /**
   * Capture a single frame from the video element
   * @returns Promise that resolves to a data URL of the captured frame
   */
  async captureFrame(): Promise<string | null> {
    if (!this.videoElement || !this.canvasElement || !this.canvasContext ||
      !this.videoElement.videoWidth || this.videoElement.readyState < this.videoElement.HAVE_CURRENT_DATA) {
      console.warn('[MediaCaptureManager] Video not ready for capture');
      return null;
    }

    try {
      // Draw the current video frame to the canvas
      this.canvasContext.drawImage(
        this.videoElement,
        0, 0,
        this.canvasElement.width,
        this.canvasElement.height
      );

      // Extract the frame as a data URL with the specified quality
      const frameDataUrl = this.canvasElement.toDataURL('image/jpeg', this.frameQuality);

      return frameDataUrl;
    } catch (error) {
      console.error('[MediaCaptureManager] Error capturing frame:', error);
      return null;
    }
  }

  /**
   * Configure Voice Activity Detection (VAD)
   * @param enabled Whether to enable VAD
   * @param options VAD options
   */
  configureVAD(enabled: boolean, options?: VADOptions): void {
    this.vadEnabled = enabled;

    console.log('[MediaCaptureManager] VAD configured:', {
      enabled,
      options
    });

    // If we're already capturing audio, set up VAD
    if (this.isCapturing && (this.currentMediaType === 'audio' || this.currentMediaType === 'audio-video')) {
      if (enabled) {
        this._setupVAD();
      } else {
        this._teardownVAD();
      }
    }
  }

  /**
   * Get the current VAD status
   * @returns Whether VAD is enabled
   */
  isVADEnabled(): boolean {
    return this.vadEnabled;
  }

  /**
   * Clean up resources
   */
  dispose(): void {
    try {
      console.log('[MediaCaptureManager] Disposing resources');

      // Stop capturing if active
      this.stopCapture();

      // Clean up audio processing
      this._teardownVAD();

      // Stop all tracks
      if (this.mediaStream) {
        this.mediaStream.getTracks().forEach(track => {
          try {
            track.stop();
          } catch (error) {
            console.warn('[MediaCaptureManager] Error stopping track:', error);
          }
        });
        this.mediaStream = null;
      }

      // Clean up video element
      if (this.videoElement) {
        try {
          // Stop video playback
          this.videoElement.pause();
          this.videoElement.srcObject = null;

          // Remove from DOM
          if (this.videoElement.parentNode) {
            this.videoElement.parentNode.removeChild(this.videoElement);
          }
        } catch (error) {
          console.warn('[MediaCaptureManager] Error cleaning up video element:', error);
        } finally {
          this.videoElement = null;
        }
      }

      // Clean up canvas element
      if (this.canvasElement) {
        try {
          if (this.canvasElement.parentNode) {
            this.canvasElement.parentNode.removeChild(this.canvasElement);
          }
        } catch (error) {
          console.warn('[MediaCaptureManager] Error cleaning up canvas element:', error);
        } finally {
          this.canvasElement = null;
          this.canvasContext = null;
        }
      }

      // Clean up audio context
      if (this.audioContext) {
        try {
          this.audioContext.close();
        } catch (error) {
          console.warn('[MediaCaptureManager] Error closing audio context:', error);
        } finally {
          this.audioContext = null;
          this.audioAnalyser = null;
          this.audioSource = null;
          this.audioScriptProcessor = null;
        }
      }

      console.log('[MediaCaptureManager] Resources disposed successfully');
    } catch (error) {
      console.error('[MediaCaptureManager] Error during disposal:', error);
    }
  }

  /**
   * Set up video frame extraction using canvas
   * @private
   */
  private _setupVideoFrameExtraction(): Promise<void> {
    return new Promise((resolve) => {
      // Create video element if it doesn't exist
      if (!this.videoElement) {
        this.videoElement = document.createElement('video');
        this.videoElement.autoplay = true;
        this.videoElement.playsInline = true;
        this.videoElement.muted = true; // Mute to avoid feedback
        this.videoElement.style.display = 'none'; // Hide the video element
        document.body.appendChild(this.videoElement);
      }

      // Create canvas element if it doesn't exist
      if (!this.canvasElement) {
        this.canvasElement = document.createElement('canvas');
        this.canvasElement.style.display = 'none'; // Hide the canvas element
        document.body.appendChild(this.canvasElement);
        this.canvasContext = this.canvasElement.getContext('2d', { willReadFrequently: true });
      }

      // Connect the media stream to the video element
      if (this.mediaStream) {
        this.videoElement.srcObject = this.mediaStream;
      }

      // Wait for metadata to load to get correct dimensions
      this.videoElement.onloadedmetadata = () => {
        // Set canvas dimensions
        if (this.canvasElement && this.videoElement) {
          this.canvasElement.width = this.videoElement.videoWidth;
          this.canvasElement.height = this.videoElement.videoHeight;
          console.log(`[MediaCaptureManager] Video dimensions: ${this.videoElement.videoWidth}x${this.videoElement.videoHeight}`);
        }

        // Start playing the video
        if (this.videoElement) {
          this.videoElement.play().then(() => {
            console.log('[MediaCaptureManager] Video playback started');
            resolve();
          }).catch(error => {
            console.error('[MediaCaptureManager] Error starting video playback:', error);
            resolve(); // Resolve anyway to continue
          });
        } else {
          resolve();
        }
      };

      // Handle errors
      if (this.videoElement) {
        this.videoElement.onerror = (error) => {
          console.error('[MediaCaptureManager] Video element error:', error);
          resolve(); // Resolve anyway to continue
        };
      } else {
        resolve();
      }
    });
  }

  /**
   * Start capturing frames from the video element
   * @private
   */
  private _startFrameCapture(): void {
    // Reset frame collection
    this.frameCount = 0;
    this.lastCaptureTime = 0;

    // Cancel any existing animation frame
    if (this.animationFrameId !== null) {
      cancelAnimationFrame(this.animationFrameId);
      this.animationFrameId = null;
    }

    // Start the capture loop
    this._captureFrameLoop();
  }

  /**
   * Frame capture loop using requestAnimationFrame
   * @private
   */
  private _captureFrameLoop(): void {
    this.animationFrameId = requestAnimationFrame((timestamp) => {
      // Check if it's time to capture a frame
      if (!this.lastCaptureTime || (timestamp - this.lastCaptureTime) >= this.captureRateMs) {
        this.lastCaptureTime = timestamp;
        this._captureFrameAndNotify();

        // If we've captured enough frames, stop
        if (this.frameCount >= this.maxFrames) {
          this._stopFrameCapture();
          return; // Exit the loop
        }
      }

      // Continue the loop
      this._captureFrameLoop();
    });
  }

  /**
   * Stop capturing frames
   * @private
   */
  private _stopFrameCapture(): void {
    // Cancel the animation frame if it exists
    if (this.animationFrameId !== null) {
      cancelAnimationFrame(this.animationFrameId);
      this.animationFrameId = null;
    }
  }

  /**
   * Capture a frame and notify via callback
   * @private
   */
  private async _captureFrameAndNotify(): Promise<void> {
    const frameDataUrl = await this.captureFrame();

    if (frameDataUrl) {
      this.frameCount++;

      // Call the frame captured callback
      this.options.onFrame?.({
        frameNumber: this.frameCount,
        totalFrames: this.maxFrames,
        dataUrl: frameDataUrl
      });

      console.log(`[MediaCaptureManager] Captured frame ${this.frameCount}/${this.maxFrames}`);
    }
  }

  /**
   * @deprecated Legacy VAD setup - use _setupClientVAD instead
   */
  private _setupVAD(): void {
    console.warn('[MediaCaptureManager] _setupVAD is deprecated. Use _setupClientVAD instead.');
    // Legacy method kept for backward compatibility
  }

  /**
   * @deprecated Legacy ScriptProcessorNode VAD - use _setupClientVAD instead
   */
  private _setupVADScriptProcessor(): void {
    console.warn('[MediaCaptureManager] _setupVADScriptProcessor is deprecated. Use _setupClientVAD instead.');
    // Legacy method kept for backward compatibility
  }

  /**
   * @deprecated Legacy amplitude calculation - handled by modern VAD library
   */
  private _calculateAmplitude(inputData: Float32Array): number {
    let sum = 0;
    for (let i = 0; i < inputData.length; i++) {
      sum += Math.abs(inputData[i]);
    }
    return sum / inputData.length;
  }

  /**
   * @deprecated Use _teardownLegacyVAD instead
   * Kept for backward compatibility
   */
  private _teardownVAD(): void {
    this._teardownLegacyVAD();
  }

  /**
   * Clean up legacy VAD components (ScriptProcessorNode, AnalyserNode)
   * @private
   */
  private _teardownLegacyVAD(): void {
    console.log('[MediaCaptureManager] Cleaning up legacy VAD components...');
    if (this.audioScriptProcessor) {
      this.audioScriptProcessor.onaudioprocess = null;
      this.audioScriptProcessor.disconnect();
      this.audioScriptProcessor = null;
      console.log('[MediaCaptureManager] audioScriptProcessor cleaned up');
    }
    if (this.audioAnalyser) {
      this.audioAnalyser.disconnect();
      this.audioAnalyser = null;
      console.log('[MediaCaptureManager] audioAnalyser disconnected and nulled');
    }
    if (this.audioSource) {
      this.audioSource.disconnect();
      this.audioSource = null;
      console.log('[MediaCaptureManager] audioSource disconnected and nulled');
    }
    console.log('[MediaCaptureManager] Legacy VAD components cleaned up');
  }

  /**
   * @deprecated Use startCapture() instead
   * Kept for backward compatibility
   */
  async initialize(mediaType: MediaType = 'audio-video'): Promise<boolean> {
    console.warn('[MediaCaptureManager] initialize() is deprecated. Use startCapture() instead.');
    return this._initializeMediaDevices(mediaType);
  }
}
