/**
 * Renderer setup for 3D scenes
 * Provides functions for setting up WebGL renderer with various configurations
 */

import * as THREE from 'three';
import { VRButton } from 'three/examples/jsm/webxr/VRButton.js';

/**
 * Renderer configuration interface
 */
export interface RendererConfig {
  antialias?: boolean;
  alpha?: boolean;
  preserveDrawingBuffer?: boolean;
  powerPreference?: 'high-performance' | 'low-power' | 'default';
  precision?: 'highp' | 'mediump' | 'lowp';
  logarithmicDepthBuffer?: boolean;
  toneMapping?: THREE.ToneMapping;
  toneMappingExposure?: number;
  outputColorSpace?: THREE.ColorSpace;
  physicallyCorrectLights?: boolean;
  shadowMap?: {
    enabled?: boolean;
    type?: THREE.ShadowMapType;
    autoUpdate?: boolean;
  };
  xr?: {
    enabled?: boolean;
    addVRButton?: boolean;
  };
  pixelRatio?: number;
}

/**
 * Default renderer configuration
 */
export const DEFAULT_RENDERER_CONFIG: RendererConfig = {
  antialias: true,
  alpha: true,
  preserveDrawingBuffer: true,
  powerPreference: 'high-performance',
  toneMapping: THREE.ACESFilmicToneMapping,
  outputColorSpace: THREE.SRGBColorSpace,
  shadowMap: {
    enabled: true,
    type: THREE.PCFSoftShadowMap,
    autoUpdate: true
  },
  physicallyCorrectLights: true, // Enable physically correct lighting
  xr: {
    enabled: true,
    addVRButton: false
  },
  pixelRatio: window.devicePixelRatio
};

/**
 * Class to manage WebGL renderer
 */
export class SceneRenderer {
  private container: HTMLElement;

  renderer: THREE.WebGLRenderer;
  vrButton: HTMLElement | null = null;

  /**
   * Create a new SceneRenderer instance
   * @param container - The DOM element to append the renderer to
   * @param config - The renderer configuration
   */
  constructor(container: HTMLElement, config: RendererConfig = DEFAULT_RENDERER_CONFIG) {
    this.container = container;

    // Create renderer
    this.renderer = new THREE.WebGLRenderer({
      antialias: config.antialias,
      alpha: config.alpha,
      preserveDrawingBuffer: config.preserveDrawingBuffer,
      powerPreference: config.powerPreference,
      precision: config.precision,
      logarithmicDepthBuffer: config.logarithmicDepthBuffer
    });

    // Configure renderer
    this.setupRenderer(config);

    // Append to container
    this.container.appendChild(this.renderer.domElement);
  }

  /**
   * Set up the renderer based on the provided configuration
   * @param config - The renderer configuration
   */
  setupRenderer(config: RendererConfig = DEFAULT_RENDERER_CONFIG): void {
    // Set pixel ratio
    this.renderer.setPixelRatio(config.pixelRatio || window.devicePixelRatio);

    // Set size to container size
    this.updateSize();

    // Configure tone mapping
    if (config.toneMapping !== undefined) {
      this.renderer.toneMapping = config.toneMapping;
    }

    if (config.toneMappingExposure !== undefined) {
      this.renderer.toneMappingExposure = config.toneMappingExposure;
    }

    // Configure color space
    if (config.outputColorSpace !== undefined) {
      this.renderer.outputColorSpace = config.outputColorSpace;
    }

    // Configure physically correct lights
    if (config.physicallyCorrectLights !== undefined) {
      this.renderer.physicallyCorrectLights = config.physicallyCorrectLights;
    }

    // Configure shadow map
    if (config.shadowMap) {
      this.renderer.shadowMap.enabled = config.shadowMap.enabled !== undefined ? config.shadowMap.enabled : true;

      if (config.shadowMap.type !== undefined) {
        this.renderer.shadowMap.type = config.shadowMap.type;
      }

      if (config.shadowMap.autoUpdate !== undefined) {
        this.renderer.shadowMap.autoUpdate = config.shadowMap.autoUpdate;
      }
    }

    // Configure XR
    if (config.xr) {
      this.renderer.xr.enabled = config.xr.enabled !== undefined ? config.xr.enabled : true;

      // Add VR button if enabled
      if (config.xr.addVRButton) {
        this.setupVRButton();
      }
    }
  }

  /**
   * Set up VR button
   */
  setupVRButton(): void {
    this.vrButton = VRButton.createButton(this.renderer);
    document.body.appendChild(this.vrButton);
  }

  /**
   * Update renderer size to match container size
   */
  updateSize(): void {
    const width = this.container.clientWidth;
    const height = this.container.clientHeight;
    this.renderer.setSize(width, height);
  }

  /**
   * Start the animation loop
   * @param callback - The callback function to call on each frame
   */
  startAnimationLoop(callback: () => void): void {
    this.renderer.setAnimationLoop(callback);
  }

  /**
   * Stop the animation loop
   */
  stopAnimationLoop(): void {
    this.renderer.setAnimationLoop(null);
  }

  /**
   * Render a scene with a camera
   * @param scene - The scene to render
   * @param camera - The camera to use
   */
  render(scene: THREE.Scene, camera: THREE.Camera): void {
    this.renderer.render(scene, camera);
  }

  /**
   * Dispose of the renderer and clean up resources
   */
  dispose(): void {
    // Remove VR button if it exists
    if (this.vrButton && this.vrButton.parentNode) {
      this.vrButton.parentNode.removeChild(this.vrButton);
    }

    // Dispose of renderer
    this.renderer.dispose();

    // Remove renderer from container
    if (this.renderer.domElement.parentNode) {
      this.renderer.domElement.parentNode.removeChild(this.renderer.domElement);
    }
  }
}
