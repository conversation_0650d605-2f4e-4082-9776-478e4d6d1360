/**
 * Lighting setup for 3D scenes
 * Provides functions for setting up different lighting configurations
 */

import * as THREE from 'three';

/**
 * Light configuration interface
 */
export interface LightConfig {
  ambient?: {
    color?: number;
    intensity?: number;
  };
  directional?: {
    color?: number;
    intensity?: number;
    position?: { x: number; y: number; z: number };
    castShadow?: boolean;
    shadowMapSize?: number;
  };
  fill?: {
    color?: number;
    intensity?: number;
    position?: { x: number; y: number; z: number };
  };
  spot?: {
    color?: number;
    intensity?: number;
    position?: { x: number; y: number; z: number };
    target?: { x: number; y: number; z: number };
    angle?: number;
    penumbra?: number;
    decay?: number;
    distance?: number;
    castShadow?: boolean;
  };
}

/**
 * Default lighting configuration
 */
export const DEFAULT_LIGHT_CONFIG: LightConfig = {
  ambient: {
    color: 0xffffff,
    intensity: 0.4 // Increased for better ambient illumination
  },
  directional: {
    color: 0xffffff,
    intensity: 1.2, // Increased for stronger shadows
    position: { x: 10, y: 10, z: 10 }, // Increased distance for better shadow casting
    castShadow: true,
    shadowMapSize: 2048 // Increased for better shadow quality
  },
  fill: {
    color: 0xc9e8ff, // Slightly blue fill light for contrast
    intensity: 0.6, // Increased for better fill
    position: { x: -5, y: 3, z: -5 }
  }
};

/**
 * Class to manage scene lighting
 */
export class SceneLighting {
  private scene: THREE.Scene;
  private mainLight: THREE.DirectionalLight | null = null;
  private fillLight: THREE.DirectionalLight | null = null;
  private ambientLight: THREE.AmbientLight | null = null;
  private spotLight: THREE.SpotLight | null = null;

  /**
   * Create a new SceneLighting instance
   * @param scene - The THREE.Scene to add lights to
   */
  constructor(scene: THREE.Scene) {
    this.scene = scene;
  }

  /**
   * Set up lighting based on the provided configuration
   * @param config - The lighting configuration
   */
  setupLighting(config: LightConfig = DEFAULT_LIGHT_CONFIG): void {
    // Remove any existing lights
    this.cleanupLights();

    // Set up ambient light
    if (config.ambient) {
      this.setupAmbientLight(config.ambient.color, config.ambient.intensity);
    }

    // Set up directional light
    if (config.directional) {
      this.setupDirectionalLight(
        config.directional.color,
        config.directional.intensity,
        config.directional.position,
        config.directional.castShadow,
        config.directional.shadowMapSize
      );
    }

    // Set up fill light
    if (config.fill) {
      this.setupFillLight(
        config.fill.color,
        config.fill.intensity,
        config.fill.position
      );
    }

    // Set up spot light
    if (config.spot) {
      this.setupSpotLight(
        config.spot.color,
        config.spot.intensity,
        config.spot.position,
        config.spot.target,
        config.spot.angle,
        config.spot.penumbra,
        config.spot.decay,
        config.spot.distance,
        config.spot.castShadow
      );
    }
  }

  /**
   * Set up ambient light
   * @param color - The light color (default: white)
   * @param intensity - The light intensity (default: 0.3)
   */
  setupAmbientLight(color: number = 0xffffff, intensity: number = 0.3): void {
    this.ambientLight = new THREE.AmbientLight(color, intensity);
    this.scene.add(this.ambientLight);
  }

  /**
   * Set up directional light
   * @param color - The light color (default: white)
   * @param intensity - The light intensity (default: 1.0)
   * @param position - The light position (default: { x: 5, y: 5, z: 5 })
   * @param castShadow - Whether the light should cast shadows (default: true)
   * @param shadowMapSize - The shadow map size (default: 1024)
   */
  setupDirectionalLight(
    color: number = 0xffffff,
    intensity: number = 1.0,
    position: { x: number; y: number; z: number } = { x: 5, y: 5, z: 5 },
    castShadow: boolean = true,
    shadowMapSize: number = 1024
  ): void {
    this.mainLight = new THREE.DirectionalLight(color, intensity);
    this.mainLight.position.set(position.x, position.y, position.z);

    // Add subtle ambient occlusion effect
    this.mainLight.shadow.normalBias = 0.05;

    // Configure shadows
    if (castShadow) {
      this.mainLight.castShadow = true;
      this.mainLight.shadow.mapSize.width = shadowMapSize;
      this.mainLight.shadow.mapSize.height = shadowMapSize;
      this.mainLight.shadow.camera.near = 0.5;
      this.mainLight.shadow.camera.far = 100; // Increased for better shadow coverage
      this.mainLight.shadow.bias = -0.0005; // Reduced to minimize shadow acne

      // Enable shadow blur for softer shadows
      this.mainLight.shadow.radius = 2; // Blur radius for softer shadows

      // Improve shadow quality
      this.mainLight.shadow.blurSamples = 8; // More samples for better quality

      // Set shadow camera frustum - increased for better coverage
      const shadowCameraSize = 20; // Doubled to cover the entire box
      this.mainLight.shadow.camera.left = -shadowCameraSize;
      this.mainLight.shadow.camera.right = shadowCameraSize;
      this.mainLight.shadow.camera.top = shadowCameraSize;
      this.mainLight.shadow.camera.bottom = -shadowCameraSize;

      // Add a helper to visualize the shadow camera (uncomment for debugging)
      // const helper = new THREE.CameraHelper(this.mainLight.shadow.camera);
      // this.scene.add(helper);
    }

    this.scene.add(this.mainLight);
  }

  /**
   * Set up fill light
   * @param color - The light color (default: white)
   * @param intensity - The light intensity (default: 0.5)
   * @param position - The light position (default: { x: -5, y: 3, z: -5 })
   */
  setupFillLight(
    color: number = 0xffffff,
    intensity: number = 0.5,
    position: { x: number; y: number; z: number } = { x: -5, y: 3, z: -5 }
  ): void {
    this.fillLight = new THREE.DirectionalLight(color, intensity);
    this.fillLight.position.set(position.x, position.y, position.z);
    this.fillLight.castShadow = false;
    this.scene.add(this.fillLight);
  }

  /**
   * Set up spot light
   * @param color - The light color (default: white)
   * @param intensity - The light intensity (default: 1.0)
   * @param position - The light position (default: { x: 0, y: 10, z: 0 })
   * @param target - The light target position (default: { x: 0, y: 0, z: 0 })
   * @param angle - The light cone angle in radians (default: Math.PI/6)
   * @param penumbra - The percentage of the spotlight cone that is attenuated due to penumbra (default: 0.1)
   * @param decay - The amount the light dims along the distance of the light (default: 2)
   * @param distance - Maximum range of the light (default: 0 - no limit)
   * @param castShadow - Whether the light should cast shadows (default: true)
   */
  setupSpotLight(
    color: number = 0xffffff,
    intensity: number = 1.0,
    position: { x: number; y: number; z: number } = { x: 0, y: 10, z: 0 },
    target: { x: number; y: number; z: number } = { x: 0, y: 0, z: 0 },
    angle: number = Math.PI / 6,
    penumbra: number = 0.1,
    decay: number = 2,
    distance: number = 0,
    castShadow: boolean = true
  ): void {
    this.spotLight = new THREE.SpotLight(color, intensity, distance, angle, penumbra, decay);
    this.spotLight.position.set(position.x, position.y, position.z);

    // Set target position
    this.spotLight.target.position.set(target.x, target.y, target.z);
    this.scene.add(this.spotLight.target);

    // Configure shadows
    if (castShadow) {
      this.spotLight.castShadow = true;
      this.spotLight.shadow.mapSize.width = 1024;
      this.spotLight.shadow.mapSize.height = 1024;
      this.spotLight.shadow.camera.near = 0.5;
      this.spotLight.shadow.camera.far = 50;
      this.spotLight.shadow.bias = -0.001;
    }

    this.scene.add(this.spotLight);
  }

  /**
   * Clean up all lights in the scene
   */
  private cleanupLights(): void {
    // Remove any existing lights
    this.scene.children.forEach(child => {
      if (child.isLight) {
        this.scene.remove(child);
      }
    });

    // Reset light references
    this.mainLight = null;
    this.fillLight = null;
    this.ambientLight = null;
    this.spotLight = null;
  }
}
