/**
 * Camera setup for 3D scenes
 * Provides functions for setting up different camera configurations
 */

import * as THREE from 'three';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js';

/**
 * Camera configuration interface
 */
export interface CameraConfig {
  type?: 'perspective' | 'orthographic';
  fov?: number;
  aspect?: number;
  near?: number;
  far?: number;
  position?: { x: number; y: number; z: number };
  lookAt?: { x: number; y: number; z: number };
  enableControls?: boolean;
  controlsConfig?: {
    enableDamping?: boolean;
    dampingFactor?: number;
    enableZoom?: boolean;
    minDistance?: number;
    maxDistance?: number;
    enablePan?: boolean;
    autoRotate?: boolean;
    autoRotateSpeed?: number;
  };
}

/**
 * Default camera configuration
 */
export const DEFAULT_CAMERA_CONFIG: CameraConfig = {
  type: 'perspective',
  fov: 45,
  near: 0.1,
  far: 1000,
  position: { x: 0, y: 0, z: 5 },
  lookAt: { x: 0, y: 0, z: 0 },
  enableControls: true,
  controlsConfig: {
    enableDamping: true,
    dampingFactor: 0.05,
    enableZoom: true,
    minDistance: 1,
    maxDistance: 50,
    enablePan: true,
    autoRotate: false,
    autoRotateSpeed: 1.0
  }
};

/**
 * Class to manage scene camera
 */
export class SceneCamera {
  private scene: THREE.Scene;
  private domElement: HTMLElement;
  private debug: boolean;

  camera: THREE.PerspectiveCamera | THREE.OrthographicCamera;
  controls: OrbitControls | null = null;

  /**
   * Create a new SceneCamera instance
   * @param scene - The THREE.Scene the camera will view
   * @param domElement - The DOM element for controls
   * @param debug - Whether to enable debug logging (default: false)
   */
  constructor(scene: THREE.Scene, domElement: HTMLElement, debug: boolean = false) {
    this.scene = scene;
    this.domElement = domElement;
    this.debug = debug;

    // Create a default perspective camera
    this.camera = new THREE.PerspectiveCamera(
      DEFAULT_CAMERA_CONFIG.fov,
      window.innerWidth / window.innerHeight,
      DEFAULT_CAMERA_CONFIG.near,
      DEFAULT_CAMERA_CONFIG.far
    );
  }

  /**
   * Set up the camera based on the provided configuration
   * @param config - The camera configuration
   */
  setupCamera(config: CameraConfig = DEFAULT_CAMERA_CONFIG): void {
    if (this.debug) {
      console.log("[SceneCamera] Setting up camera with config:", config);
    }

    // Create camera based on type
    if (config.type === 'orthographic') {
      // Create orthographic camera
      const aspect = config.aspect || window.innerWidth / window.innerHeight;
      const frustumSize = 10;
      this.camera = new THREE.OrthographicCamera(
        frustumSize * aspect / -2,
        frustumSize * aspect / 2,
        frustumSize / 2,
        frustumSize / -2,
        config.near || 0.1,
        config.far || 1000
      );
    } else {
      // Create perspective camera
      this.camera = new THREE.PerspectiveCamera(
        config.fov || 45,
        config.aspect || window.innerWidth / window.innerHeight,
        config.near || 0.1,
        config.far || 1000
      );
    }

    // Set camera position
    const position = config.position || { x: 0, y: 0, z: 5 };
    this.camera.position.set(position.x, position.y, position.z);
    if (this.debug) {
      console.log("[SceneCamera] Camera position set to:", this.camera.position.toArray());
    }

    // Set camera look at
    const lookAt = config.lookAt || { x: 0, y: 0, z: 0 };
    this.camera.lookAt(new THREE.Vector3(lookAt.x, lookAt.y, lookAt.z));
    if (this.debug) {
      console.log("[SceneCamera] Camera looking at:", lookAt);
      console.log("[SceneCamera] Camera setup complete. Check if camera is inside environment box in render loop.");
    }

    // Set up controls if enabled
    if (config.enableControls) {
      if (this.debug) {
        console.log("[SceneCamera] Setting up camera controls");
      }
      this.setupControls(config.controlsConfig);
    } else if (this.controls) {
      this.controls.dispose();
      this.controls = null;
    }
  }

  /**
   * Set up orbit controls for the camera
   * @param config - The controls configuration
   */
  setupControls(config = DEFAULT_CAMERA_CONFIG.controlsConfig): void {
    // Dispose of existing controls if any
    if (this.controls) {
      this.controls.dispose();
    }

    // Create new controls
    this.controls = new OrbitControls(this.camera, this.domElement);

    // Apply configuration
    if (config) {
      this.controls.enableDamping = config.enableDamping !== undefined ? config.enableDamping : true;
      this.controls.dampingFactor = config.dampingFactor || 0.05;
      this.controls.enableZoom = config.enableZoom !== undefined ? config.enableZoom : true;
      this.controls.minDistance = config.minDistance || 1;
      this.controls.maxDistance = config.maxDistance || 50;
      this.controls.enablePan = config.enablePan !== undefined ? config.enablePan : true;
      this.controls.autoRotate = config.autoRotate || false;
      this.controls.autoRotateSpeed = config.autoRotateSpeed || 1.0;
    }
  }

  /**
   * Update the camera aspect ratio
   * @param width - The viewport width
   * @param height - The viewport height
   */
  updateAspect(width: number, height: number): void {
    if (this.camera instanceof THREE.PerspectiveCamera) {
      this.camera.aspect = width / height;
      this.camera.updateProjectionMatrix();
    } else if (this.camera instanceof THREE.OrthographicCamera) {
      const frustumSize = 10;
      const aspect = width / height;
      this.camera.left = frustumSize * aspect / -2;
      this.camera.right = frustumSize * aspect / 2;
      this.camera.top = frustumSize / 2;
      this.camera.bottom = frustumSize / -2;
      this.camera.updateProjectionMatrix();
    }
  }

  /**
   * Update controls (should be called in animation loop)
   */
  update(): void {
    if (this.controls && this.controls.enableDamping) {
      this.controls.update();
    }

    // Occasionally log camera position for debugging
    if (this.debug && Math.random() < 0.005) { // Log approximately every 200 frames
      console.log("[SceneCamera] Current camera position:", this.camera.position.toArray());

      // Check if camera is inside any box environment
      const boxEnv = this.scene.getObjectByName('ThinBoxEnvironment');
      if (boxEnv && boxEnv instanceof THREE.Mesh && boxEnv.geometry instanceof THREE.BoxGeometry) {
        const boxPos = boxEnv.position;
        const boxSize = {
          width: boxEnv.geometry.parameters.width,
          height: boxEnv.geometry.parameters.height,
          depth: boxEnv.geometry.parameters.depth
        };

        const isInside =
          Math.abs(this.camera.position.x - boxPos.x) < boxSize.width / 2 &&
          Math.abs(this.camera.position.y - boxPos.y) < boxSize.height / 2 &&
          Math.abs(this.camera.position.z - boxPos.z) < boxSize.depth / 2;

        console.log(`[SceneCamera] Camera inside box: ${isInside}`, {
          boxPosition: boxPos.toArray(),
          boxSize,
          cameraPosition: this.camera.position.toArray(),
          distanceToCenter: this.camera.position.distanceTo(boxPos)
        });
      }
    }
  }
}
