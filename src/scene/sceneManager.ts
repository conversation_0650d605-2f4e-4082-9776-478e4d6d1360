/**
 * Scene Manager
 * Provides a unified interface for managing all scene components
 */

import * as THREE from 'three';
import { SceneEnvironment, EnvironmentConfig } from '@/scene/environment';
import { DEFAULT_ENVIRONMENT_CONFIG } from '@/scene/environment';
import { SceneLighting, LightConfig, DEFAULT_LIGHT_CONFIG } from './lighting';
import { SceneC<PERSON>ra, CameraConfig, DEFAULT_CAMERA_CONFIG } from './camera';
import { SceneRenderer, RendererConfig, DEFAULT_RENDERER_CONFIG } from './renderer';
import { createLogger, setLogLevel, LogLevel } from '@/utils/logger';

/**
 * Scene manager configuration interface
 */
export interface SceneManagerConfig {
  environment?: EnvironmentConfig;
  lighting?: LightConfig;
  camera?: CameraConfig;
  renderer?: RendererConfig;
  debug?: boolean;
}

/**
 * Default scene manager configuration
 */
export const DEFAULT_SCENE_MANAGER_CONFIG: SceneManagerConfig = {
  environment: DEFAULT_ENVIRONMENT_CONFIG,
  lighting: DEFAULT_LIGHT_CONFIG,
  camera: DEFAULT_CAMERA_CONFIG,
  renderer: DEFAULT_RENDERER_CONFIG,
  debug: false
};

/**
 * Class to manage all scene components
 */
export class SceneManager {
  private container: HTMLElement;
  private logger: ReturnType<typeof createLogger>;

  scene: THREE.Scene;
  environment: SceneEnvironment;
  lighting: SceneLighting;
  camera: SceneCamera;
  renderer: SceneRenderer;

  /**
   * Create a new SceneManager instance
   * @param container - The DOM element to append the renderer to
   * @param config - The scene manager configuration
   */
  constructor(container: HTMLElement, config: SceneManagerConfig = DEFAULT_SCENE_MANAGER_CONFIG) {
    this.container = container;
    this.logger = createLogger('SceneManager');

    if (config.debug) {
      setLogLevel(LogLevel.DEBUG);
      this.logger.debug("Debug mode enabled for SceneManager");
    }

    // Create scene
    this.scene = new THREE.Scene();

    // Create renderer
    this.renderer = new SceneRenderer(container, config.renderer);

    // Create camera
    this.camera = new SceneCamera(this.scene, this.renderer.renderer.domElement);
    this.camera.setupCamera(config.camera);

    // Create lighting
    this.lighting = new SceneLighting(this.scene);
    this.lighting.setupLighting(config.lighting);

    // Create environment with debug flag
    this.environment = new SceneEnvironment(this.scene, this.renderer.renderer, config.environment, config.debug);
    this.environment.setupEnvironment(config.environment);

    // Set up resize handler
    this.setupResizeHandler();
  }

  /**
   * Set up resize handler
   */
  private setupResizeHandler(): void {
    window.addEventListener('resize', () => {
      // Update renderer size
      this.renderer.updateSize();

      // Update camera aspect ratio
      const width = this.container.clientWidth;
      const height = this.container.clientHeight;
      this.camera.updateAspect(width, height);
    });
  }

  /**
   * Start the animation loop
   */
  start(): void {
    this.renderer.startAnimationLoop(() => this.update());
  }

  /**
   * Stop the animation loop
   */
  stop(): void {
    this.renderer.stopAnimationLoop();
  }

  /**
   * Update function called on each frame
   */
  update(): void {
    // Update camera controls
    this.camera.update();

    // Render scene
    this.renderer.render(this.scene, this.camera.camera);
  }

  /**
   * Add an object to the scene
   * @param object - The object to add
   */
  addObject(object: THREE.Object3D): void {
    this.scene.add(object);
  }

  /**
   * Remove an object from the scene
   * @param object - The object to remove
   */
  removeObject(object: THREE.Object3D): void {
    this.scene.remove(object);
  }

  /**
   * Dispose of all scene components
   */
  dispose(): void {
    // Stop animation loop
    this.stop();

    // Remove resize handler
    window.removeEventListener('resize', this.setupResizeHandler);

    // Dispose of renderer
    this.renderer.dispose();

    // Clear scene
    while (this.scene.children.length > 0) {
      const object = this.scene.children[0];
      this.scene.remove(object);
    }
  }
}
