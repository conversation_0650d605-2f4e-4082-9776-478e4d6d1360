/**
 * Environment setup for 3D scenes
 * Provides functions for setting up different environment types
 */

import * as THREE from 'three';
import { RoomEnvironment } from 'three/examples/jsm/environments/RoomEnvironment.js';
import { createLogger } from '@/utils/logger';

/**
 * Environment configuration interface
 */
export interface EnvironmentConfig {
    type: 'blank' | 'thinBox' | 'hdr';
    thinBox?: {
        size?: number;
        color?: number;
        setupIBL?: boolean;
        position?: { x: number; y: number; z: number };
        width?: number;
        height?: number;
        depth?: number;
        metalness?: number;
        roughness?: number;
        receiveShadows?: boolean;
        ibl?: {
            sigmaRadians?: number;
            maxSamples?: number;
        };
        scaleFactor?: number; // Multiplier for the calculated size
        minSize?: number; // Minimum size of the box
        maxSize?: number; // Maximum size of the box
        verticalOffsetScale?: number; // Fraction of avatar height to add above head
        horizontalOffsetScale?: number; // Fraction of avatar height to add to each side
        depthOffsetScale?: number; // Fraction of avatar height to add to front/back
        // Enhanced visualization options
        floorColor?: number; // Color for the floor
        wallColor?: number; // Color for the walls
        ceilingColor?: number; // Color for the ceiling
        floorMetalness?: number; // Metalness for the floor
        floorRoughness?: number; // Roughness for the floor
        wallMetalness?: number; // Metalness for the walls
        wallRoughness?: number; // Roughness for the walls
        useGridPattern?: boolean; // Whether to use a grid pattern on the floor
        gridColor?: number; // Color for the grid pattern
        gridSize?: number; // Size of the grid pattern
        fogEnabled?: boolean; // Whether to enable fog
        fogColor?: number; // Color for the fog
        fogDensity?: number; // Density of the fog
    };
    hdr?: {
        path: string;
    };
    backgroundColor?: number;
}

/**
 * Default environment configuration
 */
export const DEFAULT_ENVIRONMENT_CONFIG: EnvironmentConfig = {
    type: 'thinBox',
    thinBox: {
        size: 20,
        color: 0xeeeeee,
        setupIBL: true,
        position: { x: 0, y: 0, z: 0 },
        metalness: 0.1,
        roughness: 0.9,
        receiveShadows: true,
        scaleFactor: 0.5,
        minSize: 10, // Increased minimum size for better visibility
        maxSize: 30,
        width: 10,   // Default width to ensure walls are visible
        height: 10,  // Default height
        depth: 10,   // Default depth
        ibl: {
            sigmaRadians: 0.03,
            maxSamples: 20
        },
        verticalOffsetScale: 0.1, // 10% of avatar height above head
        horizontalOffsetScale: 0.25, // 25% of avatar height to each side
        depthOffsetScale: 0.6, // 60% of avatar height to front/back
        // Enhanced visualization options
        floorColor: 0x303030, // Dark gray floor
        wallColor: 0x404040, // Medium gray walls
        ceilingColor: 0x505050, // Light gray ceiling
        floorMetalness: 0.2, // Slightly metallic floor for reflections
        floorRoughness: 0.7, // Moderately rough floor
        wallMetalness: 0.05, // Minimal metalness for walls
        wallRoughness: 0.9, // Rough walls
        useGridPattern: true, // Use grid pattern on floor
        gridColor: 0x555555, // Grid color
        gridSize: 1, // Grid size
        fogEnabled: true, // Enable fog
        fogColor: 0x222222, // Dark fog
        fogDensity: 0.02 // Subtle fog density
    },
    backgroundColor: 0x222222
};


/**
 * Class to manage scene environments
 */
export class SceneEnvironment {
    private scene: THREE.Scene;
    private renderer: THREE.WebGLRenderer;
    private boxEnvironmentMesh: THREE.Object3D | null = null; // Changed from Mesh to Object3D to support Group
    private roomEnvironmentInstance: RoomEnvironment | null = null;
    private config: EnvironmentConfig;
    private boundingBox: THREE.Box3;
    private logger: ReturnType<typeof createLogger>;
    private debug: boolean;
    private gridTexture: THREE.Texture | null = null;

    /**
     * Create a new SceneEnvironment instance
     * @param scene - The THREE.Scene to add the environment to
     * @param renderer - The THREE.WebGLRenderer to use for environment maps
     * @param config - Optional environment configuration to override defaults
     * @param debug - Whether to enable debug logging (default: false)
     */
    constructor(
        scene: THREE.Scene,
        renderer: THREE.WebGLRenderer,
        config: EnvironmentConfig = DEFAULT_ENVIRONMENT_CONFIG,
        debug: boolean = false
    ) {
        this.scene = scene;
        this.renderer = renderer;
        this.config = { ...DEFAULT_ENVIRONMENT_CONFIG, ...config };
        this.boundingBox = new THREE.Box3();
        this.debug = debug;

        // Create logger with appropriate level
        this.logger = createLogger('SceneEnvironment');
        if (this.debug) {
            this.logger.debug("Debug mode enabled for SceneEnvironment");
        }
    }

    /**
     * Create a grid texture for the floor
     * @param gridSize - Size of the grid cells
     * @param gridColor - Color of the grid lines
     * @param backgroundColor - Background color of the grid
     * @returns A texture with a grid pattern
     */
    private createGridTexture(
        gridSize: number = 1,
        gridColor: number = 0x555555,
        backgroundColor: number = 0x303030
    ): THREE.Texture {
        // Create a canvas to draw the grid
        const size = 512; // Texture size
        const lineWidth = 1; // Width of grid lines

        const canvas = document.createElement('canvas');
        canvas.width = size;
        canvas.height = size;

        const context = canvas.getContext('2d');
        if (!context) {
            this.logger.error("Failed to get canvas context for grid texture");
            // Return a default texture if context creation fails
            return new THREE.Texture();
        }

        // Fill background
        context.fillStyle = '#' + backgroundColor.toString(16).padStart(6, '0');
        context.fillRect(0, 0, size, size);

        // Draw grid lines
        context.strokeStyle = '#' + gridColor.toString(16).padStart(6, '0');
        context.lineWidth = lineWidth;

        // Calculate cell size in pixels
        const cellSize = size / gridSize;

        // Draw vertical lines
        for (let i = 0; i <= gridSize; i++) {
            const pos = i * cellSize;
            context.beginPath();
            context.moveTo(pos, 0);
            context.lineTo(pos, size);
            context.stroke();
        }

        // Draw horizontal lines
        for (let i = 0; i <= gridSize; i++) {
            const pos = i * cellSize;
            context.beginPath();
            context.moveTo(0, pos);
            context.lineTo(size, pos);
            context.stroke();
        }

        // Create texture from canvas
        const texture = new THREE.CanvasTexture(canvas);
        texture.wrapS = THREE.RepeatWrapping;
        texture.wrapT = THREE.RepeatWrapping;
        texture.repeat.set(10, 10); // Repeat the texture
        texture.minFilter = THREE.LinearFilter;
        texture.magFilter = THREE.LinearFilter;

        return texture;
    }

    /**
     * Calculate the appropriate size for the environment box based on scene content
     * @returns Object containing width, height, and depth
     */
    private calculateEnvironmentSize(): { width: number; height: number; depth: number } {
        // Reset bounding box
        this.boundingBox.setFromObject(this.scene);

        if (this.debug) {
            this.logger.debug("Scene bounding box:", {
                min: this.boundingBox.min.toArray(),
                max: this.boundingBox.max.toArray()
            });
        }

        // Get the size of the bounding box
        const size = new THREE.Vector3();
        this.boundingBox.getSize(size);

        if (this.debug) {
            this.logger.debug("Raw scene size:", size.toArray());
        }

        // Get configuration values
        const config = this.config.thinBox || {};
        const scaleFactor = config.scaleFactor || 1.5; // Default to 1.5x the mesh size
        const minSize = config.minSize || 5; // Minimum size of 5 units
        const maxSize = config.maxSize || 50; // Maximum size of 50 units
        const verticalOffsetScale = config.verticalOffsetScale ?? 0.1;
        const horizontalOffsetScale = config.horizontalOffsetScale ?? 0.1;
        const depthOffsetScale = config.depthOffsetScale ?? 0.2;

        if (this.debug) {
            this.logger.debug("Size calculation parameters:", {
                scaleFactor,
                minSize,
                maxSize,
                verticalOffsetScale,
                horizontalOffsetScale,
                depthOffsetScale
            });
        }

        // Always try to find avatar first, regardless of bounding box validity
        const avatarInfo = this.findAvatar();

        if (avatarInfo) {
            if (this.debug) {
                this.logger.debug("Using avatar as reference for environment size:", avatarInfo.size);
            }

            // Use avatar dimensions with configurable offsets
            const avatarHeight = avatarInfo.size.y;
            const avatarWidth = avatarInfo.size.x;
            const avatarDepth = avatarInfo.size.z;

            // Set size based on avatar dimensions with appropriate offsets
            size.set(
                scaleFactor*avatarWidth + avatarWidth * horizontalOffsetScale * 2,
                scaleFactor*avatarHeight + avatarHeight * verticalOffsetScale,
                scaleFactor*avatarDepth + avatarDepth * depthOffsetScale * 2
            );
        } else if (!isFinite(size.x) || !isFinite(size.y) || !isFinite(size.z) ||
            size.x <= 0 || size.y <= 0 || size.z <= 0) {
            // Use default values from config if no avatar found and bounding box is invalid
            const defaultWidth = this.config.thinBox?.width || minSize / scaleFactor;
            const defaultHeight = this.config.thinBox?.height || minSize / scaleFactor;
            const defaultDepth = this.config.thinBox?.depth || minSize / scaleFactor;
            size.set(defaultWidth, defaultHeight, defaultDepth);

            if (this.debug) {
                this.logger.debug("Invalid bounding box detected and no avatar found, using default size:", size.toArray());
            }
        }

        // Calculate dimensions with scale factor, adjusted to fit avatar properly
        // For avatars, use a more precise calculation to ensure the box fits well
        let width, height, depth;

        if (avatarInfo) {
            // For avatars, use the actual dimensions without enforcing minimum size
            // This ensures the box fits closely around the avatar
            width = size.x * scaleFactor;
            height = size.y * scaleFactor;
            depth = size.z * scaleFactor;

            // Log the pre-clamped dimensions when we have an avatar
            if (this.debug) {
                this.logger.debug("Pre-clamped avatar-based dimensions:", {
                    width, height, depth,
                    avatarSize: avatarInfo.size.toArray(),
                    scaleFactor
                });
            }
        } else {
            // For non-avatar objects or when no specific object is found,
            // use the minimum size constraints
            width = Math.max(size.x * scaleFactor, minSize);
            height = Math.max(size.y * scaleFactor, minSize);
            depth = Math.max(size.z * scaleFactor, minSize * 0.3);
        }

        // Apply minimum size only if not an avatar (for avatars we want precise fitting)
        if (!avatarInfo) {
            width = Math.max(width, minSize);
            height = Math.max(height, minSize);
            depth = Math.max(depth, minSize * 0.3);
        }

        // Clamp to maximum size
        width = Math.min(width, maxSize);
        height = Math.min(height, maxSize);
        depth = Math.min(depth, maxSize * 0.4);

        if (this.debug) {
            this.logger.debug("Calculated environment dimensions:", {
                width,
                height,
                depth,
                originalSize: size.toArray()
            });
        }

        return { width, height, depth };
    }

    /**
     * Find the avatar or main mesh in the scene and get its information
     * @returns Object with avatar information or null if not found
     */
    private findAvatar(): {
        object: THREE.Object3D,
        size: THREE.Vector3,
        position: THREE.Vector3,
        feetY: number,
        bbox: THREE.Box3
    } | null {
        let avatar: THREE.Object3D | null = null;

        // First, look for objects named 'avatar' or with userData.type containing 'avatar'
        this.scene.traverse((object: THREE.Object3D) => {
            if (object instanceof THREE.Object3D) {
                if (object.name.toLowerCase().includes('avatar') ||
                    (object.userData && object.userData.type &&
                        object.userData.type.toLowerCase().includes('avatar'))) {
                    avatar = object;
                    return; // Exit traverse once found
                }
            }
        });

        // If no avatar found, look for the largest mesh in the scene
        if (!avatar) {
            let largestMesh: THREE.Mesh | null = null;
            let largestVolume = 0;

            this.scene.traverse((object: THREE.Object3D) => {
                if (object instanceof THREE.Mesh && object.geometry) {
                    // Skip environment meshes and helper objects
                    if (object.name === 'ThinBoxEnvironment' ||
                        object.name === 'ThinBoxEnvironmentDebug' ||
                        object.name === 'ThinBoxEnvironmentWireframe' ||
                        object.name === 'AvatarFloorMarker' ||
                        object.name.includes('helper') ||
                        object.name.includes('Helper')) {
                        return;
                    }

                    // Calculate approximate volume
                    const bbox = new THREE.Box3().setFromObject(object);
                    const size = new THREE.Vector3();
                    bbox.getSize(size);

                    // Skip objects with zero or invalid dimensions
                    if (size.x <= 0 || size.y <= 0 || size.z <= 0 ||
                        !isFinite(size.x) || !isFinite(size.y) || !isFinite(size.z)) {
                        return;
                    }

                    const volume = size.x * size.y * size.z;

                    if (volume > largestVolume) {
                        largestVolume = volume;
                        largestMesh = object;
                    }
                }
            });

            avatar = largestMesh;
        }

        // If we found an avatar, extract its information
        if (avatar) {
            // Ensure avatar is treated as Object3D for TypeScript
            const obj3D = avatar as THREE.Object3D;

            const bbox = new THREE.Box3().setFromObject(obj3D);
            const size = new THREE.Vector3();
            bbox.getSize(size);

            // Ensure size is valid (non-zero and finite)
            if (size.x <= 0 || !isFinite(size.x)) size.x = 1;
            if (size.y <= 0 || !isFinite(size.y)) size.y = 2; // Default human-like height
            if (size.z <= 0 || !isFinite(size.z)) size.z = 1;

            const position = new THREE.Vector3();

            // Get world position
            obj3D.getWorldPosition(position);

            // The feet Y position is the bottom of the bounding box
            const feetY = bbox.min.y;

            if (this.debug) {
                this.logger.debug("Avatar info:", {
                    name: obj3D.name,
                    size: size.toArray(),
                    position: position.toArray(),
                    boundingBox: {
                        min: bbox.min.toArray(),
                        max: bbox.max.toArray()
                    },
                    feetY
                });
            }

            return {
                object: obj3D,
                size,
                position,
                feetY,
                bbox
            };
        }

        return null;
    }




    /**
     * Update the environment for a newly loaded mesh
     * This method should be called when a new mesh is loaded to ensure the environment
     * is properly sized and positioned relative to the mesh
     * @param mesh - The newly loaded mesh
     */
    updateEnvironmentForMesh(mesh: THREE.Object3D): void {
        if (!mesh) return;

        if (this.debug) {
            this.logger.debug("Updating environment for newly loaded mesh:", mesh.name);
        }

        // Add the mesh to the scene temporarily if it's not already there
        const wasInScene = this.scene.children.includes(mesh);
        if (!wasInScene) {
            this.scene.add(mesh);
        }

        // Calculate the bounding box and get mesh information
        const bbox = new THREE.Box3().setFromObject(mesh);
        const size = new THREE.Vector3();
        bbox.getSize(size);
        const position = new THREE.Vector3();
        mesh.getWorldPosition(position);
        const feetY = bbox.min.y;

        if (this.debug) {
            this.logger.debug("Mesh dimensions and position:", {
                name: mesh.name,
                size: size.toArray(),
                position: position.toArray(),
                boundingBox: {
                    min: bbox.min.toArray(),
                    max: bbox.max.toArray()
                },
                feetY
            });
        }

        // Use the unified environment size calculation
        const { width, height, depth } = this.calculateEnvironmentSize();

        // Remove the mesh from the scene if it wasn't there originally
        if (!wasInScene) {
            this.scene.remove(mesh);
        }

        // If we already have a box environment, update it
        if (this.config.type === 'thinBox' && this.boxEnvironmentMesh) {
            // For a group-based environment, we need to recreate it with the new dimensions
            // It's simpler to recreate than to update all the individual planes

            // Remove the current environment
            this.cleanupEnvironment();

            // Create a new environment with the updated dimensions
            this.setupThinBoxEnvironment(
                undefined, // size (not used when width/height/depth are provided)
                this.config.thinBox?.color,
                this.config.thinBox?.setupIBL,
                { x: position.x, y: feetY + (height / 2), z: position.z }, // position
                width,
                height,
                depth,
                this.config.thinBox?.metalness,
                this.config.thinBox?.roughness,
                this.config.thinBox?.receiveShadows
            );

            if (this.debug) {
                this.logger.debug("Box positioned at:", {
                    x: position.x,
                    y: feetY + (height / 2),
                    z: position.z,
                    feetY,
                    height,
                    meshPosition: position.toArray()
                });
            }


            if (this.debug) {
                this.createOrUpdateFloorMarker(position, feetY, width, depth);
                this.logger.debug("Environment updated for mesh:", {
                    meshName: mesh.name,
                    dimensions: { width, height, depth },
                    position: this.boxEnvironmentMesh?.position.toArray() || 'null'
                });
            }
        } else {
            // If we don't have a box environment yet, create one with the calculated dimensions
            this.setupThinBoxEnvironment(
                undefined, // size (not used when width/height/depth are provided)
                this.config.thinBox?.color,
                this.config.thinBox?.setupIBL,
                { x: position.x, y: feetY + (height / 2), z: position.z }, // position
                width,
                height,
                depth,
                this.config.thinBox?.metalness,
                this.config.thinBox?.roughness,
                this.config.thinBox?.receiveShadows
            );
        }
    }

    /**
     * Create or update the floor marker to visualize the avatar's feet position
     * @param position - The position of the avatar
     * @param feetY - The Y coordinate of the avatar's feet
     * @param boxWidth - The width of the environment box
     * @param boxDepth - The depth of the environment box
     */
    private createOrUpdateFloorMarker(
        position: THREE.Vector3,
        feetY: number,
        boxWidth: number,
        boxDepth: number
    ): void {
        // Look for existing floor marker
        let floorMarker = this.scene.getObjectByName('AvatarFloorMarker');

        // Create a new floor marker if it doesn't exist
        if (!floorMarker) {
            floorMarker = new THREE.Mesh(
                new THREE.PlaneGeometry(boxWidth * 0.5, boxDepth * 0.5),
                new THREE.MeshBasicMaterial({
                    color: 0x00ff00,
                    side: THREE.DoubleSide,
                    transparent: true,
                    opacity: 0.3
                })
            );
            floorMarker.rotation.x = Math.PI / 2; // Rotate to be horizontal
            floorMarker.name = 'AvatarFloorMarker';
            this.scene.add(floorMarker);

            if (this.debug) {
                this.logger.debug("Created floor marker");
            }
        } else if (floorMarker instanceof THREE.Mesh) {
            // Update the geometry of the existing floor marker
            floorMarker.geometry.dispose();
            floorMarker.geometry = new THREE.PlaneGeometry(boxWidth * 0.5, boxDepth * 0.5);
        }

        // Position the floor marker
        if (floorMarker instanceof THREE.Object3D) {
            floorMarker.position.set(
                position.x,
                feetY + 0.01, // Slightly above the feet
                position.z
            );
        }
    }

    /**
     * Get the current environment configuration
     * @returns The current environment configuration
     */
    getConfig(): EnvironmentConfig {
        return this.config;
    }

    /**
     * Set up the environment based on the provided configuration
     * @param config - The environment configuration
     */
    setupEnvironment(config: EnvironmentConfig = DEFAULT_ENVIRONMENT_CONFIG): void {
        this.config = config;
        switch (config.type) {
            case 'thinBox':
                this.setupThinBoxEnvironment(
                    config.thinBox?.size,
                    config.thinBox?.color,
                    config.thinBox?.setupIBL,
                    config.thinBox?.position,
                    config.thinBox?.width,
                    config.thinBox?.height,
                    config.thinBox?.depth,
                    config.thinBox?.metalness,
                    config.thinBox?.roughness,
                    config.thinBox?.receiveShadows
                );
                break;
            case 'blank':
                this.setupBlankEnvironment(config.backgroundColor);
                break;
            case 'hdr':
                // HDR environment setup would be implemented here
                this.logger.warn('HDR environment not implemented yet');
                this.setupBlankEnvironment(config.backgroundColor);
                break;
            default:
                this.setupBlankEnvironment(config.backgroundColor);
        }
    }

    /**
     * Set up a blank environment with optional background color
     * @param backgroundColor - The background color (default: black)
     */
    setupBlankEnvironment(backgroundColor: number = 0x000000): void {
        // Clean up existing environment
        this.cleanupEnvironment();

        // Set background color
        this.scene.background = new THREE.Color(backgroundColor);

        // Set up IBL
        this.setupImageBasedLighting();
    }

    /**
     * Set up a thin box environment that can receive shadows
     * @param size - The size of the box (default: 20)
     * @param color - The color of the box (default: 0xeeeeee)
     * @param setupIBL - Whether to set up image-based lighting (default: true)
     * @param position - Optional position override
     * @param width - Optional width override (default: size)
     * @param height - Optional height override (default: size)
     * @param depth - Optional depth override (default: size * 0.3)
     * @param metalness - Optional metalness value (default: 0.1)
     * @param roughness - Optional roughness value (default: 0.9)
     * @param receiveShadows - Whether the box should receive shadows (default: true)
     */
    setupThinBoxEnvironment(
        size: number = 20,
        color: number = 0xeeeeee,
        setupIBL: boolean = true,
        position?: { x: number; y: number; z: number },
        width?: number,
        height?: number,
        depth?: number,
        metalness: number = 0.1,
        roughness: number = 0.9,
        receiveShadows: boolean = true
    ): void {
        if (this.debug) {
            console.log("[SceneEnvironment] Starting thin box environment setup");
            this.logger.debug("Starting thin box environment setup with params:", {
                size, color, setupIBL, position, width, height, depth, metalness, roughness, receiveShadows
            });
        }

        // Clean up existing environment
        this.cleanupEnvironment();
        if (this.debug) {
            console.log("[SceneEnvironment] Cleaned up existing environment");
        }

        // Calculate dimensions based on scene content if not explicitly provided
        let boxWidth = width;
        let boxHeight = height;
        let boxDepth = depth;

        if (!boxWidth || !boxHeight || !boxDepth) {
            const calculatedSize = this.calculateEnvironmentSize();
            if (this.debug) {
                console.log("[SceneEnvironment] Calculated environment size:", calculatedSize);
                this.logger.debug("Calculated environment size:", calculatedSize);
            }
            boxWidth = boxWidth || calculatedSize.width;
            boxHeight = boxHeight || calculatedSize.height;
            boxDepth = boxDepth || calculatedSize.depth;
        }

        if (this.debug) {
            console.log("[SceneEnvironment] Final box dimensions:", { boxWidth, boxHeight, boxDepth });
            this.logger.debug("Final box dimensions:", { boxWidth, boxHeight, boxDepth });
        }

        let boxPosition = position ? { ...position } : { x: 0, y: 0, z: 0 };

        // Get configuration values for enhanced visualization
        const config = this.config.thinBox || {};
        const floorColor = config.floorColor || 0x303030;
        const wallColor = config.wallColor || 0x404040;
        const ceilingColor = config.ceilingColor || 0x505050;
        const floorMetalness = config.floorMetalness !== undefined ? config.floorMetalness : 0.2;
        const floorRoughness = config.floorRoughness !== undefined ? config.floorRoughness : 0.7;
        const wallMetalness = config.wallMetalness !== undefined ? config.wallMetalness : 0.05;
        const wallRoughness = config.wallRoughness !== undefined ? config.wallRoughness : 0.9;
        const useGridPattern = config.useGridPattern !== undefined ? config.useGridPattern : true;
        const gridColor = config.gridColor || 0x555555;
        const gridSize = config.gridSize || 1;

        // Create grid texture for floor if enabled
        let floorTexture = null;
        if (useGridPattern) {
            floorTexture = this.createGridTexture(gridSize, gridColor, floorColor);
            this.gridTexture = floorTexture; // Store for later use/disposal
        }

        // Create a group to hold all environment parts
        const environmentGroup = new THREE.Group();
        environmentGroup.name = 'ThinBoxEnvironment';

        // Create floor (bottom face)
        const floorGeometry = new THREE.PlaneGeometry(boxWidth, boxDepth);
        const floorMaterial = new THREE.MeshStandardMaterial({
            color: floorColor,
            metalness: floorMetalness,
            roughness: floorRoughness,
            side: THREE.FrontSide,
            map: floorTexture
        });
        const floor = new THREE.Mesh(floorGeometry, floorMaterial);
        floor.rotation.x = -Math.PI / 2; // Rotate to be horizontal
        floor.position.set(0, -boxHeight / 2, 0); // Position at bottom of box
        floor.receiveShadow = receiveShadows;
        floor.name = 'ThinBoxEnvironment_Floor';
        environmentGroup.add(floor);

        // Create ceiling (top face)
        const ceilingGeometry = new THREE.PlaneGeometry(boxWidth, boxDepth);
        const ceilingMaterial = new THREE.MeshStandardMaterial({
            color: ceilingColor,
            metalness: wallMetalness,
            roughness: wallRoughness,
            side: THREE.FrontSide
        });
        const ceiling = new THREE.Mesh(ceilingGeometry, ceilingMaterial);
        ceiling.rotation.x = Math.PI / 2; // Rotate to be horizontal
        ceiling.position.set(0, boxHeight / 2, 0); // Position at top of box
        ceiling.receiveShadow = receiveShadows;
        ceiling.name = 'ThinBoxEnvironment_Ceiling';
        environmentGroup.add(ceiling);

        // Create walls (4 sides)
        const wallMaterial = new THREE.MeshStandardMaterial({
            color: wallColor,
            metalness: wallMetalness,
            roughness: wallRoughness,
            side: THREE.FrontSide
        });

        // Back wall
        const backWallGeometry = new THREE.PlaneGeometry(boxWidth, boxHeight);
        const backWall = new THREE.Mesh(backWallGeometry, wallMaterial.clone());
        backWall.position.set(0, 0, -boxDepth / 2);
        backWall.receiveShadow = receiveShadows;
        backWall.name = 'ThinBoxEnvironment_BackWall';
        environmentGroup.add(backWall);

        // Front wall
        const frontWallGeometry = new THREE.PlaneGeometry(boxWidth, boxHeight);
        const frontWall = new THREE.Mesh(frontWallGeometry, wallMaterial.clone());
        frontWall.position.set(0, 0, boxDepth / 2);
        frontWall.rotation.y = Math.PI; // Rotate to face inside
        frontWall.receiveShadow = receiveShadows;
        frontWall.name = 'ThinBoxEnvironment_FrontWall';
        environmentGroup.add(frontWall);

        // Left wall
        const leftWallGeometry = new THREE.PlaneGeometry(boxDepth, boxHeight);
        const leftWall = new THREE.Mesh(leftWallGeometry, wallMaterial.clone());
        leftWall.position.set(-boxWidth / 2, 0, 0);
        leftWall.rotation.y = Math.PI / 2; // Rotate to face inside
        leftWall.receiveShadow = receiveShadows;
        leftWall.name = 'ThinBoxEnvironment_LeftWall';
        environmentGroup.add(leftWall);

        // Right wall
        const rightWallGeometry = new THREE.PlaneGeometry(boxDepth, boxHeight);
        const rightWall = new THREE.Mesh(rightWallGeometry, wallMaterial.clone());
        rightWall.position.set(boxWidth / 2, 0, 0);
        rightWall.rotation.y = -Math.PI / 2; // Rotate to face inside
        rightWall.receiveShadow = receiveShadows;
        rightWall.name = 'ThinBoxEnvironment_RightWall';
        environmentGroup.add(rightWall);

        // Position the environment group
        environmentGroup.position.set(boxPosition.x, boxPosition.y, boxPosition.z);

        // Store reference to the environment mesh
        this.boxEnvironmentMesh = environmentGroup;

        if (this.debug) {
            this.logger.debug("Box positioned at:", {
                position: boxPosition,
                dimensions: {
                    width: boxWidth,
                    height: boxHeight,
                    depth: boxDepth
                }
            });
        }

        // Add a debug helper to visualize the box (wireframe) only in debug mode
        if (this.debug) {
            // Create a wireframe box to visualize the environment boundaries
            const debugBoxGeometry = new THREE.BoxGeometry(boxWidth, boxHeight, boxDepth);
            const wireframe = new THREE.WireframeGeometry(debugBoxGeometry);
            const line = new THREE.LineSegments(wireframe);
            line.material = new THREE.LineBasicMaterial({ color: 0xff0000 });
            line.position.copy(this.boxEnvironmentMesh.position);
            line.name = 'ThinBoxEnvironmentWireframe';
            this.scene.add(line);
            console.log("[SceneEnvironment] Added wireframe helper for box visualization");

            console.log("[SceneEnvironment] Box environment created and positioned:", {
                name: this.boxEnvironmentMesh.name,
                position: boxPosition
            });
            this.logger.debug("Box environment created and positioned:", {
                name: this.boxEnvironmentMesh.name,
                position: boxPosition,
                children: this.boxEnvironmentMesh.children.length
            });

            // Create a duplicate box with FrontSide rendering for debugging
            const debugMaterial = new THREE.MeshStandardMaterial({
                color: 0xff0000,
                side: THREE.FrontSide,
                wireframe: true,
                transparent: true,
                opacity: 0.3
            });

            const debugBox = new THREE.Mesh(debugBoxGeometry.clone(), debugMaterial);
            debugBox.position.copy(this.boxEnvironmentMesh.position);
            debugBox.name = 'ThinBoxEnvironmentDebug';
            this.scene.add(debugBox);
            console.log("[SceneEnvironment] Added debug box with FrontSide rendering");

            // Add detailed debug information about the environment
            try {
                this.logger.debug("Box environment details:", {
                    visible: this.boxEnvironmentMesh.visible,
                    position: this.boxEnvironmentMesh.position.toArray(),
                    dimensions: {
                        width: boxWidth,
                        height: boxHeight,
                        depth: boxDepth
                    },
                    children: this.boxEnvironmentMesh.children.map((child: THREE.Object3D) => ({
                        name: child.name,
                        type: child.type,
                        visible: child.visible
                    })),
                    inScene: this.scene.children.includes(this.boxEnvironmentMesh),
                    uuid: this.boxEnvironmentMesh.uuid,
                    sceneChildrenCount: this.scene.children.length
                });
            } catch (error) {
                this.logger.error("Error logging box details:", error);
            }
        }

        // Make sure the box is visible and properly set up
        if (this.boxEnvironmentMesh) {
            this.boxEnvironmentMesh.visible = true;

            // Add the box to the scene
            this.scene.add(this.boxEnvironmentMesh);
            if (this.debug) {
                console.log("[SceneEnvironment] Added box to scene");
            }
        }

        // Set scene background to a dark color (but not too dark)
        this.scene.background = new THREE.Color(0x222222);
        if (this.debug) {
            console.log("[SceneEnvironment] Set scene background color");
            this.logger.debug("Scene background color set to:", this.scene.background.getHexString());
        }

        // Add fog for depth perception if enabled
        const fogEnabled = this.config.thinBox?.fogEnabled !== undefined ? this.config.thinBox.fogEnabled : true;
        const fogColor = this.config.thinBox?.fogColor || 0x222222;
        const fogDensity = this.config.thinBox?.fogDensity || 0.02;

        if (fogEnabled) {
            this.scene.fog = new THREE.FogExp2(fogColor, fogDensity);
            if (this.debug) {
                this.logger.debug("Added fog to scene:", {
                    color: fogColor.toString(16),
                    density: fogDensity
                });
            }
        } else {
            this.scene.fog = null;
        }

        // Setup IBL if needed
        if (setupIBL) {
            if (this.debug) {
                console.log("[SceneEnvironment] Setting up IBL...");
                this.logger.debug("Setting up IBL...");
            }
            this.setupImageBasedLighting();
        } else if (this.debug) {
            console.log("[SceneEnvironment] IBL setup skipped");
            this.logger.debug("IBL setup skipped");
        }

        // Log scene hierarchy for debugging
        if (this.debug) {
            this.logger.debug("Scene hierarchy:");
            this.scene.traverse((object: THREE.Object3D) => {
                this.logger.debug(`- ${object.name || 'unnamed'} (${object.type}): visible=${object.visible}, position=[${object.position.x}, ${object.position.y}, ${object.position.z}]`);
            });
        }

        if (this.debug) {
            console.log("[SceneEnvironment] Thin box environment setup complete");
            this.logger.debug("Thin box environment setup complete");
        }
    }

    /**
     * Set up image-based lighting using RoomEnvironment
     */
    private setupImageBasedLighting(): void {
        try {
            if (!this.scene.environment) {
                if (this.debug) {
                    this.logger.debug("Setting up default IBL from RoomEnvironment.");
                }

                // Create PMREM generator
                const pmremGenerator = new THREE.PMREMGenerator(this.renderer);
                pmremGenerator.compileEquirectangularShader();

                // Create RoomEnvironment instance if needed
                if (!this.roomEnvironmentInstance) {
                    if (this.debug) {
                        this.logger.debug("Creating new RoomEnvironment instance");
                    }
                    this.roomEnvironmentInstance = new RoomEnvironment();
                }

                if (this.debug) {
                    this.logger.debug("RoomEnvironment instance created,", this.roomEnvironmentInstance);
                }

                // Get configuration values
                const sigmaRadians = this.config?.thinBox?.ibl?.sigmaRadians || 0.03;
                const maxSamples = this.config?.thinBox?.ibl?.maxSamples || 20;

                if (this.debug) {
                    this.logger.debug("IBL configuration:", {
                        sigmaRadians,
                        maxSamples,
                        rendererInfo: {
                            outputColorSpace: this.renderer.outputColorSpace,
                            toneMapping: this.renderer.toneMapping
                        }
                    });
                }

                // Generate environment map
                const envMap = pmremGenerator.fromScene(this.roomEnvironmentInstance, sigmaRadians).texture;

                // Apply environment map
                this.scene.environment = envMap;

                if (this.debug) {
                    this.logger.debug("IBL environment map created and applied to scene", {
                        envMapSize: envMap.image?.width || 'unknown',
                        envMapType: envMap.type,
                        envMapFormat: envMap.format
                    });
                }

                // Clean up
                pmremGenerator.dispose();
            } else {
                if (this.debug) {
                    this.logger.debug("IBL already provided, not overriding with RoomEnvironment.", {
                        existingEnvMap: {
                            type: this.scene.environment.type,
                            format: this.scene.environment.format
                        }
                    });
                }
            }
        } catch (error) {
            this.logger.error("Failed to setup IBL:", error);
            // Fallback to basic environment
            this.scene.environment = null;
            this.scene.background = new THREE.Color(this.config.backgroundColor || 0x222222);
        }
    }

    /**
     * Clean up the current environment
     */
    private cleanupEnvironment(): void {
        // Clean up main box mesh
        if (this.boxEnvironmentMesh) {
            // If it's a group, dispose of all children's geometries and materials
            if (this.boxEnvironmentMesh instanceof THREE.Group) {
                this.boxEnvironmentMesh.children.forEach((child: THREE.Object3D) => {
                    if (child instanceof THREE.Mesh) {
                        // Dispose of geometry
                        if (child.geometry) {
                            child.geometry.dispose();
                        }

                        // Dispose of material
                        if (child.material) {
                            if (Array.isArray(child.material)) {
                                child.material.forEach((m: THREE.Material) => {
                                    if ((m as THREE.MeshStandardMaterial).map) {
                                        (m as THREE.MeshStandardMaterial).map?.dispose();
                                    }
                                    m.dispose();
                                });
                            } else {
                                const material = child.material as THREE.MeshStandardMaterial;
                                if (material.map) {
                                    material.map.dispose();
                                }
                                material.dispose();
                            }
                        }
                    }
                });
            } else if (this.boxEnvironmentMesh instanceof THREE.Mesh) {
                // Handle the case where it's a single mesh
                if (this.boxEnvironmentMesh.geometry) {
                    this.boxEnvironmentMesh.geometry.dispose();
                }

                if (this.boxEnvironmentMesh.material) {
                    if (Array.isArray(this.boxEnvironmentMesh.material)) {
                        this.boxEnvironmentMesh.material.forEach((m: THREE.Material) => {
                            if ((m as THREE.MeshStandardMaterial).map) {
                                (m as THREE.MeshStandardMaterial).map?.dispose();
                            }
                            m.dispose();
                        });
                    } else {
                        const material = this.boxEnvironmentMesh.material as THREE.MeshStandardMaterial;
                        if (material.map) {
                            material.map.dispose();
                        }
                        material.dispose();
                    }
                }
            }

            // Remove from scene and clear reference
            this.scene.remove(this.boxEnvironmentMesh);
            this.boxEnvironmentMesh = null;
        }

        // Clean up grid texture
        if (this.gridTexture) {
            this.gridTexture.dispose();
            this.gridTexture = null;
        }

        // Clean up debug wireframe
        const wireframe = this.scene.getObjectByName('ThinBoxEnvironmentWireframe');
        if (wireframe) {
            this.scene.remove(wireframe);
            if (wireframe instanceof THREE.LineSegments && wireframe.geometry) {
                wireframe.geometry.dispose();

                // Handle material disposal
                if ((wireframe as THREE.LineSegments).material) {
                    const material = (wireframe as THREE.LineSegments).material;
                    if (Array.isArray(material)) {
                        material.forEach((m: THREE.Material) => m.dispose());
                    } else {
                        material.dispose();
                    }
                }
            }
        }

        // Clean up debug box
        const debugBox = this.scene.getObjectByName('ThinBoxEnvironmentDebug');
        if (debugBox) {
            this.scene.remove(debugBox);
            if (debugBox instanceof THREE.Mesh && debugBox.geometry) {
                debugBox.geometry.dispose();

                // Handle material disposal
                if (debugBox.material) {
                    const material = debugBox.material;
                    if (Array.isArray(material)) {
                        material.forEach((m: THREE.Material) => m.dispose());
                    } else {
                        material.dispose();
                    }
                }
            }
        }

        // Clean up floor marker
        const floorMarker = this.scene.getObjectByName('AvatarFloorMarker');
        if (floorMarker) {
            this.scene.remove(floorMarker);
            if (floorMarker instanceof THREE.Mesh && floorMarker.geometry) {
                floorMarker.geometry.dispose();

                // Handle material disposal
                if (floorMarker.material) {
                    const material = floorMarker.material;
                    if (Array.isArray(material)) {
                        material.forEach((m: THREE.Material) => m.dispose());
                    } else {
                        material.dispose();
                    }
                }
            }
        }

        if (this.debug) {
            this.logger.debug("Environment and debug objects cleaned up");
        }
    }
}
