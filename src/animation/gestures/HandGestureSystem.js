/**
 * HandGestureSystem.js
 * Main hand gesture system that consolidates all gesture functionality
 * This is an optional module that can be enabled/disabled
 */

import * as THREE from 'three';
import { createLogger } from '../../utils/logger.js';
import GestureManager from './GestureManager.js';
import { ANIMATION_TIMING } from './AnimationCore.js';

const logger = createLogger('HandGestureSystem');

export class HandGestureSystem {
    constructor(animator, options = {}) {
        this.animator = animator;
        this.enabled = options.enabled !== undefined ? options.enabled : false; // Default: disabled
        this.options = {
            debug: false,
            visualizeTargets: false,
            speakingProbability: 0.5,
            idleGestureProbability: 0.6,
            gestureFrequency: 8000, // Base frequency in ms
            gestureVariation: 7000,  // Random variation in ms
            ...options
        };

        // Initialize components only if enabled
        this.gestureManager = null;
        this.lastIdleGestureTime = 0;
        this.lastSpeakingGestureTime = 0;

        if (this.enabled) {
            this._initialize();
        }

        if (this.options.debug) {
            logger.debug(`HandGestureSystem ${this.enabled ? 'enabled' : 'disabled'}`);
        }
    }

    /**
     * Initialize the gesture system
     * @private
     */
    _initialize() {
        try {
            this.gestureManager = new GestureManager(this.animator, {
                debug: this.options.debug,
                visualizeTargets: this.options.visualizeTargets
            });

            if (this.options.debug) {
                logger.debug('HandGestureSystem initialized successfully');
            }
        } catch (error) {
            logger.error('Failed to initialize HandGestureSystem:', error);
            this.enabled = false;
        }
    }

    /**
     * Enable the gesture system
     */
    enable() {
        if (!this.enabled) {
            this.enabled = true;
            if (!this.gestureManager) {
                this._initialize();
            }
            if (this.options.debug) {
                logger.debug('HandGestureSystem enabled');
            }
        }
    }

    /**
     * Disable the gesture system
     */
    disable() {
        this.enabled = false;
        if (this.options.debug) {
            logger.debug('HandGestureSystem disabled');
        }
    }

    /**
     * Check if gesture system is enabled and ready
     */
    isReady() {
        return this.enabled && this.gestureManager !== null;
    }

    /**
     * Add idle hand gestures (called from BaseAnimator)
     * @param {number} now - Current timestamp
     */
    addIdleGestures(now) {
        if (!this.isReady()) return;

        // Initialize timing if needed
        if (!this.lastIdleGestureTime) {
            this.lastIdleGestureTime = now;
        }

        // Check if it's time for a new gesture
        const timeSinceLastGesture = now - this.lastIdleGestureTime;
        const nextGestureTime = this.options.gestureFrequency + Math.random() * this.options.gestureVariation;

        if (timeSinceLastGesture > nextGestureTime) {
            if (Math.random() < this.options.idleGestureProbability) {
                this._addIdleWavingGesture().catch(error => {
                    if (this.options.debug) {
                        logger.warn('Failed to add idle waving gesture:', error);
                    }
                });
                this.lastIdleGestureTime = now;
            }
        }
    }

    /**
     * Add speaking hand gestures
     * @param {number} delay - Delay before gesture starts
     * @param {number} probability - Probability of gesture (0-1)
     */
    async speakWithHands(delay = 0, probability = null) {
        if (!this.isReady()) return;

        const gestureProb = probability !== null ? probability : this.options.speakingProbability;

        if (Math.random() > gestureProb) {
            return;
        }

        try {
            // Select random side and intensity
            const sides = ['left', 'right'];
            const selectedSide = sides[Math.floor(Math.random() * sides.length)];
            const intensity = 0.4 + Math.random() * 0.4; // 0.4 to 0.8

            if (this.options.debug && Math.random() < 0.3) {
                logger.debug(`🗣️ Creating speaking gesture: ${selectedSide} side, intensity: ${intensity.toFixed(2)}`);
            }

            // Create speaking gesture
            const speakingTemplate = await this.gestureManager.createSpeakingGesture(selectedSide, intensity);

            if (speakingTemplate) {
                const anim = this.animator.animFactory(speakingTemplate);
                if (anim) {
                    anim.isSpeakingGesture = true;
                    anim.gestureType = `speaking_${selectedSide}`;
                    anim.isGestureSystem = true; // Mark as gesture system animation

                    // Apply delay if specified
                    if (delay > 0) {
                        setTimeout(() => {
                            this.animator.animQueue.push(anim);
                        }, delay);
                    } else {
                        this.animator.animQueue.push(anim);
                    }

                    if (this.options.debug && Math.random() < 0.2) {
                        logger.debug(`✅ Added speaking gesture: ${selectedSide} side`);
                    }
                }
            }
        } catch (error) {
            if (this.options.debug) {
                logger.warn('Failed to create speaking gesture:', error);
            }
        }
    }

    /**
     * Add idle waving gesture
     * @private
     */
    async _addIdleWavingGesture() {
        if (!this.isReady()) return;

        try {
            // Select random side and wave type
            const sides = ['left', 'right'];
            const waveTypes = ['friendly', 'gentle', 'subtle'];

            const selectedSide = sides[Math.floor(Math.random() * sides.length)];
            const selectedType = waveTypes[Math.floor(Math.random() * waveTypes.length)];

            if (this.options.debug && Math.random() < 0.3) {
                logger.debug(`👋 Creating natural ${selectedType} waving gesture: ${selectedSide} side`);
            }

            // Create gesture using GestureManager
            const wavingTemplate = await this.gestureManager.createWavingGesture(selectedSide, selectedType);

            if (wavingTemplate) {
                const anim = this.animator.animFactory(wavingTemplate);
                if (anim) {
                    anim.isIdleGesture = true;
                    anim.isWaving = true;
                    anim.gestureType = `${selectedType}_${selectedSide}_wave`;
                    anim.returnToNeutral = true; // Flag for faster return
                    anim.isGestureSystem = true; // Mark as gesture system animation
                    this.animator.animQueue.push(anim);

                    if (this.options.debug && Math.random() < 0.2) {
                        logger.debug(`✅ Added natural waving gesture: ${selectedType} ${selectedSide} wave`);
                    }
                }
            }
        } catch (error) {
            if (this.options.debug) {
                logger.warn('Failed to create waving gesture:', error);
            }
        }
    }

    /**
     * Add subtle hand movement (fallback when gestures fail)
     */
    addSubtleHandMovement() {
        if (!this.isReady()) return;

        try {
            // Create a simple return-to-neutral gesture as fallback
            const side = Math.random() < 0.5 ? 'left' : 'right';
            const returnGesture = this.gestureManager.createReturnToNeutralGesture(side);

            if (returnGesture) {
                const anim = this.animator.animFactory(returnGesture);
                if (anim) {
                    anim.isSubtleMovement = true;
                    anim.isGestureSystem = true;
                    this.animator.animQueue.push(anim);
                }
            }
        } catch (error) {
            if (this.options.debug) {
                logger.warn('Failed to create subtle hand movement:', error);
            }
        }
    }

    /**
     * Create enhanced IK targets for speaking gestures
     * @returns {Array} Array of IK target configurations
     */
    createEnhancedIKTargets() {
        if (!this.isReady()) return [];

        // This could be moved to GestureManager in the future
        // For now, return empty array to maintain compatibility
        return [];
    }

    /**
     * Update gesture system options
     * @param {Object} newOptions - New options to merge
     */
    updateOptions(newOptions) {
        this.options = { ...this.options, ...newOptions };

        // Update enabled state if specified
        if (newOptions.enabled !== undefined) {
            if (newOptions.enabled && !this.enabled) {
                this.enable();
            } else if (!newOptions.enabled && this.enabled) {
                this.disable();
            }
        }

        // Update gesture manager options if it exists
        if (this.gestureManager) {
            this.gestureManager.options = {
                ...this.gestureManager.options,
                debug: this.options.debug,
                visualizeTargets: this.options.visualizeTargets
            };
        }
    }

    /**
     * Get current gesture system status
     */
    getStatus() {
        return {
            enabled: this.enabled,
            ready: this.isReady(),
            gestureManagerAvailable: this.gestureManager !== null,
            lastIdleGestureTime: this.lastIdleGestureTime,
            options: { ...this.options }
        };
    }

    /**
     * Dispose of the gesture system
     */
    dispose() {
        if (this.gestureManager) {
            this.gestureManager.dispose();
            this.gestureManager = null;
        }

        this.enabled = false;

        if (this.options.debug) {
            logger.debug('HandGestureSystem disposed');
        }
    }
}

export default HandGestureSystem; 