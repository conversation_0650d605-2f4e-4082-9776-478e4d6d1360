/**
 * GestureConfig.js
 * Configuration and constants for the gesture system
 */

// Default gesture system configuration
export const GESTURE_CONFIG = {
    // Feature flags
    ENABLED_BY_DEFAULT: false, // Gesture system disabled by default

    // Timing settings
    TIMING: {
        IDLE_GESTURE_FREQUENCY: 8000,     // Base frequency for idle gestures (ms)
        IDLE_GESTURE_VARIATION: 7000,     // Random variation (ms)
        SPEAKING_GESTURE_DELAY: 0,        // Default delay for speaking gestures
    },

    // Probability settings
    PROBABILITIES: {
        IDLE_GESTURE: 0.6,                // 60% chance for idle gestures
        SPEAKING_GESTURE: 0.5,            // 50% chance for speaking gestures
        WAVE_TYPES: {
            friendly: 0.4,                // 40% chance for friendly waves
            gentle: 0.4,                  // 40% chance for gentle waves
            subtle: 0.2                   // 20% chance for subtle waves
        }
    },

    // Gesture intensity ranges
    INTENSITY: {
        WAVE: {
            friendly: { min: 0.7, max: 0.9 },
            gentle: { min: 0.5, max: 0.7 },
            subtle: { min: 0.3, max: 0.5 }
        },
        SPEAKING: {
            min: 0.4,
            max: 0.8
        }
    },

    // Animation priorities
    PRIORITIES: {
        SPEAKING_GESTURE: 10,
        WAVING_GESTURE: 4,
        SUBTLE_MOVEMENT: 2,
        RETURN_TO_NEUTRAL: 9
    },

    // Debug settings
    DEBUG: {
        LOG_GESTURE_CREATION: 0.3,        // 30% chance to log gesture creation
        LOG_GESTURE_SUCCESS: 0.2,         // 20% chance to log successful gestures
        LOG_ERRORS: 1.0,                  // Always log errors
        VISUALIZE_TARGETS: false          // Don't visualize targets by default
    }
};

// Gesture type definitions
export const GESTURE_TYPES = {
    WAVE: {
        FRIENDLY: 'friendly',
        GENTLE: 'gentle',
        SUBTLE: 'subtle'
    },
    SPEAKING: {
        CONVERSATIONAL: 'conversational',
        EMPHATIC: 'emphatic',
        DESCRIPTIVE: 'descriptive'
    },
    UTILITY: {
        RETURN_TO_NEUTRAL: 'returnToNeutral',
        SUBTLE_MOVEMENT: 'subtleMovement'
    }
};

// Side definitions
export const GESTURE_SIDES = {
    LEFT: 'left',
    RIGHT: 'right',
    BOTH: 'both'
};

// Validation rules for gesture parameters
export const VALIDATION_RULES = {
    INTENSITY: {
        min: 0.1,
        max: 1.0
    },
    PROBABILITY: {
        min: 0.0,
        max: 1.0
    },
    TIMING: {
        min: 100,    // Minimum 100ms
        max: 30000   // Maximum 30 seconds
    }
};

/**
 * Validate gesture configuration
 * @param {Object} config - Configuration to validate
 * @returns {Object} - Validation result with isValid and errors
 */
export function validateGestureConfig(config) {
    const errors = [];

    // Validate probabilities
    if (config.probabilities) {
        Object.entries(config.probabilities).forEach(([key, value]) => {
            if (typeof value === 'number') {
                if (value < VALIDATION_RULES.PROBABILITY.min || value > VALIDATION_RULES.PROBABILITY.max) {
                    errors.push(`Probability ${key} must be between ${VALIDATION_RULES.PROBABILITY.min} and ${VALIDATION_RULES.PROBABILITY.max}`);
                }
            }
        });
    }

    // Validate timing
    if (config.timing) {
        Object.entries(config.timing).forEach(([key, value]) => {
            if (typeof value === 'number') {
                if (value < VALIDATION_RULES.TIMING.min || value > VALIDATION_RULES.TIMING.max) {
                    errors.push(`Timing ${key} must be between ${VALIDATION_RULES.TIMING.min} and ${VALIDATION_RULES.TIMING.max}`);
                }
            }
        });
    }

    return {
        isValid: errors.length === 0,
        errors
    };
}

/**
 * Create default gesture options
 * @param {Object} overrides - Options to override defaults
 * @returns {Object} - Complete gesture options
 */
export function createGestureOptions(overrides = {}) {
    return {
        enabled: overrides.enabled !== undefined ? overrides.enabled : GESTURE_CONFIG.ENABLED_BY_DEFAULT,
        debug: overrides.debug || false,
        visualizeTargets: overrides.visualizeTargets || GESTURE_CONFIG.DEBUG.VISUALIZE_TARGETS,

        // Probabilities
        speakingProbability: overrides.speakingProbability || GESTURE_CONFIG.PROBABILITIES.SPEAKING_GESTURE,
        idleGestureProbability: overrides.idleGestureProbability || GESTURE_CONFIG.PROBABILITIES.IDLE_GESTURE,

        // Timing
        gestureFrequency: overrides.gestureFrequency || GESTURE_CONFIG.TIMING.IDLE_GESTURE_FREQUENCY,
        gestureVariation: overrides.gestureVariation || GESTURE_CONFIG.TIMING.IDLE_GESTURE_VARIATION,

        // Custom overrides
        ...overrides
    };
}

/**
 * Get gesture intensity for a specific type
 * @param {string} gestureType - Type of gesture
 * @param {string} subType - Subtype (e.g., 'friendly', 'gentle')
 * @returns {number} - Random intensity within appropriate range
 */
export function getGestureIntensity(gestureType, subType = null) {
    if (gestureType === 'wave' && subType && GESTURE_CONFIG.INTENSITY.WAVE[subType]) {
        const range = GESTURE_CONFIG.INTENSITY.WAVE[subType];
        return range.min + Math.random() * (range.max - range.min);
    }

    if (gestureType === 'speaking') {
        const range = GESTURE_CONFIG.INTENSITY.SPEAKING;
        return range.min + Math.random() * (range.max - range.min);
    }

    // Default intensity
    return 0.5 + Math.random() * 0.3; // 0.5 to 0.8
}

/**
 * Select random wave type based on probabilities
 * @returns {string} - Selected wave type
 */
export function selectRandomWaveType() {
    const random = Math.random();
    const probs = GESTURE_CONFIG.PROBABILITIES.WAVE_TYPES;

    if (random < probs.friendly) {
        return GESTURE_TYPES.WAVE.FRIENDLY;
    } else if (random < probs.friendly + probs.gentle) {
        return GESTURE_TYPES.WAVE.GENTLE;
    } else {
        return GESTURE_TYPES.WAVE.SUBTLE;
    }
}

/**
 * Select random side
 * @param {boolean} allowBoth - Whether to allow 'both' as an option
 * @returns {string} - Selected side
 */
export function selectRandomSide(allowBoth = false) {
    const sides = [GESTURE_SIDES.LEFT, GESTURE_SIDES.RIGHT];
    if (allowBoth) {
        sides.push(GESTURE_SIDES.BOTH);
    }

    return sides[Math.floor(Math.random() * sides.length)];
}

export default {
    GESTURE_CONFIG,
    GESTURE_TYPES,
    GESTURE_SIDES,
    VALIDATION_RULES,
    validateGestureConfig,
    createGestureOptions,
    getGestureIntensity,
    selectRandomWaveType,
    selectRandomSide
}; 