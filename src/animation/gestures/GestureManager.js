/**
 * GestureManager.js
 * Manages all gesture creation, IK targeting, and natural movement
 * Addresses the "large amplitude" and "hands held in air" issues
 */

import * as THREE from 'three';
import { createLogger } from '../../utils/logger.js';
import {
    ANIMATION_TIMING,
    calculateNaturalTarget,
    calculateElbowPoleTarget,
    validateNaturalPose
} from './AnimationCore.js';

const logger = createLogger('GestureManager');

export class GestureManager {
    constructor(animator, options = {}) {
        this.animator = animator;
        this.options = {
            debug: false,
            visualizeTargets: false, // For debugging target positions
            ...options
        };

        // Target visualization helpers (for debugging)
        this.targetSpheres = [];
        this.poleTargetSpheres = [];
    }

    /**
     * Create natural waving gesture with proper proportions
     * @param {string} side - 'left' or 'right'
     * @param {string} waveType - 'friendly', 'gentle', 'subtle'
     * @returns {Object} - Gesture template
     */
    async createWavingGesture(side, waveType = 'friendly') {
        const shoulderBone = this.animator.bones[side === 'left' ? 'LeftShoulder' : 'RightShoulder'];
        if (!shoulderBone) {
            logger.warn(`Shoulder bone not found for ${side} side`);
            return null;
        }

        const shoulderPos = new THREE.Vector3();
        shoulderBone.getWorldPosition(shoulderPos);

        // Calculate natural targets based on wave type and avatar proportions
        const intensity = this._getWaveIntensity(waveType);
        const targets = this._createWaveTargets(side, shoulderPos, intensity);

        // Create gesture template with improved timing
        const template = {
            name: `naturalWave_${side}_${waveType}`,
            dt: [
                ANIMATION_TIMING.GESTURE_DURATIONS.WAVE_RAISE,
                ANIMATION_TIMING.GESTURE_DURATIONS.WAVE_HOLD,
                ANIMATION_TIMING.GESTURE_DURATIONS.WAVE_RETURN // Faster return
            ],
            vs: {
                moveto: [
                    { props: {} }, // Raise to wave position
                    { props: {} }, // Hold wave position
                    { props: {} }  // Return to neutral (faster)
                ]
            }
        };

        // Apply IK solutions for each phase
        await this._applyIKToGesturePhases(template, side, targets);

        if (this.options.visualizeTargets) {
            this._visualizeTargets(targets);
        }

        return template;
    }

    /**
     * Create natural speaking gesture
     * @param {string} side - 'left' or 'right'
     * @param {number} intensity - Gesture intensity (0-1)
     * @returns {Object} - Gesture template
     */
    async createSpeakingGesture(side, intensity = 0.6) {
        const shoulderBone = this.animator.bones[side === 'left' ? 'LeftShoulder' : 'RightShoulder'];
        if (!shoulderBone) return null;

        const shoulderPos = new THREE.Vector3();
        shoulderBone.getWorldPosition(shoulderPos);

        // Create natural speaking targets (closer to body, palm inward)
        const target = calculateNaturalTarget(side, shoulderPos, 'speak', intensity);

        const template = {
            name: `naturalSpeak_${side}`,
            dt: [ANIMATION_TIMING.GESTURE_DURATIONS.SPEAKING_TRANSITION],
            vs: {
                moveto: [{ props: {} }]
            }
        };

        // Apply IK solution
        await this._applySingleIKTarget(template, side, target, 0);

        return template;
    }

    /**
     * Create return-to-neutral gesture with faster timing
     * @param {string} side - 'left' or 'right'
     * @returns {Object} - Gesture template
     */
    createReturnToNeutralGesture(side) {
        return {
            name: `returnToNeutral_${side}`,
            dt: [ANIMATION_TIMING.GESTURE_DURATIONS.WAVE_RETURN], // Fast return
            vs: {
                moveto: [{
                    props: {
                        [`${side === 'left' ? 'Left' : 'Right'}Arm.quaternion`]: null,
                        [`${side === 'left' ? 'Left' : 'Right'}ForeArm.quaternion`]: null,
                        [`${side === 'left' ? 'Left' : 'Right'}Hand.quaternion`]: null
                    }
                }]
            },
            returnToNeutral: true, // Flag for faster blend speed
            priority: 9 // High priority to ensure quick execution
        };
    }

    /**
     * Get wave intensity based on type
     * @private
     */
    _getWaveIntensity(waveType) {
        switch (waveType) {
            case 'friendly': return 0.8;
            case 'gentle': return 0.6;
            case 'subtle': return 0.4;
            default: return 0.7;
        }
    }

    /**
     * Create wave targets with natural proportions
     * @private
     */
    _createWaveTargets(side, shoulderPos, intensity) {
        return {
            raise: calculateNaturalTarget(side, shoulderPos, 'wave', intensity * 0.8),
            wave: calculateNaturalTarget(side, shoulderPos, 'wave', intensity),
            neutral: calculateNaturalTarget(side, shoulderPos, 'neutral', 0.3)
        };
    }

    /**
 * Apply IK solutions to all gesture phases
 * @private
 */
    async _applyIKToGesturePhases(template, side, targets) {
        const targetArray = [targets.raise, targets.wave, targets.neutral];

        for (let index = 0; index < targetArray.length; index++) {
            await this._applySingleIKTarget(template, side, targetArray[index], index);
        }
    }

    /**
     * Apply IK solution to a single gesture phase
     * @private
     */
    async _applySingleIKTarget(template, side, target, phaseIndex) {
        try {
            // Get shoulder position for pole target calculation
            const shoulderBone = this.animator.bones[side === 'left' ? 'LeftShoulder' : 'RightShoulder'];
            const shoulderPos = new THREE.Vector3();
            shoulderBone.getWorldPosition(shoulderPos);

            // Create pole target for natural elbow positioning
            const poleTarget = calculateElbowPoleTarget(side, shoulderPos, target);

            // Create IK configuration
            const ikConfig = this._createIKConfig(side);

            // Try FABRIK first, then fallback to CCD
            let ikResult = {};

            if (this.animator.fabrikSolver) {
                try {
                    ikResult = this.animator.fabrikSolver.solve(
                        this.animator.mesh,
                        ikConfig,
                        target,
                        poleTarget
                    );
                } catch (error) {
                    if (this.options.debug) {
                        logger.warn(`FABRIK failed for ${side} gesture, using CCD fallback`);
                    }
                }
            }

            // Fallback to CCD if FABRIK failed
            if (!ikResult || Object.keys(ikResult).length === 0) {
                try {
                    const AnimationUtils = await import('../AnimationUtils.js');
                    ikResult = AnimationUtils.ikSolve(this.animator.mesh, ikConfig, target, true);
                } catch (importError) {
                    if (this.options.debug) {
                        logger.warn('Could not import AnimationUtils for CCD fallback');
                    }
                    ikResult = {};
                }
            }

            // Validate and apply result
            if (this._validateGestureResult(ikResult, side)) {
                Object.assign(template.vs.moveto[phaseIndex].props, ikResult);
            } else {
                // Apply neutral position if validation fails
                this._applyNeutralPosition(template.vs.moveto[phaseIndex].props, side);
            }

        } catch (error) {
            logger.error(`Failed to apply IK for ${side} gesture:`, error);
            this._applyNeutralPosition(template.vs.moveto[phaseIndex].props, side);
        }
    }

    /**
     * Create IK configuration for a side
     * @private
     */
    _createIKConfig(side) {
        const prefix = side === 'left' ? 'Left' : 'Right';

        return {
            iterations: 15,
            root: `${prefix}Shoulder`,
            effector: `${prefix}Hand`,
            links: [
                {
                    link: `${prefix}Hand`,
                    minx: -0.3, maxx: 0.3,
                    miny: -0.4, maxy: 0.6,
                    minz: -0.3, maxz: 0.5 // More conservative Z limits
                },
                {
                    link: `${prefix}ForeArm`,
                    minx: -0.2, maxx: 1.2,
                    miny: -0.6, maxy: 0.6,
                    minz: side === 'left' ? -0.5 : 0.2,
                    maxz: side === 'left' ? 3 : 1.8
                },
                {
                    link: `${prefix}Arm`,
                    minx: -0.6, maxx: 0.6,
                    miny: -0.3, maxy: 0.8,
                    minz: 0.2, maxz: 1.2 // Reduced max Z for more natural poses
                }
            ]
        };
    }

    /**
     * Validate gesture result for naturalness
     * @private
     */
    _validateGestureResult(ikResult, side) {
        if (!ikResult || Object.keys(ikResult).length === 0) {
            return false;
        }

        // Check each bone quaternion for naturalness
        const prefix = side === 'left' ? 'Left' : 'Right';
        const bones = ['Arm', 'ForeArm', 'Hand'];

        for (const bone of bones) {
            const key = `${prefix}${bone}.quaternion`;
            if (ikResult[key]) {
                const boneType = bone.toLowerCase();
                if (!validateNaturalPose(ikResult[key], boneType, true)) {
                    if (this.options.debug) {
                        logger.warn(`Unnatural pose detected for ${key}`);
                    }
                    return false;
                }
            }
        }

        return true;
    }

    /**
     * Apply neutral position when IK fails
     * @private
     */
    _applyNeutralPosition(props, side) {
        const prefix = side === 'left' ? 'Left' : 'Right';

        // Set to null to return to original pose
        props[`${prefix}Arm.quaternion`] = null;
        props[`${prefix}ForeArm.quaternion`] = null;
        props[`${prefix}Hand.quaternion`] = null;
    }

    /**
     * Visualize targets for debugging (creates small spheres)
     * @private
     */
    _visualizeTargets(targets) {
        if (!this.options.visualizeTargets || typeof window === 'undefined') return;

        // Clean up previous spheres
        this._cleanupVisualization();

        // Create spheres for each target
        Object.entries(targets).forEach(([name, position], index) => {
            const geometry = new THREE.SphereGeometry(0.02, 8, 8);
            const material = new THREE.MeshBasicMaterial({
                color: index === 0 ? 0xff0000 : index === 1 ? 0x00ff00 : 0x0000ff
            });
            const sphere = new THREE.Mesh(geometry, material);
            sphere.position.copy(position);

            if (this.animator.mesh.parent) {
                this.animator.mesh.parent.add(sphere);
                this.targetSpheres.push(sphere);
            }
        });

        // Auto-cleanup after 5 seconds
        setTimeout(() => this._cleanupVisualization(), 5000);
    }

    /**
     * Cleanup target visualization
     * @private
     */
    _cleanupVisualization() {
        [...this.targetSpheres, ...this.poleTargetSpheres].forEach(sphere => {
            if (sphere.parent) {
                sphere.parent.remove(sphere);
            }
        });
        this.targetSpheres = [];
        this.poleTargetSpheres = [];
    }

    /**
     * Dispose of the gesture manager
     */
    dispose() {
        this._cleanupVisualization();
    }
}

export default GestureManager; 