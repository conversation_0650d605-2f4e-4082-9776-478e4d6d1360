/**
 * Gestures Module Index
 * Provides a clean API for the gesture system
 * 
 * Usage:
 * import { HandGestureSystem, createGestureOptions } from './gestures/index.js';
 * 
 * // Enable gestures
 * const gestureOptions = createGestureOptions({ enabled: true });
 * const gestureSystem = new HandGestureSystem(animator, gestureOptions);
 */

// Main gesture system
export { HandGestureSystem } from './HandGestureSystem.js';
export { GestureManager } from './GestureManager.js';

// Configuration and utilities
export {
    GESTURE_CONFIG,
    GESTURE_TYPES,
    GESTURE_SIDES,
    VALIDATION_RULES,
    validateGestureConfig,
    createGestureOptions,
    getGestureIntensity,
    selectRandomWaveType,
    selectRandomSide
} from './GestureConfig.js';

// Core animation utilities (re-exported for convenience)
export {
    ANIMATION_TIMING,
    calculateNaturalTarget,
    calculateElbowPoleTarget,
    validateNaturalPose,
    createAnimationCurve
} from './AnimationCore.js';

/**
 * Quick setup function for enabling gestures
 * @param {Object} animator - The animator instance
 * @param {Object} options - Gesture options
 * @returns {HandGestureSystem} - Configured gesture system
 */
export function setupGestureSystem(animator, options = {}) {
    const gestureOptions = createGestureOptions({
        enabled: true, // Enable by default when using this function
        ...options
    });

    return new HandGestureSystem(animator, gestureOptions);
}

/**
 * Check if gesture system is available
 * @returns {boolean} - Whether gesture system can be used
 */
export function isGestureSystemAvailable() {
    try {
        // Try to import the main components
        return typeof HandGestureSystem !== 'undefined' &&
            typeof GestureManager !== 'undefined';
    } catch (error) {
        return false;
    }
}

/**
 * Get default gesture configuration
 * @returns {Object} - Default configuration object
 */
export function getDefaultGestureConfig() {
    return {
        enabled: GESTURE_CONFIG.ENABLED_BY_DEFAULT,
        timing: { ...GESTURE_CONFIG.TIMING },
        probabilities: { ...GESTURE_CONFIG.PROBABILITIES },
        intensity: { ...GESTURE_CONFIG.INTENSITY },
        priorities: { ...GESTURE_CONFIG.PRIORITIES },
        debug: { ...GESTURE_CONFIG.DEBUG }
    };
}

// Default export for convenience
export default {
    HandGestureSystem,
    GestureManager,
    setupGestureSystem,
    createGestureOptions,
    isGestureSystemAvailable,
    getDefaultGestureConfig,
    GESTURE_CONFIG,
    GESTURE_TYPES,
    GESTURE_SIDES
}; 