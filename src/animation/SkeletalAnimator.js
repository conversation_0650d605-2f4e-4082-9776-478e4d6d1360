/**
 * SkeletalAnimator.js
 *
 * Consolidated animation system that combines functionality from:
 * - skeletalAnimation.js
 * - AnimationController.js
 *
 * This class handles loading and playing skeletal animations,
 * with support for FBX files, animation transitions, and bone constraints.
 * It extends BaseAnimator to leverage the existing animation framework.
 */

import * as THREE from 'three';
import { OneEuroFilter } from './AnimationFilters.js';
import { createLogger, LogLevel } from '../utils/logger.js';
import { BaseAnimator } from './BaseAnimator.js';
import { ANIMATION_REGISTRY, DEFAULT_CATEGORY_DURATIONS } from './AnimationConfig.js';
import { ANIMATION_TRANSITIONS } from './AnimationConfig.js';

// Create a logger for this module
const logger = createLogger('SkeletalAnimator');
logger.setLogLevel(LogLevel.INFO);

// Dynamic import for FBXLoader to avoid issues with different THREE.js versions
async function getFBXLoader() {
    try {
        // Try different import approaches
        try {
            const module = await import('three/addons/loaders/FBXLoader.js');
            return module.FBXLoader;
        } catch (error) {
            const module = await import('three/examples/jsm/loaders/FBXLoader.js');
            return module.FBXLoader;
        }
    } catch (error) {
        logger.error('Failed to import FBXLoader:', error);
        throw new Error('Failed to import FBXLoader. Make sure THREE.js is properly installed.');
    }
}

export class SkeletalAnimator extends BaseAnimator {
    /**
     * Create a new SkeletalAnimator
     * @param {Object} tragetmesh - The talkinghead object containing the avatar and scene
     * @param {Object} options - Configuration options
     */
    constructor(targetMesh, options = {}) {
        // Find a suitable mesh in the scene to pass to BaseAnimator
        // Call parent constructor with the found mesh or null
        super(targetMesh || null, {
            debug: false,
            defaultTransitionDuration: 1.0, // seconds - increased from 0.3 for better visibility
            ...options
        });
        // Store LLM service if provided in options
        this.llmService = options.llmService || null;

        // Animation clips storage
        this.animClips = [];

        // Track current mixer for animations
        this.mixer = null;

        // Store the current animation action
        this.currentAction = null;

        // Animation state tracking
        this.currentState = 'idle';
        this.lastAnimationCategory = null;
        this.currentAnimationFile = null;
        this.currentAnimationShouldLoop = false;

        // Animation transition duration
        this.transitionDuration = ANIMATION_TRANSITIONS.defaultDuration || 300;

        // Animation paths
        this.animationBasePath = '/assets/animations/';

        // Animation cache
        this.animationCache = new Map();

        // Create a clock for animation timing
        this.clock = new THREE.Clock();

        // Filters and constraints for bone movement
        this.filters = null;
        this.rotationConstraints = null;

        // Store original bone data for restoration
        this.originalBoneData = null;

        // Store a reference to the last valid armature
        this.lastValidArmature = null;

        // Track animation change timing to prevent rapid switching
        this.lastAnimationChangeTime = 0;
        this.lastTriggeredAnimation = null;

        // **GRACE PERIOD FIX**: Initialize completion grace period tracking
        // Used to prevent BaseAnimator from immediately taking over after FBX completion
        this.completionGracePeriod = null;

        // **CRITICAL FIX**: Store the target mesh as the armature reference immediately
        if (targetMesh) {
            this.lastValidArmature = targetMesh;
            logger.debug(`Stored initial armature reference: ${targetMesh.name || 'unnamed'}`);
        }

        logger.info('SkeletalAnimator initialized');
    }

    /**
     * Start the animation system - now delegates to BaseAnimator
     */
    start() {
        if (this.isRunning) return;

        // Start the clock for FBX timing
        this.clock.start();

        // Additional initialization specific to SkeletalAnimator
        if (this.mixer) {
            // If we already have a mixer, make sure it's ready for new animations
            this.mixer.timeScale = 1.0;
        }

        // Delegate to BaseAnimator's animation loop instead of our own
        this.startAnimationLoop();

        logger.info('SkeletalAnimator started - using BaseAnimator animation loop');
    }

    /**
     * Ensure the animation system is running
     * This method can be called multiple times safely
     */
    ensureRunning() {
        if (!this.isRunning) {
            logger.info('SkeletalAnimator was not running, starting now');
            this.start();
        }
    }

    /**
     * Stop the animation system - now delegates to BaseAnimator
     */
    stop() {
        if (!this.isRunning) return;

        // Additional cleanup specific to SkeletalAnimator
        if (this.mixer) {
            try {
                this.mixer.stopAllAction();
            } catch (error) {
                logger.error('Error stopping mixer actions:', error);
            }
        }

        // Delegate to BaseAnimator's stop method
        this.stopAnimationLoop();

        logger.info('SkeletalAnimator stopped - using BaseAnimator animation loop');
    }

    /**
     * Update FBX mixer and apply constraints
     * This is called by BaseAnimator's main animation loop
     * @param {number} deltaTime - Delta time from BaseAnimator
     * @protected
     */
    updateFBXAnimation(deltaTime) {
        if (!this.isRunning) return;

        try {
            // Update mixer if it exists
            if (this.mixer) {
                // Check if mixer is valid before updating
                if (!this.mixer.update) {
                    logger.warn('Mixer is invalid, stopping animation');
                    this.stopAnimation();
                    return;
                }

                // Check if mixer._root exists before trying to access it
                if (!this.mixer._root) {
                    logger.warn('Mixer root is null before update, animation may have finished naturally');
                    // Instead of immediately stopping, check if we have a stored reference to the armature
                    if (this.lastValidArmature) {
                        logger.info('Using last valid armature reference to perform cleanup');
                        // Use the stored reference to perform cleanup
                        this._cleanupWithStoredArmature(this.lastValidArmature);
                    } else {
                        // If we don't have a stored reference, we have to stop
                        this.stopAnimation();
                    }
                    return;
                }

                // Store the armature reference before update to preserve positions
                const armature = this.mixer._root;

                // Store original positions before mixer update
                let originalArmaturePosition = null;
                let originalHipsPosition = null;

                if (armature) {
                    originalArmaturePosition = armature.position.clone();

                    // Find and store Hips bone position
                    armature.traverse(obj => {
                        if (obj.name === 'Hips' && obj.position) {
                            originalHipsPosition = obj.position.clone();
                        }
                    });
                }

                // **DEBUG**: Log animation state before mixer update
                if (this.options.debug && Math.random() < 0.02) {
                    const currentActionName = this.currentAction ? this.currentAction.getClip().name : 'none';
                    const effectiveWeight = this.currentAction ? this.currentAction.getEffectiveWeight() : 0;
                    const timeScale = this.currentAction ? this.currentAction.timeScale : 0;
                    logger.debug(`[DEBUG] Before mixer.update: action=${currentActionName}, weight=${effectiveWeight.toFixed(3)}, timeScale=${timeScale}, deltaTime=${deltaTime.toFixed(4)}`);
                }

                // Update the mixer with the provided delta time
                this.mixer.update(deltaTime);

                // **DEBUG**: Log key bone positions after mixer update  
                if (this.options.debug && Math.random() < 0.01) {
                    armature.traverse(obj => {
                        if (obj.name === 'Hips' && obj.position) {
                            logger.debug(`[DEBUG] After mixer.update - Hips pos: (${obj.position.x.toFixed(3)}, ${obj.position.y.toFixed(3)}, ${obj.position.z.toFixed(3)})`);
                        }
                        if (obj.name === 'Spine' && obj.rotation) {
                            logger.debug(`[DEBUG] After mixer.update - Spine rot: (${obj.rotation.x.toFixed(3)}, ${obj.rotation.y.toFixed(3)}, ${obj.rotation.z.toFixed(3)})`);
                        }
                    });
                }

                // Check again if mixer exists after update (it might have been set to null during update)
                if (!this.mixer) {
                    logger.warn('Mixer was set to null during update');
                    return;
                }

                // Check if mixer._root exists and is valid after update
                if (!armature || !this.mixer._root) {
                    logger.warn('Mixer root is null after update, animation may have finished naturally');
                    // Instead of immediately stopping, check if we have a stored reference to the armature
                    if (this.lastValidArmature) {
                        logger.info('Using last valid armature reference to perform cleanup');
                        // Use the stored reference to perform cleanup
                        this._cleanupWithStoredArmature(this.lastValidArmature);
                    } else {
                        // If we don't have a stored reference, we have to stop
                        this.stopAnimation();
                    }
                    return;
                }

                // Store the valid armature reference for potential future use
                this.lastValidArmature = armature;

                // Verify armature is still valid
                if (!armature || !armature.traverse || !armature.updateMatrix) {
                    logger.warn('Armature is invalid, stopping animation');
                    this.stopAnimation();
                    return;
                }


                // **STRICT Y POSITION FIX**: Prevent upward floating during non-dance animations
                // Always preserve armature position to prevent "flying away"
                if (originalArmaturePosition) {
                    // Restore armature position completely to prevent any translation
                    armature.position.copy(originalArmaturePosition);
                }

                if (originalHipsPosition) {
                    // Find and restore Hips bone Y position strictly
                    armature.traverse(obj => {
                        if (obj.name === 'Hips' && obj.position) {
                            // **STRICT Y CONSTRAINT**: Always preserve Y position to prevent upward drift
                            // Allow X and Z movement for proper animation, but lock Y position
                            obj.position.y = originalHipsPosition.y;

                            // Force matrix update for the Hips bone
                            obj.updateMatrix();
                            obj.updateMatrixWorld(true);
                        }
                    });
                }

                // Apply bone rotation constraints and filters (but not position changes)
                this._applyBoneConstraintsAndFilters(armature);
            }
        } catch (error) {
            logger.error('Error in SkeletalAnimator FBX update:', error);
            // On error, stop the animation to prevent further errors
            this.stopAnimation();
        }
    }

    /**
     * Apply bone constraints and filters to the armature
     * @param {THREE.Object3D} armature - The armature to apply constraints to
     * @private
     */
    _applyBoneConstraintsAndFilters(armature) {
        // **DANCE ANIMATION FIX**: Skip constraints and filters for dance/performance animations
        // Dance animations need full freedom of movement
        if (this.lastAnimationCategory === 'performance' ||
            this.lastAnimationCategory === 'dancing' ||
            (this.currentAnimationFile && this.currentAnimationFile.toLowerCase().includes('dance'))) {

            if (this.options.debug && Math.random() < 0.01) { // Log occasionally
                logger.debug(`[DEBUG] Skipping constraints/filters for dance animation: ${this.lastAnimationCategory}/${this.currentAnimationFile}`);
            }
            return; // Skip all constraints and filters for dance animations
        }

        armature.traverse(obj => {
            if (obj && obj.isBone) {
                // Apply filters to reduce jitter
                if (this.filters && this.filters[obj.name] && obj.rotation) {
                    const filter = this.filters[obj.name];

                    // Apply to each rotation axis
                    const timestamp = performance.now() / 1000;

                    if (this.options.debug && obj.name === 'Head' && Math.random() < 0.005) {
                        logger.debug(`[DEBUG] Applying filter to ${obj.name}: before=(${obj.rotation.x.toFixed(3)}, ${obj.rotation.y.toFixed(3)}, ${obj.rotation.z.toFixed(3)})`);
                    }

                    // Filter rotation values
                    const filteredX = filter.x.filter(obj.rotation.x, timestamp);
                    const filteredY = filter.y.filter(obj.rotation.y, timestamp);
                    const filteredZ = filter.z.filter(obj.rotation.z, timestamp);

                    // Apply filtered values
                    obj.rotation.set(filteredX, filteredY, filteredZ);

                    if (this.options.debug && obj.name === 'Head' && Math.random() < 0.005) {
                        logger.debug(`[DEBUG] Applied filter to ${obj.name}: after=(${obj.rotation.x.toFixed(3)}, ${obj.rotation.y.toFixed(3)}, ${obj.rotation.z.toFixed(3)})`);
                    }
                }

                // Apply constraints to limit rotation range
                if (this.rotationConstraints && this.rotationConstraints[obj.name] && obj.rotation) {
                    const constraints = this.rotationConstraints[obj.name];

                    if (this.options.debug && obj.name === 'Head' && Math.random() < 0.005) {
                        logger.debug(`[DEBUG] Applying constraints to ${obj.name}: before=(${obj.rotation.x.toFixed(3)}, ${obj.rotation.y.toFixed(3)}, ${obj.rotation.z.toFixed(3)})`);
                    }

                    // Limit rotation to constraint range
                    obj.rotation.x = Math.max(constraints.min.x, Math.min(constraints.max.x, obj.rotation.x));
                    obj.rotation.y = Math.max(constraints.min.y, Math.min(constraints.max.y, obj.rotation.y));
                    obj.rotation.z = Math.max(constraints.min.z, Math.min(constraints.max.z, obj.rotation.z));

                    if (this.options.debug && obj.name === 'Head' && Math.random() < 0.005) {
                        logger.debug(`[DEBUG] Applied constraints to ${obj.name}: after=(${obj.rotation.x.toFixed(3)}, ${obj.rotation.y.toFixed(3)}, ${obj.rotation.z.toFixed(3)})`);
                    }
                }
            }
        });

        // Note: Position preservation is now handled in the main update loop
        // This method only handles rotation constraints and filters
    }

    /**
     * Process animation queue for SkeletalAnimator
     * This method integrates FBX animations with BaseAnimator's queue processing
     * Called by BaseAnimator.animate() through inheritance
     * @private
     * @override
     */
    _processAnimationQueue() {
        // Process FBX animations from the queue first
        if (this.animQueue && this.animQueue.length > 0) {
            const currentTime = this.animClock;

            // Look for FBX animations that are ready to play
            for (let i = this.animQueue.length - 1; i >= 0; i--) {
                const anim = this.animQueue[i];

                // Check if this is an FBX animation that's ready to start
                if (anim.isFBXAnimation && (!anim.t || currentTime >= anim.t)) {
                    // Remove from queue before playing to avoid reprocessing
                    this.animQueue.splice(i, 1);

                    if (this.options.debug) {
                        logger.debug(`🎬 Processing queued FBX animation: ${anim.fbxCategory}/${anim.fbxFile} (ID: ${anim.id})`);
                    }

                    // Play the FBX animation immediately
                    this.playAnimation(anim.fbxCategory, anim.fbxFile, anim.fbxDuration)
                        .then(success => {
                            if (this.options.debug) {
                                logger.debug(`✅ FBX animation ${success ? 'succeeded' : 'failed'}: ${anim.fbxCategory}/${anim.fbxFile}`);
                            }

                            // Trigger completion callback if provided
                            if (anim.onComplete && typeof anim.onComplete === 'function') {
                                try {
                                    anim.onComplete(anim);
                                } catch (error) {
                                    logger.error('Error in FBX animation completion callback:', error);
                                }
                            }
                        })
                        .catch(error => {
                            logger.error(`Failed to play queued FBX animation: ${anim.fbxCategory}/${anim.fbxFile}`, error);
                        });
                }
            }
        }

        // Always delegate to BaseAnimator for processing regular animations
        // This ensures consistent animation handling across both systems
        if (super._processAnimationQueue) {
            super._processAnimationQueue();
        }
    }

    /**
     * Check if FBX animations are currently active
     * This allows BaseAnimator to determine if it should suppress conflicting animations
     * @returns {boolean} True if FBX animations are playing
     */
    isFBXAnimationActive() {
        const hasValidMixer = !!(this.mixer && this.mixer._root);
        const hasCurrentAction = !!(this.currentAction);
        const actionIsRunning = hasCurrentAction && this.currentAction.isRunning();

        // Also check if we have a recent action start to catch fade-in periods
        const recentActionStart = this.lastAnimationChangeTime &&
            (performance.now() - this.lastAnimationChangeTime) < 2000; // Within 2 seconds

        // **GRACE PERIOD FIX**: Check if we're in the completion grace period
        // This prevents BaseAnimator from immediately taking over after FBX completion
        const inCompletionGracePeriod = this.completionGracePeriod &&
            (performance.now() - this.completionGracePeriod) < 3000; // 3 second grace period

        const isActive = hasValidMixer && (actionIsRunning || recentActionStart || inCompletionGracePeriod);

        // Debug logging (reduce frequency to avoid spam)
        if (this.options.debug && Math.random() < 0.02) {
            logger.debug(`FBX Active Check: mixer=${hasValidMixer}, action=${hasCurrentAction}, running=${actionIsRunning}, recent=${recentActionStart}, grace=${inCompletionGracePeriod}, result=${isActive}`);
        }

        return isActive;
    }

    /**
     * Helper method to restore original bone data
     * @param {THREE.Object3D} armature - The armature to restore
     * @private
     */
    _restoreOriginalBoneData(armature) {
        if (!armature || !this.originalBoneData) return;

        try {
            logger.info('Restoring original bone positions and rotations');

            // First, restore the armature's original transformation
            if (armature.userData && armature.userData.originalPosition) {
                logger.debug(`Restoring armature position from (${armature.position.x}, ${armature.position.y}, ${armature.position.z}) to (${armature.userData.originalPosition.x}, ${armature.userData.originalPosition.y}, ${armature.userData.originalPosition.z})`);

                // Check if position objects are valid before copying
                if (armature.position && armature.userData.originalPosition &&
                    typeof armature.position.copy === 'function') {
                    armature.position.copy(armature.userData.originalPosition);
                }

                // Check if quaternion objects are valid before copying
                if (armature.quaternion && armature.userData.originalQuaternion &&
                    typeof armature.quaternion.copy === 'function') {
                    armature.quaternion.copy(armature.userData.originalQuaternion);
                }

                // Check if scale objects are valid before copying
                if (armature.scale && armature.userData.originalScale &&
                    typeof armature.scale.copy === 'function') {
                    armature.scale.copy(armature.userData.originalScale);
                }
            }

            // Process bones in hierarchical order (parents first)
            const processedBones = new Set();
            const processBone = (boneName) => {
                if (processedBones.has(boneName)) return;

                const originalData = this.originalBoneData[boneName];
                if (!originalData) return;

                // Process parent first if it exists
                if (originalData.parentName && this.originalBoneData[originalData.parentName]) {
                    processBone(originalData.parentName);
                }

                // Mark as processed
                processedBones.add(boneName);

                // Find the bone object
                let boneObj = null;
                if (typeof armature.traverse === 'function') {
                    armature.traverse(obj => {
                        if (obj && obj.name === boneName) boneObj = obj;
                    });
                }

                if (!boneObj) return;

                // Special handling for Hips bone
                if (boneName === 'Hips') {
                    logger.debug(`Restoring Hips position from (${boneObj.position.x}, ${boneObj.position.y}, ${boneObj.position.z}) to (${originalData.position.x}, ${originalData.position.y}, ${originalData.position.z})`);

                    // For Hips, fully restore the original position
                    if (boneObj.position && typeof boneObj.position.copy === 'function') {
                        boneObj.position.copy(originalData.position);
                    }

                    if (boneObj.quaternion && typeof boneObj.quaternion.copy === 'function') {
                        boneObj.quaternion.copy(originalData.quaternion);
                    }

                    if (boneObj.scale && typeof boneObj.scale.copy === 'function') {
                        boneObj.scale.copy(originalData.scale);
                    }

                    // Force update the matrix if methods exist
                    if (typeof boneObj.updateMatrix === 'function') {
                        boneObj.updateMatrix();
                    }

                    if (typeof boneObj.updateMatrixWorld === 'function') {
                        boneObj.updateMatrixWorld(true);
                    }
                } else {
                    // For other bones, use lerp for smooth transition if methods exist
                    if (boneObj.position && originalData.position &&
                        typeof boneObj.position.lerp === 'function') {
                        boneObj.position.lerp(originalData.position, 0.5);
                    }

                    if (boneObj.quaternion && originalData.quaternion &&
                        typeof boneObj.quaternion.slerp === 'function') {
                        boneObj.quaternion.slerp(originalData.quaternion, 0.5);
                    }

                    if (boneObj.scale && originalData.scale &&
                        typeof boneObj.scale.lerp === 'function') {
                        boneObj.scale.lerp(originalData.scale, 0.5);
                    }
                }
            };

            // Start with the Hips bone (usually the root of the skeleton)
            if (this.originalBoneData['Hips']) {
                processBone('Hips');
            }

            // Process any remaining bones
            Object.keys(this.originalBoneData).forEach(processBone);

            // Force update the entire hierarchy if methods exist
            if (typeof armature.updateMatrix === 'function') {
                armature.updateMatrix();
            }

            if (typeof armature.updateMatrixWorld === 'function') {
                armature.updateMatrixWorld(true);
            }
        } catch (error) {
            logger.error('Error restoring original bone data:', error);
        } finally {
            // Clear the stored data regardless of success or failure
            this.originalBoneData = null;
        }
    }
    /**
 * Set the avatar state and update animations accordingly
 * Override the BaseAnimator's setState to handle FBX animations
 * @param {string} state The new state ('idle', 'listening', 'speaking', 'processing')
 */
    setState(state) {
        const stateChanged = this.currentState !== state;

        if (this.options.debug) {
            logger.debug(`SkeletalAnimator setState called: ${this.currentState} -> ${state}`);
        }

        if (!stateChanged) {
            logger.debug(`State unchanged (${state}), skipping transition`);
            return;
        }

        logger.info(`SkeletalAnimator state changing: ${this.currentState} -> ${state}`);

        // **BLENDING FIX**: Don't fade out FBX animations during speaking/listening transitions
        // Instead, allow FBX animations to continue while BaseAnimator adds complementary animations
        if (state === 'speaking' || state === 'listening') {
            // Update state without stopping FBX animations
            this.currentState = state;
            this.lastAnimationCategory = state === 'speaking' ? 'talking' : 'listening';
            this.isSpeaking = (state === 'speaking');
            this.isListening = (state === 'listening');

            // Call parent setState to activate BaseAnimator's complementary animations
            super.setState(state);

            if (this.isFBXAnimationActive()) {
                logger.info(`🎬 FBX animation continues during ${state} state - BaseAnimator will add complementary animations`);
            } else {
                logger.info(`No FBX animation active - using full BaseAnimator ${state} animations`);
            }
            return;
        }

        // For other states (idle, processing), handle normally
        this.currentState = state;
        this.lastAnimationCategory = state;

        // Reset state flags based on new state
        this.isSpeaking = (state === 'speaking');
        this.isListening = (state === 'listening');

        // Call parent setState for non-speaking/listening states
        super.setState(state);
    }


    /**
     * Enhanced cleanup method that properly restores BaseAnimator state
     * @param {THREE.Object3D} armature - The armature to clean up
     * @private
     */
    _cleanupWithStoredArmature(armature) {
        if (!armature) return;

        try {
            // **STEP 1**: Restore BaseAnimator state if it exists
            if (armature.userData && armature.userData.previousAnimationState) {
                this._restoreBaseAnimatorState(armature);
            }

            // **STEP 2**: Restore original bone data if we have it
            if (this.originalBoneData) {
                this._restoreOriginalBoneData(armature);
            }

            // **STEP 3**: Clean up FBX animation state
            this._cleanupFBXAnimationState(armature);

            // **STEP 4**: Clear our internal state
            this._clearInternalState();

        } catch (error) {
            logger.error('Error in enhanced cleanup:', error);
        }
    }

    /**
     * Restore BaseAnimator to its pre-FBX state
     * @param {THREE.Object3D} armature - The armature with stored state
     * @private
     */
    _restoreBaseAnimatorState(armature) {
        try {
            const prevState = armature.userData.previousAnimationState;
            const baseAnimator = armature.userData.baseAnimator;

            if (!baseAnimator) {
                logger.warn('No BaseAnimator reference found for state restoration');
                return;
            }

            logger.info('Restoring BaseAnimator to pre-FBX state');

            // **RESTORE ANIMATION QUEUE**: Put back the animations that were running
            if (prevState.animQueue && Array.isArray(prevState.animQueue)) {
                baseAnimator.animQueue = [...prevState.animQueue];
                logger.debug(`Restored ${prevState.animQueue.length} animations to queue`);
            }

            // **RESTART ANIMATION LOOP**: If it was running before
            if (prevState.wasRunning && !baseAnimator.isRunning) {
                logger.info('Restarting BaseAnimator animation loop');
                baseAnimator.startAnimationLoop();
            }

            // **RESTORE ORIGINAL POSE**: Ensure natural positioning
            if (baseAnimator.restoreOriginalPose && typeof baseAnimator.restoreOriginalPose === 'function') {
                logger.info('Calling BaseAnimator restoreOriginalPose');
                baseAnimator.restoreOriginalPose();
            }

            // **ADD IDLE ANIMATIONS**: Get natural movement going again
            if (baseAnimator.addIdleAnimations && typeof baseAnimator.addIdleAnimations === 'function') {
                setTimeout(() => {
                    baseAnimator.addIdleAnimations();
                }, 500); // Small delay to let pose restoration complete
            }

            // Clear the stored state
            delete armature.userData.previousAnimationState;

        } catch (error) {
            logger.error('Error restoring BaseAnimator state:', error);
        }
    }

    /**
     * Clean up FBX animation specific state
     * @param {THREE.Object3D} armature - The armature to clean up
     * @private
     */
    _cleanupFBXAnimationState(armature) {
        if (armature.userData) {
            // Clear FBX animation state
            delete armature.userData.fbxAnimationState;

            // Clear any temporary FBX-related data
            delete armature.userData.fbxTransitionData;
        }
    }

    /**
     * Clear internal SkeletalAnimator state
     * @private
     */
    _clearInternalState() {
        // Clean up mixer event listeners before setting to null
        if (this.mixer) {
            try {
                // THREE.js AnimationMixer uses removeAllEventListeners() method
                if (typeof this.mixer.removeAllEventListeners === 'function') {
                    this.mixer.removeAllEventListeners();
                    logger.debug('Removed all mixer event listeners');
                } else {
                    // Fallback: Try to remove specific listeners
                    if (typeof this.mixer.removeEventListener === 'function') {
                        this.mixer.removeEventListener('loop');
                        this.mixer.removeEventListener('finished');
                        logger.debug('Removed specific mixer event listeners');
                    }
                }
            } catch (error) {
                logger.warn('Error removing mixer event listeners:', error);
            }
        }

        // **DANCE ANIMATION FIX**: Clear FBX animation info from mesh userData
        if (this.mesh && this.mesh.userData && this.mesh.userData.currentFBXAnimation) {
            delete this.mesh.userData.currentFBXAnimation;

            if (this.options.debug) {
                logger.debug(`[DEBUG] Cleared FBX animation info from mesh userData`);
            }
        }

        // Clear references
        this.lastValidArmature = null;
        this.currentAction = null;
        this.mixer = null;

        // **GRACE PERIOD FIX**: Clear completion grace period when clearing state
        this.completionGracePeriod = null;

        // Clear animation state tracking
        this.lastAnimationCategory = null;
        this.currentAnimationFile = null;
        this.currentAnimationShouldLoop = false;

        // Clear filters and constraints
        this.filters = null;
        this.rotationConstraints = null;
        this.originalBoneData = null;

        logger.debug('Cleared SkeletalAnimator internal state');
    }

    /**
     * Enhanced stopAnimation that handles BaseAnimator transition better
     */
    stopAnimation() {
        if (this.options.debug) {
            logger.debug('[DEBUG] Enhanced stopAnimation called');
        }

        // Store reference to the root before stopping the mixer
        let armature = null;

        if (this.mixer && this.mixer._root) {
            armature = this.mixer._root;
        } else if (this.lastValidArmature) {
            armature = this.lastValidArmature;
        }

        try {
            // Stop all FBX actions if mixer exists
            if (this.mixer && typeof this.mixer.stopAllAction === 'function') {
                this.mixer.stopAllAction();
            }

            // Stop current action if it exists
            if (this.currentAction && typeof this.currentAction.stop === 'function') {
                this.currentAction.stop();
            }

            // Clear looping flag when stopping
            this.currentAnimationShouldLoop = false;

        } catch (error) {
            logger.error('Error stopping FBX animation actions:', error);
        }

        // Use enhanced cleanup if we have a valid armature
        if (armature) {
            this._cleanupWithStoredArmature(armature);
        } else {
            this._clearInternalState();
            logger.warn('No valid armature found for enhanced cleanup');
        }
    }

    /**
     * Force clear all animation state - used for emergency cleanup like after voice cloning
     * This is more aggressive than regular stopAnimation and ensures clean state
     */
    forceStopAllAnimations() {
        if (this.options.debug) {
            logger.debug('[DEBUG] Force stop all animations called');
        }

        try {
            // Immediately stop all mixer actions
            if (this.mixer && typeof this.mixer.stopAllAction === 'function') {
                this.mixer.stopAllAction();
            }

            // Stop and clear current action
            if (this.currentAction) {
                if (typeof this.currentAction.stop === 'function') {
                    this.currentAction.stop();
                }
                this.currentAction = null;
            }

            // Force clear all internal state
            this.lastAnimationCategory = null;
            this.currentAnimationFile = null;
            this.currentAnimationShouldLoop = false;
            this.completionGracePeriod = null;
            this.lastAnimationChangeTime = 0;

            // Clear FBX animation info from mesh userData if it exists
            if (this.mesh && this.mesh.userData && this.mesh.userData.currentFBXAnimation) {
                delete this.mesh.userData.currentFBXAnimation;
                logger.debug('[DEBUG] Cleared FBX animation info from mesh userData');
            }

            // Reset state flags
            this.isSpeaking = false;
            this.isListening = false;

            logger.info('Force cleared all FBX animation state');

        } catch (error) {
            logger.error('Error in force stop all animations:', error);
        }
    }



    // The _createListeningAnimation method has been removed as we now use BaseAnimator's setState
    // which handles the listening animation state

    /**
     * Queue an animation for future playback with proper integration to BaseAnimator's queue system
     * @param {string} category Animation category
     * @param {string} file Animation file name  
     * @param {Object} options Animation options
     * @param {number} options.delay Delay before playing (seconds)
     * @param {number} options.duration Duration to play (seconds, 0 for default)
     * @param {Function} options.onComplete Callback function when animation completes
     * @returns {Promise<string>} Animation ID for tracking
     */
    async queueAnimation(category, file, options = {}) {
        try {
            // Validate inputs
            if (!category || !file) {
                throw new Error('Category and file are required');
            }

            // Check if animation exists
            if (!this.loadedAnimations[category] || !this.loadedAnimations[category][file]) {
                throw new Error(`Animation not found: ${category}/${file}`);
            }

            // Get animation duration from loaded animations
            const animationInfo = this.loadedAnimations[category][file];
            const finalDuration = options.duration || animationInfo.duration || 2.0; // Default 2 seconds

            // Extract options with defaults
            const {
                delay = 0,
                onComplete = null
            } = options;

            if (this.options.debug) {
                logger.debug(`Queueing FBX animation: ${category}/${file} (duration: ${finalDuration}s, delay: ${delay}s)`);
            }

            // Create animation template that integrates with BaseAnimator's system
            const animationTemplate = {
                name: `fbx_${category}_${file}`,
                dt: [finalDuration * 1000], // Convert to milliseconds for BaseAnimator
                vs: {
                    // Standard BaseAnimator animation structure
                    morphTargetInfluences: [],
                    // Add FBX-specific metadata
                    fbxAnimation: {
                        category: category,
                        file: file,
                        duration: finalDuration
                    }
                }
            };

            // Create animation object using BaseAnimator's animFactory
            const anim = this.animFactory(animationTemplate);

            if (!anim) {
                throw new Error(`Failed to create animation object for: ${category}/${file}`);
            }

            // Set FBX-specific properties for queue processing
            anim.isFBXAnimation = true;
            anim.fbxCategory = category;
            anim.fbxFile = file;
            anim.fbxDuration = finalDuration;

            // Set completion callback if provided
            if (onComplete) {
                anim.onComplete = onComplete;
            }

            // Handle delay by setting start time
            if (delay > 0) {
                const currentTime = this.currentTime || (Date.now() / 1000);
                anim.t = currentTime + delay;
            } else {
                anim.t = this.currentTime || (Date.now() / 1000);
            }

            // Add to animation queue (animations play sequentially)
            this.animQueue.push(anim);

            if (this.options.debug) {
                logger.debug(`✅ Queued FBX animation: ${category}/${file} (ID: ${anim.id})`);
            }

            return anim.id; // Return animation ID for tracking

        } catch (error) {
            logger.error(`Failed to queue animation ${category}/${file}:`, error);
            throw error;
        }
    }

    /**
     * Play an animation immediately
     * @param {string} category Animation category or null
     * @param {string} file Animation file name
     * @param {number} duration Duration to play (ms, 0 for default)
     * @returns {Promise<boolean>} Success status
     */
    async playAnimation(category, file, duration = 0) {
        if (this.options.debug) {
            logger.debug(`[DEBUG] playAnimation called: category=${category}, file=${file}, duration=${duration}`);
        }

        // **DUPLICATE PREVENTION**: Prevent same animation from being triggered rapidly
        const animationKey = `${category}/${file}`;
        if (this.lastTriggeredAnimation === animationKey &&
            this.lastAnimationChangeTime &&
            (performance.now() - this.lastAnimationChangeTime) < 500) {
            if (this.options.debug) {
                logger.debug(`[DEBUG] Skipping duplicate animation request: ${animationKey}`);
            }
            return true; // Return success since animation is already playing
        }

        // **CRITICAL FIX**: Ensure the animation system is running before playing animations
        this.ensureRunning();

        // Track animation changes for better crossfade handling
        const now = performance.now();
        const timeSinceLastChange = this.lastAnimationChangeTime ? (now - this.lastAnimationChangeTime) : 1000;
        this.lastAnimationChangeTime = now;
        this.lastTriggeredAnimation = animationKey;

        if (this.options.debug) {
            logger.debug(`[DEBUG] Animation change request: ${category}/${file} (${timeSinceLastChange}ms since last change)`);
        }

        try {
            // Build animation path
            const animationPath = `${this.animationBasePath}${file}`;

            // Check cache first
            let animationClip;
            if (this.animationCache.has(file)) {
                animationClip = this.animationCache.get(file);
                logger.info(`Using cached animation: ${file}`);

                // Even for cached animations, we need to process the clip to filter out tracks for missing bones
                // This is important because different models may have different bone structures
                this._processAnimationClip(animationClip);
            } else {
                // Load animation file
                logger.info(`Loading animation: ${animationPath}`);

                // Get the FBXLoader dynamically
                const FBXLoader = await getFBXLoader();
                const loader = new FBXLoader();

                try {
                    // Try to load the animation
                    const fbx = await loader.loadAsync(animationPath);

                    if (fbx && fbx.animations && fbx.animations.length > 0) {
                        animationClip = fbx.animations[0];

                        // Process animation clip (scale, rename tracks, etc.)
                        this._processAnimationClip(animationClip);

                        // Cache for future use
                        this.animationCache.set(file, animationClip);
                        logger.info(`Animation cached for future use: ${file}`);
                    } else {
                        throw new Error('No animations found in FBX file');
                    }
                } catch (loadError) {
                    // Try with /assets/ prefix as fallback
                    logger.warn(`Failed to load animation from ${animationPath}, trying with /assets/ prefix`);
                    try {
                        const fbx = await loader.loadAsync(`/assets/${animationPath}`);

                        if (fbx && fbx.animations && fbx.animations.length > 0) {
                            animationClip = fbx.animations[0];

                            // Process animation clip (scale, rename tracks, etc.)
                            this._processAnimationClip(animationClip);

                            // Cache for future use
                            this.animationCache.set(file, animationClip);
                            logger.info(`Animation cached for future use: ${file}`);
                        } else {
                            throw new Error('No animations found in FBX file');
                        }
                    } catch (fallbackError) {
                        throw new Error(`Failed to load animation: ${fallbackError.message}`);
                    }
                }
            }

            // Find the target mesh/armature
            const armature = this.mesh;

            // Use either the found armature or the last resort
            const targetArmature = armature;

            // Store original bone data for restoration (only if not already stored)
            if (!this.originalBoneData) {
                this._storeOriginalBoneData(targetArmature);
            }

            // Create or reuse mixer
            if (!this.mixer) {
                this.mixer = new THREE.AnimationMixer(targetArmature);
                if (this.options.debug) {
                    logger.debug('[DEBUG] Created new THREE.AnimationMixer for targetArmature:', targetArmature);
                }
                // Add event listeners for animation completion
                this._setupMixerEventListeners(targetArmature);
            }

            // Use category-specific transition durations when available
            const transitionSettings = ANIMATION_REGISTRY?.getTransitionSettings?.(category);
            const defaultDuration = this.options.defaultTransitionDuration || 0.5; // 500ms fallback

            // Convert milliseconds to seconds for category settings
            const fadeOutDuration = transitionSettings ? (transitionSettings.fadeOut / 1000) : defaultDuration;
            const fadeInDuration = transitionSettings ? (transitionSettings.fadeIn / 1000) : defaultDuration;

            if (this.options.debug) {
                logger.debug(`[DEBUG] Using consistent transition timing: fadeOut=${fadeOutDuration}s, fadeIn=${fadeInDuration}s`);
            }

            // **CRITICAL FIX**: Clean up old actions to prevent weight=0 issue
            // Remove all stale actions that have been faded out or are no longer needed
            if (this.mixer && this.mixer._actions) {
                const staleActions = this.mixer._actions.filter(action =>
                    !action.enabled || action.getEffectiveWeight() < 0.001
                );

                if (staleActions.length > 0) {
                    if (this.options.debug) {
                        logger.debug(`[DEBUG] Removing ${staleActions.length} stale actions from mixer`);
                    }

                    staleActions.forEach(action => {
                        try {
                            action.stop();
                            this.mixer.uncacheAction(action.getClip());
                        } catch (error) {
                            if (this.options.debug) {
                                logger.debug(`[DEBUG] Error cleaning up stale action: ${error.message}`);
                            }
                        }
                    });
                }
            }

            // Stop current animation with improved crossfade handling
            if (this.currentAction) {
                // Ensure fadeOutDuration is a valid number
                const safeFadeOutDuration = typeof fadeOutDuration === 'number' && !isNaN(fadeOutDuration) ? fadeOutDuration : 0.5;

                if (this.options.debug) {
                    logger.debug(`[DEBUG] Crossfading from current animation (${this.lastAnimationCategory}) to new animation (${category}/${file})`);
                    logger.debug(`[DEBUG] Fading out current action with duration: ${safeFadeOutDuration}s`);
                }

                try {
                    // Simple fade out - no complex weight manipulation
                    this.currentAction.fadeOut(safeFadeOutDuration);

                    if (this.options.debug) {
                        logger.debug(`[DEBUG] Fading out current action with duration: ${safeFadeOutDuration}s`);
                    }
                } catch (error) {
                    logger.error(`Error during fadeOut: ${error.message}`);
                    // Fallback: immediately stop the action
                    if (this.currentAction.stop) {
                        this.currentAction.stop();
                    }
                }
            } else {
                if (this.options.debug) {
                    logger.debug(`[DEBUG] No current action to fade out, starting fresh animation: ${category}/${file}`);
                }
            }

            // Create new animation action with error handling for missing bones
            let action;
            try {
                // Verify mixer is still valid before creating action
                if (!this.mixer || !this.mixer.clipAction) {
                    throw new Error('Animation mixer is invalid or was destroyed');
                }

                action = this.mixer.clipAction(animationClip);

                // Verify the action was created successfully
                if (!action) {
                    throw new Error('Failed to create animation action from clip');
                }

                // Configure action based on animation type and duration
                if (duration > 0) {
                    // For animations with a specified duration, play once and stop
                    action.setLoop(THREE.LoopOnce);
                    action.clampWhenFinished = true;
                } else {
                    // **PERFORMANCE ANIMATION FIX**: Check animation config to determine if it should loop
                    const animationData = ANIMATION_REGISTRY?.getByFilename(file);
                    const shouldLoop = animationData && animationData.loopable;

                    if (shouldLoop) {
                        logger.info(`[SkeletalAnimator] 🔄 Setting animation to loop: ${file} (category: ${category}, loopable: ${animationData.loopable})`);
                        action.setLoop(THREE.LoopRepeat);
                        action.clampWhenFinished = false;
                    } else {
                        logger.info(`[SkeletalAnimator] ▶️ Playing animation once: ${file} (category: ${category}, loopable: ${animationData ? animationData.loopable : 'unknown'})`);
                        action.setLoop(THREE.LoopOnce);
                        action.clampWhenFinished = true;
                    }
                }

                // Ensure fadeInDuration is a valid number
                const safeFadeInDuration = typeof fadeInDuration === 'number' && !isNaN(fadeInDuration) ? fadeInDuration : 0.5;

                // **CRITICAL FIX**: Call play() BEFORE fadeIn() for proper weight handling
                // This ensures the action is in the correct state for weight transitions
                action.play();

                // **WEIGHT FIX**: Ensure action is properly enabled and weighted
                if (!action.enabled) {
                    action.enabled = true;
                    if (this.options.debug) {
                        logger.debug(`[DEBUG] Manually enabled action after play()`);
                    }
                }

                // Simple fade in - let THREE.js handle the weight properly
                action.fadeIn(safeFadeInDuration);

                // **ADDITIONAL FIX**: Verify action state after fade-in and force enable if needed
                setTimeout(() => {
                    if (action && !action.enabled && action === this.currentAction) {
                        action.enabled = true;
                        action.setEffectiveWeight(1.0);
                        if (this.options.debug) {
                            logger.debug(`[DEBUG] Force-enabled action after fade-in timeout`);
                        }
                    }
                }, 100); // Small delay to allow fade-in to take effect

                if (this.options.debug) {
                    logger.debug(`[DEBUG] Started action and fading in with duration: ${safeFadeInDuration}s`);
                    logger.debug(`[DEBUG] Action state after play+fadeIn: enabled=${action.enabled}, weight=${action.getEffectiveWeight().toFixed(3)}, time=${action.time.toFixed(3)}`);
                }
            } catch (error) {
                // Handle the "THREE.PropertyBinding: No target node found for track" error
                if (error.message && error.message.includes('No target node found for track')) {
                    logger.error(`Animation binding error: ${error.message}`);
                    logger.warn('This usually happens when the animation contains bones that don\'t exist in the model.');

                    // Try to extract the bone name from the error message
                    const match = error.message.match(/track: ([^.]+)/);
                    if (match && match[1]) {
                        const missingBone = match[1];
                        logger.warn(`Missing bone: ${missingBone}. Attempting to filter out problematic tracks.`);

                        // Filter out tracks for the problematic bone
                        const filteredTracks = animationClip.tracks.filter(track => {
                            const trackBoneName = track.name.split('.')[0];
                            return trackBoneName !== missingBone;
                        });

                        if (filteredTracks.length < animationClip.tracks.length) {
                            logger.info(`Removed ${animationClip.tracks.length - filteredTracks.length} tracks for bone ${missingBone}`);
                            animationClip.tracks = filteredTracks;

                            // Try again with the filtered clip
                            try {
                                action = this.mixer.clipAction(animationClip);

                                // Configure action based on animation type and duration
                                if (duration > 0) {
                                    // If a specific duration is set, play once and stop
                                    action.setLoop(THREE.LoopOnce);
                                    action.clampWhenFinished = true;
                                } else {
                                    // For loopable animations or indefinite duration, loop continuously
                                    action.setLoop(THREE.LoopRepeat);
                                    action.clampWhenFinished = false;
                                }

                                // **CRITICAL FIX**: Call play() BEFORE fadeIn() for proper weight handling
                                action.play();

                                // **WEIGHT FIX**: Ensure action is properly enabled
                                if (!action.enabled) {
                                    action.enabled = true;
                                }

                                action.fadeIn(fadeInDuration);
                            } catch (retryError) {
                                logger.error(`Still failed after filtering: ${retryError.message}`);
                                throw new Error(`Failed to play animation after filtering: ${retryError.message}`);
                            }
                        } else {
                            throw new Error(`Failed to filter problematic tracks: ${error.message}`);
                        }
                    } else {
                        throw new Error(`Failed to identify problematic bone: ${error.message}`);
                    }
                } else {
                    // Re-throw other errors
                    throw error;
                }
            }

            // Apply time scale based on animation type
            if (category === 'dancing' || category === 'fighting' || category === 'performance') {
                // Use normal speed for these animations
                action.timeScale = 1.0;

                if (this.options.debug) {
                    logger.debug(`[DEBUG] Set timeScale=1.0 for ${category} animation: ${file}`);
                }
            } else {
                // Use consistent timing for all other animations (removed talking special case)
                action.timeScale = 0.9;

                if (this.options.debug) {
                    logger.debug(`[DEBUG] Set timeScale=0.9 for ${category} animation: ${file}`);
                }
            }

            // Store as current animation
            this.currentAction = action;
            this.lastAnimationCategory = category;
            this.currentAnimationFile = file;

            // **DANCE ANIMATION FIX**: Store current FBX animation info in mesh userData
            // so BaseAnimator can check if it should skip breathing animations
            if (this.mesh && this.mesh.userData) {
                this.mesh.userData.currentFBXAnimation = {
                    category: category,
                    file: file,
                    isActive: true
                };

                if (this.options.debug) {
                    logger.debug(`[DEBUG] Stored FBX animation info in mesh userData: ${category}/${file}`);
                }
            }

            // **LOOPING TRACKING FIX**: Track if this animation should loop (consistent with loop setting above)
            const animationData = ANIMATION_REGISTRY?.getByFilename(file);
            this.currentAnimationShouldLoop = animationData && animationData.loopable;

            // **GRACE PERIOD FIX**: Clear completion grace period when new animation starts
            // This ensures the grace period only applies to post-completion transitions
            if (this.completionGracePeriod) {
                this.completionGracePeriod = null;
                if (this.options.debug) {
                    logger.debug(`[DEBUG] Cleared completion grace period for new animation: ${category}/${file}`);
                }
            }

            // Calculate natural animation duration if not specified
            if (duration === 0 && animationClip && animationClip.duration) {
                // Use the FBX animation's natural duration (convert from seconds to milliseconds)
                duration = Math.round(animationClip.duration * 1000);
                if (this.options.debug) {
                    logger.debug(`[DEBUG] Using natural FBX duration: ${duration}ms (${animationClip.duration}s)`);
                }
            }

            if (this.options.debug) {
                logger.debug(`[DEBUG] Playing animation: ${category}/${file} (fadeIn: ${fadeInDuration}s, fadeOut: ${fadeOutDuration}s, duration: ${duration}ms)`);
            }

            // Set timeout to stop animation based on natural or specified duration
            // Performance animations should loop indefinitely until interrupted, so skip timeout
            if (duration > 0 && category !== 'performance') {
                // Add some buffer time to allow the animation to complete fully
                const safeFadeInDuration = typeof fadeInDuration === 'number' && !isNaN(fadeInDuration) ? fadeInDuration : 0.5;
                const timeoutDuration = duration + (safeFadeInDuration * 1000) + 500; // Add fade-in time + 500ms buffer

                setTimeout(() => {
                    // Double-check that this action is still current and valid
                    if (this.currentAction === action && action && typeof action.isRunning === 'function' && action.isRunning()) {
                        if (this.options.debug) {
                            logger.debug(`[DEBUG] Animation timeout reached after ${timeoutDuration}ms, handling completion`);
                        }
                        this._handleAnimationCompletion(action, category, fadeOutDuration);
                    } else if (this.options.debug) {
                        logger.debug(`[DEBUG] Animation timeout reached but action is no longer current or running`);
                    }
                }, timeoutDuration);
            } else if (category === 'performance') {
                logger.info(`[SkeletalAnimator] 🔄 Performance animation will loop indefinitely until interrupted: ${file}`);
            }

            return true;
        } catch (error) {
            if (this.options.debug) {
                logger.error(`[DEBUG] Error in playAnimation: ${error.message}`);
            }
            logger.error(`Error playing animation: ${error.message}`);
            return false;
        }
    }

    /**
     * Process an animation clip (scale, rename tracks, etc.)
     * @param {THREE.AnimationClip} clip - The animation clip to process
     * @private
     */
    _processAnimationClip(clip) {
        if (!clip || !clip.tracks) return;

        // Scale factor for positions (Mixamo=100, RPM=1)
        const scale = 0.01;

        // Collect existing bones in the armature to filter tracks
        const existingBones = new Set();
        if (this.mesh) {
            this.mesh.traverse(obj => {
                if (obj.isBone || obj.type === 'Bone') {
                    existingBones.add(obj.name);
                }
            });
            logger.debug(`Found ${existingBones.size} bones in armature for track filtering`);
        }

        // Filter and process tracks
        const originalTrackCount = clip.tracks.length;
        const filteredTracks = [];
        const trackStats = { position: 0, quaternion: 0, scale: 0, other: 0 };

        clip.tracks.forEach(track => {
            // Remove mixamorig prefix if present
            track.name = track.name.replaceAll('mixamorig', '');

            // Get bone name and property type from track name
            const [boneName, propName] = track.name.split('.');

            // Skip tracks for bones that don't exist in the model
            if (existingBones.size > 0 && !existingBones.has(boneName)) {
                // logger.debug(`Skipping track for non-existent bone: ${boneName}.${propName}`);
                return;
            }

            // Add the track to filtered tracks
            filteredTracks.push(track);

            // **DEBUG**: Track what types of animations we're processing
            if (propName === 'position') {
                trackStats.position++;
            } else if (propName === 'quaternion') {
                trackStats.quaternion++;
            } else if (propName === 'scale') {
                trackStats.scale++;
            } else {
                trackStats.other++;
            }

            // Scale position values
            if (propName === 'position' && track.values) {
                // **DEBUG**: Log position scaling for key bones
                if (this.options.debug && (boneName === 'Hips' || boneName === 'Spine')) {
                    const originalFirst = track.values[0];
                    const originalLast = track.values[track.values.length - 3]; // Last X value
                    logger.debug(`[DEBUG] Scaling ${boneName} position track: first=${originalFirst.toFixed(3)} -> ${(originalFirst * scale).toFixed(3)}`);
                }

                for (let i = 0; i < track.values.length; i++) {
                    track.values[i] = track.values[i] * scale;
                }
            }
        });

        // Update the clip with filtered tracks
        if (filteredTracks.length !== originalTrackCount) {
            logger.info(`Filtered animation tracks from ${originalTrackCount} to ${filteredTracks.length}`);
            clip.tracks = filteredTracks;
        }

        // **DEBUG**: Log track statistics for dance animations
        if (this.options.debug && (this.lastAnimationCategory === 'performance' || this.lastAnimationCategory === 'dancing')) {
            logger.debug(`[DEBUG] Track stats - Position: ${trackStats.position}, Rotation: ${trackStats.quaternion}, Scale: ${trackStats.scale}, Other: ${trackStats.other}`);
        }
    }

    /**
     * Store original bone data for restoration
     * @param {THREE.Object3D} armature - The armature to store data for
     * @private
     */
    _storeOriginalBoneData(armature) {
        // Check if armature is valid
        if (!armature || !armature.traverse || typeof armature.traverse !== 'function') {
            logger.error('Invalid armature provided to _storeOriginalBoneData');
            return;
        }

        this.originalBoneData = {};

        // Store the armature's original world matrix
        if (!armature.userData) {
            armature.userData = {};
        }

        // Store the armature's original position, rotation, and scale
        armature.userData.originalPosition = armature.position.clone();
        armature.userData.originalQuaternion = armature.quaternion.clone();
        armature.userData.originalScale = armature.scale.clone();

        logger.debug(`Storing armature original position: ${armature.position.x}, ${armature.position.y}, ${armature.position.z}`);

        // Collect existing bones in the armature
        armature.traverse(obj => {
            if (obj.isBone || obj.type === 'Bone') {
                this.originalBoneData[obj.name] = {
                    position: obj.position.clone(),
                    quaternion: obj.quaternion.clone(),
                    scale: obj.scale.clone(),
                    // Store parent relationship
                    parentName: obj.parent ? obj.parent.name : null
                };

                // Store the original world matrix for absolute positioning
                if (!obj.userData) {
                    obj.userData = {};
                }
                obj.userData.originalMatrix = obj.matrixWorld.clone();

                // For Hips bone, log detailed information
                if (obj.name === 'Hips') {
                    logger.debug(`Storing Hips original position: ${obj.position.x}, ${obj.position.y}, ${obj.position.z}`);
                }
            }
        });

        // **SIMPLIFIED FILTERING**: Only apply light filtering to reduce jitter without dampening animation
        this.filters = {};
        const bonesNeedingFilters = ['Head']; // Only filter Head to reduce over-damping

        armature.traverse(obj => {
            if ((obj.isBone || obj.type === 'Bone') && bonesNeedingFilters.includes(obj.name)) {
                // Use lighter filter settings to preserve animation movement
                const minCutOff = 2.0; // Higher cutoff = less filtering
                const beta = 0.01; // Higher beta = more responsive

                this.filters[obj.name] = {
                    x: new OneEuroFilter(60, minCutOff, beta, 1.0),
                    y: new OneEuroFilter(60, minCutOff, beta, 1.0),
                    z: new OneEuroFilter(60, minCutOff, beta, 1.0)
                };
                logger.debug(`Created light filter for ${obj.name}`);
            }
        });

        // **RELAXED CONSTRAINTS**: Only prevent extreme rotations that would break the model
        this.rotationConstraints = {
            Head: {
                min: new THREE.Vector3(-1.5, -1.5, -1.0), // Much more permissive
                max: new THREE.Vector3(1.5, 1.5, 1.0)
            }
        };
    }

    /**
     * Set up event listeners for the animation mixer
     * @param {THREE.Object3D} armature - The armature being animated
     * @private
     */
    _setupMixerEventListeners(armature) {
        if (!this.mixer) return;
        if (this.options.debug) {
            logger.debug('[DEBUG] Setting up mixer event listeners for armature:', armature);
        }
        // Add a callback that's called during animation loop - position preservation is now handled in main update
        this.mixer.addEventListener('loop', () => {
            // Position preservation is now handled in the main updateFBXAnimation method
            // This event listener is kept for potential future use
            if (this.options.debug) {
                // Only log occasionally to reduce noise
                if (Math.random() < 0.01) { // 1% chance to log
                    logger.debug('Animation loop event - position preservation handled in main update');
                }
            }
        });

        // Handle animation finish - restore base animator state
        this.mixer.addEventListener('finished', () => {
            if (this.options.debug) {
                logger.debug('[DEBUG] Mixer finished event triggered');
            }

            // **LOOPING FIX**: Check if the current animation should be looping
            // If it should loop, restart it immediately instead of stopping
            if (this.currentAnimationShouldLoop && this.currentAction) {
                if (this.options.debug) {
                    logger.debug(`[DEBUG] Animation finished but should loop - restarting: ${this.lastAnimationCategory}/${this.currentAnimationFile}`);
                }

                try {
                    // Reset and restart the action for proper looping
                    this.currentAction.reset();
                    this.currentAction.play();
                    logger.info(`🔄 Restarted looping animation: ${this.currentAnimationFile}`);
                    return; // Exit early, don't stop the animation
                } catch (error) {
                    logger.error('Error restarting looping animation:', error);
                    // Fall through to normal completion handling if restart fails
                }
            }

            logger.info('Animation finished, performing final restoration');

            // Use setTimeout to avoid race condition with the update loop
            setTimeout(() => {
                // Double-check that we still have valid references
                if (!this.mixer || !armature || !this.originalBoneData) {
                    logger.warn('Mixer or armature no longer available for restoration');
                    return;
                }

                try {
                    // First, ensure the entire armature hierarchy is properly restored
                    // Restore armature transformation
                    if (armature.userData && armature.userData.originalPosition) {
                        armature.position.copy(armature.userData.originalPosition);
                        armature.quaternion.copy(armature.userData.originalQuaternion);
                        armature.scale.copy(armature.userData.originalScale);
                    }

                    // Special focus on the Hips bone
                    armature.traverse(obj => {
                        if (obj.name === 'Hips' && this.originalBoneData[obj.name]) {
                            const originalData = this.originalBoneData[obj.name];

                            // Log before state
                            if (this.options.debug) {
                                logger.debug(`Final Hips restoration`);
                            }

                            // Fully restore original position and rotation
                            obj.position.copy(originalData.position);
                            obj.quaternion.copy(originalData.quaternion);
                            obj.scale.copy(originalData.scale);

                            // Force update matrices
                            obj.updateMatrix();
                            obj.updateMatrixWorld(true);
                        }
                    });

                    // Force update the entire hierarchy
                    armature.updateMatrix();
                    armature.updateMatrixWorld(true);

                    // Then stop the animation
                    this.stopAnimation();
                } catch (error) {
                    logger.error('Error in finished event handler:', error);
                    // Fallback: just stop the animation
                    this.stopAnimation();
                }
            }, 0); // Execute on next tick to avoid race condition
        });
    }

    /**
     * Handle animation completion with smooth transitions and return-to-idle
     * Enhanced to work better with rapid animation changes
     * @param {THREE.AnimationAction} action - The completed animation action
     * @param {string} category - Animation category
     * @param {number} fadeOutDuration - Fade out duration
     * @private
     */
    async _handleAnimationCompletion(action, category, fadeOutDuration) {
        try {
            // Import transition configuration
            const { LLM_ANIMATION_SYSTEM } = await import('./AnimationConfig.js');

            // Check if this action is still the current action
            // If not, another animation has already taken over - skip processing
            if (this.currentAction !== action) {
                if (this.options.debug) {
                    logger.debug(`[DEBUG] Skipping completion handling - action is no longer current`);
                }
                return;
            }

            // Get transition settings for this category
            const transitionSettings = LLM_ANIMATION_SYSTEM.getTransitionSettings(category);
            const shouldReturn = LLM_ANIMATION_SYSTEM.shouldReturnToIdle(category);

            // Ensure we have valid fade out duration with better fallbacks
            let safeFadeOutDuration = 0.5; // Default fallback

            if (transitionSettings && typeof transitionSettings.fadeOut === 'number') {
                safeFadeOutDuration = transitionSettings.fadeOut / 1000;
            } else if (typeof fadeOutDuration === 'number' && !isNaN(fadeOutDuration)) {
                safeFadeOutDuration = fadeOutDuration;
            }

            // Check for rapid animation changes - don't fade out if a new animation started recently
            const now = performance.now();
            const timeSinceLastChange = this.lastAnimationChangeTime ? (now - this.lastAnimationChangeTime) : 1000;

            if (timeSinceLastChange < 300) {
                // Very recent animation change - likely already handled by new animation
                if (this.options.debug) {
                    logger.debug(`[DEBUG] Skipping completion fadeOut - rapid animation change detected (${timeSinceLastChange}ms ago)`);
                }
            } else {
                // Normal completion - fade out if this action is still current
                if (action && typeof action.fadeOut === 'function' && this.currentAction === action) {
                    try {
                        action.fadeOut(Math.max(safeFadeOutDuration, 0.1)); // Minimum 0.1s fade

                        if (this.options.debug) {
                            logger.debug(`[DEBUG] Fading out completed animation: ${category} (duration: ${safeFadeOutDuration}s)`);
                        }
                    } catch (error) {
                        logger.error(`Error during completion fadeOut: ${error.message}`);
                        // Fallback: immediately stop if fade fails
                        if (action.stop) {
                            action.stop();
                        }
                    }
                }
            }

            // Handle return to idle for non-looping animations
            if (shouldReturn && transitionSettings && transitionSettings.returnDelay > 0) {
                // Only schedule return to idle if no new animation has started
                setTimeout(async () => {
                    // Double-check that no new animation has started and we're still idle
                    const timeSinceLastCheck = performance.now() - this.lastAnimationChangeTime;
                    if (timeSinceLastCheck > transitionSettings.returnDelay * 0.8) {
                        await this._returnToIdleState();
                    } else if (this.options.debug) {
                        logger.debug(`[DEBUG] Skipping return to idle - new animation detected`);
                    }
                }, transitionSettings.returnDelay);

                if (this.options.debug) {
                    logger.debug(`[DEBUG] Scheduled return to idle in ${transitionSettings.returnDelay}ms for category: ${category}`);
                }
            }

            // Clear current animation reference after appropriate delay
            const clearDelay = Math.max(safeFadeOutDuration * 1000, 100); // Minimum 100ms delay

            setTimeout(() => {
                // Only clear if this action is still the current one
                if (this.currentAction === action) {
                    this.currentAction = null;
                    this.currentAnimationShouldLoop = false; // Clear looping flag

                    // **GRACE PERIOD FIX**: Set completion grace period timestamp
                    // This prevents BaseAnimator from immediately taking over
                    this.completionGracePeriod = performance.now();

                    if (this.options.debug) {
                        logger.debug(`[DEBUG] Cleared current action reference for category: ${category}`);
                        logger.debug(`[DEBUG] Started completion grace period (3 seconds) to prevent immediate BaseAnimator takeover`);
                    }
                }
            }, clearDelay);

        } catch (error) {
            logger.error('Error handling animation completion:', error);

            // Enhanced fallback: ensure clean state
            setTimeout(() => {
                if (this.currentAction === action) {
                    this.currentAction = null;

                    // If action is still playing, force stop it
                    if (action && action.isRunning && action.isRunning()) {
                        try {
                            if (action.stop) {
                                action.stop();
                            }
                        } catch (stopError) {
                            logger.error(`Error stopping action in fallback: ${stopError.message}`);
                        }
                    }
                }
            }, Math.max((fadeOutDuration || 0.5) * 1000, 200));
        }
    }

    /**
     * Return to idle state with smooth transition
     * @private
     */
    async _returnToIdleState() {
        try {
            // Don't return to idle if we're currently speaking or listening
            if (this.isSpeaking || this.isListening) {
                if (this.options.debug) {
                    logger.debug('Skipping return to idle - avatar is speaking or listening');
                }
                return;
            }

            // // Import animation configuration
            // const { ANIMATION_TRANSITIONS } = await import('./AnimationConfig.js');
            // const idleAnimation = ANIMATION_TRANSITIONS.returnToIdle.idleAnimation;

            // // Play idle animation with smooth transition
            // await this.playAnimation('idle', idleAnimation, 0); // 0 duration = loop indefinitely

            // if (this.options.debug) {
            //     logger.debug(`Returned to idle state: ${idleAnimation}`);
            // }

        } catch (error) {
            logger.error('Error returning to idle state:', error);
        }
    }


}
