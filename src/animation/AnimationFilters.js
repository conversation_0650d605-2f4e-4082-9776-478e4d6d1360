/**
 * AnimationFilters.js
 * 
 * This file contains filter implementations for smoothing animation data.
 * The primary implementation is the 1€ Filter (One Euro Filter) which provides
 * efficient, low-latency filtering of noisy signals.
 * 
 * Author: G<PERSON> Casiez
 * Details: https://gery.casiez.net/1euro/
 *
 * Copyright 2019 Inria
 *
 * BSD License https://opensource.org/licenses/BSD-3-Clause
 */

import { createLogger } from '../utils/logger.js';
const logger = createLogger('AnimationFilters');
/**
 * Low-pass filter implementation
 * Used by the OneEuroFilter to smooth signals
 */
export class LowPassFilter {
    /**
     * Create a new low-pass filter
     * @param {number} alpha - Filter coefficient (0-1)
     * @param {number} initval - Initial value
     */
    constructor(alpha, initval = 0.0) {
        this.y = this.s = initval;
        this.setAlpha(alpha);
        this.initialized = false;
    }

    /**
     * Set the filter coefficient
     * @param {number} alpha - Filter coefficient (0-1)
     */
    setAlpha(alpha) {
        if (alpha <= 0.0 || alpha > 1.0)
            logger.warn("alpha should be in (0.0., 1.0]");
        this.a = alpha;
    }

    /**
     * Filter a value
     * @param {number} value - Value to filter
     * @returns {number} Filtered value
     */
    filter(value) {
        let result;
        if (this.initialized)
            result = this.a * value + (1.0 - this.a) * this.s;
        else {
            result = value;
            this.initialized = true;
        }
        this.y = value;
        this.s = result;
        return result;
    }

    /**
     * Filter a value with a specific alpha
     * @param {number} value - Value to filter
     * @param {number} alpha - Filter coefficient to use
     * @returns {number} Filtered value
     */
    filterWithAlpha(value, alpha) {
        this.setAlpha(alpha);
        return this.filter(value);
    }

    /**
     * Check if the filter has a last raw value
     * @returns {boolean} True if the filter has been initialized
     */
    hasLastRawValue() {
        return this.initialized;
    }

    /**
     * Get the last raw value
     * @returns {number} Last raw value
     */
    lastRawValue() {
        return this.y;
    }

    /**
     * Get the last filtered value
     * @returns {number} Last filtered value
     */
    lastFilteredValue() {
        return this.s;
    }

    /**
     * Reset the filter
     */
    reset() {
        this.initialized = false;
    }
}

/**
 * One Euro Filter implementation
 * Provides efficient, low-latency filtering of noisy signals
 */
export class OneEuroFilter {
    /**
     * Create a new One Euro Filter
     * @param {number} freq - Sampling frequency
     * @param {number} mincutoff - Minimum cutoff frequency
     * @param {number} beta_ - Cutoff slope
     * @param {number} dcutoff - Derivative cutoff frequency
     */
    constructor(freq, mincutoff = 1.0, beta_ = 0.0, dcutoff = 1.0) {
        this.setFrequency(freq);
        this.setMinCutoff(mincutoff);
        this.setBeta(beta_);
        this.setDerivateCutoff(dcutoff);
        this.x = new LowPassFilter(this.alpha(mincutoff));
        this.dx = new LowPassFilter(this.alpha(dcutoff));
        this.lasttime = undefined;
    }

    /**
     * Calculate alpha value from cutoff frequency
     * @param {number} cutoff - Cutoff frequency
     * @returns {number} Alpha value
     */
    alpha(cutoff) {
        const te = 1.0 / this.freq;
        const tau = 1.0 / (2 * Math.PI * cutoff);
        return 1.0 / (1.0 + tau / te);
    }

    /**
     * Set the sampling frequency
     * @param {number} f - Frequency in Hz
     */
    setFrequency(f) {
        if (f <= 0) logger.warn("freq should be >0");
        this.freq = f;
    }

    /**
     * Set the minimum cutoff frequency
     * @param {number} mc - Minimum cutoff frequency
     */
    setMinCutoff(mc) {
        if (mc <= 0) logger.warn("mincutoff should be >0");
        this.mincutoff = mc;
    }

    /**
     * Set the beta parameter (cutoff slope)
     * @param {number} b - Beta value
     */
    setBeta(b) {
        this.beta_ = b;
    }

    /**
     * Set the derivative cutoff frequency
     * @param {number} dc - Derivative cutoff frequency
     */
    setDerivateCutoff(dc) {
        if (dc <= 0) logger.warn("dcutoff should be >0");
        this.dcutoff = dc;
    }

    /**
     * Reset the filter
     */
    reset() {
        this.x.reset();
        this.dx.reset();
        this.lasttime = undefined;
    }

    /**
     * Filter a value
     * @param {number} value - Value to filter
     * @param {number} timestamp - Timestamp in seconds
     * @returns {number} Filtered value
     */
    filter(value, timestamp = undefined) {
        // update the sampling frequency based on timestamps
        if (this.lasttime != undefined && timestamp != undefined && timestamp > this.lasttime)
            this.freq = 1.0 / (timestamp - this.lasttime);
        this.lasttime = timestamp;
        // estimate the current variation per second
        const dvalue = this.x.hasLastRawValue() ? (value - this.x.lastFilteredValue()) * this.freq : 0.0;
        const edvalue = this.dx.filterWithAlpha(dvalue, this.alpha(this.dcutoff));
        // use it to update the cutoff frequency
        const cutoff = this.mincutoff + this.beta_ * Math.abs(edvalue);
        // filter the given value
        return this.x.filterWithAlpha(value, this.alpha(cutoff));
    }
}
