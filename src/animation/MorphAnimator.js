/**
 * MorphAnimator.js
 * A simplified animator for meshes with morph targets
 * Extracted from talkinghead.mjs to provide a more maintainable solution
 */

import * as THREE from 'three';
import { createLogger } from '../utils/logger.js';
import { BaseAnimator } from './BaseAnimator.js';
import {
    MORPH_TARGET_CONFIG,
    MT_CUSTOMS,
    VISEME_NAMES,
    REQUIRED_MORPH_TARGETS,
    INITIAL_MORPH_VALUES
} from './AnimationConfig.js';

import {
    applyEasing,
    hasMorphTargets
} from './AnimationUtils.js';

// Create a logger for this module
const logger = createLogger('MorphAnimator');

export class MorphAnimator extends BaseAnimator {
    /**
     * Constructor for MorphAnimator
     * @param {THREE.Object3D} mesh - The mesh with morph targets to animate
     * @param {Object} options - Configuration options
     */
    constructor(mesh, options = {}) {
        // Call parent constructor
        super(mesh, options);

        if (this.options.debug) {
            logger.debug('[DEBUG] MorphAnimator constructor: debug mode enabled');
        }

        // Set up morph target configuration from the unified config
        this.mtEasing = MORPH_TARGET_CONFIG.defaults.easing;
        this.mtAccDefault = MORPH_TARGET_CONFIG.defaults.acceleration;
        this.mtMaxVDefault = MORPH_TARGET_CONFIG.defaults.maxVelocity;
        this.mtBaselineDefault = MORPH_TARGET_CONFIG.defaults.baseline;
        this.mtMinDefault = MORPH_TARGET_CONFIG.defaults.min;
        this.mtMaxDefault = MORPH_TARGET_CONFIG.defaults.max;

        // Set up morph target exceptions
        this.mtAccExceptions = MORPH_TARGET_CONFIG.exceptions.acceleration;
        this.mtMaxVExceptions = MORPH_TARGET_CONFIG.exceptions.maxVelocity;
        this.mtBaselineExceptions = MORPH_TARGET_CONFIG.exceptions.baseline;
        this.mtMinExceptions = MORPH_TARGET_CONFIG.exceptions.min;
        this.mtMaxExceptions = MORPH_TARGET_CONFIG.exceptions.max;

        // Set up morph target limits and change handlers
        this.mtLimits = MORPH_TARGET_CONFIG.constraints;
        this.mtOnChange = MORPH_TARGET_CONFIG.changeHandlers;
        this.mtRandomized = MORPH_TARGET_CONFIG.randomized;

        // Initialize morph target dictionaries
        this.morphTargetDictionary = {};
        this.morphTargetInfluences = [];
        this.mtAvatar = {}; // Initialize mtAvatar as an empty object

        // Viseme names for lip sync from config - initialize BEFORE setupMorphTargets
        this.visemeNames = VISEME_NAMES;

        // Find morph targets
        this.morphs = [];
        this.findMorphTargets(mesh);

        // Set up morph targets after finding them
        this.setupMorphTargets();

        // Initialize the animator
        this.initialize();
    }

    /**
     * Find morph targets in the mesh
     * Uses the shared hasMorphTargets utility function
     * @param {THREE.Object3D} mesh - The mesh to search
     */
    findMorphTargets(mesh) {
        // Get detailed morph target information
        const morphInfo = hasMorphTargets(mesh, true);

        if (typeof morphInfo === 'object' && morphInfo.hasMorphTargets) {
            // Log morph target information at debug level
            logger.debug(`Found ${morphInfo.morphTargetInfo.count} morph targets in ${morphInfo.morphTargetInfo.meshes.length} meshes`);

            // Find all meshes with morph targets
            mesh.traverse(object => {
                if (object.morphTargetInfluences &&
                    object.morphTargetInfluences.length > 0 &&
                    object.morphTargetDictionary) {
                    this.morphs.push(object);
                    if (this.options.debug) {
                        logger.debug(`[DEBUG] Found morph mesh: ${object.name || 'unnamed'} with targets:`, Object.keys(object.morphTargetDictionary));
                    }
                }
            });
        } else {
            if (this.options.debug) {
                logger.warn('[DEBUG] No morph targets found in mesh');
            }
            logger.warn('No morph targets found in mesh');
        }
    }

    /**
     * Set up morph target dictionary and influences
     */
    setupMorphTargets() {
        // Create a temporary dictionary to collect morph targets
        const mtTemp = {};

        // Use required morph targets from configuration
        // This ensures all necessary morph targets exist even if not found in the mesh
        const requiredMorphs = [...REQUIRED_MORPH_TARGETS];

        // Also add viseme names without the prefix if not already included
        // Add safety check to ensure visemeNames is defined
        if (this.visemeNames && Array.isArray(this.visemeNames)) {
            this.visemeNames.forEach(name => {
                if (!requiredMorphs.includes(name) && !requiredMorphs.includes('viseme_' + name)) {
                    requiredMorphs.push(name);
                }
            });
        } else {
            // If visemeNames is not defined, use the imported VISEME_NAMES directly
            VISEME_NAMES.forEach(name => {
                if (!requiredMorphs.includes(name) && !requiredMorphs.includes('viseme_' + name)) {
                    requiredMorphs.push(name);
                }
            });
        }

        // Create placeholders for all required morphs
        requiredMorphs.forEach(name => {
            // Get baseline value from configuration or use default
            const baseline = this.mtBaselineExceptions[name] !== undefined ?
                this.mtBaselineExceptions[name] : this.mtBaselineDefault;

            // Get min/max values from configuration or use defaults
            const min = this.mtMinExceptions[name] !== undefined ?
                this.mtMinExceptions[name] : this.mtMinDefault;

            const max = this.mtMaxExceptions[name] !== undefined ?
                this.mtMaxExceptions[name] : this.mtMaxDefault;

            // Get acceleration and max velocity from configuration or use defaults
            const acc = this.mtAccExceptions[name] !== undefined ?
                this.mtAccExceptions[name] : this.mtAccDefault;

            const maxV = this.mtMaxVExceptions[name] !== undefined ?
                this.mtMaxVExceptions[name] : this.mtMaxVDefault;

            // Create morph target entry as a placeholder
            mtTemp[name] = {
                meshes: [],
                indices: [],
                value: baseline !== null ? baseline : 0,
                baseline: baseline !== null ? baseline : 0,
                min: min,
                max: max,
                acc: acc,
                maxV: maxV,
                velocity: 0,
                target: baseline !== null ? baseline : 0,
                needsUpdate: false,
                isCustom: MT_CUSTOMS.includes(name),
                isPlaceholder: true // Mark as placeholder
            };
        });

        // Now combine all actual morph targets from all meshes
        if (this.morphs && this.morphs.length > 0) {
            this.morphs.forEach(morphMesh => {
                if (morphMesh.morphTargetDictionary && morphMesh.morphTargetInfluences) {
                    Object.entries(morphMesh.morphTargetDictionary).forEach(([name, index]) => {
                        // If this morph target doesn't exist in our temp dictionary, create it
                        if (!mtTemp[name]) {
                            // Get baseline value from configuration or use default
                            const baseline = this.mtBaselineExceptions[name] !== undefined ?
                                this.mtBaselineExceptions[name] : this.mtBaselineDefault;

                            // Get min/max values from configuration or use defaults
                            const min = this.mtMinExceptions[name] !== undefined ?
                                this.mtMinExceptions[name] : this.mtMinDefault;

                            const max = this.mtMaxExceptions[name] !== undefined ?
                                this.mtMaxExceptions[name] : this.mtMaxDefault;

                            // Get acceleration and max velocity from configuration or use defaults
                            const acc = this.mtAccExceptions[name] !== undefined ?
                                this.mtAccExceptions[name] : this.mtAccDefault;

                            const maxV = this.mtMaxVExceptions[name] !== undefined ?
                                this.mtMaxVExceptions[name] : this.mtMaxVDefault;

                            // Create morph target entry
                            mtTemp[name] = {
                                meshes: [],
                                indices: [],
                                value: baseline !== null ? baseline : 0,
                                baseline: baseline !== null ? baseline : 0,
                                min: min,
                                max: max,
                                acc: acc,
                                maxV: maxV,
                                velocity: 0,
                                target: baseline !== null ? baseline : 0,
                                needsUpdate: true,
                                isCustom: MT_CUSTOMS.includes(name),
                                isPlaceholder: false
                            };
                        } else {
                            // If it was a placeholder, mark it as no longer a placeholder
                            mtTemp[name].isPlaceholder = false;
                        }

                        // Add mesh and index to the morph target
                        mtTemp[name].meshes.push(morphMesh);
                        mtTemp[name].indices.push(index);
                    });
                }
            });
        }

        // Set the morph target dictionary and mtAvatar
        this.morphTargetDictionary = mtTemp;
        this.mtAvatar = mtTemp;
    }

    /**
     * Override the base class initialize method to add morph-specific initialization
     */
    initialize() {
        // Call the parent class initialize method
        super.initialize();

        // Add any morph-specific initialization here if needed in the future
    }

    /**
     * Override the base class setInitialPose to add morph-specific initial pose
     * Based on the original talkinghead.mjs implementation
     */
    setInitialPose() {
        // First call the base class method to set up bone positions
        super.setInitialPose();

        // Reset all morph targets to ensure we start from a clean state
        this.resetMorphTargets();

        // Apply initial morph target values from configuration
        Object.entries(INITIAL_MORPH_VALUES).forEach(([name, value]) => {
            if (this.hasMorphTarget(name)) {
                this.setMorphTargetValue(name, value);
            }
        });

        // Apply the morph targets immediately
        this.applyMorphTargets();

        // Set mood to neutral (this will apply mood-specific baseline values)
        if (typeof this.setMood === 'function') {
            this.setMood('neutral');
        }
    }

    /**
     * Check if a morph target exists
     * @param {string} name - Morph target name
     * @returns {boolean} True if the morph target exists
     */
    hasMorphTarget(name) {
        // Check if the morph target exists directly
        if (this.morphTargetDictionary[name]) {
            return true;
        }

        // Try with alternative naming conventions
        const alternatives = [
            name.toLowerCase(),
            name.toUpperCase(),
            name.charAt(0).toUpperCase() + name.slice(1),
            'viseme_' + name,
            name.replace('viseme_', '')
        ];

        for (const alt of alternatives) {
            if (this.morphTargetDictionary[alt]) {
                return true;
            }
        }

        return false;
    }

    /**
     * Override the base class addIdleAnimations method to add morph-specific idle animations
     * Enhanced with better error handling and logging
     */
    addIdleAnimations() {
        try {
            // Call the parent class method to add standard idle animations
            super.addIdleAnimations();

            // Add morph-specific idle animations
            if (this.morphTargetDictionary) {
                this.addMorphSpecificIdleAnimations();
            }

            // Log success at debug level
            logger.debug('MorphAnimator idle animations added successfully');
        } catch (error) {
            logger.error('Error in MorphAnimator.addIdleAnimations:', error);
        }
    }

    /**
     * Add morph-specific idle animations
     * Separated from addIdleAnimations for better code organization
     */
    addMorphSpecificIdleAnimations() {
        try {
            // Add subtle random eye movement
            if (this.hasMorphTarget('eyeLookUp') && this.hasMorphTarget('eyeLookDown') &&
                this.hasMorphTarget('eyeLookLeft') && this.hasMorphTarget('eyeLookRight')) {

                const eyeMovementAnim = {
                    id: this.nextAnimId++,
                    type: 'idle',
                    startTime: this.animClock,
                    duration: 5000,
                    loop: true,
                    morphTargets: {
                        eyeLookUp: 0.1,
                        eyeLookDown: 0.1,
                        eyeLookLeft: 0.1,
                        eyeLookRight: 0.1
                    }
                };

                this.animQueue.push(eyeMovementAnim);
                logger.debug('Added morph-specific eye movement animation');
            }

            // Add subtle random eyebrow movement
            if (this.hasMorphTarget('browInnerUp') || this.hasMorphTarget('browOuterUp')) {
                const browMovementAnim = {
                    id: this.nextAnimId++,
                    type: 'idle',
                    startTime: this.animClock,
                    duration: 8000,
                    loop: true,
                    morphTargets: {}
                };

                if (this.hasMorphTarget('browInnerUp')) {
                    browMovementAnim.morphTargets.browInnerUp = 0.05;
                }

                if (this.hasMorphTarget('browOuterUp')) {
                    browMovementAnim.morphTargets.browOuterUp = 0.05;
                }

                this.animQueue.push(browMovementAnim);
                logger.debug('Added morph-specific eyebrow movement animation');
            }
        } catch (error) {
            logger.error('Error adding morph-specific idle animations:', error);
        }
    }

    /**
     * Apply animation based on type
     * Override the base class method to handle morph-specific animations
     * @param {Object} anim - Animation data
     * @param {number} progress - Animation progress (0-1)
     */
    applyAnimation(anim, progress) {
        // Apply easing to all animations for more natural movement
        const easedProgress = applyEasing(progress, anim.easing || 'easeInOutQuad');

        // Handle morph-specific animation types first
        switch (anim.type) {
            case 'morph':
                this.applyMorphAnimation(anim, easedProgress);
                return; // Return early to avoid calling the base class method
            case 'viseme':
                this.applyVisemeAnimation(anim, easedProgress);
                return; // Return early to avoid calling the base class method
            case 'expression':
                this.applyExpressionAnimation(anim, easedProgress);
                return; // Return early to avoid calling the base class method
        }

        // For other animation types, call the base class method
        super.applyAnimation(anim, progress);
    }

    /**
     * Apply morph animation
     * @param {Object} anim - Animation data
     * @param {number} progress - Animation progress (0-1)
     */
    applyMorphAnimation(anim, progress) {
        // Use easing function for smooth movement
        const easedProgress = applyEasing(progress, 'easeInOutQuad');

        // Apply morph target values
        if (anim.morphTargets) {
            Object.entries(anim.morphTargets).forEach(([name, value]) => {
                this.setMorphTargetValue(name, value * easedProgress);
            });
        }
    }

    /**
     * Apply viseme animation for lip sync
     * @param {Object} anim - Animation data
     * @param {number} progress - Animation progress (0-1)
     */
    applyVisemeAnimation(anim, progress) {
        // Use easing function for smooth movement
        const easedProgress = applyEasing(progress, 'easeInOutQuad');

        // Apply viseme morph targets
        if (anim.viseme) {
            const visemeTargets = this.getVisemeTargets(anim.viseme);
            Object.entries(visemeTargets).forEach(([name, value]) => {
                // Only apply if the morph target exists
                if (this.hasMorphTarget(name)) {
                    this.setMorphTargetValue(name, value * easedProgress);
                }
            });
        }
    }

    /**
     * Apply viseme directly (not through animation queue)
     * @param {string} viseme - Viseme name
     * @param {number} intensity - Animation intensity (0-1)
     */
    applyViseme(viseme, intensity = 1.0) {
        // Get morph targets for this viseme
        const targets = this.getVisemeTargets(viseme);

        // Apply each morph target with the specified intensity
        for (const [name, value] of Object.entries(targets)) {
            // Only apply if the morph target exists
            if (this.hasMorphTarget(name)) {
                this.setMorphTargetValue(name, value * intensity);
            }
        }

        // Apply the morph targets immediately
        this.applyMorphTargets();
    }

    /**
     * Apply viseme with safety checks and smooth transitions
     * @param {string} viseme - Viseme name
     * @param {number} intensity - Animation intensity (0-1)
     * @param {boolean} smooth - Whether to smooth the transition
     * @returns {Array} List of applied morph target names
     */
    applyVisemeSafe(viseme, intensity = 1.0, smooth = true) {
        // Get morph targets for this viseme
        const targets = this.getVisemeTargets(viseme);
        const appliedTargets = [];

        // Apply each morph target with the specified intensity
        for (const [name, value] of Object.entries(targets)) {
            // Only apply if the morph target exists
            if (this.hasMorphTarget(name)) {
                // If smooth transition is enabled, use a smaller value for smoother animation
                const targetValue = smooth ? value * intensity * 0.8 : value * intensity;

                // Apply the morph target
                if (this.setMorphTargetValue(name, targetValue)) {
                    appliedTargets.push(name);
                }
            }
        }

        // Apply the morph targets immediately
        this.applyMorphTargets();

        // Return the list of applied targets for debugging
        return appliedTargets;
    }

    /**
     * Apply expression animation
     * @param {Object} anim - Animation data
     * @param {number} progress - Animation progress (0-1)
     */
    applyExpressionAnimation(anim, progress) {
        // Use easing function for smooth movement
        const easedProgress = applyEasing(progress, 'easeInOutQuad');

        // Apply expression morph targets
        if (anim.expression) {
            const expressionTargets = this.getExpressionTargets(anim.expression);
            Object.entries(expressionTargets).forEach(([name, value]) => {
                this.setMorphTargetValue(name, value * easedProgress);
            });
        }
    }

    /**
     * Get morph targets for a viseme
     * Based on the original talkinghead.mjs implementation
     * Using extremely conservative values to avoid distortion
     * @param {string} viseme - Viseme name
     * @returns {Object} Morph target values
     */
    getVisemeTargets(viseme) {
        // Viseme mapping based on original implementation
        // Using extremely conservative values to avoid distortion
        const visemeMap = {
            // Silence - mouth closed naturally
            'sil': {
                'mouthClose': 0.5,
                'jawOpen': 0.0,
                'mouthOpen': 0.0
            },

            // Bilabial consonants (p, b, m) - lips pressed together
            'PP': {
                'mouthClose': 0.6,
                'jawOpen': 0.0,
                'mouthOpen': 0.0
            },

            // Labiodental consonants (f, v) - lower lip touches upper teeth
            'FF': {
                'mouthClose': 0.4,
                'mouthFunnel': 0.1,
                'jawOpen': 0.05,
                'mouthOpen': 0.05
            },

            // Dental consonants (th) - tongue between teeth
            'TH': {
                'mouthClose': 0.2,
                'tongueOut': 0.2,
                'jawOpen': 0.1,
                'mouthOpen': 0.1
            },

            // Alveolar consonants (t, d, n) - tongue tip touches ridge
            'DD': {
                'mouthClose': 0.2,
                'jawOpen': 0.1,
                'mouthOpen': 0.1
            },

            // Velar consonants (k, g) - back of tongue touches soft palate
            'kk': {
                'mouthClose': 0.2,
                'jawOpen': 0.15,
                'mouthOpen': 0.15
            },

            // Postalveolar consonants (ch, j, sh) - tongue near ridge
            'CH': {
                'mouthClose': 0.2,
                'mouthFunnel': 0.2,
                'jawOpen': 0.1,
                'mouthOpen': 0.1
            },

            // Sibilants (s, z) - high-pitched sounds with tongue near teeth
            'SS': {
                'mouthSmile': 0.15,
                'mouthClose': 0.2,
                'jawOpen': 0.05,
                'mouthOpen': 0.05,
                'mouthStretch': 0.1
            },

            // Nasal consonants (n, ng) - air through nose
            'nn': {
                'mouthClose': 0.2,
                'jawOpen': 0.05,
                'mouthOpen': 0.05
            },

            // Approximants (r, l) - partial closure
            'RR': {
                'mouthClose': 0.2,
                'mouthFunnel': 0.15,
                'jawOpen': 0.1,
                'mouthOpen': 0.1
            },

            // Open vowel (a) - wide open mouth
            'aa': {
                'jawOpen': 0.4,
                'mouthOpen': 0.4,
                'mouthClose': 0.0
            },

            // Mid-front vowel (e) - medium open mouth with smile
            'E': {
                'jawOpen': 0.25,
                'mouthOpen': 0.25,
                'mouthSmile': 0.15,
                'mouthClose': 0.1
            },

            // High-front vowel (i) - less open with spread lips
            'ih': {
                'jawOpen': 0.2,
                'mouthOpen': 0.2,
                'mouthClose': 0.2
            },

            // Mid-back rounded vowel (o) - rounded lips
            'oh': {
                'jawOpen': 0.3,
                'mouthOpen': 0.3,
                'mouthFunnel': 0.25,
                'mouthClose': 0.1
            },

            // High-back rounded vowel (u) - pursed lips
            'ou': {
                'mouthFunnel': 0.4,
                'jawOpen': 0.15,
                'mouthOpen': 0.15,
                'mouthClose': 0.2
            }
        };

        // If the requested viseme isn't in our map, return a neutral mouth position
        return visemeMap[viseme] || {
            'mouthClose': 0.5,
            'jawOpen': 0.0,
            'mouthOpen': 0.0
        };
    }

    /**
     * Get morph targets for an expression
     * @param {string} expression - Expression name
     * @returns {Object} Morph target values
     */
    getExpressionTargets(expression) {
        // Basic expression mapping to morph targets
        const expressionMap = {
            'neutral': {},
            'smile': { 'mouthSmile': 1.0, 'cheekSquint': 0.3 },
            'sad': { 'mouthFrown': 0.7, 'browInnerUp': 0.5 },
            'angry': { 'browDown': 0.7, 'eyeSquint': 0.5, 'mouthFrown': 0.5 },
            'surprised': { 'browUp': 0.7, 'eyeWide': 0.7, 'jawOpen': 0.3 },
            'disgusted': { 'noseSneer': 0.7, 'mouthFrown': 0.5 },
            'fearful': { 'browInnerUp': 0.5, 'eyeWide': 0.5 },
            'happy': { 'mouthSmile': 1.0, 'cheekSquint': 0.5 }
        };

        return expressionMap[expression] || {};
    }

    /**
     * Apply audio-based animations
     * Extends the base class method with morph-specific features
     * Based on the original talkinghead.mjs implementation
     * Uses extremely conservative values to avoid distortion
     */
    applyAudioBasedAnimations() {
        // Get normalized volume from base class
        // This also handles bone-based animations like head and neck movement
        const normalizedVolume = super.applyAudioBasedAnimations();

        // Apply morph-specific animations for lip sync and facial expressions
        if (this.morphTargetDictionary) {
            // Apply jaw and mouth movement based on volume
            // Using extremely conservative values to avoid distortion
            if (this.hasMorphTarget('jawOpen')) {
                this.setMorphTargetValue('jawOpen', normalizedVolume * 0.4);
            }

            if (this.hasMorphTarget('mouthOpen')) {
                this.setMorphTargetValue('mouthOpen', normalizedVolume * 0.5);
            }

            // Reduce mouth closure when speaking
            if (this.hasMorphTarget('mouthClose')) {
                // Start from the neutral position (0.5) and reduce based on volume
                this.setMorphTargetValue('mouthClose', Math.max(0, 0.5 - normalizedVolume * 0.5));
            }

            // Add very subtle variation to mouth shapes for more natural speech
            if (this.hasMorphTarget('mouthFunnel')) {
                const mouthVariation = Math.sin(this.animClock * 0.01) * 0.05;
                this.setMorphTargetValue('mouthFunnel', normalizedVolume * 0.15 * (1 + mouthVariation));
            }

            if (this.hasMorphTarget('mouthSmile')) {
                // Very minimal smile during speech
                this.setMorphTargetValue('mouthSmile', normalizedVolume * 0.05);
            }

            // Add occasional eye blinks during speech for realism
            if (Math.random() < 0.002) {
                this.addBlinkAnimation();
            }

            // Only add eyebrow movement for very high volume
            if (normalizedVolume > 0.7 && this.hasMorphTarget('browUp')) {
                this.setMorphTargetValue('browUp', (normalizedVolume - 0.7) * 0.15);
            }
        }

        return normalizedVolume;
    }

    /**
     * Apply current morph target values to the mesh
     * Called at the end of each animation frame
     * Based on the original talkinghead.mjs implementation
     */
    applyMorphTargets() {
        // Safety check - make sure morphTargetDictionary is set
        if (!this.morphTargetDictionary) {
            logger.warn('morphTargetDictionary not set, skipping morph target application');
            return;
        }

        // Update morph target values with physics-based animation
        this.updateMorphTargetPhysics();

        // Apply limits and constraints from configuration
        this.applyMorphTargetLimits();

        // Apply values to the actual mesh
        Object.entries(this.morphTargetDictionary).forEach(([name, data]) => {
            // Skip if data is invalid or is a placeholder
            if (!data || typeof data !== 'object' || data.isPlaceholder) {
                return;
            }

            // Skip if no meshes to apply to
            if (!data.meshes || !Array.isArray(data.meshes) || data.meshes.length === 0) {
                return;
            }

            try {
                data.meshes.forEach((mesh, i) => {
                    // Skip if mesh or morphTargetInfluences is invalid
                    if (!mesh || !mesh.morphTargetInfluences) {
                        return;
                    }

                    const index = data.indices[i];
                    if (index !== undefined && index >= 0 && index < mesh.morphTargetInfluences.length) {
                        mesh.morphTargetInfluences[index] = data.value;
                    }
                });
            } catch (error) {
                logger.warn(`Error applying morph target ${name}: ${error.message}`);
            }
        });
    }

    /**
     * Update morph target physics for smooth animation
     * Based on the original talkinghead.mjs implementation
     */
    updateMorphTargetPhysics() {
        // Safety check - make sure morphTargetDictionary is set
        if (!this.morphTargetDictionary) {
            logger.warn('morphTargetDictionary not set, skipping physics update');
            return;
        }

        const dt = 1 / 60; // Assume 60fps for physics calculations

        Object.entries(this.morphTargetDictionary).forEach(([name, data]) => {
            // Skip if data is invalid
            if (!data || typeof data !== 'object') {
                logger.warn(`Invalid morph target data for ${name}, skipping`);
                return;
            }

            // Skip if no update needed
            if (!data.needsUpdate && Math.abs(data.value - data.target) < 0.001) {
                return;
            }

            try {
                // Ensure all required properties exist with defaults if missing
                if (data.target === undefined) data.target = data.value || 0;
                if (data.acc === undefined) data.acc = this.mtAccDefault;
                if (data.maxV === undefined) data.maxV = this.mtMaxVDefault;
                if (data.velocity === undefined) data.velocity = 0;
                if (data.min === undefined) data.min = this.mtMinDefault;
                if (data.max === undefined) data.max = this.mtMaxDefault;

                // Calculate acceleration towards target
                const diff = data.target - data.value;
                const acc = diff * data.acc;

                // Update velocity with acceleration
                data.velocity += acc;

                // Apply damping
                data.velocity *= 0.9;

                // Limit velocity to max value
                if (Math.abs(data.velocity) > data.maxV) {
                    data.velocity = Math.sign(data.velocity) * data.maxV;
                }

                // Update value with velocity
                data.value += data.velocity * dt;

                // Clamp value to min/max range
                data.value = Math.max(data.min, Math.min(data.max, data.value));

                // Mark as updated
                data.needsUpdate = false;
            } catch (error) {
                logger.warn(`Error updating physics for morph target ${name}: ${error.message}`);
            }
        });
    }

    /**
     * Apply limits and constraints to morph target values
     * Based on the original talkinghead.mjs implementation
     */
    applyMorphTargetLimits() {
        // Safety check - make sure mtAvatar is set
        if (!this.mtAvatar) {
            logger.warn('mtAvatar not set, using morphTargetDictionary as fallback');
            this.mtAvatar = this.morphTargetDictionary || {};
        }

        // Safety check - make sure morphTargetDictionary is set
        if (!this.morphTargetDictionary) {
            logger.warn('morphTargetDictionary not set, using empty object as fallback');
            this.morphTargetDictionary = {};
            return; // Skip applying limits if no dictionary
        }

        // Apply limits from configuration
        if (this.mtLimits) {
            Object.entries(this.mtLimits).forEach(([name, limitFn]) => {
                if (this.morphTargetDictionary[name]) {
                    try {
                        // Call the limit function with the animator as context
                        const limitedValue = limitFn.call(this, this.morphTargetDictionary[name].value);
                        this.morphTargetDictionary[name].value = limitedValue;
                    } catch (error) {
                        logger.warn(`Error applying limit for morph target ${name}: ${error.message}`);
                    }
                }
            });
        }

        // Apply onChange handlers from configuration
        if (this.mtOnChange) {
            Object.entries(this.mtOnChange).forEach(([name, changeFn]) => {
                if (this.morphTargetDictionary[name] && this.morphTargetDictionary[name].needsUpdate) {
                    try {
                        // Call the onChange function with the animator as context
                        changeFn.call(this);
                    } catch (error) {
                        logger.warn(`Error applying onChange for morph target ${name}: ${error.message}`);
                    }
                }
            });
        }
    }

    /**
     * Set a morph target value
     * @param {string} name - Morph target name
     * @param {number} value - Morph target value (0-1)
     * @param {boolean} immediate - Whether to apply the value immediately (default: false)
     * @returns {boolean} True if the morph target was set successfully
     */
    setMorphTargetValue(name, value, immediate = false) {
        // Check if the morph target exists directly
        if (this.morphTargetDictionary[name]) {
            // If immediate, set the value directly
            if (immediate) {
                this.morphTargetDictionary[name].value = value;
            } else {
                // Otherwise, set the target value for physics-based animation
                this.morphTargetDictionary[name].target = value;
                this.morphTargetDictionary[name].needsUpdate = true;

                // Call any onChange handlers for this morph target
                if (this.mtOnChange[name]) {
                    this.mtOnChange[name].call(this);
                }
            }
            return true;
        }

        // Try with alternative naming conventions
        const alternatives = [
            name.toLowerCase(),
            name.toUpperCase(),
            name.charAt(0).toUpperCase() + name.slice(1),
            'viseme_' + name,
            name.replace('viseme_', '')
        ];

        for (const alt of alternatives) {
            if (this.morphTargetDictionary[alt]) {
                // If immediate, set the value directly
                if (immediate) {
                    this.morphTargetDictionary[alt].value = value;
                } else {
                    // Otherwise, set the target value for physics-based animation
                    this.morphTargetDictionary[alt].target = value;
                    this.morphTargetDictionary[alt].needsUpdate = true;

                    // Call any onChange handlers for this morph target
                    if (this.mtOnChange[alt]) {
                        this.mtOnChange[alt].call(this);
                    }
                }
                return true;
            }
        }

        // If we still can't find it, log a warning (only in debug mode)
        if (this.options.debug && value > 0) {
            logger.warn(`Morph target not found: ${name}`);
        }

        return false;
    }

    /**
     * Reset all morph target values
     * @param {boolean} immediate - Whether to apply the reset immediately (default: false)
     */
    resetMorphTargets(immediate = false) {
        Object.keys(this.morphTargetDictionary).forEach(name => {
            const baseline = this.morphTargetDictionary[name].baseline;

            if (immediate) {
                // Set value directly
                this.morphTargetDictionary[name].value = baseline;
                this.morphTargetDictionary[name].velocity = 0;
            } else {
                // Set target for physics-based animation
                this.morphTargetDictionary[name].target = baseline;
                this.morphTargetDictionary[name].needsUpdate = true;
            }
        });

        // Apply immediately if requested
        if (immediate) {
            this.applyMorphTargets();
        }
    }

    /**
     * Add blink animation
     * Enhanced with more natural blinking based on the original talkinghead.mjs implementation
     * Implements the abstract method from BaseAnimator
     */
    addBlinkAnimation() {
        // Create a more natural blink with three phases:
        // 1. Close eyes quickly
        // 2. Hold briefly
        // 3. Open eyes slightly more slowly

        // Phase 1: Close eyes quickly
        const blinkCloseAnim = {
            id: this.nextAnimId++,
            type: 'morph',
            startTime: this.animClock,
            duration: 80, // Faster close
            easingType: 'easeIn',
            morphTargets: {
                'eyeBlink': 1.0,
                'eyeBlinkLeft': 1.0,
                'eyeBlinkRight': 1.0,
                'eyeSquint': 0.2 // Slight squint for more natural look
            }
        };

        // Phase 2: Hold briefly
        const blinkHoldAnim = {
            id: this.nextAnimId++,
            type: 'morph',
            startTime: this.animClock + 80, // Start after close
            duration: 30, // Brief hold
            morphTargets: {
                'eyeBlink': 1.0,
                'eyeBlinkLeft': 1.0,
                'eyeBlinkRight': 1.0,
                'eyeSquint': 0.2
            }
        };

        // Phase 3: Open eyes slightly more slowly
        const blinkOpenAnim = {
            id: this.nextAnimId++,
            type: 'morph',
            startTime: this.animClock + 110, // Start after hold
            duration: 120, // Slower open
            easingType: 'easeOut',
            morphTargets: {
                'eyeBlink': 0.0,
                'eyeBlinkLeft': 0.0,
                'eyeBlinkRight': 0.0,
                'eyeSquint': 0.0
            }
        };

        // Add all phases to the animation queue
        this.animQueue.push(blinkCloseAnim);
        this.animQueue.push(blinkHoldAnim);
        this.animQueue.push(blinkOpenAnim);

        // Occasionally add a double blink (about 15% of the time)
        if (Math.random() < 0.15) {
            // Add a second blink after a short delay
            const secondBlinkDelay = 230 + Math.random() * 100;

            // Phase 1: Close eyes quickly (second blink)
            const secondBlinkCloseAnim = {
                id: this.nextAnimId++,
                type: 'morph',
                startTime: this.animClock + secondBlinkDelay,
                duration: 80,
                easingType: 'easeIn',
                morphTargets: {
                    'eyeBlink': 1.0,
                    'eyeBlinkLeft': 1.0,
                    'eyeBlinkRight': 1.0,
                    'eyeSquint': 0.2
                }
            };

            // Phase 2: Hold briefly (second blink)
            const secondBlinkHoldAnim = {
                id: this.nextAnimId++,
                type: 'morph',
                startTime: this.animClock + secondBlinkDelay + 80,
                duration: 30,
                morphTargets: {
                    'eyeBlink': 1.0,
                    'eyeBlinkLeft': 1.0,
                    'eyeBlinkRight': 1.0,
                    'eyeSquint': 0.2
                }
            };

            // Phase 3: Open eyes slightly more slowly (second blink)
            const secondBlinkOpenAnim = {
                id: this.nextAnimId++,
                type: 'morph',
                startTime: this.animClock + secondBlinkDelay + 110,
                duration: 120,
                easingType: 'easeOut',
                morphTargets: {
                    'eyeBlink': 0.0,
                    'eyeBlinkLeft': 0.0,
                    'eyeBlinkRight': 0.0,
                    'eyeSquint': 0.0
                }
            };

            // Add second blink to the animation queue
            this.animQueue.push(secondBlinkCloseAnim);
            this.animQueue.push(secondBlinkHoldAnim);
            this.animQueue.push(secondBlinkOpenAnim);

            // logger.info('Added double blink animation');
        } else {
            // logger.info('Added blink animation');
        }
    }

    /**
     * Add head movement animation
     * Extends the base class method with morph target animations
     */
    addHeadMovementAnimation() {
        // First call the base class method to handle bone animations
        super.addHeadMovementAnimation();

        // Add morph target animations specific to MorphAnimator
        const morphHeadAnim = {
            id: this.nextAnimId++,
            type: 'morph',
            startTime: this.animClock,
            duration: 2000 + Math.random() * 1000,
            morphTargets: {
                'headUp': (Math.random() - 0.5) * 0.3 * this.options.idleAnimationIntensity,
                'headDown': (Math.random() - 0.5) * 0.3 * this.options.idleAnimationIntensity,
                'headLeft': (Math.random() - 0.5) * 0.3 * this.options.idleAnimationIntensity,
                'headRight': (Math.random() - 0.5) * 0.3 * this.options.idleAnimationIntensity
            }
        };

        this.animQueue.push(morphHeadAnim);
        logger.debug('Added morph-specific head movement animation');
    }

    /**
     * Make the avatar look at the camera with morph-specific enhancements
     * @param {number} duration - Duration of the animation in milliseconds
     */
    lookAtCamera(duration = 1000) {
        // Call the base class method to handle bone animations
        super.lookAtCamera(duration);

        // Create morph-specific look at camera animation template
        const morphLookAtTemplate = {
            name: 'morphLookAt',
            dt: [duration],
            vs: {
                headUp: [0.1],
                headLeft: [0],
                headRight: [0],
                eyeSquint: [0],
                browUp: [0.2],
                eyeWide: [0.15]  // Add slightly wider eyes for attentiveness
            }
        };

        // Create animation using the factory
        const lookAtAnim = this.animFactory(morphLookAtTemplate);

        if (lookAtAnim) {
            // Remove any existing head movement morph animations to avoid conflicts
            this.animQueue = this.animQueue.filter(anim =>
                anim.type !== 'morphLookAt' &&
                !(anim.template && anim.template.name === 'morphLookAt')
            );

            // Add the new morph animation
            this.animQueue.push(lookAtAnim);
            logger.debug('Added morph-specific lookAtCamera animation');
        } else {
            logger.error('Failed to create morph lookAtCamera animation');
        }
    }

    /**
     * Set mood/expression using animFactory
     * @param {string} mood - Mood name ('neutral', 'happy', 'sad', etc.)
     */
    setMood(mood) {
        // Get expression targets for the mood
        const expressionTargets = this.getExpressionTargets(mood);

        if (!expressionTargets || Object.keys(expressionTargets).length === 0) {
            logger.warn(`No expression targets found for mood: ${mood}`);
            return;
        }

        // Create expression animation template
        const expressionTemplate = {
            name: 'expression',
            dt: [1000], // 1 second duration
            vs: expressionTargets
        };

        // Create animation using the factory
        const expressionAnim = this.animFactory(expressionTemplate);

        if (expressionAnim) {
            // Remove any existing expression animations
            this.animQueue = this.animQueue.filter(anim =>
                anim.type !== 'expression' &&
                !(anim.template && anim.template.name === 'expression')
            );

            this.animQueue.push(expressionAnim);
            logger.debug(`Set mood to: ${mood} using animFactory`);
        } else {
            logger.error(`Failed to create expression animation for mood: ${mood}`);
        }
    }

    /**
     * Generate lip sync for text using animFactory
     * This creates viseme animations using the animation factory
     * @param {string} text - Text to generate lip sync for
     * @param {string} language - Language code ('en' or 'zh')
     * @returns {Array} Array of viseme animations
     */
    generateLipSyncForText(text, language = 'en') {
        if (!text) return [];

        logger.debug(`Generating lip sync for text in ${language} language using animFactory`);

        const visemeAnims = [];
        let currentTime = 0;

        // Simple mapping of characters to visemes - enhanced for better mouth movement
        const charToViseme = {
            'a': 'aa', 'e': 'E', 'i': 'ih', 'o': 'oh', 'u': 'ou',
            'p': 'PP', 'b': 'PP', 'm': 'PP',
            'f': 'FF', 'v': 'FF',
            't': 'DD', 'd': 'DD', 'n': 'nn',
            's': 'SS', 'z': 'SS',
            'r': 'RR', 'l': 'RR',
            'k': 'kk', 'g': 'kk',
            'h': 'CH', 'j': 'CH', 'sh': 'CH', 'ch': 'CH',
            'th': 'TH',
            // Chinese-specific mappings for common pinyin initials
            'zh': 'CH', 'ch': 'CH', 'sh': 'CH', 'r': 'RR',
            'z': 'SS', 'c': 'SS', 's': 'SS',
            'y': 'ih', 'w': 'ou'
        };

        // Enhanced viseme durations for more natural speech
        const visemeDurations = {
            'aa': 150,  // Longer for open mouth sounds
            'E': 120,
            'ih': 100,
            'oh': 130,
            'ou': 120,
            'PP': 80,   // Shorter for plosives
            'FF': 100,
            'TH': 100,
            'DD': 80,
            'SS': 100,
            'CH': 90,
            'kk': 80,
            'nn': 90,
            'RR': 100,
            'sil': 50   // Short silence
        };

        // Split text into words
        const words = text.split(/\s+/);

        // Process each word with improved timing
        for (const word of words) {
            if (word.length === 0) continue;

            // Process each character in the word with improved viseme selection
            for (let i = 0; i < word.length; i++) {
                const char = word[i].toLowerCase();
                let viseme = charToViseme[char] || 'sil';

                // Check for digraphs (two-character sounds)
                if (i < word.length - 1) {
                    const digraph = char + word[i + 1].toLowerCase();
                    if (charToViseme[digraph]) {
                        viseme = charToViseme[digraph];
                        i++; // Skip next character
                    }
                }

                // Get appropriate duration for this viseme
                const duration = visemeDurations[viseme] || 100;

                // Get viseme targets for this viseme
                const visemeTargets = this.getVisemeTargets(viseme);

                if (visemeTargets && Object.keys(visemeTargets).length > 0) {
                    // Create viseme animation template
                    const visemeTemplate = {
                        name: 'viseme',
                        delay: currentTime,
                        dt: [duration],
                        vs: visemeTargets
                    };

                    // Create animation using the factory
                    const visemeAnim = this.animFactory(visemeTemplate);

                    if (visemeAnim) {
                        visemeAnim.viseme = viseme; // Store viseme name for reference
                        visemeAnims.push(visemeAnim);
                    }
                }

                currentTime += duration - 20; // Slight overlap for smoother transitions
            }

            // Add a small gap between words
            currentTime += 60;
        }

        // Add a final silence using animFactory
        const silenceTargets = this.getVisemeTargets('sil');
        if (silenceTargets && Object.keys(silenceTargets).length > 0) {
            const finalSilenceTemplate = {
                name: 'viseme',
                delay: currentTime,
                dt: [100],
                vs: silenceTargets
            };

            const finalSilence = this.animFactory(finalSilenceTemplate);
            if (finalSilence) {
                finalSilence.viseme = 'sil';
                visemeAnims.push(finalSilence);
            }
        }

        // Add animations to queue
        visemeAnims.forEach(anim => this.animQueue.push(anim));

        logger.info(`Generated ${visemeAnims.length} viseme animations for lip sync using animFactory`);
        return visemeAnims;
    }

    /**
     * Override animate method to apply morph targets after processing animations
     * @param {number} timestamp - High precision timestamp
     */
    animate(timestamp) {
        // Call base class animate
        super.animate(timestamp);

        // Apply morph target values to the mesh
        this.applyMorphTargets();
    }

    /**
     * Restore head to original position
     * Used after head movement animations
     */
    restoreHeadPosition() {
        if (!this.originalRotations) return;

        // Restore head rotation
        if (this.bones.Head && this.bones.Head.rotation && this.originalRotations.Head) {
            this.bones.Head.rotation.x = this.originalRotations.Head.x;
            this.bones.Head.rotation.y = this.originalRotations.Head.y;
            this.bones.Head.rotation.z = this.originalRotations.Head.z;
        }

        // Restore neck rotation
        if (this.bones.Neck && this.bones.Neck.rotation && this.originalRotations.Neck) {
            this.bones.Neck.rotation.x = this.originalRotations.Neck.x;
            this.bones.Neck.rotation.y = this.originalRotations.Neck.y;
            this.bones.Neck.rotation.z = this.originalRotations.Neck.z;
        }
    }

    /**
     * Add hand gestures during speech
     * Extends the base class method with morph-specific features
     * @param {number} intensity - Animation intensity based on audio volume
     */
    speakWithHands(intensity = 0.5) {
        // Call the base class method to handle bone animations
        super.speakWithHands(intensity);

        // Add morph-specific hand gesture animations if available
        if (this.morphTargetDictionary) {
            // Choose a random gesture type for morph targets
            const morphGestureType = Math.floor(Math.random() * 3);

            // Apply morph target animations based on gesture type
            switch (morphGestureType) {
                case 0: // Subtle smile during gesture
                    this.setMorphTargetValue('mouthSmile', 0.3 * intensity);
                    setTimeout(() => {
                        this.setMorphTargetValue('mouthSmile', 0);
                    }, 800 + Math.random() * 500);
                    break;

                case 1: // Raised eyebrows for emphasis
                    this.setMorphTargetValue('browUp', 0.4 * intensity);
                    setTimeout(() => {
                        this.setMorphTargetValue('browUp', 0);
                    }, 600 + Math.random() * 400);
                    break;

                case 2: // Squint slightly for thoughtful expression
                    this.setMorphTargetValue('eyeSquint', 0.2 * intensity);
                    setTimeout(() => {
                        this.setMorphTargetValue('eyeSquint', 0);
                    }, 700 + Math.random() * 500);
                    break;
            }
        }
    }

    /**
     * Dispose resources
     * Override base class method to clean up morph-specific resources
     */
    dispose() {
        // Reset all morph targets
        this.resetMorphTargets();

        // Restore original positions
        if (this.originalRotations) {
            this.restoreHeadPosition();
            this.restoreHandPosition();
        }

        // Call base class dispose
        super.dispose();

        // Clear morph-specific references
        this.morphs = [];
        this.morphTargetDictionary = {};
        this.originalRotations = null;

        logger.info('MorphAnimator resources disposed');
    }
}
