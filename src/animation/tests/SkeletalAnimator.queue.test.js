/**
 * SkeletalAnimator Animation Queue Tests
 * 
 * Tests for the FBX animation queueing system implementation
 */

import { jest } from '@jest/globals';

describe('SkeletalAnimator Animation Queue', () => {
    let SkeletalAnimator;
    let BaseAnimator;
    let animator;
    let mockScene;
    let mockMixer;
    let mockAction;

    beforeAll(async () => {
        // Mock Three.js classes
        global.THREE = {
            AnimationMixer: jest.fn().mockImplementation(() => ({
                clipAction: jest.fn().mockReturnValue({
                    play: jest.fn(),
                    stop: jest.fn(),
                    setLoop: jest.fn(),
                    setEffectiveWeight: jest.fn(),
                    fadeIn: jest.fn(),
                    fadeOut: jest.fn(),
                    crossFadeFrom: jest.fn(),
                    crossFadeTo: jest.fn(),
                    reset: jest.fn(),
                    setDuration: jest.fn(),
                    paused: false,
                    enabled: true,
                    weight: 1,
                    time: 0,
                    timeScale: 1
                }),
                update: jest.fn(),
                getRoot: jest.fn()
            }),
                LoopOnce: 2200,
                LoopRepeat: 2201,
                LoopPingPong: 2202,
                Object3D: jest.fn(),
                Clock: jest.fn().mockImplementation(() => ({
                    getDelta: jest.fn().mockReturnValue(0.016),
                    getElapsedTime: jest.fn().mockReturnValue(1.0)
                }))
        };

        // Mock BaseAnimator
        const { BaseAnimator: MockBaseAnimator } = await import('../BaseAnimator.js');
        BaseAnimator = MockBaseAnimator;

        // Mock the SkeletalAnimator
        const { SkeletalAnimator: MockSkeletalAnimator } = await import('../SkeletalAnimator.js');
        SkeletalAnimator = MockSkeletalAnimator;
    });

    beforeEach(() => {
        // Create mock scene and mixer
        mockScene = {
            traverse: jest.fn()
        };

        mockMixer = new THREE.AnimationMixer();
        mockAction = mockMixer.clipAction();

        // Create animator instance
        animator = new SkeletalAnimator(mockScene);

        // Mock the mixer creation
        animator.mixer = mockMixer;

        // Mock the animation loading
        animator.loadedAnimations = {
            'category1': {
                'test1': { duration: 2.5 },
                'test2': { duration: 1.8 }
            },
            'category2': {
                'test3': { duration: 3.2 }
            }
        };

        // Mock the playAnimation method to track calls
        animator.playAnimation = jest.fn().mockResolvedValue();

        // Initialize animation queue
        animator.animQueue = [];
        animator.currentTime = 0;

        // Mock clock for timing
        animator.clock = new THREE.Clock();
    });

    describe('queueAnimation method', () => {
        test('should queue a simple animation with default priority', async () => {
            const animId = await animator.queueAnimation('category1', 'test1');

            expect(animId).toBeDefined();
            expect(typeof animId).toBe('string');
            expect(animator.animQueue).toHaveLength(1);

            const queuedAnim = animator.animQueue[0];
            expect(queuedAnim.isFBXAnimation).toBe(true);
            expect(queuedAnim.fbxCategory).toBe('category1');
            expect(queuedAnim.fbxFile).toBe('test1');
            expect(queuedAnim.fbxDuration).toBe(2.5);
            expect(queuedAnim.priority).toBe(50); // default priority
        });

        test('should queue animation with custom priority and delay', async () => {
            const options = {
                priority: 80,
                delay: 1.5
            };

            const animId = await animator.queueAnimation('category2', 'test3', options);

            expect(animId).toBeDefined();
            expect(animator.animQueue).toHaveLength(1);

            const queuedAnim = animator.animQueue[0];
            expect(queuedAnim.priority).toBe(80);
            expect(queuedAnim.t).toBeGreaterThan(animator.currentTime); // should have delay
            expect(queuedAnim.fbxDuration).toBe(3.2);
        });

        test('should handle multiple queued animations with different priorities', async () => {
            // Queue animations with different priorities
            await animator.queueAnimation('category1', 'test1', { priority: 30 });
            await animator.queueAnimation('category1', 'test2', { priority: 70 });
            await animator.queueAnimation('category2', 'test3', { priority: 50 });

            expect(animator.animQueue).toHaveLength(3);

            // Verify all animations are marked as FBX
            animator.animQueue.forEach(anim => {
                expect(anim.isFBXAnimation).toBe(true);
            });
        });

        test('should handle animation with callback', async () => {
            const mockCallback = jest.fn();
            const options = {
                onComplete: mockCallback
            };

            const animId = await animator.queueAnimation('category1', 'test1', options);

            const queuedAnim = animator.animQueue[0];
            expect(queuedAnim.onComplete).toBe(mockCallback);
        });

        test('should throw error for invalid category', async () => {
            await expect(
                animator.queueAnimation('invalidCategory', 'test1')
            ).rejects.toThrow();
        });

        test('should throw error for invalid animation file', async () => {
            await expect(
                animator.queueAnimation('category1', 'invalidFile')
            ).rejects.toThrow();
        });
    });

    describe('_processAnimationQueue method', () => {
        test('should process FBX animations when ready', async () => {
            // Queue an animation that should be ready immediately
            await animator.queueAnimation('category1', 'test1');

            // Set current time to make animation ready
            animator.currentTime = Date.now() / 1000;

            // Process the queue
            await animator._processAnimationQueue();

            // Verify playAnimation was called
            expect(animator.playAnimation).toHaveBeenCalledWith('category1', 'test1', expect.any(Object));

            // Animation should be removed from queue after processing
            expect(animator.animQueue).toHaveLength(0);
        });

        test('should not process FBX animations that are not ready yet', async () => {
            // Queue an animation with delay
            await animator.queueAnimation('category1', 'test1', { delay: 2.0 });

            // Set current time before the delay expires
            animator.currentTime = Date.now() / 1000;

            // Process the queue
            await animator._processAnimationQueue();

            // Verify playAnimation was NOT called
            expect(animator.playAnimation).not.toHaveBeenCalled();

            // Animation should still be in queue
            expect(animator.animationQueue).toHaveLength(1);
        });

        test('should handle multiple ready animations in priority order', async () => {
            // Queue multiple animations with different priorities
            await animator.queueAnimation('category1', 'test1', { priority: 30 });
            await animator.queueAnimation('category1', 'test2', { priority: 70 });
            await animator.queueAnimation('category2', 'test3', { priority: 50 });

            // Make all animations ready
            animator.currentTime = Date.now() / 1000;

            // Process the queue
            await animator._processAnimationQueue();

            // Should process highest priority first (70)
            expect(animator.playAnimation).toHaveBeenCalledWith('category1', 'test2', expect.any(Object));
        });

        test('should call completion callbacks', async () => {
            const mockCallback = jest.fn();

            // Queue animation with callback
            await animator.queueAnimation('category1', 'test1', {
                onComplete: mockCallback
            });

            // Make animation ready and process
            animator.currentTime = Date.now() / 1000;
            await animator._processAnimationQueue();

            // Simulate animation completion (in real implementation, this would be triggered by the actual animation ending)
            const processedAnim = animator.playAnimation.mock.calls[0][2];
            if (processedAnim && processedAnim.onComplete) {
                processedAnim.onComplete();
            }

            expect(mockCallback).toHaveBeenCalled();
        });

        test('should handle errors gracefully', async () => {
            // Mock playAnimation to throw an error
            animator.playAnimation.mockRejectedValueOnce(new Error('Animation failed'));

            // Queue animation
            await animator.queueAnimation('category1', 'test1');

            // Make animation ready and process
            animator.currentTime = Date.now() / 1000;

            // Should not throw error
            await expect(animator._processAnimationQueue()).resolves.not.toThrow();

            // Animation should still be removed from queue even if it failed
            expect(animator.animationQueue).toHaveLength(0);
        });
    });

    describe('Integration with BaseAnimator', () => {
        test('should delegate non-FBX animations to BaseAnimator', async () => {
            // Mock BaseAnimator's _processAnimationQueue method
            const mockBaseProcess = jest.fn();
            Object.setPrototypeOf(animator, {
                ...Object.getPrototypeOf(animator),
                _processAnimationQueue: mockBaseProcess
            });

            // Add a non-FBX animation to the queue manually
            animator.animationQueue.push({
                id: 'test-anim',
                isFBXAnimation: false,
                t: animator.currentTime - 1, // ready to play
                priority: 50
            });

            // Process queue
            await animator._processAnimationQueue();

            // Should call BaseAnimator's implementation for non-FBX animations
            expect(mockBaseProcess).toHaveBeenCalled();
        });
    });

    describe('Performance and Memory', () => {
        test('should handle large number of queued animations', async () => {
            const numAnimations = 100;

            // Queue many animations
            for (let i = 0; i < numAnimations; i++) {
                await animator.queueAnimation('category1', 'test1', {
                    delay: i * 0.1,
                    priority: Math.floor(Math.random() * 100)
                });
            }

            expect(animator.animationQueue).toHaveLength(numAnimations);

            // Process should complete without hanging
            const startTime = Date.now();
            await animator._processAnimationQueue();
            const endTime = Date.now();

            // Should complete in reasonable time (less than 1 second)
            expect(endTime - startTime).toBeLessThan(1000);
        });
    });
});
