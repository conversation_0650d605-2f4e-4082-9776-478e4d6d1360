/**
 * AnimationConfig.js
 * Centralized Animation Configuration System
 *
 * This file provides a unified configuration system for all animation-related functionality.
 * It consolidates configuration from multiple sources and provides clean APIs for animation management.
 *
 * MAIN SECTIONS:
 * ============
 * 1. CENTRALIZED ANIMATION SYSTEM (NEW)
 *    - Animation Registry: Single source of truth for all animations
 *    - Duration Management: Centralized duration calculation
 *    - Animation Triggering: Unified animation execution system
 *    - LLM Integration: Intelligent animation selection
 *
 * 2. LEGACY CONFIGURATION (MAINTAINED FOR COMPATIBILITY)
 *    - Core configuration values
 *    - Animation timing and physics
 *    - Pose and gesture templates
 *    - Animation templates and moods
 *    - Morph target settings
 *    - Bone structure definitions
 *    - Utility functions
 *
 * USAGE:
 * ======
 * // Get animation duration from category defaults
 * const duration = DEFAULT_CATEGORY_DURATIONS['happy'] || 3000;
 *
 * // Trigger animation
 * await AnimationTrigger.triggerAnimation(animationMapping, animator, options);
 *
 * // LLM animation selection (now handled in llm.ts)
 * const result = await SHARED_PROMPT_COMPONENTS.parseUnifiedAnimationResponse(llmResponse);
 */



// ============================================================================
// SECTION 1: CENTRALIZED ANIMATION SYSTEM (NEW)
// ============================================================================
// This section contains the new unified animation system that consolidates
// all animation-related functionality into clean, reusable APIs.

/**
 * Optimized Animation Registry with Direct Access Patterns
 * Designed for O(1) lookups and clean organization
 */
export const ANIMATION_REGISTRY = {
    basePath: 'public/assets/animations/',

    // Direct access mappings (O(1) lookups)
    byFilename: {},  // filename -> animation data
    byId: {},        // id -> animation data
    byCategory: {},  // category -> array of animation data

    // Raw animation definitions (English-only, optimized for single LLM call)
    definitions: {
        emotional: {
            description: 'for strong emotions',
            subcategories: {
                positive: {
                    description: 'for happiness and excitement',
                    animations: {
                        'Happy Idle.fbx': {
                            id: 'happy_idle',
                            description: 'Happy idle pose with positive body language',
                            loopable: false
                        },
                        'Excited.fbx': {
                            id: 'excited',
                            description: 'Energetic excitement with arm movements',
                            loopable: false
                        }
                    }
                },
                negative: {
                    description: 'for sadness and anger',
                    animations: {
                        'Sad Idle.fbx': {
                            id: 'sad_idle',
                            description: 'Sad pose with drooped shoulders',
                            loopable: true
                        },
                        'Sad Idle_2.fbx': {
                            id: 'sad_idle_alt',
                            description: 'Alternative sad pose with head hanging',
                            loopable: true
                        },
                        'Angry.fbx': {
                            id: 'angry',
                            description: 'Angry pose with clenched fists',
                            loopable: true
                        },
                        'Angry_1.fbx': {
                            id: 'angry_alt',
                            description: 'Angry animation with pointing gestures',
                            loopable: true
                        }
                    }
                }
            }
        },
        communication: {
            description: 'for active conversation and social interactions',
            subcategories: {
                speaking: {
                    description: 'for natural conversation and talking',
                    animations: {
                        'Talking.fbx': {
                            id: 'talking',
                            description: 'Natural talking with hand gestures',
                            loopable: true
                        },
                        'Sitting Talking.fbx': {
                            id: 'sitting_talking',
                            description: 'Sitting conversation with hand gestures',
                            loopable: true
                        },
                        'Standing Arguing.fbx': {
                            id: 'arguing',
                            description: 'Argumentative stance with assertive gestures for debates',
                            loopable: true
                        }
                    }
                },
                social: {
                    description: 'for greetings and special social interactions',
                    animations: {
                        'Standing Greeting.fbx': {
                            id: 'greeting',
                            description: 'Friendly greeting with waving hand',
                            loopable: false
                        }
                    }
                }
            }
        },
        performance: {
            description: 'for physical performances, activities, and entertainment',
            subcategories: {
                dance: {
                    description: 'for various dance styles and music-related activities',
                    animations: {
                        'Tut Hip Hop Dance.fbx': {
                            id: 'dance_tutting',
                            description: 'Hip hop tutting with geometric arm movements',
                            loopable: true
                        },
                        'Snake Hip Hop Dance.fbx': {
                            id: 'dance_snake',
                            description: 'Hip hop dance with smooth, snake-like body isolations and fluid arm movements',
                            loopable: true
                        },
                        'Samba Dancing.fbx': {
                            id: 'dance_samba',
                            description: 'Energetic samba dance with rhythmic hip movements and quick footwork',
                            loopable: true
                        },
                        'Northern Soul Spin Combo.fbx': {
                            id: 'dance_northern_soul',
                            description: 'Retro dance combo featuring fast spins, shuffles, and smooth foot slides inspired by Northern Soul style',
                            loopable: true
                        },
                        'Shopping Cart Dance.fbx': {
                            id: 'dance_shopping',
                            description: 'Playful dance move mimicking pushing a shopping cart with exaggerated arm gestures and light foot bounces',
                            loopable: true
                        },
                        'Locking Hip Hop Dance.fbx': {
                            id: 'dance_locking',
                            description: 'Funky hip hop routine with sharp arm locks, wrist rolls, and dynamic freezes characteristic of the locking style',
                            loopable: true
                        },
                        'Wave Hip Hop Dance.fbx': {
                            id: 'dance_wave',
                            description: 'Hip hop wave with flowing body movements',
                            loopable: true
                        },
                        'Silly Dancing.fbx': {
                            id: 'dance_silly',
                            description: 'Playful dance with exaggerated fun movements',
                            loopable: true
                        },
                        'Rumba Dancing.fbx': {
                            id: 'dance_rumba',
                            description: 'Elegant rumba with smooth hip movements',
                            loopable: true
                        },
                        'Listening To Music.fbx': {
                            id: 'listening_music',
                            description: 'Listening to music with head bobbing',
                            loopable: true
                        }
                    }
                },
                action: {
                    description: 'for martial arts, boxing, dynamic movements, celebrations, meditation and special actions',
                    animations: {
                        'Jab Cross.fbx': {
                            id: 'boxing_jab',
                            description: 'Boxing jab-cross combination',
                            loopable: false
                        },
                        'Martelo 2.fbx': {
                            id: 'capoeira_kick',
                            description: 'Capoeira martelo kick with circular motion',
                            loopable: false
                        },
                        'Hurricane Kick.fbx': {
                            id: 'spinning_kick',
                            description: 'Dynamic spinning hurricane kick',
                            loopable: false
                        },
                        'Falling.fbx': {
                            id: 'falling',
                            description: 'Falling backwards with arms flailing',
                            loopable: false
                        },
                        'Female Standing Pose.fbx': {
                            id: 'female_pose',
                            description: 'Elegant female standing pose',
                            loopable: false
                        },
                        'Joyful Jump.fbx': {
                            id: 'jump_joy',
                            description: 'Joyful jumping with arms raised',
                            loopable: false
                        },
                        'Praying.fbx': {
                            id: 'praying',
                            description: 'Peaceful prayer with hands together',
                            loopable: false
                        }
                    }
                }
            }
        },
        idle: {
            description: 'for neutral states and common conversation replies - PREFERRED for normal responses',
            subcategories: {
                neutral: {
                    description: 'for basic idle poses used in common replies - PRIMARY CHOICE for conversations',
                    animations: {
                        'Idle.fbx': {
                            id: 'idle',
                            description: 'Basic neutral idle pose - PRIMARY for common conversation replies (PREFERRED OVER communication animations)',
                            loopable: true
                        },
                        'Sitting.fbx': {
                            id: 'sitting',
                            description: 'Relaxed sitting position - secondary for casual replies',
                            loopable: true
                        }
                    }
                }
            }
        }
    },

    // Simple O(1) access methods
    getByFilename(filename) {
        return this.byFilename[filename] || null;
    },

    getById(id) {
        return this.byId[id] || null;
    },

    getByCategory(category) {
        return this.byCategory[category] || [];
    },

    /**
     * Get random animation from category/subcategory
     */
    getRandomFromCategory(category, subcategory = null) {
        const categoryAnimations = this.getByCategory(category);
        if (!categoryAnimations.length) return null;

        // Filter by subcategory if specified
        const filteredAnimations = subcategory
            ? categoryAnimations.filter(anim => anim.subcategory === subcategory)
            : categoryAnimations;

        if (!filteredAnimations.length) return null;

        // Return random animation
        const randomIndex = Math.floor(Math.random() * filteredAnimations.length);
        return filteredAnimations[randomIndex];
    },

    /**
     * Legacy compatibility methods (now use O(1) lookups)
     */
    getAnimationById(animationId) {
        return this.getById(animationId);
    },

    getAllAnimations() {
        return this.byFilename;
    },

    /**
     * Initialize the O(1) lookup maps from the definitions
     * This should be called once after the registry is defined
     */
    initialize() {
        // Clear existing mappings
        this.byFilename = {};
        this.byId = {};
        this.byCategory = {};

        // Populate mappings from definitions
        for (const [categoryKey, categoryData] of Object.entries(this.definitions)) {
            this.byCategory[categoryKey] = [];

            for (const [subcategoryKey, subcategoryData] of Object.entries(categoryData.subcategories)) {
                for (const [filename, animData] of Object.entries(subcategoryData.animations)) {
                    const fullAnimData = {
                        ...animData,
                        filename,
                        file: filename,
                        category: categoryKey,
                        subcategory: subcategoryKey,
                        fullPath: `${this.basePath}${filename}`
                    };

                    // Populate all lookup maps
                    this.byFilename[filename] = fullAnimData;
                    this.byId[animData.id] = fullAnimData;
                    this.byCategory[categoryKey].push(fullAnimData);
                }
            }
        }
    }
};

// Initialize the registry
ANIMATION_REGISTRY.initialize();


/**
 * Default animation durations by category (fallback when FBX duration unavailable)
 * These are estimates - actual duration should be read from FBX files
 */
export const DEFAULT_CATEGORY_DURATIONS = {
    'emotional': 3500,
    'communication': 4000,
    'social': 3000,
    'performance': 8000,  // Unified performance category covering dance, combat, movement, entertainment, meditation
    'idle': 2000
};





/**
 * LLM-Driven Unified Animation Selection System
 * The LLM acts as the "brain" of the avatar, making both conversation and animation decisions simultaneously
 */
export const LLM_ANIMATION_SYSTEM = {
    /**
     * Get talking animation IDs (optimized with O(1) access)
     */
    getTalkingAnimations() {
        return ANIMATION_REGISTRY.getByCategory('communication')
            .filter(anim => anim.subcategory === 'speaking')
            .map(anim => anim.id);
    },



    /**
     * Optimized O(1) lookup methods using direct access mappings
     */
    getAnimationFileById(animationId) {
        return ANIMATION_REGISTRY.getById(animationId)?.file || null;
    },

    getAnimationCategoryById(animationId) {
        return ANIMATION_REGISTRY.getById(animationId)?.category || 'communication';
    },

    getAnimationById(animationId) {
        return ANIMATION_REGISTRY.getById(animationId);
    },

    /**
     * Get transition settings for animation category
     */
    getTransitionSettings(category) {
        const categorySettings = ANIMATION_TRANSITIONS.categoryTransitions[category];
        if (categorySettings) {
            return {
                fadeOut: categorySettings.fadeOut,
                fadeIn: categorySettings.fadeIn,
                returnDelay: categorySettings.returnDelay
            };
        }

        // Return default settings
        return {
            fadeOut: ANIMATION_TRANSITIONS.defaultDuration,
            fadeIn: ANIMATION_TRANSITIONS.defaultDuration,
            returnDelay: ANIMATION_TRANSITIONS.returnToIdle.delay
        };
    },

    /**
     * Check if animation should auto-return to idle
     */
    shouldReturnToIdle(category) {
        if (!ANIMATION_TRANSITIONS.returnToIdle.enabled) return false;

        const settings = ANIMATION_TRANSITIONS.categoryTransitions[category];
        return !settings || settings.returnDelay > 0;
    },

    /**
     * Generate category descriptions for LLM prompts
     * This replaces hardcoded category descriptions in llm.ts
     */
    generateCategoryDescriptions() {
        const categories = [];

        for (const [categoryKey, categoryData] of Object.entries(ANIMATION_REGISTRY.definitions)) {
            const animations = ANIMATION_REGISTRY.getByCategory(categoryKey);
            // Get unique animation IDs to avoid duplicates
            const uniqueIds = [...new Set(animations.map(anim => anim.id))];
            categories.push(`- ${categoryKey}: ${uniqueIds.join(', ')} (${categoryData.description})`);
        }

        return categories.join('\n') + '\nNOTE: Talking animations are automatically triggered during TTS speech.';
    },

    /**
     * Generate valid animation IDs list for LLM prompts
     * This replaces hardcoded animation ID lists in llm.ts
     */
    generateValidAnimationIds() {
        const allAnimationIds = new Set(); // Use Set to automatically handle duplicates

        for (const categoryKey of Object.keys(ANIMATION_REGISTRY.definitions)) {
            const animations = ANIMATION_REGISTRY.getByCategory(categoryKey);
            animations.forEach(anim => allAnimationIds.add(anim.id));
        }

        return Array.from(allAnimationIds).join(', ');
    },

    /**
     * Generate dynamic selection rules for LLM prompts
     * This replaces hardcoded animation rules in base.js
     */
    generateSelectionRules() {
        // Get animations by category for dynamic rule generation (remove duplicates)
        const danceAnimations = [...new Set(ANIMATION_REGISTRY.getByCategory('performance')
            .filter(anim => anim.id.startsWith('dance_'))
            .map(anim => `${anim.id} (${anim.description || anim.id.replace('dance_', '').replace('_', ' ')})`))]
            .join('\n- ');

        const emotionAnimations = [...new Set(ANIMATION_REGISTRY.getByCategory('emotional')
            .map(anim => `${anim.id} (${anim.description || anim.id.replace('_', ' ')})`))]
            .join(', ');

        const actionAnimations = [...new Set(ANIMATION_REGISTRY.getByCategory('performance')
            .filter(anim => !anim.id.startsWith('dance_') && !anim.id.includes('music'))
            .map(anim => `${anim.id} (${anim.description || anim.id.replace('_', ' ')})`))]
            .join('\n- ');

        const communicationAnimations = [...new Set(ANIMATION_REGISTRY.getByCategory('communication')
            .map(anim => anim.id))]
            .join(', ');

        return `CRITICAL ANIMATION ID RULES:
1. MUST use EXACT animation IDs from the registry above - ZERO tolerance for variations
2. Choose specific animations ONLY for explicit user dance/action requests

EXACT DANCE IDs (NO variations allowed):
- ${danceAnimations}

EXACT EMOTION IDs:
- ${emotionAnimations} (NO variations like "happy" or "excitement")

EXACT ACTION IDs:
- ${actionAnimations}

EXACT COMMUNICATION IDs:
- ${communicationAnimations}

FORBIDDEN ANIMATION IDs:
- Category names: "combat", "dance", "emotional", "performance"
- Modified IDs: "dancing_wave", "dance_wavy", "wave_dance", "happy_pose"
- Partial IDs: "wave", "silly", "tutting"

VALIDATION: Animation ID MUST exist in the provided registry list above`;
    },

    /**
     * Generate dynamic positive response examples for LLM prompts
     * This replaces hardcoded examples in base.js
     */
    generatePositiveResponseExamples() {
        // Get key animations from different categories for examples
        const greetingAnim = ANIMATION_REGISTRY.getById('greeting');
        const danceAnims = ANIMATION_REGISTRY.getByCategory('performance').filter(anim => anim.id.startsWith('dance_'));
        const actionAnims = ANIMATION_REGISTRY.getByCategory('performance').filter(anim =>
            anim.id.includes('kick') || anim.id.includes('jump') || anim.id.includes('martial') || anim.id.includes('capoeira')
        );

        // Build examples dynamically
        const examples = [];

        // Dance examples
        if (danceAnims.length > 0) {
            const sillyDance = danceAnims.find(anim => anim.id.includes('silly')) || danceAnims[0];
            const waveDance = danceAnims.find(anim => anim.id.includes('wave')) || danceAnims[1] || danceAnims[0];

            examples.push(`"Dance for me!" → {"animation": "${sillyDance.id}", "responseText": "Let me show you some moves!"}`);
            examples.push(`"跳舞!" → {"animation": "${waveDance.id}", "responseText": "来看我的舞蹈动作!"}`);
        }

        // Greeting example
        if (greetingAnim) {
            examples.push(`"Hello!" → {"animation": "${greetingAnim.id}", "responseText": "Hi there! How can I help?"}`);
        }

        // Action examples
        if (actionAnims.length > 0) {
            const kickAnim = actionAnims.find(anim => anim.id.includes('kick')) || actionAnims[0];
            const jumpAnim = actionAnims.find(anim => anim.id.includes('jump')) || actionAnims[1] || actionAnims[0];

            if (kickAnim.id.includes('capoeira')) {
                examples.push(`"Do martial arts!" → {"animation": "${kickAnim.id}", "responseText": "Watch this capoeira kick!"}`);
            } else if (kickAnim.id.includes('spinning')) {
                examples.push(`"Show me a spinning kick!" → {"animation": "${kickAnim.id}", "responseText": "Here's a powerful hurricane kick for you!"}`);
                examples.push(`"做个旋风腿!" → {"animation": "${kickAnim.id}", "responseText": "看我的旋风腿!"}`);
            }

            if (jumpAnim && jumpAnim.id.includes('joy')) {
                examples.push(`"Celebrate!" → {"animation": "${jumpAnim.id}", "responseText": "Let's celebrate together!"}`);
            }
        }

        // Generic action example
        const genericAction = actionAnims.find(anim => anim.id.includes('spinning')) || actionAnims[0];
        if (genericAction) {
            examples.push(`"演示一个动作" → {"animation": "${genericAction.id}", "responseText": "来看这个厉害的动作!"}`);
        }

        return examples.join('\n');
    }
};

/**
 * Enhanced Animation Transition Configuration
 * Manages smooth transitions and return mechanisms
 */
export const ANIMATION_TRANSITIONS = {
    defaultDuration: 500, // Default transition duration in milliseconds
    minDuration: 100,     // Minimum transition duration
    maxDuration: 2000,    // Maximum transition duration

    // Return-to-idle configuration
    returnToIdle: {
        enabled: true,
        delay: 1000,      // Delay before returning to idle after animation completes
        fadeOutDuration: 800,  // Duration to fade out current animation
        fadeInDuration: 600,   // Duration to fade in idle animation
        idleAnimation: 'Happy Idle.fbx' // Default idle animation to return to
    },

    // Category-specific transition settings
    categoryTransitions: {
        'performance': { fadeOut: 1000, fadeIn: 800, returnDelay: 2000 }, // Unified performance category (includes meditation)
        'emotional': { fadeOut: 800, fadeIn: 600, returnDelay: 1000 },
        'communication': { fadeOut: 600, fadeIn: 500, returnDelay: 0 } // Increased fade times to prevent rapid cross-fading issues
    },

};

// Registry initialization complete
console.log(`[AnimationConfig] Initialized ${Object.keys(ANIMATION_REGISTRY.byFilename).length} animations with O(1) access`);

/**
 * Default animation for fallback when no other selection is made
 */
export const DEFAULT_ANIMATION = {
    file: 'Happy Idle.fbx',
    id: 'happy_idle',
    confidence: 0.1,
    reasoning: 'Default fallback animation'
};

/**
 * Bone hierarchy and classification system
 * Used for intelligent animation filtering and constraint application
 */
export const BONE_DEFINITIONS = {
    // Major bones that are typically animated by FBX files
    // These should have minimal filtering/constraints when FBX animations are playing
    MAJOR_BONES: [
        'Hips',           // Root bone, controls overall position
        'Spine', 'Spine1', 'Spine2',  // Spine chain
        'Neck', 'Head',   // Head and neck
        'LeftArm', 'LeftForeArm', 'LeftHand',  // Left arm chain
        'RightArm', 'RightForeArm', 'RightHand', // Right arm chain
        'LeftUpLeg', 'LeftLeg', 'LeftFoot',     // Left leg chain
        'RightUpLeg', 'RightLeg', 'RightFoot'   // Right leg chain
    ],

    // Minor bones that can have filtering/constraints applied even during FBX animations
    // These are usually fingers, toes, or secondary bones
    MINOR_BONES: [
        // Finger bones
        'LeftHandThumb1', 'LeftHandThumb2', 'LeftHandThumb3',
        'LeftHandIndex1', 'LeftHandIndex2', 'LeftHandIndex3',
        'LeftHandMiddle1', 'LeftHandMiddle2', 'LeftHandMiddle3',
        'LeftHandRing1', 'LeftHandRing2', 'LeftHandRing3',
        'LeftHandPinky1', 'LeftHandPinky2', 'LeftHandPinky3',
        'RightHandThumb1', 'RightHandThumb2', 'RightHandThumb3',
        'RightHandIndex1', 'RightHandIndex2', 'RightHandIndex3',
        'RightHandMiddle1', 'RightHandMiddle2', 'RightHandMiddle3',
        'RightHandRing1', 'RightHandRing2', 'RightHandRing3',
        'RightHandPinky1', 'RightHandPinky2', 'RightHandPinky3',

        // Toe bones
        'LeftToeBase', 'RightToeBase',

        // Facial bones (if present)
        'LeftEye', 'RightEye',
        'Jaw'
    ],

    // Utility functions for bone classification
    isMajorBone: function (boneName) {
        return this.MAJOR_BONES.includes(boneName);
    },

    isMinorBone: function (boneName) {
        return this.MINOR_BONES.includes(boneName);
    },

    // Get all bones that should be exempt from filtering during FBX animations
    getFBXAnimationExemptBones: function () {
        return [...this.MAJOR_BONES];
    },

    // Get all bones that can still be filtered during FBX animations
    getFilterableBones: function () {
        return [...this.MINOR_BONES];
    }
};

// ============================================================================
// SECTION 2: LEGACY CONFIGURATION (MAINTAINED FOR COMPATIBILITY)
// ============================================================================
// This section contains the original animation configuration system.
// It is maintained for backward compatibility with existing code.

/**
 * Default configuration values
 * These values are used as defaults throughout the animation system
 */
export const DEFAULT_CONFIG = {
    // Default pose name
    defaultPoseName: "side",

    // Default weight on left leg
    defaultPoseWeightOnLeft: true,

    // Avatar height in meters (calculated based on eye level on avatar load)
    defaultAvatarHeight: 1.7,

    // Model configuration
    modelRoot: "Armature",
    modelPixelRatio: 1,
    modelFPS: 30,
    modelMovementFactor: 1,

    // Camera configuration
    cameraView: 'full',
    cameraDistance: 0,
    cameraX: 0,
    cameraY: 0,
    cameraRotateX: 0,
    cameraRotateY: 0,
    cameraRotateEnable: true,
    cameraPanEnable: false,
    cameraZoomEnable: false,

    // Lighting configuration
    lightAmbientColor: 0xffffff,
    lightAmbientIntensity: 2,
    lightDirectColor: 0x8888aa,
    lightDirectIntensity: 30,
    lightDirectPhi: 1,
    lightDirectTheta: 2,
    lightSpotIntensity: 0,
    lightSpotColor: 0x3388ff,
    lightSpotPhi: 0.1,
    lightSpotTheta: 4,
    lightSpotDispersion: 1,

    // Avatar behavior configuration
    avatarMood: "neutral",
    avatarMute: false,
    avatarIdleEyeContact: 0.2,
    avatarIdleHeadMove: 0.5,
    avatarSpeakingEyeContact: 0.5,
    avatarSpeakingHeadMove: 0.5,
    avatarIgnoreCamera: false
};

/**
 * Animation timing and clock configuration
 */
export const ANIM_TIMING = {
    // Default frame duration (can be overridden by animator)
    frameDuration: 1000 / 30, // Default 30 FPS

    // Default slowdown rate for animations
    slowdownRate: 1,

    // Default easing factor for sigmoid function
    easingFactor: 5,

    // Animation loop management
    loopingAnimationDuration: 2000, // Default duration for looping animations (ms)
    idleAnimationInterval: [3000, 7000], // Min/max interval between idle animations (ms)
    blinkInterval: [3000, 7000], // Min/max interval between blinks (ms)
    blinkDuration: [100, 300], // Min/max duration of a blink (ms)

    // Head movement timing
    headMovementDuration: [1000, 2000], // Min/max duration of head movement (ms)
    headMovementInterval: [2000, 5000], // Min/max interval between head movements (ms)

    // Hand gesture timing
    handGestureDuration: [800, 1500], // Min/max duration of hand gestures (ms)
    handGestureInterval: [3000, 6000], // Min/max interval between hand gestures (ms)

    // Transition timing
    poseTransitionDuration: 1000, // Default duration for pose transitions (ms)
    expressionTransitionDuration: 500, // Default duration for expression transitions (ms)

    // Animation queue management
    maxQueueSize: 100, // Maximum number of animations in queue
    cleanupInterval: 5000 // Interval to clean up completed animations (ms)
};

/**
 * Pose templates for different body positions
 * Each pose contains rotation values for bones
 */
export const POSE_TEMPLATES = {
    'side': {
        standing: true,
        props: {
            'Hips.position': { x: 0, y: 1, z: 0 }, 'Hips.rotation': { x: -0.003, y: -0.017, z: 0.1 }, 'Spine.rotation': { x: -0.103, y: -0.002, z: -0.063 }, 'Spine1.rotation': { x: 0.042, y: -0.02, z: -0.069 }, 'Spine2.rotation': { x: 0.131, y: -0.012, z: -0.065 }, 'Neck.rotation': { x: 0.027, y: 0.006, z: 0 }, 'Head.rotation': { x: 0.077, y: -0.065, z: 0 }, 'LeftShoulder.rotation': { x: 1.599, y: 0.084, z: -1.77 }, 'LeftArm.rotation': { x: 1.364, y: 0.052, z: -0.044 }, 'LeftForeArm.rotation': { x: 0.002, y: -0.007, z: 0.331 }, 'LeftHand.rotation': { x: 0.104, y: -0.067, z: -0.174 }, 'LeftHandThumb1.rotation': { x: 0.231, y: 0.258, z: 0.355 }, 'LeftHandThumb2.rotation': { x: -0.106, y: -0.339, z: -0.454 }, 'LeftHandThumb3.rotation': { x: -0.02, y: -0.142, z: -0.004 }, 'LeftHandIndex1.rotation': { x: 0.148, y: 0.032, z: -0.069 }, 'LeftHandIndex2.rotation': { x: 0.326, y: -0.049, z: -0.029 }, 'LeftHandIndex3.rotation': { x: 0.247, y: -0.053, z: -0.073 }, 'LeftHandMiddle1.rotation': { x: 0.238, y: -0.057, z: -0.089 }, 'LeftHandMiddle2.rotation': { x: 0.469, y: -0.036, z: -0.081 }, 'LeftHandMiddle3.rotation': { x: 0.206, y: -0.015, z: -0.017 }, 'LeftHandRing1.rotation': { x: 0.187, y: -0.118, z: -0.157 }, 'LeftHandRing2.rotation': { x: 0.579, y: 0.02, z: -0.097 }, 'LeftHandRing3.rotation': { x: 0.272, y: 0.021, z: -0.063 }, 'LeftHandPinky1.rotation': { x: 0.405, y: -0.182, z: -0.138 }, 'LeftHandPinky2.rotation': { x: 0.613, y: 0.128, z: -0.144 }, 'LeftHandPinky3.rotation': { x: 0.268, y: 0.094, z: -0.081 }, 'RightShoulder.rotation': { x: 1.541, y: 0.192, z: 1.775 }, 'RightArm.rotation': { x: 1.273, y: -0.352, z: -0.067 }, 'RightForeArm.rotation': { x: -0.011, y: -0.031, z: -0.357 }, 'RightHand.rotation': { x: -0.008, y: 0.312, z: -0.028 }, 'RightHandThumb1.rotation': { x: 0.23, y: -0.258, z: -0.355 }, 'RightHandThumb2.rotation': { x: -0.107, y: 0.339, z: 0.454 }, 'RightHandThumb3.rotation': { x: -0.02, y: 0.142, z: 0.004 }, 'RightHandIndex1.rotation': { x: 0.148, y: -0.031, z: 0.069 }, 'RightHandIndex2.rotation': { x: 0.326, y: 0.049, z: 0.029 }, 'RightHandIndex3.rotation': { x: 0.247, y: 0.053, z: 0.073 }, 'RightHandMiddle1.rotation': { x: 0.237, y: 0.057, z: 0.089 }, 'RightHandMiddle2.rotation': { x: 0.469, y: 0.036, z: 0.081 }, 'RightHandMiddle3.rotation': { x: 0.206, y: 0.015, z: 0.017 }, 'RightHandRing1.rotation': { x: 0.204, y: 0.086, z: 0.135 }, 'RightHandRing2.rotation': { x: 0.579, y: -0.02, z: 0.098 }, 'RightHandRing3.rotation': { x: 0.272, y: -0.021, z: 0.063 }, 'RightHandPinky1.rotation': { x: 0.404, y: 0.182, z: 0.137 }, 'RightHandPinky2.rotation': { x: 0.613, y: -0.128, z: 0.144 }, 'RightHandPinky3.rotation': { x: 0.268, y: -0.094, z: 0.081 }, 'LeftUpLeg.rotation': { x: 0.096, y: 0.209, z: 2.983 }, 'LeftLeg.rotation': { x: -0.053, y: 0.042, z: -0.017 }, 'LeftFoot.rotation': { x: 1.091, y: 0.15, z: 0.026 }, 'LeftToeBase.rotation': { x: 0.469, y: -0.07, z: -0.015 }, 'RightUpLeg.rotation': { x: -0.307, y: -0.219, z: 2.912 }, 'RightLeg.rotation': { x: -0.359, y: 0.164, z: 0.015 }, 'RightFoot.rotation': { x: 1.035, y: 0.11, z: 0.005 }, 'RightToeBase.rotation': { x: 0.467, y: 0.07, z: 0.015 }
        }
    },

    'hip': {
        standing: true,
        props: {
            'Hips.position': { x: 0, y: 1, z: 0 }, 'Hips.rotation': { x: -0.036, y: 0.09, z: 0.135 }, 'Spine.rotation': { x: 0.076, y: -0.035, z: 0.01 }, 'Spine1.rotation': { x: -0.096, y: 0.013, z: -0.094 }, 'Spine2.rotation': { x: -0.014, y: 0.002, z: -0.097 }, 'Neck.rotation': { x: 0.034, y: -0.051, z: -0.075 }, 'Head.rotation': { x: 0.298, y: -0.1, z: 0.154 }, 'LeftShoulder.rotation': { x: 1.694, y: 0.011, z: -1.68 }, 'LeftArm.rotation': { x: 1.343, y: 0.177, z: -0.153 }, 'LeftForeArm.rotation': { x: -0.049, y: 0.134, z: 0.351 }, 'LeftHand.rotation': { x: 0.057, y: -0.189, z: -0.026 }, 'LeftHandThumb1.rotation': { x: 0.368, y: -0.066, z: 0.438 }, 'LeftHandThumb2.rotation': { x: -0.156, y: 0.029, z: -0.369 }, 'LeftHandThumb3.rotation': { x: 0.034, y: -0.009, z: 0.016 }, 'LeftHandIndex1.rotation': { x: 0.157, y: -0.002, z: -0.171 }, 'LeftHandIndex2.rotation': { x: 0.099, y: 0, z: 0 }, 'LeftHandIndex3.rotation': { x: 0.1, y: 0, z: 0 }, 'LeftHandMiddle1.rotation': { x: 0.222, y: -0.019, z: -0.16 }, 'LeftHandMiddle2.rotation': { x: 0.142, y: 0, z: 0 }, 'LeftHandMiddle3.rotation': { x: 0.141, y: 0, z: 0 }, 'LeftHandRing1.rotation': { x: 0.333, y: -0.039, z: -0.174 }, 'LeftHandRing2.rotation': { x: 0.214, y: 0, z: 0 }, 'LeftHandRing3.rotation': { x: 0.213, y: 0, z: 0 }, 'LeftHandPinky1.rotation': { x: 0.483, y: -0.069, z: -0.189 }, 'LeftHandPinky2.rotation': { x: 0.312, y: 0, z: 0 }, 'LeftHandPinky3.rotation': { x: 0.309, y: 0, z: 0 }, 'RightShoulder.rotation': { x: 1.597, y: 0.012, z: 1.816 }, 'RightArm.rotation': { x: 0.618, y: -1.274, z: -0.266 }, 'RightForeArm.rotation': { x: -0.395, y: -0.097, z: -1.342 }, 'RightHand.rotation': { x: -0.816, y: -0.057, z: -0.976 }, 'RightHandThumb1.rotation': { x: 0.42, y: 0.23, z: -1.172 }, 'RightHandThumb2.rotation': { x: -0.027, y: 0.361, z: 0.122 }, 'RightHandThumb3.rotation': { x: 0.076, y: 0.125, z: -0.371 }, 'RightHandIndex1.rotation': { x: -0.158, y: -0.045, z: 0.033 }, 'RightHandIndex2.rotation': { x: 0.391, y: 0.051, z: 0.025 }, 'RightHandIndex3.rotation': { x: 0.317, y: 0.058, z: 0.07 }, 'RightHandMiddle1.rotation': { x: 0.486, y: 0.066, z: 0.014 }, 'RightHandMiddle2.rotation': { x: 0.718, y: 0.055, z: 0.07 }, 'RightHandMiddle3.rotation': { x: 0.453, y: 0.019, z: 0.013 }, 'RightHandRing1.rotation': { x: 0.591, y: 0.241, z: 0.11 }, 'RightHandRing2.rotation': { x: 1.014, y: 0.023, z: 0.097 }, 'RightHandRing3.rotation': { x: 0.708, y: 0.008, z: 0.066 }, 'RightHandPinky1.rotation': { x: 1.02, y: 0.305, z: 0.051 }, 'RightHandPinky2.rotation': { x: 1.187, y: -0.028, z: 0.191 }, 'RightHandPinky3.rotation': { x: 0.872, y: -0.031, z: 0.121 }, 'LeftUpLeg.rotation': { x: -0.095, y: -0.058, z: -3.338 }, 'LeftLeg.rotation': { x: -0.366, y: 0.287, z: -0.021 }, 'LeftFoot.rotation': { x: 1.131, y: 0.21, z: 0.176 }, 'LeftToeBase.rotation': { x: 0.739, y: -0.068, z: -0.001 }, 'RightUpLeg.rotation': { x: -0.502, y: 0.362, z: 3.153 }, 'RightLeg.rotation': { x: -1.002, y: 0.109, z: 0.008 }, 'RightFoot.rotation': { x: 0.626, y: -0.097, z: -0.194 }, 'RightToeBase.rotation': { x: 1.33, y: 0.288, z: -0.078 }
        }
    },

    'turn': {
        standing: true,
        props: {
            'Hips.position': { x: 0, y: 1, z: 0 }, 'Hips.rotation': { x: -0.07, y: -0.604, z: -0.004 }, 'Spine.rotation': { x: -0.007, y: 0.003, z: 0.071 }, 'Spine1.rotation': { x: -0.053, y: 0.024, z: -0.06 }, 'Spine2.rotation': { x: 0.074, y: 0.013, z: -0.068 }, 'Neck.rotation': { x: 0.03, y: 0.186, z: -0.077 }, 'Head.rotation': { x: 0.045, y: 0.243, z: -0.086 }, 'LeftShoulder.rotation': { x: 1.717, y: -0.085, z: -1.761 }, 'LeftArm.rotation': { x: 1.314, y: 0.07, z: -0.057 }, 'LeftForeArm.rotation': { x: -0.151, y: 0.714, z: 0.302 }, 'LeftHand.rotation': { x: -0.069, y: 0.003, z: -0.118 }, 'LeftHandThumb1.rotation': { x: 0.23, y: 0.258, z: 0.354 }, 'LeftHandThumb2.rotation': { x: -0.107, y: -0.338, z: -0.455 }, 'LeftHandThumb3.rotation': { x: -0.015, y: -0.142, z: 0.002 }, 'LeftHandIndex1.rotation': { x: 0.145, y: 0.032, z: -0.069 }, 'LeftHandIndex2.rotation': { x: 0.323, y: -0.049, z: -0.028 }, 'LeftHandIndex3.rotation': { x: 0.249, y: -0.053, z: -0.074 }, 'LeftHandMiddle1.rotation': { x: 0.235, y: -0.057, z: -0.088 }, 'LeftHandMiddle2.rotation': { x: 0.468, y: -0.036, z: -0.081 }, 'LeftHandMiddle3.rotation': { x: 0.203, y: -0.015, z: -0.017 }, 'LeftHandRing1.rotation': { x: 0.185, y: -0.118, z: -0.157 }, 'LeftHandRing2.rotation': { x: 0.578, y: 0.02, z: -0.097 }, 'LeftHandRing3.rotation': { x: 0.27, y: 0.021, z: -0.063 }, 'LeftHandPinky1.rotation': { x: 0.404, y: -0.182, z: -0.138 }, 'LeftHandPinky2.rotation': { x: 0.612, y: 0.128, z: -0.144 }, 'LeftHandPinky3.rotation': { x: 0.267, y: 0.094, z: -0.081 }, 'RightShoulder.rotation': { x: 1.605, y: 0.17, z: 1.625 }, 'RightArm.rotation': { x: 1.574, y: -0.655, z: 0.388 }, 'RightForeArm.rotation': { x: -0.36, y: -0.849, z: -0.465 }, 'RightHand.rotation': { x: 0.114, y: 0.416, z: -0.069 }, 'RightHandThumb1.rotation': { x: 0.486, y: 0.009, z: -0.492 }, 'RightHandThumb2.rotation': { x: -0.073, y: -0.01, z: 0.284 }, 'RightHandThumb3.rotation': { x: -0.054, y: -0.006, z: 0.209 }, 'RightHandIndex1.rotation': { x: 0.245, y: -0.014, z: 0.052 }, 'RightHandIndex2.rotation': { x: 0.155, y: 0, z: 0 }, 'RightHandIndex3.rotation': { x: 0.153, y: 0, z: 0 }, 'RightHandMiddle1.rotation': { x: 0.238, y: 0.004, z: 0.028 }, 'RightHandMiddle2.rotation': { x: 0.15, y: 0, z: 0 }, 'RightHandMiddle3.rotation': { x: 0.149, y: 0, z: 0 }, 'RightHandRing1.rotation': { x: 0.267, y: 0.012, z: 0.007 }, 'RightHandRing2.rotation': { x: 0.169, y: 0, z: 0 }, 'RightHandRing3.rotation': { x: 0.167, y: 0, z: 0 }, 'RightHandPinky1.rotation': { x: 0.304, y: 0.018, z: -0.021 }, 'RightHandPinky2.rotation': { x: 0.192, y: 0, z: 0 }, 'RightHandPinky3.rotation': { x: 0.19, y: 0, z: 0 }, 'LeftUpLeg.rotation': { x: -0.001, y: -0.058, z: -3.238 }, 'LeftLeg.rotation': { x: -0.29, y: 0.058, z: -0.021 }, 'LeftFoot.rotation': { x: 1.288, y: 0.168, z: 0.183 }, 'LeftToeBase.rotation': { x: 0.363, y: -0.09, z: -0.01 }, 'RightUpLeg.rotation': { x: -0.100, y: 0.36, z: 3.062 }, 'RightLeg.rotation': { x: -0.67, y: -0.304, z: 0.043 }, 'RightFoot.rotation': { x: 1.195, y: -0.159, z: -0.294 }, 'RightToeBase.rotation': { x: 0.737, y: 0.164, z: -0.002 }
        }
    },

    'bend': {
        bend: true, standing: true,
        props: {
            'Hips.position': { x: -0.007, y: 0.943, z: -0.001 }, 'Hips.rotation': { x: 1.488, y: -0.633, z: 1.435 }, 'Spine.rotation': { x: -0.126, y: 0.007, z: -0.057 }, 'Spine1.rotation': { x: -0.134, y: 0.009, z: 0.01 }, 'Spine2.rotation': { x: -0.019, y: 0, z: -0.002 }, 'Neck.rotation': { x: -0.159, y: 0.572, z: -0.108 }, 'Head.rotation': { x: -0.064, y: 0.716, z: -0.257 }, 'RightShoulder.rotation': { x: 1.625, y: -0.043, z: 1.382 }, 'RightArm.rotation': { x: 0.746, y: -0.96, z: -1.009 }, 'RightForeArm.rotation': { x: -0.199, y: -0.528, z: -0.38 }, 'RightHand.rotation': { x: -0.261, y: -0.043, z: -0.027 }, 'RightHandThumb1.rotation': { x: 0.172, y: -0.138, z: -0.445 }, 'RightHandThumb2.rotation': { x: -0.158, y: 0.327, z: 0.545 }, 'RightHandThumb3.rotation': { x: -0.062, y: 0.138, z: 0.152 }, 'RightHandIndex1.rotation': { x: 0.328, y: -0.005, z: 0.132 }, 'RightHandIndex2.rotation': { x: 0.303, y: 0.049, z: 0.028 }, 'RightHandIndex3.rotation': { x: 0.241, y: 0.046, z: 0.077 }, 'RightHandMiddle1.rotation': { x: 0.309, y: 0.074, z: 0.089 }, 'RightHandMiddle2.rotation': { x: 0.392, y: 0.036, z: 0.081 }, 'RightHandMiddle3.rotation': { x: 0.199, y: 0.014, z: 0.019 }, 'RightHandRing1.rotation': { x: 0.239, y: 0.143, z: 0.091 }, 'RightHandRing2.rotation': { x: 0.275, y: -0.02, z: 0.097 }, 'RightHandRing3.rotation': { x: 0.248, y: -0.023, z: 0.061 }, 'RightHandPinky1.rotation': { x: 0.211, y: 0.154, z: 0.029 }, 'RightHandPinky2.rotation': { x: 0.348, y: -0.128, z: 0.144 }, 'RightHandPinky3.rotation': { x: 0.21, y: -0.091, z: 0.065 }, 'LeftShoulder.rotation': { x: 1.626, y: -0.027, z: -1.367 }, 'LeftArm.rotation': { x: 1.048, y: 0.737, z: 0.712 }, 'LeftForeArm.rotation': { x: -0.508, y: 0.879, z: 0.625 }, 'LeftHand.rotation': { x: 0.06, y: -0.243, z: -0.079 }, 'LeftHandThumb1.rotation': { x: 0.187, y: -0.072, z: 0.346 }, 'LeftHandThumb2.rotation': { x: -0.066, y: 0.008, z: -0.256 }, 'LeftHandThumb3.rotation': { x: -0.085, y: 0.014, z: -0.334 }, 'LeftHandIndex1.rotation': { x: -0.1, y: 0.016, z: -0.058 }, 'LeftHandIndex2.rotation': { x: 0.334, y: 0, z: 0 }, 'LeftHandIndex3.rotation': { x: 0.281, y: 0, z: 0 }, 'LeftHandMiddle1.rotation': { x: -0.056, y: 0, z: 0 }, 'LeftHandMiddle2.rotation': { x: 0.258, y: 0, z: 0 }, 'LeftHandMiddle3.rotation': { x: 0.26, y: 0, z: 0 }, 'LeftHandRing1.rotation': { x: -0.067, y: -0.002, z: 0.008 }, 'LeftHandRing2.rotation': { x: 0.259, y: 0, z: 0 }, 'LeftHandRing3.rotation': { x: 0.276, y: 0, z: 0 }, 'LeftHandPinky1.rotation': { x: -0.128, y: -0.007, z: 0.042 }, 'LeftHandPinky2.rotation': { x: 0.227, y: 0, z: 0 }, 'LeftHandPinky3.rotation': { x: 0.145, y: 0, z: 0 }, 'RightUpLeg.rotation': { x: -1.507, y: 0.2, z: -3.043 }, 'RightLeg.rotation': { x: -0.689, y: -0.124, z: 0.017 }, 'RightFoot.rotation': { x: 0.909, y: 0.008, z: -0.093 }, 'RightToeBase.rotation': { x: 0.842, y: 0.075, z: -0.008 }, 'LeftUpLeg.rotation': { x: -1.449, y: -0.2, z: 3.018 }, 'LeftLeg.rotation': { x: -0.74, y: -0.115, z: -0.008 }, 'LeftFoot.rotation': { x: 1.048, y: -0.058, z: 0.117 }, 'LeftToeBase.rotation': { x: 0.807, y: -0.067, z: 0.003 }
        }
    },

    'back': {
        standing: true,
        props: {
            'Hips.position': { x: 0, y: 1, z: 0 }, 'Hips.rotation': { x: -0.732, y: -1.463, z: -0.637 }, 'Spine.rotation': { x: -0.171, y: 0.106, z: 0.157 }, 'Spine1.rotation': { x: -0.044, y: 0.138, z: -0.059 }, 'Spine2.rotation': { x: 0.082, y: 0.133, z: -0.074 }, 'Neck.rotation': { x: 0.39, y: 0.591, z: -0.248 }, 'Head.rotation': { x: -0.001, y: 0.596, z: -0.057 }, 'LeftShoulder.rotation': { x: 1.676, y: 0.007, z: -1.892 }, 'LeftArm.rotation': { x: -5.566, y: 1.188, z: -0.173 }, 'LeftForeArm.rotation': { x: -0.673, y: -0.105, z: 1.702 }, 'LeftHand.rotation': { x: -0.469, y: -0.739, z: 0.003 }, 'LeftHandThumb1.rotation': { x: 0.876, y: 0.274, z: 0.793 }, 'LeftHandThumb2.rotation': { x: 0.161, y: -0.23, z: -0.172 }, 'LeftHandThumb3.rotation': { x: 0.078, y: 0.027, z: 0.156 }, 'LeftHandIndex1.rotation': { x: -0.085, y: -0.002, z: 0.009 }, 'LeftHandIndex2.rotation': { x: 0.176, y: 0, z: -0.002 }, 'LeftHandIndex3.rotation': { x: -0.036, y: 0.001, z: -0.035 }, 'LeftHandMiddle1.rotation': { x: 0.015, y: 0.144, z: -0.076 }, 'LeftHandMiddle2.rotation': { x: 0.378, y: -0.007, z: -0.077 }, 'LeftHandMiddle3.rotation': { x: -0.141, y: -0.001, z: 0.031 }, 'LeftHandRing1.rotation': { x: 0.039, y: 0.02, z: -0.2 }, 'LeftHandRing2.rotation': { x: 0.25, y: -0.002, z: -0.073 }, 'LeftHandRing3.rotation': { x: 0.236, y: 0.006, z: -0.075 }, 'LeftHandPinky1.rotation': { x: 0.172, y: -0.033, z: -0.275 }, 'LeftHandPinky2.rotation': { x: 0.216, y: 0.043, z: -0.054 }, 'LeftHandPinky3.rotation': { x: 0.325, y: 0.078, z: -0.13 }, 'RightShoulder.rotation': { x: 2.015, y: -0.168, z: 1.706 }, 'RightArm.rotation': { x: 0.203, y: -1.258, z: -0.782 }, 'RightForeArm.rotation': { x: -0.658, y: -0.133, z: -1.401 }, 'RightHand.rotation': { x: -1.504, y: 0.375, z: -0.005 }, 'RightHandThumb1.rotation': { x: 0.413, y: -0.158, z: -1.121 }, 'RightHandThumb2.rotation': { x: -0.142, y: -0.008, z: 0.209 }, 'RightHandThumb3.rotation': { x: -0.091, y: 0.021, z: 0.142 }, 'RightHandIndex1.rotation': { x: -0.167, y: 0.014, z: -0.072 }, 'RightHandIndex2.rotation': { x: 0.474, y: 0.009, z: 0.051 }, 'RightHandIndex3.rotation': { x: 0.115, y: 0.006, z: 0.047 }, 'RightHandMiddle1.rotation': { x: 0.385, y: 0.019, z: 0.144 }, 'RightHandMiddle2.rotation': { x: 0.559, y: 0.035, z: 0.101 }, 'RightHandMiddle3.rotation': { x: 0.229, y: 0, z: 0.027 }, 'RightHandRing1.rotation': { x: 0.48, y: 0.026, z: 0.23 }, 'RightHandRing2.rotation': { x: 0.772, y: 0.038, z: 0.109 }, 'RightHandRing3.rotation': { x: 0.622, y: 0.039, z: 0.106 }, 'RightHandPinky1.rotation': { x: 0.767, y: 0.288, z: 0.353 }, 'RightHandPinky2.rotation': { x: 0.886, y: 0.049, z: 0.122 }, 'RightHandPinky3.rotation': { x: 0.662, y: 0.044, z: 0.113 }, 'LeftUpLeg.rotation': { x: -0.206, y: -0.268, z: -3.343 }, 'LeftLeg.rotation': { x: -0.333, y: 0.757, z: -0.043 }, 'LeftFoot.rotation': { x: 1.049, y: 0.167, z: 0.287 }, 'LeftToeBase.rotation': { x: 0.672, y: -0.069, z: -0.004 }, 'RightUpLeg.rotation': { x: 0.055, y: -0.226, z: 3.037 }, 'RightLeg.rotation': { x: -0.559, y: 0.39, z: -0.001 }, 'RightFoot.rotation': { x: 1.2, y: 0.133, z: 0.085 }, 'RightToeBase.rotation': { x: 0.92, y: 0.093, z: -0.013 }
        }
    },

    'straight': {
        standing: true,
        props: {
            'Hips.position': { x: 0, y: 0.989, z: 0.001 }, 'Hips.rotation': { x: 0.047, y: 0.007, z: -0.007 }, 'Spine.rotation': { x: -0.143, y: -0.007, z: 0.005 }, 'Spine1.rotation': { x: -0.043, y: -0.014, z: 0.012 }, 'Spine2.rotation': { x: 0.072, y: -0.013, z: 0.013 }, 'Neck.rotation': { x: 0.048, y: -0.003, z: 0.012 }, 'Head.rotation': { x: 0.05, y: -0.02, z: -0.017 }, 'LeftShoulder.rotation': { x: 1.62, y: -0.166, z: -1.605 }, 'LeftArm.rotation': { x: 1.275, y: 0.544, z: -0.092 }, 'LeftForeArm.rotation': { x: 0, y: 0, z: 0.302 }, 'LeftHand.rotation': { x: -0.225, y: -0.154, z: 0.11 }, 'LeftHandThumb1.rotation': { x: 0.435, y: -0.044, z: 0.457 }, 'LeftHandThumb2.rotation': { x: -0.028, y: 0.002, z: -0.246 }, 'LeftHandThumb3.rotation': { x: -0.236, y: -0.025, z: 0.113 }, 'LeftHandIndex1.rotation': { x: 0.218, y: 0.008, z: -0.081 }, 'LeftHandIndex2.rotation': { x: 0.165, y: -0.001, z: -0.017 }, 'LeftHandIndex3.rotation': { x: 0.165, y: -0.001, z: -0.017 }, 'LeftHandMiddle1.rotation': { x: 0.235, y: -0.011, z: -0.065 }, 'LeftHandMiddle2.rotation': { x: 0.182, y: -0.002, z: -0.019 }, 'LeftHandMiddle3.rotation': { x: 0.182, y: -0.002, z: -0.019 }, 'LeftHandRing1.rotation': { x: 0.316, y: -0.017, z: 0.008 }, 'LeftHandRing2.rotation': { x: 0.253, y: -0.003, z: -0.026 }, 'LeftHandRing3.rotation': { x: 0.255, y: -0.003, z: -0.026 }, 'LeftHandPinky1.rotation': { x: 0.336, y: -0.062, z: 0.088 }, 'LeftHandPinky2.rotation': { x: 0.276, y: -0.004, z: -0.028 }, 'LeftHandPinky3.rotation': { x: 0.276, y: -0.004, z: -0.028 }, 'RightShoulder.rotation': { x: 1.615, y: 0.064, z: 1.53 }, 'RightArm.rotation': { x: 1.313, y: -0.424, z: 0.131 }, 'RightForeArm.rotation': { x: 0, y: 0, z: -0.317 }, 'RightHand.rotation': { x: -0.158, y: -0.639, z: -0.196 }, 'RightHandThumb1.rotation': { x: 0.44, y: 0.048, z: -0.549 }, 'RightHandThumb2.rotation': { x: -0.056, y: -0.008, z: 0.274 }, 'RightHandThumb3.rotation': { x: -0.258, y: 0.031, z: -0.095 }, 'RightHandIndex1.rotation': { x: 0.169, y: -0.011, z: 0.105 }, 'RightHandIndex2.rotation': { x: 0.134, y: 0.001, z: 0.011 }, 'RightHandIndex3.rotation': { x: 0.134, y: 0.001, z: 0.011 }, 'RightHandMiddle1.rotation': { x: 0.288, y: 0.014, z: 0.092 }, 'RightHandMiddle2.rotation': { x: 0.248, y: 0.003, z: 0.02 }, 'RightHandMiddle3.rotation': { x: 0.249, y: 0.003, z: 0.02 }, 'RightHandRing1.rotation': { x: 0.369, y: 0.019, z: 0.006 }, 'RightHandRing2.rotation': { x: 0.321, y: 0.004, z: 0.026 }, 'RightHandRing3.rotation': { x: 0.323, y: 0.004, z: 0.026 }, 'RightHandPinky1.rotation': { x: 0.468, y: 0.085, z: -0.03 }, 'RightHandPinky2.rotation': { x: 0.427, y: 0.007, z: 0.034 }, 'RightHandPinky3.rotation': { x: 0.142, y: 0.001, z: 0.012 }, 'LeftUpLeg.rotation': { x: -0.077, y: -0.058, z: 3.126 }, 'LeftLeg.rotation': { x: -0.252, y: 0.001, z: -0.018 }, 'LeftFoot.rotation': { x: 1.315, y: -0.064, z: 0.315 }, 'LeftToeBase.rotation': { x: 0.577, y: -0.07, z: -0.009 }, 'RightUpLeg.rotation': { x: -0.083, y: -0.032, z: 3.124 }, 'RightLeg.rotation': { x: -0.272, y: -0.003, z: 0.021 }, 'RightFoot.rotation': { x: 1.342, y: 0.076, z: -0.222 }, 'RightToeBase.rotation': { x: 0.44, y: 0.069, z: 0.016 }
        }
    },

    'wide': {
        standing: true,
        props: {
            'Hips.position': { x: 0, y: 1.017, z: 0.016 }, 'Hips.rotation': { x: 0.064, y: -0.048, z: 0.059 }, 'Spine.rotation': { x: -0.123, y: 0, z: -0.018 }, 'Spine1.rotation': { x: 0.014, y: 0.003, z: -0.006 }, 'Spine2.rotation': { x: 0.04, y: 0.003, z: -0.007 }, 'Neck.rotation': { x: 0.101, y: 0.007, z: -0.035 }, 'Head.rotation': { x: -0.091, y: -0.049, z: 0.105 }, 'RightShoulder.rotation': { x: 1.831, y: 0.017, z: 1.731 }, 'RightArm.rotation': { x: -1.673, y: -1.102, z: -3.132 }, 'RightForeArm.rotation': { x: 0.265, y: 0.23, z: -0.824 }, 'RightHand.rotation': { x: -0.52, y: 0.345, z: -0.061 }, 'RightHandThumb1.rotation': { x: 0.291, y: 0.056, z: -0.428 }, 'RightHandThumb2.rotation': { x: 0.025, y: 0.005, z: 0.166 }, 'RightHandThumb3.rotation': { x: -0.089, y: 0.009, z: 0.068 }, 'RightHandIndex1.rotation': { x: 0.392, y: -0.015, z: 0.11 }, 'RightHandIndex2.rotation': { x: 0.391, y: 0.001, z: 0.004 }, 'RightHandIndex3.rotation': { x: 0.326, y: 0, z: 0.003 }, 'RightHandMiddle1.rotation': { x: 0.285, y: 0.068, z: 0.081 }, 'RightHandMiddle2.rotation': { x: 0.519, y: 0.004, z: 0.011 }, 'RightHandMiddle3.rotation': { x: 0.252, y: 0, z: 0.001 }, 'RightHandRing1.rotation': { x: 0.207, y: 0.133, z: 0.146 }, 'RightHandRing2.rotation': { x: 0.597, y: 0.004, z: 0.004 }, 'RightHandRing3.rotation': { x: 0.292, y: 0.002, z: 0.012 }, 'RightHandPinky1.rotation': { x: 0.338, y: 0.182, z: 0.136 }, 'RightHandPinky2.rotation': { x: 0.533, y: 0.002, z: 0.004 }, 'RightHandPinky3.rotation': { x: 0.194, y: 0, z: 0.002 }, 'LeftShoulder.rotation': { x: 1.83, y: -0.063, z: -1.808 }, 'LeftArm.rotation': { x: -1.907, y: 1.228, z: -2.959 }, 'LeftForeArm.rotation': { x: -0.159, y: 0.268, z: 0.572 }, 'LeftHand.rotation': { x: 0.069, y: -0.498, z: -0.025 }, 'LeftHandThumb1.rotation': { x: 0.738, y: 0.123, z: 0.178 }, 'LeftHandThumb2.rotation': { x: -0.26, y: 0.028, z: -0.477 }, 'LeftHandThumb3.rotation': { x: -0.448, y: 0.093, z: -0.661 }, 'LeftHandIndex1.rotation': { x: 1.064, y: 0.005, z: -0.13 }, 'LeftHandIndex2.rotation': { x: 1.55, y: -0.143, z: -0.136 }, 'LeftHandIndex3.rotation': { x: 0.722, y: -0.076, z: -0.127 }, 'LeftHandMiddle1.rotation': { x: 1.095, y: -0.091, z: 0.006 }, 'LeftHandMiddle2.rotation': { x: 1.493, y: -0.174, z: -0.151 }, 'LeftHandMiddle3.rotation': { x: 0.651, y: -0.031, z: -0.087 }, 'LeftHandRing1.rotation': { x: 1.083, y: -0.224, z: 0.072 }, 'LeftHandRing2.rotation': { x: 1.145, y: -0.107, z: -0.195 }, 'LeftHandRing3.rotation': { x: 1.208, y: -0.134, z: -0.158 }, 'LeftHandPinky1.rotation': { x: 0.964, y: -0.383, z: 0.128 }, 'LeftHandPinky2.rotation': { x: 1.457, y: -0.146, z: -0.159 }, 'LeftHandPinky3.rotation': { x: 1.019, y: -0.102, z: -0.141 }, 'RightUpLeg.rotation': { x: -0.221, y: -0.233, z: 2.87 }, 'RightLeg.rotation': { x: -0.339, y: -0.043, z: -0.041 }, 'RightFoot.rotation': { x: 1.081, y: 0.177, z: 0.114 }, 'RightToeBase.rotation': { x: 0.775, y: 0, z: 0 }, 'LeftUpLeg.rotation': { x: -0.185, y: 0.184, z: 3.131 }, 'LeftLeg.rotation': { x: -0.408, y: 0.129, z: 0.02 }, 'LeftFoot.rotation': { x: 1.167, y: -0.002, z: -0.007 }, 'LeftToeBase.rotation': { x: 0.723, y: 0, z: 0 }
        }
    },

    'oneknee': {
        kneeling: true,
        props: {
            'Hips.position': { x: -0.005, y: 0.415, z: -0.017 }, 'Hips.rotation': { x: -0.25, y: 0.04, z: -0.238 }, 'Spine.rotation': { x: 0.037, y: 0.043, z: 0.047 }, 'Spine1.rotation': { x: 0.317, y: 0.103, z: 0.066 }, 'Spine2.rotation': { x: 0.433, y: 0.109, z: 0.054 }, 'Neck.rotation': { x: -0.156, y: -0.092, z: 0.059 }, 'Head.rotation': { x: -0.398, y: -0.032, z: 0.018 }, 'RightShoulder.rotation': { x: 1.546, y: 0.119, z: 1.528 }, 'RightArm.rotation': { x: 0.896, y: -0.247, z: -0.512 }, 'RightForeArm.rotation': { x: 0.007, y: 0, z: -1.622 }, 'RightHand.rotation': { x: 1.139, y: -0.853, z: 0.874 }, 'RightHandThumb1.rotation': { x: 0.176, y: 0.107, z: -0.311 }, 'RightHandThumb2.rotation': { x: -0.047, y: -0.003, z: 0.12 }, 'RightHandThumb3.rotation': { x: 0, y: 0, z: 0 }, 'RightHandIndex1.rotation': { x: 0.186, y: 0.005, z: 0.125 }, 'RightHandIndex2.rotation': { x: 0.454, y: 0.005, z: 0.015 }, 'RightHandIndex3.rotation': { x: 0, y: 0, z: 0 }, 'RightHandMiddle1.rotation': { x: 0.444, y: 0.035, z: 0.127 }, 'RightHandMiddle2.rotation': { x: 0.403, y: -0.006, z: -0.04 }, 'RightHandMiddle3.rotation': { x: 0, y: 0, z: 0 }, 'RightHandRing1.rotation': { x: 0.543, y: 0.074, z: 0.121 }, 'RightHandRing2.rotation': { x: 0.48, y: -0.018, z: -0.063 }, 'RightHandRing3.rotation': { x: 0, y: 0, z: 0 }, 'RightHandPinky1.rotation': { x: 0.464, y: 0.086, z: 0.113 }, 'RightHandPinky2.rotation': { x: 0.667, y: -0.06, z: -0.128 }, 'RightHandPinky3.rotation': { x: 0, y: 0, z: 0 }, 'LeftShoulder.rotation': { x: 1.545, y: -0.116, z: -1.529 }, 'LeftArm.rotation': { x: 0.799, y: 0.631, z: 0.556 }, 'LeftForeArm.rotation': { x: -0.002, y: 0.007, z: 0.926 }, 'LeftHand.rotation': { x: -0.508, y: 0.439, z: 0.502 }, 'LeftHandThumb1.rotation': { x: 0.651, y: -0.035, z: 0.308 }, 'LeftHandThumb2.rotation': { x: -0.053, y: 0.008, z: -0.11 }, 'LeftHandThumb3.rotation': { x: 0, y: 0, z: 0 }, 'LeftHandIndex1.rotation': { x: 0.662, y: -0.053, z: -0.116 }, 'LeftHandIndex2.rotation': { x: 0.309, y: -0.004, z: -0.02 }, 'LeftHandIndex3.rotation': { x: 0, y: 0, z: 0 }, 'LeftHandMiddle1.rotation': { x: 0.501, y: -0.062, z: -0.12 }, 'LeftHandMiddle2.rotation': { x: 0.144, y: -0.002, z: 0.016 }, 'LeftHandMiddle3.rotation': { x: 0, y: 0, z: 0 }, 'LeftHandRing1.rotation': { x: 0.397, y: -0.029, z: -0.143 }, 'LeftHandRing2.rotation': { x: 0.328, y: 0.01, z: 0.059 }, 'LeftHandRing3.rotation': { x: 0, y: 0, z: 0 }, 'LeftHandPinky1.rotation': { x: 0.194, y: 0.008, z: -0.164 }, 'LeftHandPinky2.rotation': { x: 0.38, y: 0.031, z: 0.128 }, 'LeftHandPinky3.rotation': { x: 0, y: 0, z: 0 }, 'RightUpLeg.rotation': { x: -1.594, y: -0.251, z: 2.792 }, 'RightLeg.rotation': { x: -2.301, y: -0.073, z: 0.055 }, 'RightFoot.rotation': { x: 1.553, y: -0.207, z: -0.094 }, 'RightToeBase.rotation': { x: 0.459, y: 0.069, z: 0.016 }, 'LeftUpLeg.rotation': { x: -0.788, y: -0.236, z: -2.881 }, 'LeftLeg.rotation': { x: -2.703, y: 0.012, z: -0.047 }, 'LeftFoot.rotation': { x: 2.191, y: -0.102, z: 0.019 }, 'LeftToeBase.rotation': { x: 1.215, y: -0.027, z: 0.01 }
        }
    },

    'kneel': {
        kneeling: true, lying: true,
        props: {
            'Hips.position': { x: 0, y: 0.532, z: -0.002 }, 'Hips.rotation': { x: 0.018, y: -0.008, z: -0.017 }, 'Spine.rotation': { x: -0.139, y: -0.01, z: 0.002 }, 'Spine1.rotation': { x: 0.002, y: -0.002, z: 0.001 }, 'Spine2.rotation': { x: 0.028, y: -0.002, z: 0.001 }, 'Neck.rotation': { x: -0.007, y: 0, z: -0.002 }, 'Head.rotation': { x: -0.02, y: -0.008, z: -0.004 }, 'LeftShoulder.rotation': { x: 1.77, y: -0.428, z: -1.588 }, 'LeftArm.rotation': { x: 0.911, y: 0.343, z: 0.083 }, 'LeftForeArm.rotation': { x: 0, y: 0, z: 0.347 }, 'LeftHand.rotation': { x: 0.033, y: -0.052, z: -0.105 }, 'LeftHandThumb1.rotation': { x: 0.508, y: -0.22, z: 0.708 }, 'LeftHandThumb2.rotation': { x: -0.323, y: -0.139, z: -0.56 }, 'LeftHandThumb3.rotation': { x: -0.328, y: 0.16, z: -0.301 }, 'LeftHandIndex1.rotation': { x: 0.178, y: 0.248, z: 0.045 }, 'LeftHandIndex2.rotation': { x: 0.236, y: -0.002, z: -0.019 }, 'LeftHandIndex3.rotation': { x: -0.062, y: 0, z: 0.005 }, 'LeftHandMiddle1.rotation': { x: 0.123, y: -0.005, z: -0.019 }, 'LeftHandMiddle2.rotation': { x: 0.589, y: -0.014, z: -0.045 }, 'LeftHandMiddle3.rotation': { x: 0.231, y: -0.002, z: -0.019 }, 'LeftHandRing1.rotation': { x: 0.196, y: -0.008, z: -0.091 }, 'LeftHandRing2.rotation': { x: 0.483, y: -0.009, z: -0.038 }, 'LeftHandRing3.rotation': { x: 0.367, y: -0.005, z: -0.029 }, 'LeftHandPinky1.rotation': { x: 0.191, y: -0.269, z: -0.246 }, 'LeftHandPinky2.rotation': { x: 0.37, y: -0.006, z: -0.029 }, 'LeftHandPinky3.rotation': { x: 0.368, y: -0.005, z: -0.029 }, 'RightShoulder.rotation': { x: 1.73, y: 0.434, z: 1.715 }, 'RightArm.rotation': { x: 0.841, y: -0.508, z: -0.155 }, 'RightForeArm.rotation': { x: 0, y: 0, z: -0.355 }, 'RightHand.rotation': { x: 0.091, y: 0.137, z: 0.197 }, 'RightHandThumb1.rotation': { x: 0.33, y: 0.051, z: -0.753 }, 'RightHandThumb2.rotation': { x: -0.113, y: 0.075, z: 0.612 }, 'RightHandThumb3.rotation': { x: -0.271, y: -0.166, z: 0.164 }, 'RightHandIndex1.rotation': { x: 0.073, y: 0.001, z: -0.093 }, 'RightHandIndex2.rotation': { x: 0.338, y: 0.006, z: 0.034 }, 'RightHandIndex3.rotation': { x: 0.131, y: 0.001, z: 0.013 }, 'RightHandMiddle1.rotation': { x: 0.13, y: 0.005, z: -0.017 }, 'RightHandMiddle2.rotation': { x: 0.602, y: 0.018, z: 0.058 }, 'RightHandMiddle3.rotation': { x: -0.031, y: 0, z: -0.003 }, 'RightHandRing1.rotation': { x: 0.351, y: 0.019, z: 0.045 }, 'RightHandRing2.rotation': { x: 0.19, y: 0.002, z: 0.019 }, 'RightHandRing3.rotation': { x: 0.21, y: 0.002, z: 0.021 }, 'RightHandPinky1.rotation': { x: 0.256, y: 0.17, z: 0.118 }, 'RightHandPinky2.rotation': { x: 0.451, y: 0.01, z: 0.045 }, 'RightHandPinky3.rotation': { x: 0.346, y: 0.006, z: 0.035 }, 'LeftUpLeg.rotation': { x: -0.06, y: 0.1, z: -2.918 }, 'LeftLeg.rotation': { x: -1.933, y: -0.01, z: 0.011 }, 'LeftFoot.rotation': { x: 0.774, y: -0.162, z: -0.144 }, 'LeftToeBase.rotation': { x: 1.188, y: 0, z: 0 }, 'RightUpLeg.rotation': { x: -0.099, y: -0.057, z: 2.922 }, 'RightLeg.rotation': { x: -1.93, y: 0.172, z: -0.02 }, 'RightFoot.rotation': { x: 0.644, y: 0.251, z: 0.212 }, 'RightToeBase.rotation': { x: 0.638, y: -0.034, z: -0.001 }
        }
    },

    'sitting': {
        sitting: true, lying: true,
        props: {
            'Hips.position': { x: 0, y: 0.117, z: 0.005 }, 'Hips.rotation': { x: -0.411, y: -0.049, z: 0.056 }, 'Spine.rotation': { x: 0.45, y: -0.039, z: -0.116 }, 'Spine1.rotation': { x: 0.092, y: -0.076, z: 0.08 }, 'Spine2.rotation': { x: 0.073, y: 0.035, z: 0.066 }, 'Neck.rotation': { x: 0.051, y: 0.053, z: -0.079 }, 'Head.rotation': { x: -0.169, y: 0.009, z: 0.034 }, 'LeftShoulder.rotation': { x: 1.756, y: -0.037, z: -1.301 }, 'LeftArm.rotation': { x: -0.098, y: 0.016, z: 1.006 }, 'LeftForeArm.rotation': { x: -0.089, y: 0.08, z: 0.837 }, 'LeftHand.rotation': { x: 0.262, y: -0.399, z: 0.3 }, 'LeftHandThumb1.rotation': { x: 0.149, y: -0.043, z: 0.452 }, 'LeftHandThumb2.rotation': { x: 0.032, y: 0.006, z: -0.162 }, 'LeftHandThumb3.rotation': { x: -0.086, y: -0.005, z: -0.069 }, 'LeftHandIndex1.rotation': { x: 0.145, y: 0.032, z: -0.069 }, 'LeftHandIndex2.rotation': { x: 0.325, y: -0.001, z: -0.004 }, 'LeftHandIndex3.rotation': { x: 0.253, y: 0, z: -0.003 }, 'LeftHandMiddle1.rotation': { x: 0.186, y: -0.051, z: -0.091 }, 'LeftHandMiddle2.rotation': { x: 0.42, y: -0.003, z: -0.011 }, 'LeftHandMiddle3.rotation': { x: 0.153, y: 0.001, z: -0.001 }, 'LeftHandRing1.rotation': { x: 0.087, y: -0.19, z: -0.078 }, 'LeftHandRing2.rotation': { x: 0.488, y: -0.004, z: -0.005 }, 'LeftHandRing3.rotation': { x: 0.183, y: -0.001, z: -0.012 }, 'LeftHandPinky1.rotation': { x: 0.205, y: -0.262, z: 0.051 }, 'LeftHandPinky2.rotation': { x: 0.407, y: -0.002, z: -0.004 }, 'LeftHandPinky3.rotation': { x: 0.068, y: 0, z: -0.002 }, 'RightShoulder.rotation': { x: 1.619, y: -0.139, z: 1.179 }, 'RightArm.rotation': { x: 0.17, y: -0.037, z: -1.07 }, 'RightForeArm.rotation': { x: -0.044, y: -0.056, z: -0.665 }, 'RightHand.rotation': { x: 0.278, y: 0.454, z: -0.253 }, 'RightHandThumb1.rotation': { x: 0.173, y: 0.089, z: -0.584 }, 'RightHandThumb2.rotation': { x: -0.003, y: -0.004, z: 0.299 }, 'RightHandThumb3.rotation': { x: -0.133, y: -0.002, z: 0.235 }, 'RightHandIndex1.rotation': { x: 0.393, y: -0.023, z: 0.108 }, 'RightHandIndex2.rotation': { x: 0.391, y: 0.001, z: 0.004 }, 'RightHandIndex3.rotation': { x: 0.326, y: 0, z: 0.003 }, 'RightHandMiddle1.rotation': { x: 0.285, y: 0.062, z: 0.086 }, 'RightHandMiddle2.rotation': { x: 0.519, y: 0.003, z: 0.011 }, 'RightHandMiddle3.rotation': { x: 0.252, y: -0.001, z: 0.001 }, 'RightHandRing1.rotation': { x: 0.207, y: 0.122, z: 0.155 }, 'RightHandRing2.rotation': { x: 0.597, y: 0.004, z: 0.005 }, 'RightHandRing3.rotation': { x: 0.292, y: 0.001, z: 0.012 }, 'RightHandPinky1.rotation': { x: 0.338, y: 0.171, z: 0.149 }, 'RightHandPinky2.rotation': { x: 0.533, y: 0.002, z: 0.004 }, 'RightHandPinky3.rotation': { x: 0.194, y: 0, z: 0.002 }, 'LeftUpLeg.rotation': { x: -1.957, y: 0.083, z: -2.886 }, 'LeftLeg.rotation': { x: -1.46, y: 0.123, z: 0.005 }, 'LeftFoot.rotation': { x: -0.013, y: 0.016, z: 0.09 }, 'LeftToeBase.rotation': { x: 0.744, y: 0, z: 0 }, 'RightUpLeg.rotation': { x: -1.994, y: 0.125, z: 2.905 }, 'RightLeg.rotation': { x: -1.5, y: -0.202, z: -0.006 }, 'RightFoot.rotation': { x: -0.012, y: -0.065, z: 0.081 }, 'RightToeBase.rotation': { x: 0.758, y: 0, z: 0 }
        }
    }
};

/**
 * Pose deltas for animation transitions
 * NOTE: In this object (x,y,z) are always Euler rotations despite the name!!
 * NOTE: This object should include all the used delta properties.
 */
export const POSE_DELTA = {
    props: {
        'Hips.quaternion': { x: 0, y: 0, z: 0 }, 'Spine.quaternion': { x: 0, y: 0, z: 0 },
        'Spine1.quaternion': { x: 0, y: 0, z: 0 }, 'Neck.quaternion': { x: 0, y: 0, z: 0 },
        'Head.quaternion': { x: 0, y: 0, z: 0 }, 'Spine1.scale': { x: 0, y: 0, z: 0 },
        'Neck.scale': { x: 0, y: 0, z: 0 }, 'LeftArm.scale': { x: 0, y: 0, z: 0 },
        'RightArm.scale': { x: 0, y: 0, z: 0 }
    }
};
/**
 * Helper function to add quaternion properties for limbs to POSE_DELTA
 * This is used by the animator to initialize the pose delta properties
 */
export function initializePoseDelta(poseDelta) {
    // Add legs, arms and hands
    ['Left', 'Right'].forEach(x => {
        ['Leg', 'UpLeg', 'Arm', 'ForeArm', 'Hand'].forEach(y => {
            poseDelta.props[x + y + '.quaternion'] = { x: 0, y: 0, z: 0 };
        });
        ['HandThumb', 'HandIndex', 'HandMiddle', 'HandRing', 'HandPinky'].forEach(y => {
            poseDelta.props[x + y + '1.quaternion'] = { x: 0, y: 0, z: 0 };
            poseDelta.props[x + y + '2.quaternion'] = { x: 0, y: 0, z: 0 };
            poseDelta.props[x + y + '3.quaternion'] = { x: 0, y: 0, z: 0 };
        });
    });
    return poseDelta;
}

// Default animation configuration values are defined at the top of the file


/**
 * Animation templates
 *
 * baseline: Describes morph target baseline. Values can be either float or
 *           an array [start,end,skew] describing a probability distribution.
 * speech  : Describes voice rate, pitch and volume as deltas to the values
 *           set as options.
 * anims   : Animations for breathing, pose, etc. To be used animation
 *           sequence is selected in the following order:
 *           1. State (idle, speaking, listening)
 *           2. Mood (moodX, moodY)
 *           3. Pose (poseX, poseY)
 *           5. View (full, upper, head)
 *           6. Body form ('M','F')
 *           7. Alt (sequence of objects with propabilities p. If p is not
 *              specified, the remaining part is shared equivally among
 *              the rest.)
 *           8. Current object
 * object  : delay, delta times dt and values vs.
 */

/**
 * Animation template for eye movements
 * Note: The p (probability) functions need to be evaluated in the context of the animator
 */
export const ANIM_TEMPLATE_EYES = {
    name: 'eyes',
    idle: {
        alt: [
            {
                // This function will be called with the animator's context
                p: function () {
                    return (this.avatar?.hasOwnProperty('avatarIdleEyeContact') ?
                        this.avatar.avatarIdleEyeContact :
                        this.opt.avatarIdleEyeContact);
                },
                delay: [200, 5000],
                dt: [200, [2000, 5000], [3000, 10000, 1, 2]],
                vs: {
                    // This will be evaluated in the animator
                    headMove: function (animator) {
                        return [animator.avatar?.hasOwnProperty('avatarIdleHeadMove') ?
                            animator.avatar.avatarIdleHeadMove :
                            animator.opt.avatarIdleHeadMove];
                    },
                    eyesRotateY: [[-0.6, 0.6]],
                    eyesRotateX: [[-0.2, 0.6]],
                    eyeContact: [null, 1]
                }
            },
            {
                delay: [200, 5000],
                dt: [200, [2000, 5000, 1, 2]],
                vs: {
                    // This will be evaluated in the animator
                    headMove: function (animator) {
                        return [animator.avatar?.hasOwnProperty('avatarIdleHeadMove') ?
                            animator.avatar.avatarIdleHeadMove :
                            animator.opt.avatarIdleHeadMove];
                    },
                    eyesRotateY: [[-0.6, 0.6]],
                    eyesRotateX: [[-0.2, 0.6]]
                }
            }
        ]
    },
    speaking: {
        alt: [
            {
                // This function will be called with the animator's context
                p: function () {
                    return (this.avatar?.hasOwnProperty('avatarSpeakingEyeContact') ?
                        this.avatar.avatarSpeakingEyeContact :
                        this.opt.avatarSpeakingEyeContact);
                },
                delay: [200, 5000],
                dt: [0, [3000, 10000, 1, 2], [2000, 5000]],
                vs: {
                    eyeContact: [1, null],
                    // This will be evaluated in the animator
                    headMove: function (animator) {
                        return [null,
                            (animator.avatar?.hasOwnProperty('avatarSpeakingHeadMove') ?
                                animator.avatar.avatarSpeakingHeadMove :
                                animator.opt.avatarSpeakingHeadMove),
                            null];
                    },
                    eyesRotateY: [null, [-0.6, 0.6]],
                    eyesRotateX: [null, [-0.2, 0.6]]
                }
            },
            {
                delay: [200, 5000],
                dt: [200, [2000, 5000, 1, 2]],
                vs: {
                    // This will be evaluated in the animator
                    headMove: function (animator) {
                        return [(animator.avatar?.hasOwnProperty('avatarSpeakingHeadMove') ?
                            animator.avatar.avatarSpeakingHeadMove :
                            animator.opt.avatarSpeakingHeadMove),
                            null];
                    },
                    eyesRotateY: [[-0.6, 0.6]],
                    eyesRotateX: [[-0.2, 0.6]]
                }
            }
        ]
    }
};

/**
 * Morph target configuration
 * Unified configuration for morph target animation
 */
export const MORPH_TARGET_CONFIG = {
    // Default values for all morph targets
    defaults: {
        easing: 5,           // Sigmoid factor for default ease in/out
        acceleration: 0.01,  // Default acceleration [rad / s^2]
        maxVelocity: 5,      // Default maximum velocity [rad / s]
        baseline: 0,         // Default baseline value
        min: 0,              // Default minimum value
        max: 1,              // Default maximum value
    },

    // Custom morph targets (non-standard targets)
    custom: [
        "handFistLeft", "handFistRight", 'bodyRotateX', 'bodyRotateY',
        'bodyRotateZ', 'headRotateX', 'headRotateY', 'headRotateZ', 'chestInhale'
    ],

    // Exceptions to default values for specific morph targets
    exceptions: {
        // Acceleration exceptions
        acceleration: {
            eyeBlinkLeft: 0.1, eyeBlinkRight: 0.1, eyeLookOutLeft: 0.1,
            eyeLookInLeft: 0.1, eyeLookOutRight: 0.1, eyeLookInRight: 0.1
        },

        // Maximum velocity exceptions
        maxVelocity: {
            bodyRotateX: 1, bodyRotateY: 1, bodyRotateZ: 1
        },

        // Baseline exceptions
        baseline: {
            bodyRotateX: null, bodyRotateY: null, bodyRotateZ: null,
            eyeLookOutLeft: null, eyeLookInLeft: null, eyeLookOutRight: null,
            eyeLookInRight: null, eyesLookDown: null, eyesLookUp: null
        },

        // Min/max value exceptions
        min: {
            bodyRotateX: -1, bodyRotateY: -1, bodyRotateZ: -1,
            headRotateX: -1, headRotateY: -1, headRotateZ: -1
        },

        max: {}
    },

    // Constraint functions for morph targets
    constraints: {
        eyeBlinkLeft: function (v) {
            return Math.max(v, (this.mtAvatar['eyesLookDown'].value + this.mtAvatar['browDownLeft'].value) / 2);
        },
        eyeBlinkRight: function (v) {
            return Math.max(v, (this.mtAvatar['eyesLookDown'].value + this.mtAvatar['browDownRight'].value) / 2);
        }
    },

    // Change handlers for specific morph targets
    // These functions will be called with the animator's context
    changeHandlers: {
        eyesLookDown: function () {
            this.mtAvatar['eyeBlinkLeft'].needsUpdate = true;
            this.mtAvatar['eyeBlinkRight'].needsUpdate = true;
        },
        browDownLeft: function () {
            this.mtAvatar['eyeBlinkLeft'].needsUpdate = true;
        },
        browDownRight: function () {
            this.mtAvatar['eyeBlinkRight'].needsUpdate = true;
        }
    },

    // Morph targets that can be randomized for more natural animations
    randomized: [
        'mouthDimpleLeft', 'mouthDimpleRight', 'mouthLeft', 'mouthPressLeft',
        'mouthPressRight', 'mouthStretchLeft', 'mouthStretchRight',
        'mouthShrugLower', 'mouthShrugUpper', 'noseSneerLeft', 'noseSneerRight',
        'mouthRollLower', 'mouthRollUpper', 'browDownLeft', 'browDownRight',
        'browOuterUpLeft', 'browOuterUpRight', 'cheekPuff', 'cheekSquintLeft',
        'cheekSquintRight'
    ]
};

// Simplified backward compatibility exports (use MORPH_TARGET_CONFIG directly instead)
export const MT_CUSTOMS = MORPH_TARGET_CONFIG.custom;

// Animation timing and clock configuration is defined at the top of the file

/**
 * Lip-sync configuration
 * These settings control how speech is mapped to facial expressions
 */

/**
 * Standard viseme names used for lip sync
 */
export const VISEME_NAMES = [
    'aa', 'E', 'I', 'O', 'U', 'PP', 'SS', 'TH', 'DD', 'FF', 'kk',
    'nn', 'RR', 'CH', 'sil'
];

/**
 * Viseme to morph target mapping
 * Maps each viseme to a set of morph target values
 */
export const VISEME_TO_MORPH = {
    // Vowels
    'aa': { // "ah" as in "father"
        mouthOpen: 0.7,
        jawOpen: 0.5,
        mouthFunnel: 0.2,
        tongueOut: 0.1
    },
    'E': { // "eh" as in "bed"
        mouthOpen: 0.5,
        jawOpen: 0.3,
        mouthStretchLeft: 0.3,
        mouthStretchRight: 0.3
    },
    'I': { // "ee" as in "see"
        mouthOpen: 0.3,
        jawOpen: 0.2,
        mouthStretchLeft: 0.5,
        mouthStretchRight: 0.5
    },
    'O': { // "oh" as in "go"
        mouthOpen: 0.6,
        jawOpen: 0.4,
        mouthFunnel: 0.6,
        mouthPucker: 0.4
    },
    'U': { // "oo" as in "boot"
        mouthOpen: 0.3,
        jawOpen: 0.2,
        mouthPucker: 0.8
    },

    // Consonants
    'PP': { // "p", "b", "m"
        mouthClose: 0.9,
        mouthPucker: 0.3
    },
    'SS': { // "s", "z"
        mouthOpen: 0.2,
        jawOpen: 0.1,
        mouthStretchLeft: 0.3,
        mouthStretchRight: 0.3
    },
    'TH': { // "th" as in "think"
        mouthOpen: 0.2,
        jawOpen: 0.1,
        tongueOut: 0.5
    },
    'DD': { // "d", "t"
        mouthOpen: 0.3,
        jawOpen: 0.2,
        tongueOut: 0.3
    },
    'FF': { // "f", "v"
        mouthOpen: 0.2,
        jawOpen: 0.1,
        mouthPucker: 0.3,
        mouthLowerDownLeft: 0.3,
        mouthLowerDownRight: 0.3
    },
    'kk': { // "k", "g"
        mouthOpen: 0.4,
        jawOpen: 0.3,
        tongueOut: 0.2
    },
    'nn': { // "n"
        mouthOpen: 0.2,
        jawOpen: 0.1,
        tongueOut: 0.4
    },
    'RR': { // "r"
        mouthOpen: 0.3,
        jawOpen: 0.2,
        mouthFunnel: 0.3
    },
    'CH': { // "ch", "j", "sh"
        mouthOpen: 0.2,
        jawOpen: 0.1,
        mouthPucker: 0.5
    },
    'sil': { // Silence
        mouthClose: 0.5
    }
};

/**
 * Language-specific viseme mappings
 * Maps phonemes in different languages to standard visemes
 */
export const LANGUAGE_VISEME_MAPS = {
    'en': {
        // English phoneme to viseme mapping
        'AA': 'aa', 'AE': 'aa', 'AH': 'aa', 'AO': 'O', 'AW': 'aa',
        'AY': 'aa', 'EH': 'E', 'ER': 'E', 'EY': 'E', 'IH': 'I',
        'IY': 'I', 'OW': 'O', 'OY': 'O', 'UH': 'U', 'UW': 'U',
        'B': 'PP', 'D': 'DD', 'DH': 'TH', 'F': 'FF', 'G': 'kk',
        'HH': 'kk', 'JH': 'CH', 'K': 'kk', 'L': 'DD', 'M': 'PP',
        'N': 'nn', 'NG': 'nn', 'P': 'PP', 'R': 'RR', 'S': 'SS',
        'SH': 'CH', 'T': 'DD', 'TH': 'TH', 'V': 'FF', 'W': 'U',
        'Y': 'I', 'Z': 'SS', 'ZH': 'CH', 'sil': 'sil'
    },
    'zh': {
        // Simplified Chinese phoneme to viseme mapping
        'a': 'aa', 'ai': 'aa', 'an': 'aa', 'ang': 'aa', 'ao': 'O',
        'e': 'E', 'ei': 'E', 'en': 'E', 'eng': 'E', 'er': 'E',
        'i': 'I', 'ia': 'I', 'ian': 'I', 'iang': 'I', 'iao': 'I',
        'ie': 'I', 'in': 'I', 'ing': 'I', 'iong': 'I', 'iu': 'I',
        'o': 'O', 'ong': 'O', 'ou': 'O', 'u': 'U', 'ua': 'U',
        'uai': 'U', 'uan': 'U', 'uang': 'U', 'ui': 'U', 'un': 'U',
        'uo': 'U', 'v': 'U', 'b': 'PP', 'p': 'PP', 'm': 'PP',
        'f': 'FF', 'd': 'DD', 't': 'DD', 'n': 'nn', 'l': 'DD',
        'g': 'kk', 'k': 'kk', 'h': 'kk', 'j': 'CH', 'q': 'CH',
        'x': 'SS', 'zh': 'CH', 'ch': 'CH', 'sh': 'CH', 'r': 'RR',
        'z': 'SS', 'c': 'SS', 's': 'SS', 'sil': 'sil'
    }
};

/**
 * Required morph targets for animation
 * These will be created as placeholders if not found in the mesh
 */
export const REQUIRED_MORPH_TARGETS = [
    // Basic mouth morphs
    'mouthOpen', 'mouthClose', 'mouthSmile', 'jawOpen',
    // Eye morphs
    'eyeBlinkLeft', 'eyeBlinkRight', 'eyesClosed',
    'eyesLookUp', 'eyesLookDown',
    'eyeLookOutLeft', 'eyeLookInLeft', 'eyeLookOutRight', 'eyeLookInRight',
    'eyeSquintLeft', 'eyeSquintRight', 'eyeWideLeft', 'eyeWideRight',
    // Brow morphs
    'browDownLeft', 'browDownRight', 'browInnerUp',
    'browOuterUpLeft', 'browOuterUpRight',
    // Additional mouth morphs
    'mouthFrownLeft', 'mouthFrownRight', 'mouthLeft', 'mouthDimpleLeft', 'mouthDimpleRight',
    'mouthPucker', 'mouthRollLower', 'mouthRollUpper', 'mouthShrugLower', 'mouthShrugUpper',
    'mouthStretchLeft', 'mouthStretchRight', 'mouthUpperUpLeft', 'mouthUpperUpRight',
    'mouthFunnel', 'mouthPressLeft', 'mouthPressRight',
    // Cheek morphs
    'cheekPuff', 'cheekSquintLeft', 'cheekSquintRight',
    // Nose morphs
    'noseSneerLeft', 'noseSneerRight',
    // Custom morphs
    'bodyRotateX', 'bodyRotateY', 'bodyRotateZ',
    'headRotateX', 'headRotateY', 'headRotateZ', 'chestInhale',
    // Visemes with prefix
    'viseme_sil', 'viseme_PP', 'viseme_FF', 'viseme_TH', 'viseme_DD',
    'viseme_kk', 'viseme_CH', 'viseme_SS', 'viseme_nn', 'viseme_RR',
    'viseme_aa', 'viseme_E', 'viseme_I', 'viseme_O', 'viseme_U'
];

/**
 * Initial pose morph target values
 * These values are applied when setting the initial pose
 */
export const INITIAL_MORPH_VALUES = {
    // Subtle natural expression
    'mouthClose': 0.5,      // Neutral mouth closure
    'jawOpen': 0.01,        // Very slight jaw opening
    'mouthSmile': 0.02,     // Barely noticeable smile
    'eyesLookDown': 0.05,   // Slight downward gaze
    'browInnerUp': 0.05,    // Slight brow raise for attentive look
    'chestInhale': 0.3,     // Natural breathing position

    // Custom morph targets for body/head rotation
    'bodyRotateX': 0.0,     // Neutral body rotation X
    'bodyRotateY': 0.0,     // Neutral body rotation Y
    'bodyRotateZ': 0.0,     // Neutral body rotation Z
    'headRotateX': 0.0,     // Neutral head rotation X
    'headRotateY': 0.0,     // Neutral head rotation Y
    'headRotateZ': 0.0      // Neutral head rotation Z
};


/**
 * Bone structure definitions for different avatar types
 * These are used for mesh type detection and animation
 */

/**
 * Bone structure definitions for different avatar types
 * Organized by category for easier reference and mesh type detection
 */

/**
 * Core bone groups used by all avatar types
 * These are the essential bones needed for basic animation
 */
export const BONE_GROUPS = {
    // Core skeleton bones (required for all avatars)
    CORE: [
        'Hips', 'Spine', 'Spine1', 'Spine2', 'Neck', 'Head'
    ],

    // Arm bones (required for gestures)
    ARMS: [
        'LeftShoulder', 'LeftArm', 'LeftForeArm', 'LeftHand',
        'RightShoulder', 'RightArm', 'RightForeArm', 'RightHand'
    ],

    // Leg bones (required for standing poses)
    LEGS: [
        'LeftUpLeg', 'LeftLeg', 'LeftFoot', 'LeftToeBase',
        'RightUpLeg', 'RightLeg', 'RightFoot', 'RightToeBase'
    ],

    // Eye bones (optional, used for eye movement)
    EYES: [
        'LeftEye', 'RightEye'
    ]
};

/**
 * Finger bone names for detailed hand animations
 * Used by both doll and RPM avatars
 */
export const FINGER_BONE_NAMES = [
    'LeftHandThumb1', 'LeftHandThumb2', 'LeftHandThumb3',
    'LeftHandIndex1', 'LeftHandIndex2', 'LeftHandIndex3',
    'LeftHandMiddle1', 'LeftHandMiddle2', 'LeftHandMiddle3',
    'LeftHandRing1', 'LeftHandRing2', 'LeftHandRing3',
    'LeftHandPinky1', 'LeftHandPinky2', 'LeftHandPinky3',
    'RightHandThumb1', 'RightHandThumb2', 'RightHandThumb3',
    'RightHandIndex1', 'RightHandIndex2', 'RightHandIndex3',
    'RightHandMiddle1', 'RightHandMiddle2', 'RightHandMiddle3',
    'RightHandRing1', 'RightHandRing2', 'RightHandRing3',
    'RightHandPinky1', 'RightHandPinky2', 'RightHandPinky3'
];

/**
 * Doll generation bone names
 * These are the minimum bones required for a doll-type avatar
 * Used for mesh type detection in BaseAnimator
 */
export const DOLL_BONE_NAMES = [
    ...BONE_GROUPS.CORE,
    ...BONE_GROUPS.ARMS,
    ...BONE_GROUPS.LEGS
];

/**
 * ReadyPlayerMe bone names (essential subset)
 * These are the bones required for RPM avatar detection
 * Full list is not needed for detection purposes
 */
export const RPM_BONE_NAMES = [
    ...DOLL_BONE_NAMES,
    ...BONE_GROUPS.EYES,
    ...FINGER_BONE_NAMES,
    'HeadTop_End', 'LeftToe_End', 'RightToe_End'
];

/**
 * IK (Inverse Kinematics) configuration
 * Defines the parent-child relationships for IK bones
 * Used for advanced animation features
 */
export const IK_SETUP = {
    'LeftShoulder': null, 'LeftArm': 'LeftShoulder', 'LeftForeArm': 'LeftArm',
    'LeftHand': 'LeftForeArm', 'LeftHandMiddle1': 'LeftHand',
    'RightShoulder': null, 'RightArm': 'RightShoulder', 'RightForeArm': 'RightArm',
    'RightHand': 'RightForeArm', 'RightHandMiddle1': 'RightHand'
};

/**
 * Legacy animation configuration (simplified)
 */
export const ANIMATION_CONFIG = {
    creation: { defaultDelay: 0, defaultDuration: 1000, defaultEasing: 'easeInOutQuad' },
    processing: { blendDuration: 300, useQuaternionSlerp: true }
};

/**
 * Calculate distance between two 3D points
 * @param {Object} point1 - First point with x, y, z properties
 * @param {Object} point2 - Second point with x, y, z properties
 * @returns {number} Distance between the points
 */
export function distance3D(point1, point2) {
    const dx = point2.x - point1.x;
    const dy = point2.y - point1.y;
    const dz = point2.z - point1.z;
    return Math.sqrt(dx * dx + dy * dy + dz * dz);
}


// Duplicate declarations removed - all centralized animation functionality
// is now defined in SECTION 1 above
/**
 * Default animation values for the animator
 * These values are used by the animation templates and functions
 */
export const DEFAULT_ANIMATION_VALUES = {
    // Default animator options
    options: {
        debug: false,
        headMovementIntensity: 0.5,
        handGestureIntensity: 0.7,
        idleAnimationIntensity: 0.3,
        speakingAnimationIntensity: 0.6,
        blinkFrequency: 0.2,
        usePhysicsBasedAnimation: true,
        modelMovementFactor: 1.0
    },

    // Default values for idle animations
    idle: {
        avatarIdleHeadMove: 0.3,
        avatarIdleEyeContact: 0.7,
        eyeMovementIntensity: 0.6,
        blinkFrequency: 0.2,
        headMovementIntensity: 0.5
    },

    // Default values for speaking animations
    speaking: {
        avatarSpeakingHeadMove: 0.6,
        avatarSpeakingEyeContact: 0.8,
        handGestureIntensity: 0.7,
        speakingAnimationIntensity: 0.6
    },

    // Simplified animation templates
    templates: {
        // Simplified eye animation template
        eyes: {
            name: 'eyes',
            dt: [200, 2000, 3000],
            vs: {
                eyesRotateY: [[-0.6, 0.6]],
                eyesRotateX: [[-0.2, 0.6]],
                headMove: [0.3] // Will be replaced with actual value
            }
        },

        // Simplified blink animation template
        blink: {
            name: 'blink',
            dt: [50, 100, 100],
            vs: {
                eyeBlinkLeft: [1, 1, 0],
                eyeBlinkRight: [1, 1, 0]
            }
        },

        // Head movement animation templates
        headMovement: {
            // Base template for all head movements
            base: {
                name: 'headMovement',
                dt: [750, 1000, 750], // Smooth in, hold, smooth out
                transitionTime: 750,
                vs: {
                    headRotateX: [0, 0, 0],
                    headRotateY: [0, 0, 0],
                    headRotateZ: [0, 0, 0],
                    neckRotateX: [0, 0, 0],
                    neckRotateY: [0, 0, 0],
                    neckRotateZ: [0, 0, 0]
                }
            },
            // Nod animation (up/down)
            nod: {
                name: 'headMovement',
                dt: [750, 1000, 750], // Smooth in, hold, smooth out
                vs: {
                    headRotateX: [0, 0.2, 0], // Up/down movement
                    neckRotateX: [0, 0.1, 0]  // Neck follows with smaller movement
                }
            },
            // Shake animation (left/right)
            shake: {
                name: 'headMovement',
                dt: [750, 1000, 750], // Smooth in, hold, smooth out
                vs: {
                    headRotateY: [0, 0.2, 0], // Left/right movement
                    neckRotateY: [0, 0.1, 0]  // Neck follows with smaller movement
                }
            },
            // Tilt animation (side to side)
            tilt: {
                name: 'headMovement',
                dt: [750, 1000, 750], // Smooth in, hold, smooth out
                vs: {
                    headRotateZ: [0, 0.15, 0] // Side to side tilt
                }
            }
        },

        // Look at animations
        lookAt: {
            // Look at camera
            camera: {
                name: 'lookat',
                dt: [750, 1000], // Smooth transition, then hold
                vs: {
                    bodyRotateX: [0, 0.1],    // Look slightly up at camera
                    bodyRotateY: [0, 0],      // Center horizontally
                    eyesRotateX: [0, 0.1],    // Eyes follow head
                    eyesRotateY: [0, 0],      // Eyes centered
                    browInnerUp: [[0, 0.3]],  // Slight eyebrow raise for attentive look
                    eyeContact: [1],          // Making eye contact
                    headMove: [0]             // Stop random head movement
                }
            },
            // Look in random direction
            random: {
                name: 'lookat',
                dt: [750, 1000], // Smooth transition, then hold
                vs: {
                    bodyRotateX: [0, 0],      // Will be set dynamically
                    bodyRotateY: [0, 0],      // Will be set dynamically
                    eyesRotateX: [0, 0],      // Will be set dynamically
                    eyesRotateY: [0, 0],      // Will be set dynamically
                    browInnerUp: [[0, 0.2]],  // Slight eyebrow movement
                    eyeContact: [0],          // Not making direct eye contact
                    headMove: [0]             // Stop random head movement
                }
            }
        }
    }
};
// Export to window object for browser access
if (typeof window !== 'undefined') {
    window.AnimationConfig = {
        ANIMATION_REGISTRY,
        LLM_ANIMATION_SYSTEM,
        DEFAULT_ANIMATION,
        ANIMATION_TRANSITIONS,
        BONE_DEFINITIONS,
        DEFAULT_CATEGORY_DURATIONS
        // Note: AnimationTrigger is now in SkeletalAnimator.js
        // Note: EASING_FUNCTIONS is now in AnimationUtils.js
    };
}