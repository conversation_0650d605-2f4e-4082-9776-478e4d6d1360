/**
 * AnimationUtils.js
 * Shared utility functions for animation system
 * Used by both BaseAnimator and MorphAnimator
 *
 * Includes:
 * - Mesh detection and analysis
 * - Bone finding and manipulation
 * - Animation creation and interpolation
 * - Audio analysis and processing
 * - THREE.js object conversion utilities
 */

import * as THREE from 'three';
import { createLogger } from '../utils/logger.js';
import { DOLL_BONE_NAMES } from './AnimationConfig.js';
import { sigmoidFactory, random } from '../utils/mathUtils.js';

// Create a logger for this module
const logger = createLogger('AnimationUtils');

/**
 * Animation easing functions
 * These functions control how animations interpolate between values
 * Moved from AnimationConfig.js for better organization
 */
export const EASING_FUNCTIONS = {
    // Linear interpolation (no easing)
    linear: (t) => t,

    // Quadratic easing
    easeInQuad: (t) => t * t,
    easeOutQuad: (t) => t * (2 - t),
    easeInOutQuad: (t) => t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t,

    // Cubic easing
    easeInCubic: (t) => t * t * t,
    easeOutCubic: (t) => (--t) * t * t + 1,
    easeInOutCubic: (t) => t < 0.5 ? 4 * t * t * t : (t - 1) * (2 * t - 2) * (2 * t - 2) + 1,

    // Quartic easing
    easeInQuart: (t) => t * t * t * t,
    easeOutQuart: (t) => 1 - (--t) * t * t * t,
    easeInOutQuart: (t) => t < 0.5 ? 8 * t * t * t * t : 1 - 8 * (--t) * t * t * t,

    // Quintic easing
    easeInQuint: (t) => t * t * t * t * t,
    easeOutQuint: (t) => 1 + (--t) * t * t * t * t,
    easeInOutQuint: (t) => t < 0.5 ? 16 * t * t * t * t * t : 1 + 16 * (--t) * t * t * t * t,

    // Sinusoidal easing
    easeInSine: (t) => 1 - Math.cos(t * Math.PI / 2),
    easeOutSine: (t) => Math.sin(t * Math.PI / 2),
    easeInOutSine: (t) => -(Math.cos(Math.PI * t) - 1) / 2,

    // Exponential easing
    easeInExpo: (t) => t === 0 ? 0 : Math.pow(2, 10 * (t - 1)),
    easeOutExpo: (t) => t === 1 ? 1 : 1 - Math.pow(2, -10 * t),
    easeInOutExpo: (t) => {
        if (t === 0) return 0;
        if (t === 1) return 1;
        if ((t *= 2) < 1) return 0.5 * Math.pow(2, 10 * (t - 1));
        return 0.5 * (2 - Math.pow(2, -10 * (t - 1)));
    },

    // Circular easing
    easeInCirc: (t) => 1 - Math.sqrt(1 - t * t),
    easeOutCirc: (t) => Math.sqrt(1 - (--t) * t),
    easeInOutCirc: (t) => {
        if ((t *= 2) < 1) return -0.5 * (Math.sqrt(1 - t * t) - 1);
        return 0.5 * (Math.sqrt(1 - (t -= 2) * t) + 1);
    },

    // Back easing
    easeInBack: (t) => {
        const c1 = 1.70158;
        const c3 = c1 + 1;
        return c3 * t * t * t - c1 * t * t;
    },
    easeOutBack: (t) => {
        const c1 = 1.70158;
        const c3 = c1 + 1;
        return 1 + c3 * Math.pow(t - 1, 3) + c1 * Math.pow(t - 1, 2);
    },
    easeInOutBack: (t) => {
        const c1 = 1.70158;
        const c2 = c1 * 1.525;
        return t < 0.5
            ? (Math.pow(2 * t, 2) * ((c2 + 1) * 2 * t - c2)) / 2
            : (Math.pow(2 * t - 2, 2) * ((c2 + 1) * (t * 2 - 2) + c2) + 2) / 2;
    },

    // Elastic easing
    easeInElastic: (t) => {
        const c4 = (2 * Math.PI) / 3;
        return t === 0 ? 0 : t === 1 ? 1 : -Math.pow(2, 10 * t - 10) * Math.sin((t * 10 - 10.75) * c4);
    },
    easeOutElastic: (t) => {
        const c4 = (2 * Math.PI) / 3;
        return t === 0 ? 0 : t === 1 ? 1 : Math.pow(2, -10 * t) * Math.sin((t * 10 - 0.75) * c4) + 1;
    },
    easeInOutElastic: (t) => {
        const c5 = (2 * Math.PI) / 4.5;
        return t === 0 ? 0 : t === 1 ? 1 : t < 0.5
            ? -(Math.pow(2, 20 * t - 10) * Math.sin((20 * t - 11.125) * c5)) / 2
            : (Math.pow(2, -20 * t + 10) * Math.sin((20 * t - 11.125) * c5)) / 2 + 1;
    },

    // Bounce easing
    easeInBounce: (t) => 1 - EASING_FUNCTIONS.easeOutBounce(1 - t),
    easeOutBounce: (t) => {
        if (t < (1 / 2.75)) {
            return 7.5625 * t * t;
        } else if (t < (2 / 2.75)) {
            return 7.5625 * (t -= (1.5 / 2.75)) * t + 0.75;
        } else if (t < (2.5 / 2.75)) {
            return 7.5625 * (t -= (2.25 / 2.75)) * t + 0.9375;
        } else {
            return 7.5625 * (t -= (2.625 / 2.75)) * t + 0.984375;
        }
    },
    easeInOutBounce: (t) => {
        if (t < 0.5) return EASING_FUNCTIONS.easeInBounce(t * 2) * 0.5;
        return EASING_FUNCTIONS.easeOutBounce(t * 2 - 1) * 0.5 + 0.5;
    }
};

/**
 * Debug bones in the mesh
 * @param {THREE.Object3D} armature - The armature to debug
 * @param {boolean} [detailed=false] - Whether to show detailed information including quaternions
 * @param {Array} [filterBones=null] - Optional array of bone names to filter for (only show these)
 * @returns {Array} Array of bone names
 */
export function debugBones(armature, detailed = false, filterBones = null) {
    logger.debug('Debugging bone structure:');
    if (!armature) {
        logger.warn('No armature found!');
        return [];
    }

    const bones = [];
    const boneDetails = {};

    armature.traverse(node => {
        if (node.isBone) {
            // Check if we should include this bone based on filter
            if (!filterBones || filterBones.includes(node.name)) {
                bones.push(node.name);

                // Store additional details if requested
                if (detailed) {
                    boneDetails[node.name] = {
                        position: node.position ? { x: node.position.x, y: node.position.y, z: node.position.z } : null,
                        quaternion: node.quaternion ? {
                            x: node.quaternion.x,
                            y: node.quaternion.y,
                            z: node.quaternion.z,
                            w: node.quaternion.w
                        } : null,
                        parent: node.parent ? node.parent.name : null,
                        children: node.children ? node.children.map(c => c.name) : []
                    };

                    logger.debug(`- Bone: ${node.name} (parent: ${node.parent ? node.parent.name : 'none'}, children: ${node.children.length})`);
                    if (node.name.includes('Hand') || node.name.includes('Arm')) {
                        logger.debug(`  Position: (${node.position.x.toFixed(2)}, ${node.position.y.toFixed(2)}, ${node.position.z.toFixed(2)})`);
                        logger.debug(`  Quaternion: (${node.quaternion.x.toFixed(2)}, ${node.quaternion.y.toFixed(2)}, ${node.quaternion.z.toFixed(2)}, ${node.quaternion.w.toFixed(2)})`);
                    }
                }
            }
        }
    });

    logger.info(`Found ${bones.length} bones in armature`);

    // Return just the bone names or the full details based on detailed flag
    return detailed ? { bones, details: boneDetails } : bones;
}


/**
 * Find bones in a mesh
 * @param {THREE.Object3D} mesh - The mesh to search
 * @param {Array} boneList - List of bone names to find
 * @returns {Object} Object with bone names as keys and bone objects as values
 */
export function findBonesInMesh(mesh, boneList) {
    if (!mesh || !boneList || !Array.isArray(boneList)) {
        logger.warn('Invalid parameters for findBonesInMesh');
        return { bones: {}, armature: null };
    }

    const bones = {};

    // First find the armature
    let armature = null;

    // Try to find the armature/skeleton within the mesh
    if (mesh) {
        // First, check if the mesh itself is a SkinnedMesh with a skeleton
        if (mesh.isSkinnedMesh && mesh.skeleton) {
            armature = mesh;
            logger.debug('Found armature: mesh is a SkinnedMesh with skeleton');
        } else {
            // Look for a child named "Armature" (common in many 3D models)
            mesh.traverse(node => {
                if (!armature && (node.name === 'Armature' || node.name.includes('Armature'))) {
                    armature = node;
                    logger.debug(`Found armature by name: ${node.name}`);
                }
            });

            // If no armature found by name, look for any SkinnedMesh with a skeleton
            if (!armature) {
                mesh.traverse(node => {
                    if (!armature && node.isSkinnedMesh && node.skeleton) {
                        armature = node;
                        logger.debug('Found armature: SkinnedMesh with skeleton in children');
                    }
                });
            }

            // If still no armature found, look for any object with bones
            if (!armature) {
                mesh.traverse(node => {
                    if (!armature && node.isBone) {
                        // Found a bone, use its parent as the armature
                        armature = node.parent;
                        logger.debug('Found armature: parent of a bone');
                    }
                });
            }

            // Special case for models with "Root_Scene" structure (like the one in the console output)
            if (!armature && mesh.name === 'Root_Scene') {
                // Look for a child that might contain bones
                let potentialArmature = null;

                // First try to find a child with bones
                mesh.traverse(node => {
                    if (!potentialArmature && node !== mesh && node.children && node.children.length > 0) {
                        // Check if this node has any bone children
                        let hasBones = false;
                        node.traverse(child => {
                            if (child.isBone) {
                                hasBones = true;
                            }
                        });

                        if (hasBones) {
                            potentialArmature = node;
                            logger.debug(`Found potential armature in Root_Scene: ${node.name}`);
                        }
                    }
                });

                // If we found a potential armature, use it
                if (potentialArmature) {
                    armature = potentialArmature;
                }
            }

            // If still no armature found, use the mesh itself as a fallback
            if (!armature) {
                armature = mesh;
                logger.debug('No specific armature found, using mesh as armature');
            }
        }
    } else {
        logger.warn('No mesh provided to findBonesInMesh');
    }

    // Try multiple strategies to find all bones


    // Strategy 2: Search the armature if we didn't find enough bones
    if (Object.keys(bones).length < boneList.length * 0.5) {
        logger.info('Searching armature for bones');
        if (armature) {
            armature.traverse(node => {
                if (boneList.includes(node.name) && !bones[node.name]) {
                    bones[node.name] = node;
                    logger.debug(`Found bone: ${node.name}`);
                }
            });
        }

        // If still not enough bones, search the entire mesh as a fallback
        if (Object.keys(bones).length < boneList.length * 0.5) {
            logger.info('Searching entire mesh for bones');
            mesh.traverse(node => {
                if (boneList.includes(node.name) && !bones[node.name]) {
                    bones[node.name] = node;
                    logger.debug(`Found bone: ${node.name}`);
                }
            });
        }
    }

    // Strategy 3: Check SkinnedMesh skeleton
    if (Object.keys(bones).length < boneList.length * 0.5) {
        logger.info('Searching for SkinnedMesh skeleton');

        // Find all SkinnedMesh objects, starting with the armature if available
        const skinnedMeshes = [];

        // First check the armature for SkinnedMesh objects
        if (armature) {
            armature.traverse(node => {
                if (node.isSkinnedMesh && node.skeleton && node.skeleton.bones) {
                    skinnedMeshes.push(node);
                }
            });
        }

        // If no SkinnedMesh found in armature, search the entire mesh
        if (skinnedMeshes.length === 0) {
            mesh.traverse(node => {
                if (node.isSkinnedMesh && node.skeleton && node.skeleton.bones) {
                    skinnedMeshes.push(node);
                }
            });
        }

        // Process all found SkinnedMesh objects
        skinnedMeshes.forEach(skinnedMesh => {
            logger.info(`Found SkinnedMesh with ${skinnedMesh.skeleton.bones.length} bones`);

            // Map bones by name
            skinnedMesh.skeleton.bones.forEach(bone => {
                if (boneList.includes(bone.name) && !bones[bone.name]) {
                    bones[bone.name] = bone;
                    logger.debug(`Found bone in skeleton: ${bone.name}`);
                }
            });
        });
    }

    // Log bone finding results
    const foundCount = Object.keys(bones).length;
    const totalCount = boneList.length;
    logger.info(`Found ${foundCount}/${totalCount} bones (${Math.round(foundCount / totalCount * 100)}%)`);

    return { bones, armature };
}

/**
 * Get random value from configuration
 * Enhanced version that handles arrays and distributions
 * @param {number|Array} config - Value or array of [min, max] or distribution
 * @returns {number} Random value
 */
export function getRandomValue(config) {
    if (typeof config === 'number') {
        return config;
    }

    if (Array.isArray(config)) {
        if (config.length === 1) {
            return config[0];
        } else if (config.length === 2) {
            // Use the random function from mathUtils.js for consistency
            return random(config[0], config[1]);
        } else {
            // More complex distribution
            // For now, just return a random value between min and max
            return random(config[0], config[1]);
        }
    }

    return 0;
}

/**
 * Generate a random value from a Gaussian distribution
 * @param {number} mean - Mean value
 * @param {number} stddev - Standard deviation
 * @returns {number} Random value from Gaussian distribution
 */
export function randomGaussian(mean = 0, stddev = 1) {
    // Box-Muller transform
    let u = 0, v = 0;
    while (u === 0) u = Math.random(); // Converting [0,1) to (0,1)
    while (v === 0) v = Math.random();
    const z = Math.sqrt(-2.0 * Math.log(u)) * Math.cos(2.0 * Math.PI * v);
    return mean + z * stddev;
}

/**
 * Apply easing function to a value
 * @param {number} t - Value between 0 and 1
 * @param {string} easingType - Name of easing function
 * @returns {number} Eased value
 */
export function applyEasing(t, easingType = 'easeInOutQuad') {
    // Clamp t to [0, 1]
    t = Math.max(0, Math.min(1, t));

    // Get easing function
    const easingFunction = EASING_FUNCTIONS[easingType] || EASING_FUNCTIONS.easeInOutQuad;

    // Apply easing
    return easingFunction(t);
}

/**
 * Linear interpolation function moved to mathUtils.js for better organization
 * Import from there: { lerp }
 */

/**
 * Normalize audio data for animation
 * @param {Float32Array} channelData - Audio channel data
 * @returns {number} Normalized volume (0-1)
 */
export function normalizeAudioVolume(channelData) {
    if (!channelData || channelData.length === 0) {
        return 0;
    }

    // Calculate RMS (root mean square) volume
    let sum = 0;
    for (let i = 0; i < channelData.length; i++) {
        sum += channelData[i] * channelData[i];
    }
    const rms = Math.sqrt(sum / channelData.length);

    // Convert to decibels and normalize to 0-1 range
    // -60dB to 0dB mapped to 0-1
    const db = 20 * Math.log10(Math.max(rms, 1e-6));
    const normalized = Math.max(0, Math.min(1, (db + 60) / 60));

    return normalized;
}


/**
 * Create a simple audio analyzer
 * @returns {Object} Audio analyzer with context and analyzer node
 */
export function createAudioAnalyzer() {
    try {
        // Create audio context
        const audioContext = new (window.AudioContext || window.webkitAudioContext)();

        // Create analyzer node
        const analyzerNode = audioContext.createAnalyser();
        analyzerNode.fftSize = 256;
        analyzerNode.smoothingTimeConstant = 0.1;
        analyzerNode.minDecibels = -70;
        analyzerNode.maxDecibels = -10;

        // Create silent destination to avoid audio output
        const silentDestination = audioContext.createGain();
        silentDestination.gain.value = 0;
        silentDestination.connect(audioContext.destination);
        analyzerNode.connect(silentDestination);

        return {
            context: audioContext,
            analyzer: analyzerNode,
            silentDestination: silentDestination
        };
    } catch (error) {
        logger.error('Error creating audio analyzer:', error);
        return null;
    }
}

/**
 * Analyze audio data for animation
 * @param {AudioBuffer|Float32Array} audioData - Audio data to analyze
 * @param {AudioAnalyser} analyzer - Audio analyzer node
 * @returns {Object} Analysis result with volume and frequency data
 */
export function analyzeAudioData(audioData, analyzer) {
    if (!audioData || !analyzer) {
        return { volume: 0, frequencyData: new Uint8Array(16) };
    }

    try {
        // Get frequency data
        const frequencyData = new Uint8Array(analyzer.frequencyBinCount);
        analyzer.getByteFrequencyData(frequencyData);

        // Get volume from time domain data
        const timeDomainData = new Uint8Array(analyzer.fftSize);
        analyzer.getByteTimeDomainData(timeDomainData);

        // Calculate volume (0-1)
        let sum = 0;
        for (let i = 0; i < timeDomainData.length; i++) {
            const value = (timeDomainData[i] - 128) / 128;
            sum += value * value;
        }
        const volume = Math.sqrt(sum / timeDomainData.length);

        return { volume, frequencyData };
    } catch (error) {
        logger.error('Error analyzing audio data:', error);
        return { volume: 0, frequencyData: new Uint8Array(16) };
    }
}

/**
 * Detect the type of mesh based on its bone structure
 * Uses bone lists from AnimationConfig.js for more accurate detection
 * @param {Object} bones - Object containing bone references
 * @returns {Object} Detection result with mesh type and features
 */
export function detectMeshType(bones) {
    const result = {
        found: false,
        isRPM: false,
        isDoll: false,
        meshType: 'unknown',
        hasMorphTargets: false
    };

    // Check if we have any bones at all
    if (!bones || Object.keys(bones).length === 0) {
        return result;
    }

    // We found at least some bones
    result.found = true;

    // Check for critical bones that all avatars should have
    const criticalBones = ['Head', 'Neck', 'Spine'];
    const hasBasicBones = criticalBones.every(boneName => bones[boneName]);

    // Check for RPM-specific bones (using a subset of RPM_BONE_NAMES)
    // RPM avatars typically have eye bones and HeadTop_End
    const rpmSpecificBones = ['HeadTop_End', 'LeftEye', 'RightEye'];
    const hasRPMBones = rpmSpecificBones.some(boneName => bones[boneName]);

    // Check for finger bones which are typically present in RPM avatars
    const fingerBones = ['LeftHandThumb1', 'RightHandThumb1'];
    const hasFingerBones = fingerBones.some(boneName => bones[boneName]);

    // Determine avatar type based on bone structure
    result.isRPM = hasBasicBones && (hasRPMBones || hasFingerBones);
    result.isDoll = hasBasicBones && !result.isRPM;

    // Set mesh type based on detection
    if (result.isRPM) {
        result.meshType = 'ReadyPlayerMe avatar';
    } else if (result.isDoll) {
        result.meshType = 'Doll avatar';
    } else if (hasBasicBones) {
        result.meshType = 'Standard skeleton';
    } else {
        result.meshType = 'Unknown skeleton';
    }

    return result;
}

/**
 * Check if a mesh has morph targets
 * @param {THREE.Mesh|THREE.SkinnedMesh} mesh - The mesh to check
 * @returns {Object} Object with hasMorphTargets flag and morphTargetInfo
 */
export function hasMorphTargets(mesh) {
    if (!mesh) return false;

    // Result object with detailed information
    const result = {
        hasMorphTargets: false,
        morphTargetInfo: {
            count: 0,
            meshes: []
        }
    };

    // Function to check a single node for morph targets
    function checkNodeForMorphTargets(node) {
        if (node.morphTargetDictionary && Object.keys(node.morphTargetDictionary).length > 0) {
            result.hasMorphTargets = true;
            result.morphTargetInfo.count += Object.keys(node.morphTargetDictionary).length;
            result.morphTargetInfo.meshes.push({
                name: node.name || 'unnamed',
                morphTargets: Object.keys(node.morphTargetDictionary)
            });
            return true;
        }
        return false;
    }

    // Check the mesh itself
    checkNodeForMorphTargets(mesh);

    // Check all child meshes (non-recursive to avoid stack overflow)
    if (mesh.children && mesh.children.length > 0) {
        mesh.traverse(node => {
            if (node !== mesh) { // Skip the root mesh as we already checked it
                checkNodeForMorphTargets(node);
            }
        });
    }

    // For backward compatibility, return just the boolean if called from old code
    if (typeof arguments[1] === 'boolean' && arguments[1] === true) {
        return result;
    }

    return result.hasMorphTargets;
}

/**
 * Get a complete detection result for a mesh
 * Analyzes bone structure and morph targets
 * @param {THREE.Mesh|THREE.SkinnedMesh} mesh - The mesh to analyze
 * @param {Object} bones - Object containing bone references
 * @returns {Object} Complete detection result with mesh type and capabilities
 */
export function analyzeMesh(mesh, bones) {
    // If bones are not provided, try to find them
    let armature = null;
    let foundMesh = null;

    if (!bones && mesh) {
        const boneList = [...DOLL_BONE_NAMES];
        const { bones: foundBones, armature: foundArmature } = findBonesInMesh(mesh, boneList);
        bones = foundBones;
        armature = foundArmature;
    }

    // Find the SkinnedMesh with morph targets, if any
    if (mesh) {
        if (mesh.isSkinnedMesh) {
            foundMesh = mesh;
        } else {
            // Search for a skinned mesh within the object
            mesh.traverse(node => {
                if (!foundMesh && node.isSkinnedMesh) {
                    foundMesh = node;
                }
            });
        }
    }

    // Get mesh type based on bone structure
    const result = detectMeshType(bones);

    // Include references to the found objects
    result.armature = armature;
    result.foundMesh = foundMesh;

    // Check for morph targets and get detailed information
    const morphTargetResult = hasMorphTargets(foundMesh || mesh, true);

    if (typeof morphTargetResult === 'object') {
        // We got the detailed result
        result.hasMorphTargets = morphTargetResult.hasMorphTargets;
        result.morphTargetInfo = morphTargetResult.morphTargetInfo;
    } else {
        // We got just the boolean
        result.hasMorphTargets = morphTargetResult;
    }

    // Add additional capabilities based on detection
    result.capabilities = {
        canAnimate: result.found,
        canLipSync: result.hasMorphTargets,
        canBlink: result.hasMorphTargets || (result.found && bones && (bones.LeftEye || bones.RightEye)),
        canGesture: result.found && bones && bones.LeftHand && bones.RightHand,
        canMoveHead: result.found && bones && bones.Head && bones.Neck
    };

    return result;
}

// ============================================================================
// Animation Creation and Interpolation Functions
// ============================================================================

/**
 * Create an animation from a template
 * @param {Object} template Animation template
 * @param {Object} options Additional options
 * @returns {Object} Animation object
 */
export function animFactory(template, options = {}) {
    // Default options
    const defaultOptions = {
        clock: 0,
        easing: sigmoidFactory(5)
    };

    const mergedOptions = { ...defaultOptions, ...options };

    // Create animation object
    const anim = {
        template: template,
        ts: [],
        vs: [],
        easing: mergedOptions.easing
    };

    // Process template
    if (template) {
        // Process delay
        let delay = 0;
        if (template.delay !== undefined) {
            if (Array.isArray(template.delay)) {
                delay = random(template.delay[0], template.delay[1]);
            } else {
                delay = template.delay;
            }
        }

        // Process durations
        let dts = [];
        if (template.dt) {
            if (Array.isArray(template.dt)) {
                if (Array.isArray(template.dt[0])) {
                    // Array of arrays
                    dts = template.dt.map(dt => {
                        if (Array.isArray(dt)) {
                            return random(dt[0], dt[1]);
                        } else {
                            return dt;
                        }
                    });
                } else {
                    // Single array
                    dts = [random(template.dt[0], template.dt[1])];
                }
            } else {
                // Single value
                dts = [template.dt];
            }
        }

        // Create timestamps
        let t = mergedOptions.clock + delay;
        anim.ts = [t];
        for (let i = 0; i < dts.length; i++) {
            t += dts[i];
            anim.ts.push(t);
        }

        // Process values
        if (template.vs) {
            for (const [k, v] of Object.entries(template.vs)) {
                if (Array.isArray(v)) {
                    if (Array.isArray(v[0])) {
                        // Array of arrays (ranges)
                        const values = v.map(range => {
                            if (Array.isArray(range)) {
                                return random(range[0], range[1]);
                            } else {
                                return range;
                            }
                        });
                        anim.vs.push({ k, v: values });
                    } else {
                        // Single array
                        anim.vs.push({ k, v });
                    }
                } else {
                    // Single value
                    anim.vs.push({ k, v: [v] });
                }
            }
        }
    }

    return anim;
}

/**
 * Convert internal notation to THREE objects
 * @param {Object} pose Pose object
 * @returns {Object} THREE objects
 */
export function propsToThreeObjects(pose) {
    const result = {};

    if (!pose) return result;

    for (const [key, value] of Object.entries(pose)) {
        const parts = key.split('.');
        const objName = parts[0];
        const propName = parts[1];

        if (propName === 'position') {
            result[key] = new THREE.Vector3(value.x, value.y, value.z);
        } else if (propName === 'rotation') {
            const euler = new THREE.Euler(value.x, value.y, value.z);
            result[`${objName}.quaternion`] = new THREE.Quaternion().setFromEuler(euler);
        } else if (propName === 'quaternion') {
            result[key] = new THREE.Quaternion(value.x, value.y, value.z, value.w);
        } else if (propName === 'scale') {
            result[key] = new THREE.Vector3(value.x, value.y, value.z);
        }
    }

    return result;
}

/**
 * Mirror a pose (swap left/right)
 * @param {Object} pose Pose object
 * @returns {Object} Mirrored pose
 */
export function mirrorPose(pose) {
    const result = {};

    for (const [key, value] of Object.entries(pose)) {
        let newKey = key;

        // Swap Left/Right in the key
        if (key.includes('Left')) {
            newKey = key.replace('Left', 'Right');
        } else if (key.includes('Right')) {
            newKey = key.replace('Right', 'Left');
        }

        // Clone the value
        if (value instanceof THREE.Vector3) {
            result[newKey] = value.clone();
            // Mirror X coordinate
            result[newKey].x = -result[newKey].x;
        } else if (value instanceof THREE.Quaternion) {
            result[newKey] = value.clone();
            // Mirror rotation
            result[newKey].y = -result[newKey].y;
            result[newKey].z = -result[newKey].z;
        } else {
            result[newKey] = value;
        }
    }

    return result;
}

/**
 * Interpolate between two poses
 * @param {Object} poseA First pose
 * @param {Object} poseB Second pose
 * @param {number} t Interpolation factor (0-1)
 * @param {function} easing Easing function
 * @returns {Object} Interpolated pose
 */
export function interpolatePoses(poseA, poseB, t, easing = null) {
    const result = {};
    const easedT = easing ? easing(t) : t;

    // Get all keys from both poses
    const keys = new Set([...Object.keys(poseA), ...Object.keys(poseB)]);

    for (const key of keys) {
        const valueA = poseA[key];
        const valueB = poseB[key];

        // If key exists in both poses
        if (valueA !== undefined && valueB !== undefined) {
            if (valueA instanceof THREE.Vector3 && valueB instanceof THREE.Vector3) {
                result[key] = new THREE.Vector3().lerpVectors(valueA, valueB, easedT);
            } else if (valueA instanceof THREE.Quaternion && valueB instanceof THREE.Quaternion) {
                result[key] = new THREE.Quaternion().slerpQuaternions(valueA, valueB, easedT);
            }
        }
        // If key exists only in one pose
        else if (valueA !== undefined) {
            if (valueA instanceof THREE.Vector3) {
                result[key] = valueA.clone();
            } else if (valueA instanceof THREE.Quaternion) {
                result[key] = valueA.clone();
            }
        } else if (valueB !== undefined) {
            if (valueB instanceof THREE.Vector3) {
                result[key] = valueB.clone();
            } else if (valueB instanceof THREE.Quaternion) {
                result[key] = valueB.clone();
            }
        }
    }

    return result;
}

/**
 * Apply morph targets to a mesh
 * @param {THREE.Mesh} mesh Mesh to apply morph targets to
 * @param {Object} morphTargets Morph targets object
 */
export function applyMorphTargets(mesh, morphTargets) {
    if (!mesh || !mesh.morphTargetDictionary || !mesh.morphTargetInfluences) {
        return;
    }

    for (const [name, value] of Object.entries(morphTargets)) {
        const index = mesh.morphTargetDictionary[name];
        if (index !== undefined) {
            mesh.morphTargetInfluences[index] = value;
        }
    }
}

/**
 * Cyclic Coordinate Descent (CCD) Inverse Kinematic (IK) algorithm.
 * Adapted from talkinghead.mjs implementation
 * @param {Object} mesh - The mesh containing the armature
 * @param {Object} ikConfig - IK configuration object
 * @param {THREE.Vector3} [target=null] - Target coordinate, if null return to template
 * @param {Boolean} [relative=false] - If true, target is relative to root
 * @param {number} [duration=null] - If set, apply in specified milliseconds
 * @returns {Object} - The resulting bone quaternions for animation
 */
export function ikSolve(mesh, ikConfig, target = null, relative = false, duration = null) {
    if (!mesh) {
        logger.warn('Invalid mesh parameter for ikSolve: mesh is null or undefined');
        return {};
    }

    if (!ikConfig) {
        logger.warn('Invalid ikConfig parameter for ikSolve: ikConfig is null or undefined');
        return {};
    }

    // Log IK configuration for debugging
    logger.info(`IK solve called with config: root=${ikConfig.root}, effector=${ikConfig.effector}, links=${ikConfig.links ? ikConfig.links.length : 0}`);

    // Validate required IK parameters
    if (!ikConfig.root) {
        logger.warn('Missing root bone in ikConfig');
        return {};
    }

    if (!ikConfig.effector) {
        logger.warn('Missing effector bone in ikConfig');
        return {};
    }

    if (!ikConfig.links || !Array.isArray(ikConfig.links) || ikConfig.links.length === 0) {
        logger.warn('Missing or invalid links in ikConfig');
        return {};
    }
    // Create helper vectors and quaternions
    const targetVec = new THREE.Vector3();
    const effectorPos = new THREE.Vector3();
    const effectorVec = new THREE.Vector3();
    const linkPos = new THREE.Vector3();
    const invLinkQ = new THREE.Quaternion();
    const linkScale = new THREE.Vector3();
    const axis = new THREE.Vector3();
    const vector = new THREE.Vector3();

    // Find the armature in the mesh
    let armature = null;
    if (mesh.isSkinnedMesh) {
        armature = mesh;
    } else {
        mesh.traverse(node => {
            if (!armature && (node.isBone || (node.isSkinnedMesh && node.skeleton))) {
                armature = node;
            }
        });
    }

    if (!armature) {
        logger.warn('No armature found for IK solve');
        return {};
    }

    // Collect all available bones from the armature
    const availableBones = [];
    armature.traverse(node => {
        if (node.isBone) {
            availableBones.push(node.name);
        }
    });

    // Create a temporary IK mesh for calculations
    const ikMesh = new THREE.Object3D();

    // Check if this is a simplified doll mesh
    const isDoll = availableBones.length < 30; // Simplified meshes have fewer bones
    logger.info(`Mesh appears to be a ${isDoll ? 'simplified doll' : 'detailed'} mesh with ${availableBones.length} bones`);

    // Adapt IK setup based on mesh type
    let ikSetup;
    if (isDoll) {
        // Simplified setup for doll meshes (no finger bones)
        ikSetup = {
            'LeftShoulder': null, 'LeftArm': 'LeftShoulder', 'LeftForeArm': 'LeftArm',
            'LeftHand': 'LeftForeArm', // No finger bones for doll
            'RightShoulder': null, 'RightArm': 'RightShoulder', 'RightForeArm': 'RightArm',
            'RightHand': 'RightForeArm' // No finger bones for doll
        };

        // If the effector is a finger bone that doesn't exist, use the hand instead
        if (ikConfig.effector === 'LeftHandMiddle1' && !availableBones.includes('LeftHandMiddle1')) {
            ikConfig.effector = 'LeftHand';
            logger.info(`Adapting IK config for doll mesh: using LeftHand as effector instead of LeftHandMiddle1`);
        }
        if (ikConfig.effector === 'RightHandMiddle1' && !availableBones.includes('RightHandMiddle1')) {
            ikConfig.effector = 'RightHand';
            logger.info(`Adapting IK config for doll mesh: using RightHand as effector instead of RightHandMiddle1`);
        }
    } else {
        // Detailed setup for full meshes (with finger bones)
        ikSetup = {
            'LeftShoulder': null, 'LeftArm': 'LeftShoulder', 'LeftForeArm': 'LeftArm',
            'LeftHand': 'LeftForeArm', 'LeftHandMiddle1': 'LeftHand',
            'RightShoulder': null, 'RightArm': 'RightShoulder', 'RightForeArm': 'RightArm',
            'RightHand': 'RightForeArm', 'RightHandMiddle1': 'RightHand'
        };
    }

    // Create IK bones
    const ikBones = {};

    // Process each bone in the IK setup
    Object.entries(ikSetup).forEach(([boneName, parentName]) => {
        // Skip bones that don't exist in this mesh
        if (!availableBones.includes(boneName)) {
            logger.info(`Skipping bone ${boneName} as it doesn't exist in this mesh`);
            return;
        }

        const bone = new THREE.Bone();
        bone.name = boneName;

        // Find the corresponding bone in the armature
        let originalBone = null;
        armature.traverse(node => {
            if (node.name === boneName) {
                originalBone = node;
            }
        });

        if (originalBone) {
            // Copy position and rotation from original bone
            bone.position.copy(originalBone.position);
            bone.quaternion.copy(originalBone.quaternion);

            // Add to parent or to ikMesh
            if (parentName) {
                if (ikBones[parentName]) {
                    ikBones[parentName].add(bone);
                } else {
                    // If parent doesn't exist but we need this bone, add it directly to ikMesh
                    if (!availableBones.includes(parentName)) {
                        logger.info(`Parent bone ${parentName} doesn't exist, adding ${boneName} directly to ikMesh`);
                        ikMesh.add(bone);
                    } else {
                        logger.warn(`Parent bone ${parentName} not found for ${boneName}`);
                    }
                }
            } else {
                ikMesh.add(bone);
            }

            ikBones[boneName] = bone;
            logger.info(`Successfully created IK bone: ${boneName}`);
        } else {
            logger.warn(`Original bone not found in armature: ${boneName}`);
        }
    });

    // Get root and effector bones
    const root = ikBones[ikConfig.root];
    const effector = ikBones[ikConfig.effector];

    if (!root || !effector) {
        logger.warn('Root or effector bone not found for IK solve');
        return {};
    }

    // Set root position and rotation
    const originalRoot = armature.getObjectByName(ikConfig.root);
    if (originalRoot) {
        root.position.setFromMatrixPosition(originalRoot.matrixWorld);
        root.quaternion.setFromRotationMatrix(originalRoot.matrixWorld);

        if (target && relative) {
            target.add(root.position);
        }
    }

    // Set up links
    const links = ikConfig.links || [];
    links.forEach(link => {
        link.bone = ikBones[link.link];
        if (link.bone) {
            // Get original bone quaternion
            const originalBone = armature.getObjectByName(link.link);
            if (originalBone) {
                link.bone.quaternion.copy(originalBone.quaternion);
            }
        }
    });

    // Update IK mesh world matrices
    ikMesh.updateMatrixWorld(true);

    // Set iterations
    const iterations = ikConfig.iterations || 10;

    // If no target, return to template pose
    if (!target) {
        // Return result with original quaternions
        const result = {};
        links.forEach(link => {
            if (link.bone) {
                result[`${link.link}.quaternion`] = link.bone.quaternion.clone();
            }
        });
        return result;
    }

    // Get target position
    targetVec.copy(target);

    // Run IK algorithm
    for (let i = 0; i < iterations; i++) {
        // Get effector position
        effector.getWorldPosition(effectorPos);

        // Check if we're close enough to target
        if (effectorPos.distanceTo(targetVec) < 0.01) {
            break;
        }

        // Process each link
        for (let j = 0; j < links.length; j++) {
            const link = links[j];
            if (!link.bone) continue;

            // Get link position
            link.bone.getWorldPosition(linkPos);

            // Get direction to effector
            effectorVec.subVectors(effectorPos, linkPos).normalize();

            // Get direction to target
            vector.subVectors(targetVec, linkPos).normalize();

            // Get rotation axis and angle
            axis.crossVectors(effectorVec, vector).normalize();
            const angle = effectorVec.angleTo(vector);

            // Skip if angle is too small
            if (angle < 0.01) continue;

            // Apply constraints
            let constrainedAngle = angle;
            if (link.maxAngle !== undefined) {
                constrainedAngle = Math.min(angle, link.maxAngle);
            }

            // Create rotation quaternion
            const q = new THREE.Quaternion().setFromAxisAngle(axis, constrainedAngle);

            // Apply rotation to link
            link.bone.quaternion.premultiply(q);

            // Apply rotation constraints
            if (link.minx !== undefined || link.maxx !== undefined ||
                link.miny !== undefined || link.maxy !== undefined ||
                link.minz !== undefined || link.maxz !== undefined) {

                // Get Euler angles
                const euler = new THREE.Euler().setFromQuaternion(link.bone.quaternion);

                // Apply constraints
                if (link.minx !== undefined) euler.x = Math.max(euler.x, link.minx);
                if (link.maxx !== undefined) euler.x = Math.min(euler.x, link.maxx);
                if (link.miny !== undefined) euler.y = Math.max(euler.y, link.miny);
                if (link.maxy !== undefined) euler.y = Math.min(euler.y, link.maxy);
                if (link.minz !== undefined) euler.z = Math.max(euler.z, link.minz);
                if (link.maxz !== undefined) euler.z = Math.min(euler.z, link.maxz);

                // Set quaternion from constrained Euler angles
                link.bone.quaternion.setFromEuler(euler);
            }

            // Update world matrices
            ikMesh.updateMatrixWorld(true);

            // Get new effector position
            effector.getWorldPosition(effectorPos);

            // Check if we're close enough to target
            if (effectorPos.distanceTo(targetVec) < 0.01) {
                break;
            }
        }
    }

    // Return result with final quaternions
    const result = {};
    links.forEach(link => {
        if (link.bone) {
            result[`${link.link}.quaternion`] = link.bone.quaternion.clone();
        }
    });

    return result;
}

/**
 * Find all morph targets in a mesh
 * Moved from AnimationConfig.js for better organization
 * @param {Object} mesh - The mesh to search
 * @returns {Array} Array of objects with morph targets
 */
export function findMorphTargets(mesh) {
    const morphs = [];
    mesh.traverse(object => {
        if (object.morphTargetInfluences &&
            object.morphTargetInfluences.length > 0 &&
            object.morphTargetDictionary) {
            morphs.push(object);
        }
    });
    return morphs;
}

/**
 * Find a bone by name in a mesh
 * Moved from AnimationConfig.js for better organization
 * @param {Object} mesh - The mesh to search
 * @param {string} boneName - The name of the bone to find
 * @returns {Object|null} The bone object or null if not found
 */
export function findBone(mesh, boneName) {
    let bone = null;
    mesh.traverse(object => {
        if (object.isBone && object.name === boneName) {
            bone = object;
        }
    });
    return bone;
}

/**
 * Gaussian random number generator
 * Alternative name for randomGaussian for backward compatibility
 * @param {number} mean - Mean value
 * @param {number} stddev - Standard deviation
 * @returns {number} Random number with gaussian distribution
 */
export function gaussianRandom(mean = 0, stddev = 1) {
    return randomGaussian(mean, stddev);
}