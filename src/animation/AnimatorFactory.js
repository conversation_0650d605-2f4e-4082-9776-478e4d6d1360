/**
 * AnimatorFactory.js
 * Factory for creating the appropriate animator based on mesh type
 *
 * To enable debug mode for detailed animation/FBX/Three.js logs:
 *   const animator = AnimatorFactory.createAnimator(mesh, { debug: true });
 */

import { MorphAnimator } from './MorphAnimator.js';
import { BaseAnimator } from './BaseAnimator.js';
import { SkeletalAnimator } from './SkeletalAnimator.js';
import { createLogger } from '../utils/logger.js';

// Create a logger for this module
const logger = createLogger('AnimatorFactory');

export class AnimatorFactory {
    /**
     * Create an appropriate animator based on mesh capabilities
     * All animator classes implement a common interface directly
     * @param {THREE.Object3D} mesh - The mesh to animate
     * @param {Object} options - Configuration options
     * @returns {MorphAnimator|BaseAnimator|SkeletalAnimator} The appropriate animator instance
     */
    static createAnimator(mesh, options = {}) {
        // Allow debug option to be set for detailed logging
        const opts = { ...options };
        if (options.debug) {
            logger.debug('Debug mode enabled for AnimatorFactory and animators');
        }

        // Check if we explicitly want to use SkeletalAnimator
        if (options.useSkeletalAnimator) {
            logger.debug('Using SkeletalAnimator as explicitly requested');
            return new SkeletalAnimator(mesh, opts);
        }

        // Check if the mesh has morph targets (for lip sync)
        if (AnimatorFactory.hasMorphTargets(mesh)) {
            logger.debug('Mesh has morph targets, creating MorphAnimator');
            return new MorphAnimator(mesh, opts);
        } else {
            // Check if mesh has a skeleton for FBX animations
            let hasSkeleton = false;
            mesh.traverse((child) => {
                if (child.isBone || child.type === 'Bone' || child.isSkinnedMesh) {
                    hasSkeleton = true;
                }
            });

            if (hasSkeleton) {
                logger.debug('Mesh has a skeleton, creating SkeletalAnimator for FBX animations');
                return new SkeletalAnimator(mesh, opts);
            } else {
                logger.debug('Mesh does not have morph targets or skeleton, creating BaseAnimator');
                return new BaseAnimator(mesh, opts);
            }
        }
    }

    /**
     * Check if a mesh has morph targets suitable for lip sync
     * @param {THREE.Object3D} mesh - The mesh to check
     * @returns {boolean} True if the mesh has suitable morph targets
     */
    static hasMorphTargets(mesh) {
        let hasMorphs = false;

        // Check if the mesh itself has morph targets
        if (mesh.morphTargetInfluences && mesh.morphTargetInfluences.length > 0 && mesh.morphTargetDictionary) {
            hasMorphs = true;
        } else {
            // Check children recursively
            mesh.traverse((child) => {
                if (child.morphTargetInfluences &&
                    child.morphTargetInfluences.length > 0 &&
                    child.morphTargetDictionary) {

                    // Check for specific morph targets used in lip sync
                    const morphTargetNames = Object.keys(child.morphTargetDictionary);
                    const hasLipMorphs = morphTargetNames.some(name =>
                        name.includes('mouth') ||
                        name.includes('viseme') ||
                        name.includes('lip') ||
                        name.includes('jaw')
                    );

                    if (hasLipMorphs) {
                        hasMorphs = true;
                    }
                }
            });
        }

        return hasMorphs;
    }

    /**
     * @deprecated Use the animator instance directly instead
     * Get the appropriate animator interface based on the mesh type
     * This ensures both animator types can be used with the same API
     * @param {MorphAnimator|BaseAnimator|SkeletalAnimator} animator - The animator instance
     * @returns {Object} Unified animator interface
     */
    static getAnimatorInterface(animator) {
        logger.warn('getAnimatorInterface is deprecated. Use the animator instance directly instead.');
        return animator;
    }
}
