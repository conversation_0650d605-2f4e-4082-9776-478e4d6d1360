/// <reference types="vite/client" />

interface ImportMetaEnv {
    readonly VITE_ASSETS_PATH: string
    readonly VITE_MEDIAPIPE_MODEL_PATH: string
    readonly VITE_APP_ENV: string
    readonly VITE_DEBUG_MODE: string
    readonly VITE_LOOKING_GLASS_MOCK: string
    readonly VITE_APP_TITLE: string
    readonly VITE_ENABLE_STATS: string
    readonly VITE_VIDEO_FILE_PATH: string

    // Download server configuration
    readonly VITE_DOWNLOAD_SERVER_PORT: string
    readonly VITE_DOWNLOAD_SERVER_FALLBACK_START_PORT: string
    readonly VITE_DOWNLOAD_SERVER_MAX_PORT_ATTEMPTS: string
    readonly VITE_DOWNLOAD_ASSETS_DIR: string
    readonly VITE_SUPPORTED_MESH_EXTENSIONS: string

    // Server configuration
    readonly VITE_SERVER_HOST: string
    readonly VITE_SERVER_PORT: string

    // API endpoints
    readonly VITE_GRADIO_Anyto3D_ENDPOINT: string
    readonly VITE_ASR_ENDPOINT: string
    readonly VITE_OLLAMA_API_ENDPOINT: string
    readonly VITE_OLLAMA_DEFAULT_MODEL: string
    readonly VITE_TTS_ENDPOINT: string
    readonly VITE_LETTA_API_ENDPOINT: string
    readonly VITE_LLM_API_ENDPOINT: string
    readonly VITE_SGLANG_QWEN_API_ENDPOINT: string
    readonly VITE_LLM_PROVIDER: string
    readonly VITE_TRIPO_DOLL_ENDPOINT: string

    // TTS API keys
    readonly VITE_SILICONFLOW_API_KEY: string
    readonly VITE_GOOGLE_TTS_API_KEY: string
    readonly VITE_MICROSOFT_TTS_API_KEY: string
    readonly VITE_ELEVEN_TTS_API_KEY: string

    // SGLang configuration
    readonly VITE_SGLANG_MODEL: string
    readonly VITE_SGLANG_TEMPERATURE: string
    readonly VITE_SGLANG_MAX_TOKENS: string
    readonly VITE_SGLANG_TIMEOUT: string
}

interface ImportMeta {
    readonly env: ImportMetaEnv
}