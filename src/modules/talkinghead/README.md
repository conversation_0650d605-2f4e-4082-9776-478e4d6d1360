# TalkingHead Module

This module provides a 3D talking avatar with lip-sync capabilities, powered by various text-to-speech services. It supports both ReadyPlayerMe meshes with morph targets and simple rigged meshes without morph targets.

## Structure

- `src/talkinghead.mjs`: Main TalkingHead implementation for meshes with morph targets
- `src/RigAnimator.js`: Simplified animator for meshes without morph targets
- `src/animatorFactory.js`: Factory to create the appropriate animator based on mesh type
- `src/ttsServices.js`: Text-to-speech service implementations
- `src/logger.js`: Unified logging utility
- `src/audioUtils.js`: Audio processing utilities

## Features

- **Multiple TTS Services**: Support for Google TTS, Microsoft TTS, ElevenLabs, and SparkTTS
- **Streaming Audio**: Real-time audio streaming for reduced latency
- **Lip Sync**: Automatic lip synchronization with speech (for meshes with morph targets)
- **Bone-Based Animation**: Animation for meshes without morph targets using skeletal bones
- **Hand Gestures**: Speaking animations with hand movements for all mesh types
- **Head Movements**: Natural head movements synchronized with speech
- **Mesh Type Detection**: Automatic detection of mesh capabilities
- **Fallback Mechanisms**: Graceful degradation when audio services fail
- **Unified Logging**: Consistent logging across the module

## TTS Services

The module supports the following TTS services:

- **GoogleTTSService**: Basic TTS using Google's API
- **MicrosoftTTSService**: Advanced TTS with viseme data for better lip sync
- **ElevenLabsTTSService**: High-quality voice synthesis
- **ServerTTSService**: Server-side TTS processing
- **SparkTTSService**: Advanced TTS with voice cloning capabilities

## Audio Utilities

The `audioUtils.js` file provides helper functions for audio processing:

- **Audio Format Detection**: Identify MP3, WAV, and OGG formats
- **Audio Creation**: Create audio elements from blobs
- **Audio Playback**: Promise-based audio playback
- **Audio Quality Checking**: Detect silent or corrupted audio
- **Fallback Tone Generation**: Create test tones when audio fails

## Logging

The `logger.js` file provides a unified logging utility:

```javascript
import { createLogger, setLogLevel, LogLevel } from './logger.js';

// Create a logger for a specific module
const logger = createLogger('MyModule');

// Log messages at different levels
logger.debug('Debug message');
logger.info('Info message');
logger.warn('Warning message');
logger.error('Error message', someErrorObject);

// Set the global log level
setLogLevel(LogLevel.DEBUG); // Show all logs
setLogLevel(LogLevel.INFO);  // Show info, warn, and error logs
setLogLevel(LogLevel.WARN);  // Show only warn and error logs
setLogLevel(LogLevel.ERROR); // Show only error logs
setLogLevel(LogLevel.NONE);  // Disable all logs
```

## Usage

```javascript
import { TalkingHead } from './talkinghead.mjs';
import { TTSServiceFactory } from './ttsServices.js';
import { setLogLevel, LogLevel } from './logger.js';

// Set log level
setLogLevel(LogLevel.INFO);

// Create a TTS service
const ttsService = TTSServiceFactory.createService('spark', {
  endpoint: 'http://localhost:2994/sparktts-proxy',
  voiceId: '殷夫人',
  gender: 'female'
});

// Create a TalkingHead instance
const talkingHead = new TalkingHead({
  ttsService,
  element: document.getElementById('avatar-container')
});

// Speak text
talkingHead.speak('Hello, world!');
```

## Error Handling

The module includes robust error handling:

- **Silent Audio Detection**: Automatically detects and handles silent audio
- **Format Detection**: Identifies audio formats for proper processing
- **Fallback Mechanisms**: Uses test tones when audio processing fails
- **Detailed Logging**: Provides detailed logs for debugging

## Configuration

You can configure the module through environment variables or direct options:

- **TTS Service**: Select which TTS service to use
- **Voice Selection**: Choose voice by ID, gender, or language
- **Streaming Options**: Configure chunk size and processing options
- **Log Level**: Control verbosity of logs
- **Animation Intensity**: Control the intensity of animations for RigAnimator

## Animator Types

The module provides two types of animators:

### TalkingHead

The `TalkingHead` class provides full animation capabilities for meshes with morph targets:

- Lip sync with viseme mapping
- Eye animations including blinking and eye contact
- Hand gestures during speech
- Head movements synchronized with audio
- Various mood-based animations

### RigAnimator

The `RigAnimator` class provides animation capabilities for meshes without morph targets:

- Hand gestures using bone rotations
- Head movements using neck and head bones
- Body posture animations using spine bones
- Audio-driven animation intensity
- Compatible with standard skeletal rigs (e.g., Doll Gen, Mixamo)

### AnimatorFactory

The `AnimatorFactory` class determines which animator to use based on the mesh type:

```javascript
// Determine if the mesh has morph targets for lip sync
const hasMorphTargets = AnimatorFactory.hasMorphTargets(meshToUse);

if (hasMorphTargets) {
    // Create TalkingHead instance for meshes with morph targets
    animator = new TalkingHead(container, options, viewer);
} else {
    // Create RigAnimator for meshes without morph targets
    animator = new RigAnimator(meshToUse, options);
}
```
