/**
 * Example of using SherpaOnnxSTTService with SenseVoice model
 * 
 * This example demonstrates how to configure and use the SenseVoice
 * speech recognition model with the TalkingAvatar component.
 */

// Import required modules
import { TalkingAvatar } from '../../../app/viewer/talkingavatar.js';
import { STTServiceFactory } from '../src/sttServices.js';
import { config } from '../../../config/client.js';

// Example function to initialize TalkingAvatar with SenseVoice
async function initializeTalkingAvatarWithSenseVoice(viewer) {
    // Create TalkingAvatar instance
    const avatar = new TalkingAvatar(viewer);
    
    // Create SherpaOnnx STT service with SenseVoice model
    const sttService = STTServiceFactory.createService('sherpa', {
        modelType: 'senseVoice', // Use SenseVoice model
        useWasm: true, // Use WebAssembly implementation
        sampleRate: 16000,
        language: 'auto', // SenseVoice supports multiple languages
        continuous: false,
        interimResults: true,
        maxSilenceTime: 2000,
        maxSpeechTime: 10000,
        uiManager: viewer.uiManager, // Pass UI manager for loading indicators
        onResult: (text, isFinal) => {
            console.log(`Speech recognition result: "${text}" (final: ${isFinal})`);
            
            if (isFinal) {
                // Process final result
                console.log('Processing final result:', text);
            }
        },
        onError: (error) => {
            console.error('Speech recognition error:', error);
        },
        onStatusChange: (status) => {
            console.log('Speech recognition status:', status);
        }
    });
    
    // Initialize the STT service
    console.log('Initializing SenseVoice STT service...');
    const initialized = await sttService.initialize();
    if (!initialized) {
        console.error('Failed to initialize SenseVoice STT service');
        return null;
    }
    
    console.log('SenseVoice STT service initialized successfully');
    
    // Override the default STT service in TalkingAvatar
    avatar.sttServiceInstance = sttService;
    avatar.sttService = 'sherpa'; // Update the service type
    
    // Initialize the avatar
    await avatar.initialize();
    
    return avatar;
}

// Example usage
async function main() {
    // Assuming 'viewer' is your 3D viewer instance
    const viewer = window.viewer;
    
    if (!viewer) {
        console.error('Viewer not found');
        return;
    }
    
    try {
        console.log('Starting SenseVoice example...');
        console.log('Model path:', config.modelConfig.sherpaOnnx.senseVoice.modelPath);
        
        const avatar = await initializeTalkingAvatarWithSenseVoice(viewer);
        
        if (avatar) {
            console.log('TalkingAvatar initialized with SenseVoice STT service');
            
            // Example: Start listening for speech
            // avatar.startListening();
            
            // Add a button to the UI to start listening
            const button = document.createElement('button');
            button.textContent = 'Start SenseVoice Listening';
            button.style.position = 'absolute';
            button.style.bottom = '20px';
            button.style.left = '20px';
            button.style.zIndex = '1000';
            button.style.padding = '10px';
            button.style.backgroundColor = '#007bff';
            button.style.color = 'white';
            button.style.border = 'none';
            button.style.borderRadius = '5px';
            button.style.cursor = 'pointer';
            
            button.addEventListener('click', () => {
                if (avatar.isListening) {
                    avatar.stopListening();
                    button.textContent = 'Start SenseVoice Listening';
                    button.style.backgroundColor = '#007bff';
                } else {
                    avatar.startListening();
                    button.textContent = 'Stop SenseVoice Listening';
                    button.style.backgroundColor = '#dc3545';
                }
            });
            
            document.body.appendChild(button);
        }
    } catch (error) {
        console.error('Error initializing TalkingAvatar with SenseVoice:', error);
    }
}

// Run the example when the page loads
window.addEventListener('load', main);
