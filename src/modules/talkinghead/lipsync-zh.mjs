// lipsync-zh.mjs
/**
* @class Chinese (Mandarin) lip-sync processor
* <AUTHOR> Assistant
*/

// pinyin 库在主文件中导入

class LipsyncZh {
    /**
    * @constructor
    */
    constructor() {
        // Debug mode flag - set to true to enable console logging
        this.debug = false;

        // 拼音声母（初始音）到 Oculus OVR viseme 的映射
        // Oculus viseme: https://developers.meta.com/horizon/documentation/unity/audio-ovrlipsync-viseme-reference/
        // Ready Play Me avatars: https://docs.readyplayer.me/ready-player-me/api-reference/avatars/morph-targets/oculus-ovr-libsync
        this.initialToViseme = {
            'b': 'PP', 'p': 'PP', 'm': 'PP',  // 双唇音
            'f': 'FF',                        // 唇齿音
            'd': 'DD', 't': 'DD', 'n': 'DD', 'l': 'DD',  // 舌尖前音
            'g': 'kk', 'k': 'kk', 'h': 'kk',  // 舌根音
            'j': 'CH', 'q': 'CH', 'x': 'CH',  // 舌面音
            'zh': 'CH', 'ch': 'CH', 'sh': 'CH', 'r': 'RR',  // 舌尖后音（卷舌音）
            'z': 'SS', 'c': 'SS', 's': 'SS',  // 舌尖前音（平舌音）
            'y': 'I',                         // 半元音，接近元音 i
            'w': 'U'                          // 半元音，接近元音 u
        };

        // 拼音韵母（元音部分）到 Oculus OVR viseme 的映射 - 优化更多韵母映射为 'aa' 来增强嘴部动作
        this.finalToViseme = {
            'a': 'aa', 'ai': 'aa', 'an': 'aa', 'ang': 'aa', 'ao': 'aa',
            'e': 'aa', 'ei': 'aa', 'en': 'E', 'eng': 'E', 'er': 'E', // 将更多 e 音映射为 aa
            'i': 'I', 'ia': 'aa', 'ian': 'aa', 'iang': 'aa', 'iao': 'aa', 'ie': 'aa', // 将复合元音多映射为 aa
            'in': 'I', 'ing': 'I', 'iong': 'O', 'iu': 'U',
            'o': 'O', 'ong': 'O', 'ou': 'O',
            'u': 'U', 'ua': 'aa', 'uai': 'aa', 'uan': 'aa', 'uang': 'aa', 'ue': 'aa', // 增加 aa 映射
            'ui': 'U', 'un': 'U', 'uo': 'aa', // uo 改为 aa
            'v': 'U', 've': 'aa', 'van': 'aa', 'vn': 'U',  // ü系列韵母
            // 单独的韵母声调（无声母时）
            'yi': 'I', 'wu': 'U', 'yu': 'U'
        };

        // 增加嘴部动作可视性的时长系数
        this.visemeDurations = {
            'aa': 1.5,   // 大幅增加，确保嘴巴张得更大更明显
            'E': 1.2,    // 增加
            'I': 1.0,
            'O': 1.3,    // 增加
            'U': 1.1,
            'PP': 1.1,
            'SS': 1.2,
            'TH': 1.0,
            'CH': 1.1,
            'DD': 1.05,
            'FF': 1.0,
            'kk': 1.2,
            'nn': 0.9,
            'RR': 0.9,
            'sil': 0.2   // 减少静音时长
        };

        // 特殊字符的停顿时间，相对单位 (1=平均)
        this.specialDurations = {
            ' ': 0.5, ',': 1.5, '，': 1.5, '。': 2, '.': 2, '、': 1, // 减少标点停顿
            '-': 0.3, '!': 1.5, '?': 1.5, '？': 1.5, '！': 1.5
        };

        // 中文数字转换
        this.digits = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九'];
        this.units = ['', '十', '百', '千', '万', '十万', '百万', '千万', '亿', '十亿', '百亿', '千亿'];

        // 符号到中文的映射
        this.symbols = {
            '%': '百分之', '€': '欧元', '&': '和', '+': '加',
            '$': '美元', '¥': '人民币', '￥': '人民币'
        };
        this.symbolsReg = /[%€&\+\$¥￥]/g;

        // 优化默认音节持续时间 - 降低数值以避免过长的时间间隔
        this.defaultSyllableDuration = 200;

        // 最小可见持续时间 - 确保有足够时间显示每个口型
        this.minVisemeDuration = 100;

        // 强制将某些闭合音转为开口音
        this.forceOpenMapping = true;
    }

    // Log helper method for debug mode
    log(...args) {
        if (this.debug) {
            console.log(...args);
        }
    }

    /**
    * 将阿拉伯数字转换为中文数字
    * @param {string} numStr 数字字符串
    * @return {string} 转换后的中文数字
    */
    numberToChineseWords(numStr) {
        const num = parseInt(numStr);
        if (isNaN(num)) return numStr;

        if (num === 0) return this.digits[0];

        // 处理大数字（上限 9999 亿）
        if (num > 999999999999) {
            return numStr; // 太大的数字直接返回原始值
        }

        let result = '';
        let numString = num.toString();
        let length = numString.length;

        for (let i = 0; i < length; i++) {
            const digit = parseInt(numString[i]);
            const unitIndex = length - i - 1;

            if (digit === 0) {
                // 跳过连续的零
                if (result.charAt(result.length - 1) !== this.digits[0]) {
                    result += this.digits[0];
                }
            } else {
                // 对于 10-19 之间的数字，"一十"简化为"十"
                if (digit === 1 && unitIndex === 1 && i === 0) {
                    result += this.units[unitIndex];
                } else {
                    result += this.digits[digit] + this.units[unitIndex];
                }
            }
        }

        // 清理末尾的零
        result = result.replace(/零+$/, '');

        return result;
    }

    /**
    * 解析拼音字符串，提取声母和韵母
    * @param {string} pinyinStr 单个拼音音节
    * @return {Object} 包含声母和韵母的对象
    */
    parsePinyin(pinyinStr) {
        // 移除声调（唇形同步不需要声调）
        pinyinStr = pinyinStr.normalize('NFD').replace(/[\u0300-\u036f]/g, '').toLowerCase();

        // 特殊情况处理：独立韵母
        if (pinyinStr.startsWith('yi')) return { initial: '', final: 'yi' };
        if (pinyinStr.startsWith('wu')) return { initial: '', final: 'wu' };
        if (pinyinStr.startsWith('yu')) return { initial: '', final: 'yu' };

        // 处理双字母声母
        if (pinyinStr.startsWith('zh') || pinyinStr.startsWith('ch') || pinyinStr.startsWith('sh')) {
            return {
                initial: pinyinStr.substring(0, 2),
                final: pinyinStr.substring(2)
            };
        }

        // 处理单字母声母
        const singleInitials = ['b', 'p', 'm', 'f', 'd', 't', 'n', 'l', 'g', 'k', 'h', 'j', 'q', 'x', 'r', 'z', 'c', 's'];
        if (singleInitials.some(initial => pinyinStr.startsWith(initial))) {
            return {
                initial: pinyinStr[0],
                final: pinyinStr.substring(1)
            };
        }

        // 无声母，全部为韵母（如 'a', 'o', 'e'）
        return {
            initial: '',
            final: pinyinStr
        };
    }

    /**
    * 预处理文本:
    * - 转换符号为中文词语
    * - 转换数字为中文词语
    * - 过滤掉不需要发音的字符
    * @param {string} s 文本
    * @return {string} 预处理后的文本
    */
    preProcessText(s) {
        this.log('[LipsyncZh] 预处理文本:', s);
        const processed = s.replace(/[#_*\":;]/g, '')
            .replace(this.symbolsReg, (symbol) => {
                this.log(`[LipsyncZh] 转换符号 "${symbol}" 为 "${this.symbols[symbol]}"`);
                return ' ' + this.symbols[symbol] + ' ';
            })
            .replace(/(\d)\.(\d)/g, '$1点$2')  // 小数点
            .replace(/\d+/g, (num) => {
                const words = this.numberToChineseWords(num);
                this.log(`[LipsyncZh] 转换数字 "${num}" 为 "${words}"`);
                return words;
            })  // 数字转中文
            .replace(/(\D)\1{2,}/g, "$1$1")  // 最多保留两个连续相同字符
            .replace(/\s+/g, ' ')  // 压缩空格
            .trim();

        this.log('[LipsyncZh] 预处理后文本:', processed);
        return processed;
    }

    /**
    * 将中文文本转换为 Oculus LipSync Visemes 和持续时间
    * @param {string} text 中文文本
    * @return {Object} Oculus LipSync Visemes 和持续时间
    */
    wordsToVisemes(text) {
        this.log('[LipsyncZh] 处理文本转换为口型:', text);

        const o = { visemes: [], times: [], durations: [], words: [] };
        let currentTimeMs = 0;

        // 提取单词
        for (let i = 0; i < text.length; i++) {
            const char = text.charAt(i);
            if (!/\s|[，。？！、；：""''（）【】《》…—～·]/g.test(char)) {
                o.words.push(char);
            }
        }

        // 获取拼音结果
        let syllables = [];
        if (this.pinyin) {
            const pinyinResults = this.pinyin(text, { style: this.pinyin.STYLE_NORMAL, heteronym: false });
            syllables = pinyinResults.flat();
        } else {
            this.log('[LipsyncZh] 警告: pinyin 函数未定义，无法生成拼音');
            // 简单的回退方案：将每个字符作为一个音节
            for (let i = 0; i < text.length; i++) {
                if (!/\s|[，。？！、；：""''（）【】《》…—～·]/g.test(text[i])) {
                    syllables.push(text[i]);
                }
            }
        }
        this.log('[LipsyncZh] 拼音结果:', syllables);

        // 优化参数 - 降低基础时长以使动画更流畅
        const baseSyllableDurationMs = 300;
        const minVisemeDurationMs = 150;
        const transitionDurationMs = 50; // 过渡时间

        // 优化: 强制多使用 aa 发音
        const forceOpenMouthRatio = 0.6; // 60%的几率将非aa音转为aa音

        // 将长句子分组处理，每组最多8个音节
        const maxSyllablesPerGroup = 8;
        const syllableGroups = [];

        for (let i = 0; i < syllables.length; i += maxSyllablesPerGroup) {
            syllableGroups.push(syllables.slice(i, i + maxSyllablesPerGroup));
        }

        this.log('[LipsyncZh] 音节分组:', syllableGroups);

        // 为每个音节组处理口型
        syllableGroups.forEach((group, groupIndex) => {
            let syllableStartTime = currentTimeMs;

            // 在组之间添加短暂过渡
            if (groupIndex > 0) {
                o.visemes.push('sil');
                o.times.push(currentTimeMs / 1000.0);
                o.durations.push(transitionDurationMs / 1000.0);
                currentTimeMs += transitionDurationMs;
                this.log(`[LipsyncZh] 组间过渡静音，时长: ${transitionDurationMs}ms`);
            }

            group.forEach((syllable, i) => {
                const { initial, final } = this.parsePinyin(syllable);
                this.log(`[LipsyncZh] 解析音节 "${syllable}": 声母="${initial}", 韵母="${final}"`);

                let primaryViseme = 'sil'; // 默认为静音

                // 口型映射优化
                if (final && this.finalToViseme[final]) {
                    primaryViseme = this.finalToViseme[final];
                } else if (final && final.length > 0 && this.finalToViseme[final.charAt(0)]) {
                    primaryViseme = this.finalToViseme[final.charAt(0)];
                } else if (initial && this.initialToViseme[initial]) {
                    primaryViseme = this.initialToViseme[initial];
                }

                this.log(`[LipsyncZh] 初始映射音节 "${syllable}" 到口型 "${primaryViseme}"`);

                // 优化: 随机将一些音素强制映射为"aa"，使嘴巴张开更明显
                if (this.forceOpenMapping && primaryViseme !== 'aa' && Math.random() < forceOpenMouthRatio) {
                    // 但不对所有音素都这么做，保留一些变化
                    if (!(primaryViseme === 'PP' || primaryViseme === 'FF' || primaryViseme === 'TH')) {
                        this.log(`[LipsyncZh] 强制映射 ${syllable} 的 ${primaryViseme} 为 aa 以提高可见度`);
                        primaryViseme = 'aa';
                    }
                }

                // 音节持续时间 - 根据音节位置调整
                let visemeDurationMs = baseSyllableDurationMs;

                // 句首和句尾的音节延长时间
                if (i === 0 || i === group.length - 1) {
                    visemeDurationMs *= 1.2;
                }

                // 确保最小持续时间
                visemeDurationMs = Math.max(minVisemeDurationMs, visemeDurationMs);

                // 使用音素特定的持续时间系数进行调整
                if (this.visemeDurations[primaryViseme]) {
                    visemeDurationMs *= this.visemeDurations[primaryViseme];
                }

                // 添加口型
                if (primaryViseme !== 'sil') {
                    o.visemes.push(primaryViseme);
                    o.times.push(currentTimeMs / 1000.0);
                    o.durations.push(visemeDurationMs / 1000.0);
                    this.log(`[LipsyncZh] 添加口型 "${primaryViseme}" 时间 ${currentTimeMs}ms, 持续 ${visemeDurationMs}ms`);
                    currentTimeMs += visemeDurationMs;
                } else {
                    // 短静音 - 减少静音时间
                    const silDuration = 30; // 比之前更短
                    o.visemes.push('sil');
                    o.times.push(currentTimeMs / 1000.0);
                    o.durations.push(silDuration / 1000.0);
                    this.log(`[LipsyncZh] 添加静音，时长: ${silDuration}ms`);
                    currentTimeMs += silDuration;
                }
            });
        });

        // 确保最后是静音
        if (o.visemes.length === 0 || o.visemes[o.visemes.length - 1] !== 'sil') {
            const finalSilDuration = 150; // 较短的结束静音
            o.visemes.push('sil');
            o.times.push(currentTimeMs / 1000.0);
            o.durations.push(finalSilDuration / 1000.0);
            this.log(`[LipsyncZh] 添加结束静音，时长: ${finalSilDuration}ms`);
        }

        this.log('[LipsyncZh] 生成的口型数据:', JSON.stringify(o));
        this.log(`[LipsyncZh] 总口型数: ${o.visemes.length}, 总持续时间: ${currentTimeMs}ms`);
        return o;
    }

    /**
     * 设置调试模式
     * @param {boolean} enabled - 是否启用调试模式
     */
    setDebug(enabled) {
        this.debug = !!enabled;
        this.log('[LipsyncZh] 调试模式', this.debug ? '已启用' : '已禁用');
        return this;
    }
}

export { LipsyncZh };
