# SparkTTS Integration for TalkingHead

This document describes how to use the SparkTTS service with the TalkingHead module.

## Overview

SparkTTS is a high-quality text-to-speech service that supports streaming audio generation. It provides both native endpoints and OpenAI-compatible endpoints for easy integration.

## Features

- Streaming TTS for reduced latency
- Multiple voice options
- Voice cloning capabilities
- OpenAI-compatible API
- Adjustable speech parameters (pitch, speed, etc.)

## Usage

### Basic Usage

```javascript
import { TTSServiceFactory } from './ttsServices.js';

// Create a SparkTTS service
const ttsService = TTSServiceFactory.createService('spark', {
  endpoint: 'http://your-sparktts-server:8000',
  talkingHead: talkingHeadInstance // Optional reference to TalkingHead instance
});

// Initialize the service
await ttsService.initialize();

// Speak text
const result = await ttsService.speak('Hello, world!', {
  stream: true, // Enable streaming (default)
  gender: 'female', // 'male' or 'female'
  pitch: 'moderate', // 'low', 'moderate', 'high'
  speed: 'moderate' // 'slow', 'moderate', 'fast'
});
```

### Using a Specific Voice

```javascript
// Get available voices
const voices = await ttsService.getAvailableVoices();
console.log('Available voices:', voices);

// Speak with a specific voice
const result = await ttsService.speak('Hello, world!', {
  voiceId: 'voice_name_here'
});
```

### Using the OpenAI-Compatible Endpoint

```javascript
// Use the OpenAI-compatible endpoint
const result = await ttsService.speak('Hello, world!', {
  useOpenAIEndpoint: true,
  model: 'tts-1', // TTS model
  voice: 'alloy', // Voice name
  response_format: 'mp3', // Output format
  stream: true // Enable streaming
});
```

### Voice Cloning

SparkTTS supports voice cloning, but this is not directly exposed in the TTS service. You can use the underlying API endpoints directly if needed.

## Configuration Options

| Option | Description | Default |
|--------|-------------|---------|
| endpoint | SparkTTS server URL | 'http://localhost:8000' |
| gender | Voice gender | 'female' |
| pitch | Voice pitch | 'moderate' |
| speed | Speech speed | 'moderate' |
| temperature | Generation temperature | 0.9 |
| top_k | Top-k sampling parameter | 50 |
| top_p | Top-p sampling parameter | 0.95 |
| response_format | Audio format | 'mp3' |
| stream | Enable streaming | true |

## Integration with TalkingHead

When a TalkingHead instance is provided, the SparkTTS service will automatically:

1. Send audio chunks to the TalkingHead for processing during streaming
2. Use the TalkingHead's audio playback capabilities
3. Stop the TalkingHead's audio when the TTS service is stopped

## Error Handling

The service includes robust error handling for:

- Connection failures
- API errors
- Request aborts
- Streaming errors

All errors are logged to the console and returned in the result object.
