import { TalkingHeadUI } from './ui.js';

/**
 * ReadyPlayerMe.js
 * Handles Ready Player Me avatar creation and integration
 */
export class ReadyPlayerMe {
    constructor(callback) {
        this.onAvatarExported = callback || function () { };
        this.ui = null;
        this.avatarMetadata = null;
    }

    /**
     * Opens the Ready Player Me avatar creation interface
     */
    openAvatarCreator() {
        console.log('[ReadyPlayerMe] Opening avatar creator');

        // Create UI instance if not already created
        if (!this.ui) {
            this.ui = new TalkingHeadUI(null);
        }

        // Get modal elements from UI class
        const { modal, closeButton, iframe } = this.ui.createReadyPlayerMeModal();
        document.body.appendChild(modal);

        // Add message event handler
        const handleMessage = (event) => {
            const json = this._parseMessage(event);

            if (!json || json.source !== 'readyplayerme') {
                return;
            }

            console.log('[ReadyPlayerMe] Message received:', json);

            // Subscribe to events when frame is ready
            if (json.eventName === 'v1.frame.ready') {
                iframe.contentWindow.postMessage(
                    JSON.stringify({
                        target: 'readyplayerme',
                        type: 'subscribe',
                        eventName: 'v1.**'
                    }),
                    '*'
                );
            }

            // Handle avatar export
            if (json.eventName === 'v1.avatar.exported') {
                console.log(`[ReadyPlayerMe] Avatar URL: ${json.data.url}`);

                // Fetch and store avatar metadata
                this.fetchAvatarMetadata(json.data.url)
                    .then(() => {
                        // Close the modal
                        document.body.removeChild(modal);
                        window.removeEventListener('message', handleMessage);

                        // Call the callback with the avatar URL
                        this.onAvatarExported(json.data.url);
                    })
                    .catch(error => {
                        console.error('[ReadyPlayerMe] Error fetching avatar metadata:', error);
                        // Still continue with the avatar URL even if metadata fetch fails
                        document.body.removeChild(modal);
                        window.removeEventListener('message', handleMessage);
                        this.onAvatarExported(json.data.url);
                    });
            }
        };

        // Setup event listeners
        window.addEventListener('message', handleMessage);
        closeButton.addEventListener('click', () => {
            document.body.removeChild(modal);
            window.removeEventListener('message', handleMessage);
        });
    }

    /**
     * Fetch metadata for an avatar from the ReadyPlayerMe API
     * @param {string} avatarUrl - The avatar URL
     * @returns {Promise<Object>} The metadata object
     */
    async fetchAvatarMetadata(avatarUrl) {
        try {
            // Extract avatar ID from the URL
            // Format: https://models.readyplayer.me/[AVATAR_ID].glb
            const urlParts = avatarUrl.split('/');
            const filenamePart = urlParts[urlParts.length - 1];
            const avatarId = filenamePart.split('.')[0];

            if (!avatarId) {
                throw new Error('Could not extract avatar ID from URL');
            }

            console.log(`[ReadyPlayerMe] Fetching metadata for avatar ID: ${avatarId}`);

            // Construct metadata URL
            const metadataUrl = `https://models.readyplayer.me/${avatarId}.json`;

            // Fetch metadata
            const response = await fetch(metadataUrl);

            if (!response.ok) {
                throw new Error(`Metadata fetch failed with status: ${response.status}`);
            }

            const metadata = await response.json();
            console.log('[ReadyPlayerMe] Avatar metadata:', metadata);

            // Store metadata
            this.avatarMetadata = metadata;

            return metadata;
        } catch (error) {
            console.error('[ReadyPlayerMe] Error fetching avatar metadata:', error);
            this.avatarMetadata = null;
            throw error;
        }
    }

    /**
     * Get the gender from the avatar metadata
     * @returns {string} 'male' or 'female' based on outfitGender
     */
    getAvatarGender() {
        if (!this.avatarMetadata) {
            return 'male'; // Default to male if no metadata
        }

        // Map RPM's 'outfitGender' to our system's gender values
        return this.avatarMetadata.outfitGender === 'feminine' ? 'female' : 'male';
    }

    /**
     * Parse a message from the Ready Player Me iframe
     * @param {MessageEvent} event - The message event
     * @returns {Object|null} The parsed message or null if parsing fails
     * @private
     */
    _parseMessage(event) {
        try {
            return JSON.parse(event.data);
        } catch (error) {
            return null;
        }
    }

    /**
     * Generate a stable ID from an avatar URL for consistent naming
     * @param {string} url - The avatar URL
     * @returns {string} A stable ID based on the URL
     */
    generateStableId(url) {
        // For URLs or other inputs, hash them for stable file naming
        let hash = 0;
        for (let i = 0; i < url.length; i++) {
            const char = url.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash;
        }
        return Math.abs(hash).toString(16).substring(0, 8);
    }

    /**
     * Clean up resources
     */
    dispose() {
        if (this.ui) {
            this.ui = null;
        }
        this.avatarMetadata = null;
    }
}

export default ReadyPlayerMe;
