# Sherpa-ONNX Speech Recognition for TalkingHead

This document explains how to use the Sherpa-ONNX speech recognition service with the TalkingHead module.

## Overview

The `SherpaOnnxSTTService` provides a streaming speech recognition service using the [sherpa-onnx](https://github.com/k2-fsa/sherpa-onnx) library. This service allows for real-time speech recognition directly in the browser using WebAssembly, with a fallback to Node.js if needed.

## Automatic Model Loading

The service uses `SherpaOnnxLoader` to automatically download and cache models as needed:

1. When you initialize the service, it checks if the model exists locally in the public directory.
2. If the model doesn't exist, it automatically downloads and extracts it from the source URL.
3. The model is cached for future use, so it only needs to be downloaded once.

This means you don't need to manually download or install models - everything happens automatically when you use the service.

## Configuration

To use the Sherpa-ONNX STT service, simply specify the model type and other parameters:

```javascript
import { STTServiceFactory } from '../src/sttServices.js';

// Create Sherpa-ONNX STT service with SenseVoice model
const sttService = STTServiceFactory.createService('sherpa', {
    modelType: 'senseVoice', // Use SenseVoice model (default)
    useWasm: true,           // Use WebAssembly implementation (default)
    sampleRate: 16000,
    language: 'auto',        // SenseVoice supports multiple languages
    continuous: false,
    interimResults: true,
    maxSilenceTime: 2000,
    maxSpeechTime: 10000,
    onResult: (text, isFinal) => {
        console.log(`Speech recognition result: "${text}" (final: ${isFinal})`);
    },
    onError: (error) => {
        console.error('Speech recognition error:', error);
    },
    onStatusChange: (status) => {
        console.log('Speech recognition status:', status);
    }
});

// Initialize the STT service
await sttService.initialize();
```

## Using with TalkingAvatar

To use the Sherpa-ONNX STT service with TalkingAvatar:

```javascript
import { TalkingAvatar } from '../../../app/viewer/talkingavatar.js';

// Create TalkingAvatar instance
const avatar = new TalkingAvatar(viewer);

// Create and initialize Sherpa-ONNX STT service
const sttService = STTServiceFactory.createService('sherpa', {
    modelType: 'senseVoice',
    useWasm: true
});
await sttService.initialize();

// Override the default STT service in TalkingAvatar
avatar.sttServiceInstance = sttService;
avatar.sttService = 'sherpa'; // Update the service type

// Initialize the avatar
await avatar.initialize();

// Start listening for speech
avatar.startListening();
```

## Available Models

### SenseVoice (Default)

SenseVoice is a multilingual model that supports:
- Chinese (Mandarin)
- English
- Japanese
- Korean
- Cantonese (Yue)

This model provides excellent accuracy and performance across multiple languages, making it ideal for international applications.

### Adding Custom Models

To add support for additional models:

1. Update the `modelConfig` in `sherpaOnnxLoader.js` to include your new model:

```javascript
this.modelConfig = {
    senseVoice: {
        modelPath: config.modelConfig.sherpaOnnx.senseVoice.modelPath,
        fallbackPath: config.modelConfig.sherpaOnnx.senseVoice.fallbackPath
    },
    myCustomModel: {
        modelPath: 'models/tts/my-custom-model',
        fallbackPath: 'https://example.com/my-custom-model.tar.bz2'
    }
};
```

2. Update the client config in `src/config/client.ts` to include your model:

```typescript
sherpaOnnx: {
    senseVoice: {
        modelPath: 'tts/sherpa-onnx-sense-voice-zh-en-ja-ko-yue-2024-07-17',
        fallbackPath: 'https://github.com/k2-fsa/sherpa-onnx/releases/download/asr-models/sherpa-onnx-sense-voice-zh-en-ja-ko-yue-2024-07-17.tar.bz2'
    },
    myCustomModel: {
        modelPath: 'tts/my-custom-model',
        fallbackPath: 'https://example.com/my-custom-model.tar.bz2'
    }
}
```

## Advanced Configuration

### Hotwords/Contextual Biasing

You can bias the recognition towards specific words or phrases:

```javascript
const modelConfig = {
    // ... other config
    hotwords: [
        { hotword: "TalkingHead", boost: 10.0 },
        { hotword: "Hologram", boost: 8.0 }
    ]
};
```

### Endpoint Detection

Configure when to consider an utterance complete:

```javascript
const modelConfig = {
    // ... other config
    enableEndpoint: true,
    rule1MinTrailingSilence: 2.4,  // seconds
    rule2MinTrailingSilence: 1.2,  // seconds
    rule3MinUtteranceLength: 20    // frames
};
```

## Troubleshooting

If you encounter issues:

1. Check that the model files exist at the specified paths
2. Verify that the sherpa-onnx-node package is installed correctly
3. Check browser console for error messages
4. Ensure microphone permissions are granted

## Example

See the complete example in `src/modules/talkinghead/examples/sherpa-onnx-example.js`.
