/**
 * Sherpa-ONNX Server - Handles model downloads and server-side processing
 */

import express from 'express';
import { createServer } from 'http';
import { Server } from 'socket.io';
import { spawn } from 'child_process';
import path from 'path';
import fs from 'fs';
import { fileURLToPath } from 'url';
import { MODEL_CONFIGS } from '../../config/models.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

class SherpaOnnxServer {
    constructor(options = {}) {
        this.port = options.port || 3000;
        this.app = express();
        this.server = createServer(this.app);
        this.io = new Server(this.server);
        this.process = null;
        this.isRunning = false;
        this.onStatusChange = options.onStatusChange || (() => { });
        this.onError = options.onError || (() => { });
        this.setupRoutes();
        this.setupWebSocket();
    }

    setupRoutes() {
        // Serve static files
        this.app.use(express.static(path.join(__dirname, 'public')));
        this.app.use(express.json());

        // Create directory endpoint
        this.app.post('/:path(*)', async (req, res) => {
            try {
                const dirPath = path.join(__dirname, 'public', req.params.path);
                if (req.body.createDir) {
                    await fs.promises.mkdir(dirPath, { recursive: true });
                    res.json({ success: true, path: dirPath });
                } else {
                    res.status(400).json({ error: 'Invalid request' });
                }
            } catch (error) {
                console.error('[SherpaOnnxServer] Directory creation failed:', error);
                res.status(500).json({ error: error.message });
            }
        });

        // Download endpoint
        this.app.post('/download', async (req, res) => {
            try {
                const { url, targetDir, isArchive, filename } = req.body;
                if (!url || !targetDir) {
                    return res.status(400).json({ error: 'Missing required parameters' });
                }

                const targetPath = path.join(__dirname, 'public', targetDir);
                await fs.promises.mkdir(targetPath, { recursive: true });

                if (isArchive) {
                    // Download and extract archive
                    const downloadPath = path.join(targetPath, 'temp.zip');
                    await this.downloadFile(url, downloadPath);
                    await this.extractArchive(downloadPath, targetPath);
                    await fs.promises.unlink(downloadPath);
                } else if (filename) {
                    // Download single file
                    const filePath = path.join(targetPath, filename);
                    await this.downloadFile(url, filePath);
                }

                res.json({ success: true, path: targetPath });
            } catch (error) {
                console.error('[SherpaOnnxServer] Download failed:', error);
                res.status(500).json({ error: error.message });
            }
        });
    }

    setupWebSocket() {
        this.io.on('connection', (socket) => {
            console.log('[SherpaOnnxServer] Client connected');

            socket.on('disconnect', () => {
                console.log('[SherpaOnnxServer] Client disconnected');
            });

            socket.on('start', async (config) => {
                try {
                    await this.startServer(config);
                    socket.emit('status', { status: 'running' });
                } catch (error) {
                    console.error('[SherpaOnnxServer] Failed to start server:', error);
                    socket.emit('error', { error: error.message });
                }
            });

            socket.on('stop', () => {
                this.stopServer();
                socket.emit('status', { status: 'stopped' });
            });
        });
    }

    async downloadFile(url, targetPath) {
        const response = await fetch(url);
        if (!response.ok) {
            throw new Error(`Failed to download file: ${response.statusText}`);
        }

        const fileStream = fs.createWriteStream(targetPath);
        await new Promise((resolve, reject) => {
            response.body.pipe(fileStream);
            response.body.on('error', reject);
            fileStream.on('finish', resolve);
        });
    }

    async extractArchive(archivePath, targetPath) {
        const unzip = spawn('unzip', ['-o', archivePath, '-d', targetPath]);
        await new Promise((resolve, reject) => {
            unzip.on('close', (code) => {
                if (code === 0) resolve();
                else reject(new Error(`Unzip failed with code ${code}`));
            });
            unzip.on('error', reject);
        });
    }

    async startServer(config) {
        if (this.isRunning) {
            console.warn('[SherpaOnnxServer] Server is already running');
            return;
        }

        try {
            const modelConfig = MODEL_CONFIGS.sherpaOnnx?.models?.[config.modelType];
            if (!modelConfig) {
                throw new Error(`Unknown model type: ${config.modelType}`);
            }

            const modelPath = path.join(__dirname, 'public', modelConfig.modelPath);
            if (!fs.existsSync(modelPath)) {
                throw new Error(`Model not found at: ${modelPath}`);
            }

            const args = [
                '--model', modelPath,
                '--port', this.port.toString(),
                '--debug', 'false'
            ];

            this.process = spawn('sherpa-onnx', args);
            this.isRunning = true;
            this.onStatusChange('running');

            this.process.stdout.on('data', (data) => {
                console.log(`[SherpaOnnxServer] ${data}`);
            });

            this.process.stderr.on('data', (data) => {
                console.error(`[SherpaOnnxServer] ${data}`);
            });

            this.process.on('close', (code) => {
                console.log(`[SherpaOnnxServer] Process exited with code ${code}`);
                this.isRunning = false;
                this.onStatusChange('stopped');
            });

            this.process.on('error', (error) => {
                console.error('[SherpaOnnxServer] Process error:', error);
                this.onError(error);
                this.isRunning = false;
                this.onStatusChange('error');
            });
        } catch (error) {
            console.error('[SherpaOnnxServer] Failed to start server:', error);
            this.onError(error);
            this.onStatusChange('error');
            throw error;
        }
    }

    stopServer() {
        if (!this.isRunning) return;

        if (this.process) {
            this.process.kill();
            this.process = null;
        }

        this.isRunning = false;
        this.onStatusChange('stopped');
    }

    async start() {
        try {
            await new Promise((resolve, reject) => {
                this.server.listen(this.port, () => {
                    console.log(`[SherpaOnnxServer] Server listening on port ${this.port}`);
                    resolve();
                });
                this.server.on('error', reject);
            });
        } catch (error) {
            console.error('[SherpaOnnxServer] Failed to start server:', error);
            this.onError(error);
            throw error;
        }
    }

    async stop() {
        this.stopServer();
        await new Promise((resolve) => {
            this.server.close(resolve);
        });
    }
}

export default SherpaOnnxServer; 