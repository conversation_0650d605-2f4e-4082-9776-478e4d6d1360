/**
 * SherpaOnnxLoader - <PERSON>les loading and caching of Sherpa-ONNX models
 */

import { MODEL_CONFIGS } from '../../config/models.js';

class SherpaOnnxLoader {
    constructor(options = {}) {
        this.uiManager = options.uiManager;
        this.modelCache = new Map();
        this.modelStorage = window.indexedDB;
        this.initializeModelCache();
        this._isLoadingModel = false;
        this._initializationPromise = null;
        this.modelPath = null;
        this.recognizer = null;
    }

    async initializeModelCache() {
        try {
            const request = this.modelStorage.open('SherpaOnnxModelCache', 1);
            request.onerror = () => console.error('[SherpaOnnxLoader] Failed to open model cache database');
            request.onupgradeneeded = (event) => {
                const db = event.target.result;
                if (!db.objectStoreNames.contains('models')) {
                    db.createObjectStore('models', { keyPath: 'name' });
                }
            };
            request.onsuccess = () => console.log('[SherpaOnnxLoader] Model cache initialized');
        } catch (error) {
            console.error('[SherpaOnnxLoader] Model cache initialization failed:', error);
        }
    }

    async initialize(modelType = MODEL_CONFIGS.sherpaOnnx?.defaultModel || 'dolphin') {
        if (this._initializationPromise) {
            return this._initializationPromise;
        }

        this._initializationPromise = (async () => {
            try {
                const modelConfig = MODEL_CONFIGS.sherpaOnnx?.models?.[modelType];
                if (!modelConfig) {
                    throw new Error(`Unknown model type: ${modelType}`);
                }

                // Check if model files exist locally
                const modelPath = await this.checkModelFilesExist(modelConfig);

                // Create recognizer configuration
                const recognizerConfig = {
                    ...modelConfig.nodeConfig,
                    sampleRate: 16000,
                    provider: 'cpu',
                    debug: false,
                    decodingMethod: 'greedy_search'
                };

                return {
                    ...modelConfig,
                    verifiedPath: modelPath,
                    recognizerConfig
                };
            } catch (error) {
                console.error('[SherpaOnnxLoader] Initialization failed:', error);
                throw error;
            } finally {
                this._initializationPromise = null;
            }
        })();

        return this._initializationPromise;
    }

    async checkModelFilesExist(modelConfig) {
        if (this._isLoadingModel) {
            await new Promise(resolve => {
                const checkLoading = setInterval(() => {
                    if (!this._isLoadingModel) {
                        clearInterval(checkLoading);
                        resolve();
                    }
                }, 100);
            });
        }

        this._isLoadingModel = true;
        this.uiManager?.showLoadingPanel('Checking SherpaOnnx model availability...');

        try {
            const baseUrl = MODEL_CONFIGS.modelConfig?.baseUrl || '/models';
            const cleanModelPath = modelConfig.modelPath.replace(baseUrl, '').replace(/^\/+/, '');
            const localUrl = `${baseUrl}/${cleanModelPath}`;
            console.log(`[SherpaOnnxLoader] Checking local model at: ${localUrl}`);

            const isVadModel = modelConfig.modelPath.includes('vad');
            const requiredFiles = isVadModel ?
                [modelConfig.modelPath.split('/').pop()] :
                ['model.onnx', 'tokens.txt'];

            let allFilesExist = true;
            for (const file of requiredFiles) {
                try {
                    const fileUrl = isVadModel ? localUrl : `${localUrl}/${file}`;
                    console.log(`[SherpaOnnxLoader] Checking file: ${fileUrl}`);
                    const response = await fetch(fileUrl, { method: 'HEAD' });
                    if (!response.ok) {
                        console.log(`[SherpaOnnxLoader] Required file not found: ${file}`);
                        allFilesExist = false;
                        break;
                    }
                } catch (error) {
                    console.log(`[SherpaOnnxLoader] Error checking file ${file}:`, error);
                    allFilesExist = false;
                    break;
                }
            }

            if (allFilesExist) {
                console.log('[SherpaOnnxLoader] All required model files found in local storage');
                return localUrl;
            }

            let modelType = 'unknown';
            if (modelConfig.modelPath.includes('dolphin')) modelType = 'dolphin';
            else if (modelConfig.modelPath.includes('sense-voice')) modelType = 'senseVoice';
            else if (modelConfig.modelPath.includes('vad')) modelType = 'sileroVad';

            const instructions = `
⚠️ Model files for ${modelType} not found!

Please download the model files from the sherpa-onnx repository:

  ${modelConfig.fallbackPath}

Or use the server's download functionality to get them automatically.`;

            console.warn(instructions);

            if (this.uiManager) {
                this.uiManager.showErrorMessage(
                    'Model files not found',
                    instructions,
                    {
                        showCloseButton: true,
                        duration: 0
                    }
                );
            }

            throw new Error(`Model files for ${modelType} not found. Please download them first.`);
        } catch (error) {
            console.error('[SherpaOnnxLoader] Model check failed:', error);
            throw error;
        } finally {
            this.uiManager?.hideLoadingPanel();
            this._isLoadingModel = false;
        }
    }

    async ensureModelExists(modelConfig) {
        if (this._isLoadingModel) {
            await new Promise(resolve => {
                const checkLoading = setInterval(() => {
                    if (!this._isLoadingModel) {
                        clearInterval(checkLoading);
                        resolve();
                    }
                }, 100);
            });
        }

        this._isLoadingModel = true;
        this.uiManager?.showLoadingPanel('Checking SherpaOnnx model availability...');

        try {
            const baseUrl = MODEL_CONFIGS.modelConfig?.baseUrl || '/models';
            const cleanModelPath = modelConfig.modelPath.replace(baseUrl, '').replace(/^\/+/, '');
            const localUrl = `${baseUrl}/${cleanModelPath}`;
            console.log(`[SherpaOnnxLoader] Checking local model at: ${localUrl}`);

            const isVadModel = modelConfig.modelPath.includes('vad');
            const requiredFiles = isVadModel ?
                [modelConfig.modelPath.split('/').pop()] :
                ['model.onnx', 'tokens.txt'];

            let allFilesExist = true;
            for (const file of requiredFiles) {
                try {
                    const fileUrl = isVadModel ? localUrl : `${localUrl}/${file}`;
                    console.log(`[SherpaOnnxLoader] Checking file: ${fileUrl}`);
                    const response = await fetch(fileUrl, { method: 'HEAD' });
                    if (!response.ok) {
                        console.log(`[SherpaOnnxLoader] Required file not found: ${file}`);
                        allFilesExist = false;
                        break;
                    }
                } catch (error) {
                    console.log(`[SherpaOnnxLoader] Error checking file ${file}:`, error);
                    allFilesExist = false;
                    break;
                }
            }

            if (allFilesExist) {
                console.log('[SherpaOnnxLoader] All required model files found in local storage');
                return localUrl;
            }

            console.log('[SherpaOnnxLoader] Some model files missing, downloading...');
            this.uiManager?.updateLoadingProgress('Downloading SherpaOnnx model...');

            const remoteUrl = modelConfig.fallbackPath;
            console.log(`[SherpaOnnxLoader] Downloading model from: ${remoteUrl}`);

            try {
                const cleanModelPath = modelConfig.modelPath.replace(baseUrl, '').replace(/^\/+/, '');
                const saveResult = await this.saveModelToPublic(`${baseUrl}/${cleanModelPath}`, remoteUrl);
                if (!saveResult) {
                    throw new Error('Failed to save model to public directory');
                }

                console.log('[SherpaOnnxLoader] Model saved to public directory:', saveResult);

                let verificationPassed = true;
                const filesToVerify = isVadModel ?
                    [modelConfig.modelPath.split('/').pop()] :
                    ['model.onnx', 'tokens.txt'];

                for (const file of filesToVerify) {
                    try {
                        const cleanModelPath = modelConfig.modelPath.replace(baseUrl, '').replace(/^\/+/, '');
                        const fileUrl = isVadModel ? `${baseUrl}/${cleanModelPath}` : `${baseUrl}/${cleanModelPath}/${file}`;
                        console.log(`[SherpaOnnxLoader] Verifying file: ${fileUrl}`);
                        const response = await fetch(fileUrl, { method: 'HEAD' });
                        if (!response.ok) {
                            console.error(`[SherpaOnnxLoader] Required file not found after download: ${file}`);
                            verificationPassed = false;
                            break;
                        }
                    } catch (error) {
                        console.error(`[SherpaOnnxLoader] Error verifying file ${file}:`, error);
                        verificationPassed = false;
                        break;
                    }
                }

                if (!verificationPassed) {
                    throw new Error('Model files verification failed after download');
                }

                return localUrl;
            } catch (error) {
                console.error('[SherpaOnnxLoader] Failed to download and save model:', error);
                throw error;
            }
        } catch (error) {
            console.error('[SherpaOnnxLoader] Model check/download failed:', error);
            throw error;
        } finally {
            this.uiManager?.hideLoadingPanel();
            this._isLoadingModel = false;
        }
    }

    async saveModelToPublic(modelPath, remoteUrl) {
        this.uiManager?.updateLoadingProgress('Preparing download...');

        const isVadModel = modelPath.includes('vad');
        const modelDir = modelPath.split('/').slice(0, -1).join('/').replace(/^\/+|\/+$/g, '');
        console.log('[SherpaOnnxLoader] Preparing download:', {
            modelPath,
            modelDir,
            remoteUrl,
            isVadModel
        });

        try {
            const baseUrl = MODEL_CONFIGS.modelConfig?.baseUrl || '/models';
            const cleanModelDir = modelDir.replace(baseUrl, '').replace(/^\/+/, '');
            const createDirResponse = await fetch(`${baseUrl}/${cleanModelDir}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ createDir: true })
            });

            if (!createDirResponse.ok) {
                throw new Error(`Failed to create directory: ${await createDirResponse.text()}`);
            }

            this.uiManager?.updateLoadingProgress('Directory created...');
        } catch (error) {
            console.error('[SherpaOnnxLoader] Directory creation failed:', error);
            throw error;
        }

        this.uiManager?.updateLoadingProgress('Downloading and extracting model archive...');

        try {
            const baseUrl = MODEL_CONFIGS.modelConfig?.baseUrl || '/models';
            const cleanModelDir = modelDir.replace(baseUrl, '').replace(/^\/+/, '');
            const downloadUrl = `${baseUrl}/download`;
            const isVadModel = modelPath.includes('vad');

            console.log(`[SherpaOnnxLoader] Using download endpoint for ${isVadModel ? 'VAD model' : 'speech model'}:`, {
                url: downloadUrl,
                remoteUrl: remoteUrl,
                targetDir: cleanModelDir,
                isArchive: !isVadModel
            });

            const requestBody = {
                url: remoteUrl,
                targetDir: cleanModelDir,
                isArchive: !isVadModel
            };

            if (isVadModel) {
                requestBody.filename = modelPath.split('/').pop();
            }

            const downloadResponse = await fetch(downloadUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestBody)
            });

            if (!downloadResponse.ok) {
                throw new Error(`Failed to download model: ${await downloadResponse.text()}`);
            }

            const result = await downloadResponse.json();
            console.log(`[SherpaOnnxLoader] ${isVadModel ? 'VAD model' : 'Model'} downloaded successfully:`, result);

            this.uiManager?.updateLoadingProgress(`${isVadModel ? 'VAD model' : 'Model'} downloaded successfully`);

            return result;
        } catch (error) {
            console.error('[SherpaOnnxLoader] Model download and extraction error:', error);
            throw error;
        }
    }
}

export default SherpaOnnxLoader; 