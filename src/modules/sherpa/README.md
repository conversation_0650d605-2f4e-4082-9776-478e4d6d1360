# Sherpa-ONNX Module

This module provides speech-to-text functionality using Sherpa-ONNX, a fast and lightweight speech recognition toolkit.

## Prerequisites

- Node.js 16 or higher
- npm 7 or higher
- Sherpa-ONNX CLI tool installed globally

## Installation

1. Install the required dependencies:
```bash
npm install
```

2. Install Sherpa-ONNX CLI tool globally:
```bash
npm install -g sherpa-onnx
```

## Usage

The module consists of three main components:

1. `SherpaOnnxLoader`: Handles loading and caching of Sherpa-ONNX models
2. `SherpaOnnxSTTService`: Provides speech-to-text functionality
3. `SherpaOnnxServer`: Manages the server-side processing and model downloads

### Basic Usage

```javascript
import { SherpaOnnxSTTService } from './sherpa';

const sttService = new SherpaOnnxSTTService({
    uiManager: yourUIManager,
    onResult: (text) => {
        console.log('Recognized text:', text);
    },
    onError: (error) => {
        console.error('Error:', error);
    },
    onStatusChange: (status) => {
        console.log('Status:', status);
    }
});

// Initialize with default model
await sttService.initialize();

// Start listening
await sttService.startListening();

// Stop listening
sttService.stopListening();

// Cleanup
await sttService.cleanup();
```

### Server Usage

```javascript
import { SherpaOnnxServer } from './sherpa';

const server = new SherpaOnnxServer({
    port: 3000,
    onStatusChange: (status) => {
        console.log('Server status:', status);
    },
    onError: (error) => {
        console.error('Server error:', error);
    }
});

// Start the server
await server.start();

// Start the Sherpa-ONNX process
await server.startServer({
    modelType: 'dolphin'
});

// Stop the server
await server.stop();
```

## Model Configuration

The module uses the model configuration from `config/models.js`. Make sure to update the configuration with the correct model paths and settings.

## Error Handling

The module provides comprehensive error handling through the `onError` callback. Common errors include:

- Model not found
- Server startup failure
- Audio processing errors
- Network errors during model download

## Status Updates

The module provides status updates through the `onStatusChange` callback. Possible statuses include:

- `initializing`: Module is being initialized
- `ready`: Module is ready to use
- `listening`: Audio capture is active
- `processing`: Audio is being processed
- `stopped`: Module is stopped
- `error`: An error has occurred
- `cleaned`: Module has been cleaned up

## Contributing

1. Fork the repository
2. Create your feature branch
3. Commit your changes
4. Push to the branch
5. Create a new Pull Request 