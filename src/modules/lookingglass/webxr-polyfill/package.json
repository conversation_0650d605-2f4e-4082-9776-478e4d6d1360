{"name": "@lookingglass/webxr-polyfill", "version": "0.1.1", "homepage": "https://github.com/immersive-web/webxr-polyfill", "main": "build/webxr-polyfill.js", "module": "build/webxr-polyfill.module.js", "authors": ["<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>"], "description": "Use the WebXR Device API today, providing fallbacks to native WebVR 1.1 and Cardboard.", "devDependencies": {"babel-core": "^6.26.3", "babel-plugin-external-helpers": "^6.22.0", "babel-polyfill": "^6.26.0", "babel-preset-env": "^1.7.0", "babel-register": "^6.26.0", "chai": "^3.5.0", "cross-env": "^5.1.3", "eslint": "^4.16.0", "eslint-config-google": "^0.9.1", "fast-async": "^6.3.0", "jsdom": "^11.11.0", "mocha": "^5.0.0", "mock-require": "^3.0.1", "raf": "^3.4.0", "rollup": "^0.55.3", "rollup-plugin-babel": "^3.0.2", "rollup-plugin-cleanup": "^1.0.1", "rollup-plugin-commonjs": "^8.3.0", "rollup-plugin-node-resolve": "^3.0.0", "rollup-plugin-replace": "^2.0.0", "rollup-plugin-uglify": "^3.0.0", "semver": "^5.5.0", "serve": "^11.0.2", "uglify-es": "^3.3.9"}, "keywords": ["vr", "webvr", "webxr"], "license": "Apache-2.0", "scripts": {"build-script": "cross-env NODE_ENV=production rollup -c", "build-module": "cross-env NODE_ENV=production rollup -c rollup.config.module.js", "build-min": "cross-env NODE_ENV=production rollup -c rollup.config.min.js", "build": "npm run build-script && npm run build-min && npm run build-module", "test": "cross-env NODE_ENV=test mocha --require ./test/setup.js --exit --recursive", "preversion": "npm install && echo \"NO TESTS RUNNING\"", "version": "npm run build && git add build/*", "postversion": "git push && git push --tags && npm publish"}, "repository": {"type": "git", "url": "git+https://github.com/immersive-web/webxr-polyfill.git"}, "bugs": {"url": "https://github.com/immersive-web/webxr-polyfill/issues"}, "dependencies": {"cardboard-vr-display": "^1.0.19", "gl-matrix": "^2.8.1"}, "directories": {"example": "examples", "test": "test"}, "author": "Looking Glass Factory"}