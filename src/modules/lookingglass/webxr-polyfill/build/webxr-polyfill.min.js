/**
 * @license
 * webxr-polyfill
 * Copyright (c) 2017 Google
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
/**
 * @license
 * cardboard-vr-display
 * Copyright (c) 2015-2017 Google
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
/**
 * @license
 * webvr-polyfill-dpdb 
 * Copyright (c) 2017 Google
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
/**
 * @license
 * wglu-preserve-state
 * Copyright (c) 2016, Brandon Jones.
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
 * THE SOFTWARE.
 */
/**
 * @license
 * nosleep.js
 * Copyright (c) 2017, Rich Tibbett
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
 * THE SOFTWARE.
 */
!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):e.WebXRPolyfill=t()}(this,function(){"use strict";const e="undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{},t=Symbol("@@webxr-polyfill/EventTarget");class i{constructor(){this[t]={listeners:new Map}}addEventListener(e,i){if("string"!=typeof e)throw new Error("`type` must be a string");if("function"!=typeof i)throw new Error("`listener` must be a function");const r=this[t].listeners.get(e)||[];r.push(i),this[t].listeners.set(e,r)}removeEventListener(e,i){if("string"!=typeof e)throw new Error("`type` must be a string");if("function"!=typeof i)throw new Error("`listener` must be a function");const r=this[t].listeners.get(e)||[];for(let e=r.length;e>=0;e--)r[e]===i&&r.pop()}dispatchEvent(e,i){const r=this[t].listeners.get(e)||[],s=[];for(let e=0;e<r.length;e++)s[e]=r[e];for(let e of s)e(i);"function"==typeof this[`on${e}`]&&this[`on${e}`](i)}}const r=1e-6;let s="undefined"!=typeof Float32Array?Float32Array:Array;Math.PI;function n(){let e=new s(16);return s!=Float32Array&&(e[1]=0,e[2]=0,e[3]=0,e[4]=0,e[6]=0,e[7]=0,e[8]=0,e[9]=0,e[11]=0,e[12]=0,e[13]=0,e[14]=0),e[0]=1,e[5]=1,e[10]=1,e[15]=1,e}function a(e){return e[0]=1,e[1]=0,e[2]=0,e[3]=0,e[4]=0,e[5]=1,e[6]=0,e[7]=0,e[8]=0,e[9]=0,e[10]=1,e[11]=0,e[12]=0,e[13]=0,e[14]=0,e[15]=1,e}function o(e,t){let i=t[0],r=t[1],s=t[2],n=t[3],a=t[4],o=t[5],l=t[6],A=t[7],h=t[8],c=t[9],d=t[10],u=t[11],p=t[12],f=t[13],m=t[14],g=t[15],v=i*o-r*a,w=i*l-s*a,y=i*A-n*a,b=r*l-s*o,E=r*A-n*o,S=s*A-n*l,M=h*f-c*p,x=h*m-d*p,_=h*g-u*p,F=c*m-d*f,R=c*g-u*f,T=d*g-u*m,B=v*T-w*R+y*F+b*_-E*x+S*M;return B?(B=1/B,e[0]=(o*T-l*R+A*F)*B,e[1]=(s*R-r*T-n*F)*B,e[2]=(f*S-m*E+g*b)*B,e[3]=(d*E-c*S-u*b)*B,e[4]=(l*_-a*T-A*x)*B,e[5]=(i*T-s*_+n*x)*B,e[6]=(m*y-p*S-g*w)*B,e[7]=(h*S-d*y+u*w)*B,e[8]=(a*R-o*_+A*M)*B,e[9]=(r*_-i*R-n*M)*B,e[10]=(p*E-f*y+g*v)*B,e[11]=(c*y-h*E-u*v)*B,e[12]=(o*x-a*F-l*M)*B,e[13]=(i*F-r*x+s*M)*B,e[14]=(f*w-p*b-m*v)*B,e[15]=(h*b-c*w+d*v)*B,e):null}function l(e,t,i){let r=t[0],s=t[1],n=t[2],a=t[3],o=t[4],l=t[5],A=t[6],h=t[7],c=t[8],d=t[9],u=t[10],p=t[11],f=t[12],m=t[13],g=t[14],v=t[15],w=i[0],y=i[1],b=i[2],E=i[3];return e[0]=w*r+y*o+b*c+E*f,e[1]=w*s+y*l+b*d+E*m,e[2]=w*n+y*A+b*u+E*g,e[3]=w*a+y*h+b*p+E*v,w=i[4],y=i[5],b=i[6],E=i[7],e[4]=w*r+y*o+b*c+E*f,e[5]=w*s+y*l+b*d+E*m,e[6]=w*n+y*A+b*u+E*g,e[7]=w*a+y*h+b*p+E*v,w=i[8],y=i[9],b=i[10],E=i[11],e[8]=w*r+y*o+b*c+E*f,e[9]=w*s+y*l+b*d+E*m,e[10]=w*n+y*A+b*u+E*g,e[11]=w*a+y*h+b*p+E*v,w=i[12],y=i[13],b=i[14],E=i[15],e[12]=w*r+y*o+b*c+E*f,e[13]=w*s+y*l+b*d+E*m,e[14]=w*n+y*A+b*u+E*g,e[15]=w*a+y*h+b*p+E*v,e}function A(e,t,i){let r=t[0],s=t[1],n=t[2],a=t[3],o=r+r,l=s+s,A=n+n,h=r*o,c=r*l,d=r*A,u=s*l,p=s*A,f=n*A,m=a*o,g=a*l,v=a*A;return e[0]=1-(u+f),e[1]=c+v,e[2]=d-g,e[3]=0,e[4]=c-v,e[5]=1-(h+f),e[6]=p+m,e[7]=0,e[8]=d+g,e[9]=p-m,e[10]=1-(h+u),e[11]=0,e[12]=i[0],e[13]=i[1],e[14]=i[2],e[15]=1,e}function h(e,t){return e[0]=t[12],e[1]=t[13],e[2]=t[14],e}function c(e,t){let i=t[0]+t[5]+t[10],r=0;return i>0?(r=2*Math.sqrt(i+1),e[3]=.25*r,e[0]=(t[6]-t[9])/r,e[1]=(t[8]-t[2])/r,e[2]=(t[1]-t[4])/r):t[0]>t[5]&&t[0]>t[10]?(r=2*Math.sqrt(1+t[0]-t[5]-t[10]),e[3]=(t[6]-t[9])/r,e[0]=.25*r,e[1]=(t[1]+t[4])/r,e[2]=(t[8]+t[2])/r):t[5]>t[10]?(r=2*Math.sqrt(1+t[5]-t[0]-t[10]),e[3]=(t[8]-t[2])/r,e[0]=(t[1]+t[4])/r,e[1]=.25*r,e[2]=(t[6]+t[9])/r):(r=2*Math.sqrt(1+t[10]-t[0]-t[5]),e[3]=(t[1]-t[4])/r,e[0]=(t[8]+t[2])/r,e[1]=(t[6]+t[9])/r,e[2]=.25*r),e}function d(e,t,i,r,s){let n,a=1/Math.tan(t/2);return e[0]=a/i,e[1]=0,e[2]=0,e[3]=0,e[4]=0,e[5]=a,e[6]=0,e[7]=0,e[8]=0,e[9]=0,e[11]=-1,e[12]=0,e[13]=0,e[15]=0,null!=s&&s!==1/0?(n=1/(r-s),e[10]=(s+r)*n,e[14]=2*s*r*n):(e[10]=-1,e[14]=-2*r),e}function u(){let e=new s(3);return s!=Float32Array&&(e[0]=0,e[1]=0,e[2]=0),e}function p(e){var t=new s(3);return t[0]=e[0],t[1]=e[1],t[2]=e[2],t}function f(e,t,i){let r=new s(3);return r[0]=e,r[1]=t,r[2]=i,r}function m(e,t){return e[0]=t[0],e[1]=t[1],e[2]=t[2],e}function g(e,t,i){return e[0]=t[0]+i[0],e[1]=t[1]+i[1],e[2]=t[2]+i[2],e}function v(e,t,i){return e[0]=t[0]*i,e[1]=t[1]*i,e[2]=t[2]*i,e}function w(e,t){let i=t[0],r=t[1],s=t[2],n=i*i+r*r+s*s;return n>0&&(n=1/Math.sqrt(n),e[0]=t[0]*n,e[1]=t[1]*n,e[2]=t[2]*n),e}function y(e,t){return e[0]*t[0]+e[1]*t[1]+e[2]*t[2]}function b(e,t,i){let r=t[0],s=t[1],n=t[2],a=i[0],o=i[1],l=i[2];return e[0]=s*l-n*o,e[1]=n*a-r*l,e[2]=r*o-s*a,e}function E(e,t,i){let r=i[0],s=i[1],n=i[2],a=i[3],o=t[0],l=t[1],A=t[2],h=s*A-n*l,c=n*o-r*A,d=r*l-s*o,u=s*d-n*c,p=n*h-r*d,f=r*c-s*h,m=2*a;return h*=m,c*=m,d*=m,u*=2,p*=2,f*=2,e[0]=o+h+u,e[1]=l+c+p,e[2]=A+d+f,e}const S=function(e){let t=e[0],i=e[1],r=e[2];return Math.sqrt(t*t+i*i+r*r)};!function(){let e=u()}();!function(){let e=function(){let e=new s(4);return s!=Float32Array&&(e[0]=0,e[1]=0,e[2]=0,e[3]=0),e}()}();function M(){let e=new s(4);return s!=Float32Array&&(e[0]=0,e[1]=0,e[2]=0),e[3]=1,e}function x(e,t,i){let r=t[0],s=t[1],n=t[2],a=t[3],o=i[0],l=i[1],A=i[2],h=i[3];return e[0]=r*h+a*o+s*A-n*l,e[1]=s*h+a*l+n*o-r*A,e[2]=n*h+a*A+r*l-s*o,e[3]=a*h-r*o-s*l-n*A,e}function _(e,t,i,s){let n,a,o,l,A,h=t[0],c=t[1],d=t[2],u=t[3],p=i[0],f=i[1],m=i[2],g=i[3];return(a=h*p+c*f+d*m+u*g)<0&&(a=-a,p=-p,f=-f,m=-m,g=-g),1-a>r?(n=Math.acos(a),o=Math.sin(n),l=Math.sin((1-s)*n)/o,A=Math.sin(s*n)/o):(l=1-s,A=s),e[0]=l*h+A*p,e[1]=l*c+A*f,e[2]=l*d+A*m,e[3]=l*u+A*g,e}function F(e,t){let i=t[0],r=t[1],s=t[2],n=t[3],a=i*i+r*r+s*s+n*n,o=a?1/a:0;return e[0]=-i*o,e[1]=-r*o,e[2]=-s*o,e[3]=n*o,e}const R=function(e){let t=new s(4);return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t},T=function(e,t,i,r){let n=new s(4);return n[0]=e,n[1]=t,n[2]=i,n[3]=r,n},B=function(e,t){return e[0]=t[0],e[1]=t[1],e[2]=t[2],e[3]=t[3],e},P=function(e,t){let i=t[0],r=t[1],s=t[2],n=t[3],a=i*i+r*r+s*s+n*n;return a>0&&(a=1/Math.sqrt(a),e[0]=i*a,e[1]=r*a,e[2]=s*a,e[3]=n*a),e},C=(function(){let e=u(),t=f(1,0,0),i=f(0,1,0)}(),function(){let e=M(),t=M()}(),function(){let e=function(){let e=new s(9);return s!=Float32Array&&(e[1]=0,e[2]=0,e[3]=0,e[5]=0,e[6]=0,e[7]=0),e[0]=1,e[4]=1,e[8]=1,e}()}(),Symbol("@@webxr-polyfill/XRRigidTransform"));class D{constructor(){if(this[C]={matrix:null,position:null,orientation:null,inverse:null},0===arguments.length)this[C].matrix=a(new Float32Array(16));else if(1===arguments.length)arguments[0]instanceof Float32Array?this[C].matrix=arguments[0]:(this[C].position=this._getPoint(arguments[0]),this[C].orientation=DOMPointReadOnly.fromPoint({x:0,y:0,z:0,w:1}));else{if(2!==arguments.length)throw new Error("Too many arguments!");this[C].position=this._getPoint(arguments[0]),this[C].orientation=this._getPoint(arguments[1])}if(this[C].matrix){let e=u();h(e,this[C].matrix),this[C].position=DOMPointReadOnly.fromPoint({x:e[0],y:e[1],z:e[2]});let t=M();c(t,this[C].matrix),this[C].orientation=DOMPointReadOnly.fromPoint({x:t[0],y:t[1],z:t[2],w:t[3]})}else this[C].matrix=a(new Float32Array(16)),A(this[C].matrix,T(this[C].orientation.x,this[C].orientation.y,this[C].orientation.z,this[C].orientation.w),f(this[C].position.x,this[C].position.y,this[C].position.z))}_getPoint(e){return e instanceof DOMPointReadOnly?e:DOMPointReadOnly.fromPoint(e)}get matrix(){return this[C].matrix}get position(){return this[C].position}get orientation(){return this[C].orientation}get inverse(){if(null===this[C].inverse){let e=a(new Float32Array(16));o(e,this[C].matrix),this[C].inverse=new D(e),this[C].inverse[C].inverse=this}return this[C].inverse}}const I=Symbol("@@webxr-polyfill/XRSpace");class L{constructor(e=null,t=null){this[I]={specialType:e,inputSource:t,baseMatrix:null,inverseBaseMatrix:null,lastFrameId:-1}}get _specialType(){return this[I].specialType}get _inputSource(){return this[I].inputSource}_ensurePoseUpdated(e,t){t!=this[I].lastFrameId&&(this[I].lastFrameId=t,this._onPoseUpdate(e))}_onPoseUpdate(e){"viewer"==this[I].specialType&&(this._baseMatrix=e.getBasePoseMatrix())}set _baseMatrix(e){this[I].baseMatrix=e,this[I].inverseBaseMatrix=null}get _baseMatrix(){return this[I].baseMatrix||this[I].inverseBaseMatrix&&(this[I].baseMatrix=new Float32Array(16),o(this[I].baseMatrix,this[I].inverseBaseMatrix)),this[I].baseMatrix}set _inverseBaseMatrix(e){this[I].inverseBaseMatrix=e,this[I].baseMatrix=null}get _inverseBaseMatrix(){return this[I].inverseBaseMatrix||this[I].baseMatrix&&(this[I].inverseBaseMatrix=new Float32Array(16),o(this[I].inverseBaseMatrix,this[I].baseMatrix)),this[I].inverseBaseMatrix}_getSpaceRelativeTransform(e){if(!this._inverseBaseMatrix||!e._baseMatrix)return null;let t=new Float32Array(16);return l(t,this._inverseBaseMatrix,e._baseMatrix),new D(t)}}const O=1.6,N=Symbol("@@webxr-polyfill/XRReferenceSpace"),G=["viewer","local","local-floor","bounded-floor","unbounded"];class Q extends L{constructor(e,t=null){if(!G.includes(e))throw new Error(`XRReferenceSpaceType must be one of ${G}`);if(super(e),"bounded-floor"===e&&!t)throw new Error("XRReferenceSpace cannot use 'bounded-floor' type if the platform does not provide the floor level");(function(e){return"bounded-floor"===e||"local-floor"===e})(e)&&!t&&((t=a(new Float32Array(16)))[13]=O),this._inverseBaseMatrix=t||a(new Float32Array(16)),this[N]={type:e,transform:t,originOffset:a(new Float32Array(16))}}_transformBasePoseMatrix(e,t){l(e,this._inverseBaseMatrix,t)}_originOffsetMatrix(){return this[N].originOffset}_adjustForOriginOffset(e){let t=new Float32Array(16);o(t,this[N].originOffset),l(e,t,e)}_getSpaceRelativeTransform(e){let t=super._getSpaceRelativeTransform(e);return this._adjustForOriginOffset(t.matrix),new XRRigidTransform(t.matrix)}getOffsetReferenceSpace(e){let t=new Q(this[N].type,this[N].transform,this[N].bounds);return l(t[N].originOffset,this[N].originOffset,e.matrix),t}}const k=Symbol("@@webxr-polyfill/XR"),z=["inline","immersive-vr","immersive-ar"],V={inline:{requiredFeatures:["viewer"],optionalFeatures:[]},"immersive-vr":{requiredFeatures:["viewer","local"],optionalFeatures:[]},"immersive-ar":{requiredFeatures:["viewer","local"],optionalFeatures:[]}},U="Polyfill Error: Must call navigator.xr.isSessionSupported() with any XRSessionMode\nor navigator.xr.requestSession('inline') prior to requesting an immersive\nsession. This is a limitation specific to the WebXR Polyfill and does not apply\nto native implementations of the API.";let H;if("performance"in e==!1){let e=Date.now();H=(()=>Date.now()-e)}else H=(()=>performance.now());var X=H;const W=Symbol("@@webxr-polyfill/XRPose");class j{constructor(e,t){this[W]={transform:e,emulatedPosition:t}}get transform(){return this[W].transform}get emulatedPosition(){return this[W].emulatedPosition}}const q=Symbol("@@webxr-polyfill/XRViewerPose");class Y extends j{constructor(e,t,i=!1){super(e,i),this[q]={views:t}}get views(){return this[q].views}}const Z=Symbol("@@webxr-polyfill/XRViewport");class J{constructor(e){this[Z]={target:e}}get x(){return this[Z].target.x}get y(){return this[Z].target.y}get width(){return this[Z].target.width}get height(){return this[Z].target.height}}const K=["left","right","none"],$=Symbol("@@webxr-polyfill/XRView");class ee{constructor(e,t,i,r){if(!K.includes(i))throw new Error(`XREye must be one of: ${K}`);const s=Object.create(null),n=new J(s);this[$]={device:e,eye:i,viewport:n,temp:s,sessionId:r,transform:t}}get eye(){return this[$].eye}get projectionMatrix(){return this[$].device.getProjectionMatrix(this.eye)}get transform(){return this[$].transform}_getViewport(e){if(this[$].device.getViewport(this[$].sessionId,this.eye,e,this[$].temp))return this[$].viewport}}const te=Symbol("@@webxr-polyfill/XRFrame"),ie="XRFrame access outside the callback that produced it is invalid.",re="getViewerPose can only be called on XRFrame objects passed to XRSession.requestAnimationFrame callbacks.";let se=0;class ne{constructor(e,t,i){this[te]={id:++se,active:!1,animationFrame:!1,device:e,session:t,sessionId:i}}get session(){return this[te].session}getViewerPose(e){if(!this[te].animationFrame)throw new DOMException(re,"InvalidStateError");if(!this[te].active)throw new DOMException(ie,"InvalidStateError");const t=this[te].device,i=this[te].session;i[we].viewerSpace._ensurePoseUpdated(t,this[te].id),e._ensurePoseUpdated(t,this[te].id);let r=e._getSpaceRelativeTransform(i[we].viewerSpace);const s=[];for(let r of i[we].viewSpaces){r._ensurePoseUpdated(t,this[te].id);let i=e._getSpaceRelativeTransform(r),n=new ee(t,i,r.eye,this[te].sessionId);s.push(n)}return new Y(r,s,!1)}getPose(e,t){if(!this[te].active)throw new DOMException(ie,"InvalidStateError");const i=this[te].device;if("target-ray"===e._specialType||"grip"===e._specialType)return i.getInputPose(e._inputSource,t,e._specialType);{e._ensurePoseUpdated(i,this[te].id),t._ensurePoseUpdated(i,this[te].id);let r=t._getSpaceRelativeTransform(e);return r?new XRPose(r,!1):null}}}const ae=Symbol("@@webxr-polyfill/XRRenderState"),oe=Object.freeze({depthNear:.1,depthFar:1e3,inlineVerticalFieldOfView:null,baseLayer:null});class le{constructor(e={}){const t=Object.assign({},oe,e);this[ae]={config:t}}get depthNear(){return this[ae].config.depthNear}get depthFar(){return this[ae].config.depthFar}get inlineVerticalFieldOfView(){return this[ae].config.inlineVerticalFieldOfView}get baseLayer(){return this[ae].config.baseLayer}}const Ae=Symbol("@@webxr-polyfill/polyfilled-xr-compatible"),he=Symbol("@@webxr-polyfill/xr-compatible"),ce=Symbol("@@webxr-polyfill/XRWebGLLayer"),de=Object.freeze({antialias:!0,depth:!1,stencil:!1,alpha:!0,multiview:!1,ignoreDepthValues:!1,framebufferScaleFactor:1});const ue=Symbol("@@webxr-polyfill/XRInputSourceEvent");class pe extends Event{constructor(e,t){super(e,t),this[ue]={frame:t.frame,inputSource:t.inputSource},Object.setPrototypeOf(this,pe.prototype)}get frame(){return this[ue].frame}get inputSource(){return this[ue].inputSource}}const fe=Symbol("@@webxr-polyfill/XRSessionEvent");class me extends Event{constructor(e,t){super(e,t),this[fe]={session:t.session},Object.setPrototypeOf(this,me.prototype)}get session(){return this[fe].session}}const ge=Symbol("@@webxr-polyfill/XRInputSourcesChangeEvent");class ve extends Event{constructor(e,t){super(e,t),this[ge]={session:t.session,added:t.added,removed:t.removed},Object.setPrototypeOf(this,ve.prototype)}get session(){return this[ge].session}get added(){return this[ge].added}get removed(){return this[ge].removed}}const we=Symbol("@@webxr-polyfill/XRSession");class ye extends L{constructor(e){super(e)}get eye(){return this._specialType}_onPoseUpdate(e){this._inverseBaseMatrix=e.getBaseViewMatrix(this._specialType)}}class be extends i{constructor(e,t,i){super();let r="inline"!=t,s=new le({inlineVerticalFieldOfView:r?null:.5*Math.PI});this[we]={device:e,mode:t,immersive:r,ended:!1,suspended:!1,frameCallbacks:[],currentFrameCallbacks:null,frameHandle:0,deviceFrameHandle:null,id:i,activeRenderState:s,pendingRenderState:null,viewerSpace:new Q("viewer"),viewSpaces:[],currentInputSources:[]},r?this[we].viewSpaces.push(new ye("left"),new ye("right")):this[we].viewSpaces.push(new ye("none")),this[we].onDeviceFrame=(()=>{if(this[we].ended||this[we].suspended)return;if(this[we].deviceFrameHandle=null,this[we].startDeviceFrameLoop(),null!==this[we].pendingRenderState&&(this[we].activeRenderState=new le(this[we].pendingRenderState),this[we].pendingRenderState=null,this[we].activeRenderState.baseLayer&&this[we].device.onBaseLayerSet(this[we].id,this[we].activeRenderState.baseLayer)),null===this[we].activeRenderState.baseLayer)return;const t=new ne(e,this,this[we].id),i=this[we].currentFrameCallbacks=this[we].frameCallbacks;this[we].frameCallbacks=[],t[te].active=!0,t[te].animationFrame=!0,this[we].device.onFrameStart(this[we].id,this[we].activeRenderState),this._checkInputSourcesChange();const r=X();for(let e=0;e<i.length;e++)try{i[e].cancelled||"function"!=typeof i[e].callback||i[e].callback(r,t)}catch(e){console.error(e)}this[we].currentFrameCallbacks=null,t[te].active=!1,this[we].device.onFrameEnd(this[we].id)}),this[we].startDeviceFrameLoop=(()=>{null===this[we].deviceFrameHandle&&(this[we].deviceFrameHandle=this[we].device.requestAnimationFrame(this[we].onDeviceFrame))}),this[we].stopDeviceFrameLoop=(()=>{const e=this[we].deviceFrameHandle;null!==e&&(this[we].device.cancelAnimationFrame(e),this[we].deviceFrameHandle=null)}),this[we].onPresentationEnd=(t=>{if(t!==this[we].id)return this[we].suspended=!1,this[we].startDeviceFrameLoop(),void this.dispatchEvent("focus",{session:this});this[we].ended=!0,this[we].stopDeviceFrameLoop(),e.removeEventListener("@@webxr-polyfill/vr-present-end",this[we].onPresentationEnd),e.removeEventListener("@@webxr-polyfill/vr-present-start",this[we].onPresentationStart),e.removeEventListener("@@webxr-polyfill/input-select-start",this[we].onSelectStart),e.removeEventListener("@@webxr-polyfill/input-select-end",this[we].onSelectEnd),this.dispatchEvent("end",new me("end",{session:this}))}),e.addEventListener("@@webxr-polyfill/vr-present-end",this[we].onPresentationEnd),this[we].onPresentationStart=(e=>{e!==this[we].id&&(this[we].suspended=!0,this[we].stopDeviceFrameLoop(),this.dispatchEvent("blur",{session:this}))}),e.addEventListener("@@webxr-polyfill/vr-present-start",this[we].onPresentationStart),this[we].onSelectStart=(e=>{e.sessionId===this[we].id&&this[we].dispatchInputSourceEvent("selectstart",e.inputSource)}),e.addEventListener("@@webxr-polyfill/input-select-start",this[we].onSelectStart),this[we].onSelectEnd=(e=>{e.sessionId===this[we].id&&(this[we].dispatchInputSourceEvent("selectend",e.inputSource),this[we].dispatchInputSourceEvent("select",e.inputSource))}),e.addEventListener("@@webxr-polyfill/input-select-end",this[we].onSelectEnd),this[we].onSqueezeStart=(e=>{e.sessionId===this[we].id&&this[we].dispatchInputSourceEvent("squeezestart",e.inputSource)}),e.addEventListener("@@webxr-polyfill/input-squeeze-start",this[we].onSqueezeStart),this[we].onSqueezeEnd=(e=>{e.sessionId===this[we].id&&(this[we].dispatchInputSourceEvent("squeezeend",e.inputSource),this[we].dispatchInputSourceEvent("squeeze",e.inputSource))}),e.addEventListener("@@webxr-polyfill/input-squeeze-end",this[we].onSqueezeEnd),this[we].dispatchInputSourceEvent=((t,i)=>{const r=new ne(e,this,this[we].id),s=new pe(t,{frame:r,inputSource:i});r[te].active=!0,this.dispatchEvent(t,s),r[te].active=!1}),this[we].startDeviceFrameLoop(),this.onblur=void 0,this.onfocus=void 0,this.onresetpose=void 0,this.onend=void 0,this.onselect=void 0,this.onselectstart=void 0,this.onselectend=void 0}get renderState(){return this[we].activeRenderState}get environmentBlendMode(){return this[we].device.environmentBlendMode||"opaque"}async requestReferenceSpace(e){if(this[we].ended)return;if(!G.includes(e))throw new TypeError(`XRReferenceSpaceType must be one of ${G}`);if(!this[we].device.doesSessionSupportReferenceSpace(this[we].id,e))throw new DOMException(`The ${e} reference space is not supported by this session.`,"NotSupportedError");if("viewer"===e)return this[we].viewerSpace;let t=await this[we].device.requestFrameOfReferenceTransform(e);if("bounded-floor"===e){if(!t)throw new DOMException(`${e} XRReferenceSpace not supported by this device.`,"NotSupportedError");if(!this[we].device.requestStageBounds())throw new DOMException(`${e} XRReferenceSpace not supported by this device.`,"NotSupportedError");throw new DOMException(`The WebXR polyfill does not support the ${e} reference space yet.`,"NotSupportedError")}return new Q(e,t)}requestAnimationFrame(e){if(this[we].ended)return;const t=++this[we].frameHandle;return this[we].frameCallbacks.push({handle:t,callback:e,cancelled:!1}),t}cancelAnimationFrame(e){let t=this[we].frameCallbacks,i=t.findIndex(t=>t&&t.handle===e);i>-1&&(t[i].cancelled=!0,t.splice(i,1)),(t=this[we].currentFrameCallbacks)&&(i=t.findIndex(t=>t&&t.handle===e))>-1&&(t[i].cancelled=!0)}get inputSources(){return this[we].device.getInputSources()}async end(){if(!this[we].ended)return this[we].immersive&&(this[we].ended=!0,this[we].device.removeEventListener("@@webxr-polyfill/vr-present-start",this[we].onPresentationStart),this[we].device.removeEventListener("@@webxr-polyfill/vr-present-end",this[we].onPresentationEnd),this[we].device.removeEventListener("@@webxr-polyfill/input-select-start",this[we].onSelectStart),this[we].device.removeEventListener("@@webxr-polyfill/input-select-end",this[we].onSelectEnd),this.dispatchEvent("end",new me("end",{session:this}))),this[we].stopDeviceFrameLoop(),this[we].device.endSession(this[we].id)}updateRenderState(e){if(this[we].ended){throw new Error("Can't call updateRenderState on an XRSession that has already ended.")}if(e.baseLayer&&e.baseLayer._session!==this){throw new Error("Called updateRenderState with a base layer that was created by a different session.")}if(null!==e.inlineVerticalFieldOfView&&void 0!==e.inlineVerticalFieldOfView){if(this[we].immersive){throw new Error("inlineVerticalFieldOfView must not be set for an XRRenderState passed to updateRenderState for an immersive session.")}e.inlineVerticalFieldOfView=Math.min(3.13,Math.max(.01,e.inlineVerticalFieldOfView))}if(null===this[we].pendingRenderState){const e=this[we].activeRenderState;this[we].pendingRenderState={depthNear:e.depthNear,depthFar:e.depthFar,inlineVerticalFieldOfView:e.inlineVerticalFieldOfView,baseLayer:e.baseLayer}}Object.assign(this[we].pendingRenderState,e)}_checkInputSourcesChange(){const e=[],t=[],i=this.inputSources,r=this[we].currentInputSources;for(const t of i)r.includes(t)||e.push(t);for(const e of r)i.includes(e)||t.push(e);(e.length>0||t.length>0)&&this.dispatchEvent("inputsourceschange",new ve("inputsourceschange",{session:this,added:e,removed:t})),this[we].currentInputSources.length=0;for(const e of i)this[we].currentInputSources.push(e)}}const Ee=Symbol("@@webxr-polyfill/XRInputSource");class Se{constructor(e){this[Ee]={impl:e,gripSpace:new L("grip",this),targetRaySpace:new L("target-ray",this)}}get handedness(){return this[Ee].impl.handedness}get targetRayMode(){return this[Ee].impl.targetRayMode}get gripSpace(){let e=this[Ee].impl.targetRayMode;return"gaze"===e||"screen"===e?null:this[Ee].gripSpace}get targetRaySpace(){return this[Ee].targetRaySpace}get profiles(){return this[Ee].impl.profiles}get gamepad(){return this[Ee].impl.gamepad}}const Me=Symbol("@@webxr-polyfill/XRReferenceSpaceEvent");class xe extends Event{constructor(e,t){super(e,t),this[Me]={referenceSpace:t.referenceSpace,transform:t.transform||null},Object.setPrototypeOf(this,xe.prototype)}get referenceSpace(){return this[Me].referenceSpace}get transform(){return this[Me].transform}}var _e={XRSystem:class extends i{constructor(e){super(),this[k]={device:null,devicePromise:e,immersiveSession:null,inlineSessions:new Set},e.then(e=>{this[k].device=e})}async isSessionSupported(e){return this[k].device||await this[k].devicePromise,"inline"!=e?Promise.resolve(this[k].device.isSessionSupported(e)):Promise.resolve(!0)}async requestSession(e,t){if(!this[k].device){if("inline"!=e)throw new Error(U);await this[k].devicePromise}if(!z.includes(e))throw new TypeError(`The provided value '${e}' is not a valid enum value of type XRSessionMode`);const i=V[e],r=i.requiredFeatures.concat(t&&t.requiredFeatures?t.requiredFeatures:[]),s=i.optionalFeatures.concat(t&&t.optionalFeatures?t.optionalFeatures:[]),n=new Set;let a=!1;for(let e of r)this[k].device.isFeatureSupported(e)?n.add(e):(console.error(`The required feature '${e}' is not supported`),a=!0);if(a)throw new DOMException("Session does not support some required features","NotSupportedError");for(let e of s)this[k].device.isFeatureSupported(e)?n.add(e):console.log(`The optional feature '${e}' is not supported`);const o=await this[k].device.requestSession(e,n),l=new XRSession(this[k].device,e,o);"inline"==e?this[k].inlineSessions.add(l):this[k].immersiveSession=l;const A=()=>{"inline"==e?this[k].inlineSessions.delete(l):this[k].immersiveSession=null,l.removeEventListener("end",A)};return l.addEventListener("end",A),l}},XRSession:be,XRSessionEvent:me,XRFrame:ne,XRView:ee,XRViewport:J,XRViewerPose:Y,XRWebGLLayer:class{constructor(e,t,i={}){const r=Object.assign({},de,i);if(!(e instanceof be))throw new Error("session must be a XRSession");if(e.ended)throw new Error("InvalidStateError");if(t[Ae]&&!0!==t[he])throw new Error("InvalidStateError");const s=t.getParameter(t.FRAMEBUFFER_BINDING);this[ce]={context:t,config:r,framebuffer:s,session:e}}get context(){return this[ce].context}get antialias(){return this[ce].config.antialias}get ignoreDepthValues(){return!0}get framebuffer(){return this[ce].framebuffer}get framebufferWidth(){return this[ce].context.drawingBufferWidth}get framebufferHeight(){return this[ce].context.drawingBufferHeight}get _session(){return this[ce].session}getViewport(e){return e._getViewport(this)}static getNativeFramebufferScaleFactor(e){if(!e)throw new TypeError("getNativeFramebufferScaleFactor must be passed a session.");return e[we].ended?0:1}},XRSpace:L,XRReferenceSpace:Q,XRReferenceSpaceEvent:xe,XRInputSource:Se,XRInputSourceEvent:pe,XRInputSourcesChangeEvent:ve,XRRenderState:le,XRRigidTransform:D,XRPose:j};const Fe=e=>"function"!=typeof e.prototype.makeXRCompatible&&(e.prototype.makeXRCompatible=function(){return this[he]=!0,Promise.resolve()},!0),Re=e=>{const t=e.prototype.getContext;e.prototype.getContext=function(e,i){const r=t.call(this,e,i);return r&&(r[Ae]=!0,i&&"xrCompatible"in i&&(r[he]=i.xrCompatible)),r}},Te=e=>!(!e.ImageBitmapRenderingContext||!e.createImageBitmap);var Be;const Pe=e=>{e.style.display="block",e.style.position="absolute",e.style.width=e.style.height="1px",e.style.top=e.style.left="0px"};var Ce="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};var De,Ie,Le=(function(e,t){e.exports=function(){var e,t,i,r=function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")},s=function(){function e(e,t){for(var i=0;i<t.length;i++){var r=t[i];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,i,r){return i&&e(t.prototype,i),r&&e(t,r),t}}(),n=function(e,t){if(Array.isArray(e))return e;if(Symbol.iterator in Object(e))return function(e,t){var i=[],r=!0,s=!1,n=void 0;try{for(var a,o=e[Symbol.iterator]();!(r=(a=o.next()).done)&&(i.push(a.value),!t||i.length!==t);r=!0);}catch(e){s=!0,n=e}finally{try{!r&&o.return&&o.return()}finally{if(s)throw n}}return i}(e,t);throw new TypeError("Invalid attempt to destructure non-iterable instance")},a=function(e,t,i){return e+(t-e)*i},o=function(){var e=/iPad|iPhone|iPod/.test(navigator.platform);return function(){return e}}(),l=function(){var e=-1!==navigator.userAgent.indexOf("Version")&&-1!==navigator.userAgent.indexOf("Android")&&-1!==navigator.userAgent.indexOf("Chrome");return function(){return e}}(),A=function(){var e=/^((?!chrome|android).)*safari/i.test(navigator.userAgent);return function(){return e}}(),h=function(){var e=-1!==navigator.userAgent.indexOf("Firefox")&&-1!==navigator.userAgent.indexOf("Android");return function(){return e}}(),c=(t=navigator.userAgent.match(/.*Chrome\/([0-9]+)/),i=t?parseInt(t[1],10):null,function(){return i}),d=function(){var e=!1;return e=o()&&A()&&-1!==navigator.userAgent.indexOf("13_4"),function(){return e}}(),u=function(){var e=!1;if(65===c()){var t=navigator.userAgent.match(/.*Chrome\/([0-9\.]*)/);if(t){var i=t[1].split("."),r=n(i,4),s=(r[0],r[1],r[2]),a=r[3];e=3325===parseInt(s,10)&&parseInt(a,10)<148}}return function(){return e}}(),p=function(){var e=-1!==navigator.userAgent.indexOf("R7 Build");return function(){return e}}(),f=function(){var e=90==window.orientation||-90==window.orientation;return p()?!e:e},m=function(){return Math.max(window.screen.width,window.screen.height)*window.devicePixelRatio},g=function(){return Math.min(window.screen.width,window.screen.height)*window.devicePixelRatio},v=function(){if(document.exitFullscreen)document.exitFullscreen();else if(document.webkitExitFullscreen)document.webkitExitFullscreen();else if(document.mozCancelFullScreen)document.mozCancelFullScreen();else{if(!document.msExitFullscreen)return!1;document.msExitFullscreen()}return!0},w=function(e,t,i,r){var s=e.createShader(e.VERTEX_SHADER);e.shaderSource(s,t),e.compileShader(s);var n=e.createShader(e.FRAGMENT_SHADER);e.shaderSource(n,i),e.compileShader(n);var a=e.createProgram();for(var o in e.attachShader(a,s),e.attachShader(a,n),r)e.bindAttribLocation(a,r[o],o);return e.linkProgram(a),e.deleteShader(s),e.deleteShader(n),a},y=function(e,t){for(var i={},r=e.getProgramParameter(t,e.ACTIVE_UNIFORMS),s="",n=0;n<r;n++){var a=e.getActiveUniform(t,n);s=a.name.replace("[0]",""),i[s]=e.getUniformLocation(t,s)}return i},b=function(){var e,t=!1;return e=navigator.userAgent||navigator.vendor||window.opera,(/(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows ce|xda|xiino/i.test(e)||/1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s\-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|\-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw\-(n|u)|c55\/|capi|ccwa|cdm\-|cell|chtm|cldc|cmd\-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc\-s|devi|dica|dmob|do(c|p)o|ds(12|\-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(\-|_)|g1 u|g560|gene|gf\-5|g\-mo|go(\.w|od)|gr(ad|un)|haie|hcit|hd\-(m|p|t)|hei\-|hi(pt|ta)|hp( i|ip)|hs\-c|ht(c(\-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i\-(20|go|ma)|i230|iac( |\-|\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\/)|klon|kpt |kwc\-|kyo(c|k)|le(no|xi)|lg( g|\/(k|l|u)|50|54|\-[a-w])|libw|lynx|m1\-w|m3ga|m50\/|ma(te|ui|xo)|mc(01|21|ca)|m\-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(\-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)\-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|\-([1-8]|c))|phil|pire|pl(ay|uc)|pn\-2|po(ck|rt|se)|prox|psio|pt\-g|qa\-a|qc(07|12|21|32|60|\-[2-7]|i\-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h\-|oo|p\-)|sdk\/|se(c(\-|0|1)|47|mc|nd|ri)|sgh\-|shar|sie(\-|m)|sk\-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h\-|v\-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl\-|tdg\-|tel(i|m)|tim\-|t\-mo|to(pl|sh)|ts(70|m\-|m3|m5)|tx\-9|up(\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|\-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(\-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas\-|your|zeto|zte\-/i.test(e.substr(0,4)))&&(t=!0),t},E=function(e,t){for(var i in t)t.hasOwnProperty(i)&&(e[i]=t[i]);return e},S=function(e){if(o()){var t=e.style.width,i=e.style.height;e.style.width=parseInt(t)+1+"px",e.style.height=parseInt(i)+"px",setTimeout(function(){e.style.width=t,e.style.height=i},100)}window.canvas=e},M=function(){var e=Math.PI/180,t=.25*Math.PI,i=new Float32Array([0,0,0,1]),r=new Float32Array([0,0,0]);function s(s,n,a,o,l,A){!function(i,r,s,n){var a=Math.tan(r?r.upDegrees*e:t),o=Math.tan(r?r.downDegrees*e:t),l=Math.tan(r?r.leftDegrees*e:t),A=Math.tan(r?r.rightDegrees*e:t),h=2/(l+A),c=2/(a+o);i[0]=h,i[1]=0,i[2]=0,i[3]=0,i[4]=0,i[5]=c,i[6]=0,i[7]=0,i[8]=-(l-A)*h*.5,i[9]=(a-o)*c*.5,i[10]=n/(s-n),i[11]=-1,i[12]=0,i[13]=0,i[14]=n*s/(s-n),i[15]=0}(s,o||null,A.depthNear,A.depthFar);var h,c,d,u,p,f,m,g,v,w,y,b,E,S,M,x,_,F,R,T=a.orientation||i,B=a.position||r;h=n,d=B,u=(c=T)[0],p=c[1],f=c[2],m=c[3],y=u*(g=u+u),b=u*(v=p+p),E=u*(w=f+f),S=p*v,M=p*w,x=f*w,_=m*g,F=m*v,R=m*w,h[0]=1-(S+x),h[1]=b+R,h[2]=E-F,h[3]=0,h[4]=b-R,h[5]=1-(y+x),h[6]=M+_,h[7]=0,h[8]=E+F,h[9]=M-_,h[10]=1-(y+S),h[11]=0,h[12]=d[0],h[13]=d[1],h[14]=d[2],h[15]=1,l&&function(e,t,i){var r,s,n,a,o,l,A,h,c,d,u,p,f=i[0],m=i[1],g=i[2];t===e?(e[12]=t[0]*f+t[4]*m+t[8]*g+t[12],e[13]=t[1]*f+t[5]*m+t[9]*g+t[13],e[14]=t[2]*f+t[6]*m+t[10]*g+t[14],e[15]=t[3]*f+t[7]*m+t[11]*g+t[15]):(r=t[0],s=t[1],n=t[2],a=t[3],o=t[4],l=t[5],A=t[6],h=t[7],c=t[8],d=t[9],u=t[10],p=t[11],e[0]=r,e[1]=s,e[2]=n,e[3]=a,e[4]=o,e[5]=l,e[6]=A,e[7]=h,e[8]=c,e[9]=d,e[10]=u,e[11]=p,e[12]=r*f+o*m+c*g+t[12],e[13]=s*f+l*m+d*g+t[13],e[14]=n*f+A*m+u*g+t[14],e[15]=a*f+h*m+p*g+t[15])}(n,n,l),function(e,t){var i=t[0],r=t[1],s=t[2],n=t[3],a=t[4],o=t[5],l=t[6],A=t[7],h=t[8],c=t[9],d=t[10],u=t[11],p=t[12],f=t[13],m=t[14],g=t[15],v=i*o-r*a,w=i*l-s*a,y=i*A-n*a,b=r*l-s*o,E=r*A-n*o,S=s*A-n*l,M=h*f-c*p,x=h*m-d*p,_=h*g-u*p,F=c*m-d*f,R=c*g-u*f,T=d*g-u*m,B=v*T-w*R+y*F+b*_-E*x+S*M;if(!B)return null;B=1/B,e[0]=(o*T-l*R+A*F)*B,e[1]=(s*R-r*T-n*F)*B,e[2]=(f*S-m*E+g*b)*B,e[3]=(d*E-c*S-u*b)*B,e[4]=(l*_-a*T-A*x)*B,e[5]=(i*T-s*_+n*x)*B,e[6]=(m*y-p*S-g*w)*B,e[7]=(h*S-d*y+u*w)*B,e[8]=(a*R-o*_+A*M)*B,e[9]=(r*_-i*R-n*M)*B,e[10]=(p*E-f*y+g*v)*B,e[11]=(c*y-h*E-u*v)*B,e[12]=(o*x-a*F-l*M)*B,e[13]=(i*F-r*x+s*M)*B,e[14]=(f*w-p*b-m*v)*B,e[15]=(h*b-c*w+d*v)*B}(n,n)}return function(e,t,i){return!(!e||!t||(e.pose=t,e.timestamp=t.timestamp,s(e.leftProjectionMatrix,e.leftViewMatrix,t,i._getFieldOfView("left"),i._getEyeOffset("left"),i),s(e.rightProjectionMatrix,e.rightViewMatrix,t,i._getFieldOfView("right"),i._getEyeOffset("right"),i),0))}}(),x=function(e){var t,i=e.indexOf("://");t=-1!==i?i+3:0;var r=e.indexOf("/",t);return-1===r&&(r=e.length),e.substring(0,r)},_=(e={},function(t,i){void 0===e[t]&&(console.warn("webvr-polyfill: "+i),e[t]=!0)}),F=function(e,t){var i=t?"Please use "+t+" instead.":"";_(e,e+" has been deprecated. This may not work on native WebVR displays. "+i)},R=function(e,t,i){if(t){for(var r=[],s=null,n=0;n<t.length;++n){var a=t[n];switch(a){case e.TEXTURE_BINDING_2D:case e.TEXTURE_BINDING_CUBE_MAP:var o=t[++n];if(o<e.TEXTURE0||o>e.TEXTURE31){console.error("TEXTURE_BINDING_2D or TEXTURE_BINDING_CUBE_MAP must be followed by a valid texture unit"),r.push(null,null);break}s||(s=e.getParameter(e.ACTIVE_TEXTURE)),e.activeTexture(o),r.push(e.getParameter(a),null);break;case e.ACTIVE_TEXTURE:s=e.getParameter(e.ACTIVE_TEXTURE),r.push(null);break;default:r.push(e.getParameter(a))}}i(e);for(var n=0;n<t.length;++n){var a=t[n],l=r[n];switch(a){case e.ACTIVE_TEXTURE:break;case e.ARRAY_BUFFER_BINDING:e.bindBuffer(e.ARRAY_BUFFER,l);break;case e.COLOR_CLEAR_VALUE:e.clearColor(l[0],l[1],l[2],l[3]);break;case e.COLOR_WRITEMASK:e.colorMask(l[0],l[1],l[2],l[3]);break;case e.CURRENT_PROGRAM:e.useProgram(l);break;case e.ELEMENT_ARRAY_BUFFER_BINDING:e.bindBuffer(e.ELEMENT_ARRAY_BUFFER,l);break;case e.FRAMEBUFFER_BINDING:e.bindFramebuffer(e.FRAMEBUFFER,l);break;case e.RENDERBUFFER_BINDING:e.bindRenderbuffer(e.RENDERBUFFER,l);break;case e.TEXTURE_BINDING_2D:var o=t[++n];if(o<e.TEXTURE0||o>e.TEXTURE31)break;e.activeTexture(o),e.bindTexture(e.TEXTURE_2D,l);break;case e.TEXTURE_BINDING_CUBE_MAP:var o=t[++n];if(o<e.TEXTURE0||o>e.TEXTURE31)break;e.activeTexture(o),e.bindTexture(e.TEXTURE_CUBE_MAP,l);break;case e.VIEWPORT:e.viewport(l[0],l[1],l[2],l[3]);break;case e.BLEND:case e.CULL_FACE:case e.DEPTH_TEST:case e.SCISSOR_TEST:case e.STENCIL_TEST:l?e.enable(a):e.disable(a);break;default:console.log("No GL restore behavior for 0x"+a.toString(16))}s&&e.activeTexture(s)}}else i(e)},T=["attribute vec2 position;","attribute vec3 texCoord;","varying vec2 vTexCoord;","uniform vec4 viewportOffsetScale[2];","void main() {","  vec4 viewport = viewportOffsetScale[int(texCoord.z)];","  vTexCoord = (texCoord.xy * viewport.zw) + viewport.xy;","  gl_Position = vec4( position, 1.0, 1.0 );","}"].join("\n"),B=["precision mediump float;","uniform sampler2D diffuse;","varying vec2 vTexCoord;","void main() {","  gl_FragColor = texture2D(diffuse, vTexCoord);","}"].join("\n");function P(e,t,i,r){this.gl=e,this.cardboardUI=t,this.bufferScale=i,this.dirtySubmitFrameBindings=r,this.ctxAttribs=e.getContextAttributes(),this.instanceExt=e.getExtension("ANGLE_instanced_arrays"),this.meshWidth=20,this.meshHeight=20,this.bufferWidth=e.drawingBufferWidth,this.bufferHeight=e.drawingBufferHeight,this.realBindFramebuffer=e.bindFramebuffer,this.realEnable=e.enable,this.realDisable=e.disable,this.realColorMask=e.colorMask,this.realClearColor=e.clearColor,this.realViewport=e.viewport,o()||(this.realCanvasWidth=Object.getOwnPropertyDescriptor(e.canvas.__proto__,"width"),this.realCanvasHeight=Object.getOwnPropertyDescriptor(e.canvas.__proto__,"height")),this.isPatched=!1,this.lastBoundFramebuffer=null,this.cullFace=!1,this.depthTest=!1,this.blend=!1,this.scissorTest=!1,this.stencilTest=!1,this.viewport=[0,0,0,0],this.colorMask=[!0,!0,!0,!0],this.clearColor=[0,0,0,0],this.attribs={position:0,texCoord:1},this.program=w(e,T,B,this.attribs),this.uniforms=y(e,this.program),this.viewportOffsetScale=new Float32Array(8),this.setTextureBounds(),this.vertexBuffer=e.createBuffer(),this.indexBuffer=e.createBuffer(),this.indexCount=0,this.renderTarget=e.createTexture(),this.framebuffer=e.createFramebuffer(),this.depthStencilBuffer=null,this.depthBuffer=null,this.stencilBuffer=null,this.ctxAttribs.depth&&this.ctxAttribs.stencil?this.depthStencilBuffer=e.createRenderbuffer():this.ctxAttribs.depth?this.depthBuffer=e.createRenderbuffer():this.ctxAttribs.stencil&&(this.stencilBuffer=e.createRenderbuffer()),this.patch(),this.onResize()}P.prototype.destroy=function(){var e=this.gl;this.unpatch(),e.deleteProgram(this.program),e.deleteBuffer(this.vertexBuffer),e.deleteBuffer(this.indexBuffer),e.deleteTexture(this.renderTarget),e.deleteFramebuffer(this.framebuffer),this.depthStencilBuffer&&e.deleteRenderbuffer(this.depthStencilBuffer),this.depthBuffer&&e.deleteRenderbuffer(this.depthBuffer),this.stencilBuffer&&e.deleteRenderbuffer(this.stencilBuffer),this.cardboardUI&&this.cardboardUI.destroy()},P.prototype.onResize=function(){var e=this.gl,t=this,i=[e.RENDERBUFFER_BINDING,e.TEXTURE_BINDING_2D,e.TEXTURE0];R(e,i,function(e){t.realBindFramebuffer.call(e,e.FRAMEBUFFER,null),t.scissorTest&&t.realDisable.call(e,e.SCISSOR_TEST),t.realColorMask.call(e,!0,!0,!0,!0),t.realViewport.call(e,0,0,e.drawingBufferWidth,e.drawingBufferHeight),t.realClearColor.call(e,0,0,0,1),e.clear(e.COLOR_BUFFER_BIT),t.realBindFramebuffer.call(e,e.FRAMEBUFFER,t.framebuffer),e.bindTexture(e.TEXTURE_2D,t.renderTarget),e.texImage2D(e.TEXTURE_2D,0,t.ctxAttribs.alpha?e.RGBA:e.RGB,t.bufferWidth,t.bufferHeight,0,t.ctxAttribs.alpha?e.RGBA:e.RGB,e.UNSIGNED_BYTE,null),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_MAG_FILTER,e.LINEAR),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_MIN_FILTER,e.LINEAR),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_WRAP_S,e.CLAMP_TO_EDGE),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_WRAP_T,e.CLAMP_TO_EDGE),e.framebufferTexture2D(e.FRAMEBUFFER,e.COLOR_ATTACHMENT0,e.TEXTURE_2D,t.renderTarget,0),t.ctxAttribs.depth&&t.ctxAttribs.stencil?(e.bindRenderbuffer(e.RENDERBUFFER,t.depthStencilBuffer),e.renderbufferStorage(e.RENDERBUFFER,e.DEPTH_STENCIL,t.bufferWidth,t.bufferHeight),e.framebufferRenderbuffer(e.FRAMEBUFFER,e.DEPTH_STENCIL_ATTACHMENT,e.RENDERBUFFER,t.depthStencilBuffer)):t.ctxAttribs.depth?(e.bindRenderbuffer(e.RENDERBUFFER,t.depthBuffer),e.renderbufferStorage(e.RENDERBUFFER,e.DEPTH_COMPONENT16,t.bufferWidth,t.bufferHeight),e.framebufferRenderbuffer(e.FRAMEBUFFER,e.DEPTH_ATTACHMENT,e.RENDERBUFFER,t.depthBuffer)):t.ctxAttribs.stencil&&(e.bindRenderbuffer(e.RENDERBUFFER,t.stencilBuffer),e.renderbufferStorage(e.RENDERBUFFER,e.STENCIL_INDEX8,t.bufferWidth,t.bufferHeight),e.framebufferRenderbuffer(e.FRAMEBUFFER,e.STENCIL_ATTACHMENT,e.RENDERBUFFER,t.stencilBuffer)),!e.checkFramebufferStatus(e.FRAMEBUFFER)===e.FRAMEBUFFER_COMPLETE&&console.error("Framebuffer incomplete!"),t.realBindFramebuffer.call(e,e.FRAMEBUFFER,t.lastBoundFramebuffer),t.scissorTest&&t.realEnable.call(e,e.SCISSOR_TEST),t.realColorMask.apply(e,t.colorMask),t.realViewport.apply(e,t.viewport),t.realClearColor.apply(e,t.clearColor)}),this.cardboardUI&&this.cardboardUI.onResize()},P.prototype.patch=function(){if(!this.isPatched){var e=this,t=this.gl.canvas,i=this.gl;o()||(t.width=m()*this.bufferScale,t.height=g()*this.bufferScale,Object.defineProperty(t,"width",{configurable:!0,enumerable:!0,get:function(){return e.bufferWidth},set:function(i){e.bufferWidth=i,e.realCanvasWidth.set.call(t,i),e.onResize()}}),Object.defineProperty(t,"height",{configurable:!0,enumerable:!0,get:function(){return e.bufferHeight},set:function(i){e.bufferHeight=i,e.realCanvasHeight.set.call(t,i),e.onResize()}})),this.lastBoundFramebuffer=i.getParameter(i.FRAMEBUFFER_BINDING),null==this.lastBoundFramebuffer&&(this.lastBoundFramebuffer=this.framebuffer,this.gl.bindFramebuffer(i.FRAMEBUFFER,this.framebuffer)),this.gl.bindFramebuffer=function(t,r){e.lastBoundFramebuffer=r||e.framebuffer,e.realBindFramebuffer.call(i,t,e.lastBoundFramebuffer)},this.cullFace=i.getParameter(i.CULL_FACE),this.depthTest=i.getParameter(i.DEPTH_TEST),this.blend=i.getParameter(i.BLEND),this.scissorTest=i.getParameter(i.SCISSOR_TEST),this.stencilTest=i.getParameter(i.STENCIL_TEST),i.enable=function(t){switch(t){case i.CULL_FACE:e.cullFace=!0;break;case i.DEPTH_TEST:e.depthTest=!0;break;case i.BLEND:e.blend=!0;break;case i.SCISSOR_TEST:e.scissorTest=!0;break;case i.STENCIL_TEST:e.stencilTest=!0}e.realEnable.call(i,t)},i.disable=function(t){switch(t){case i.CULL_FACE:e.cullFace=!1;break;case i.DEPTH_TEST:e.depthTest=!1;break;case i.BLEND:e.blend=!1;break;case i.SCISSOR_TEST:e.scissorTest=!1;break;case i.STENCIL_TEST:e.stencilTest=!1}e.realDisable.call(i,t)},this.colorMask=i.getParameter(i.COLOR_WRITEMASK),i.colorMask=function(t,r,s,n){e.colorMask[0]=t,e.colorMask[1]=r,e.colorMask[2]=s,e.colorMask[3]=n,e.realColorMask.call(i,t,r,s,n)},this.clearColor=i.getParameter(i.COLOR_CLEAR_VALUE),i.clearColor=function(t,r,s,n){e.clearColor[0]=t,e.clearColor[1]=r,e.clearColor[2]=s,e.clearColor[3]=n,e.realClearColor.call(i,t,r,s,n)},this.viewport=i.getParameter(i.VIEWPORT),i.viewport=function(t,r,s,n){e.viewport[0]=t,e.viewport[1]=r,e.viewport[2]=s,e.viewport[3]=n,e.realViewport.call(i,t,r,s,n)},this.isPatched=!0,S(t)}},P.prototype.unpatch=function(){if(this.isPatched){var e=this.gl,t=this.gl.canvas;o()||(Object.defineProperty(t,"width",this.realCanvasWidth),Object.defineProperty(t,"height",this.realCanvasHeight)),t.width=this.bufferWidth,t.height=this.bufferHeight,e.bindFramebuffer=this.realBindFramebuffer,e.enable=this.realEnable,e.disable=this.realDisable,e.colorMask=this.realColorMask,e.clearColor=this.realClearColor,e.viewport=this.realViewport,this.lastBoundFramebuffer==this.framebuffer&&e.bindFramebuffer(e.FRAMEBUFFER,null),this.isPatched=!1,setTimeout(function(){S(t)},1)}},P.prototype.setTextureBounds=function(e,t){e||(e=[0,0,.5,1]),t||(t=[.5,0,.5,1]),this.viewportOffsetScale[0]=e[0],this.viewportOffsetScale[1]=e[1],this.viewportOffsetScale[2]=e[2],this.viewportOffsetScale[3]=e[3],this.viewportOffsetScale[4]=t[0],this.viewportOffsetScale[5]=t[1],this.viewportOffsetScale[6]=t[2],this.viewportOffsetScale[7]=t[3]},P.prototype.submitFrame=function(){var e=this.gl,t=this,i=[];if(this.dirtySubmitFrameBindings||i.push(e.CURRENT_PROGRAM,e.ARRAY_BUFFER_BINDING,e.ELEMENT_ARRAY_BUFFER_BINDING,e.TEXTURE_BINDING_2D,e.TEXTURE0),R(e,i,function(e){t.realBindFramebuffer.call(e,e.FRAMEBUFFER,null);var i=0,r=0;t.instanceExt&&(i=e.getVertexAttrib(t.attribs.position,t.instanceExt.VERTEX_ATTRIB_ARRAY_DIVISOR_ANGLE),r=e.getVertexAttrib(t.attribs.texCoord,t.instanceExt.VERTEX_ATTRIB_ARRAY_DIVISOR_ANGLE)),t.cullFace&&t.realDisable.call(e,e.CULL_FACE),t.depthTest&&t.realDisable.call(e,e.DEPTH_TEST),t.blend&&t.realDisable.call(e,e.BLEND),t.scissorTest&&t.realDisable.call(e,e.SCISSOR_TEST),t.stencilTest&&t.realDisable.call(e,e.STENCIL_TEST),t.realColorMask.call(e,!0,!0,!0,!0),t.realViewport.call(e,0,0,e.drawingBufferWidth,e.drawingBufferHeight),(t.ctxAttribs.alpha||o())&&(t.realClearColor.call(e,0,0,0,1),e.clear(e.COLOR_BUFFER_BIT)),e.useProgram(t.program),e.bindBuffer(e.ELEMENT_ARRAY_BUFFER,t.indexBuffer),e.bindBuffer(e.ARRAY_BUFFER,t.vertexBuffer),e.enableVertexAttribArray(t.attribs.position),e.enableVertexAttribArray(t.attribs.texCoord),e.vertexAttribPointer(t.attribs.position,2,e.FLOAT,!1,20,0),e.vertexAttribPointer(t.attribs.texCoord,3,e.FLOAT,!1,20,8),t.instanceExt&&(0!=i&&t.instanceExt.vertexAttribDivisorANGLE(t.attribs.position,0),0!=r&&t.instanceExt.vertexAttribDivisorANGLE(t.attribs.texCoord,0)),e.activeTexture(e.TEXTURE0),e.uniform1i(t.uniforms.diffuse,0),e.bindTexture(e.TEXTURE_2D,t.renderTarget),e.uniform4fv(t.uniforms.viewportOffsetScale,t.viewportOffsetScale),e.drawElements(e.TRIANGLES,t.indexCount,e.UNSIGNED_SHORT,0),t.cardboardUI&&t.cardboardUI.renderNoState(),t.realBindFramebuffer.call(t.gl,e.FRAMEBUFFER,t.framebuffer),t.ctxAttribs.preserveDrawingBuffer||(t.realClearColor.call(e,0,0,0,0),e.clear(e.COLOR_BUFFER_BIT)),t.dirtySubmitFrameBindings||t.realBindFramebuffer.call(e,e.FRAMEBUFFER,t.lastBoundFramebuffer),t.cullFace&&t.realEnable.call(e,e.CULL_FACE),t.depthTest&&t.realEnable.call(e,e.DEPTH_TEST),t.blend&&t.realEnable.call(e,e.BLEND),t.scissorTest&&t.realEnable.call(e,e.SCISSOR_TEST),t.stencilTest&&t.realEnable.call(e,e.STENCIL_TEST),t.realColorMask.apply(e,t.colorMask),t.realViewport.apply(e,t.viewport),!t.ctxAttribs.alpha&&t.ctxAttribs.preserveDrawingBuffer||t.realClearColor.apply(e,t.clearColor),t.instanceExt&&(0!=i&&t.instanceExt.vertexAttribDivisorANGLE(t.attribs.position,i),0!=r&&t.instanceExt.vertexAttribDivisorANGLE(t.attribs.texCoord,r))}),o()){var r=e.canvas;r.width==t.bufferWidth&&r.height==t.bufferHeight||(t.bufferWidth=r.width,t.bufferHeight=r.height,t.onResize())}},P.prototype.updateDeviceInfo=function(e){var t=this.gl,i=this,r=[t.ARRAY_BUFFER_BINDING,t.ELEMENT_ARRAY_BUFFER_BINDING];R(t,r,function(t){var r=i.computeMeshVertices_(i.meshWidth,i.meshHeight,e);if(t.bindBuffer(t.ARRAY_BUFFER,i.vertexBuffer),t.bufferData(t.ARRAY_BUFFER,r,t.STATIC_DRAW),!i.indexCount){var s=i.computeMeshIndices_(i.meshWidth,i.meshHeight);t.bindBuffer(t.ELEMENT_ARRAY_BUFFER,i.indexBuffer),t.bufferData(t.ELEMENT_ARRAY_BUFFER,s,t.STATIC_DRAW),i.indexCount=s.length}})},P.prototype.computeMeshVertices_=function(e,t,i){for(var r=new Float32Array(2*e*t*5),s=i.getLeftEyeVisibleTanAngles(),n=i.getLeftEyeNoLensTanAngles(),o=i.getLeftEyeVisibleScreenRect(n),l=0,A=0;A<2;A++){for(var h=0;h<t;h++)for(var c=0;c<e;c++,l++){var d=c/(e-1),u=h/(t-1),p=d,f=u,m=a(s[0],s[2],d),g=a(s[3],s[1],u),v=Math.sqrt(m*m+g*g),w=i.distortion.distortInverse(v),y=m*w/v,b=g*w/v;d=(y-n[0])/(n[2]-n[0]),u=(b-n[3])/(n[1]-n[3]),d=2*(o.x+d*o.width-.5),u=2*(o.y+u*o.height-.5),r[5*l+0]=d,r[5*l+1]=u,r[5*l+2]=p,r[5*l+3]=f,r[5*l+4]=A}var E=s[2]-s[0];s[0]=-(E+s[0]),s[2]=E-s[2],E=n[2]-n[0],n[0]=-(E+n[0]),n[2]=E-n[2],o.x=1-(o.x+o.width)}return r},P.prototype.computeMeshIndices_=function(e,t){for(var i=new Uint16Array(2*(e-1)*(t-1)*6),r=e/2,s=t/2,n=0,a=0,o=0;o<2;o++)for(var l=0;l<t;l++)for(var A=0;A<e;A++,n++)0!=A&&0!=l&&(A<=r==l<=s?(i[a++]=n,i[a++]=n-e-1,i[a++]=n-e,i[a++]=n-e-1,i[a++]=n,i[a++]=n-1):(i[a++]=n-1,i[a++]=n-e,i[a++]=n,i[a++]=n-e,i[a++]=n-1,i[a++]=n-e-1));return i},P.prototype.getOwnPropertyDescriptor_=function(e,t){var i=Object.getOwnPropertyDescriptor(e,t);return void 0!==i.get&&void 0!==i.set||(i.configurable=!0,i.enumerable=!0,i.get=function(){return this.getAttribute(t)},i.set=function(e){this.setAttribute(t,e)}),i};var C=["attribute vec2 position;","uniform mat4 projectionMat;","void main() {","  gl_Position = projectionMat * vec4( position, -1.0, 1.0 );","}"].join("\n"),D=["precision mediump float;","uniform vec4 color;","void main() {","  gl_FragColor = color;","}"].join("\n"),I=Math.PI/180,L=.3125;function O(e){this.gl=e,this.attribs={position:0},this.program=w(e,C,D,this.attribs),this.uniforms=y(e,this.program),this.vertexBuffer=e.createBuffer(),this.gearOffset=0,this.gearVertexCount=0,this.arrowOffset=0,this.arrowVertexCount=0,this.projMat=new Float32Array(16),this.listener=null,this.onResize()}function N(e){this.coefficients=e}O.prototype.destroy=function(){var e=this.gl;this.listener&&e.canvas.removeEventListener("click",this.listener,!1),e.deleteProgram(this.program),e.deleteBuffer(this.vertexBuffer)},O.prototype.listen=function(e,t){var i=this.gl.canvas;this.listener=function(r){var s=i.clientWidth/2;r.clientX>s-42&&r.clientX<s+42&&r.clientY>i.clientHeight-42?e(r):r.clientX<42&&r.clientY<42&&t(r)},i.addEventListener("click",this.listener,!1)},O.prototype.onResize=function(){var e=this.gl,t=this,i=[e.ARRAY_BUFFER_BINDING];R(e,i,function(e){var i=[],r=e.drawingBufferWidth/2,s=Math.max(screen.width,screen.height)*window.devicePixelRatio,n=e.drawingBufferWidth/s,a=n*window.devicePixelRatio,o=4*a/2,l=42*a,A=28*a/2,h=14*a;function c(e,t){var s=(90-e)*I,n=Math.cos(s),a=Math.sin(s);i.push(L*n*A+r,L*a*A+A),i.push(t*n*A+r,t*a*A+A)}i.push(r-o,l),i.push(r-o,e.drawingBufferHeight),i.push(r+o,l),i.push(r+o,e.drawingBufferHeight),t.gearOffset=i.length/2;for(var d=0;d<=6;d++){var u=60*d;c(u,1),c(u+12,1),c(u+20,.75),c(u+40,.75),c(u+48,1)}function p(t,r){i.push(h+t,e.drawingBufferHeight-h-r)}t.gearVertexCount=i.length/2-t.gearOffset,t.arrowOffset=i.length/2;var f=o/Math.sin(45*I);p(0,A),p(A,0),p(A+f,f),p(f,A+f),p(f,A-f),p(0,A),p(A,2*A),p(A+f,2*A-f),p(f,A-f),p(0,A),p(f,A-o),p(28*a,A-o),p(f,A+o),p(28*a,A+o),t.arrowVertexCount=i.length/2-t.arrowOffset,e.bindBuffer(e.ARRAY_BUFFER,t.vertexBuffer),e.bufferData(e.ARRAY_BUFFER,new Float32Array(i),e.STATIC_DRAW)})},O.prototype.render=function(){var e=this.gl,t=this,i=[e.CULL_FACE,e.DEPTH_TEST,e.BLEND,e.SCISSOR_TEST,e.STENCIL_TEST,e.COLOR_WRITEMASK,e.VIEWPORT,e.CURRENT_PROGRAM,e.ARRAY_BUFFER_BINDING];R(e,i,function(e){e.disable(e.CULL_FACE),e.disable(e.DEPTH_TEST),e.disable(e.BLEND),e.disable(e.SCISSOR_TEST),e.disable(e.STENCIL_TEST),e.colorMask(!0,!0,!0,!0),e.viewport(0,0,e.drawingBufferWidth,e.drawingBufferHeight),t.renderNoState()})},O.prototype.renderNoState=function(){var e,t,i,r,s,n,a,o,l,A,h=this.gl;h.useProgram(this.program),h.bindBuffer(h.ARRAY_BUFFER,this.vertexBuffer),h.enableVertexAttribArray(this.attribs.position),h.vertexAttribPointer(this.attribs.position,2,h.FLOAT,!1,8,0),h.uniform4f(this.uniforms.color,1,1,1,1),e=this.projMat,t=0,i=h.drawingBufferWidth,r=0,s=h.drawingBufferHeight,o=1/(t-i),l=1/(r-s),A=1/((n=.1)-(a=1024)),e[0]=-2*o,e[1]=0,e[2]=0,e[3]=0,e[4]=0,e[5]=-2*l,e[6]=0,e[7]=0,e[8]=0,e[9]=0,e[10]=2*A,e[11]=0,e[12]=(t+i)*o,e[13]=(s+r)*l,e[14]=(a+n)*A,e[15]=1,h.uniformMatrix4fv(this.uniforms.projectionMat,!1,this.projMat),h.drawArrays(h.TRIANGLE_STRIP,0,4),h.drawArrays(h.TRIANGLE_STRIP,this.gearOffset,this.gearVertexCount),h.drawArrays(h.TRIANGLE_STRIP,this.arrowOffset,this.arrowVertexCount)},N.prototype.distortInverse=function(e){for(var t=0,i=1,r=e-this.distort(t);Math.abs(i-t)>1e-4;){var s=e-this.distort(i),n=i-s*((i-t)/(s-r));t=i,i=n,r=s}return i},N.prototype.distort=function(e){for(var t=e*e,i=0,r=0;r<this.coefficients.length;r++)i=t*(i+this.coefficients[r]);return(i+1)*e};var G=Math.PI/180,Q=180/Math.PI,k=function(e,t,i){this.x=e||0,this.y=t||0,this.z=i||0};k.prototype={constructor:k,set:function(e,t,i){return this.x=e,this.y=t,this.z=i,this},copy:function(e){return this.x=e.x,this.y=e.y,this.z=e.z,this},length:function(){return Math.sqrt(this.x*this.x+this.y*this.y+this.z*this.z)},normalize:function(){var e=this.length();if(0!==e){var t=1/e;this.multiplyScalar(t)}else this.x=0,this.y=0,this.z=0;return this},multiplyScalar:function(e){this.x*=e,this.y*=e,this.z*=e},applyQuaternion:function(e){var t=this.x,i=this.y,r=this.z,s=e.x,n=e.y,a=e.z,o=e.w,l=o*t+n*r-a*i,A=o*i+a*t-s*r,h=o*r+s*i-n*t,c=-s*t-n*i-a*r;return this.x=l*o+c*-s+A*-a-h*-n,this.y=A*o+c*-n+h*-s-l*-a,this.z=h*o+c*-a+l*-n-A*-s,this},dot:function(e){return this.x*e.x+this.y*e.y+this.z*e.z},crossVectors:function(e,t){var i=e.x,r=e.y,s=e.z,n=t.x,a=t.y,o=t.z;return this.x=r*o-s*a,this.y=s*n-i*o,this.z=i*a-r*n,this}};var z,V,U=function(e,t,i,r){this.x=e||0,this.y=t||0,this.z=i||0,this.w=void 0!==r?r:1};function H(e){this.width=e.width||m(),this.height=e.height||g(),this.widthMeters=e.widthMeters,this.heightMeters=e.heightMeters,this.bevelMeters=e.bevelMeters}U.prototype={constructor:U,set:function(e,t,i,r){return this.x=e,this.y=t,this.z=i,this.w=r,this},copy:function(e){return this.x=e.x,this.y=e.y,this.z=e.z,this.w=e.w,this},setFromEulerXYZ:function(e,t,i){var r=Math.cos(e/2),s=Math.cos(t/2),n=Math.cos(i/2),a=Math.sin(e/2),o=Math.sin(t/2),l=Math.sin(i/2);return this.x=a*s*n+r*o*l,this.y=r*o*n-a*s*l,this.z=r*s*l+a*o*n,this.w=r*s*n-a*o*l,this},setFromEulerYXZ:function(e,t,i){var r=Math.cos(e/2),s=Math.cos(t/2),n=Math.cos(i/2),a=Math.sin(e/2),o=Math.sin(t/2),l=Math.sin(i/2);return this.x=a*s*n+r*o*l,this.y=r*o*n-a*s*l,this.z=r*s*l-a*o*n,this.w=r*s*n+a*o*l,this},setFromAxisAngle:function(e,t){var i=t/2,r=Math.sin(i);return this.x=e.x*r,this.y=e.y*r,this.z=e.z*r,this.w=Math.cos(i),this},multiply:function(e){return this.multiplyQuaternions(this,e)},multiplyQuaternions:function(e,t){var i=e.x,r=e.y,s=e.z,n=e.w,a=t.x,o=t.y,l=t.z,A=t.w;return this.x=i*A+n*a+r*l-s*o,this.y=r*A+n*o+s*a-i*l,this.z=s*A+n*l+i*o-r*a,this.w=n*A-i*a-r*o-s*l,this},inverse:function(){return this.x*=-1,this.y*=-1,this.z*=-1,this.normalize(),this},normalize:function(){var e=Math.sqrt(this.x*this.x+this.y*this.y+this.z*this.z+this.w*this.w);return 0===e?(this.x=0,this.y=0,this.z=0,this.w=1):(e=1/e,this.x=this.x*e,this.y=this.y*e,this.z=this.z*e,this.w=this.w*e),this},slerp:function(e,t){if(0===t)return this;if(1===t)return this.copy(e);var i=this.x,r=this.y,s=this.z,n=this.w,a=n*e.w+i*e.x+r*e.y+s*e.z;if(a<0?(this.w=-e.w,this.x=-e.x,this.y=-e.y,this.z=-e.z,a=-a):this.copy(e),a>=1)return this.w=n,this.x=i,this.y=r,this.z=s,this;var o=Math.acos(a),l=Math.sqrt(1-a*a);if(Math.abs(l)<.001)return this.w=.5*(n+this.w),this.x=.5*(i+this.x),this.y=.5*(r+this.y),this.z=.5*(s+this.z),this;var A=Math.sin((1-t)*o)/l,h=Math.sin(t*o)/l;return this.w=n*A+this.w*h,this.x=i*A+this.x*h,this.y=r*A+this.y*h,this.z=s*A+this.z*h,this},setFromUnitVectors:function(e,t){return void 0===z&&(z=new k),(V=e.dot(t)+1)<1e-6?(V=0,Math.abs(e.x)>Math.abs(e.z)?z.set(-e.y,e.x,0):z.set(0,-e.z,e.y)):z.crossVectors(e,t),this.x=z.x,this.y=z.y,this.z=z.z,this.w=V,this.normalize(),this}};var X=new H({widthMeters:.11,heightMeters:.062,bevelMeters:.004}),W=new H({widthMeters:.1038,heightMeters:.0584,bevelMeters:.004}),j={CardboardV1:new Y({id:"CardboardV1",label:"Cardboard I/O 2014",fov:40,interLensDistance:.06,baselineLensDistance:.035,screenLensDistance:.042,distortionCoefficients:[.441,.156],inverseCoefficients:[-.4410035,.42756155,-.4804439,.5460139,-.58821183,.5733938,-.48303202,.33299083,-.17573841,.0651772,-.01488963,.001559834]}),CardboardV2:new Y({id:"CardboardV2",label:"Cardboard I/O 2015",fov:60,interLensDistance:.064,baselineLensDistance:.035,screenLensDistance:.039,distortionCoefficients:[.34,.55],inverseCoefficients:[-.33836704,-.18162185,.862655,-1.2462051,1.0560602,-.58208317,.21609078,-.05444823,.009177956,-.0009904169,6183535e-11,-16981803e-13]})};function q(e,t){this.viewer=j.CardboardV2,this.updateDeviceParams(e),this.distortion=new N(this.viewer.distortionCoefficients);for(var i=0;i<t.length;i++){var r=t[i];j[r.id]=new Y(r)}}function Y(e){this.id=e.id,this.label=e.label,this.fov=e.fov,this.interLensDistance=e.interLensDistance,this.baselineLensDistance=e.baselineLensDistance,this.screenLensDistance=e.screenLensDistance,this.distortionCoefficients=e.distortionCoefficients,this.inverseCoefficients=e.inverseCoefficients}q.prototype.updateDeviceParams=function(e){this.device=this.determineDevice_(e)||this.device},q.prototype.getDevice=function(){return this.device},q.prototype.setViewer=function(e){this.viewer=e,this.distortion=new N(this.viewer.distortionCoefficients)},q.prototype.determineDevice_=function(e){if(!e)return o()?(console.warn("Using fallback iOS device measurements."),W):(console.warn("Using fallback Android device measurements."),X);var t=.0254/e.xdpi,i=.0254/e.ydpi,r=m(),s=g();return new H({widthMeters:t*r,heightMeters:i*s,bevelMeters:.001*e.bevelMm})},q.prototype.getDistortedFieldOfViewLeftEye=function(){var e=this.viewer,t=this.device,i=this.distortion,r=e.screenLensDistance,s=(t.widthMeters-e.interLensDistance)/2,n=e.interLensDistance/2,a=e.baselineLensDistance-t.bevelMeters,o=t.heightMeters-a,l=Q*Math.atan(i.distort(s/r)),A=Q*Math.atan(i.distort(n/r)),h=Q*Math.atan(i.distort(a/r)),c=Q*Math.atan(i.distort(o/r));return{leftDegrees:Math.min(l,e.fov),rightDegrees:Math.min(A,e.fov),downDegrees:Math.min(h,e.fov),upDegrees:Math.min(c,e.fov)}},q.prototype.getLeftEyeVisibleTanAngles=function(){var e=this.viewer,t=this.device,i=this.distortion,r=Math.tan(-G*e.fov),s=Math.tan(G*e.fov),n=Math.tan(G*e.fov),a=Math.tan(-G*e.fov),o=t.widthMeters/4,l=t.heightMeters/2,A=e.baselineLensDistance-t.bevelMeters-l,h=e.interLensDistance/2-o,c=-A,d=e.screenLensDistance,u=i.distort((h-o)/d),p=i.distort((c+l)/d),f=i.distort((h+o)/d),m=i.distort((c-l)/d),g=new Float32Array(4);return g[0]=Math.max(r,u),g[1]=Math.min(s,p),g[2]=Math.min(n,f),g[3]=Math.max(a,m),g},q.prototype.getLeftEyeNoLensTanAngles=function(){var e=this.viewer,t=this.device,i=this.distortion,r=new Float32Array(4),s=i.distortInverse(Math.tan(-G*e.fov)),n=i.distortInverse(Math.tan(G*e.fov)),a=i.distortInverse(Math.tan(G*e.fov)),o=i.distortInverse(Math.tan(-G*e.fov)),l=t.widthMeters/4,A=t.heightMeters/2,h=e.baselineLensDistance-t.bevelMeters-A,c=e.interLensDistance/2-l,d=-h,u=e.screenLensDistance,p=(c-l)/u,f=(d+A)/u,m=(c+l)/u,g=(d-A)/u;return r[0]=Math.max(s,p),r[1]=Math.min(n,f),r[2]=Math.min(a,m),r[3]=Math.max(o,g),r},q.prototype.getLeftEyeVisibleScreenRect=function(e){var t=this.viewer,i=this.device,r=t.screenLensDistance,s=(i.widthMeters-t.interLensDistance)/2,n=t.baselineLensDistance-i.bevelMeters,a=(e[0]*r+s)/i.widthMeters,o=(e[1]*r+n)/i.heightMeters,l=(e[2]*r+s)/i.widthMeters,A=(e[3]*r+n)/i.heightMeters;return{x:a,y:A,width:l-a,height:o-A}},q.prototype.getFieldOfViewLeftEye=function(e){return e?this.getUndistortedFieldOfViewLeftEye():this.getDistortedFieldOfViewLeftEye()},q.prototype.getFieldOfViewRightEye=function(e){var t=this.getFieldOfViewLeftEye(e);return{leftDegrees:t.rightDegrees,rightDegrees:t.leftDegrees,upDegrees:t.upDegrees,downDegrees:t.downDegrees}},q.prototype.getUndistortedFieldOfViewLeftEye=function(){var e=this.getUndistortedParams_();return{leftDegrees:Q*Math.atan(e.outerDist),rightDegrees:Q*Math.atan(e.innerDist),downDegrees:Q*Math.atan(e.bottomDist),upDegrees:Q*Math.atan(e.topDist)}},q.prototype.getUndistortedViewportLeftEye=function(){var e=this.getUndistortedParams_(),t=this.viewer,i=this.device,r=t.screenLensDistance,s=i.widthMeters/r,n=i.heightMeters/r,a=i.width/s,o=i.height/n,l=Math.round((e.eyePosX-e.outerDist)*a),A=Math.round((e.eyePosY-e.bottomDist)*o);return{x:l,y:A,width:Math.round((e.eyePosX+e.innerDist)*a)-l,height:Math.round((e.eyePosY+e.topDist)*o)-A}},q.prototype.getUndistortedParams_=function(){var e=this.viewer,t=this.device,i=this.distortion,r=e.screenLensDistance,s=e.interLensDistance/2/r,n=t.widthMeters/r,a=t.heightMeters/r,o=n/2-s,l=(e.baselineLensDistance-t.bevelMeters)/r,A=e.fov,h=i.distortInverse(Math.tan(G*A)),c=Math.min(o,h),d=Math.min(s,h),u=Math.min(l,h),p=Math.min(a-l,h);return{outerDist:c,innerDist:d,topDist:p,bottomDist:u,eyePosX:o,eyePosY:l}},q.Viewers=j;var Z={format:1,last_updated:"2019-11-09T17:36:14Z",devices:[{type:"android",rules:[{mdmh:"asus/*/Nexus 7/*"},{ua:"Nexus 7"}],dpi:[320.8,323],bw:3,ac:500},{type:"android",rules:[{mdmh:"asus/*/ASUS_X00PD/*"},{ua:"ASUS_X00PD"}],dpi:245,bw:3,ac:500},{type:"android",rules:[{mdmh:"asus/*/ASUS_X008D/*"},{ua:"ASUS_X008D"}],dpi:282,bw:3,ac:500},{type:"android",rules:[{mdmh:"asus/*/ASUS_Z00AD/*"},{ua:"ASUS_Z00AD"}],dpi:[403,404.6],bw:3,ac:1e3},{type:"android",rules:[{mdmh:"Google/*/Pixel 2 XL/*"},{ua:"Pixel 2 XL"}],dpi:537.9,bw:3,ac:1e3},{type:"android",rules:[{mdmh:"Google/*/Pixel 3 XL/*"},{ua:"Pixel 3 XL"}],dpi:[558.5,553.8],bw:3,ac:1e3},{type:"android",rules:[{mdmh:"Google/*/Pixel XL/*"},{ua:"Pixel XL"}],dpi:[537.9,533],bw:3,ac:1e3},{type:"android",rules:[{mdmh:"Google/*/Pixel 3/*"},{ua:"Pixel 3"}],dpi:442.4,bw:3,ac:1e3},{type:"android",rules:[{mdmh:"Google/*/Pixel 2/*"},{ua:"Pixel 2"}],dpi:441,bw:3,ac:500},{type:"android",rules:[{mdmh:"Google/*/Pixel/*"},{ua:"Pixel"}],dpi:[432.6,436.7],bw:3,ac:1e3},{type:"android",rules:[{mdmh:"HTC/*/HTC6435LVW/*"},{ua:"HTC6435LVW"}],dpi:[449.7,443.3],bw:3,ac:1e3},{type:"android",rules:[{mdmh:"HTC/*/HTC One XL/*"},{ua:"HTC One XL"}],dpi:[315.3,314.6],bw:3,ac:1e3},{type:"android",rules:[{mdmh:"htc/*/Nexus 9/*"},{ua:"Nexus 9"}],dpi:289,bw:3,ac:500},{type:"android",rules:[{mdmh:"HTC/*/HTC One M9/*"},{ua:"HTC One M9"}],dpi:[442.5,443.3],bw:3,ac:500},{type:"android",rules:[{mdmh:"HTC/*/HTC One_M8/*"},{ua:"HTC One_M8"}],dpi:[449.7,447.4],bw:3,ac:500},{type:"android",rules:[{mdmh:"HTC/*/HTC One/*"},{ua:"HTC One"}],dpi:472.8,bw:3,ac:1e3},{type:"android",rules:[{mdmh:"Huawei/*/Nexus 6P/*"},{ua:"Nexus 6P"}],dpi:[515.1,518],bw:3,ac:1e3},{type:"android",rules:[{mdmh:"Huawei/*/BLN-L24/*"},{ua:"HONORBLN-L24"}],dpi:480,bw:4,ac:500},{type:"android",rules:[{mdmh:"Huawei/*/BKL-L09/*"},{ua:"BKL-L09"}],dpi:403,bw:3.47,ac:500},{type:"android",rules:[{mdmh:"LENOVO/*/Lenovo PB2-690Y/*"},{ua:"Lenovo PB2-690Y"}],dpi:[457.2,454.713],bw:3,ac:500},{type:"android",rules:[{mdmh:"LGE/*/Nexus 5X/*"},{ua:"Nexus 5X"}],dpi:[422,419.9],bw:3,ac:1e3},{type:"android",rules:[{mdmh:"LGE/*/LGMS345/*"},{ua:"LGMS345"}],dpi:[221.7,219.1],bw:3,ac:500},{type:"android",rules:[{mdmh:"LGE/*/LG-D800/*"},{ua:"LG-D800"}],dpi:[422,424.1],bw:3,ac:500},{type:"android",rules:[{mdmh:"LGE/*/LG-D850/*"},{ua:"LG-D850"}],dpi:[537.9,541.9],bw:3,ac:500},{type:"android",rules:[{mdmh:"LGE/*/VS985 4G/*"},{ua:"VS985 4G"}],dpi:[537.9,535.6],bw:3,ac:1e3},{type:"android",rules:[{mdmh:"LGE/*/Nexus 5/*"},{ua:"Nexus 5 B"}],dpi:[442.4,444.8],bw:3,ac:1e3},{type:"android",rules:[{mdmh:"LGE/*/Nexus 4/*"},{ua:"Nexus 4"}],dpi:[319.8,318.4],bw:3,ac:1e3},{type:"android",rules:[{mdmh:"LGE/*/LG-P769/*"},{ua:"LG-P769"}],dpi:[240.6,247.5],bw:3,ac:1e3},{type:"android",rules:[{mdmh:"LGE/*/LGMS323/*"},{ua:"LGMS323"}],dpi:[206.6,204.6],bw:3,ac:1e3},{type:"android",rules:[{mdmh:"LGE/*/LGLS996/*"},{ua:"LGLS996"}],dpi:[403.4,401.5],bw:3,ac:1e3},{type:"android",rules:[{mdmh:"Micromax/*/4560MMX/*"},{ua:"4560MMX"}],dpi:[240,219.4],bw:3,ac:1e3},{type:"android",rules:[{mdmh:"Micromax/*/A250/*"},{ua:"Micromax A250"}],dpi:[480,446.4],bw:3,ac:1e3},{type:"android",rules:[{mdmh:"Micromax/*/Micromax AQ4501/*"},{ua:"Micromax AQ4501"}],dpi:240,bw:3,ac:500},{type:"android",rules:[{mdmh:"motorola/*/G5/*"},{ua:"Moto G (5) Plus"}],dpi:[403.4,403],bw:3,ac:1e3},{type:"android",rules:[{mdmh:"motorola/*/DROID RAZR/*"},{ua:"DROID RAZR"}],dpi:[368.1,256.7],bw:3,ac:1e3},{type:"android",rules:[{mdmh:"motorola/*/XT830C/*"},{ua:"XT830C"}],dpi:[254,255.9],bw:3,ac:1e3},{type:"android",rules:[{mdmh:"motorola/*/XT1021/*"},{ua:"XT1021"}],dpi:[254,256.7],bw:3,ac:500},{type:"android",rules:[{mdmh:"motorola/*/XT1023/*"},{ua:"XT1023"}],dpi:[254,256.7],bw:3,ac:500},{type:"android",rules:[{mdmh:"motorola/*/XT1028/*"},{ua:"XT1028"}],dpi:[326.6,327.6],bw:3,ac:1e3},{type:"android",rules:[{mdmh:"motorola/*/XT1034/*"},{ua:"XT1034"}],dpi:[326.6,328.4],bw:3,ac:500},{type:"android",rules:[{mdmh:"motorola/*/XT1053/*"},{ua:"XT1053"}],dpi:[315.3,316.1],bw:3,ac:1e3},{type:"android",rules:[{mdmh:"motorola/*/XT1562/*"},{ua:"XT1562"}],dpi:[403.4,402.7],bw:3,ac:1e3},{type:"android",rules:[{mdmh:"motorola/*/Nexus 6/*"},{ua:"Nexus 6 B"}],dpi:[494.3,489.7],bw:3,ac:1e3},{type:"android",rules:[{mdmh:"motorola/*/XT1063/*"},{ua:"XT1063"}],dpi:[295,296.6],bw:3,ac:1e3},{type:"android",rules:[{mdmh:"motorola/*/XT1064/*"},{ua:"XT1064"}],dpi:[295,295.6],bw:3,ac:500},{type:"android",rules:[{mdmh:"motorola/*/XT1092/*"},{ua:"XT1092"}],dpi:[422,424.1],bw:3,ac:500},{type:"android",rules:[{mdmh:"motorola/*/XT1095/*"},{ua:"XT1095"}],dpi:[422,423.4],bw:3,ac:1e3},{type:"android",rules:[{mdmh:"motorola/*/G4/*"},{ua:"Moto G (4)"}],dpi:401,bw:4,ac:1e3},{type:"android",rules:[{mdmh:"OnePlus/*/A0001/*"},{ua:"A0001"}],dpi:[403.4,401],bw:3,ac:1e3},{type:"android",rules:[{mdmh:"OnePlus/*/ONE E1001/*"},{ua:"ONE E1001"}],dpi:[442.4,441.4],bw:3,ac:1e3},{type:"android",rules:[{mdmh:"OnePlus/*/ONE E1003/*"},{ua:"ONE E1003"}],dpi:[442.4,441.4],bw:3,ac:1e3},{type:"android",rules:[{mdmh:"OnePlus/*/ONE E1005/*"},{ua:"ONE E1005"}],dpi:[442.4,441.4],bw:3,ac:1e3},{type:"android",rules:[{mdmh:"OnePlus/*/ONE A2001/*"},{ua:"ONE A2001"}],dpi:[391.9,405.4],bw:3,ac:1e3},{type:"android",rules:[{mdmh:"OnePlus/*/ONE A2003/*"},{ua:"ONE A2003"}],dpi:[391.9,405.4],bw:3,ac:1e3},{type:"android",rules:[{mdmh:"OnePlus/*/ONE A2005/*"},{ua:"ONE A2005"}],dpi:[391.9,405.4],bw:3,ac:1e3},{type:"android",rules:[{mdmh:"OnePlus/*/ONEPLUS A3000/*"},{ua:"ONEPLUS A3000"}],dpi:401,bw:3,ac:500},{type:"android",rules:[{mdmh:"OnePlus/*/ONEPLUS A3003/*"},{ua:"ONEPLUS A3003"}],dpi:401,bw:3,ac:500},{type:"android",rules:[{mdmh:"OnePlus/*/ONEPLUS A3010/*"},{ua:"ONEPLUS A3010"}],dpi:401,bw:3,ac:500},{type:"android",rules:[{mdmh:"OnePlus/*/ONEPLUS A5000/*"},{ua:"ONEPLUS A5000 "}],dpi:[403.411,399.737],bw:3,ac:1e3},{type:"android",rules:[{mdmh:"OnePlus/*/ONE A5010/*"},{ua:"ONEPLUS A5010"}],dpi:[403,400],bw:2,ac:1e3},{type:"android",rules:[{mdmh:"OnePlus/*/ONEPLUS A6000/*"},{ua:"ONEPLUS A6000"}],dpi:401,bw:3,ac:500},{type:"android",rules:[{mdmh:"OnePlus/*/ONEPLUS A6003/*"},{ua:"ONEPLUS A6003"}],dpi:401,bw:3,ac:500},{type:"android",rules:[{mdmh:"OnePlus/*/ONEPLUS A6010/*"},{ua:"ONEPLUS A6010"}],dpi:401,bw:2,ac:500},{type:"android",rules:[{mdmh:"OnePlus/*/ONEPLUS A6013/*"},{ua:"ONEPLUS A6013"}],dpi:401,bw:2,ac:500},{type:"android",rules:[{mdmh:"OPPO/*/X909/*"},{ua:"X909"}],dpi:[442.4,444.1],bw:3,ac:1e3},{type:"android",rules:[{mdmh:"samsung/*/GT-I9082/*"},{ua:"GT-I9082"}],dpi:[184.7,185.4],bw:3,ac:1e3},{type:"android",rules:[{mdmh:"samsung/*/SM-G360P/*"},{ua:"SM-G360P"}],dpi:[196.7,205.4],bw:3,ac:1e3},{type:"android",rules:[{mdmh:"samsung/*/Nexus S/*"},{ua:"Nexus S"}],dpi:[234.5,229.8],bw:3,ac:1e3},{type:"android",rules:[{mdmh:"samsung/*/GT-I9300/*"},{ua:"GT-I9300"}],dpi:[304.8,303.9],bw:5,ac:500},{type:"android",rules:[{mdmh:"samsung/*/SM-T230NU/*"},{ua:"SM-T230NU"}],dpi:216,bw:3,ac:500},{type:"android",rules:[{mdmh:"samsung/*/SGH-T399/*"},{ua:"SGH-T399"}],dpi:[217.7,231.4],bw:3,ac:1e3},{type:"android",rules:[{mdmh:"samsung/*/SGH-M919/*"},{ua:"SGH-M919"}],dpi:[440.8,437.7],bw:3,ac:1e3},{type:"android",rules:[{mdmh:"samsung/*/SM-N9005/*"},{ua:"SM-N9005"}],dpi:[386.4,387],bw:3,ac:500},{type:"android",rules:[{mdmh:"samsung/*/SAMSUNG-SM-N900A/*"},{ua:"SAMSUNG-SM-N900A"}],dpi:[386.4,387.7],bw:3,ac:1e3},{type:"android",rules:[{mdmh:"samsung/*/GT-I9500/*"},{ua:"GT-I9500"}],dpi:[442.5,443.3],bw:3,ac:500},{type:"android",rules:[{mdmh:"samsung/*/GT-I9505/*"},{ua:"GT-I9505"}],dpi:439.4,bw:4,ac:1e3},{type:"android",rules:[{mdmh:"samsung/*/SM-G900F/*"},{ua:"SM-G900F"}],dpi:[415.6,431.6],bw:5,ac:1e3},{type:"android",rules:[{mdmh:"samsung/*/SM-G900M/*"},{ua:"SM-G900M"}],dpi:[415.6,431.6],bw:5,ac:1e3},{type:"android",rules:[{mdmh:"samsung/*/SM-G800F/*"},{ua:"SM-G800F"}],dpi:326.8,bw:3,ac:1e3},{type:"android",rules:[{mdmh:"samsung/*/SM-G906S/*"},{ua:"SM-G906S"}],dpi:[562.7,572.4],bw:3,ac:1e3},{type:"android",rules:[{mdmh:"samsung/*/GT-I9300/*"},{ua:"GT-I9300"}],dpi:[306.7,304.8],bw:5,ac:1e3},{type:"android",rules:[{mdmh:"samsung/*/SM-T535/*"},{ua:"SM-T535"}],dpi:[142.6,136.4],bw:3,ac:500},{type:"android",rules:[{mdmh:"samsung/*/SM-N920C/*"},{ua:"SM-N920C"}],dpi:[515.1,518.4],bw:3,ac:1e3},{type:"android",rules:[{mdmh:"samsung/*/SM-N920P/*"},{ua:"SM-N920P"}],dpi:[386.3655,390.144],bw:3,ac:1e3},{type:"android",rules:[{mdmh:"samsung/*/SM-N920W8/*"},{ua:"SM-N920W8"}],dpi:[515.1,518.4],bw:3,ac:1e3},{type:"android",rules:[{mdmh:"samsung/*/GT-I9300I/*"},{ua:"GT-I9300I"}],dpi:[304.8,305.8],bw:3,ac:1e3},{type:"android",rules:[{mdmh:"samsung/*/GT-I9195/*"},{ua:"GT-I9195"}],dpi:[249.4,256.7],bw:3,ac:500},{type:"android",rules:[{mdmh:"samsung/*/SPH-L520/*"},{ua:"SPH-L520"}],dpi:[249.4,255.9],bw:3,ac:1e3},{type:"android",rules:[{mdmh:"samsung/*/SAMSUNG-SGH-I717/*"},{ua:"SAMSUNG-SGH-I717"}],dpi:285.8,bw:3,ac:1e3},{type:"android",rules:[{mdmh:"samsung/*/SPH-D710/*"},{ua:"SPH-D710"}],dpi:[217.7,204.2],bw:3,ac:1e3},{type:"android",rules:[{mdmh:"samsung/*/GT-N7100/*"},{ua:"GT-N7100"}],dpi:265.1,bw:3,ac:1e3},{type:"android",rules:[{mdmh:"samsung/*/SCH-I605/*"},{ua:"SCH-I605"}],dpi:265.1,bw:3,ac:1e3},{type:"android",rules:[{mdmh:"samsung/*/Galaxy Nexus/*"},{ua:"Galaxy Nexus"}],dpi:[315.3,314.2],bw:3,ac:1e3},{type:"android",rules:[{mdmh:"samsung/*/SM-N910H/*"},{ua:"SM-N910H"}],dpi:[515.1,518],bw:3,ac:1e3},{type:"android",rules:[{mdmh:"samsung/*/SM-N910C/*"},{ua:"SM-N910C"}],dpi:[515.2,520.2],bw:3,ac:500},{type:"android",rules:[{mdmh:"samsung/*/SM-G130M/*"},{ua:"SM-G130M"}],dpi:[165.9,164.8],bw:3,ac:500},{type:"android",rules:[{mdmh:"samsung/*/SM-G928I/*"},{ua:"SM-G928I"}],dpi:[515.1,518.4],bw:3,ac:1e3},{type:"android",rules:[{mdmh:"samsung/*/SM-G920F/*"},{ua:"SM-G920F"}],dpi:580.6,bw:3,ac:500},{type:"android",rules:[{mdmh:"samsung/*/SM-G920P/*"},{ua:"SM-G920P"}],dpi:[522.5,577],bw:3,ac:1e3},{type:"android",rules:[{mdmh:"samsung/*/SM-G925F/*"},{ua:"SM-G925F"}],dpi:580.6,bw:3,ac:500},{type:"android",rules:[{mdmh:"samsung/*/SM-G925V/*"},{ua:"SM-G925V"}],dpi:[522.5,576.6],bw:3,ac:1e3},{type:"android",rules:[{mdmh:"samsung/*/SM-G930F/*"},{ua:"SM-G930F"}],dpi:576.6,bw:3,ac:1e3},{type:"android",rules:[{mdmh:"samsung/*/SM-G935F/*"},{ua:"SM-G935F"}],dpi:533,bw:3,ac:500},{type:"android",rules:[{mdmh:"samsung/*/SM-G950F/*"},{ua:"SM-G950F"}],dpi:[562.707,565.293],bw:3,ac:500},{type:"android",rules:[{mdmh:"samsung/*/SM-G955U/*"},{ua:"SM-G955U"}],dpi:[522.514,525.762],bw:3,ac:500},{type:"android",rules:[{mdmh:"samsung/*/SM-G955F/*"},{ua:"SM-G955F"}],dpi:[522.514,525.762],bw:3,ac:500},{type:"android",rules:[{mdmh:"samsung/*/SM-G960F/*"},{ua:"SM-G960F"}],dpi:[569.575,571.5],bw:3,ac:1e3},{type:"android",rules:[{mdmh:"samsung/*/SM-G9600/*"},{ua:"SM-G9600"}],dpi:[569.575,571.5],bw:3,ac:1e3},{type:"android",rules:[{mdmh:"samsung/*/SM-G960T/*"},{ua:"SM-G960T"}],dpi:[569.575,571.5],bw:3,ac:1e3},{type:"android",rules:[{mdmh:"samsung/*/SM-G960N/*"},{ua:"SM-G960N"}],dpi:[569.575,571.5],bw:3,ac:1e3},{type:"android",rules:[{mdmh:"samsung/*/SM-G960U/*"},{ua:"SM-G960U"}],dpi:[569.575,571.5],bw:3,ac:1e3},{type:"android",rules:[{mdmh:"samsung/*/SM-G9608/*"},{ua:"SM-G9608"}],dpi:[569.575,571.5],bw:3,ac:1e3},{type:"android",rules:[{mdmh:"samsung/*/SM-G960FD/*"},{ua:"SM-G960FD"}],dpi:[569.575,571.5],bw:3,ac:1e3},{type:"android",rules:[{mdmh:"samsung/*/SM-G960W/*"},{ua:"SM-G960W"}],dpi:[569.575,571.5],bw:3,ac:1e3},{type:"android",rules:[{mdmh:"samsung/*/SM-G965F/*"},{ua:"SM-G965F"}],dpi:529,bw:2,ac:1e3},{type:"android",rules:[{mdmh:"Sony/*/C6903/*"},{ua:"C6903"}],dpi:[442.5,443.3],bw:3,ac:500},{type:"android",rules:[{mdmh:"Sony/*/D6653/*"},{ua:"D6653"}],dpi:[428.6,427.6],bw:3,ac:1e3},{type:"android",rules:[{mdmh:"Sony/*/E6653/*"},{ua:"E6653"}],dpi:[428.6,425.7],bw:3,ac:1e3},{type:"android",rules:[{mdmh:"Sony/*/E6853/*"},{ua:"E6853"}],dpi:[403.4,401.9],bw:3,ac:1e3},{type:"android",rules:[{mdmh:"Sony/*/SGP321/*"},{ua:"SGP321"}],dpi:[224.7,224.1],bw:3,ac:500},{type:"android",rules:[{mdmh:"TCT/*/ALCATEL ONE TOUCH Fierce/*"},{ua:"ALCATEL ONE TOUCH Fierce"}],dpi:[240,247.5],bw:3,ac:1e3},{type:"android",rules:[{mdmh:"THL/*/thl 5000/*"},{ua:"thl 5000"}],dpi:[480,443.3],bw:3,ac:1e3},{type:"android",rules:[{mdmh:"Fly/*/IQ4412/*"},{ua:"IQ4412"}],dpi:307.9,bw:3,ac:1e3},{type:"android",rules:[{mdmh:"ZTE/*/ZTE Blade L2/*"},{ua:"ZTE Blade L2"}],dpi:240,bw:3,ac:500},{type:"android",rules:[{mdmh:"BENEVE/*/VR518/*"},{ua:"VR518"}],dpi:480,bw:3,ac:500},{type:"ios",rules:[{res:[640,960]}],dpi:[325.1,328.4],bw:4,ac:1e3},{type:"ios",rules:[{res:[640,1136]}],dpi:[317.1,320.2],bw:3,ac:1e3},{type:"ios",rules:[{res:[750,1334]}],dpi:326.4,bw:4,ac:1e3},{type:"ios",rules:[{res:[1242,2208]}],dpi:[453.6,458.4],bw:4,ac:1e3},{type:"ios",rules:[{res:[1125,2001]}],dpi:[410.9,415.4],bw:4,ac:1e3},{type:"ios",rules:[{res:[1125,2436]}],dpi:458,bw:4,ac:1e3},{type:"android",rules:[{mdmh:"Huawei/*/EML-L29/*"},{ua:"EML-L29"}],dpi:428,bw:3.45,ac:500},{type:"android",rules:[{mdmh:"Nokia/*/Nokia 7.1/*"},{ua:"Nokia 7.1"}],dpi:[432,431.9],bw:3,ac:500},{type:"ios",rules:[{res:[1242,2688]}],dpi:458,bw:4,ac:1e3},{type:"android",rules:[{mdmh:"samsung/*/SM-G570M/*"},{ua:"SM-G570M"}],dpi:320,bw:3.684,ac:1e3},{type:"android",rules:[{mdmh:"samsung/*/SM-G970F/*"},{ua:"SM-G970F"}],dpi:438,bw:2.281,ac:500},{type:"android",rules:[{mdmh:"samsung/*/SM-G973F/*"},{ua:"SM-G973F"}],dpi:550,bw:2.002,ac:500},{type:"android",rules:[{mdmh:"samsung/*/SM-G975F/*"},{ua:"SM-G975F"}],dpi:522,bw:2.054,ac:500},{type:"android",rules:[{mdmh:"samsung/*/SM-G977F/*"},{ua:"SM-G977F"}],dpi:505,bw:2.334,ac:500},{type:"ios",rules:[{res:[828,1792]}],dpi:326,bw:5,ac:500}]};function J(e,t){if(this.dpdb=Z,this.recalculateDeviceParams_(),e){this.onDeviceParamsUpdated=t;var i=new XMLHttpRequest,r=this;i.open("GET",e,!0),i.addEventListener("load",function(){r.loading=!1,i.status>=200&&i.status<=299?(r.dpdb=JSON.parse(i.response),r.recalculateDeviceParams_()):console.error("Error loading online DPDB!")}),i.send()}}function K(e){this.xdpi=e.xdpi,this.ydpi=e.ydpi,this.bevelMm=e.bevelMm}function $(e,t){this.set(e,t)}function ee(e,t){this.kFilter=e,this.isDebug=t,this.currentAccelMeasurement=new $,this.currentGyroMeasurement=new $,this.previousGyroMeasurement=new $,o()?this.filterQ=new U(-1,0,0,1):this.filterQ=new U(1,0,0,1),this.previousFilterQ=new U,this.previousFilterQ.copy(this.filterQ),this.accelQ=new U,this.isOrientationInitialized=!1,this.estimatedGravity=new k,this.measuredGravity=new k,this.gyroIntegralQ=new U}function te(e,t){this.predictionTimeS=e,this.isDebug=t,this.previousQ=new U,this.previousTimestampS=null,this.deltaQ=new U,this.outQ=new U}function ie(e,t,i,r){this.yawOnly=i,this.accelerometer=new k,this.gyroscope=new k,this.filter=new ee(e,r),this.posePredictor=new te(t,r),this.isFirefoxAndroid=h(),this.isIOS=o();var s=c();this.isDeviceMotionInRadians=!this.isIOS&&s&&s<66,this.isWithoutDeviceMotion=u()||d(),this.filterToWorldQ=new U,o()?this.filterToWorldQ.setFromAxisAngle(new k(1,0,0),Math.PI/2):this.filterToWorldQ.setFromAxisAngle(new k(1,0,0),-Math.PI/2),this.inverseWorldToScreenQ=new U,this.worldToScreenQ=new U,this.originalPoseAdjustQ=new U,this.originalPoseAdjustQ.setFromAxisAngle(new k(0,0,1),-window.orientation*Math.PI/180),this.setScreenTransform_(),f()&&this.filterToWorldQ.multiply(this.inverseWorldToScreenQ),this.resetQ=new U,this.orientationOut_=new Float32Array(4),this.start()}J.prototype.getDeviceParams=function(){return this.deviceParams},J.prototype.recalculateDeviceParams_=function(){var e=this.calcDeviceParams_();e?(this.deviceParams=e,this.onDeviceParamsUpdated&&this.onDeviceParamsUpdated(this.deviceParams)):console.error("Failed to recalculate device parameters.")},J.prototype.calcDeviceParams_=function(){var e=this.dpdb;if(!e)return console.error("DPDB not available."),null;if(1!=e.format)return console.error("DPDB has unexpected format version."),null;if(!e.devices||!e.devices.length)return console.error("DPDB does not have a devices section."),null;var t=navigator.userAgent||navigator.vendor||window.opera,i=m(),r=g();if(!e.devices)return console.error("DPDB has no devices section."),null;for(var s=0;s<e.devices.length;s++){var n=e.devices[s];if(n.rules)if("ios"==n.type||"android"==n.type){if(o()==("ios"==n.type)){for(var a=!1,l=0;l<n.rules.length;l++){var A=n.rules[l];if(this.ruleMatches_(A,t,i,r)){a=!0;break}}if(a){var h=n.dpi[0]||n.dpi,c=n.dpi[1]||n.dpi;return new K({xdpi:h,ydpi:c,bevelMm:n.bw})}}}else console.warn("Device["+s+"] has invalid type.");else console.warn("Device["+s+"] has no rules section.")}return console.warn("No DPDB device match."),null},J.prototype.ruleMatches_=function(e,t,i,r){if(!e.ua&&!e.res)return!1;if(e.ua&&"SM"===e.ua.substring(0,2)&&(e.ua=e.ua.substring(0,7)),e.ua&&t.indexOf(e.ua)<0)return!1;if(e.res){if(!e.res[0]||!e.res[1])return!1;var s=e.res[0],n=e.res[1];if(Math.min(i,r)!=Math.min(s,n)||Math.max(i,r)!=Math.max(s,n))return!1}return!0},$.prototype.set=function(e,t){this.sample=e,this.timestampS=t},$.prototype.copy=function(e){this.set(e.sample,e.timestampS)},ee.prototype.addAccelMeasurement=function(e,t){this.currentAccelMeasurement.set(e,t)},ee.prototype.addGyroMeasurement=function(e,t){this.currentGyroMeasurement.set(e,t);var i,r=t-this.previousGyroMeasurement.timestampS;i=r,!(isNaN(i)||i<=.001||i>1)&&this.run_(),this.previousGyroMeasurement.copy(this.currentGyroMeasurement)},ee.prototype.run_=function(){if(!this.isOrientationInitialized)return this.accelQ=this.accelToQuaternion_(this.currentAccelMeasurement.sample),this.previousFilterQ.copy(this.accelQ),void(this.isOrientationInitialized=!0);var e=this.currentGyroMeasurement.timestampS-this.previousGyroMeasurement.timestampS,t=this.gyroToQuaternionDelta_(this.currentGyroMeasurement.sample,e);this.gyroIntegralQ.multiply(t),this.filterQ.copy(this.previousFilterQ),this.filterQ.multiply(t);var i=new U;i.copy(this.filterQ),i.inverse(),this.estimatedGravity.set(0,0,-1),this.estimatedGravity.applyQuaternion(i),this.estimatedGravity.normalize(),this.measuredGravity.copy(this.currentAccelMeasurement.sample),this.measuredGravity.normalize();var r,s=new U;s.setFromUnitVectors(this.estimatedGravity,this.measuredGravity),s.inverse(),this.isDebug&&console.log("Delta: %d deg, G_est: (%s, %s, %s), G_meas: (%s, %s, %s)",Q*((r=s).w>1?(console.warn("getQuaternionAngle: w > 1"),0):2*Math.acos(r.w)),this.estimatedGravity.x.toFixed(1),this.estimatedGravity.y.toFixed(1),this.estimatedGravity.z.toFixed(1),this.measuredGravity.x.toFixed(1),this.measuredGravity.y.toFixed(1),this.measuredGravity.z.toFixed(1));var n=new U;n.copy(this.filterQ),n.multiply(s),this.filterQ.slerp(n,1-this.kFilter),this.previousFilterQ.copy(this.filterQ)},ee.prototype.getOrientation=function(){return this.filterQ},ee.prototype.accelToQuaternion_=function(e){var t=new k;t.copy(e),t.normalize();var i=new U;return i.setFromUnitVectors(new k(0,0,-1),t),i.inverse(),i},ee.prototype.gyroToQuaternionDelta_=function(e,t){var i=new U,r=new k;return r.copy(e),r.normalize(),i.setFromAxisAngle(r,e.length()*t),i},te.prototype.getPrediction=function(e,t,i){if(!this.previousTimestampS)return this.previousQ.copy(e),this.previousTimestampS=i,e;var r=new k;r.copy(t),r.normalize();var s=t.length();if(s<20*G)return this.isDebug&&console.log("Moving slowly, at %s deg/s: no prediction",(Q*s).toFixed(1)),this.outQ.copy(e),this.previousQ.copy(e),this.outQ;var n=s*this.predictionTimeS;return this.deltaQ.setFromAxisAngle(r,n),this.outQ.copy(this.previousQ),this.outQ.multiply(this.deltaQ),this.previousQ.copy(e),this.previousTimestampS=i,this.outQ},ie.prototype.getPosition=function(){return null},ie.prototype.getOrientation=function(){var e=void 0;if(this.isWithoutDeviceMotion&&this._deviceOrientationQ){this.deviceOrientationFixQ=this.deviceOrientationFixQ||(r=(new U).setFromAxisAngle(new k(0,0,-1),0),s=new U,-90===window.orientation?s.setFromAxisAngle(new k(0,1,0),Math.PI/-2):s.setFromAxisAngle(new k(0,1,0),Math.PI/2),r.multiply(s)),this.deviceOrientationFilterToWorldQ=this.deviceOrientationFilterToWorldQ||((i=new U).setFromAxisAngle(new k(1,0,0),-Math.PI/2),i),e=this._deviceOrientationQ;var t=new U;return t.copy(e),t.multiply(this.deviceOrientationFilterToWorldQ),t.multiply(this.resetQ),t.multiply(this.worldToScreenQ),t.multiplyQuaternions(this.deviceOrientationFixQ,t),this.yawOnly&&(t.x=0,t.z=0,t.normalize()),this.orientationOut_[0]=t.x,this.orientationOut_[1]=t.y,this.orientationOut_[2]=t.z,this.orientationOut_[3]=t.w,this.orientationOut_}var i,r,s,n=this.filter.getOrientation();e=this.posePredictor.getPrediction(n,this.gyroscope,this.previousTimestampS);var t=new U;return t.copy(this.filterToWorldQ),t.multiply(this.resetQ),t.multiply(e),t.multiply(this.worldToScreenQ),this.yawOnly&&(t.x=0,t.z=0,t.normalize()),this.orientationOut_[0]=t.x,this.orientationOut_[1]=t.y,this.orientationOut_[2]=t.z,this.orientationOut_[3]=t.w,this.orientationOut_},ie.prototype.resetPose=function(){this.resetQ.copy(this.filter.getOrientation()),this.resetQ.x=0,this.resetQ.y=0,this.resetQ.z*=-1,this.resetQ.normalize(),f()&&this.resetQ.multiply(this.inverseWorldToScreenQ),this.resetQ.multiply(this.originalPoseAdjustQ)},ie.prototype.onDeviceOrientation_=function(e){this._deviceOrientationQ=this._deviceOrientationQ||new U;var t=e.alpha,i=e.beta,r=e.gamma;t=(t||0)*Math.PI/180,i=(i||0)*Math.PI/180,r=(r||0)*Math.PI/180,this._deviceOrientationQ.setFromEulerYXZ(i,t,-r)},ie.prototype.onDeviceMotion_=function(e){this.updateDeviceMotion_(e)},ie.prototype.updateDeviceMotion_=function(e){var t=e.accelerationIncludingGravity,i=e.rotationRate,r=e.timeStamp/1e3,s=r-this.previousTimestampS;return s<0?(_("fusion-pose-sensor:invalid:non-monotonic","Invalid timestamps detected: non-monotonic timestamp from devicemotion"),void(this.previousTimestampS=r)):s<=.001||s>1?(_("fusion-pose-sensor:invalid:outside-threshold","Invalid timestamps detected: Timestamp from devicemotion outside expected range."),void(this.previousTimestampS=r)):(this.accelerometer.set(-t.x,-t.y,-t.z),i&&(p()?this.gyroscope.set(-i.beta,i.alpha,i.gamma):this.gyroscope.set(i.alpha,i.beta,i.gamma),this.isDeviceMotionInRadians||this.gyroscope.multiplyScalar(Math.PI/180),this.filter.addGyroMeasurement(this.gyroscope,r)),this.filter.addAccelMeasurement(this.accelerometer,r),void(this.previousTimestampS=r))},ie.prototype.onOrientationChange_=function(e){this.setScreenTransform_()},ie.prototype.onMessage_=function(e){var t=e.data;if(t&&t.type){var i=t.type.toLowerCase();"devicemotion"===i&&this.updateDeviceMotion_(t.deviceMotionEvent)}},ie.prototype.setScreenTransform_=function(){switch(this.worldToScreenQ.set(0,0,0,1),window.orientation){case 0:break;case 90:this.worldToScreenQ.setFromAxisAngle(new k(0,0,1),-Math.PI/2);break;case-90:this.worldToScreenQ.setFromAxisAngle(new k(0,0,1),Math.PI/2)}this.inverseWorldToScreenQ.copy(this.worldToScreenQ),this.inverseWorldToScreenQ.inverse()},ie.prototype.start=function(){var e,t,i;this.onDeviceMotionCallback_=this.onDeviceMotion_.bind(this),this.onOrientationChangeCallback_=this.onOrientationChange_.bind(this),this.onMessageCallback_=this.onMessage_.bind(this),this.onDeviceOrientationCallback_=this.onDeviceOrientation_.bind(this),o()&&(e=window.self!==window.top,t=x(document.referrer),i=x(window.location.href),e&&t!==i)&&window.addEventListener("message",this.onMessageCallback_),window.addEventListener("orientationchange",this.onOrientationChangeCallback_),this.isWithoutDeviceMotion?window.addEventListener("deviceorientation",this.onDeviceOrientationCallback_):window.addEventListener("devicemotion",this.onDeviceMotionCallback_)},ie.prototype.stop=function(){window.removeEventListener("devicemotion",this.onDeviceMotionCallback_),window.removeEventListener("deviceorientation",this.onDeviceOrientationCallback_),window.removeEventListener("orientationchange",this.onOrientationChangeCallback_),window.removeEventListener("message",this.onMessageCallback_)};var re=new k(1,0,0),se=new k(0,0,1),ne=new U;ne.setFromAxisAngle(re,-Math.PI/2),ne.multiply((new U).setFromAxisAngle(se,Math.PI/2));var ae=function(){function e(t){r(this,e),this.config=t,this.sensor=null,this.fusionSensor=null,this._out=new Float32Array(4),this.api=null,this.errors=[],this._sensorQ=new U,this._outQ=new U,this._onSensorRead=this._onSensorRead.bind(this),this._onSensorError=this._onSensorError.bind(this),this.init()}return s(e,[{key:"init",value:function(){var e=null;try{(e=new RelativeOrientationSensor({frequency:60,referenceFrame:"screen"})).addEventListener("error",this._onSensorError)}catch(e){this.errors.push(e),"SecurityError"===e.name?(console.error("Cannot construct sensors due to the Feature Policy"),console.warn('Attempting to fall back using "devicemotion"; however this will fail in the future without correct permissions.'),this.useDeviceMotion()):"ReferenceError"===e.name?this.useDeviceMotion():console.error(e)}e&&(this.api="sensor",this.sensor=e,this.sensor.addEventListener("reading",this._onSensorRead),this.sensor.start())}},{key:"useDeviceMotion",value:function(){this.api="devicemotion",this.fusionSensor=new ie(this.config.K_FILTER,this.config.PREDICTION_TIME_S,this.config.YAW_ONLY,this.config.DEBUG),this.sensor&&(this.sensor.removeEventListener("reading",this._onSensorRead),this.sensor.removeEventListener("error",this._onSensorError),this.sensor=null)}},{key:"getOrientation",value:function(){if(this.fusionSensor)return this.fusionSensor.getOrientation();if(!this.sensor||!this.sensor.quaternion)return this._out[0]=this._out[1]=this._out[2]=0,this._out[3]=1,this._out;var e=this.sensor.quaternion;this._sensorQ.set(e[0],e[1],e[2],e[3]);var t=this._outQ;return t.copy(ne),t.multiply(this._sensorQ),this.config.YAW_ONLY&&(t.x=t.z=0,t.normalize()),this._out[0]=t.x,this._out[1]=t.y,this._out[2]=t.z,this._out[3]=t.w,this._out}},{key:"_onSensorError",value:function(e){this.errors.push(e.error),"NotAllowedError"===e.error.name?console.error("Permission to access sensor was denied"):"NotReadableError"===e.error.name?console.error("Sensor could not be read"):console.error(e.error),this.useDeviceMotion()}},{key:"_onSensorRead",value:function(){}}]),e}();function oe(){this.loadIcon_();var e=document.createElement("div"),t=e.style;t.position="fixed",t.top=0,t.right=0,t.bottom=0,t.left=0,t.backgroundColor="gray",t.fontFamily="sans-serif",t.zIndex=1e6;var i=document.createElement("img");i.src=this.icon;var t=i.style;t.marginLeft="25%",t.marginTop="25%",t.width="50%",e.appendChild(i);var r=document.createElement("div"),t=r.style;t.textAlign="center",t.fontSize="16px",t.lineHeight="24px",t.margin="24px 25%",t.width="50%",r.innerHTML="Place your phone into your Cardboard viewer.",e.appendChild(r);var s=document.createElement("div"),t=s.style;t.backgroundColor="#CFD8DC",t.position="fixed",t.bottom=0,t.width="100%",t.height="48px",t.padding="14px 24px",t.boxSizing="border-box",t.color="#656A6B",e.appendChild(s);var n=document.createElement("div");n.style.float="left",n.innerHTML="No Cardboard viewer?";var a=document.createElement("a");a.href="https://www.google.com/get/cardboard/get-cardboard/",a.innerHTML="get one",a.target="_blank";var t=a.style;t.float="right",t.fontWeight=600,t.textTransform="uppercase",t.borderLeft="1px solid gray",t.paddingLeft="24px",t.textDecoration="none",t.color="#656A6B",s.appendChild(n),s.appendChild(a),this.overlay=e,this.text=r,this.hide()}oe.prototype.show=function(e){e||this.overlay.parentElement?e&&(this.overlay.parentElement&&this.overlay.parentElement!=e&&this.overlay.parentElement.removeChild(this.overlay),e.appendChild(this.overlay)):document.body.appendChild(this.overlay),this.overlay.style.display="block";var t=this.overlay.querySelector("img"),i=t.style;f()?(i.width="20%",i.marginLeft="40%",i.marginTop="3%"):(i.width="50%",i.marginLeft="25%",i.marginTop="25%")},oe.prototype.hide=function(){this.overlay.style.display="none"},oe.prototype.showTemporarily=function(e,t){this.show(t),this.timer=setTimeout(this.hide.bind(this),e)},oe.prototype.disableShowTemporarily=function(){clearTimeout(this.timer)},oe.prototype.update=function(){this.disableShowTemporarily(),!f()&&b()?this.show():this.hide()},oe.prototype.loadIcon_=function(){this.icon="data:image/svg+xml,"+encodeURIComponent("<svg width='198' height='240' viewBox='0 0 198 240' xmlns='http://www.w3.org/2000/svg'><g fill='none' fill-rule='evenodd'><path d='M149.625 109.527l6.737 3.891v.886c0 .177.013.36.038.549.01.081.02.162.027.242.14 1.415.974 2.998 2.105 3.999l5.72 5.062.081-.09s4.382-2.53 5.235-3.024l25.97 14.993v54.001c0 .771-.386 1.217-.948 1.217-.233 0-.495-.076-.772-.236l-23.967-13.838-.014.024-27.322 15.775-.85-1.323c-4.731-1.529-9.748-2.74-14.951-3.61a.27.27 0 0 0-.007.024l-5.067 16.961-7.891 4.556-.037-.063v27.59c0 .772-.386 1.217-.948 1.217-.232 0-.495-.076-.772-.236l-42.473-24.522c-.95-.549-1.72-1.877-1.72-2.967v-1.035l-.021.047a5.111 5.111 0 0 0-1.816-.399 5.682 5.682 0 0 0-.546.001 13.724 13.724 0 0 1-1.918-.041c-1.655-.153-3.2-.6-4.404-1.296l-46.576-26.89.005.012-10.278-18.75c-1.001-1.827-.241-4.216 1.698-5.336l56.011-32.345a4.194 4.194 0 0 1 2.099-.572c1.326 0 2.572.659 3.227 1.853l.005-.003.227.413-.006.004a9.63 9.63 0 0 0 1.477 2.018l.277.27c1.914 1.85 4.468 2.801 7.113 2.801 1.949 0 3.948-.517 5.775-1.572.013 0 7.319-4.219 7.319-4.219a4.194 4.194 0 0 1 2.099-.572c1.326 0 2.572.658 3.226 1.853l3.25 5.928.022-.018 6.785 3.917-.105-.182 46.881-26.965m0-1.635c-.282 0-.563.073-.815.218l-46.169 26.556-5.41-3.124-3.005-5.481c-.913-1.667-2.699-2.702-4.66-2.703-1.011 0-2.02.274-2.917.792a3825 3825 0 0 1-7.275 4.195l-.044.024a9.937 9.937 0 0 1-4.957 1.353c-2.292 0-4.414-.832-5.976-2.342l-.252-.245a7.992 7.992 0 0 1-1.139-1.534 1.379 1.379 0 0 0-.06-.122l-.227-.414a1.718 1.718 0 0 0-.095-.154c-.938-1.574-2.673-2.545-4.571-2.545-1.011 0-2.02.274-2.917.792L3.125 155.502c-2.699 1.559-3.738 4.94-2.314 7.538l10.278 18.75c.177.323.448.563.761.704l46.426 26.804c1.403.81 3.157 1.332 5.072 1.508a15.661 15.661 0 0 0 2.146.046 4.766 4.766 0 0 1 .396 0c.096.004.19.011.283.022.109 1.593 1.159 3.323 2.529 4.114l42.472 24.522c.524.302 1.058.455 1.59.455 1.497 0 2.583-1.2 2.583-2.852v-26.562l7.111-4.105a1.64 1.64 0 0 0 .749-.948l4.658-15.593c4.414.797 8.692 1.848 12.742 3.128l.533.829a1.634 1.634 0 0 0 2.193.531l26.532-15.317L193 192.433c.523.302 1.058.455 1.59.455 1.497 0 2.583-1.199 2.583-2.852v-54.001c0-.584-.312-1.124-.818-1.416l-25.97-14.993a1.633 1.633 0 0 0-1.636.001c-.606.351-2.993 1.73-4.325 2.498l-4.809-4.255c-.819-.725-1.461-1.933-1.561-2.936a7.776 7.776 0 0 0-.033-.294 2.487 2.487 0 0 1-.023-.336v-.886c0-.584-.312-1.123-.817-1.416l-6.739-3.891a1.633 1.633 0 0 0-.817-.219' fill='#455A64'/><path d='M96.027 132.636l46.576 26.891c1.204.695 1.979 1.587 2.242 2.541l-.01.007-81.374 46.982h-.001c-1.654-.152-3.199-.6-4.403-1.295l-46.576-26.891 83.546-48.235' fill='#FAFAFA'/><path d='M63.461 209.174c-.008 0-.015 0-.022-.002-1.693-.156-3.228-.609-4.441-1.309l-46.576-26.89a.118.118 0 0 1 0-.203l83.546-48.235a.117.117 0 0 1 .117 0l46.576 26.891c1.227.708 2.021 1.612 2.296 2.611a.116.116 0 0 1-.042.124l-.021.016-81.375 46.981a.11.11 0 0 1-.058.016zm-50.747-28.303l46.401 26.79c1.178.68 2.671 1.121 4.32 1.276l81.272-46.922c-.279-.907-1.025-1.73-2.163-2.387l-46.517-26.857-83.313 48.1z' fill='#607D8B'/><path d='M148.327 165.471a5.85 5.85 0 0 1-.546.001c-1.894-.083-3.302-1.038-3.145-2.132a2.693 2.693 0 0 0-.072-1.105l-81.103 46.822c.628.058 1.272.073 1.918.042.182-.009.364-.009.546-.001 1.894.083 3.302 1.038 3.145 2.132l79.257-45.759' fill='#FFF'/><path d='M69.07 211.347a.118.118 0 0 1-.115-.134c.045-.317-.057-.637-.297-.925-.505-.61-1.555-1.022-2.738-1.074a5.966 5.966 0 0 0-.535.001 14.03 14.03 0 0 1-1.935-.041.117.117 0 0 1-.103-.092.116.116 0 0 1 .055-.126l81.104-46.822a.117.117 0 0 1 .171.07c.104.381.129.768.074 1.153-.045.316.057.637.296.925.506.61 1.555 1.021 2.739 1.073.178.008.357.008.535-.001a.117.117 0 0 1 .064.218l-79.256 45.759a.114.114 0 0 1-.059.016zm-3.405-2.372c.089 0 .177.002.265.006 1.266.056 2.353.488 2.908 1.158.227.274.35.575.36.882l78.685-45.429c-.036 0-.072-.001-.107-.003-1.267-.056-2.354-.489-2.909-1.158-.282-.34-.402-.724-.347-1.107a2.604 2.604 0 0 0-.032-.91L63.846 208.97a13.91 13.91 0 0 0 1.528.012c.097-.005.194-.007.291-.007z' fill='#607D8B'/><path d='M2.208 162.134c-1.001-1.827-.241-4.217 1.698-5.337l56.011-32.344c1.939-1.12 4.324-.546 5.326 1.281l.232.41a9.344 9.344 0 0 0 1.47 2.021l.278.27c3.325 3.214 8.583 3.716 12.888 1.23l7.319-4.22c1.94-1.119 4.324-.546 5.325 1.282l3.25 5.928-83.519 48.229-10.278-18.75z' fill='#FAFAFA'/><path d='M12.486 181.001a.112.112 0 0 1-.031-.005.114.114 0 0 1-.071-.056L2.106 162.19c-1.031-1.88-.249-4.345 1.742-5.494l56.01-32.344a4.328 4.328 0 0 1 2.158-.588c1.415 0 2.65.702 3.311 1.882.01.008.018.017.024.028l.227.414a.122.122 0 0 1 .013.038 9.508 9.508 0 0 0 1.439 1.959l.275.266c1.846 1.786 4.344 2.769 7.031 2.769 1.977 0 3.954-.538 5.717-1.557a.148.148 0 0 1 .035-.013l7.284-4.206a4.321 4.321 0 0 1 2.157-.588c1.427 0 2.672.716 3.329 1.914l3.249 5.929a.116.116 0 0 1-.044.157l-83.518 48.229a.116.116 0 0 1-.059.016zm49.53-57.004c-.704 0-1.41.193-2.041.557l-56.01 32.345c-1.882 1.086-2.624 3.409-1.655 5.179l10.221 18.645 83.317-48.112-3.195-5.829c-.615-1.122-1.783-1.792-3.124-1.792a4.08 4.08 0 0 0-2.04.557l-7.317 4.225a.148.148 0 0 1-.035.013 11.7 11.7 0 0 1-5.801 1.569c-2.748 0-5.303-1.007-7.194-2.835l-.278-.27a9.716 9.716 0 0 1-1.497-2.046.096.096 0 0 1-.013-.037l-.191-.347a.11.11 0 0 1-.023-.029c-.615-1.123-1.783-1.793-3.124-1.793z' fill='#607D8B'/><path d='M42.434 155.808c-2.51-.001-4.697-1.258-5.852-3.365-1.811-3.304-.438-7.634 3.059-9.654l12.291-7.098a7.599 7.599 0 0 1 3.789-1.033c2.51 0 4.697 1.258 5.852 3.365 1.811 3.304.439 7.634-3.059 9.654l-12.291 7.098a7.606 7.606 0 0 1-3.789 1.033zm13.287-20.683a7.128 7.128 0 0 0-3.555.971l-12.291 7.098c-3.279 1.893-4.573 5.942-2.883 9.024 1.071 1.955 3.106 3.122 5.442 3.122a7.13 7.13 0 0 0 3.556-.97l12.291-7.098c3.279-1.893 4.572-5.942 2.883-9.024-1.072-1.955-3.106-3.123-5.443-3.123z' fill='#607D8B'/><path d='M149.588 109.407l6.737 3.89v.887c0 .176.013.36.037.549.011.081.02.161.028.242.14 1.415.973 2.998 2.105 3.999l7.396 6.545c.177.156.358.295.541.415 1.579 1.04 2.95.466 3.062-1.282.049-.784.057-1.595.023-2.429l-.003-.16v-1.151l25.987 15.003v54c0 1.09-.77 1.53-1.72.982l-42.473-24.523c-.95-.548-1.72-1.877-1.72-2.966v-34.033' fill='#FAFAFA'/><path d='M194.553 191.25c-.257 0-.54-.085-.831-.253l-42.472-24.521c-.981-.567-1.779-1.943-1.779-3.068v-34.033h.234v34.033c0 1.051.745 2.336 1.661 2.866l42.473 24.521c.424.245.816.288 1.103.122.285-.164.442-.52.442-1.002v-53.933l-25.753-14.868.003 1.106c.034.832.026 1.654-.024 2.439-.054.844-.396 1.464-.963 1.746-.619.309-1.45.173-2.28-.373a5.023 5.023 0 0 1-.553-.426l-7.397-6.544c-1.158-1.026-1.999-2.625-2.143-4.076a9.624 9.624 0 0 0-.027-.238 4.241 4.241 0 0 1-.038-.564v-.82l-6.68-3.856.117-.202 6.738 3.89.058.034v.954c0 .171.012.351.036.533.011.083.021.165.029.246.138 1.395.948 2.935 2.065 3.923l7.397 6.545c.173.153.35.289.527.406.758.499 1.504.63 2.047.359.49-.243.786-.795.834-1.551.05-.778.057-1.591.024-2.417l-.004-.163v-1.355l.175.1 25.987 15.004.059.033v54.068c0 .569-.198.996-.559 1.204a1.002 1.002 0 0 1-.506.131' fill='#607D8B'/><path d='M145.685 163.161l24.115 13.922-25.978 14.998-1.462-.307c-6.534-2.17-13.628-3.728-21.019-4.616-4.365-.524-8.663 1.096-9.598 3.62a2.746 2.746 0 0 0-.011 1.928c1.538 4.267 4.236 8.363 7.995 12.135l.532.845-25.977 14.997-24.115-13.922 75.518-43.6' fill='#FFF'/><path d='M94.282 220.818l-.059-.033-24.29-14.024.175-.101 75.577-43.634.058.033 24.29 14.024-26.191 15.122-.045-.01-1.461-.307c-6.549-2.174-13.613-3.725-21.009-4.614a13.744 13.744 0 0 0-1.638-.097c-3.758 0-7.054 1.531-7.837 3.642a2.62 2.62 0 0 0-.01 1.848c1.535 4.258 4.216 8.326 7.968 12.091l.016.021.526.835.006.01.064.102-.105.061-25.977 14.998-.058.033zm-23.881-14.057l23.881 13.788 24.802-14.32c.546-.315.846-.489 1.017-.575l-.466-.74c-3.771-3.787-6.467-7.881-8.013-12.168a2.851 2.851 0 0 1 .011-2.008c.815-2.199 4.203-3.795 8.056-3.795.557 0 1.117.033 1.666.099 7.412.891 14.491 2.445 21.041 4.621.836.175 1.215.254 1.39.304l25.78-14.884-23.881-13.788-75.284 43.466z' fill='#607D8B'/><path d='M167.23 125.979v50.871l-27.321 15.773-6.461-14.167c-.91-1.996-3.428-1.738-5.624.574a10.238 10.238 0 0 0-2.33 4.018l-6.46 21.628-27.322 15.774v-50.871l75.518-43.6' fill='#FFF'/><path d='M91.712 220.567a.127.127 0 0 1-.059-.016.118.118 0 0 1-.058-.101v-50.871c0-.042.023-.08.058-.101l75.519-43.6a.117.117 0 0 1 .175.101v50.871c0 .041-.023.08-.059.1l-27.321 15.775a.118.118 0 0 1-.094.01.12.12 0 0 1-.071-.063l-6.46-14.168c-.375-.822-1.062-1.275-1.934-1.275-1.089 0-2.364.686-3.5 1.881a10.206 10.206 0 0 0-2.302 3.972l-6.46 21.627a.118.118 0 0 1-.054.068L91.77 220.551a.12.12 0 0 1-.058.016zm.117-50.92v50.601l27.106-15.65 6.447-21.583a10.286 10.286 0 0 1 2.357-4.065c1.18-1.242 2.517-1.954 3.669-1.954.969 0 1.731.501 2.146 1.411l6.407 14.051 27.152-15.676v-50.601l-75.284 43.466z' fill='#607D8B'/><path d='M168.543 126.213v50.87l-27.322 15.774-6.46-14.168c-.91-1.995-3.428-1.738-5.624.574a10.248 10.248 0 0 0-2.33 4.019l-6.461 21.627-27.321 15.774v-50.87l75.518-43.6' fill='#FFF'/><path d='M93.025 220.8a.123.123 0 0 1-.059-.015.12.12 0 0 1-.058-.101v-50.871c0-.042.023-.08.058-.101l75.518-43.6a.112.112 0 0 1 .117 0c.036.02.059.059.059.1v50.871a.116.116 0 0 1-.059.101l-27.321 15.774a.111.111 0 0 1-.094.01.115.115 0 0 1-.071-.062l-6.46-14.168c-.375-.823-1.062-1.275-1.935-1.275-1.088 0-2.363.685-3.499 1.881a10.19 10.19 0 0 0-2.302 3.971l-6.461 21.628a.108.108 0 0 1-.053.067l-27.322 15.775a.12.12 0 0 1-.058.015zm.117-50.919v50.6l27.106-15.649 6.447-21.584a10.293 10.293 0 0 1 2.357-4.065c1.179-1.241 2.516-1.954 3.668-1.954.969 0 1.732.502 2.147 1.412l6.407 14.051 27.152-15.676v-50.601l-75.284 43.466z' fill='#607D8B'/><path d='M169.8 177.083l-27.322 15.774-6.46-14.168c-.91-1.995-3.428-1.738-5.625.574a10.246 10.246 0 0 0-2.329 4.019l-6.461 21.627-27.321 15.774v-50.87l75.518-43.6v50.87z' fill='#FAFAFA'/><path d='M94.282 220.917a.234.234 0 0 1-.234-.233v-50.871c0-.083.045-.161.117-.202l75.518-43.601a.234.234 0 1 1 .35.202v50.871a.233.233 0 0 1-.116.202l-27.322 15.775a.232.232 0 0 1-.329-.106l-6.461-14.168c-.36-.789-.992-1.206-1.828-1.206-1.056 0-2.301.672-3.415 1.844a10.099 10.099 0 0 0-2.275 3.924l-6.46 21.628a.235.235 0 0 1-.107.136l-27.322 15.774a.23.23 0 0 1-.116.031zm.233-50.969v50.331l26.891-15.525 6.434-21.539a10.41 10.41 0 0 1 2.384-4.112c1.201-1.265 2.569-1.991 3.753-1.991 1.018 0 1.818.526 2.253 1.48l6.354 13.934 26.982-15.578v-50.331l-75.051 43.331z' fill='#607D8B'/><path d='M109.894 199.943c-1.774 0-3.241-.725-4.244-2.12a.224.224 0 0 1 .023-.294.233.233 0 0 1 .301-.023c.78.547 1.705.827 2.75.827 1.323 0 2.754-.439 4.256-1.306 5.311-3.067 9.631-10.518 9.631-16.611 0-1.927-.442-3.56-1.278-4.724a.232.232 0 0 1 .323-.327c1.671 1.172 2.591 3.381 2.591 6.219 0 6.242-4.426 13.863-9.865 17.003-1.574.908-3.084 1.356-4.488 1.356zm-2.969-1.542c.813.651 1.82.877 2.968.877h.001c1.321 0 2.753-.327 4.254-1.194 5.311-3.067 9.632-10.463 9.632-16.556 0-1.979-.463-3.599-1.326-4.761.411 1.035.625 2.275.625 3.635 0 6.243-4.426 13.883-9.865 17.023-1.574.909-3.084 1.317-4.49 1.317-.641 0-1.243-.149-1.799-.341z' fill='#607D8B'/><path d='M113.097 197.23c5.384-3.108 9.748-10.636 9.748-16.814 0-2.051-.483-3.692-1.323-4.86-1.784-1.252-4.374-1.194-7.257.47-5.384 3.108-9.748 10.636-9.748 16.814 0 2.051.483 3.692 1.323 4.86 1.784 1.252 4.374 1.194 7.257-.47' fill='#FAFAFA'/><path d='M108.724 198.614c-1.142 0-2.158-.213-3.019-.817-.021-.014-.04.014-.055-.007-.894-1.244-1.367-2.948-1.367-4.973 0-6.242 4.426-13.864 9.865-17.005 1.574-.908 3.084-1.363 4.49-1.363 1.142 0 2.158.309 3.018.913a.23.23 0 0 1 .056.056c.894 1.244 1.367 2.972 1.367 4.997 0 6.243-4.426 13.783-9.865 16.923-1.574.909-3.084 1.276-4.49 1.276zm-2.718-1.109c.774.532 1.688.776 2.718.776 1.323 0 2.754-.413 4.256-1.28 5.311-3.066 9.631-10.505 9.631-16.598 0-1.909-.434-3.523-1.255-4.685-.774-.533-1.688-.799-2.718-.799-1.323 0-2.755.441-4.256 1.308-5.311 3.066-9.631 10.506-9.631 16.599 0 1.909.434 3.517 1.255 4.679z' fill='#607D8B'/><path d='M149.318 114.262l-9.984 8.878 15.893 11.031 5.589-6.112-11.498-13.797' fill='#FAFAFA'/><path d='M169.676 120.84l-9.748 5.627c-3.642 2.103-9.528 2.113-13.147.024-3.62-2.089-3.601-5.488.041-7.591l9.495-5.608-6.729-3.885-81.836 47.071 45.923 26.514 3.081-1.779c.631-.365.869-.898.618-1.39-2.357-4.632-2.593-9.546-.683-14.262 5.638-13.92 24.509-24.815 48.618-28.07 8.169-1.103 16.68-.967 24.704.394.852.145 1.776.008 2.407-.357l3.081-1.778-25.825-14.91' fill='#FAFAFA'/><path d='M113.675 183.459a.47.47 0 0 1-.233-.062l-45.924-26.515a.468.468 0 0 1 .001-.809l81.836-47.071a.467.467 0 0 1 .466 0l6.729 3.885a.467.467 0 0 1-.467.809l-6.496-3.75-80.9 46.533 44.988 25.973 2.848-1.644c.192-.111.62-.409.435-.773-2.416-4.748-2.658-9.814-.7-14.65 2.806-6.927 8.885-13.242 17.582-18.263 8.657-4.998 19.518-8.489 31.407-10.094 8.198-1.107 16.79-.97 24.844.397.739.125 1.561.007 2.095-.301l2.381-1.374-25.125-14.506a.467.467 0 0 1 .467-.809l25.825 14.91a.467.467 0 0 1 0 .809l-3.081 1.779c-.721.417-1.763.575-2.718.413-7.963-1.351-16.457-1.486-24.563-.392-11.77 1.589-22.512 5.039-31.065 9.977-8.514 4.916-14.456 11.073-17.183 17.805-1.854 4.578-1.623 9.376.666 13.875.37.725.055 1.513-.8 2.006l-3.081 1.78a.476.476 0 0 1-.234.062' fill='#455A64'/><path d='M153.316 128.279c-2.413 0-4.821-.528-6.652-1.586-1.818-1.049-2.82-2.461-2.82-3.975 0-1.527 1.016-2.955 2.861-4.02l9.493-5.607a.233.233 0 1 1 .238.402l-9.496 5.609c-1.696.979-2.628 2.263-2.628 3.616 0 1.34.918 2.608 2.585 3.571 3.549 2.049 9.343 2.038 12.914-.024l9.748-5.628a.234.234 0 0 1 .234.405l-9.748 5.628c-1.858 1.072-4.296 1.609-6.729 1.609' fill='#607D8B'/><path d='M113.675 182.992l-45.913-26.508M113.675 183.342a.346.346 0 0 1-.175-.047l-45.913-26.508a.35.35 0 1 1 .35-.607l45.913 26.508a.35.35 0 0 1-.175.654' fill='#455A64'/><path d='M67.762 156.484v54.001c0 1.09.77 2.418 1.72 2.967l42.473 24.521c.95.549 1.72.11 1.72-.98v-54.001' fill='#FAFAFA'/><path d='M112.727 238.561c-.297 0-.62-.095-.947-.285l-42.473-24.521c-1.063-.613-1.895-2.05-1.895-3.27v-54.001a.35.35 0 1 1 .701 0v54.001c0 .96.707 2.18 1.544 2.663l42.473 24.522c.344.198.661.243.87.122.206-.119.325-.411.325-.799v-54.001a.35.35 0 1 1 .7 0v54.001c0 .655-.239 1.154-.675 1.406a1.235 1.235 0 0 1-.623.162' fill='#455A64'/><path d='M112.86 147.512h-.001c-2.318 0-4.499-.522-6.142-1.471-1.705-.984-2.643-2.315-2.643-3.749 0-1.445.952-2.791 2.68-3.788l12.041-6.953c1.668-.962 3.874-1.493 6.212-1.493 2.318 0 4.499.523 6.143 1.472 1.704.984 2.643 2.315 2.643 3.748 0 1.446-.952 2.791-2.68 3.789l-12.042 6.952c-1.668.963-3.874 1.493-6.211 1.493zm12.147-16.753c-2.217 0-4.298.497-5.861 1.399l-12.042 6.952c-1.502.868-2.33 1.998-2.33 3.182 0 1.173.815 2.289 2.293 3.142 1.538.889 3.596 1.378 5.792 1.378h.001c2.216 0 4.298-.497 5.861-1.399l12.041-6.953c1.502-.867 2.33-1.997 2.33-3.182 0-1.172-.814-2.288-2.292-3.142-1.539-.888-3.596-1.377-5.793-1.377z' fill='#607D8B'/><path d='M165.63 123.219l-5.734 3.311c-3.167 1.828-8.286 1.837-11.433.02-3.147-1.817-3.131-4.772.036-6.601l5.734-3.31 11.397 6.58' fill='#FAFAFA'/><path d='M154.233 117.448l9.995 5.771-4.682 2.704c-1.434.827-3.352 1.283-5.399 1.283-2.029 0-3.923-.449-5.333-1.263-1.29-.744-2-1.694-2-2.674 0-.991.723-1.955 2.036-2.713l5.383-3.108m0-.809l-5.734 3.31c-3.167 1.829-3.183 4.784-.036 6.601 1.568.905 3.623 1.357 5.684 1.357 2.077 0 4.159-.46 5.749-1.377l5.734-3.311-11.397-6.58M145.445 179.667c-1.773 0-3.241-.85-4.243-2.245-.067-.092-.057-.275.023-.356.08-.081.207-.12.3-.055.781.548 1.706.812 2.751.811 1.322 0 2.754-.446 4.256-1.313 5.31-3.066 9.631-10.522 9.631-16.615 0-1.927-.442-3.562-1.279-4.726a.235.235 0 0 1 .024-.301.232.232 0 0 1 .3-.027c1.67 1.172 2.59 3.38 2.59 6.219 0 6.242-4.425 13.987-9.865 17.127-1.573.908-3.083 1.481-4.488 1.481zM142.476 178c.814.651 1.82 1.002 2.969 1.002 1.322 0 2.753-.452 4.255-1.32 5.31-3.065 9.631-10.523 9.631-16.617 0-1.98-.463-3.63-1.325-4.793.411 1.035.624 2.26.624 3.62 0 6.242-4.425 13.875-9.865 17.015-1.573.909-3.084 1.376-4.489 1.376a5.49 5.49 0 0 1-1.8-.283z' fill='#607D8B'/><path d='M148.648 176.704c5.384-3.108 9.748-10.636 9.748-16.813 0-2.052-.483-3.693-1.322-4.861-1.785-1.252-4.375-1.194-7.258.471-5.383 3.108-9.748 10.636-9.748 16.813 0 2.051.484 3.692 1.323 4.86 1.785 1.253 4.374 1.195 7.257-.47' fill='#FAFAFA'/><path d='M144.276 178.276c-1.143 0-2.158-.307-3.019-.911a.217.217 0 0 1-.055-.054c-.895-1.244-1.367-2.972-1.367-4.997 0-6.241 4.425-13.875 9.865-17.016 1.573-.908 3.084-1.369 4.489-1.369 1.143 0 2.158.307 3.019.91a.24.24 0 0 1 .055.055c.894 1.244 1.367 2.971 1.367 4.997 0 6.241-4.425 13.875-9.865 17.016-1.573.908-3.084 1.369-4.489 1.369zm-2.718-1.172c.773.533 1.687.901 2.718.901 1.322 0 2.754-.538 4.256-1.405 5.31-3.066 9.631-10.567 9.631-16.661 0-1.908-.434-3.554-1.256-4.716-.774-.532-1.688-.814-2.718-.814-1.322 0-2.754.433-4.256 1.3-5.31 3.066-9.631 10.564-9.631 16.657 0 1.91.434 3.576 1.256 4.738z' fill='#607D8B'/><path d='M150.72 172.361l-.363-.295a24.105 24.105 0 0 0 2.148-3.128 24.05 24.05 0 0 0 1.977-4.375l.443.149a24.54 24.54 0 0 1-2.015 4.46 24.61 24.61 0 0 1-2.19 3.189M115.917 191.514l-.363-.294a24.174 24.174 0 0 0 2.148-3.128 24.038 24.038 0 0 0 1.976-4.375l.443.148a24.48 24.48 0 0 1-2.015 4.461 24.662 24.662 0 0 1-2.189 3.188M114 237.476V182.584 237.476' fill='#607D8B'/><g><path d='M81.822 37.474c.017-.135-.075-.28-.267-.392-.327-.188-.826-.21-1.109-.045l-6.012 3.471c-.131.076-.194.178-.191.285.002.132.002.461.002.578v.043l-.007.128-6.591 3.779c-.001 0-2.077 1.046-2.787 5.192 0 0-.912 6.961-.898 19.745.015 12.57.606 17.07 1.167 21.351.22 1.684 3.001 2.125 3.001 2.125.331.04.698-.027 1.08-.248l75.273-43.551c1.808-1.069 2.667-3.719 3.056-6.284 1.213-7.99 1.675-32.978-.275-39.878-.196-.693-.51-1.083-.868-1.282l-2.086-.79c-.727.028-1.416.467-1.534.535L82.032 37.072l-.21.402' fill='#FFF'/><path d='M144.311 1.701l2.085.79c.358.199.672.589.868 1.282 1.949 6.9 1.487 31.887.275 39.878-.39 2.565-1.249 5.215-3.056 6.284L69.21 93.486a1.78 1.78 0 0 1-.896.258l-.183-.011c0 .001-2.782-.44-3.003-2.124-.56-4.282-1.151-8.781-1.165-21.351-.015-12.784.897-19.745.897-19.745.71-4.146 2.787-5.192 2.787-5.192l6.591-3.779.007-.128v-.043c0-.117 0-.446-.002-.578-.003-.107.059-.21.191-.285l6.012-3.472a.98.98 0 0 1 .481-.11c.218 0 .449.053.627.156.193.112.285.258.268.392l.211-.402 60.744-34.836c.117-.068.806-.507 1.534-.535m0-.997l-.039.001c-.618.023-1.283.244-1.974.656l-.021.012-60.519 34.706a2.358 2.358 0 0 0-.831-.15c-.365 0-.704.084-.98.244l-6.012 3.471c-.442.255-.699.69-.689 1.166l.001.15-6.08 3.487c-.373.199-2.542 1.531-3.29 5.898l-.006.039c-.009.07-.92 7.173-.906 19.875.014 12.62.603 17.116 1.172 21.465l.002.015c.308 2.355 3.475 2.923 3.836 2.98l.034.004c.101.013.204.019.305.019a2.77 2.77 0 0 0 1.396-.392l75.273-43.552c1.811-1.071 2.999-3.423 3.542-6.997 1.186-7.814 1.734-33.096-.301-40.299-.253-.893-.704-1.527-1.343-1.882l-.132-.062-2.085-.789a.973.973 0 0 0-.353-.065' fill='#455A64'/><path d='M128.267 11.565l1.495.434-56.339 32.326' fill='#FFF'/><path d='M74.202 90.545a.5.5 0 0 1-.25-.931l18.437-10.645a.499.499 0 1 1 .499.864L74.451 90.478l-.249.067M75.764 42.654l-.108-.062.046-.171 5.135-2.964.17.045-.045.171-5.135 2.964-.063.017M70.52 90.375V46.421l.063-.036L137.84 7.554v43.954l-.062.036L70.52 90.375zm.25-43.811v43.38l66.821-38.579V7.985L70.77 46.564z' fill='#607D8B'/><path d='M86.986 83.182c-.23.149-.612.384-.849.523l-11.505 6.701c-.237.139-.206.252.068.252h.565c.275 0 .693-.113.93-.252L87.7 83.705c.237-.139.428-.253.425-.256a11.29 11.29 0 0 1-.006-.503c0-.274-.188-.377-.418-.227l-.715.463' fill='#607D8B'/><path d='M75.266 90.782H74.7c-.2 0-.316-.056-.346-.166-.03-.11.043-.217.215-.317l11.505-6.702c.236-.138.615-.371.844-.519l.715-.464a.488.488 0 0 1 .266-.089c.172 0 .345.13.345.421 0 .214.001.363.003.437l.006.004-.004.069c-.003.075-.003.075-.486.356l-11.505 6.702a2.282 2.282 0 0 1-.992.268zm-.6-.25l.034.001h.566c.252 0 .649-.108.866-.234l11.505-6.702c.168-.098.294-.173.361-.214-.004-.084-.004-.218-.004-.437l-.095-.171-.131.049-.714.463c-.232.15-.616.386-.854.525l-11.505 6.702-.029.018z' fill='#607D8B'/><path d='M75.266 89.871H74.7c-.2 0-.316-.056-.346-.166-.03-.11.043-.217.215-.317l11.505-6.702c.258-.151.694-.268.993-.268h.565c.2 0 .316.056.346.166.03.11-.043.217-.215.317l-11.505 6.702a2.282 2.282 0 0 1-.992.268zm-.6-.25l.034.001h.566c.252 0 .649-.107.866-.234l11.505-6.702.03-.018-.035-.001h-.565c-.252 0-.649.108-.867.234l-11.505 6.702-.029.018zM74.37 90.801v-1.247 1.247' fill='#607D8B'/><path d='M68.13 93.901c-.751-.093-1.314-.737-1.439-1.376-.831-4.238-1.151-8.782-1.165-21.352-.015-12.784.897-19.745.897-19.745.711-4.146 2.787-5.192 2.787-5.192l74.859-43.219c.223-.129 2.487-1.584 3.195.923 1.95 6.9 1.488 31.887.275 39.878-.389 2.565-1.248 5.215-3.056 6.283L69.21 93.653c-.382.221-.749.288-1.08.248 0 0-2.781-.441-3.001-2.125-.561-4.281-1.152-8.781-1.167-21.351-.014-12.784.898-19.745.898-19.745.71-4.146 2.787-5.191 2.787-5.191l6.598-3.81.871-.119 6.599-3.83.046-.461L68.13 93.901' fill='#FAFAFA'/><path d='M68.317 94.161l-.215-.013h-.001l-.244-.047c-.719-.156-2.772-.736-2.976-2.292-.568-4.34-1.154-8.813-1.168-21.384-.014-12.654.891-19.707.9-19.777.725-4.231 2.832-5.338 2.922-5.382l6.628-3.827.87-.119 6.446-3.742.034-.334a.248.248 0 0 1 .273-.223.248.248 0 0 1 .223.272l-.059.589-6.752 3.919-.87.118-6.556 3.785c-.031.016-1.99 1.068-2.666 5.018-.007.06-.908 7.086-.894 19.702.014 12.539.597 16.996 1.161 21.305.091.691.689 1.154 1.309 1.452a1.95 1.95 0 0 1-.236-.609c-.781-3.984-1.155-8.202-1.17-21.399-.014-12.653.891-19.707.9-19.777.725-4.231 2.832-5.337 2.922-5.382-.004.001 74.444-42.98 74.846-43.212l.028-.017c.904-.538 1.72-.688 2.36-.433.555.221.949.733 1.172 1.52 2.014 7.128 1.46 32.219.281 39.983-.507 3.341-1.575 5.515-3.175 6.462L69.335 93.869a2.023 2.023 0 0 1-1.018.292zm-.147-.507c.293.036.604-.037.915-.217l75.273-43.551c1.823-1.078 2.602-3.915 2.934-6.106 1.174-7.731 1.731-32.695-.268-39.772-.178-.631-.473-1.032-.876-1.192-.484-.193-1.166-.052-1.921.397l-.034.021-74.858 43.218c-.031.017-1.989 1.069-2.666 5.019-.007.059-.908 7.085-.894 19.702.015 13.155.386 17.351 1.161 21.303.09.461.476.983 1.037 1.139.114.025.185.037.196.039h.001z' fill='#455A64'/><path d='M69.317 68.982c.489-.281.885-.056.885.505 0 .56-.396 1.243-.885 1.525-.488.282-.884.057-.884-.504 0-.56.396-1.243.884-1.526' fill='#FFF'/><path d='M68.92 71.133c-.289 0-.487-.228-.487-.625 0-.56.396-1.243.884-1.526a.812.812 0 0 1 .397-.121c.289 0 .488.229.488.626 0 .56-.396 1.243-.885 1.525a.812.812 0 0 1-.397.121m.794-2.459a.976.976 0 0 0-.49.147c-.548.317-.978 1.058-.978 1.687 0 .486.271.812.674.812a.985.985 0 0 0 .491-.146c.548-.317.978-1.057.978-1.687 0-.486-.272-.813-.675-.813' fill='#8097A2'/><path d='M68.92 70.947c-.271 0-.299-.307-.299-.439 0-.491.361-1.116.79-1.363a.632.632 0 0 1 .303-.096c.272 0 .301.306.301.438 0 .491-.363 1.116-.791 1.364a.629.629 0 0 1-.304.096m.794-2.086a.812.812 0 0 0-.397.121c-.488.283-.884.966-.884 1.526 0 .397.198.625.487.625a.812.812 0 0 0 .397-.121c.489-.282.885-.965.885-1.525 0-.397-.199-.626-.488-.626' fill='#8097A2'/><path d='M69.444 85.35c.264-.152.477-.031.477.272 0 .303-.213.67-.477.822-.263.153-.477.031-.477-.271 0-.302.214-.671.477-.823' fill='#FFF'/><path d='M69.23 86.51c-.156 0-.263-.123-.263-.337 0-.302.214-.671.477-.823a.431.431 0 0 1 .214-.066c.156 0 .263.124.263.338 0 .303-.213.67-.477.822a.431.431 0 0 1-.214.066m.428-1.412c-.1 0-.203.029-.307.09-.32.185-.57.618-.57.985 0 .309.185.524.449.524a.63.63 0 0 0 .308-.09c.32-.185.57-.618.57-.985 0-.309-.185-.524-.45-.524' fill='#8097A2'/><path d='M69.23 86.322l-.076-.149c0-.235.179-.544.384-.661l.12-.041.076.151c0 .234-.179.542-.383.66l-.121.04m.428-1.038a.431.431 0 0 0-.214.066c-.263.152-.477.521-.477.823 0 .214.107.337.263.337a.431.431 0 0 0 .214-.066c.264-.152.477-.519.477-.822 0-.214-.107-.338-.263-.338' fill='#8097A2'/><path d='M139.278 7.769v43.667L72.208 90.16V46.493l67.07-38.724' fill='#455A64'/><path d='M72.083 90.375V46.421l.063-.036 67.257-38.831v43.954l-.062.036-67.258 38.831zm.25-43.811v43.38l66.821-38.579V7.985L72.333 46.564z' fill='#607D8B'/></g><path d='M125.737 88.647l-7.639 3.334V84l-11.459 4.713v8.269L99 100.315l13.369 3.646 13.368-15.314' fill='#455A64'/></g></svg>")};var le="CardboardV1",Ae="WEBVR_CARDBOARD_VIEWER";function he(e){try{this.selectedKey=localStorage.getItem(Ae)}catch(e){console.error("Failed to load viewer profile: %s",e)}this.selectedKey||(this.selectedKey=e||le),this.dialog=this.createDialog_(q.Viewers),this.root=null,this.onChangeCallbacks_=[]}he.prototype.show=function(e){this.root=e,e.appendChild(this.dialog);var t=this.dialog.querySelector("#"+this.selectedKey);t.checked=!0,this.dialog.style.display="block"},he.prototype.hide=function(){this.root&&this.root.contains(this.dialog)&&this.root.removeChild(this.dialog),this.dialog.style.display="none"},he.prototype.getCurrentViewer=function(){return q.Viewers[this.selectedKey]},he.prototype.getSelectedKey_=function(){var e=this.dialog.querySelector("input[name=field]:checked");return e?e.id:null},he.prototype.onChange=function(e){this.onChangeCallbacks_.push(e)},he.prototype.fireOnChange_=function(e){for(var t=0;t<this.onChangeCallbacks_.length;t++)this.onChangeCallbacks_[t](e)},he.prototype.onSave_=function(){if(this.selectedKey=this.getSelectedKey_(),this.selectedKey&&q.Viewers[this.selectedKey]){this.fireOnChange_(q.Viewers[this.selectedKey]);try{localStorage.setItem(Ae,this.selectedKey)}catch(e){console.error("Failed to save viewer profile: %s",e)}this.hide()}else console.error("ViewerSelector.onSave_: this should never happen!")},he.prototype.createDialog_=function(e){var t=document.createElement("div");t.classList.add("webvr-polyfill-viewer-selector"),t.style.display="none";var i=document.createElement("div"),r=i.style;r.position="fixed",r.left=0,r.top=0,r.width="100%",r.height="100%",r.background="rgba(0, 0, 0, 0.3)",i.addEventListener("click",this.hide.bind(this));var s=document.createElement("div"),r=s.style;for(var n in r.boxSizing="border-box",r.position="fixed",r.top="24px",r.left="50%",r.marginLeft="-140px",r.width="280px",r.padding="24px",r.overflow="hidden",r.background="#fafafa",r.fontFamily="'Roboto', sans-serif",r.boxShadow="0px 5px 20px #666",s.appendChild(this.createH1_("Select your viewer")),e)s.appendChild(this.createChoice_(n,e[n].label));return s.appendChild(this.createButton_("Save",this.onSave_.bind(this))),t.appendChild(i),t.appendChild(s),t},he.prototype.createH1_=function(e){var t=document.createElement("h1"),i=t.style;return i.color="black",i.fontSize="20px",i.fontWeight="bold",i.marginTop=0,i.marginBottom="24px",t.innerHTML=e,t},he.prototype.createChoice_=function(e,t){var i=document.createElement("div");i.style.marginTop="8px",i.style.color="black";var r=document.createElement("input");r.style.fontSize="30px",r.setAttribute("id",e),r.setAttribute("type","radio"),r.setAttribute("value",e),r.setAttribute("name","field");var s=document.createElement("label");return s.style.marginLeft="4px",s.setAttribute("for",e),s.innerHTML=t,i.appendChild(r),i.appendChild(s),i},he.prototype.createButton_=function(e,t){var i=document.createElement("button");i.innerHTML=e;var r=i.style;return r.float="right",r.textTransform="uppercase",r.color="#1094f7",r.fontSize="14px",r.letterSpacing=0,r.border=0,r.background="none",r.marginTop="16px",i.addEventListener("click",t),i},"undefined"!=typeof window?window:void 0!==Ce||"undefined"!=typeof self&&self;var ce,de,ue=(function(e,t){var i;i=function(){return function(e){var t={};function i(r){if(t[r])return t[r].exports;var s=t[r]={i:r,l:!1,exports:{}};return e[r].call(s.exports,s,s.exports,i),s.l=!0,s.exports}return i.m=e,i.c=t,i.d=function(e,t,r){i.o(e,t)||Object.defineProperty(e,t,{configurable:!1,enumerable:!0,get:r})},i.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return i.d(t,"a",t),t},i.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},i.p="",i(i.s=0)}([function(e,t,i){var r=function(){function e(e,t){for(var i=0;i<t.length;i++){var r=t[i];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,i,r){return i&&e(t.prototype,i),r&&e(t,r),t}}(),s=i(1),n="undefined"!=typeof navigator&&parseFloat((""+(/CPU.*OS ([0-9_]{3,4})[0-9_]{0,1}|(CPU like).*AppleWebKit.*Mobile/i.exec(navigator.userAgent)||[0,""])[1]).replace("undefined","3_2").replace("_",".").replace("_",""))<10&&!window.MSStream,a=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),n?this.noSleepTimer=null:(this.noSleepVideo=document.createElement("video"),this.noSleepVideo.setAttribute("playsinline",""),this.noSleepVideo.setAttribute("src",s),this.noSleepVideo.addEventListener("timeupdate",function(e){this.noSleepVideo.currentTime>.5&&(this.noSleepVideo.currentTime=Math.random())}.bind(this)))}return r(e,[{key:"enable",value:function(){n?(this.disable(),this.noSleepTimer=window.setInterval(function(){window.location.href="/",window.setTimeout(window.stop,0)},15e3)):this.noSleepVideo.play()}},{key:"disable",value:function(){n?this.noSleepTimer&&(window.clearInterval(this.noSleepTimer),this.noSleepTimer=null):this.noSleepVideo.pause()}}]),e}();e.exports=a},function(e,t,i){e.exports="data:video/mp4;base64,AAAAIGZ0eXBtcDQyAAACAGlzb21pc28yYXZjMW1wNDEAAAAIZnJlZQAACKBtZGF0AAAC8wYF///v3EXpvebZSLeWLNgg2SPu73gyNjQgLSBjb3JlIDE0MiByMjQ3OSBkZDc5YTYxIC0gSC4yNjQvTVBFRy00IEFWQyBjb2RlYyAtIENvcHlsZWZ0IDIwMDMtMjAxNCAtIGh0dHA6Ly93d3cudmlkZW9sYW4ub3JnL3gyNjQuaHRtbCAtIG9wdGlvbnM6IGNhYmFjPTEgcmVmPTEgZGVibG9jaz0xOjA6MCBhbmFseXNlPTB4MToweDExMSBtZT1oZXggc3VibWU9MiBwc3k9MSBwc3lfcmQ9MS4wMDowLjAwIG1peGVkX3JlZj0wIG1lX3JhbmdlPTE2IGNocm9tYV9tZT0xIHRyZWxsaXM9MCA4eDhkY3Q9MCBjcW09MCBkZWFkem9uZT0yMSwxMSBmYXN0X3Bza2lwPTEgY2hyb21hX3FwX29mZnNldD0wIHRocmVhZHM9NiBsb29rYWhlYWRfdGhyZWFkcz0xIHNsaWNlZF90aHJlYWRzPTAgbnI9MCBkZWNpbWF0ZT0xIGludGVybGFjZWQ9MCBibHVyYXlfY29tcGF0PTAgY29uc3RyYWluZWRfaW50cmE9MCBiZnJhbWVzPTMgYl9weXJhbWlkPTIgYl9hZGFwdD0xIGJfYmlhcz0wIGRpcmVjdD0xIHdlaWdodGI9MSBvcGVuX2dvcD0wIHdlaWdodHA9MSBrZXlpbnQ9MzAwIGtleWludF9taW49MzAgc2NlbmVjdXQ9NDAgaW50cmFfcmVmcmVzaD0wIHJjX2xvb2thaGVhZD0xMCByYz1jcmYgbWJ0cmVlPTEgY3JmPTIwLjAgcWNvbXA9MC42MCBxcG1pbj0wIHFwbWF4PTY5IHFwc3RlcD00IHZidl9tYXhyYXRlPTIwMDAwIHZidl9idWZzaXplPTI1MDAwIGNyZl9tYXg9MC4wIG5hbF9ocmQ9bm9uZSBmaWxsZXI9MCBpcF9yYXRpbz0xLjQwIGFxPTE6MS4wMACAAAAAOWWIhAA3//p+C7v8tDDSTjf97w55i3SbRPO4ZY+hkjD5hbkAkL3zpJ6h/LR1CAABzgB1kqqzUorlhQAAAAxBmiQYhn/+qZYADLgAAAAJQZ5CQhX/AAj5IQADQGgcIQADQGgcAAAACQGeYUQn/wALKCEAA0BoHAAAAAkBnmNEJ/8ACykhAANAaBwhAANAaBwAAAANQZpoNExDP/6plgAMuSEAA0BoHAAAAAtBnoZFESwr/wAI+SEAA0BoHCEAA0BoHAAAAAkBnqVEJ/8ACykhAANAaBwAAAAJAZ6nRCf/AAsoIQADQGgcIQADQGgcAAAADUGarDRMQz/+qZYADLghAANAaBwAAAALQZ7KRRUsK/8ACPkhAANAaBwAAAAJAZ7pRCf/AAsoIQADQGgcIQADQGgcAAAACQGe60Qn/wALKCEAA0BoHAAAAA1BmvA0TEM//qmWAAy5IQADQGgcIQADQGgcAAAAC0GfDkUVLCv/AAj5IQADQGgcAAAACQGfLUQn/wALKSEAA0BoHCEAA0BoHAAAAAkBny9EJ/8ACyghAANAaBwAAAANQZs0NExDP/6plgAMuCEAA0BoHAAAAAtBn1JFFSwr/wAI+SEAA0BoHCEAA0BoHAAAAAkBn3FEJ/8ACyghAANAaBwAAAAJAZ9zRCf/AAsoIQADQGgcIQADQGgcAAAADUGbeDRMQz/+qZYADLkhAANAaBwAAAALQZ+WRRUsK/8ACPghAANAaBwhAANAaBwAAAAJAZ+1RCf/AAspIQADQGgcAAAACQGft0Qn/wALKSEAA0BoHCEAA0BoHAAAAA1Bm7w0TEM//qmWAAy4IQADQGgcAAAAC0Gf2kUVLCv/AAj5IQADQGgcAAAACQGf+UQn/wALKCEAA0BoHCEAA0BoHAAAAAkBn/tEJ/8ACykhAANAaBwAAAANQZvgNExDP/6plgAMuSEAA0BoHCEAA0BoHAAAAAtBnh5FFSwr/wAI+CEAA0BoHAAAAAkBnj1EJ/8ACyghAANAaBwhAANAaBwAAAAJAZ4/RCf/AAspIQADQGgcAAAADUGaJDRMQz/+qZYADLghAANAaBwAAAALQZ5CRRUsK/8ACPkhAANAaBwhAANAaBwAAAAJAZ5hRCf/AAsoIQADQGgcAAAACQGeY0Qn/wALKSEAA0BoHCEAA0BoHAAAAA1Bmmg0TEM//qmWAAy5IQADQGgcAAAAC0GehkUVLCv/AAj5IQADQGgcIQADQGgcAAAACQGepUQn/wALKSEAA0BoHAAAAAkBnqdEJ/8ACyghAANAaBwAAAANQZqsNExDP/6plgAMuCEAA0BoHCEAA0BoHAAAAAtBnspFFSwr/wAI+SEAA0BoHAAAAAkBnulEJ/8ACyghAANAaBwhAANAaBwAAAAJAZ7rRCf/AAsoIQADQGgcAAAADUGa8DRMQz/+qZYADLkhAANAaBwhAANAaBwAAAALQZ8ORRUsK/8ACPkhAANAaBwAAAAJAZ8tRCf/AAspIQADQGgcIQADQGgcAAAACQGfL0Qn/wALKCEAA0BoHAAAAA1BmzQ0TEM//qmWAAy4IQADQGgcAAAAC0GfUkUVLCv/AAj5IQADQGgcIQADQGgcAAAACQGfcUQn/wALKCEAA0BoHAAAAAkBn3NEJ/8ACyghAANAaBwhAANAaBwAAAANQZt4NExC//6plgAMuSEAA0BoHAAAAAtBn5ZFFSwr/wAI+CEAA0BoHCEAA0BoHAAAAAkBn7VEJ/8ACykhAANAaBwAAAAJAZ+3RCf/AAspIQADQGgcAAAADUGbuzRMQn/+nhAAYsAhAANAaBwhAANAaBwAAAAJQZ/aQhP/AAspIQADQGgcAAAACQGf+UQn/wALKCEAA0BoHCEAA0BoHCEAA0BoHCEAA0BoHCEAA0BoHCEAA0BoHAAACiFtb292AAAAbG12aGQAAAAA1YCCX9WAgl8AAAPoAAAH/AABAAABAAAAAAAAAAAAAAAAAQAAAAAAAAAAAAAAAAAAAAEAAAAAAAAAAAAAAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADAAAAGGlvZHMAAAAAEICAgAcAT////v7/AAAF+XRyYWsAAABcdGtoZAAAAAPVgIJf1YCCXwAAAAEAAAAAAAAH0AAAAAAAAAAAAAAAAAAAAAAAAQAAAAAAAAAAAAAAAAAAAAEAAAAAAAAAAAAAAAAAAEAAAAAAygAAAMoAAAAAACRlZHRzAAAAHGVsc3QAAAAAAAAAAQAAB9AAABdwAAEAAAAABXFtZGlhAAAAIG1kaGQAAAAA1YCCX9WAgl8AAV+QAAK/IFXEAAAAAAAtaGRscgAAAAAAAAAAdmlkZQAAAAAAAAAAAAAAAFZpZGVvSGFuZGxlcgAAAAUcbWluZgAAABR2bWhkAAAAAQAAAAAAAAAAAAAAJGRpbmYAAAAcZHJlZgAAAAAAAAABAAAADHVybCAAAAABAAAE3HN0YmwAAACYc3RzZAAAAAAAAAABAAAAiGF2YzEAAAAAAAAAAQAAAAAAAAAAAAAAAAAAAAAAygDKAEgAAABIAAAAAAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAY//8AAAAyYXZjQwFNQCj/4QAbZ01AKOyho3ySTUBAQFAAAAMAEAAr8gDxgxlgAQAEaO+G8gAAABhzdHRzAAAAAAAAAAEAAAA8AAALuAAAABRzdHNzAAAAAAAAAAEAAAABAAAB8GN0dHMAAAAAAAAAPAAAAAEAABdwAAAAAQAAOpgAAAABAAAXcAAAAAEAAAAAAAAAAQAAC7gAAAABAAA6mAAAAAEAABdwAAAAAQAAAAAAAAABAAALuAAAAAEAADqYAAAAAQAAF3AAAAABAAAAAAAAAAEAAAu4AAAAAQAAOpgAAAABAAAXcAAAAAEAAAAAAAAAAQAAC7gAAAABAAA6mAAAAAEAABdwAAAAAQAAAAAAAAABAAALuAAAAAEAADqYAAAAAQAAF3AAAAABAAAAAAAAAAEAAAu4AAAAAQAAOpgAAAABAAAXcAAAAAEAAAAAAAAAAQAAC7gAAAABAAA6mAAAAAEAABdwAAAAAQAAAAAAAAABAAALuAAAAAEAADqYAAAAAQAAF3AAAAABAAAAAAAAAAEAAAu4AAAAAQAAOpgAAAABAAAXcAAAAAEAAAAAAAAAAQAAC7gAAAABAAA6mAAAAAEAABdwAAAAAQAAAAAAAAABAAALuAAAAAEAADqYAAAAAQAAF3AAAAABAAAAAAAAAAEAAAu4AAAAAQAAOpgAAAABAAAXcAAAAAEAAAAAAAAAAQAAC7gAAAABAAA6mAAAAAEAABdwAAAAAQAAAAAAAAABAAALuAAAAAEAAC7gAAAAAQAAF3AAAAABAAAAAAAAABxzdHNjAAAAAAAAAAEAAAABAAAAAQAAAAEAAAEEc3RzegAAAAAAAAAAAAAAPAAAAzQAAAAQAAAADQAAAA0AAAANAAAAEQAAAA8AAAANAAAADQAAABEAAAAPAAAADQAAAA0AAAARAAAADwAAAA0AAAANAAAAEQAAAA8AAAANAAAADQAAABEAAAAPAAAADQAAAA0AAAARAAAADwAAAA0AAAANAAAAEQAAAA8AAAANAAAADQAAABEAAAAPAAAADQAAAA0AAAARAAAADwAAAA0AAAANAAAAEQAAAA8AAAANAAAADQAAABEAAAAPAAAADQAAAA0AAAARAAAADwAAAA0AAAANAAAAEQAAAA8AAAANAAAADQAAABEAAAANAAAADQAAAQBzdGNvAAAAAAAAADwAAAAwAAADZAAAA3QAAAONAAADoAAAA7kAAAPQAAAD6wAAA/4AAAQXAAAELgAABEMAAARcAAAEbwAABIwAAAShAAAEugAABM0AAATkAAAE/wAABRIAAAUrAAAFQgAABV0AAAVwAAAFiQAABaAAAAW1AAAFzgAABeEAAAX+AAAGEwAABiwAAAY/AAAGVgAABnEAAAaEAAAGnQAABrQAAAbPAAAG4gAABvUAAAcSAAAHJwAAB0AAAAdTAAAHcAAAB4UAAAeeAAAHsQAAB8gAAAfjAAAH9gAACA8AAAgmAAAIQQAACFQAAAhnAAAIhAAACJcAAAMsdHJhawAAAFx0a2hkAAAAA9WAgl/VgIJfAAAAAgAAAAAAAAf8AAAAAAAAAAAAAAABAQAAAAABAAAAAAAAAAAAAAAAAAAAAQAAAAAAAAAAAAAAAAAAQAAAAAAAAAAAAAAAAAACsm1kaWEAAAAgbWRoZAAAAADVgIJf1YCCXwAArEQAAWAAVcQAAAAAACdoZGxyAAAAAAAAAABzb3VuAAAAAAAAAAAAAAAAU3RlcmVvAAAAAmNtaW5mAAAAEHNtaGQAAAAAAAAAAAAAACRkaW5mAAAAHGRyZWYAAAAAAAAAAQAAAAx1cmwgAAAAAQAAAidzdGJsAAAAZ3N0c2QAAAAAAAAAAQAAAFdtcDRhAAAAAAAAAAEAAAAAAAAAAAACABAAAAAArEQAAAAAADNlc2RzAAAAAAOAgIAiAAIABICAgBRAFQAAAAADDUAAAAAABYCAgAISEAaAgIABAgAAABhzdHRzAAAAAAAAAAEAAABYAAAEAAAAABxzdHNjAAAAAAAAAAEAAAABAAAAAQAAAAEAAAAUc3RzegAAAAAAAAAGAAAAWAAAAXBzdGNvAAAAAAAAAFgAAAOBAAADhwAAA5oAAAOtAAADswAAA8oAAAPfAAAD5QAAA/gAAAQLAAAEEQAABCgAAAQ9AAAEUAAABFYAAARpAAAEgAAABIYAAASbAAAErgAABLQAAATHAAAE3gAABPMAAAT5AAAFDAAABR8AAAUlAAAFPAAABVEAAAVXAAAFagAABX0AAAWDAAAFmgAABa8AAAXCAAAFyAAABdsAAAXyAAAF+AAABg0AAAYgAAAGJgAABjkAAAZQAAAGZQAABmsAAAZ+AAAGkQAABpcAAAauAAAGwwAABskAAAbcAAAG7wAABwYAAAcMAAAHIQAABzQAAAc6AAAHTQAAB2QAAAdqAAAHfwAAB5IAAAeYAAAHqwAAB8IAAAfXAAAH3QAAB/AAAAgDAAAICQAACCAAAAg1AAAIOwAACE4AAAhhAAAIeAAACH4AAAiRAAAIpAAACKoAAAiwAAAItgAACLwAAAjCAAAAFnVkdGEAAAAObmFtZVN0ZXJlbwAAAHB1ZHRhAAAAaG1ldGEAAAAAAAAAIWhkbHIAAAAAAAAAAG1kaXJhcHBsAAAAAAAAAAAAAAAAO2lsc3QAAAAzqXRvbwAAACtkYXRhAAAAAQAAAABIYW5kQnJha2UgMC4xMC4yIDIwMTUwNjExMDA="}])},e.exports=i()}(de={exports:{}},de.exports),(ce=de.exports)&&ce.__esModule&&Object.prototype.hasOwnProperty.call(ce,"default")?ce.default:ce),pe=1e3,fe=[0,0,.5,1],me=[.5,0,.5,1],ge=window.requestAnimationFrame,ve=window.cancelAnimationFrame;function we(e){Object.defineProperties(this,{hasPosition:{writable:!1,enumerable:!0,value:e.hasPosition},hasExternalDisplay:{writable:!1,enumerable:!0,value:e.hasExternalDisplay},canPresent:{writable:!1,enumerable:!0,value:e.canPresent},maxLayers:{writable:!1,enumerable:!0,value:e.maxLayers},hasOrientation:{enumerable:!0,get:function(){return F("VRDisplayCapabilities.prototype.hasOrientation","VRDisplay.prototype.getFrameData"),e.hasOrientation}}})}function ye(e){var t=!("wakelock"in(e=e||{}))||e.wakelock;this.isPolyfilled=!0,this.displayId=pe++,this.displayName="",this.depthNear=.01,this.depthFar=1e4,this.isPresenting=!1,Object.defineProperty(this,"isConnected",{get:function(){return F("VRDisplay.prototype.isConnected","VRDisplayCapabilities.prototype.hasExternalDisplay"),!1}}),this.capabilities=new we({hasPosition:!1,hasOrientation:!1,hasExternalDisplay:!1,canPresent:!1,maxLayers:1}),this.stageParameters=null,this.waitingForPresent_=!1,this.layer_=null,this.originalParent_=null,this.fullscreenElement_=null,this.fullscreenWrapper_=null,this.fullscreenElementCachedStyle_=null,this.fullscreenEventTarget_=null,this.fullscreenChangeHandler_=null,this.fullscreenErrorHandler_=null,t&&b()&&(this.wakelock_=new ue)}ye.prototype.getFrameData=function(e){return M(e,this._getPose(),this)},ye.prototype.getPose=function(){return F("VRDisplay.prototype.getPose","VRDisplay.prototype.getFrameData"),this._getPose()},ye.prototype.resetPose=function(){return F("VRDisplay.prototype.resetPose"),this._resetPose()},ye.prototype.getImmediatePose=function(){return F("VRDisplay.prototype.getImmediatePose","VRDisplay.prototype.getFrameData"),this._getPose()},ye.prototype.requestAnimationFrame=function(e){return ge(e)},ye.prototype.cancelAnimationFrame=function(e){return ve(e)},ye.prototype.wrapForFullscreen=function(e){if(o())return e;if(!this.fullscreenWrapper_){this.fullscreenWrapper_=document.createElement("div");var t=["height: "+Math.min(screen.height,screen.width)+"px !important","top: 0 !important","left: 0 !important","right: 0 !important","border: 0","margin: 0","padding: 0","z-index: 999999 !important","position: fixed"];this.fullscreenWrapper_.setAttribute("style",t.join("; ")+";"),this.fullscreenWrapper_.classList.add("webvr-polyfill-fullscreen-wrapper")}if(this.fullscreenElement_==e)return this.fullscreenWrapper_;if(this.fullscreenElement_&&(this.originalParent_?this.originalParent_.appendChild(this.fullscreenElement_):this.fullscreenElement_.parentElement.removeChild(this.fullscreenElement_)),this.fullscreenElement_=e,this.originalParent_=e.parentElement,this.originalParent_||document.body.appendChild(e),!this.fullscreenWrapper_.parentElement){var i=this.fullscreenElement_.parentElement;i.insertBefore(this.fullscreenWrapper_,this.fullscreenElement_),i.removeChild(this.fullscreenElement_)}this.fullscreenWrapper_.insertBefore(this.fullscreenElement_,this.fullscreenWrapper_.firstChild),this.fullscreenElementCachedStyle_=this.fullscreenElement_.getAttribute("style");var r=this;return function(){if(r.fullscreenElement_){var e=["position: absolute","top: 0","left: 0","width: "+Math.max(screen.width,screen.height)+"px","height: "+Math.min(screen.height,screen.width)+"px","border: 0","margin: 0","padding: 0"];r.fullscreenElement_.setAttribute("style",e.join("; ")+";")}}(),this.fullscreenWrapper_},ye.prototype.removeFullscreenWrapper=function(){if(this.fullscreenElement_){var e=this.fullscreenElement_;this.fullscreenElementCachedStyle_?e.setAttribute("style",this.fullscreenElementCachedStyle_):e.removeAttribute("style"),this.fullscreenElement_=null,this.fullscreenElementCachedStyle_=null;var t=this.fullscreenWrapper_.parentElement;return this.fullscreenWrapper_.removeChild(e),this.originalParent_===t?t.insertBefore(e,this.fullscreenWrapper_):this.originalParent_&&this.originalParent_.appendChild(e),t.removeChild(this.fullscreenWrapper_),e}},ye.prototype.requestPresent=function(e){var t=this.isPresenting,i=this;return e instanceof Array||(F("VRDisplay.prototype.requestPresent with non-array argument","an array of VRLayers as the first argument"),e=[e]),new Promise(function(r,s){if(i.capabilities.canPresent)if(0==e.length||e.length>i.capabilities.maxLayers)s(new Error("Invalid number of layers."));else{var n=e[0];if(n.source){var a=n.leftBounds||fe,A=n.rightBounds||me;if(t){var h=i.layer_;h.source!==n.source&&(h.source=n.source);for(var c=0;c<4;c++)h.leftBounds[c]=a[c],h.rightBounds[c]=A[c];return i.wrapForFullscreen(i.layer_.source),i.updatePresent_(),void r()}if(i.layer_={predistorted:n.predistorted,source:n.source,leftBounds:a.slice(0),rightBounds:A.slice(0)},i.waitingForPresent_=!1,i.layer_&&i.layer_.source){var d=i.wrapForFullscreen(i.layer_.source);i.addFullscreenListeners_(d,function(){var e=document.fullscreenElement||document.webkitFullscreenElement||document.mozFullScreenElement||document.msFullscreenElement;i.isPresenting=d===e,i.isPresenting?(screen.orientation&&screen.orientation.lock&&screen.orientation.lock("landscape-primary").catch(function(e){console.error("screen.orientation.lock() failed due to",e.message)}),i.waitingForPresent_=!1,i.beginPresent_(),r()):(screen.orientation&&screen.orientation.unlock&&screen.orientation.unlock(),i.removeFullscreenWrapper(),i.disableWakeLock(),i.endPresent_(),i.removeFullscreenListeners_()),i.fireVRDisplayPresentChange_()},function(){i.waitingForPresent_&&(i.removeFullscreenWrapper(),i.removeFullscreenListeners_(),i.disableWakeLock(),i.waitingForPresent_=!1,i.isPresenting=!1,s(new Error("Unable to present.")))}),function(e){if(l())return!1;if(e.requestFullscreen)e.requestFullscreen();else if(e.webkitRequestFullscreen)e.webkitRequestFullscreen();else if(e.mozRequestFullScreen)e.mozRequestFullScreen();else{if(!e.msRequestFullscreen)return!1;e.msRequestFullscreen()}return!0}(d)?(i.enableWakeLock(),i.waitingForPresent_=!0):(o()||l())&&(i.enableWakeLock(),i.isPresenting=!0,i.beginPresent_(),i.fireVRDisplayPresentChange_(),r())}i.waitingForPresent_||o()||(v(),s(new Error("Unable to present.")))}else r()}else s(new Error("VRDisplay is not capable of presenting."))})},ye.prototype.exitPresent=function(){var e=this.isPresenting,t=this;return this.isPresenting=!1,this.layer_=null,this.disableWakeLock(),new Promise(function(i,r){e?(!v()&&o()&&(t.endPresent_(),t.fireVRDisplayPresentChange_()),l()&&(t.removeFullscreenWrapper(),t.removeFullscreenListeners_(),t.endPresent_(),t.fireVRDisplayPresentChange_()),i()):r(new Error("Was not presenting to VRDisplay."))})},ye.prototype.getLayers=function(){return this.layer_?[this.layer_]:[]},ye.prototype.fireVRDisplayPresentChange_=function(){var e=new CustomEvent("vrdisplaypresentchange",{detail:{display:this}});window.dispatchEvent(e)},ye.prototype.fireVRDisplayConnect_=function(){var e=new CustomEvent("vrdisplayconnect",{detail:{display:this}});window.dispatchEvent(e)},ye.prototype.addFullscreenListeners_=function(e,t,i){this.removeFullscreenListeners_(),this.fullscreenEventTarget_=e,this.fullscreenChangeHandler_=t,this.fullscreenErrorHandler_=i,t&&(document.fullscreenEnabled?e.addEventListener("fullscreenchange",t,!1):document.webkitFullscreenEnabled?e.addEventListener("webkitfullscreenchange",t,!1):document.mozFullScreenEnabled?document.addEventListener("mozfullscreenchange",t,!1):document.msFullscreenEnabled&&e.addEventListener("msfullscreenchange",t,!1)),i&&(document.fullscreenEnabled?e.addEventListener("fullscreenerror",i,!1):document.webkitFullscreenEnabled?e.addEventListener("webkitfullscreenerror",i,!1):document.mozFullScreenEnabled?document.addEventListener("mozfullscreenerror",i,!1):document.msFullscreenEnabled&&e.addEventListener("msfullscreenerror",i,!1))},ye.prototype.removeFullscreenListeners_=function(){if(this.fullscreenEventTarget_){var e=this.fullscreenEventTarget_;if(this.fullscreenChangeHandler_){var t=this.fullscreenChangeHandler_;e.removeEventListener("fullscreenchange",t,!1),e.removeEventListener("webkitfullscreenchange",t,!1),document.removeEventListener("mozfullscreenchange",t,!1),e.removeEventListener("msfullscreenchange",t,!1)}if(this.fullscreenErrorHandler_){var i=this.fullscreenErrorHandler_;e.removeEventListener("fullscreenerror",i,!1),e.removeEventListener("webkitfullscreenerror",i,!1),document.removeEventListener("mozfullscreenerror",i,!1),e.removeEventListener("msfullscreenerror",i,!1)}this.fullscreenEventTarget_=null,this.fullscreenChangeHandler_=null,this.fullscreenErrorHandler_=null}},ye.prototype.enableWakeLock=function(){this.wakelock_&&this.wakelock_.enable()},ye.prototype.disableWakeLock=function(){this.wakelock_&&this.wakelock_.disable()},ye.prototype.beginPresent_=function(){},ye.prototype.endPresent_=function(){},ye.prototype.submitFrame=function(e){},ye.prototype.getEyeParameters=function(e){return null};var be={ADDITIONAL_VIEWERS:[],DEFAULT_VIEWER:"",MOBILE_WAKE_LOCK:!0,DEBUG:!1,DPDB_URL:"https://dpdb.webvr.rocks/dpdb.json",K_FILTER:.98,PREDICTION_TIME_S:.04,CARDBOARD_UI_DISABLED:!1,ROTATE_INSTRUCTIONS_DISABLED:!1,YAW_ONLY:!1,BUFFER_SCALE:.5,DIRTY_SUBMIT_FRAME_BINDINGS:!1},Ee={LEFT:"left",RIGHT:"right"};function Se(e){var t=E({},be);e=E(t,e||{}),ye.call(this,{wakelock:e.MOBILE_WAKE_LOCK}),this.config=e,this.displayName="Cardboard VRDisplay",this.capabilities=new we({hasPosition:!1,hasOrientation:!0,hasExternalDisplay:!1,canPresent:!0,maxLayers:1}),this.stageParameters=null,this.bufferScale_=this.config.BUFFER_SCALE,this.poseSensor_=new ae(this.config),this.distorter_=null,this.cardboardUI_=null,this.dpdb_=new J(this.config.DPDB_URL,this.onDeviceParamsUpdated_.bind(this)),this.deviceInfo_=new q(this.dpdb_.getDeviceParams(),e.ADDITIONAL_VIEWERS),this.viewerSelector_=new he(e.DEFAULT_VIEWER),this.viewerSelector_.onChange(this.onViewerChanged_.bind(this)),this.deviceInfo_.setViewer(this.viewerSelector_.getCurrentViewer()),this.config.ROTATE_INSTRUCTIONS_DISABLED||(this.rotateInstructions_=new oe),o()&&window.addEventListener("resize",this.onResize_.bind(this))}return Se.prototype=Object.create(ye.prototype),Se.prototype._getPose=function(){return{position:null,orientation:this.poseSensor_.getOrientation(),linearVelocity:null,linearAcceleration:null,angularVelocity:null,angularAcceleration:null}},Se.prototype._resetPose=function(){this.poseSensor_.resetPose&&this.poseSensor_.resetPose()},Se.prototype._getFieldOfView=function(e){var t;if(e==Ee.LEFT)t=this.deviceInfo_.getFieldOfViewLeftEye();else{if(e!=Ee.RIGHT)return console.error("Invalid eye provided: %s",e),null;t=this.deviceInfo_.getFieldOfViewRightEye()}return t},Se.prototype._getEyeOffset=function(e){var t;if(e==Ee.LEFT)t=[.5*-this.deviceInfo_.viewer.interLensDistance,0,0];else{if(e!=Ee.RIGHT)return console.error("Invalid eye provided: %s",e),null;t=[.5*this.deviceInfo_.viewer.interLensDistance,0,0]}return t},Se.prototype.getEyeParameters=function(e){var t=this._getEyeOffset(e),i=this._getFieldOfView(e),r={offset:t,renderWidth:.5*this.deviceInfo_.device.width*this.bufferScale_,renderHeight:this.deviceInfo_.device.height*this.bufferScale_};return Object.defineProperty(r,"fieldOfView",{enumerable:!0,get:function(){return F("VRFieldOfView","VRFrameData's projection matrices"),i}}),r},Se.prototype.onDeviceParamsUpdated_=function(e){this.config.DEBUG&&console.log("DPDB reported that device params were updated."),this.deviceInfo_.updateDeviceParams(e),this.distorter_&&this.distorter_.updateDeviceInfo(this.deviceInfo_)},Se.prototype.updateBounds_=function(){this.layer_&&this.distorter_&&(this.layer_.leftBounds||this.layer_.rightBounds)&&this.distorter_.setTextureBounds(this.layer_.leftBounds,this.layer_.rightBounds)},Se.prototype.beginPresent_=function(){var e=this.layer_.source.getContext("webgl");e||(e=this.layer_.source.getContext("experimental-webgl")),e||(e=this.layer_.source.getContext("webgl2")),e&&(this.layer_.predistorted?this.config.CARDBOARD_UI_DISABLED||(e.canvas.width=m()*this.bufferScale_,e.canvas.height=g()*this.bufferScale_,this.cardboardUI_=new O(e)):(this.config.CARDBOARD_UI_DISABLED||(this.cardboardUI_=new O(e)),this.distorter_=new P(e,this.cardboardUI_,this.config.BUFFER_SCALE,this.config.DIRTY_SUBMIT_FRAME_BINDINGS),this.distorter_.updateDeviceInfo(this.deviceInfo_)),this.cardboardUI_&&this.cardboardUI_.listen(function(e){this.viewerSelector_.show(this.layer_.source.parentElement),e.stopPropagation(),e.preventDefault()}.bind(this),function(e){this.exitPresent(),e.stopPropagation(),e.preventDefault()}.bind(this)),this.rotateInstructions_&&(f()&&b()?this.rotateInstructions_.showTemporarily(3e3,this.layer_.source.parentElement):this.rotateInstructions_.update()),this.orientationHandler=this.onOrientationChange_.bind(this),window.addEventListener("orientationchange",this.orientationHandler),this.vrdisplaypresentchangeHandler=this.updateBounds_.bind(this),window.addEventListener("vrdisplaypresentchange",this.vrdisplaypresentchangeHandler),this.fireVRDisplayDeviceParamsChange_())},Se.prototype.endPresent_=function(){this.distorter_&&(this.distorter_.destroy(),this.distorter_=null),this.cardboardUI_&&(this.cardboardUI_.destroy(),this.cardboardUI_=null),this.rotateInstructions_&&this.rotateInstructions_.hide(),this.viewerSelector_.hide(),window.removeEventListener("orientationchange",this.orientationHandler),window.removeEventListener("vrdisplaypresentchange",this.vrdisplaypresentchangeHandler)},Se.prototype.updatePresent_=function(){this.endPresent_(),this.beginPresent_()},Se.prototype.submitFrame=function(e){if(this.distorter_)this.updateBounds_(),this.distorter_.submitFrame();else if(this.cardboardUI_&&this.layer_){var t=this.layer_.source.getContext("webgl");t||(t=this.layer_.source.getContext("experimental-webgl")),t||(t=this.layer_.source.getContext("webgl2"));var i=t.canvas;i.width==this.lastWidth&&i.height==this.lastHeight||this.cardboardUI_.onResize(),this.lastWidth=i.width,this.lastHeight=i.height,this.cardboardUI_.render()}},Se.prototype.onOrientationChange_=function(e){this.viewerSelector_.hide(),this.rotateInstructions_&&this.rotateInstructions_.update(),this.onResize_()},Se.prototype.onResize_=function(e){if(this.layer_){var t=this.layer_.source.getContext("webgl");t||(t=this.layer_.source.getContext("experimental-webgl")),t||(t=this.layer_.source.getContext("webgl2")),t.canvas.setAttribute("style",["position: absolute","top: 0","left: 0","width: 100vw","height: 100vh","border: 0","margin: 0","padding: 0px","box-sizing: content-box"].join("; ")+";"),S(t.canvas)}},Se.prototype.onViewerChanged_=function(e){this.deviceInfo_.setViewer(e),this.distorter_&&this.distorter_.updateDeviceInfo(this.deviceInfo_),this.fireVRDisplayDeviceParamsChange_()},Se.prototype.fireVRDisplayDeviceParamsChange_=function(){var e=new CustomEvent("vrdisplaydeviceparamschange",{detail:{vrdisplay:this,deviceInfo:this.deviceInfo_}});window.dispatchEvent(e)},Se.VRFrameData=function(){this.leftProjectionMatrix=new Float32Array(16),this.leftViewMatrix=new Float32Array(16),this.rightProjectionMatrix=new Float32Array(16),this.rightViewMatrix=new Float32Array(16),this.pose=null},Se.VRDisplay=ye,Se}()}(De={exports:{}},De.exports),De.exports),Oe=(Ie=Le)&&Ie.__esModule&&Object.prototype.hasOwnProperty.call(Ie,"default")?Ie.default:Ie;class Ne extends i{constructor(e){super(),this.global=e,this.onWindowResize=this.onWindowResize.bind(this),this.global.window.addEventListener("resize",this.onWindowResize),this.environmentBlendMode="opaque"}onBaseLayerSet(e,t){throw new Error("Not implemented")}isSessionSupported(e){throw new Error("Not implemented")}isFeatureSupported(e){throw new Error("Not implemented")}async requestSession(e,t){throw new Error("Not implemented")}requestAnimationFrame(e){throw new Error("Not implemented")}onFrameStart(e){throw new Error("Not implemented")}onFrameEnd(e){throw new Error("Not implemented")}doesSessionSupportReferenceSpace(e,t){throw new Error("Not implemented")}requestStageBounds(){throw new Error("Not implemented")}async requestFrameOfReferenceTransform(e,t){}cancelAnimationFrame(e){throw new Error("Not implemented")}endSession(e){throw new Error("Not implemented")}getViewport(e,t,i,r){throw new Error("Not implemented")}getProjectionMatrix(e){throw new Error("Not implemented")}getBasePoseMatrix(){throw new Error("Not implemented")}getBaseViewMatrix(e){throw new Error("Not implemented")}getInputSources(){throw new Error("Not implemented")}getInputPose(e,t,i){throw new Error("Not implemented")}onWindowResize(){this.onWindowResize()}}let Ge={mapping:"xr-standard",profiles:["oculus-go","generic-trigger-touchpad"],buttons:{length:3,0:1,1:null,2:0},gripTransform:{orientation:[.11*Math.PI,0,0,1]}},Qe={mapping:"xr-standard",displayProfiles:{"Oculus Quest":["oculus-touch-v2","oculus-touch","generic-trigger-squeeze-thumbstick"]},profiles:["oculus-touch","generic-trigger-squeeze-thumbstick"],axes:{length:4,0:null,1:null,2:0,3:1},buttons:{length:7,0:1,1:2,2:null,3:0,4:3,5:4,6:null},gripTransform:{position:[0,-.02,.04,1],orientation:[.11*Math.PI,0,0,1]}},ke={mapping:"xr-standard",profiles:["htc-vive","generic-trigger-squeeze-touchpad"],displayProfiles:{"HTC Vive":["htc-vive","generic-trigger-squeeze-touchpad"],"HTC Vive DVT":["htc-vive","generic-trigger-squeeze-touchpad"],"Valve Index":["valve-index","generic-trigger-squeeze-touchpad-thumbstick"]},buttons:{length:3,0:1,1:2,2:0},gripTransform:{position:[0,0,.05,1]},targetRayTransform:{orientation:[-.08*Math.PI,0,0,1]},userAgentOverrides:{Firefox:{axes:{invert:[1,3]}}}},ze={mapping:"xr-standard",profiles:["samsung-gearvr","generic-trigger-touchpad"],buttons:{length:3,0:1,1:null,2:0},gripTransform:{orientation:[.11*Math.PI,0,0,1]}},Ve={mapping:"xr-standard",profiles:["samsung-odyssey","microsoft-mixed-reality","generic-trigger-squeeze-touchpad-thumbstick"],buttons:{length:4,0:1,1:0,2:2,3:4},gripTransform:{position:[0,-.02,.04,1],orientation:[.11*Math.PI,0,0,1]}},Ue={mapping:"xr-standard",profiles:["microsoft-mixed-reality","generic-trigger-squeeze-touchpad-thumbstick"],buttons:{length:4,0:1,1:0,2:2,3:4},gripTransform:{position:[0,-.02,.04,1],orientation:[.11*Math.PI,0,0,1]}},He={"Daydream Controller":{mapping:"",profiles:["google-daydream","generic-trigger-touchpad"],buttons:{length:3,0:null,1:null,2:0}},"Gear VR Controller":ze,"HTC Vive Focus Controller":{mapping:"xr-standard",profiles:["htc-vive-focus","generic-trigger-touchpad"],buttons:{length:3,0:1,1:null,2:0}},"Oculus Go Controller":Ge,"Oculus Touch (Right)":Qe,"Oculus Touch (Left)":Qe,"OpenVR Gamepad":ke,"Spatial Controller (Spatial Interaction Source) 045E-065A":Ue,"Spatial Controller (Spatial Interaction Source) 045E-065D":Ve,"Windows Mixed Reality (Right)":Ue,"Windows Mixed Reality (Left)":Ue};const Xe=f(.155,-.465,-.15),We=f(-.155,-.465,-.15),je=f(0,0,-.25),qe=f(0,0,.05),Ye=f(-.08,.14,.08),Ze=.4,Je=.4,Ke=.61,$e=.175,et=.12,tt=.87,it=180/Math.PI;class rt{constructor(){this.hand="right",this.headElbowOffset=Xe,this.controllerQ=M(),this.lastControllerQ=M(),this.headQ=M(),this.headPos=u(),this.elbowPos=u(),this.wristPos=u(),this.time=null,this.lastTime=null,this.rootQ=M(),this.position=u()}setHandedness(e){this.hand!=e&&(this.hand=e,"left"==this.hand?this.headElbowOffset=We:this.headElbowOffset=Xe)}update(e,t){this.time=X(),e&&(B(this.lastControllerQ,this.controllerQ),B(this.controllerQ,e)),t&&(h(this.headPos,t),c(this.headQ,t));let i=this.getHeadYawOrientation_(),r=this.quatAngle_(this.lastControllerQ,this.controllerQ);r/((this.time-this.lastTime)/1e3)>Ke?_(this.rootQ,this.rootQ,i,Math.min(r/$e,1)):B(this.rootQ,i);let s=f(0,0,-1);E(s,s,this.controllerQ);let n=y(s,[0,1,0]),a=this.clamp_((n-et)/tt,0,1),o=R(this.rootQ);F(o,o),x(o,o,this.controllerQ);let l=this.elbowPos;m(l,this.headPos),g(l,l,this.headElbowOffset);let A=p(Ye);v(A,A,a),g(l,l,A);let d=this.quatAngle_(o,M())*it,u=(1-Math.pow(d/180,4))*(Ze+(1-Ze)*a*Je),w=M();_(w,w,o,u);let b=F(M(),w),S=R(o);x(S,S,b);let T=this.wristPos;m(T,qe),E(T,T,w),g(T,T,je),E(T,T,S),g(T,T,l);let P=p(Ye);v(P,P,a),g(this.position,this.wristPos,P),E(this.position,this.position,this.rootQ),this.lastTime=this.time}getPosition(){return this.position}getHeadYawOrientation_(){let e=u();return function(e,t,i){function r(e,t,i){return e<t?t:e>i?i:e}var s=t[0]*t[0],n=t[1]*t[1],a=t[2]*t[2],o=t[3]*t[3];if("XYZ"===i)e[0]=Math.atan2(2*(t[0]*t[3]-t[1]*t[2]),o-s-n+a),e[1]=Math.asin(r(2*(t[0]*t[2]+t[1]*t[3]),-1,1)),e[2]=Math.atan2(2*(t[2]*t[3]-t[0]*t[1]),o+s-n-a);else if("YXZ"===i)e[0]=Math.asin(r(2*(t[0]*t[3]-t[1]*t[2]),-1,1)),e[1]=Math.atan2(2*(t[0]*t[2]+t[1]*t[3]),o-s-n+a),e[2]=Math.atan2(2*(t[0]*t[1]+t[2]*t[3]),o-s+n-a);else if("ZXY"===i)e[0]=Math.asin(r(2*(t[0]*t[3]+t[1]*t[2]),-1,1)),e[1]=Math.atan2(2*(t[1]*t[3]-t[2]*t[0]),o-s-n+a),e[2]=Math.atan2(2*(t[2]*t[3]-t[0]*t[1]),o-s+n-a);else if("ZYX"===i)e[0]=Math.atan2(2*(t[0]*t[3]+t[2]*t[1]),o-s-n+a),e[1]=Math.asin(r(2*(t[1]*t[3]-t[0]*t[2]),-1,1)),e[2]=Math.atan2(2*(t[0]*t[1]+t[2]*t[3]),o+s-n-a);else if("YZX"===i)e[0]=Math.atan2(2*(t[0]*t[3]-t[2]*t[1]),o-s+n-a),e[1]=Math.atan2(2*(t[1]*t[3]-t[0]*t[2]),o+s-n-a),e[2]=Math.asin(r(2*(t[0]*t[1]+t[2]*t[3]),-1,1));else{if("XZY"!==i)return void console.log("No order given for quaternion to euler conversion.");e[0]=Math.atan2(2*(t[0]*t[3]+t[1]*t[2]),o-s+n-a),e[1]=Math.atan2(2*(t[0]*t[2]+t[1]*t[3]),o+s-n-a),e[2]=Math.asin(r(2*(t[2]*t[3]-t[0]*t[1]),-1,1))}}(e,this.headQ,"YXZ"),function(e,t,i,r){let s=.5*Math.PI/180;t*=s,i*=s,r*=s;let n=Math.sin(t),a=Math.cos(t),o=Math.sin(i),l=Math.cos(i),A=Math.sin(r),h=Math.cos(r);return e[0]=n*l*h-a*o*A,e[1]=a*o*h+n*l*A,e[2]=a*l*A-n*o*h,e[3]=a*l*h+n*o*A,e}(M(),0,e[1]*it,0)}clamp_(e,t,i){return Math.min(Math.max(e,t),i)}quatAngle_(e,t){let i=[0,0,-1],r=[0,0,-1];return E(i,i,e),E(r,r,t),function(e,t){let i=f(e[0],e[1],e[2]),r=f(t[0],t[1],t[2]);w(i,i),w(r,r);let s=y(i,r);return s>1?0:s<-1?Math.PI:Math.acos(s)}(i,r)}}const st=Symbol("@@webxr-polyfill/XRRemappedGamepad"),nt={pressed:!1,touched:!1,value:0};Object.freeze(nt);class at{constructor(e,t,i){if(i||(i={}),i.userAgentOverrides)for(let e in i.userAgentOverrides)if(navigator.userAgent.includes(e)){let t=i.userAgentOverrides[e];for(let e in t)e in i?Object.assign(i[e],t[e]):i[e]=t[e];break}let r=new Array(i.axes&&i.axes.length?i.axes.length:e.axes.length),s=new Array(i.buttons&&i.buttons.length?i.buttons.length:e.buttons.length),a=null;if(i.gripTransform){let e=i.gripTransform.orientation||[0,0,0,1];A(a=n(),P(e,e),i.gripTransform.position||[0,0,0])}let o=null;if(i.targetRayTransform){let e=i.targetRayTransform.orientation||[0,0,0,1];A(o=n(),P(e,e),i.targetRayTransform.position||[0,0,0])}let l=i.profiles;i.displayProfiles&&t.displayName in i.displayProfiles&&(l=i.displayProfiles[t.displayName]),this[st]={gamepad:e,map:i,profiles:l||[e.id],mapping:i.mapping||e.mapping,axes:r,buttons:s,gripTransform:a,targetRayTransform:o},this._update()}_update(){let e=this[st].gamepad,t=this[st].map,i=this[st].axes;for(let r=0;r<i.length;++r)t.axes&&r in t.axes?null===t.axes[r]?i[r]=0:i[r]=e.axes[t.axes[r]]:i[r]=e.axes[r];if(t.axes&&t.axes.invert)for(let e of t.axes.invert)e<i.length&&(i[e]*=-1);let r=this[st].buttons;for(let i=0;i<r.length;++i)t.buttons&&i in t.buttons?null===t.buttons[i]?r[i]=nt:r[i]=e.buttons[t.buttons[i]]:r[i]=e.buttons[i]}get id(){return""}get _profiles(){return this[st].profiles}get index(){return-1}get connected(){return this[st].gamepad.connected}get timestamp(){return this[st].gamepad.timestamp}get mapping(){return this[st].mapping}get axes(){return this[st].axes}get buttons(){return this[st].buttons}get hapticActuators(){return this[st].gamepad.hapticActuators}}class ot{constructor(e,t,i=0,r=-1){this.polyfill=e,this.display=t,this.nativeGamepad=null,this.gamepad=null,this.inputSource=new Se(this),this.lastPosition=u(),this.emulatedPosition=!1,this.basePoseMatrix=n(),this.outputMatrix=n(),this.primaryButtonIndex=i,this.primaryActionPressed=!1,this.primarySqueezeButtonIndex=r,this.primarySqueezeActionPressed=!1,this.handedness="",this.targetRayMode="gaze",this.armModel=null}get profiles(){return this.gamepad?this.gamepad._profiles:[]}updateFromGamepad(e){this.nativeGamepad!==e&&(this.nativeGamepad=e,this.gamepad=e?new at(e,this.display,He[e.id]):null),this.handedness=""===e.hand?"none":e.hand,this.gamepad&&this.gamepad._update(),e.pose?(this.targetRayMode="tracked-pointer",this.emulatedPosition=!e.pose.hasPosition):""===e.hand&&(this.targetRayMode="gaze",this.emulatedPosition=!1)}updateBasePoseMatrix(){if(this.nativeGamepad&&this.nativeGamepad.pose){let e=this.nativeGamepad.pose,t=e.position,i=e.orientation;if(!t&&!i)return;t?(this.lastPosition[0]=t[0],this.lastPosition[1]=t[1],this.lastPosition[2]=t[2]):e.hasPosition?t=this.lastPosition:(this.armModel||(this.armModel=new rt),this.armModel.setHandedness(this.nativeGamepad.hand),this.armModel.update(i,this.polyfill.getBasePoseMatrix()),t=this.armModel.getPosition()),A(this.basePoseMatrix,i,t)}else e=this.basePoseMatrix,t=this.polyfill.getBasePoseMatrix(),e[0]=t[0],e[1]=t[1],e[2]=t[2],e[3]=t[3],e[4]=t[4],e[5]=t[5],e[6]=t[6],e[7]=t[7],e[8]=t[8],e[9]=t[9],e[10]=t[10],e[11]=t[11],e[12]=t[12],e[13]=t[13],e[14]=t[14],e[15]=t[15];var e,t;return this.basePoseMatrix}getXRPose(e,t){switch(this.updateBasePoseMatrix(),t){case"target-ray":e._transformBasePoseMatrix(this.outputMatrix,this.basePoseMatrix),this.gamepad&&this.gamepad[st].targetRayTransform&&l(this.outputMatrix,this.outputMatrix,this.gamepad[st].targetRayTransform);break;case"grip":if(!this.nativeGamepad||!this.nativeGamepad.pose)return null;e._transformBasePoseMatrix(this.outputMatrix,this.basePoseMatrix),this.gamepad&&this.gamepad[st].gripTransform&&l(this.outputMatrix,this.outputMatrix,this.gamepad[st].gripTransform);break;default:return null}return e._adjustForOriginOffset(this.outputMatrix),new XRPose(new XRRigidTransform(this.outputMatrix),this.emulatedPosition)}}const lt=!1,At={highRefreshRate:!0},ht={oculus:1,openvr:1,"spatial controller (spatial interaction source)":1};let ct=0;class dt{constructor(e,t,i={}){if(this.mode=e,this.enabledFeatures=t,this.outputContext=null,this.immersive="immersive-vr"==e||"immersive-ar"==e,this.ended=null,this.baseLayer=null,this.id=++ct,this.modifiedCanvasLayer=!1,this.outputContext&&!lt){const e=i.renderContextType||"2d";this.renderContext=this.outputContext.canvas.getContext(e)}}}class ut extends Ne{constructor(e,t){const{canPresent:i}=t.capabilities;super(e),this.display=t,this.frame=new e.VRFrameData,this.sessions=new Map,this.immersiveSession=null,this.canPresent=i,this.baseModelMatrix=n(),this.gamepadInputSources={},this.tempVec3=new Float32Array(3),this.onVRDisplayPresentChange=this.onVRDisplayPresentChange.bind(this),e.window.addEventListener("vrdisplaypresentchange",this.onVRDisplayPresentChange),this.CAN_USE_GAMEPAD=e.navigator&&"getGamepads"in e.navigator,this.HAS_BITMAP_SUPPORT=Te(e)}get depthNear(){return this.display.depthNear}set depthNear(e){this.display.depthNear=e}get depthFar(){return this.display.depthFar}set depthFar(e){this.display.depthFar=e}onBaseLayerSet(e,t){const i=this.sessions.get(e),r=t.context.canvas;if(i.immersive){const e=this.display.getEyeParameters("left"),s=this.display.getEyeParameters("right");r.width=2*Math.max(e.renderWidth,s.renderWidth),r.height=Math.max(e.renderHeight,s.renderHeight),this.display.requestPresent([{source:r,attributes:At}]).then(()=>{lt||this.global.document.body.contains(r)||(i.modifiedCanvasLayer=!0,this.global.document.body.appendChild(r),Pe(r)),i.baseLayer=t})}else i.baseLayer=t}isSessionSupported(e){return"immersive-ar"!=e&&("immersive-vr"!=e||!1!==this.canPresent)}isFeatureSupported(e){switch(e){case"viewer":case"local":case"local-floor":return!0;case"bounded":case"unbounded":default:return!1}}async requestSession(e,t){if(!this.isSessionSupported(e))return Promise.reject();let i="immersive-vr"==e;if(i){const e=this.global.document.createElement("canvas");if(!lt){e.getContext("webgl")}await this.display.requestPresent([{source:e,attributes:At}])}const r=new dt(e,t,{renderContextType:this.HAS_BITMAP_SUPPORT?"bitmaprenderer":"2d"});return this.sessions.set(r.id,r),i&&(this.immersiveSession=r,this.dispatchEvent("@@webxr-polyfill/vr-present-start",r.id)),Promise.resolve(r.id)}requestAnimationFrame(e){return this.display.requestAnimationFrame(e)}getPrimaryButtonIndex(e){let t=0,i=e.id.toLowerCase();for(let e in ht)if(i.includes(e)){t=ht[e];break}return Math.min(t,e.buttons.length-1)}onFrameStart(e,t){this.display.depthNear=t.depthNear,this.display.depthFar=t.depthFar,this.display.getFrameData(this.frame);const i=this.sessions.get(e);if(i.immersive&&this.CAN_USE_GAMEPAD){let e=this.gamepadInputSources;this.gamepadInputSources={};let t=this.global.navigator.getGamepads();for(let r=0;r<t.length;++r){let s=t[r];if(s&&s.displayId>0){let t=e[r];if(t||(t=new ot(this,this.display,this.getPrimaryButtonIndex(s))),t.updateFromGamepad(s),this.gamepadInputSources[r]=t,-1!=t.primaryButtonIndex){let e=s.buttons[t.primaryButtonIndex].pressed;e&&!t.primaryActionPressed?this.dispatchEvent("@@webxr-polyfill/input-select-start",{sessionId:i.id,inputSource:t.inputSource}):!e&&t.primaryActionPressed&&this.dispatchEvent("@@webxr-polyfill/input-select-end",{sessionId:i.id,inputSource:t.inputSource}),t.primaryActionPressed=e}if(-1!=t.primarySqueezeButtonIndex){let e=s.buttons[t.primarySqueezeButtonIndex].pressed;e&&!t.primarySqueezeActionPressed?this.dispatchEvent("@@webxr-polyfill/input-squeeze-start",{sessionId:i.id,inputSource:t.inputSource}):!e&&t.primarySqueezeActionPressed&&this.dispatchEvent("@@webxr-polyfill/input-squeeze-end",{sessionId:i.id,inputSource:t.inputSource}),t.primarySqueezeActionPressed=e}}}}if(!lt&&!i.immersive&&i.baseLayer){const e=i.baseLayer.context.canvas;d(this.frame.leftProjectionMatrix,t.inlineVerticalFieldOfView,e.width/e.height,t.depthNear,t.depthFar)}}onFrameEnd(e){const t=this.sessions.get(e);if(!t.ended&&t.baseLayer){if(t.outputContext&&(!t.immersive||this.display.capabilities.hasExternalDisplay)){const e=t.immersive&&this.display.capabilities.hasExternalDisplay,i=t.baseLayer.context.canvas,r=e?i.width/2:i.width,s=i.height;if(!lt){const e=t.outputContext.canvas,n=e.width,a=e.height,o=t.renderContext;this.HAS_BITMAP_SUPPORT?i.transferToImageBitmap?o.transferFromImageBitmap(i.transferToImageBitmap()):this.global.createImageBitmap(i,0,0,r,s,{resizeWidth:n,resizeHeight:a}).then(e=>o.transferFromImageBitmap(e)):o.drawImage(i,0,0,r,s,0,0,n,a)}}t.immersive&&t.baseLayer&&this.display.submitFrame()}}cancelAnimationFrame(e){this.display.cancelAnimationFrame(e)}async endSession(e){const t=this.sessions.get(e);if(!t.ended)return t.immersive?this.display.exitPresent():void(t.ended=!0)}doesSessionSupportReferenceSpace(e,t){const i=this.sessions.get(e);return!i.ended&&i.enabledFeatures.has(t)}requestStageBounds(){if(this.display.stageParameters){const e=this.display.stageParameters.sizeX,t=this.display.stageParameters.sizeZ,i=[];return i.push(-e/2),i.push(-t/2),i.push(e/2),i.push(-t/2),i.push(e/2),i.push(t/2),i.push(-e/2),i.push(t/2),i}return null}async requestFrameOfReferenceTransform(e,t){return("local-floor"===e||"bounded-floor"===e)&&this.display.stageParameters&&this.display.stageParameters.sittingToStandingTransform?this.display.stageParameters.sittingToStandingTransform:null}getProjectionMatrix(e){if("left"===e)return this.frame.leftProjectionMatrix;if("right"===e)return this.frame.rightProjectionMatrix;if("none"===e)return this.frame.leftProjectionMatrix;throw new Error("eye must be of type 'left' or 'right'")}getViewport(e,t,i,r){const s=this.sessions.get(e),{width:n,height:a}=i.context.canvas;if(!s.immersive)return r.x=r.y=0,r.width=n,r.height=a,!0;if("left"===t||"none"===t)r.x=0;else{if("right"!==t)return!1;r.x=n/2}return r.y=0,r.width=n/2,r.height=a,!0}getBasePoseMatrix(){let{position:e,orientation:t}=this.frame.pose;return e||t?(e||((e=this.tempVec3)[0]=e[1]=e[2]=0),A(this.baseModelMatrix,t,e),this.baseModelMatrix):this.baseModelMatrix}getBaseViewMatrix(e){if("left"===e||"none"===e)return this.frame.leftViewMatrix;if("right"===e)return this.frame.rightViewMatrix;throw new Error("eye must be of type 'left' or 'right'")}getInputSources(){let e=[];for(let t in this.gamepadInputSources)e.push(this.gamepadInputSources[t].inputSource);return e}getInputPose(e,t,i){if(!t)return null;for(let r in this.gamepadInputSources){let s=this.gamepadInputSources[r];if(s.inputSource===e)return s.getXRPose(t,i)}return null}onWindowResize(){}onVRDisplayPresentChange(e){this.display.isPresenting||this.sessions.forEach(e=>{if(e.immersive&&!e.ended){if(e.modifiedCanvasLayer){const t=e.baseLayer.context.canvas;document.body.removeChild(t),t.setAttribute("style","")}this.immersiveSession===e&&(this.immersiveSession=null),this.dispatchEvent("@@webxr-polyfill/vr-present-end",e.id)}})}}const pt=!1;let ft=0;class mt{constructor(e,t){this.mode=e,this.enabledFeatures=t,this.ended=null,this.baseLayer=null,this.id=++ft}}const gt=async function(e,t){if(t.webvr){let t=await async function(e){let t=null;if("getVRDisplays"in e.navigator)try{const i=await e.navigator.getVRDisplays();i&&i.length&&(t=new ut(e,i[0]))}catch(e){}return t}(e);if(t)return t}let i=(e=>{var t=!1;return Be=e.navigator.userAgent||e.navigator.vendor||e.opera,(/(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows ce|xda|xiino/i.test(Be)||/1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s\-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|\-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw\-(n|u)|c55\/|capi|ccwa|cdm\-|cell|chtm|cldc|cmd\-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc\-s|devi|dica|dmob|do(c|p)o|ds(12|\-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(\-|_)|g1 u|g560|gene|gf\-5|g\-mo|go(\.w|od)|gr(ad|un)|haie|hcit|hd\-(m|p|t)|hei\-|hi(pt|ta)|hp( i|ip)|hs\-c|ht(c(\-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i\-(20|go|ma)|i230|iac( |\-|\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\/)|klon|kpt |kwc\-|kyo(c|k)|le(no|xi)|lg( g|\/(k|l|u)|50|54|\-[a-w])|libw|lynx|m1\-w|m3ga|m50\/|ma(te|ui|xo)|mc(01|21|ca)|m\-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(\-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)\-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|\-([1-8]|c))|phil|pire|pl(ay|uc)|pn\-2|po(ck|rt|se)|prox|psio|pt\-g|qa\-a|qc(07|12|21|32|60|\-[2-7]|i\-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h\-|oo|p\-)|sdk\/|se(c(\-|0|1)|47|mc|nd|ri)|sgh\-|shar|sie(\-|m)|sk\-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h\-|v\-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl\-|tdg\-|tel(i|m)|tim\-|t\-mo|to(pl|sh)|ts(70|m\-|m3|m5)|tx\-9|up(\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|\-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(\-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas\-|your|zeto|zte\-/i.test(Be.substr(0,4)))&&(t=!0),t})(e);return i&&t.cardboard||!i&&t.allowCardboardOnDesktop?(e.VRFrameData||(e.VRFrameData=function(){this.rightViewMatrix=new Float32Array(16),this.leftViewMatrix=new Float32Array(16),this.rightProjectionMatrix=new Float32Array(16),this.leftProjectionMatrix=new Float32Array(16),this.pose=null}),new class extends ut{constructor(e,t){const i=new Oe(t||{});super(e,i),this.display=i,this.frame={rightViewMatrix:new Float32Array(16),leftViewMatrix:new Float32Array(16),rightProjectionMatrix:new Float32Array(16),leftProjectionMatrix:new Float32Array(16),pose:null,timestamp:null}}}(e,t.cardboardConfig)):new class extends Ne{constructor(e){super(e),this.sessions=new Map,this.projectionMatrix=n(),this.identityMatrix=n()}onBaseLayerSet(e,t){this.sessions.get(e).baseLayer=t}isSessionSupported(e){return"inline"==e}isFeatureSupported(e){switch(e){case"viewer":return!0;default:return!1}}async requestSession(e,t){if(!this.isSessionSupported(e))return Promise.reject();const i=new mt(e,t);return this.sessions.set(i.id,i),Promise.resolve(i.id)}requestAnimationFrame(e){return window.requestAnimationFrame(e)}cancelAnimationFrame(e){window.cancelAnimationFrame(e)}onFrameStart(e,t){if(pt)return;const i=this.sessions.get(e);if(i.baseLayer){const e=i.baseLayer.context.canvas;d(this.projectionMatrix,t.inlineVerticalFieldOfView,e.width/e.height,t.depthNear,t.depthFar)}}onFrameEnd(e){}async endSession(e){this.sessions.get(e).ended=!0}doesSessionSupportReferenceSpace(e,t){const i=this.sessions.get(e);return!i.ended&&i.enabledFeatures.has(t)}requestStageBounds(){return null}async requestFrameOfReferenceTransform(e,t){return null}getProjectionMatrix(e){return this.projectionMatrix}getViewport(e,t,i,r){const{width:s,height:n}=i.context.canvas;return this.sessions.get(e),r.x=r.y=0,r.width=s,r.height=n,!0}getBasePoseMatrix(){return this.identityMatrix}getBaseViewMatrix(e){return this.identityMatrix}getInputSources(){return[]}getInputPose(e,t,i){return null}onWindowResize(){}}(e)},vt={global:e,webvr:!0,cardboard:!0,cardboardConfig:null,allowCardboardOnDesktop:!1},wt=["navigator","HTMLCanvasElement","WebGLRenderingContext"];return class{constructor(e={}){this.config=Object.freeze(Object.assign({},vt,e)),this.global=this.config.global,this.nativeWebXR="xr"in this.global.navigator,this.injected=!1,this.nativeWebXR?this._injectCompatibilityShims(this.global):this._injectPolyfill(this.global)}_injectPolyfill(e){if(!wt.every(t=>!!e[t]))throw new Error(`Global must have the following attributes : ${wt}`);for(const t of Object.keys(_e))void 0!==e[t]?console.warn(`${t} already defined on global.`):e[t]=_e[t];Fe(e.WebGLRenderingContext)&&(Re(e.HTMLCanvasElement),e.OffscreenCanvas&&Re(e.OffscreenCanvas),e.WebGL2RenderingContext&&Fe(e.WebGL2RenderingContext),window.isSecureContext||console.warn("WebXR Polyfill Warning:\nThis page is not running in a secure context (https:// or localhost)!\nThis means that although the page may be able to use the WebXR Polyfill it will\nnot be able to use native WebXR implementations, and as such will not be able to\naccess dedicated VR or AR hardware, and will not be able to take advantage of\nany performance improvements a native WebXR implementation may offer. Please\nhost this content on a secure origin for the best user experience.\n")),this.injected=!0,this._patchNavigatorXR()}_patchNavigatorXR(){let e=gt(this.global,this.config);this.xr=new _e.XRSystem(e),Object.defineProperty(this.global.navigator,"xr",{value:this.xr,configurable:!0})}_injectCompatibilityShims(e){if(!wt.every(t=>!!e[t]))throw new Error(`Global must have the following attributes : ${wt}`);if(e.navigator.xr&&"supportsSession"in e.navigator.xr&&!("isSessionSupported"in e.navigator.xr)){let t=e.navigator.xr.supportsSession;e.navigator.xr.isSessionSupported=function(e){return t.call(this,e).then(()=>!0).catch(()=>!1)},e.navigator.xr.supportsSession=function(e){return console.warn("navigator.xr.supportsSession() is deprecated. Please call navigator.xr.isSessionSupported() instead and check the boolean value returned when the promise resolves."),t.call(this,e)}}}}});
