/*
 * Copyright 2016 Google Inc. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import defaultConfig from './rollup.config';
import uglify from 'rollup-plugin-uglify';
import { minify } from 'uglify-es';

export default Object.assign({}, defaultConfig, {
  output: {
    file: './build/webxr-polyfill.min.js',
    format: defaultConfig.output.format,
    name: defaultConfig.output.name,
    banner: defaultConfig.output.banner,
  },
  plugins: [...defaultConfig.plugins, uglify({
    output: {
      // Preserve license commenting in minified build:
      // https://github.com/TrySound/rollup-plugin-uglify/blob/master/README.md#comments
      comments: function(node, comment) {
        const { value, type } = comment;
        if (type == "comment2") {
          // multiline comment
          return /@preserve|@license|@cc_on/i.test(value);
        }
      }
    }
  }, minify)],
});
