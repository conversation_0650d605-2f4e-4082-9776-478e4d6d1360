{"name": "@lookingglass/webxr", "type": "module", "files": ["dist"], "main": "./dist/webxr.umd.cjs", "module": "./dist/webxr.js", "exports": {".": {"import": "./dist/webxr.js", "require": "./dist/webxr.umd.cjs"}}, "version": "0.6.0", "license": "Apache-2.0", "private": false, "description": "Official WebXR implementation for Looking Glass Holographic Displays.", "repository": {"type": "git", "url": "git+https://github.com/Looking-Glass/looking-glass-webxr.git"}, "scripts": {"dev": "vite build --mode dev && vite --mode dev --host", "build": "vite build --mode build", "production": "vite build --mode dev && vite build --mode build"}, "devDependencies": {"@babylonjs/core": "^5.43.2", "@babylonjs/loaders": "^5.43.2", "@babylonjs/materials": "^5.43.2", "@rollup/plugin-replace": "^5.0.1", "@rollup/plugin-typescript": "^9.0.2", "rollup-plugin-license": "^3.5.1", "rollup-plugin-typescript-paths": "^1.4.0", "three": "^0.146.0", "tslib": "^2.4.1", "typescript": "^4.8.4", "vite": "^3.1.2", "webgl-strict-types": "^1.0.5"}, "dependencies": {"@lookingglass/webxr-polyfill": "^0.1.1", "gl-matrix": "^2.8.1", "holoplay-core": "^0.0.11"}, "bugs": {"url": "https://github.com/Looking-Glass/looking-glass-webxr/issues"}, "homepage": "https://github.com/Looking-Glass/looking-glass-webxr#readme", "keywords": ["hologram", "3D", "looking", "glass"], "author": "Looking Glass Factory"}