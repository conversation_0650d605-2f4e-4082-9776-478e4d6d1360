export class LookingGlassGestureHandler {
    constructor() {
        this.active = false;

        // Set up message event listener for gestures
        window.addEventListener('message', this.onMessage.bind(this));
        console.log('[LookingGlassGestureHandler] Initialized');
    }

    onMessage(event) {
        // Handle gesture messages from parent window
        if (event.data?.action === 'PROCESS_GESTURE') {
            this.processGesture(event.data.gesture);
        }
    }

    processGesture(gesture) {
        if (!gesture) return;

        const config = window.lookingGlassConfig;
        if (!config) return;

        switch (gesture.type) {
            case 'rotate':
                this.handleRotationGesture(gesture, config);
                break;

            case 'scale':
                this.handleScalingGesture(gesture, config);
                break;
        }
    }

    handleRotationGesture(gesture, config) {
        if (!gesture.rotation) return;

        // Apply rotation to the Looking Glass view
        config.trackballX += gesture.rotation.x * 0.03;
        config.trackballY += gesture.rotation.y * 0.03;
    }

    handleScalingGesture(gesture, config) {
        if (!gesture.scale) return;

        // Apply scaling to the Looking Glass view (adjust target diameter)
        if (gesture.scale > 1) {
            config.targetDiam *= 0.95;
        } else {
            config.targetDiam *= 1.05;
        }
    }

    dispose() {
        window.removeEventListener('message', this.onMessage);
        this.active = false;
    }
}