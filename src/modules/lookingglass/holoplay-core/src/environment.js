/**
 * Environment detection utilities for HoloPlayCore
 */

/**
 * Check if the code is running in a Node.js environment
 * @returns {boolean} True if running in Node.js, false otherwise
 */
export function isNodeEnvironment() {
  return typeof process !== 'undefined' &&
    typeof process.versions === 'object' &&
    process.versions !== null &&
    typeof process.versions.node === 'string' &&
    // Additional check to ensure we're not in a browser environment
    typeof window === 'undefined' &&
    typeof document === 'undefined';
}

/**
 * Check if the code is running in a browser environment
 * @returns {boolean} True if running in a browser, false otherwise
 */
export function isBrowserEnvironment() {
  return typeof window !== 'undefined' && typeof document !== 'undefined';
}

/**
 * Get the appropriate WebSocket implementation for the current environment
 * @returns {WebSocket} The WebSocket implementation
 */
export function getWebSocketImplementation() {
  if (isNodeEnvironment()) {
    try {
      // In Node.js, use the 'ws' package
      return require('ws');
    } catch (error) {
      console.error('Failed to load WebSocket implementation for Node.js:', error);
      throw new Error('WebSocket implementation not available in Node.js environment');
    }
  } else if (isBrowserEnvironment()) {
    // In browser, use the native WebSocket
    return window.WebSocket;
  } else {
    throw new Error('Unknown environment, WebSocket implementation not available');
  }
}
