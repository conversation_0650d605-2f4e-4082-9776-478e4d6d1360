{"name": "holoplay-core", "version": "0.0.11", "description": "A library that works with Looking Glass HoloPlay Service", "main": "dist/holoplaycore.js", "module": "dist/holoplaycore.module.js", "scripts": {"build": "rollup --config && terser dist/holoplaycore.js -m --output dist/holoplaycore.min.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["looking glass", "holograms", "holographic"], "author": "Looking Glass Factory", "license": "SEE LICENSE IN LICENSE.txt", "dependencies": {"cbor-js": "^0.1.0", "ws": "^7.2.0"}, "devDependencies": {"@rollup/plugin-commonjs": "^11.0.2", "@rollup/plugin-node-resolve": "^7.1.1", "rollup": "^2.1.0", "terser": "^4.6.7"}}