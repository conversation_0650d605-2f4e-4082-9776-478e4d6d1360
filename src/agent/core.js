/**
 * LangGraph-based Agent Service for Hologram Avatar
 * Core agent service with generic state management for any type of agent
 */

import { StateGraph, Annotation, START, END, MemorySaver } from "@langchain/langgraph/web";
import { createReactAgent } from "@langchain/langgraph/prebuilt";
import { ChatPromptTemplate } from "@langchain/core/prompts";
import { createLogger, LogLevel } from '../utils/logger.js';
import { VLLMChatModel } from './models/VLLMChatModel.js';
import { AliyunBailianChatModel } from './models/AliyunBailianChatModel.js';
import { autoRegisterTools } from './tools/index.js';
import {
    createLangGraphSystemPrompt,
    validateLangGraphOptions,
    createLangGraphUserMessage,
    getLangGraphPromptLogger,
    logLangGraphResponse,
    logSystemPromptHierarchical,
    getMemoryContext,
    addToMemory
} from './prompts/langraph.js';
import { endpoints } from '../config/endpoints.ts';
import { getDownloadServerUrl } from '../utils/portManager.js';
import { ToolNode } from '@langchain/langgraph/prebuilt';
import { llmAPI } from '@/media/api/llmAPI.ts';

// Define unified conversation state schema with LangGraph
const ConversationState = Annotation.Root({
    // Core message history
    messages: Annotation({
        reducer: (x, y) => x.concat(y),
        default: () => []
    }),

    // Generic agent state management
    agentState: Annotation({
        reducer: (x, y) => ({ ...x, ...y }),
        default: () => ({
            mode: 'idle', // 'idle', 'listening', 'speaking', 'processing'
            emotion: 'neutral',
            animation: null,
            language: 'english',
            isListening: false,
            isSpeaking: false,
            waitingForResponse: false
        })
    }),

    // Session context
    sessionContext: Annotation({
        reducer: (x, y) => ({ ...x, ...y }),
        default: () => ({
            sessionId: 'default',
            threadId: null,
            metadata: null,
            audioInput: null,
            videoFrames: null
        })
    }),

    // Processing metadata
    processingMeta: Annotation({
        reducer: (x, y) => ({ ...x, ...y }),
        default: () => ({
            inputType: 'text',
            hasBargeIn: false,
            timestamp: Date.now(),
            processingComplete: false,
            streamingActive: false,
            lastResponse: null
        })
    })
});

/**
 * Core LangGraph Agent Service - Generic implementation
 * Uses LangGraph's conditional branching and built-in state management
 */

// Create logger with DEBUG level for enhanced error diagnosis
const logger = createLogger('LangGraphAgent', LogLevel.DEBUG);
logger.debug('🔍 [ConnectionDebug] LangGraphAgent logger initialized with DEBUG level');

// Define LangGraph state annotation for agent
const AgentState = Annotation.Root({
    // Core conversation state
    messages: Annotation({
        reducer: (x, y) => x.concat(y),
        default: () => []
    }),
    userInput: Annotation({ default: () => null }),
    agentResponse: Annotation({ default: () => null }),

    // Generic agent mode
    agentMode: Annotation({
        reducer: (x, y) => y ?? x,
        default: () => 'idle' // idle, listening, processing, speaking, error
    }),

    // Session and context
    sessionId: Annotation({ default: () => 'default' }),
    language: Annotation({ default: () => 'english' }),

    // Tool and memory state
    toolResults: Annotation({
        reducer: (x, y) => x.concat(y),
        default: () => []
    }),
    memoryContext: Annotation({ default: () => null }),

    // Streaming state
    isStreaming: Annotation({ default: () => false }),
    shouldPauseTTS: Annotation({ default: () => false }),

    // Error handling
    lastError: Annotation({ default: () => null })
});

/**
 * Core LangGraph Agent Service - Generic state management with streaming
 */
export class LangGraphAgentService {
    constructor(options = {}) {
        this.logger = createLogger('LangGraphAgentService', LogLevel.DEBUG);
        this.logger.debug('🔍 [ConnectionDebug] Creating LangGraphAgentService with enhanced logging');
        this.options = options;

        // Initialize prompt logger from LangGraph module for formatted logging
        this.promptLogger = getLangGraphPromptLogger();

        // Core configuration
        this.temperature = options.temperature || 0.7;
        this.maxTokens = options.maxTokens || 2048;

        // Store minimal services - tools are self-sufficient
        this._baseServices = {
            onStateChange: options.onStateChange || (() => { }),
            onConversationUpdate: options.onConversationUpdate || (() => { }),
            ...options.services
        };
        this.services = { ...this._baseServices };

        // Initialize memory and workflow
        this.checkpointer = new MemorySaver();
        this.agent = null;
        this.model = null;
        this.tools = [];

        // StreamProcessor for LangGraph native streaming
        this.streamProcessor = null;

        // Generic state management callbacks
        this.stateCallbacks = {
            onStateUpdate: options.onStateUpdate || (() => { }),
            onModeChange: options.onModeChange || (() => { }),
            onProcessingStart: options.onProcessingStart || (() => { }),
            onProcessingEnd: options.onProcessingEnd || (() => { })
        };

        // Memory management
        this.memoryManager = null;
        this.memoryOptions = {
            maxHistoryLength: options.maxHistoryLength || 10,
            summarizeAfter: options.summarizeAfter || 20,
            enableSummary: options.enableSummary !== false
        };

        this._initialized = false;
        this.logger.info('Core LangGraph Agent Service created');

        // Generic callbacks for external integrations
        this.callbacks = {
            onModeChange: options.onModeChange || (() => { }),
            onStateUpdate: options.onStateUpdate || (() => { }),
            onTextChunk: options.onTextChunk || (() => { }),
            onToolCall: options.onToolCall || (() => { }),
            onProcessingStart: options.onProcessingStart || (() => { }),
            onProcessingEnd: options.onProcessingEnd || (() => { }),
            onError: options.onError || (() => { })
        };
    }

    /**
     * Initialize the service with LangGraph workflow and tools
     */
    async initialize() {
        if (this._initialized) return;

        try {
            this.logger.info('Initializing Core LangGraph Agent Service...');

            // Initialize the model
            await this._initializeModel();

            // Initialize StreamProcessor for enhanced streaming capabilities
            await this._initializeStreamProcessor();

            // Auto-register available tools
            if (this.options.autoRegisterTools !== false) {
                await this._autoRegisterTools();
            }

            // Initialize memory manager
            await this._initializeMemoryManager();

            // Create the agent using LangGraph's createReactAgent
            await this._createAgent();

            // Create LangGraph workflow with conditional branching
            await this._createLangGraphWorkflow();

            this._initialized = true;
            this.logger.info('Core LangGraph Agent Service initialized successfully');

        } catch (error) {
            this.logger.error('❌ Failed to initialize Core LangGraph Agent Service:', error);
            throw error;
        }
    }

    /**
     * Initialize the VLLMChatModel
     */
    async _initializeModel() {
        // Determine model provider
        const modelProvider = this.options.modelProvider || 'vllm';
        this.logger.debug(`🔍 [ConnectionDebug] Initializing model with provider: ${modelProvider}`);

        if (modelProvider === 'aliyun' || modelProvider === 'bailian') {
            await this._initializeAliyunBailianModel();
        } else {
            await this._initializeVLLMModel();
        }
    }

    async _initializeVLLMModel() {
        // Get vLLM endpoint
        let vllmEndpoint;
        if (this.options.vllmEndpoint) {
            vllmEndpoint = this.options.vllmEndpoint;
        } else {
            try {
                vllmEndpoint = endpoints.vllm;
                this.logger.debug('Using vLLM endpoint from endpoints config:', vllmEndpoint);
            } catch (error) {
                const downloadServerUrl = getDownloadServerUrl();
                vllmEndpoint = `${downloadServerUrl}/vllm-proxy/`;
                this.logger.debug('Using vLLM endpoint from download server URL:', vllmEndpoint);
            }
        }

        // Create VLLMChatModel with optimized settings for LangGraph
        const modelConfig = {
            model: this.options.model || 'Qwen/Qwen2.5-Omni-7B',
            temperature: this.temperature,
            maxTokens: this.maxTokens,
            vllmEndpoint: vllmEndpoint,
            streaming: true,
            verbose: false
        };

        this.logger.debug('Creating VLLMChatModel with config:', modelConfig);

        try {
            this.model = new VLLMChatModel(modelConfig);

            this.logger.debug('VLLMChatModel created:', {
                modelType: this.model.constructor.name,
                hasGenerateMethod: typeof this.model._generate === 'function',
                hasStreamMethod: typeof this.model._streamResponseChunks === 'function',
                llmType: this.model._llmType(),
                identifyingParams: this.model._identifyingParams
            });

        } catch (modelError) {
            this.logger.error('Error creating VLLMChatModel:', modelError);
            throw new Error(`Failed to create VLLMChatModel: ${modelError.message}`);
        }
    }

    async _initializeAliyunBailianModel() {
        // Create AliyunBailianChatModel with optimized settings for LangGraph
        const modelConfig = {
            temperature: this.temperature,
            maxTokens: this.maxTokens,
            realtimeEndpoint: this.options.realtimeEndpoint,
            apiKey: this.options.aliyunApiKey,
            streaming: true,
            verbose: true
        };

        // Validate API key before proceeding
        const apiKey = modelConfig.apiKey || process.env.VITE_DASHSCOPE_API_KEY || process.env.VITE_ALIYUN_API_KEY;
        if (!apiKey) {
            this.logger.error('❌ [ConnectionDebug] Missing API key - cannot create Aliyun model');
            throw new Error('Missing API key for Aliyun model');
        } else if (apiKey.length < 10) {
            this.logger.warn(`⚠️ [ConnectionDebug] API key appears invalid (length=${apiKey.length})`);
        } else {
            this.logger.debug('✅ [ConnectionDebug] API key validation passed');
        }

        this.logger.debug('🔍 [ConnectionDebug] Creating AliyunBailianChatModel with config:', {
            ...modelConfig,
            apiKey: apiKey ? `${apiKey.substring(0, 3)}...${apiKey.substring(apiKey.length - 3)}` : undefined,
            apiKeyLength: apiKey?.length
        });

        // Model now uses direct connection only (proxy mode removed)
        this.logger.info(`AliyunBailianChatModel configured for direct connection only`);

        try {
            this.model = new AliyunBailianChatModel(modelConfig);

            this.logger.debug('🔍 [ConnectionDebug] AliyunBailianChatModel created:', {
                modelType: this.model.constructor.name,
                hasGenerateMethod: typeof this.model._generate === 'function',
                hasStreamMethod: typeof this.model._streamViaWebSocket === 'function',
                llmType: this.model._llmType(),
                identifyingParams: this.model._identifyingParams
            });

        } catch (modelError) {
            this.logger.error('❌ [ConnectionDebug] Error creating AliyunBailianChatModel:', modelError);
            throw new Error(`Failed to create AliyunBailianChatModel: ${modelError.message}`);
        }

        // Test the connection with timeout and error handling
        let connectionOk = false;
        try {
            // Skip realtime testing during initialization to avoid 1011 errors
            // Realtime connection will be established when listening mode is activated
            this.logger.debug('🔍 [ConnectionDebug] Testing Aliyun connection (skipping realtime test)...');
            const testPromise = this.model.testConnection({ skipRealtimeTest: true });
            const timeoutPromise = new Promise((_, reject) =>
                setTimeout(() => reject(new Error('Model connection test timeout')), 15000)
            );

            connectionOk = await Promise.race([testPromise, timeoutPromise]);

            if (connectionOk) {
                this.logger.debug('✅ [ConnectionDebug] Model connection test passed');
            } else {
                this.logger.warn('⚠️ [ConnectionDebug] Model connection test failed, but continuing...');
            }
        } catch (error) {
            this.logger.warn('⚠️ [ConnectionDebug] Model connection test error, but continuing:', error.message);
            connectionOk = false;
        }

        this.logger.debug('🔍 [ConnectionDebug] Model initialized and tested:', {
            connectionOk,
            modelReady: !!this.model
        });
    }

    /**
     * Initialize native LangGraph streaming (no separate StreamProcessor needed)
     */
    async _initializeStreamProcessor() {
        try {
            this.logger.debug('Initializing LangGraph native streaming capabilities...');

            // LangGraph handles streaming natively through the agent's .stream() method
            // No separate StreamProcessor needed - just mark as available
            this.streamProcessor = {
                processLangGraphStream: this.processLangGraphStream.bind(this),
                isNative: true,
                streamMode: 'messages'
            };

            this.logger.info('LangGraph native streaming initialized successfully');

        } catch (error) {
            this.logger.warn('Failed to initialize LangGraph streaming:', error);
            this.streamProcessor = null;
        }
    }

    /**
     * Auto-register available tools with the workflow
     */
    async _autoRegisterTools() {
        try {
            this.logger.debug('Auto-registering available tools...');
            const registrationResult = autoRegisterTools(this.services, this.options.toolOptions || {});

            // Get the tools array from the registration result
            this.tools = registrationResult.tools || [];

            this.logger.info(`Auto-registered ${this.tools.length} tools:`, {
                toolNames: this.tools.map(tool => tool.name),
                registered: registrationResult.registered,
                failed: registrationResult.failed
            });

            // Log detailed tool information
            if (this.tools.length > 0) {
                this.logger.debug('Available tools:', this.tools.map(tool => ({
                    name: tool.name,
                    description: tool.description,
                    hasSchema: !!tool.schema
                })));
            } else {
                const hasServices = this.services.ttsService || this.services.animationController;
                if (hasServices) {
                    this.logger.warn('No tools were registered despite having services. TTS functionality may not work properly. Available services:', {
                        ttsService: !!this.services.ttsService,
                        animationController: !!this.services.animationController,
                        audioPlayer: !!this.services.audioPlayer
                    });
                } else {
                    this.logger.info('No tools registered and no services available. Tools will be registered when services are provided.');
                }
            }

        } catch (error) {
            this.logger.warn('Failed to auto-register tools:', error);
            this.tools = [];
        }
    }

    /**
     * Initialize memory manager with auto-detection
     */
    async _initializeMemoryManager() {
        if (this.memoryManager) return this.memoryManager;

        try {
            // Try to import LangChain memory first (preferred for LangGraph)
            const { LangChainMemoryManager } = await import('./memory/langchain.js');
            this.memoryManager = new LangChainMemoryManager(this.memoryOptions);

            this.logger.info('Initialized LangChain memory manager for LangGraph');
            return this.memoryManager;
        } catch (langchainError) {
            this.logger.warn('LangChain memory not available, trying Mem0:', langchainError);

            try {
                // Fallback to Mem0
                const avatarMemory = await import('./memory/mem0.js');
                const mem0Instance = avatarMemory.default;

                // Initialize Mem0 if not already initialized
                if (!mem0Instance.initialized) {
                    await mem0Instance.initialize();
                }

                this.memoryManager = mem0Instance;
                this.logger.info('Initialized Mem0 memory manager for LangGraph');
                return this.memoryManager;
            } catch (mem0Error) {
                this.logger.error('Failed to initialize any memory manager:', mem0Error);
                this.memoryManager = null;
                return null;
            }
        }
    }

    /**
     * Create the React agent using LangGraph's standard implementation
     */
    async _createAgent(options = {}) {
        // Create optimized system message with generic context
        const systemMessage = await this._createSystemMessage(options);

        this.logger.debug('Creating LangGraph React agent with optimized prompt:', {
            modelType: this.model?.constructor?.name,
            toolsCount: this.tools.length,
            hasCheckpointer: !!this.checkpointer,
            systemMessageLength: systemMessage?.length || 0,
            optimizedPrompt: true
        });

        try {
            // Use createReactAgent for standardized behavior
            this.agent = createReactAgent({
                llm: this.model,
                tools: this.tools,
                checkpointSaver: this.checkpointer,
                messageModifier: systemMessage
            });

            this.logger.debug('React agent created successfully with optimized prompt:', {
                agentType: this.agent?.constructor?.name,
                hasStreamMethod: typeof this.agent?.stream === 'function',
                hasInvokeMethod: typeof this.agent?.invoke === 'function',
                agentMethods: this.agent ? Object.getOwnPropertyNames(Object.getPrototypeOf(this.agent)) : null,
                systemMessagePreview: systemMessage.substring(0, 100) + '...'
            });

        } catch (agentError) {
            this.logger.error('Error creating LangGraph agent:', agentError);
            throw new Error(`Failed to create LangGraph agent: ${agentError.message}`);
        }
    }

    /**
     * Create system message with generic context using optimized LangGraph prompting
     */
    async _createSystemMessage(options = {}) {
        try {
            // Validate and normalize options
            const validatedOptions = validateLangGraphOptions(options);

            // Use the optimized LangGraph prompting system with dynamic tool loading
            return await createLangGraphSystemPrompt(validatedOptions);
        } catch (error) {
            this.logger.warn('Error creating optimized LangGraph system message, using fallback:', error);
            // Fallback to simple system message
            return `You are Javis, a helpful AI assistant. You can use tools to enhance conversations.`;
        }
    }

    /**
     * Create simplified LangGraph workflow using conditional branching
     */
    async _createLangGraphWorkflow() {
        // Create simplified workflow with LangGraph native patterns
        const workflow = new StateGraph(AgentState);

        // Create custom ToolNode wrapper that passes services to tools
        this.toolNode = this._createServiceAwareToolNode();

        // Add nodes for streamlined processing
        workflow
            .addNode('processInput', this._processInputNode.bind(this))
            .addNode('loadMemory', this._loadMemoryNode.bind(this))
            .addNode('generateResponse', this._generateResponseNode.bind(this))
            .addNode('executeTools', this.toolNode)
            .addNode('updateAgent', this._updateAgentNode.bind(this))
            .addNode('handleError', this._handleErrorNode.bind(this));

        // Simplified conditional branching
        workflow
            .addEdge(START, 'processInput')
            .addEdge('processInput', 'loadMemory')
            .addEdge('loadMemory', 'generateResponse')
            .addConditionalEdges(
                'generateResponse',
                this._routeAfterGeneration.bind(this),
                ['executeTools', 'updateAgent', 'handleError']
            )
            .addEdge('executeTools', 'updateAgent')
            .addEdge('updateAgent', END)
            .addEdge('handleError', END);

        // Compile with memory saver for conversation history
        this.graph = workflow.compile({
            checkpointer: this.checkpointer
        });

        this.logger.info('LangGraph workflow created with service-aware ToolNode');
    }

    /**
     * Create a ToolNode with minimal callback integration (tools are self-sufficient)
     */
    _createServiceAwareToolNode() {
        const toolNode = new ToolNode(this.tools);

        // Wrap the original invoke method to add logging and callbacks
        const originalInvoke = toolNode.invoke.bind(toolNode);

        toolNode.invoke = async (input, config = {}) => {
            // Minimal config enhancement - tools handle their own setup
            const enhancedConfig = {
                ...config,
                callbacks: {
                    ...config.callbacks,
                    handleLLMNewToken: (token) => {
                        this.logger.debug('Tool callback:', token.trim());
                    }
                }
            };

            // Log tool execution for debugging
            const lastMessage = input.messages?.[input.messages.length - 1];
            if (lastMessage?.tool_calls?.length > 0) {
                this.logger.info(`🛠️ Executing ${lastMessage.tool_calls.length} self-sufficient tools:`,
                    lastMessage.tool_calls.map(tc => tc.name).join(', '));

                // Notify callbacks about tool execution
                for (const toolCall of lastMessage.tool_calls) {
                    try {
                        this.callbacks.onToolCall?.({
                            toolCall,
                            sessionId: input.sessionId || 'default'
                        });
                    } catch (error) {
                        this.logger.warn('Tool callback notification failed:', error);
                    }
                }
            }

            return await originalInvoke(input, enhancedConfig);
        };

        return toolNode;
    }

    // Node implementations

    async _processInputNode(state) {
        try {
            const { userInput, sessionId } = state;

            // Notify mode change
            await this._notifyModeChange('processing', sessionId);

            return {
                agentMode: 'processing',
                messages: [{
                    role: 'user',
                    content: userInput?.text || userInput
                }]
            };
        } catch (error) {
            return { lastError: error, agentMode: 'error' };
        }
    }

    async _loadMemoryNode(state) {
        try {
            const { sessionId } = state;

            if (this.memoryManager) {
                const memoryContext = await getMemoryContext(sessionId, this.memoryManager);
                return { memoryContext };
            }

            return { memoryContext: { conversation_history: [], memory_type: 'none' } };
        } catch (error) {
            this.logger.warn('Memory loading failed:', error);
            return { memoryContext: { conversation_history: [], memory_type: 'none' } };
        }
    }

    async _generateResponseNode(state) {
        try {
            const { messages, language, sessionId, memoryContext } = state;

            this.logger.debug('🤖 Generating LLM response in workflow node:', {
                messageCount: messages?.length || 0,
                language,
                sessionId
            });

            // Create system prompt
            const systemPrompt = await createLangGraphSystemPrompt({
                language,
                includeToolInfo: true,
                _suppressLogging: false
            });

            // Bind tools to LLM for automatic tool calling
            const llmWithTools = this.model.bindTools(this.tools);

            this.logger.debug('🔧 LLM bound with tools:', {
                toolCount: this.tools?.length || 0,
                toolNames: this.tools?.map(t => t.name) || []
            });

            // Ensure memoryContext has the required structure
            const safeMemoryContext = memoryContext || { conversation_history: [], memory_type: 'none' };
            const conversationHistory = safeMemoryContext.conversation_history || [];

            // Create proper ChatPromptTemplate following LangChain-JS best practices
            const chatPrompt = ChatPromptTemplate.fromMessages([
                ["system", systemPrompt],
                ...conversationHistory.slice(-5).map(msg => [msg.role || "human", msg.content]),
                ["human", "{input}"]
            ]);

            this.logger.debug('📋 Created ChatPromptTemplate with proper message structure');

            // Create the chain: prompt -> LLM with tools
            const chain = chatPrompt.pipe(llmWithTools);

            this.logger.debug('🔗 Created prompt chain: ChatPromptTemplate -> LLM with tools');

            // Get user input from messages
            const userInput = messages[messages.length - 1]?.content || messages[messages.length - 1] || "Hello";

            this.logger.debug('🔗 Using LLM API service instead of direct model chain to ensure server-side processing');

            // Use LLM API service instead of direct model chain
            const llmOptions = {
                provider: 'aliyun',
                model: 'qwen-omni-turbo-realtime',
                modalities: ['text', 'audio'],
                audioConfig: { voice: 'Ethan', format: 'wav' },
                tools: this.tools.map(tool => ({
                    name: tool.name,
                    description: tool.description || '',
                    schema: tool.schema || {}
                })),
                tool_choice: 'auto',
                temperature: 0.7,
                max_tokens: 2000,
                language,
                stream: false
            };

            // Prepare messages with system prompt
            const llmMessages = [
                { role: 'system', content: systemPrompt },
                ...conversationHistory.slice(-5).map(msg => ({ role: msg.role || 'human', content: msg.content })),
                { role: 'user', content: userInput }
            ];

            this.logger.debug('📡 Calling LLM API service with:', {
                messagesCount: llmMessages.length,
                toolsCount: llmOptions.tools.length,
                provider: llmOptions.provider,
                model: llmOptions.model
            });

            // Generate response using LLM API service
            const apiResponse = await llmAPI.invoke(llmMessages, llmOptions);

            // Convert API response to LangChain format
            const response = {
                content: apiResponse.content || 'No response generated',
                tool_calls: [], // Tool calls would be handled by server
                additional_kwargs: {
                    audio: apiResponse.audio || null
                }
            };

            this.logger.debug('📝 LLM API response converted:', {
                hasContent: !!response.content,
                contentPreview: response.content?.substring(0, 100) + '...' || 'No content',
                hasAudio: !!response.additional_kwargs.audio,
                audioLength: response.additional_kwargs.audio ? response.additional_kwargs.audio.length : 0
            });

            // Process audio response if present
            if (response.additional_kwargs.audio) {
                this.logger.info('🔊 Audio response detected, processing for playback...');

                try {
                    // Import and use the AudioResponseProcessor
                    const { processLLMResponseAudio } = await import('@/agent/tools/tts.js');

                    // Process the audio response
                    const audioResult = await processLLMResponseAudio(response);

                    this.logger.debug('Audio processing result:', audioResult);

                } catch (audioError) {
                    this.logger.error('Failed to process audio response:', audioError);
                    // Continue with text response even if audio fails
                }
            }

            // Notify mode change
            this.callbacks.onModeChange('speaking', sessionId);

            // Update messages to include the AI response for ToolNode
            const updatedMessages = [
                ...messages,
                response // This is the AIMessage with potential tool_calls
            ];

            return {
                agentResponse: response.content,
                messages: updatedMessages, // ToolNode needs this to see the AI message with tool_calls
                agentMode: 'speaking'
            };

        } catch (error) {
            this.logger.error('❌ Response generation failed:', error);
            this.callbacks.onError(error);
            return { lastError: error, agentMode: 'error' };
        }
    }

    async _updateAgentNode(state) {
        try {
            const { sessionId, agentResponse, userInput, memoryContext } = state;

            // Save to memory if successful
            if (agentResponse && userInput && this.memoryManager) {
                try {
                    await addToMemory(
                        sessionId,
                        userInput?.text || userInput,
                        agentResponse,
                        this.memoryManager
                    );
                } catch (memoryError) {
                    this.logger.warn('Memory save failed:', memoryError);
                }
            }

            // Return to idle state
            this.callbacks.onModeChange('idle', sessionId);

            return {
                agentMode: 'idle',
                isStreaming: false
            };
        } catch (error) {
            return { lastError: error, agentMode: 'error' };
        }
    }

    async _handleErrorNode(state) {
        const { lastError, sessionId } = state;

        this.logger.error('Handling error in workflow:', lastError);

        // Notify error
        this.callbacks.onError(lastError);
        await this._notifyModeChange('error', sessionId);

        return {
            agentMode: 'error',
            isStreaming: false
        };
    }

    /**
     * Route after response generation based on tool calls
     */
    _routeAfterGeneration(state) {
        const { messages } = state;

        this.logger.debug('🔀 Routing after generation:', {
            messageCount: messages?.length || 0,
            hasMessages: !!(messages && messages.length > 0)
        });

        if (messages && messages.length > 0) {
            const lastMessage = messages[messages.length - 1];

            this.logger.debug('🔍 Examining last message for tool calls:', {
                hasLastMessage: !!lastMessage,
                messageType: lastMessage?.constructor?.name || typeof lastMessage,
                hasToolCalls: !!(lastMessage?.tool_calls && lastMessage.tool_calls.length > 0),
                toolCallCount: lastMessage?.tool_calls?.length || 0,
                toolCallNames: lastMessage?.tool_calls?.map(tc => tc.name) || []
            });

            // Check if the last message (AI response) has tool calls
            if (lastMessage?.tool_calls && lastMessage.tool_calls.length > 0) {
                this.logger.debug('🛠️ Tool calls detected, routing to executeTools');
                return 'executeTools';
            }
        }

        this.logger.debug('✅ No tool calls detected, routing to updateAgent');
        return 'updateAgent';
    }

    /**
     * Generate response using LangGraph workflow
     */
    async generateResponse(input, options = {}) {
        if (!this._initialized) {
            await this.initialize();
        }

        const sessionId = options.sessionId || 'default';
        const language = options.language || 'english';

        try {
            this.logger.debug('🚀 LangGraph agent starting response generation:', {
                input: typeof input === 'string' ? input.substring(0, 100) + '...' : input,
                sessionId,
                language,
                streaming: options.stream !== false
            });

            // Notify processing start
            this.callbacks.onModeChange('processing', sessionId);

            this.logger.debug('🔄 Invoking LangGraph workflow...');
            // Execute the LangGraph workflow
            const result = await this.graph.invoke({
                userInput: input,
                sessionId,
                language,
                agentMode: 'processing',
                messages: [{ content: input }]
            }, {
                configurable: { thread_id: sessionId }
            });

            this.logger.debug('✅ LangGraph workflow completed:', {
                hasResult: !!result,
                hasAgentResponse: !!result?.agentResponse,
                agentResponsePreview: result?.agentResponse?.substring(0, 100) + '...' || 'No response',
                resultKeys: result ? Object.keys(result) : []
            });

            // Return streaming generator if streaming enabled
            if (options.stream !== false) {
                this.logger.debug('🌊 Creating streaming generator for response');
                return this._createStreamGenerator(result, sessionId);
            }

            this.logger.debug('🎯 Returning direct response');
            return result.agentResponse || 'I apologize, but I was unable to generate a response.';

        } catch (error) {
            this.logger.error('❌ Error in generateResponse:', error);
            this.callbacks.onModeChange('error', sessionId);
            this.callbacks.onError(error);
            throw error;
        }
    }

    /**
     * Create streaming generator for compatibility
     */
    async *_createStreamGenerator(result, sessionId) {
        const response = result.agentResponse || '';

        // Simulate streaming by chunking the response
        const words = response.split(' ');
        for (let i = 0; i < words.length; i += 3) {
            const chunk = words.slice(i, i + 3).join(' ') + ' ';

            // Notify text chunk
            this.callbacks.onTextChunk({
                content: chunk,
                isPartial: i + 3 < words.length,
                sessionId
            });

            yield {
                type: 'llm_chunk',
                data: { content: chunk }
            };

            // Small delay for streaming effect
            await new Promise(resolve => setTimeout(resolve, 50));
        }
    }

    /**
     * Process LangGraph streaming response with generic callbacks
     * Generalized method that can be used by any adapter
     * @param {AsyncIterator} streamGenerator - LangGraph streaming response
     * @param {Object} options - Processing options with callbacks
     * @returns {Promise<string>} Final response text
     */
    async processLangGraphStream(streamGenerator, options = {}) {
        let fullResponse = '';
        let lastCompleteResponse = '';
        const sessionId = options.sessionId || 'default';

        try {
            this.logger.debug('Processing LangGraph streaming response...');

            const callbacks = {
                onTextChunk: options.onTextChunk || (() => { }),
                onToolCall: options.onToolCall || (() => { }),
                onStateUpdate: options.onStateUpdate || (() => { }),
                onStatusUpdate: options.onStatusUpdate || (() => { }),
                onComplete: options.onComplete || (() => { }),
                onError: options.onError || (() => { })
            };

            for await (const chunk of streamGenerator) {
                this.logger.debug('Processing LangGraph chunk:', chunk);

                if (chunk && typeof chunk === 'object') {
                    // Handle LangGraph message chunks
                    if (chunk.type === 'llm_chunk' || chunk.type === 'message') {
                        const content = chunk.data?.content || chunk.content || '';
                        if (content) {
                            fullResponse += content;
                            callbacks.onTextChunk({
                                content,
                                isPartial: true,
                                sessionId,
                                fullText: fullResponse
                            });
                        }

                        if (chunk.data?.responseText) {
                            lastCompleteResponse = chunk.data.responseText;
                        }
                    }
                    // Handle tool execution
                    else if (chunk.type === 'tool_start') {
                        this.logger.debug('Tool execution started:', chunk.data);
                        callbacks.onToolCall({
                            type: 'tool_start',
                            toolCall: chunk.data,
                            sessionId
                        });
                    }
                    else if (chunk.type === 'tool_end' || chunk.type === 'tool_call_chunk') {
                        this.logger.debug('Tool execution completed:', chunk.data);
                        callbacks.onToolCall({
                            type: 'tool_end',
                            toolCall: chunk.data,
                            sessionId
                        });
                    }
                    // Handle workflow state updates
                    else if (chunk.type === 'state_update') {
                        callbacks.onStateUpdate({
                            update: chunk.update,
                            sessionId
                        });

                        // Extract final response from state if available
                        if (chunk.update?.messages) {
                            const lastMessage = chunk.update.messages[chunk.update.messages.length - 1];
                            if (lastMessage?.content) {
                                lastCompleteResponse = lastMessage.content;
                            }
                        }
                    }
                    else if (chunk.type === 'workflow_complete') {
                        if (chunk.data?.context?.finalResponse) {
                            lastCompleteResponse = chunk.data.context.finalResponse;
                        }
                    }

                    // Direct properties fallback for non-standard formats
                    if (chunk.responseText) {
                        lastCompleteResponse = chunk.responseText;
                    } else if (chunk.content && !fullResponse.includes(chunk.content)) {
                        fullResponse += chunk.content;
                        callbacks.onTextChunk({
                            content: chunk.content,
                            isPartial: true,
                            sessionId,
                            fullText: fullResponse
                        });
                    }
                } else if (typeof chunk === 'string') {
                    // Handle plain string chunks
                    fullResponse += chunk;
                    callbacks.onTextChunk({
                        content: chunk,
                        isPartial: true,
                        sessionId,
                        fullText: fullResponse
                    });
                }

                // Update status during streaming
                const currentResponse = lastCompleteResponse || fullResponse;
                if (currentResponse && callbacks.onStatusUpdate) {
                    const preview = currentResponse.substring(0, 30) + (currentResponse.length > 30 ? '...' : '');
                    callbacks.onStatusUpdate(`Processing: ${preview}`);
                }
            }

            // Use the most complete response available
            const finalResponse = lastCompleteResponse || fullResponse;

            this.logger.debug('LangGraph streaming completed, final response length:', finalResponse.length);

            // Trigger completion callback
            callbacks.onComplete({
                finalResponse,
                sessionId,
                fullResponse,
                lastCompleteResponse
            });

            return finalResponse;

        } catch (error) {
            this.logger.error('Error processing LangGraph stream:', error);
            callbacks.onError({
                error,
                sessionId
            });
            throw error;
        }
    }

    /**
     * Generic mode change notification
     */
    async _notifyModeChange(mode, sessionId) {
        try {
            // Notify callbacks
            this.callbacks.onModeChange(mode, sessionId);
            this.callbacks.onStateUpdate({ mode }, sessionId);

            this.logger.debug(`Agent mode changed to: ${mode}`);
        } catch (error) {
            this.logger.warn('Failed to notify mode change:', error);
        }
    }

    // Generic service management
    updateServices(newServices) {
        this.services = { ...this.services, ...newServices };

        // Tools are self-sufficient and don't need re-registration when services change
        this.logger.debug('Services updated - tools remain self-sufficient');
    }

    updateConfig(newConfig) {
        if (newConfig) {
            this.options = { ...this.options, ...newConfig };
        }
    }

    async setMode(mode, sessionId = 'default') {
        await this._notifyModeChange(mode, sessionId);
    }

    async handleInterruption(sessionId = 'default') {
        await this._notifyModeChange('listening', sessionId);
    }

    async getState(sessionId = 'default') {
        return {
            mode: 'idle',
            streaming: false,
            sessionId
        };
    }

    async testConnection() {
        if (this.model) {
            return await this.model.testConnection();
        }
        return false;
    }

    dispose() {
        this.graph = null;
        this.tools = [];
        this.services = {};
        this._initialized = false;
        this.logger.info('Core LangGraph Agent Service disposed');
    }
}

// Export default instance
const langGraphAgentService = new LangGraphAgentService();
export { langGraphAgentService };
export default LangGraphAgentService;