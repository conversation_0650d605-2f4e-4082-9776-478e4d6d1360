/**
 * Agent Utilities
 * Utility functions for agent operations including debugging, visualization, and monitoring
 */

import { createLogger } from '../utils/logger.js';

// Initialize logger
const logger = createLogger('AgentUtils');

/**
 * Debugging class for system prompts
 * Provides expandable visualization and logging of system prompts
 */
export class SystemPromptDebugger {
    constructor(options = {}) {
        this.options = {
            enabled: true,
            logLevel: 'info', // 'debug', 'info', 'warn', 'error'
            maxLength: 200,   // Max length for collapsed view
            indentSize: 2,    // Spaces for indentation
            ...options
        };

        this.logger = createLogger('SystemPromptDebugger');
    }

    /**
     * Log a system prompt with expandable sections
     * @param {string} prompt - The system prompt to debug
     * @param {Object} metadata - Additional metadata about the prompt
     */
    logSystemPrompt(prompt, metadata = {}) {
        if (!this.options.enabled) return;

        // Create sections from the prompt
        const sections = this._extractSections(prompt);

        // Log the prompt with metadata
        this.logger.info('System Prompt', {
            metadata,
            sections,
            fullPrompt: prompt
        });

        // Create a visual representation in the console
        this._consoleDisplay(prompt, sections, metadata);
    }

    /**
     * Extract logical sections from a system prompt
     * @param {string} prompt - The system prompt to analyze
     * @returns {Array} - Array of sections with title and content
     * @private
     */
    _extractSections(prompt) {
        if (!prompt) return [];

        const lines = prompt.split('\n');
        const sections = [];
        let currentSection = null;

        for (let line of lines) {
            // New section detection based on formatting patterns
            if (line.match(/^#+\s/) || // Markdown headings
                line.match(/^[A-Z][A-Z\s]+:/) || // ALL CAPS followed by colon
                line.match(/^[A-Z][a-z]+:/) || // Capitalized word followed by colon
                line.toUpperCase() === line && line.trim().length > 2) { // All caps lines

                // Save previous section if it exists
                if (currentSection) {
                    sections.push(currentSection);
                }

                // Start new section
                currentSection = {
                    title: line.trim(),
                    content: []
                };
            } else if (currentSection) {
                // Add to current section
                currentSection.content.push(line);
            } else {
                // Start with unnamed section if no section header found
                if (!currentSection) {
                    currentSection = {
                        title: 'Introduction',
                        content: [line]
                    };
                }
            }
        }

        // Add the last section
        if (currentSection && currentSection.content.length > 0) {
            sections.push(currentSection);
        }

        return sections;
    }

    /**
     * Create a visual display of the prompt in the console
     * @param {string} fullPrompt - The complete system prompt
     * @param {Array} sections - Extracted sections
     * @param {Object} metadata - Prompt metadata
     * @private
     */
    _consoleDisplay(fullPrompt, sections, metadata) {
        // Style definitions
        const styles = {
            header: 'background: #2c3e50; color: #ecf0f1; font-weight: bold; padding: 3px 5px; border-radius: 3px;',
            section: 'color: #3498db; font-weight: bold;',
            metadata: 'color: #8e44ad; font-style: italic;',
            content: 'color: #34495e;',
            stats: 'color: #7f8c8d; font-style: italic;'
        };

        // Print header
        console.groupCollapsed('%c System Prompt Debug View', styles.header);

        // Print metadata
        if (Object.keys(metadata).length > 0) {
            console.group('%c Metadata', styles.section);
            for (const [key, value] of Object.entries(metadata)) {
                console.log(`%c ${key}: %c ${value}`, styles.metadata, 'color: #2c3e50;');
            }
            console.groupEnd();
        }

        // Print sections
        console.group('%c Prompt Sections', styles.section);
        sections.forEach(section => {
            const contentText = section.content.join('\n');
            const previewText = this._getPreview(contentText);

            console.groupCollapsed(`%c ${section.title} %c (${section.content.length} lines)`,
                styles.section, styles.stats);
            console.log(`%c${contentText}`, styles.content);
            console.groupEnd();
        });
        console.groupEnd();

        // Print statistics
        const lines = fullPrompt.split('\n').length;
        const chars = fullPrompt.length;
        const words = fullPrompt.split(/\s+/).filter(Boolean).length;

        console.log('%c Prompt Statistics: %c %d lines, %d words, %d characters',
            styles.section, styles.stats, lines, words, chars);

        console.groupEnd();
    }

    /**
     * Get a preview of content text
     * @param {string} text - The text to preview
     * @returns {string} - The preview text
     * @private
     */
    _getPreview(text) {
        if (text.length <= this.options.maxLength) return text;
        return text.substring(0, this.options.maxLength) + '...';
    }
}

// Create and export a default instance
export const systemPromptDebugger = new SystemPromptDebugger();

/**
 * Format an object for console logging
 * @param {Object} obj - The object to format
 * @returns {string} - Formatted string representation
 */
export function formatObjectForLogging(obj) {
    if (!obj) return 'null';

    try {
        return JSON.stringify(obj, null, 2);
    } catch (error) {
        return `[Object with circular reference or non-serializable values: ${typeof obj}]`;
    }
}

/**
 * Create a collapsible debug section in console
 * @param {string} title - Section title
 * @param {Function} logFunction - Function that performs the actual logging
 */
export function debugSection(title, logFunction) {
    console.groupCollapsed(`[DEBUG] ${title}`);
    try {
        logFunction();
    } finally {
        console.groupEnd();
    }
}

export default {
    SystemPromptDebugger,
    systemPromptDebugger,
    formatObjectForLogging,
    debugSection
};