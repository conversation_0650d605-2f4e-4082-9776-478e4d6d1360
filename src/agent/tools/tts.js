/**
 * Agent TTS Tools with Integrated Voice Cloning
 * Simplified for agent mode only, integrates with AliyunBailianChatModel realtime API
 * Supports both CosyVoice cloning and Qwen-Omni realtime TTS
 * 
 * Based on <PERSON>yun documentation:
 * - Realtime API: https://help.aliyun.com/zh/model-studio/realtime
 * - CosyVoice Clone API: https://help.aliyun.com/zh/model-studio/cosyvoice-clone-api
 */

import { z } from 'zod';
import { tool } from '@langchain/core/tools';
import { createLogger, LogLevel } from '../../utils/logger.js';
import { getEnvVar } from '../../config/env.ts';
import {
    processLLMResponseAudio,
    playBase64Audio
} from '@/media/modality/audio.ts';

// Also import the new simplified processor
import {
    SimpleAudioProcessor,
    processLLMResponseAudio as processLLMResponseAudioSimple,
    playBase64Audio as playBase64AudioSimple
} from '@/media/modality/audioProcessor.js';

const logger = createLogger('AgentTTS');
logger.setLogLevel(LogLevel.DEBUG);

// Global audio processor instance
const audioProcessor = new SimpleAudioProcessor({ logger });

/**
 * Unified Agent TTS Service
 * Handles both standard TTS and voice cloning for agent mode
 */
class AgentTTSService {
    constructor(options = {}) {
        this.logger = createLogger('AgentTTSService');
        this.logger.setLogLevel(LogLevel.DEBUG);

        // Aliyun configuration
        this.apiKey = getEnvVar('VITE_DASHSCOPE_API_KEY', '');
        this.cosyVoiceEndpoint = 'https://dashscope.aliyuncs.com/api/v1/services/aigc/text2speech/synthesis';

        // Voice configuration based on Aliyun docs
        this.supportedVoices = {
            // Qwen-Omni realtime voices from docs
            'Chelsie': { type: 'realtime', gender: 'female' },
            'Serena': { type: 'realtime', gender: 'female' },
            'Ethan': { type: 'realtime', gender: 'male' },
            'Cherry': { type: 'realtime', gender: 'female' },
            // CosyVoice clone mappings from docs
            'loongstella': { type: 'clone', gender: 'female', alias: 'Serena' },
            'longxiaocheng': { type: 'clone', gender: 'male', alias: 'Ethan' },
            'longyue': { type: 'clone', gender: 'female', alias: 'Chelsie' },
            'longxiaobai': { type: 'clone', gender: 'female', alias: 'Cherry' }
        };

        // Agent integration
        this.audioPlayer = options.audioPlayer;
        this.aliyunModel = options.aliyunModel; // AliyunBailianChatModel instance
        this.sessionId = options.sessionId || 'agent_session';

        // Voice cloning state
        this.voiceProfiles = new Map();

        if (!this.apiKey) {
            this.logger.warn('No Aliyun API key found. Set VITE_DASHSCOPE_API_KEY environment variable.');
        }

        this.logger.info('AgentTTSService initialized for agent mode only');
    }

    /**
     * Speak text using either realtime model or voice cloning
     * @param {string} text - Text to speak
     * @param {Object} options - Speech options
     */
    async speak(text, options = {}) {
        const {
            voice = 'Serena',
            useVoiceCloning = false,
            voiceProfile = null,
            referenceAudio = null,
            streaming = true,
            sessionId = this.sessionId,
            onAudioChunk = null
        } = options;

        this.logger.info(`🎤 Agent TTS: Speaking text with voice: ${voice}`, {
            textLength: text.length,
            useVoiceCloning,
            streaming,
            sessionId
        });

        try {
            // Choose TTS method: voice cloning, realtime model, or standard
            if (useVoiceCloning || voiceProfile || referenceAudio) {
                return await this._speakWithVoiceCloning(text, options);
            } else if (this.aliyunModel?.isRealtimeModeActive()) {
                return await this._speakWithRealtimeModel(text, options);
            } else {
                return await this._speakWithCosyVoice(text, options);
            }
        } catch (error) {
            this.logger.error('Agent TTS speak error:', error);
            throw error;
        }
    }

    /**
     * Use AliyunBailianChatModel realtime for TTS
     * @private
     */
    async _speakWithRealtimeModel(text, options) {
        const { voice } = options;

        this.logger.info(`🔊 Using Qwen-Omni realtime model for TTS`);

        if (!this.aliyunModel?.isRealtimeModeActive()) {
            throw new Error('Aliyun realtime model not active');
        }

        try {
            // Update realtime session voice if needed
            if (voice && this.supportedVoices[voice]?.type === 'realtime') {
                await this.aliyunModel.updateRealtimeSession({ voice });
            }

            // Send text to realtime model - it handles audio streaming internally
            const success = await this.aliyunModel.sendRealtimeText(text);

            if (!success) {
                throw new Error('Failed to send text to realtime model');
            }

            return {
                success: true,
                method: 'realtime',
                voice,
                streaming: true
            };

        } catch (error) {
            this.logger.error('Realtime model TTS error:', error);
            throw error;
        }
    }

    /**
     * Use CosyVoice with voice cloning
     * @private
     */
    async _speakWithVoiceCloning(text, options) {
        const { voice, voiceProfile, referenceAudio, streaming } = options;

        this.logger.info(`🎭 Using CosyVoice with voice cloning`);

        try {
            // Map voice to CosyVoice name
            const cosyVoiceName = this._mapVoiceToCosyVoice(voice);

            const requestPayload = {
                model: 'cosyvoice-v2',
                input: { text },
                parameters: {
                    voice: cosyVoiceName,
                    format: 'mp3',
                    sample_rate: 22050,
                    stream: streaming
                }
            };

            // Add voice cloning parameters if provided
            if (voiceProfile || referenceAudio) {
                requestPayload.parameters.voice_clone = {
                    reference_audio: referenceAudio,
                    voice_profile: voiceProfile,
                    clone_strength: 0.8
                };
            }

            if (streaming) {
                requestPayload.parameters.stream_options = {
                    include_usage: true,
                    chunk_length: 1024
                };
            }

            const response = await fetch(this.cosyVoiceEndpoint, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this.apiKey}`,
                    'Content-Type': 'application/json',
                    'X-DashScope-SSE': streaming ? 'enable' : undefined
                },
                body: JSON.stringify(requestPayload)
            });

            if (!response.ok) {
                const errorText = await response.text();
                throw new Error(`CosyVoice API error: ${response.status} - ${errorText}`);
            }

            if (streaming) {
                await this._handleStreamingResponse(response, options);
            } else {
                const result = await response.json();
                if (result.output?.audio) {
                    await this._processAudioData(result.output.audio, options);
                }
            }

            return {
                success: true,
                method: 'voice_cloning',
                voice,
                voiceProfile,
                streaming
            };

        } catch (error) {
            this.logger.error('Voice cloning TTS error:', error);
            throw error;
        }
    }

    /**
     * Use standard CosyVoice without cloning
     * @private
     */
    async _speakWithCosyVoice(text, options) {
        this.logger.info(`🎵 Using standard CosyVoice TTS`);

        // Use voice cloning method but without cloning parameters
        return await this._speakWithVoiceCloning(text, {
            ...options,
            voiceProfile: null,
            referenceAudio: null
        });
    }

    /**
     * Handle streaming response from CosyVoice
     * @private
     */
    async _handleStreamingResponse(response, options) {
        try {
            const reader = response.body.getReader();
            const decoder = new TextDecoder();
            let buffer = '';

            while (true) {
                const { done, value } = await reader.read();
                if (done) break;

                buffer += decoder.decode(value, { stream: true });
                const lines = buffer.split('\n');
                buffer = lines.pop() || '';

                for (const line of lines) {
                    if (line.trim().startsWith('data: ')) {
                        const data = line.trim().substring(6);
                        if (data === '[DONE]') return;

                        try {
                            const chunk = JSON.parse(data);
                            if (chunk.output?.audio) {
                                await this._processAudioData(chunk.output.audio, options);
                            }
                        } catch (parseError) {
                            this.logger.warn('Failed to parse streaming chunk:', parseError);
                        }
                    }
                }
            }
        } catch (error) {
            this.logger.error('Streaming response error:', error);
        }
    }

    /**
     * Process audio data and play through audio player
     * @private
     */
    async _processAudioData(audioData, options) {
        try {
            // Convert base64 audio to buffer
            let audioBuffer;
            if (typeof audioData === 'string') {
                const binaryString = atob(audioData);
                const bytes = new Uint8Array(binaryString.length);
                for (let i = 0; i < binaryString.length; i++) {
                    bytes[i] = binaryString.charCodeAt(i);
                }
                audioBuffer = bytes.buffer;
            } else {
                audioBuffer = audioData;
            }

            // Play through audio player if available
            if (this.audioPlayer && audioBuffer) {
                await this.audioPlayer.playAudioChunk(audioBuffer);
            }

            // Call custom audio chunk handler
            if (options.onAudioChunk) {
                await options.onAudioChunk(audioBuffer);
            }

        } catch (error) {
            this.logger.error('Audio processing error:', error);
        }
    }

    /**
     * Map voice name to CosyVoice internal name
     * @private
     */
    _mapVoiceToCosyVoice(voice) {
        const voiceMap = {
            'Serena': 'loongstella',
            'Ethan': 'longxiaocheng',
            'Chelsie': 'longyue',
            'Cherry': 'longxiaobai'
        };
        return voiceMap[voice] || 'loongstella';
    }

    /**
     * Create voice profile for cloning
     */
    async createVoiceProfile(profileName, referenceAudio, metadata = {}) {
        try {
            this.logger.info(`📝 Creating voice profile: ${profileName}`);

            const profileData = {
                profileName,
                referenceAudio,
                metadata: {
                    ...metadata,
                    created_at: new Date().toISOString(),
                    model: 'cosyvoice-v2'
                }
            };

            this.voiceProfiles.set(profileName, profileData);

            return {
                success: true,
                profileId: `profile_${Date.now()}`,
                profileName,
                created: true
            };

        } catch (error) {
            this.logger.error('Error creating voice profile:', error);
            throw error;
        }
    }

    /**
     * Get available voices
     */
    getAvailableVoices() {
        return {
            realtime: Object.keys(this.supportedVoices).filter(
                voice => this.supportedVoices[voice].type === 'realtime'
            ),
            clone: Object.keys(this.supportedVoices).filter(
                voice => this.supportedVoices[voice].type === 'clone'
            ),
            profiles: Array.from(this.voiceProfiles.keys())
        };
    }
}

/**
 * Main Agent TTS Tool
 * Unified tool for agent speech synthesis with voice cloning support
 */
export const agentTTSTool = tool(
    async (input, config) => {
        const {
            text,
            voice = 'Serena',
            useVoiceCloning = false,
            voiceProfile = null,
            referenceAudio = null,
            options = {}
        } = input;

        logger.info(`🎤 Agent TTS Tool: Converting to speech`, {
            textLength: text?.length,
            voice,
            useVoiceCloning,
            hasVoiceProfile: !!voiceProfile
        });

        try {
            // Get services from config (passed by service-aware ToolNode)
            const audioPlayer = config?.audioPlayer;
            const aliyunModel = config?.aliyunModel;

            if (!audioPlayer) {
                throw new Error('Audio player not available in agent context');
            }

            // Create agent TTS service
            const ttsService = new AgentTTSService({
                audioPlayer,
                aliyunModel,
                sessionId: config?.sessionId || 'agent_tts_session'
            });

            const cleanText = text.replace(/[^\w\s.,!?;:'"()-]/g, '').trim();
            if (!cleanText) {
                throw new Error('No valid text to process');
            }

            // LangGraph streaming callback
            if (config?.callbacks?.handleLLMNewToken) {
                const method = useVoiceCloning ? '🎭 Voice cloning' : '🔊 TTS';
                await config.callbacks.handleLLMNewToken(`${method}: "${cleanText.substring(0, 30)}..."\n`);
            }

            const startTime = Date.now();

            // Speak with unified TTS service
            const result = await ttsService.speak(cleanText, {
                voice,
                useVoiceCloning,
                voiceProfile,
                referenceAudio,
                streaming: options.streaming !== false,
                onAudioChunk: (audioChunk) => {
                    logger.debug('Audio chunk received and processed');
                }
            });

            const duration = Date.now() - startTime;

            logger.info(`✅ Agent TTS completed in ${duration}ms`);

            // LangGraph completion callback
            if (config?.callbacks?.handleLLMNewToken) {
                const method = result.method === 'voice_cloning' ? 'Voice cloning' : 'TTS';
                await config.callbacks.handleLLMNewToken(`✅ ${method} completed in ${duration}ms\n`);
            }

            return {
                success: true,
                text: cleanText,
                voice,
                method: result.method,
                duration,
                streaming: result.streaming,
                message: `Successfully converted text to speech using ${result.method} in ${duration}ms`
            };

        } catch (error) {
            logger.error('Agent TTS Tool error:', error);

            // LangGraph error callback
            if (config?.callbacks?.handleLLMNewToken) {
                await config.callbacks.handleLLMNewToken(`❌ TTS error: ${error.message}\n`);
            }

            return {
                success: false,
                text,
                voice,
                error: error.message,
                duration: 0,
                message: `Failed to convert text to speech: ${error.message}`
            };
        }
    },
    {
        name: 'speak_response',
        description: 'Convert text to speech for the agent. Supports both standard TTS and voice cloning with CosyVoice. Integrates with Aliyun Qwen-Omni realtime model.',
        schema: z.object({
            text: z.string().describe('Text to convert to speech'),
            voice: z.enum(['Serena', 'Ethan', 'Chelsie', 'Cherry']).default('Serena').describe('Voice to use (Qwen-Omni realtime voices)'),
            useVoiceCloning: z.boolean().default(false).describe('Enable voice cloning with CosyVoice'),
            voiceProfile: z.string().optional().describe('Voice profile name for cloning'),
            referenceAudio: z.string().optional().describe('Base64 reference audio for voice cloning'),
            options: z.object({
                streaming: z.boolean().default(true).describe('Enable streaming audio'),
                rate: z.number().optional().default(1.0).describe('Speech rate'),
                pitch: z.number().optional().default(0).describe('Pitch adjustment'),
                volume: z.number().optional().default(1.0).describe('Volume level')
            }).optional().describe('Additional speech options')
        })
    }
);

/**
 * Voice Profile Management Tool
 * Create and manage voice profiles for cloning
 */
export const voiceProfileTool = tool(
    async (input, config) => {
        const { action, profileName, referenceAudio, metadata = {} } = input;

        logger.info(`📝 Voice Profile Tool: ${action} for profile: ${profileName}`);

        try {
            const ttsService = new AgentTTSService({
                sessionId: config?.sessionId || 'profile_session'
            });

            switch (action) {
                case 'create':
                    if (!profileName || !referenceAudio) {
                        throw new Error('Profile name and reference audio required for creation');
                    }
                    const result = await ttsService.createVoiceProfile(profileName, referenceAudio, metadata);
                    return {
                        success: true,
                        action: 'create',
                        profile: result,
                        message: `Voice profile "${profileName}" created successfully`
                    };

                case 'list':
                    const voices = ttsService.getAvailableVoices();
                    return {
                        success: true,
                        action: 'list',
                        voices,
                        message: `Available voices: Realtime: ${voices.realtime.join(', ')}, Profiles: ${voices.profiles.length}`
                    };

                default:
                    throw new Error(`Unknown action: ${action}`);
            }

        } catch (error) {
            logger.error('Voice Profile Tool error:', error);
            return {
                success: false,
                action,
                profileName,
                error: error.message,
                message: `Failed to ${action} voice profile: ${error.message}`
            };
        }
    },
    {
        name: 'voice_profile_manager',
        description: 'Manage voice profiles for cloning. Create new profiles or list existing ones.',
        schema: z.object({
            action: z.enum(['create', 'list']).describe('Action to perform'),
            profileName: z.string().optional().describe('Name of the voice profile'),
            referenceAudio: z.string().optional().describe('Base64 encoded reference audio'),
            metadata: z.object({}).optional().describe('Additional metadata for the profile')
        })
    }
);

/**
 * Enhanced play_audio tool for LangChain/LangGraphJS tool calling
 * Based on LangChain docs pattern for audio output handling
 */
export const playAudioTool = tool(
    async (input, config) => {
        const { audio, format = 'wav' } = input;

        logger.info('🔊 Playing audio via playAudioTool', {
            hasAudio: !!audio,
            audioLength: audio?.length,
            format
        });

        try {
            if (!audio) {
                throw new Error('No audio data provided');
            }

            // Use the consolidated audio processor to play the audio
            const result = await playBase64Audio(audio, format, { logger });

            return {
                success: true,
                played: true,
                format,
                audioLength: audio.length,
                duration: result.duration,
                message: `Audio played successfully (${format}, ${audio.length} chars)`
            };

        } catch (error) {
            logger.error('playAudioTool error:', error);
            return {
                success: false,
                played: false,
                error: error.message,
                message: `Failed to play audio: ${error.message}`
            };
        }
    },
    {
        name: 'play_audio',
        description: 'Play a base64-encoded audio string as audio output. Supports multiple audio formats.',
        schema: z.object({
            audio: z.string().describe('Base64-encoded audio data'),
            format: z.string().default('wav').describe('Audio format (e.g., wav, mp3, ogg)')
        })
    }
);

/**
 * LLM Response Audio Handler Tool
 * Automatically processes LangChain/LangGraph responses with audio content
 * Based on OpenAI ChatGPT audio output pattern from docs
 */
export const llmAudioHandlerTool = tool(
    async (input, config) => {
        const { response } = input;

        logger.info('🎵 Processing LLM response for audio content');

        try {
            const result = await audioProcessor.processAndPlay(response);

            return {
                success: true,
                audioFound: result.audioPlayed,
                audioLength: result.audioLength,
                error: result.error,
                message: result.audioPlayed ?
                    `Audio content found and played (${result.audioLength} chars)` :
                    'No audio content found in response'
            };

        } catch (error) {
            logger.error('llmAudioHandlerTool error:', error);
            return {
                success: false,
                audioFound: false,
                error: error.message,
                message: `Failed to process LLM response audio: ${error.message}`
            };
        }
    },
    {
        name: 'process_llm_audio_response',
        description: 'Process LangChain/LangGraph LLM responses that contain audio content. Automatically detects and plays audio from various response formats.',
        schema: z.object({
            response: z.any().describe('LLM response object that may contain audio content')
        })
    }
);

// Export tool collections for agent use
export const agentTTSToolCollection = [
    agentTTSTool,
    voiceProfileTool,
    playAudioTool,
    llmAudioHandlerTool
];

export const AGENT_TTS_TOOL_NAMES = {
    SPEAK_RESPONSE: 'speak_response',
    VOICE_PROFILE_MANAGER: 'voice_profile_manager',
    PLAY_AUDIO: 'play_audio',
    PROCESS_LLM_AUDIO_RESPONSE: 'process_llm_audio_response'
};

// Export utility functions for direct integration using consolidated audio module
export { audioProcessor, processLLMResponseAudio, playBase64Audio };

// Export the service class for direct use
export { AgentTTSService };

// Legacy exports for compatibility
export const ttsToolCollection = agentTTSToolCollection;
export const TTS_TOOL_NAMES = Object.values(AGENT_TTS_TOOL_NAMES);

export default agentTTSTool;
