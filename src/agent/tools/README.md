# Agent Tools Documentation

## Overview

The agent tools system provides LangGraph-compatible tools for various functionalities including TTS, animation, and streaming. The system has been organized to eliminate duplication and provide clear separation of concerns.

## Tool Structure

### 1. TTS Processor Tools (`ttsProcessor.js`)
**Enhanced TTS processing tools that extend StreamProcessor for core streaming capabilities**

- **`streamingTTSProcessor`** ⭐ **UNIFIED**: Advanced TTS processor that handles both direct text and LangChain stream processing with backpressure control, sentence-level chunking, and performance tracking
- **`simpleTTS`**: Basic TTS for simple use cases without advanced features  

The `streamingTTSProcessor` tool now **unifies** both direct text processing and LangChain stream processing in a single tool, extending `StreamProcessor` from `src/agent/stream/StreamProcessor.js`.

**Architecture**: `TTSStreamProcessor extends StreamProcessor` - combines robust streaming with specialized TTS processing.

### 2. TTS Health Monitoring Tools (`tts.js`)
**Diagnostic and monitoring tools for TTS services**

- **`ttsHealthMonitor`**: Real-time health monitoring and diagnostics for TTS services

### 3. Animation Tools (`animation.js`)
**Animation selection and management tools**

- **`selectAnimation`**: Select specific animations for avatars
- **`listAnimations`**: List available animations
- **`recommendAnimations`**: Get animation recommendations based on context

### 4. Core Streaming Utilities (Re-exported from `../stream/`)
**General LangChain.js streaming capabilities available through tools**

- **`StreamProcessor`**: Core streaming processor class for any LangChain Runnable
- **`createStreamProcessor`**: Factory function to create stream processors

These are **re-exported** from `src/agent/stream/StreamProcessor.js` to make them available through the tools system without duplication.

## Usage Examples

## Architecture Principles

### ✅ **No Duplication**
- **Core streaming logic** stays in `src/agent/stream/StreamProcessor.js`
- **TTS-specific logic** in `src/agent/tools/ttsProcessor.js`
- **General streaming utilities** re-exported through tools for convenience

### ✅ **Clear Separation**
- **`/stream`** - Core LangChain streaming logic
- **`/tools`** - LangGraph tools that use the core logic
- **TTS tools** - Specialized for text-to-speech processing

## Usage Examples

### Using Unified TTS Processor Tool

#### Direct Text Processing
```javascript
import { streamingTTSProcessorTool } from './src/agent/tools/ttsProcessor.js';

// Process direct text with TTS
const result = await streamingTTSProcessorTool.invoke({
    text: "Hello world! This is direct text processing.",
    ttsService: myTTSService,
    audioPlayer: myAudioPlayer,
    options: {
        waitForAudioCompletion: true
    }
});
```

#### LangChain Stream Processing
```javascript
// Process LangChain model with real-time TTS
const result = await streamingTTSProcessorTool.invoke({
    runnable: myLangChainModel,
    runnableInput: { messages: [{ role: 'user', content: 'Tell me a story' }] },
    streamConfig: { configurable: { thread_id: 'session-123' } },
    ttsService: myTTSService,
    audioPlayer: myAudioPlayer,
    options: {
        waitForAudioCompletion: true
    }
});
```

### Using Core StreamProcessor in Agent Workflows
```javascript
import { StreamProcessor } from './src/agent/tools/index.js';

// Create stream processor for LangChain workflows
const processor = new StreamProcessor({
    onTextChunk: (chunk) => console.log('Text:', chunk.content),
    onToolCall: (call) => console.log('Tool:', call.toolCall)
});

// Process any LangChain Runnable
for await (const chunk of processor.processStream(runnable, input, config)) {
    // Handle streaming chunks
}
```

### Using TTS Processing Tools
```javascript
import { registerTTS } from './src/agent/tools/index.js';

// Register TTS tools with services
const ttsTools = registerTTS(ttsService, {
    audioPlayer: audioPlayer,
    skipTTS: false
});
```

### Factory Function
```javascript
import { createStreamProcessor } from './src/agent/tools/index.js';

// Create processor with options
const processor = createStreamProcessor({
    streamMode: 'values',
    enableToolStreaming: true
});
```

## File Organization

- **`ttsProcessor.js`** - TTS-specific processing tools
- **`tts.js`** - TTS health monitoring and diagnostics  
- **`animation.js`** - Animation management tools
- **`index.js`** - Tool manager, registration system, and core utilities re-export
- **`../stream/StreamProcessor.js`** - Core streaming logic (not duplicated)

## Key Architectural Decisions

1. **Core Logic Centralized**: All general streaming logic remains in `src/agent/stream/StreamProcessor.js`
2. **Tools Use Core**: TTS and other specialized tools import and use the core StreamProcessor
3. **Convenient Re-export**: Core utilities available through tools index for agent workflows
4. **No Duplication**: Single source of truth for streaming functionality
5. **Clear Boundaries**: Specialized tools vs general utilities

## Benefits

- **🎯 Single Source of Truth**: Core streaming logic in one place
- **🔧 Tool Specialization**: TTS tools focus on TTS-specific concerns
- **📦 Convenient Access**: Core utilities available through tools system
- **🚫 No Duplication**: Eliminates code redundancy and maintenance overhead
- **🔄 Reusability**: Core StreamProcessor can be used anywhere in the system

## Tool Categories

| Category | Tools | Purpose |
|----------|-------|---------|
| `tts_advanced` | `streamingTTSProcessor`, `simpleTTS` | Core TTS processing with advanced features |
| `tts_monitoring` | `ttsHealthMonitor` | Service health and diagnostics |
| `animation` | `selectAnimation`, `listAnimations`, `recommendAnimations` | Animation control |
| `stream_processing` | Stream processor tools | Advanced streaming capabilities |

## Usage

### Auto-Registration
```javascript
import { autoRegisterTools } from './src/agent/tools/index.js';

const services = {
    ttsService: myTTSService,
    audioPlayer: myAudioPlayer,
    animationRegistry: myAnimationRegistry
};

const result = await autoRegisterTools(services);
```

### Manual Registration
```javascript
import { registerTTS, registerStreamProcessor } from './src/agent/tools/index.js';

// Register TTS tools
const ttsTools = registerTTS(ttsService);

// Register stream processor tools
const streamTools = registerStreamProcessor({
    ttsService: ttsService,
    audioPlayer: audioPlayer
});
```

### Tool Execution
```javascript
import { executeToolCall } from './src/agent/tools/index.js';

const result = await executeToolCall({
    name: 'streamingTTSProcessor',
    args: {
        text: "Hello world",
        options: {
            ttsChunkConfig: { bySentence: true },
            waitForAudioCompletion: true
        }
    }
}, services);
```

## Available Tools

### Core TTS Processing
- **`streamingTTSProcessor`**: Unified TTS processor with full feature set
  - **Dual Mode**: Handles both direct text and LangChain stream processing
  - Backpressure control
  - Sentence-level chunking
  - Audio queue management
  - Performance tracking
  - Concurrency control
  - StreamProcessor integration

- **`simpleTTS`**: Basic TTS processing
  - Direct TTS service calls
  - Simple audio playback
  - Lightweight for basic needs

### Health Monitoring
- **`ttsHealthMonitor`**: Service diagnostics
  - Health status checking
  - Performance metrics
  - Real-time diagnostics
  - Streaming-aware monitoring

### Animation Control
- **`selectAnimation`**: Trigger specific animations
- **`listAnimations`**: Get available animations
- **`recommendAnimations`**: Smart animation suggestions

## Benefits of New Structure

1. **No Duplication**: Eliminated redundant TTS tools
2. **Clear Separation**: Each file has a specific purpose
3. **Advanced Features**: Full LLMStreamProcessor capabilities available as tools
4. **Easy Integration**: Auto-registration and service injection
5. **Consistent API**: Unified tool interface across all categories
6. **Better Testing**: Focused tool responsibilities make testing easier

## Migration Guide

### From Old `streamingTTS` to New Tools

**Old approach:**
```javascript
// Used streamingTTS from tts.js (limited features)
await streamingTTS.invoke({ text, ttsService, audioPlayer });
```

**New approach:**
```javascript
// Use streamingTTSProcessor for advanced features
await streamingTTSProcessorTool.invoke({
    text,
    ttsService,
    audioPlayer,
    options: {
        ttsChunkConfig: { bySentence: true },
        backpressureConfig: { maxBufferSize: 3000 }
    }
});

// Or use simpleTTS for basic needs
await simpleTTSProcessorTool.invoke({ text, ttsService, audioPlayer });
```

The new structure provides better organization, eliminates redundancy, and offers more powerful capabilities for LangGraph agent workflows.

# LangGraph Animation Tools

## Overview

The animation tools have been enhanced with advanced **LangGraph patterns** and modern **LangChain.js** retrieval techniques. This implementation prioritizes semantic understanding and state-aware animation selection over simple keyword matching.

## Key Features

### 🎯 Advanced Semantic Search
- **Multi-Query Retrieval**: Uses LangChain's `MultiQueryRetriever` to generate diverse search queries for better results
- **Vector Embeddings**: Supports multiple embedding strategies with graceful fallbacks
- **Contextual Understanding**: Analyzes emotional profiles, usage scenarios, and movement characteristics

### 🧠 LangGraph State Management
- **Query History**: Maintains context of recent animation requests
- **User Preferences**: Learns and adapts to preferred animation categories
- **State Alignment**: Boosts relevant results based on usage patterns

### 📊 Enhanced Metadata Analysis
Each animation is enriched with:
- **Semantic Tags**: Auto-generated contextual keywords
- **Emotional Profile**: Detected mood and emotional context
- **Usage Scenarios**: Identified use cases (dialogue, entertainment, action, etc.)
- **Movement Characteristics**: Analysis of animation dynamics
- **Contextual Factors**: Formality, energy level, social appropriateness

## Architecture

### LangGraphAnimationRetriever
```javascript
class LangGraphAnimationRetriever {
  constructor() {
    this.vectorStore = null;
    this.multiQueryRetriever = null;
    this.state = {
      lastQuery: null,
      lastResults: [],
      queryHistory: [],
      preferredCategories: new Set(),
      userPatterns: new Map()
    };
  }
}
```

### Embedding Fallback Strategy
1. **OpenAI Embeddings** (if API key available)
2. **HuggingFace Transformers** (deprecated but functional)
3. **Simple Text Embeddings** (TF-IDF style fallback)

### Modern Import Paths
The implementation uses updated LangChain import paths:
```javascript
import { PromptTemplate } from '@langchain/core/prompts';
import { StringOutputParser } from '@langchain/core/output_parsers';
import { MultiQueryRetriever } from 'langchain/retrievers/multi_query';
```

## Tools Available

### 1. `select_animation`
Advanced animation selection using multi-query semantic search:
```javascript
{
  animationQuery: "happy dancing movement",
  reasoningContext: "User wants to celebrate",
  category: "dance" // optional
}
```

**Returns enhanced metadata:**
- `contextualFit`: How well the animation matches the query context
- `stateAlignment`: Alignment with user's historical preferences
- `enhancedMetadata`: Semantic tags, emotional profile, usage scenarios

### 2. `recommend_animations`
State-aware recommendations based on conversation context:
```javascript
{
  userMood: "excited",
  conversationContext: "User just achieved something great",
  userIntent: "celebration",
  limit: 5
}
```

### 3. `list_animations`
Browse available animations with filtering and descriptions.

## Multi-Query Enhancement

When an LLM is available, the system generates multiple search perspectives using modern **LCEL (LangChain Expression Language)**:

```javascript
// Modern LCEL pattern (replaces deprecated LLMChain)
// Uses correct import: PromptTemplate from '@langchain/core/prompts'
const queryExpansionChain = queryExpansionPrompt
    .pipe(this.llm)
    .pipe(new StringOutputParser());
```

**Original Query**: "happy movement"
**Generated Queries**:
- "joyful celebration animation"
- "upbeat dancing sequence" 
- "positive emotional expression"
- "energetic happy gesture"

This provides more comprehensive coverage and better semantic matching while using the latest LangChain patterns as per [LangChain deprecation guidelines](https://python.langchain.com/docs/versions/v0_2/deprecations/).

## State Management

The system learns from usage patterns:
- **Category Preferences**: Tracks which animation categories are used most
- **Query Patterns**: Identifies recurring themes in requests
- **Contextual Adaptation**: Adjusts scoring based on recent successful matches

## Error Handling & Fallbacks

The implementation provides multiple fallback layers:
1. Multi-query retrieval → Standard vector search
2. Vector embeddings → Simple text matching  
3. Semantic search → Keyword-based selection
4. Enhanced metadata → Basic animation properties

## Integration with LangGraph Agent

The tools integrate seamlessly with the LangGraph agent workflow:
- **Tool Registration**: Auto-registered in agent initialization
- **State Coordination**: Shares context with agent memory
- **Enhanced Responses**: Provides rich metadata for agent reasoning

## Performance Considerations

- **Lazy Loading**: Embeddings initialized only when needed
- **Singleton Pattern**: Single global retriever instance
- **Efficient Caching**: Vector store maintains processed animations
- **Smart Fallbacks**: Graceful degradation when advanced features unavailable

## Future Enhancements

- **Learning Integration**: Connect with agent memory for deeper personalization
- **Real-time Adaptation**: Dynamic re-ranking based on user feedback
- **Cross-Modal Search**: Integration with visual animation previews
- **Advanced Embeddings**: Custom fine-tuned models for animation semantics 