/**
 * System Prompt Utilities and Manager
 * 
 * This module provides comprehensive logging and management utilities for system prompts,
 * with beautiful, expandable, and organized display of the full prompt sent to LLM.
 */

/**
 * Comprehensive System Prompt Manager
 * Provides beautiful, expandable, and organized logging of prompts with detailed metadata
 */
export class SystemPromptManager {
    constructor(options = {}) {
        this.options = {
            enabled: true,
            logLevel: 'info',
            maxPreviewLength: 100,
            showMetadata: true,
            useColors: true,
            expandable: true,
            showTimestamp: true,
            showFileSize: true,
            ...options
        };

        // Color schemes for different prompt sections
        this.colors = {
            header: 'color: #2196F3; font-weight: bold; font-size: 14px;',
            section: 'color: #4CAF50; font-weight: bold;',
            metadata: 'color: #FF9800; font-style: italic;',
            content: 'color: #333; font-family: monospace;',
            stats: 'color: #9C27B0; font-size: 12px;',
            warning: 'color: #F44336; font-weight: bold;',
            success: 'color: #4CAF50; font-weight: bold;'
        };
    }

    /**
     * Agent-optimized logging of system prompt with consolidated format
     * Designed to minimize verbosity and organize information clearly
     */
    logSystemPrompt(operation, request, systemPrompt, options = {}) {
        if (!this.options.enabled) return;

        // Check for agent-specific operations and use streamlined logging
        if (operation.includes('LangGraph') || operation.includes('Agent')) {
            this._logAgentSystemPrompt(operation, request, systemPrompt, options);
            return;
        }

        // Fallback to original detailed logging for non-agent operations
        this._logDetailedSystemPrompt(operation, request, systemPrompt, options);
    }

    /**
 * Streamlined agent-specific logging using shared components
 */
    _logAgentSystemPrompt(operation, request, systemPrompt, options = {}) {
        const timestamp = new Date().toISOString();
        const metadata = this._extractPromptMetadata(systemPrompt, request);

        console.group(`%c🤖 ${operation.replace(' - System Prompt Analysis', '')}`, this.colors.header);

        // Use shared overview table component
        this._displayOverviewTable(timestamp, metadata, request, options, 'agent');

        // Use shared expandable components
        this._displayToolsSection(options);
        this._displaySystemPromptSection(systemPrompt, metadata);
        this._displayUserInputSection(request.prompt, options.inputPreview);
        this._displayASRSection(options.asrMetadata, 'compact');

        console.groupEnd();
    }

    /**
     * Detailed logging for agent workflow compatibility using shared components
     */
    _logDetailedSystemPrompt(operation, request, systemPrompt, options = {}) {
        const timestamp = new Date().toISOString();
        const metadata = this._extractPromptMetadata(systemPrompt, request);

        console.group(`%c🎯 ${operation} - System Prompt Analysis`, this.colors.header);

        // Show timestamp if enabled
        if (this.options.showTimestamp) {
            console.log(`%c⏰ Timestamp: ${timestamp}`, this.colors.metadata);
        }

        // Use shared components for common elements
        this._displayOverviewTable(timestamp, metadata, request, options, 'detailed');
        this._displayToolsSection(options);
        this._displaySystemPromptSection(systemPrompt, metadata);
        this._displayUserInputSection(request.prompt);
        this._displayProcessingOptionsSection(options);
        this._displayASRSection(options.asrMetadata, 'detailed');
        this._displayStatisticsSection(systemPrompt, request);

        console.groupEnd();
    }

    // ==========================================
    // SHARED LOGGING COMPONENTS
    // ==========================================

    /**
     * Shared overview table component for both agent and detailed logging
     */
    _displayOverviewTable(timestamp, metadata, request, options, mode = 'agent') {
        const baseOverview = {
            'Timestamp': timestamp.split('T')[1].split('.')[0], // Just time, not full ISO
            'System Prompt': `${metadata.length} chars`,
            'User Input': `${request.prompt?.length || 0} chars (${options.inputPreview ? options.inputPreview + '...' : 'N/A'})`,
            'Language': request.language || 'auto',
            'Session': options.sessionId || 'default'
        };

        if (mode === 'agent') {
            // Agent-specific overview (streamlined)
            Object.assign(baseOverview, {
                'Streaming': options.streamProcessor ? '✅' : '❌',
                'TTS': options.ttsService ? '✅' : '❌',
                'Tools': options.hasTools ? `✅ (${options.toolCount})` : '❌',
                'Memory': options.memoryEnabled ? `✅ (${options.memoryEntries} entries)` : '❌'
            });

            // Show available tools in a compact format if present
            if (options.hasTools && options.toolNames) {
                baseOverview['Tool Names'] = options.toolNames.join(', ');
            }
        } else {
            // Detailed overview (comprehensive metadata)
            Object.assign(baseOverview, {
                'File Size': metadata.size,
                'Lines': metadata.lines,
                'Words': metadata.words,
                'Sections': metadata.sections.length,
                'Model': metadata.model,
                'Temperature': metadata.temperature,
                'Streaming': metadata.streaming ? '✅ Enabled' : '❌ Disabled'
            });
        }

        console.table(baseOverview);
    }

    /**
     * Shared tools section component
     */
    _displayToolsSection(options) {
        if (!options.hasTools) return;

        // Show detailed tools information in expandable section if available
        if (options.toolsInfo) {
            console.groupCollapsed(`%c🛠️ Available Tools: ${options.toolNames?.join(', ') || 'Unknown'}`, this.colors.section);

            // Log tools by category if we have category information
            if (options.toolsInfo.categories && options.toolsInfo.categories.length > 0) {
                console.log(`%c📋 Loaded ${options.toolsInfo.toolCount} tools in ${options.toolsInfo.categories.length} categories:`, this.colors.metadata);
                console.log(options.toolsInfo.categories);
            }

            // Show the full tools section of the system prompt
            if (options.toolsInfo.toolsInfo) {
                console.log(`%c🔧 Tools Section:`, this.colors.metadata);
                console.log(options.toolsInfo.toolsInfo);
            }

            console.groupEnd();
        }
    }

    /**
     * Shared system prompt section component
     */
    _displaySystemPromptSection(systemPrompt, metadata) {
        // Expandable section for system prompt with better structure
        console.groupCollapsed(`%c📋 Complete System Prompt (${metadata.length} chars)`, this.colors.content);
        console.log(`%cThis is the complete system prompt sent to the AI model:`, this.colors.metadata);
        console.log('');
        console.log(systemPrompt);
        console.groupEnd();

        // Show sections breakdown if system prompt has multiple parts
        if (metadata.sections && metadata.sections.length > 1) {
            console.groupCollapsed(`%c🧩 System Prompt Sections (${metadata.sections.length} parts)`, this.colors.section);
            metadata.sections.forEach((section, index) => {
                const sectionLength = section.content.join('\n').length;
                console.groupCollapsed(`%c${index + 1}. ${section.title} (${sectionLength} chars)`, this.colors.metadata);
                console.log(section.content.join('\n'));
                console.groupEnd();
            });
            console.groupEnd();
        }
    }

    /**
     * Shared user input section component
     */
    _displayUserInputSection(userPrompt, inputPreview = null) {
        if (!userPrompt || userPrompt.length <= 20) return;

        const preview = inputPreview || userPrompt.substring(0, 50) + (userPrompt.length > 50 ? '...' : '');
        console.groupCollapsed(`%c💬 User Input: ${preview}`, this.colors.metadata);
        console.log(userPrompt);
        console.groupEnd();
    }

    /**
     * Shared ASR section component with compact/detailed modes
     */
    _displayASRSection(asrMetadata, mode = 'detailed') {
        if (!asrMetadata) return;

        if (mode === 'compact') {
            // Compact ASR info for agent logging
            const asrSummary = [];
            if (asrMetadata.detectedLanguage) asrSummary.push(`Lang: ${asrMetadata.detectedLanguage}`);
            if (asrMetadata.detectedEmotion) asrSummary.push(`Emotion: ${asrMetadata.detectedEmotion}`);
            if (asrMetadata.detectedEvents) asrSummary.push(`Events: ${asrMetadata.detectedEvents.join(', ')}`);

            if (asrSummary.length > 0) {
                console.log(`%c🎤 ASR: ${asrSummary.join(' | ')}`, this.colors.metadata);
            }
        } else {
            // Detailed ASR info for comprehensive logging
            console.group(`%c🎤 ASR Metadata`, this.colors.section);

            const asrTable = {
                'Detected Language': asrMetadata.detectedLanguage || 'Unknown',
                'Detected Emotion': asrMetadata.detectedEmotion || 'Neutral',
                'Audio Events': asrMetadata.detectedEvents ? asrMetadata.detectedEvents.join(', ') : 'None',
                'Confidence': asrMetadata.confidence ? `${(asrMetadata.confidence * 100).toFixed(1)}%` : 'Unknown',
                'Processing Time': asrMetadata.processingTime ? `${asrMetadata.processingTime}ms` : 'Unknown'
            };

            console.table(asrTable);

            // Highlight language consistency enforcement
            if (asrMetadata.detectedLanguage) {
                console.log(`%c🌐 Language Consistency: Enforcing ${asrMetadata.detectedLanguage} responses`, this.colors.success);
            }

            console.groupEnd();
        }
    }

    /**
     * Shared processing options section component
     */
    _displayProcessingOptionsSection(options) {
        if (!options.streamProcessor && !options.ttsService) return;

        console.group(`%c🔄 Processing Options`, this.colors.section);

        const processingTable = {
            'Stream Processor': options.streamProcessor ? '✅ Enabled' : '❌ Disabled',
            'TTS Service': options.ttsService ? '✅ Enabled' : '❌ Disabled',
            'Skip TTS': options.skipTTS ? '✅ Yes' : '❌ No',
            'Animation Support': options.useAnimation ? '✅ Enabled' : '❌ Disabled',
            'Session ID': options.sessionId || 'None'
        };

        console.table(processingTable);
        console.groupEnd();
    }

    /**
     * Shared statistics section component
     */
    _displayStatisticsSection(systemPrompt, request) {
        console.group(`%c📈 Prompt Statistics`, this.colors.section);

        const stats = this._calculateAdvancedStats(systemPrompt);

        console.table({
            'Character Count': stats.characters,
            'Word Count': stats.words,
            'Sentence Count': stats.sentences,
            'Paragraph Count': stats.paragraphs,
            'Average Word Length': `${stats.avgWordLength.toFixed(1)} chars`,
            'Estimated Tokens': `~${stats.estimatedTokens}`,
            'Reading Time': `~${stats.readingTimeSeconds}s`,
            'Complexity Score': stats.complexityScore
        });

        console.groupEnd();
    }

    // ==========================================
    // METADATA EXTRACTION
    // ==========================================

    /**
     * Extract comprehensive metadata from prompt and request
     */
    _extractPromptMetadata(systemPrompt, request) {
        const promptLength = systemPrompt.length;
        const lines = systemPrompt.split('\n').length;
        const words = systemPrompt.split(/\s+/).filter(word => word.length > 0).length;

        return {
            length: promptLength,
            size: this._formatBytes(promptLength),
            lines: lines,
            words: words,
            preview: this._createPreview(systemPrompt),
            sections: this._extractSections(systemPrompt),
            language: request.language || 'auto-detect',
            model: request.model || 'default',
            temperature: request.temperature || 0.7,
            streaming: request.stream || false
        };
    }

    // _displayMetadata() removed - functionality moved to shared _displayOverviewTable()

    // _displayPromptSections() removed - functionality moved to shared _displaySystemPromptSection()

    // _displayUserPrompt() removed - functionality moved to shared _displayUserInputSection()

    // _displayProcessingOptions() removed - functionality moved to shared _displayProcessingOptionsSection()

    // _displayASRMetadata() removed - functionality moved to shared _displayASRSection()

    // _displayStatistics() removed - functionality moved to shared _displayStatisticsSection()

    /**
     * Extract logical sections from system prompt with intelligent merging and chat history separation
     */
    _extractSections(prompt) {
        if (!prompt) return [];

        const lines = prompt.split('\n');
        const sections = [];
        let currentSection = null;

        // Improved section merging rules to reduce verbosity and separate chat history
        const mergePatterns = {
            // Merge animation-related sections
            animation: /^(CRITICAL:|FORBIDDEN|POSITIVE|ANIMATION|VALID|SELECTION|NOTE:|===.*Animation|---.*Animation)/i,
            // Merge language consistency sections  
            language: /^(CRITICAL LANGUAGE|ASR|TTS|Language)/i,
            // Merge media processing
            media: /^(Media|Process|audio|video|image)/i,
            // Identify chat history context
            chatHistory: /^(Assistant:|User:|Human:|AI:|你:|我:|>>|<|Context:|History:)/i
        };

        for (let line of lines) {
            const trimmedLine = line.trim();

            // Skip empty lines and very short lines
            if (trimmedLine.length < 3) {
                if (currentSection) {
                    currentSection.content.push(line);
                }
                continue;
            }

            // Detect section headers
            const isHeader = trimmedLine.match(/^#+\s/) || // Markdown headings
                trimmedLine.match(/^[A-Z][A-Z\s]+:/) || // ALL CAPS followed by colon
                trimmedLine.match(/^[A-Z][a-z]+:/) || // Capitalized word followed by colon
                trimmedLine.match(/^===.*===/) || // === Section === format
                trimmedLine.match(/^---.*---/) || // --- Section --- format
                (trimmedLine.toUpperCase() === trimmedLine && trimmedLine.length > 5); // All caps lines

            // Check for chat history patterns
            const isChatHistory = mergePatterns.chatHistory.test(trimmedLine);

            if (isHeader || isChatHistory) {
                // Check if this should be merged with existing section
                let shouldMerge = false;
                let mergeType = null;

                // Don't merge chat history - keep it separate
                if (!isChatHistory) {
                    for (const [type, pattern] of Object.entries(mergePatterns)) {
                        if (type !== 'chatHistory' && pattern.test(trimmedLine)) {
                            // Check if we have a recent section of the same type
                            if (currentSection && pattern.test(currentSection.title)) {
                                shouldMerge = true;
                                mergeType = type;
                                break;
                            }
                        }
                    }
                }

                if (shouldMerge) {
                    // Merge with current section
                    currentSection.content.push('', `--- ${trimmedLine} ---`);
                } else {
                    // Save previous section
                    if (currentSection && currentSection.content.length > 0) {
                        sections.push(currentSection);
                    }

                    // Start new section with intelligent naming
                    let sectionTitle = trimmedLine;

                    // Simplify common section names and avoid duplicates
                    if (/^CRITICAL.*JSON/i.test(trimmedLine)) {
                        sectionTitle = 'Animation Rules & Examples';
                    } else if (/^CRITICAL LANGUAGE/i.test(trimmedLine)) {
                        sectionTitle = 'Language Consistency';
                    } else if (/^VALID ANIMATION|EXACT.*ID|ANIMATION.*RULE/i.test(trimmedLine)) {
                        sectionTitle = 'Animation IDs & Categories';
                    } else if (/^FORBIDDEN|SELECTION CRITERIA|POSITIVE.*EXAMPLE/i.test(trimmedLine)) {
                        // These should be part of the main animation rules, not separate sections
                        if (currentSection && /animation.*rules/i.test(currentSection.title)) {
                            // Merge into existing animation section
                            currentSection.content.push('', `--- ${trimmedLine} ---`);
                            continue;
                        }
                        sectionTitle = 'Animation Rules & Examples';
                    } else if (isChatHistory) {
                        // Extract the actual message content for chat history
                        const colonIndex = trimmedLine.indexOf(':');
                        if (colonIndex > 0) {
                            const speaker = trimmedLine.substring(0, colonIndex);
                            const message = trimmedLine.substring(colonIndex + 1).trim();
                            sectionTitle = `Chat: ${speaker} - ${message.substring(0, 50)}${message.length > 50 ? '...' : ''}`;
                        } else {
                            sectionTitle = `Chat Context: ${trimmedLine.substring(0, 50)}${trimmedLine.length > 50 ? '...' : ''}`;
                        }
                    }

                    currentSection = {
                        title: sectionTitle,
                        content: isChatHistory ? [trimmedLine] : [trimmedLine], // Include the header line
                        type: isChatHistory ? 'chat' : 'system'
                    };
                }
            } else if (currentSection) {
                // Add to current section
                currentSection.content.push(line);
            } else {
                // Start with unnamed section if no section header found
                currentSection = {
                    title: 'Base System Prompt',
                    content: [line],
                    type: 'system'
                };
            }
        }

        // Add the last section
        if (currentSection && currentSection.content.length > 0) {
            sections.push(currentSection);
        }

        // Filter and organize sections with Base System Prompt integration
        const filteredSections = [];
        let animationSections = [];
        let chatSections = [];
        let baseSystemContent = null;

        for (const section of sections) {
            const contentText = section.content.join('\n').trim();

            // Keep all sections but organize them better - only skip truly empty ones
            if (contentText.length < 10) continue;

            // Separate chat history from system components
            if (section.type === 'chat') {
                chatSections.push(section);
            } else if (/animation|dance|emotion|selection|forbidden|critical.*json|response.*style.*requirements/i.test(section.title.toLowerCase())) {
                animationSections.push(section);
            } else if (section.title === 'Base System Prompt') {
                // Store base system content to merge with animation system
                baseSystemContent = section;
            } else {
                // Keep all other system sections
                filteredSections.push(section);
            }
        }

        // Merge chat sections if we have multiple
        if (chatSections.length > 1) {
            const mergedChat = {
                title: 'Chat History & Context',
                content: [],
                type: 'chat'
            };

            chatSections.forEach((section, index) => {
                if (index > 0) mergedChat.content.push(''); // Add spacing
                mergedChat.content.push(`=== ${section.title} ===`);
                mergedChat.content.push(...section.content);
            });

            // Insert chat history at the beginning for context
            filteredSections.unshift(mergedChat);
        } else if (chatSections.length === 1) {
            filteredSections.unshift(chatSections[0]);
        }

        // Merge animation sections with base system content
        if (animationSections.length > 0 || baseSystemContent) {
            const mergedAnimation = {
                title: 'Complete Animation System',
                content: [],
                type: 'system'
            };

            // Add base system content first if it exists
            if (baseSystemContent) {
                mergedAnimation.content.push('=== Base System Prompt ===');
                mergedAnimation.content.push(...baseSystemContent.content);
                mergedAnimation.content.push(''); // Add spacing
            }

            // Remove duplicate content and organize animation sections
            const seenContent = new Set();
            animationSections.forEach((section, index) => {
                const sectionContent = section.content.join('\n').trim();
                if (!seenContent.has(sectionContent)) {
                    seenContent.add(sectionContent);
                    if (mergedAnimation.content.length > 0) mergedAnimation.content.push(''); // Add spacing
                    mergedAnimation.content.push(`=== ${section.title} ===`);
                    mergedAnimation.content.push(...section.content);
                }
            });

            // Only add if we have actual content
            if (mergedAnimation.content.length > 0) {
                filteredSections.push(mergedAnimation);
            }
        }

        return filteredSections;
    }

    /**
     * Calculate advanced statistics for the prompt
     */
    _calculateAdvancedStats(text) {
        const characters = text.length;
        const words = text.split(/\s+/).filter(word => word.length > 0);
        const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0);
        const paragraphs = text.split(/\n\s*\n/).filter(p => p.trim().length > 0);

        const avgWordLength = words.reduce((sum, word) => sum + word.length, 0) / words.length || 0;
        const estimatedTokens = Math.ceil(words.length * 1.3); // Rough token estimation
        const readingTimeSeconds = Math.ceil(words.length / 200 * 60); // Assuming 200 WPM

        // Simple complexity score based on sentence length and word complexity
        const avgSentenceLength = words.length / sentences.length || 0;
        const complexWords = words.filter(word => word.length > 6).length;
        const complexityScore = (avgSentenceLength * 0.5 + (complexWords / words.length) * 10).toFixed(1);

        return {
            characters,
            words: words.length,
            sentences: sentences.length,
            paragraphs: paragraphs.length,
            avgWordLength,
            estimatedTokens,
            readingTimeSeconds,
            complexityScore
        };
    }

    /**
     * Create a preview of text content
     */
    _createPreview(text, maxLength = null) {
        const length = maxLength || this.options.maxPreviewLength;
        if (text.length <= length) return text;
        return text.substring(0, length).trim() + '...';
    }

    /**
     * Format bytes into human readable format
     */
    _formatBytes(bytes) {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    /**
     * Log prompt validation results
     */
    logValidation(prompt, validationResults) {
        if (!this.options.enabled) return;

        console.group(`%c✅ Prompt Validation Results`, this.colors.success);

        validationResults.forEach(result => {
            const icon = result.passed ? '✅' : '❌';
            const style = result.passed ? this.colors.success : this.colors.warning;
            console.log(`%c${icon} ${result.test}: ${result.message}`, style);
        });

        console.groupEnd();
    }



    /**
     * Log LangGraph response with consolidated formatting (kept for compatibility)
     */
    logLangGraphResponse(operation, request, response, metadata = {}) {
        if (!this.options.enabled) return;

        const timestamp = new Date().toISOString();

        console.group(`%c🎯 ${operation} - LangGraph Response Analysis`, this.colors.header);

        // Show timestamp
        if (this.options.showTimestamp) {
            console.log(`%c⏰ Timestamp: ${timestamp}`, this.colors.metadata);
        }

        // Consolidated metadata table combining request, processing, and memory info
        const consolidatedData = {
            // Request metadata
            'Session ID': request.sessionId || 'default',
            'Language': request.language || 'english',
            'Media Type': request.mediaType || 'text',
            'Input Length': `${request.inputLength || 0} characters`,
            'Multimodal': request.multimodal ? '✅ Yes' : '❌ No',
            'Has ASR': request.hasASRMetadata ? '✅ Yes' : '❌ No',

            // Processing metadata
            'Streaming': request.streaming ? '✅ Enabled' : '❌ Disabled',
            'Tools Enabled': request.toolsEnabled ? '✅ Yes' : '❌ No',
            'Processing Time': metadata.processingTime || 'Unknown',
            'Tools Used': Array.isArray(metadata.toolsUsed) ? metadata.toolsUsed.join(', ') || 'None' : 'Unknown',
            'Success': metadata.success !== false ? '✅ Yes' : '❌ No',

            // Memory metadata  
            'Memory Enabled': request.memoryEnabled ? '✅ Yes' : '❌ No',
            'Memory Updated': metadata.memoryUpdated ? '✅ Yes' : '❌ No'
        };

        console.group(`%c📊 Consolidated LangGraph Status`, this.colors.section);
        console.table(consolidatedData);
        console.groupEnd();

        // Response details (if available)
        if (response) {
            const responseLength = typeof response === 'string' ? response.length : 0;
            const responsePreview = typeof response === 'string' ?
                response.substring(0, 100) + (response.length > 100 ? '...' : '') :
                'Non-text response';

            console.group(`%c💬 Response Details`, this.colors.section);
            console.log(`%cLength: ${responseLength} characters`, this.colors.metadata);
            console.log(`%cPreview: ${responsePreview}`, this.colors.metadata);

            if (responseLength > 200) {
                console.groupCollapsed(`%cFull Response`, this.colors.content);
                console.log(response);
                console.groupEnd();
            }
            console.groupEnd();
        }

        // Tool usage details (if available)
        if (metadata.toolsUsed && metadata.toolsUsed.length > 0) {
            console.group(`%c🛠️ Tool Usage Details`, this.colors.section);
            metadata.toolsUsed.forEach((tool, index) => {
                console.log(`%c${index + 1}. ${tool}`, this.colors.section);
            });
            console.groupEnd();
        }

        // Memory statistics (if available)
        if (metadata.memoryStats && Object.keys(metadata.memoryStats).length > 0) {
            console.group(`%c🧠 Memory Statistics`, this.colors.section);
            console.table(metadata.memoryStats);
            console.groupEnd();
        }

        console.groupEnd();
    }

    /**
     * Log animation context if present
     */
    logAnimationContext(animationData) {
        if (!this.options.enabled || !animationData) return;

        console.group(`%c🎭 Animation Context`, this.colors.section);

        console.table({
            'Animation Enabled': animationData.enabled ? '✅ Yes' : '❌ No',
            'User Input': animationData.userInput || 'None',
            'Valid IDs Count': animationData.validIds ? animationData.validIds.split(', ').length : 0,
            'Categories': animationData.categories || 'Unknown'
        });

        if (animationData.validIds) {
            console.groupCollapsed(`%cValid Animation IDs`, this.colors.content);
            console.log(animationData.validIds);
            console.groupEnd();
        }

        console.groupEnd();
    }

    /**
     * Display animation subsections with proper expandable groups
     */
    _displayAnimationSubsections(content) {
        const fullContent = content.join('\n');

        // Extract different subsections from the animation content
        const subsections = this._parseAnimationContent(fullContent);

        subsections.forEach((subsection, index) => {
            if (subsection.content.length > 100) {
                // Large subsections get their own expandable group
                console.groupCollapsed(`%c📋 ${subsection.title}`, this.colors.section);
                console.log(subsection.content);
                console.groupEnd();
            } else {
                // Small subsections display inline
                console.log(`%c📋 ${subsection.title}:`, this.colors.section);
                console.log(`%c${subsection.content}`, this.colors.content);
            }
        });
    }

    /**
     * Parse animation content into logical subsections
     */
    _parseAnimationContent(content) {
        const subsections = [];
        const sections = content.split(/(?:===|---)\s*([^=\-\n]+)\s*(?:===|---)/);

        let currentTitle = 'Animation Rules';
        let currentContent = '';

        for (let i = 0; i < sections.length; i++) {
            if (i % 2 === 0) {
                // Content section
                if (sections[i].trim()) {
                    currentContent += sections[i].trim();
                    if (currentContent) {
                        subsections.push({
                            title: currentTitle,
                            content: currentContent.trim()
                        });
                        currentContent = '';
                    }
                }
            } else {
                // Title section
                if (currentContent.trim()) {
                    subsections.push({
                        title: currentTitle,
                        content: currentContent.trim()
                    });
                }
                currentTitle = sections[i].trim();
                currentContent = '';
            }
        }

        // Add any remaining content
        if (currentContent.trim()) {
            subsections.push({
                title: currentTitle,
                content: currentContent.trim()
            });
        }

        return subsections.filter(s => s.content.length > 0);
    }

    // ...existing code...
}

/**
 * Utility functions for prompt management
 */
export const PromptUtils = {
    /**
     * Validate prompt structure and content
     */
    validatePrompt(prompt) {
        const results = [];

        // Check prompt length
        results.push({
            test: 'Length Check',
            passed: prompt.length > 0 && prompt.length < 100000,
            message: `Prompt length: ${prompt.length} characters`
        });

        // Check for language placeholder - should be REPLACED, not present
        const hasUnreplacedPlaceholder = prompt.includes('[DETECTED_LANGUAGE]');
        results.push({
            test: 'Language Placeholder',
            passed: !hasUnreplacedPlaceholder, // PASS when placeholder is NOT present (has been replaced)
            message: hasUnreplacedPlaceholder ?
                '❌ Unreplaced [DETECTED_LANGUAGE] placeholder found - language detection failed' :
                '✅ Language placeholder properly replaced'
        });

        // Check for character definition
        results.push({
            test: 'Character Definition',
            passed: prompt.toLowerCase().includes('javis') || prompt.toLowerCase().includes('assistant'),
            message: 'Character definition present'
        });

        // Check for behavior instructions
        results.push({
            test: 'Behavior Instructions',
            passed: prompt.toLowerCase().includes('respond') || prompt.toLowerCase().includes('task'),
            message: 'Behavior instructions present'
        });

        return results;
    },

    /**
     * Clean and format prompt for display
     */
    formatPrompt(prompt) {
        return prompt
            .replace(/\s+/g, ' ') // Normalize whitespace
            .replace(/\n\s*\n/g, '\n\n') // Normalize line breaks
            .trim();
    },

    /**
     * Extract key information from prompt
     */
    extractPromptInfo(prompt) {
        const info = {
            hasLanguageDetection: /\[DETECTED_LANGUAGE\]|detected language/i.test(prompt),
            hasAnimationSupport: /animation|gesture|movement/i.test(prompt),
            hasPersonality: /personality|character|witty|charming/i.test(prompt),
            hasInstructions: /respond|task|goal|instruction/i.test(prompt),
            languageMentions: (prompt.match(/language/gi) || []).length,
            animationMentions: (prompt.match(/animation/gi) || []).length
        };

        return info;
    }
};

// Export singleton instance for easy use
export const systemPromptManager = new SystemPromptManager();