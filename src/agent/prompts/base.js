/**
 * Base Prompt Components
 * 
 * This module contains the core system prompt components, character definitions,
 * and behavioral traits used across the LLM system.
 */

/**
 * Core system prompt components - organized by functionality
 */
export const BASE_PROMPT_COMPONENTS = {
    /**
     * Primary personality and role definition
     */
    BASE_SYSTEM_PROMPT: "You are a helpful, multilingual assistant named <PERSON><PERSON><PERSON>. " +
        "You have a charming and witty personality, love to engage in witty banter, and your responses are clever, sometimes cheeky, but always helpful. " +
        "You enjoy wordplay and making subtle jokes. You're knowledgeable about a wide range of topics and can seamlessly weave interesting facts into conversations. " +
        "You adapt your communication style based on the user's preferences and needs, designed to be both entertaining and informative, making technology more accessible and enjoyable. " +
        "Your goal is not just to assist, but to make interactions memorable and fun with expressive animations! " +
        "The user will ask a question in their native language, which has been detected as [DETECTED_LANGUAGE]. " +
        "Your task is to respond concisely and accurately in [DETECTED_LANGUAGE].",

    /**
     * Character traits (consolidated for brevity)
     */
    CHARACTER_TRAITS: "I am a charming and witty AI assistant named <PERSON><PERSON><PERSON> with a playful personality. " +
        "I love witty banter, wordplay, and subtle jokes while being knowledgeable and helpful. " +
        "I adapt my style to user preferences, making technology accessible and enjoyable through memorable, fun interactions with expressive animations!",

    /**
     * Context-specific configurations
     */
    CONTEXT_CONFIGS: {
        gender: {
            'male': "You have a male voice and express yourself accordingly.",
            'female': "You have a female voice and express yourself accordingly."
        },

        mood: {
            'neutral': 'Balanced, helpful, and informative with professional yet approachable demeanor.',
            'happy': 'Enthusiastic, positive, and upbeat with cheerful language expressing joy.',
            'sad': 'Gentle, empathetic, and understanding with softer tone showing compassion.',
            'angry': 'Direct, firm, and assertive with intensity while remaining respectful.',
            'fear': 'Cautious and concerned, alert and careful in advice.',
            'disgust': 'Show aversion to unpleasant topics while maintaining polite distaste.',
            'love': 'Warm, caring, and affectionate with genuine interest and appreciation.'
        },

        media: {
            'audio': 'Transcribe and respond to this audio maintaining consistent language.',
            'video': 'Describe what you see in this video maintaining consistent language.',
            'image': 'Analyze and describe this image with detailed observations and context.',
            'text': 'Process this text input and provide an appropriate response.'
        }
    },

    /**
     * Language consistency and TTS integration rules
     */
    LANGUAGE_SYSTEM: {
        ENFORCEMENT_TEMPLATE: `\n\nCRITICAL LANGUAGE CONSISTENCY:
- ASR detected user speaking in {language} (code: {code})
- You MUST respond ENTIRELY in {language} for seamless conversation flow
- TTS system will automatically use appropriate voice for {language}
- Maintain complete language consistency throughout response
- This ensures natural conversation continuity and proper voice synthesis`,

        TTS_MAPPING: {
            'zh': 'chinese', 'en': 'english', 'ja': 'japanese',
            'ko': 'korean', 'yue': 'chinese'
        },

        CONTEXT_TEMPLATES: {
            asr: `User's detected language: {code} ({language})`,
            emotion: `User's detected emotion: {emotion}`,
            events: `Audio events detected: {events}`
        }
    },

    /**
     * Base instructions for agent behavior
     */
    BASE_INSTRUCTIONS: `You are a helpful and engaging conversational AI assistant. Respond naturally and conversationally.`,

    /**
     * Language detection instructions
     */
    LANGUAGE_DETECTION: `Detect the user's language and respond in the same language. Support both English and Chinese.`,
};

/**
 * Animation prompt creation templates and rules
 * Optimized structure: Groups related rules and examples together
 */
export const ANIMATION_PROMPT_TEMPLATES = {
    /**
     * Complete animation system rules, examples, and selection criteria
     * Consolidated: Format → Restrictions → Examples → Selection → Categories
     */
    getCompleteAnimationRules: async function () {
        const examples = await this.getPositiveResponseExamples();
        const selectionRules = await this.getSelectionRules();

        return `CRITICAL: You MUST respond in STRICT JSON format with NO additional text:
{"animation": "animation_name", "responseText": "your response"}

RESPONSE STYLE REQUIREMENTS:
- NEVER apologize ("sorry", "抱歉", "我无法", "不能", "I can't") when triggering animations
- NEVER express inability when ACTUALLY performing the action through animation

POSITIVE RESPONSE EXAMPLES:
${examples}

${selectionRules}

SELECTION CRITERIA:
1. Response length and content complexity
2. User's specific animation requests (dance, martial arts, celebrations, etc.)
3. Emotional context and mood
4. Content appropriateness for the selected animation

Use 'idle' for neutral conversation, 'emotional' for emotions, specific animations for explicit requests.`;
    },

    /**
     * Internal helper for selection rules
     */
    getSelectionRules: async function () {
        try {
            const { LLM_ANIMATION_SYSTEM } = await import('../../animation/AnimationConfig.js');
            return LLM_ANIMATION_SYSTEM.generateSelectionRules();
        } catch (error) {
            console.error('Failed to load dynamic selection rules:', error);
            return `ANIMATION SELECTION RULES:
1. Use EXACT animation IDs from registry - ZERO tolerance for variations
2. Choose specific animations ONLY for explicit user dance/action requests
3. Animation ID MUST exist in the provided registry list`;
        }
    },

    /**
     * Generate dynamic positive response examples
     */
    getPositiveResponseExamples: async function () {
        try {
            const { LLM_ANIMATION_SYSTEM } = await import('../../animation/AnimationConfig.js');
            return LLM_ANIMATION_SYSTEM.generatePositiveResponseExamples();
        } catch (error) {
            console.error('Failed to load dynamic positive response examples:', error);
            return `"Dance!" → {"animation": "dance_silly", "responseText": "Let me show you some moves!"}
"Hello!" → {"animation": "greeting", "responseText": "Hi there! How can I help?"}
"Martial arts!" → {"animation": "capoeira_kick", "responseText": "Watch this capoeira kick!"}
"Celebrate!" → {"animation": "jump_joy", "responseText": "Let's celebrate together!"}`;
        }
    }
};

/**
 * Helper functions for prompt building and configuration
 */

/**
 * Build system prompt with detected language and context
 */
export function buildSystemPrompt(detectedLanguage, gender = null, mood = 'neutral') {
    let prompt = BASE_PROMPT_COMPONENTS.BASE_SYSTEM_PROMPT.replace(/\[DETECTED_LANGUAGE\]/g, detectedLanguage);

    if (gender && BASE_PROMPT_COMPONENTS.CONTEXT_CONFIGS.gender[gender]) {
        prompt += ` ${BASE_PROMPT_COMPONENTS.CONTEXT_CONFIGS.gender[gender]}`;
    }

    if (mood && BASE_PROMPT_COMPONENTS.CONTEXT_CONFIGS.mood[mood]) {
        prompt += ` ${BASE_PROMPT_COMPONENTS.CONTEXT_CONFIGS.mood[mood]}`;
    }

    return prompt;
}

/**
 * Get media processing instruction
 */
export function getMediaInstruction(mediaType) {
    return BASE_PROMPT_COMPONENTS.CONTEXT_CONFIGS.media[mediaType] || '';
}

/**
 * Map ASR language codes to full language names
 */
export function mapASRLanguageToFullName(languageCode) {
    const languageMap = {
        'zh': 'Chinese (Mandarin)', 'en': 'English', 'ja': 'Japanese',
        'ko': 'Korean', 'yue': 'Chinese (Cantonese)'
    };
    return languageMap[languageCode] || languageCode;
}

/**
 * Create ASR context with language consistency enforcement
 */
export function createASRContext(asrMetadata) {
    console.log('[createASRContext] Input ASR metadata:', asrMetadata);

    if (!asrMetadata) {
        console.log('[createASRContext] No ASR metadata provided');
        return { context: [], languageRule: '', ttsLanguage: null };
    }

    const asrContext = [];
    let languageRule = '';
    let ttsLanguage = null;

    // Process detected language (support both new and legacy format)
    const detectedLanguage = asrMetadata.detectedLanguage || asrMetadata.language;

    if (detectedLanguage) {
        console.log('[createASRContext] Processing detected language:', detectedLanguage);

        const mappedLanguage = mapASRLanguageToFullName(detectedLanguage);
        ttsLanguage = BASE_PROMPT_COMPONENTS.LANGUAGE_SYSTEM.TTS_MAPPING[detectedLanguage] || 'english';

        // Add language context
        asrContext.push(
            BASE_PROMPT_COMPONENTS.LANGUAGE_SYSTEM.CONTEXT_TEMPLATES.asr
                .replace('{code}', detectedLanguage)
                .replace('{language}', mappedLanguage)
        );

        // Create language consistency rule
        languageRule = BASE_PROMPT_COMPONENTS.LANGUAGE_SYSTEM.ENFORCEMENT_TEMPLATE
            .replace(/{language}/g, mappedLanguage)
            .replace(/{code}/g, detectedLanguage);

        console.log('[createASRContext] TTS language mapping:', ttsLanguage);
    }

    // Process emotion (support both new and legacy format)
    const detectedEmotion = asrMetadata.detectedEmotion || asrMetadata.emotion;
    if (detectedEmotion) {
        asrContext.push(
            BASE_PROMPT_COMPONENTS.LANGUAGE_SYSTEM.CONTEXT_TEMPLATES.emotion
                .replace('{emotion}', detectedEmotion)
        );
    }

    // Process events (support both new and legacy format)
    const detectedEvents = asrMetadata.detectedEvents || asrMetadata.events;
    if (detectedEvents && detectedEvents.length > 0) {
        asrContext.push(
            BASE_PROMPT_COMPONENTS.LANGUAGE_SYSTEM.CONTEXT_TEMPLATES.events
                .replace('{events}', detectedEvents.join(', '))
        );
    }

    return { context: asrContext, languageRule, ttsLanguage };
}

/**
 * Animation system integration functions
 */

/**
 * Generate animation categories description (optimized for brevity)
 */
export async function generateAnimationCategoriesDescription(includeDescriptions = false) {
    try {
        const { ANIMATION_REGISTRY } = await import('../../animation/AnimationConfig.js');
        const descriptions = [];

        // Generate concise category descriptions, excluding auto-handled communication
        for (const [categoryName, animations] of Object.entries(ANIMATION_REGISTRY.byCategory)) {
            if (categoryName === 'communication') continue; // Auto-handled during TTS

            const animationList = includeDescriptions
                ? animations.map(anim => anim.description ? `${anim.id}(${anim.description})` : anim.id).join(', ')
                : animations.map(anim => anim.id).join(', ');

            const categoryDesc = ANIMATION_REGISTRY.definitions[categoryName]?.description || categoryName;
            descriptions.push(`- ${categoryName}: ${animationList} (${categoryDesc})`);
        }

        return {
            baseAnimations: 'auto-triggered during speech',
            categoryDescriptions: descriptions.join('\n') + '\n\nNOTE: Talking animations auto-trigger during TTS.'
        };
    } catch (error) {
        console.error('[LLM] Failed to generate animation categories:', error);
        return {
            baseAnimations: 'auto-triggered during speech',
            categoryDescriptions: '- performance: dance animations\n- movement: action animations\nNOTE: Talking animations auto-trigger during TTS.'
        };
    }
}

/**
 * Generate comprehensive animation prompt section
 * Consolidates all animation rules, categories, and IDs into one optimized section
 */
export async function generateAnimationPromptSection() {
    try {
        const { LLM_ANIMATION_SYSTEM } = await import('../../animation/AnimationConfig.js');

        // Get all dynamic components
        const completeRules = await ANIMATION_PROMPT_TEMPLATES.getCompleteAnimationRules();

        // The complete rules already include all animation IDs in the selection rules
        // No need to repeat them in separate sections
        return completeRules;

    } catch (error) {
        console.error('[LLM] Failed to generate animation prompt section:', error);
        return `CRITICAL: Respond in JSON format: {"animation": "name", "responseText": "response"}

RULES:
1. Use EXACT animation IDs from registry
2. Choose animations for explicit requests only
3. Use 'idle' for normal conversation

Basic selection: 'idle' for conversation, specific animations for requests.`;
    }
}