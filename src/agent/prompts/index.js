/**
 * LangChain Prompt Components
 * 
 * This module contains all prompt-related functionality separated from the server/llm.ts
 * It provides context-aware system prompts, animation templates, and conversation management
 * specifically designed for LangChain.js integration.
 */

import { ANIMATION_REGISTRY } from '../../animation/AnimationConfig.js';

/**
 * Core prompt components for LangChain-based LLM integration
 */
export const LANGCHAIN_PROMPT_COMPONENTS = {
    /**
     * Base system personas with personality traits
     */
    PERSONALITIES: {
        'english': {
            base: 'You are a helpful, friendly, and knowledgeable assistant. You communicate clearly and professionally.',
            casual: 'You are a relaxed, friendly assistant who speaks naturally and conversationally.',
            formal: 'You are a professional, articulate assistant who maintains formal language and structure.',
            enthusiastic: 'You are an energetic, positive assistant who shows excitement and enthusiasm in responses.'
        },
        'chinese': {
            base: '你是一个有用、友好且博学的助手。你的交流清晰且专业。',
            casual: '你是一个放松、友好的助手，说话自然且口语化。',
            formal: '你是一个专业、articulate的助手，保持正式的语言和结构。',
            enthusiastic: '你是一个充满活力、积极的助手，在回应中表现出兴奋和热情。'
        }
    },

    /**
     * Mood-based behavioral traits
     */
    MOOD_TRAITS: {
        'happy': 'Your responses are enthusiastic, positive, and upbeat. You use cheerful language and express joy in helping.',
        'sad': 'Your responses are gentle, empathetic, and understanding. You speak with a softer tone and show compassion.',
        'angry': 'Your responses are direct, firm, and assertive. You speak with intensity while remaining respectful.',
        'fear': 'Your responses show caution and concern. You are alert and careful in your advice.',
        'disgust': 'Your responses show aversion to unpleasant topics. You maintain politeness while expressing distaste.',
        'love': 'Your responses are warm, caring, and affectionate. You show genuine interest and appreciation.',
        'excited': 'Your responses are energetic, enthusiastic, and dynamic. You show high energy and engagement.',
        'neutral': 'Your responses are balanced, calm, and measured. You maintain professional composure.',
        'friendly': 'Your responses are warm, approachable, and welcoming. You create a comfortable atmosphere.'
    },

    /**
     * Gender-specific language adjustments
     */
    GENDER_TRAITS: {
        'male': 'Use confident, direct language patterns with assertive communication style.',
        'female': 'Use expressive, empathetic language patterns with collaborative communication style.',
        'neutral': 'Use balanced language patterns that are neither overly assertive nor overly expressive.'
    },

    /**
     * Media processing instructions for multimodal input
     */
    MEDIA_PROCESSING: {
        'audio': 'Please transcribe and respond to this audio. Maintain consistent language throughout your entire response.',
        'video': 'Please describe what you see in this video. Maintain consistent language throughout your entire response.',
        'image': 'Please analyze and describe this image. Provide detailed observations and context.',
        'text': 'Process this text input and provide an appropriate response.'
    },

    /**
     * Create base system prompt with personality and mood
     */
    createBaseSystemPrompt(language = 'english', gender = '', mood = 'neutral', options = {}) {
        const personality = this.PERSONALITIES[language]?.base || this.PERSONALITIES['english'].base;
        const moodTrait = this.MOOD_TRAITS[mood] || this.MOOD_TRAITS['neutral'];
        const genderTrait = gender ? (this.GENDER_TRAITS[gender] || '') : '';

        let systemPrompt = personality;

        if (moodTrait) {
            systemPrompt += ' ' + moodTrait;
        }

        if (genderTrait) {
            systemPrompt += ' ' + genderTrait;
        }

        // Add custom role or instructions
        if (options.role) {
            systemPrompt += ` You are specifically acting as a ${options.role}.`;
        }

        if (options.customPrompt) {
            systemPrompt = options.customPrompt;
        }

        return systemPrompt;
    },

    /**
     * Add ASR metadata context to prompts
     */
    addASRContext(basePrompt, asrMetadata) {
        if (!asrMetadata) return basePrompt;

        const asrContext = [];

        // Add language detection context
        if (asrMetadata.detectedLanguage) {
            asrContext.push(`User's detected language: ${asrMetadata.detectedLanguage}`);
        }

        // Add emotion detection context
        if (asrMetadata.detectedEmotion && asrMetadata.detectedEmotion !== 'neutral') {
            asrContext.push(`User's detected emotion: ${asrMetadata.detectedEmotion}`);
        }

        // Add confidence level context
        if (typeof asrMetadata.confidence === 'number') {
            const confidenceLevel = asrMetadata.confidence > 0.8 ? 'high' :
                asrMetadata.confidence > 0.5 ? 'medium' : 'low';
            asrContext.push(`Speech recognition confidence: ${confidenceLevel}`);
        }

        // Add audio quality context
        if (asrMetadata.audioQuality) {
            asrContext.push(`Audio quality: ${asrMetadata.audioQuality}`);
        }

        if (asrContext.length > 0) {
            return basePrompt + '\n\nAudio Context: ' + asrContext.join('. ') + '.';
        }

        return basePrompt;
    },

    /**
     * Add conversation history context
     */
    addConversationHistory(basePrompt, history, maxTurns = 3) {
        if (!history || history.length === 0) return basePrompt;

        const recentHistory = history.slice(-maxTurns * 2); // Keep last N conversation pairs
        const historyContext = recentHistory
            .map(msg => `${msg.role}: ${msg.content}`)
            .join('\n');

        return basePrompt + '\n\nRecent Conversation:\n' + historyContext;
    }
};
