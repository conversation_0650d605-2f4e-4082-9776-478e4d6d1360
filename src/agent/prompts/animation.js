/**
 * Animation-specific components for LangChain tool calling
 * Uses vector stores for semantic animation matching
 */

import { MemoryVectorStore } from 'langchain/vectorstores/memory';
import { OpenAIEmbeddings } from '@langchain/openai';
import { Document } from '@langchain/core/documents';
import { ANIMATION_REGISTRY } from '../../animation/AnimationConfig.js';

/**
 * Animation semantic search service using LangChain vector stores
 */
class AnimationVectorService {
    constructor() {
        this.vectorStore = null;
        this.embeddings = null;
        this.initialized = false;
        this.animationDocuments = [];
    }

    /**
     * Initialize vector store with animation data
     */
    async initialize() {
        if (this.initialized) return;

        try {
            // Initialize embeddings (only in Node.js environment)
            if (typeof window === 'undefined') {
                this.embeddings = new OpenAIEmbeddings({
                    modelName: "text-embedding-ada-002",
                    openAIApiKey: process.env.OPENAI_API_KEY || 'fallback'
                });
            } else {
                // In browser, skip embeddings and use fallback search
                console.log('[AnimationVectorService] Browser environment detected - using fallback search');
                this.initialized = false;
                return;
            }

            // Create vector store
            this.vectorStore = new MemoryVectorStore(this.embeddings);

            // Prepare animation documents for vector store
            await this._prepareAnimationDocuments();

            // Add documents to vector store
            if (this.animationDocuments.length > 0) {
                await this.vectorStore.addDocuments(this.animationDocuments);
            }

            this.initialized = true;
            console.log(`[AnimationVectorService] Initialized with ${this.animationDocuments.length} animations`);

        } catch (error) {
            console.error('[AnimationVectorService] Failed to initialize:', error);
            // Fall back to basic string matching
            this.initialized = false;
        }
    }

    /**
     * Find best animation match using semantic search
     * @param {string} query - User query or animation description
     * @param {number} k - Number of results to return
     * @returns {Promise<string|null>} - Best matching animation ID
     */
    async findBestMatch(query, k = 3) {
        if (!this.initialized) {
            await this.initialize();
        }

        if (!this.initialized) {
            // Fallback to registry-based matching
            return this._fallbackSearch(query);
        }

        try {
            // Perform semantic search
            const results = await this.vectorStore.similaritySearch(query, k);

            if (results.length > 0) {
                // Return the best match
                const bestMatch = results[0];
                const animationId = bestMatch.metadata.animationId;

                console.log(`[AnimationVectorService] Found semantic match: "${query}" → "${animationId}" (score: ${bestMatch.metadata.score || 'N/A'})`);
                return animationId;
            }

            return null;

        } catch (error) {
            console.error('[AnimationVectorService] Search failed:', error);
            return this._fallbackSearch(query);
        }
    }

    /**
     * Search with metadata filtering
     * @param {string} query - Search query
     * @param {Object} filter - Metadata filter
     * @returns {Promise<Array>} - Filtered results
     */
    async searchWithFilter(query, filter = {}) {
        if (!this.initialized) {
            await this.initialize();
        }

        if (!this.initialized) {
            return [];
        }

        try {
            return await this.vectorStore.similaritySearch(query, 5, filter);
        } catch (error) {
            console.error('[AnimationVectorService] Filtered search failed:', error);
            return [];
        }
    }

    /**
     * Prepare animation documents for vector search
     * @private
     */
    async _prepareAnimationDocuments() {
        try {
            const allAnimations = ANIMATION_REGISTRY.getAllAnimations();
            this.animationDocuments = [];

            for (const [animationId, animation] of Object.entries(allAnimations)) {
                // Create searchable content
                const searchableContent = [
                    animation.id,
                    animation.name || animation.id,
                    animation.description || '',
                    animation.category || '',
                    animation.subcategory || '',
                    // Add common synonyms/variations
                    this._generateAnimationSynonyms(animation)
                ].filter(Boolean).join(' ');

                // Create document for vector store
                const document = new Document({
                    pageContent: searchableContent,
                    metadata: {
                        animationId: animation.id,
                        category: animation.category,
                        subcategory: animation.subcategory,
                        description: animation.description,
                        name: animation.name || animation.id
                    }
                });

                this.animationDocuments.push(document);
            }

        } catch (error) {
            console.error('[AnimationVectorService] Failed to prepare documents:', error);
        }
    }

    /**
     * Generate synonyms and variations for better matching
     * @param {Object} animation - Animation object
     * @returns {string} - Space-separated synonyms
     * @private
     */
    _generateAnimationSynonyms(animation) {
        const synonyms = [];
        const id = animation.id.toLowerCase();

        // Common animation synonyms
        const synonymMap = {
            'dance': ['dancing', 'moves', 'rhythm', 'boogie', 'groove'],
            'wave': ['waving', 'hello', 'greeting', 'hi'],
            'martial': ['fight', 'combat', 'karate', 'kung fu'],
            'happy': ['joy', 'cheerful', 'excited', 'positive'],
            'sad': ['sorrow', 'melancholy', 'down', 'blue'],
            'angry': ['mad', 'furious', 'rage', 'upset'],
            'jump': ['leap', 'bounce', 'hop'],
            'kick': ['strike', 'hit', 'attack'],
            'punch': ['jab', 'strike', 'hit'],
            'talking': ['speaking', 'conversation', 'chat', 'discussion']
        };

        // Add synonyms based on animation ID parts
        for (const [key, variations] of Object.entries(synonymMap)) {
            if (id.includes(key)) {
                synonyms.push(...variations);
            }
        }

        // Add category-based synonyms
        if (animation.category === 'dance') {
            synonyms.push('choreography', 'performance', 'entertainment');
        } else if (animation.category === 'combat') {
            synonyms.push('martial arts', 'self defense', 'fighting');
        } else if (animation.category === 'emotion') {
            synonyms.push('feeling', 'mood', 'expression');
        }

        return synonyms.join(' ');
    }

    /**
     * Fallback search using simple string matching
     * @param {string} query - Search query
     * @returns {string|null} - Best matching animation ID
     * @private
     */
    _fallbackSearch(query) {
        const allAnimations = ANIMATION_REGISTRY.getAllAnimations();
        const normalizedQuery = query.toLowerCase();

        // Direct ID match
        for (const [animationId, animation] of Object.entries(allAnimations)) {
            if (animation.id.toLowerCase() === normalizedQuery) {
                return animation.id;
            }
        }

        // Partial match
        for (const [animationId, animation] of Object.entries(allAnimations)) {
            if (animation.id.toLowerCase().includes(normalizedQuery) ||
                normalizedQuery.includes(animation.id.toLowerCase())) {
                console.log(`[AnimationVectorService] Fallback match: "${query}" → "${animation.id}"`);
                return animation.id;
            }
        }

        return null;
    }
}

// Create singleton instance
const animationVectorService = new AnimationVectorService();

/**
 * Animation prompt components for LangChain tool calling
 * Simplified since direct tool calling handles most logic
 */
export const ANIMATION_PROMPT_COMPONENTS = {
    /**
     * Basic tool calling rules (minimal since LangChain handles this)
     */
    TOOL_CALLING_RULES: {
        ENTHUSIASM_RULE: `When triggering animations, be enthusiastic and positive. You ARE performing the action - act like it!`,

        BASIC_EXAMPLES: [
            { input: "Dance for me!", expectedTool: "select_animation", response: "Let me show you some moves!" },
            { input: "Do martial arts!", expectedTool: "select_animation", response: "Watch this amazing technique!" },
            { input: "Celebrate!", expectedTool: "select_animation", response: "Let's celebrate together!" }
        ]
    },

    /**
     * Find best animation match using semantic search
     * @param {string} query - User query or animation request
     * @returns {Promise<string|null>} - Best matching animation ID
     */
    async findBestAnimationMatch(query) {
        return await animationVectorService.findBestMatch(query);
    },

    /**
     * Search animations by category using vector store
     * @param {string} query - Search query
     * @param {string} category - Animation category filter
     * @returns {Promise<Array>} - Matching animations
     */
    async searchAnimationsByCategory(query, category) {
        return await animationVectorService.searchWithFilter(query, { category });
    },

    /**
     * Get animation recommendations based on context
     * @param {Object} context - Conversation context
     * @returns {Promise<Array>} - Recommended animation IDs
     */
    async getAnimationRecommendations(context = {}) {
        const recommendations = [];

        // Context-based recommendations
        if (context.mood === 'happy' || context.emotion === 'joy') {
            const matches = await animationVectorService.searchWithFilter('happy cheerful excited', { category: 'emotion' });
            recommendations.push(...matches.map(m => m.metadata.animationId));
        }

        if (context.userIntent?.includes('dance')) {
            const matches = await animationVectorService.searchWithFilter('dance dancing moves', { category: 'dance' });
            recommendations.push(...matches.map(m => m.metadata.animationId));
        }

        if (context.userIntent?.includes('greet')) {
            const matches = await animationVectorService.searchWithFilter('greeting hello wave', {});
            recommendations.push(...matches.map(m => m.metadata.animationId));
        }

        // Remove duplicates and limit results
        return [...new Set(recommendations)].slice(0, 5);
    },

    /**
     * Initialize the vector service (call this once during app startup)
     */
    async initialize() {
        await animationVectorService.initialize();
    },

    /**
     * Get all available animations for tool schema
     * @returns {Array} - Array of animation IDs with descriptions
     */
    getAvailableAnimations() {
        try {
            const allAnimations = ANIMATION_REGISTRY.getAllAnimations();
            return Object.values(allAnimations).map(anim => ({
                id: anim.id,
                description: anim.description || anim.name || anim.id,
                category: anim.category
            }));
        } catch (error) {
            console.error('[AnimationPrompts] Failed to get available animations:', error);
            return [];
        }
    }
};

// Export the semantic search functions that replace the old string similarity
export const semanticAnimationSearch = {
    findBestMatch: (query) => animationVectorService.findBestMatch(query),
    searchByCategory: (query, category) => animationVectorService.searchWithFilter(query, { category }),
    initialize: () => animationVectorService.initialize()
};
