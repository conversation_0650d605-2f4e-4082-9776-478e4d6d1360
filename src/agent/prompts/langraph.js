/**
 * LangGraph-specific prompting system
 * Optimized prompts for agent mode with realtime capabilities
 * Uses hyperparameters for dynamic tool information integration
 * Web-optimized for browser environments
 */

import {
    buildSystemPrompt,
    createASRContext,
    getMediaInstruction,
    BASE_PROMPT_COMPONENTS
} from './base.js';
import { SystemPromptManager } from './utils.js';
import { createLogger, LogLevel } from '@/utils/logger.js';

// Initialize loggers
const logger = createLogger('LangGraphPrompts');
const promptLogger = new SystemPromptManager({
    enableLogging: true,
    logLevel: 'detailed'
});

/**
 * Dynamic tool information provider for LangGraph prompts
 * Automatically imports and formats available tools information
 */
async function getAvailableToolsInfo(options = {}) {
    const { includeDescriptions = true, includeExamples = true } = options;

    try {
        // Dynamically import tool manager to get current tools
        const { toolManager } = await import('../tools/index.js');

        const availableTools = toolManager.getAllTools();
        const toolsByCategory = {};

        // Group tools by category
        for (const tool of availableTools) {
            const category = toolManager.toolCategories.get(tool.name) || 'general';
            if (!toolsByCategory[category]) {
                toolsByCategory[category] = [];
            }
            toolsByCategory[category].push({
                name: tool.name,
                description: tool.description || 'No description available'
            });
        }

        // Format tool information for prompt
        let toolsInfo = '';

        for (const [category, tools] of Object.entries(toolsByCategory)) {
            const categoryName = category.charAt(0).toUpperCase() + category.slice(1);
            toolsInfo += `\n### ${categoryName} Tools:\n`;

            for (const tool of tools) {
                toolsInfo += `- **${tool.name}**: ${includeDescriptions ? tool.description : 'Available'}\n`;
            }
        }

        // Add usage examples for key tools
        if (includeExamples) {
            toolsInfo += `\n### Key Tool Usage Examples:\n`;
            toolsInfo += `- speak_response: Use for all conversational responses\n`;
            toolsInfo += `- Animation tools: Use when user requests specific actions or emotions\n`;
        }

        return {
            toolsInfo,
            toolCount: availableTools.length,
            categories: Object.keys(toolsByCategory)
        };

    } catch (error) {
        logger.warn('Failed to load dynamic tools info:', error);
        // Fallback to static tool information
        return {
            toolsInfo: `\n### Available Tools:\n- speak_response: Convert text to speech\n- Animation tools: Trigger avatar animations\n`,
            toolCount: 2,
            categories: ['tts', 'animation']
        };
    }
}

/**
 * Create optimized LangGraph system prompt
 * Uses dynamic tool integration for enhanced agent capabilities
 */
export async function createLangGraphSystemPrompt(options = {}) {
    const {
        language = 'english',
        gender = 'neutral',
        mood = 'neutral',
        sessionId = 'default',
        asrMetadata = null,
        mediaType = 'text',
        userInput = '',
        includeHistory = true,
        includeToolInfo = true,
        // Internal flag to disable separate logging when called from unified logging
        _suppressLogging = false
    } = options;

    // Only log if not being called from unified logging
    if (!_suppressLogging) {
        logger.info('Creating optimized LangGraph prompt:', {
            language, gender, mood, hasASRMetadata: !!asrMetadata, mediaType, includeToolInfo
        });
    }

    // Start with official Qwen-Omni system prompt for audio output compatibility
    let systemPrompt = `You are Qwen, a virtual human developed by the Qwen Team, Alibaba Group, capable of perceiving auditory and visual inputs, as well as generating text and speech.`;

    // Add language context if ASR detected different language
    if (asrMetadata?.detectedLanguage && asrMetadata.detectedLanguage !== 'en') {
        const asrContext = createASRContext(asrMetadata);
        systemPrompt += `\n\nLanguage Context: User is speaking in ${asrMetadata.detectedLanguage}. Respond in the same language for natural conversation flow.`;

        if (asrContext.languageRule) {
            systemPrompt += `\n${asrContext.languageRule}`;
        }
    }

    // Add personality context only if different from default
    if (gender !== 'neutral' || mood !== 'neutral') {
        systemPrompt += `\n\nPersonality: `;
        if (gender !== 'neutral') {
            systemPrompt += `${gender} voice, `;
        }
        if (mood !== 'neutral') {
            systemPrompt += `${mood} mood.`;
        }
    }

    // Dynamically add tool information using hyperparameters
    if (includeToolInfo) {
        const toolsInfo = await getAvailableToolsInfo({
            includeDescriptions: true,
            includeExamples: false
        });

        systemPrompt += `\n\n## Available Tools (${toolsInfo.toolCount} tools):${toolsInfo.toolsInfo}`;

        // Remove explicit instructions - let LangGraph handle tool calling naturally
        // Tools will be available automatically through model.bindTools()

        // Tools info will be logged by the SystemPromptManager in utils.js
        // No need to log here - keep langraph.js focused on prompt creation
    }

    // Add media processing instructions only if needed
    if (mediaType && mediaType !== 'text') {
        const mediaInstruction = getMediaInstruction(mediaType);
        if (mediaInstruction) {
            systemPrompt += `\n\n## Media Processing:\n${mediaInstruction}`;
        }
    }

    // Minimal response guidelines (avoid duplication)
    systemPrompt += `\n\n## Response Style:\n`;
    systemPrompt += `- Be conversational and engaging\n`;
    systemPrompt += `- Use tools to enhance interaction quality\n`;
    systemPrompt += `- Maintain language consistency with user input\n`;

    // Only log separately if not being called from unified logging
    if (!_suppressLogging) {
        // Log optimized prompt creation
        const request = {
            operation: 'LangGraph Optimized Prompt',
            language,
            toolsIncluded: includeToolInfo,
            promptLength: systemPrompt.length,
            mediaType,
            hasASRMetadata: !!asrMetadata
        };

        promptLogger.logSystemPrompt('createLangGraphSystemPrompt', request, systemPrompt, {
            optimized: true,
            toolsAutoLoaded: includeToolInfo,
            duplicationsRemoved: true,
            webOptimized: true
        });

        logger.info('Generated optimized prompt:', {
            length: systemPrompt.length,
            toolsIncluded: includeToolInfo,
            categories: includeToolInfo ? (await getAvailableToolsInfo()).categories : []
        });
    }

    return systemPrompt;
}

/**
 * Create context-aware user message with ASR metadata
 * Enhanced metadata integration for agent mode
 */
export function createLangGraphUserMessage(input, options = {}) {
    const {
        asrMetadata = null,
        mediaType = 'text',
        includeContext = true
    } = options;

    let userMessage = '';

    // Handle different input types
    if (typeof input === 'string') {
        userMessage = input;
    } else if (input && typeof input === 'object') {
        userMessage = input.text || '';

        // Add multimodal context naturally
        if (input.audio) {
            userMessage += '\n[Audio input provided]';
        }
        if (input.video || input.videoFrames) {
            const frameCount = (input.video || input.videoFrames || []).length;
            userMessage += `\n[Video context: ${frameCount} frames provided]`;
        }
    }

    // Add ASR context naturally for enhanced understanding
    if (includeContext && asrMetadata) {
        const contextDetails = [];

        if (asrMetadata.detectedLanguage) {
            contextDetails.push(`Language: ${asrMetadata.detectedLanguage}`);
        }
        if (asrMetadata.detectedEmotion && asrMetadata.detectedEmotion !== 'neutral') {
            contextDetails.push(`Emotion: ${asrMetadata.detectedEmotion}`);
        }
        if (asrMetadata.detectedEvents && asrMetadata.detectedEvents.length > 0) {
            contextDetails.push(`Audio events: ${asrMetadata.detectedEvents.join(', ')}`);
        }
        if (asrMetadata.confidence !== undefined) {
            contextDetails.push(`Confidence: ${Math.round(asrMetadata.confidence * 100)}%`);
        }

        if (contextDetails.length > 0) {
            userMessage += `\n[${contextDetails.join(', ')}]`;
        }
    }

    // Log user message creation with context
    if (asrMetadata || mediaType !== 'text') {
        const userRequest = {
            operation: 'LangGraph User Message Creation',
            mediaType,
            hasASRMetadata: !!asrMetadata,
            messageLength: userMessage.length,
            inputType: typeof input
        };

        promptLogger.logValidation(userMessage, {
            hasASRContext: !!(asrMetadata && includeContext),
            mediaContext: mediaType !== 'text',
            messageType: 'user_input'
        });
    }

    return userMessage;
}

/**
 * Validate and normalize LangGraph prompt options
 */
export function validateLangGraphOptions(options = {}) {
    const validated = {
        language: options.language || 'english',
        gender: ['male', 'female', 'neutral'].includes(options.gender) ? options.gender : 'neutral',
        mood: ['neutral', 'happy', 'sad', 'angry', 'fear', 'disgust', 'love'].includes(options.mood) ? options.mood : 'neutral',
        sessionId: options.sessionId || 'default',
        mediaType: ['text', 'audio', 'video', 'audio-video'].includes(options.mediaType) ? options.mediaType : 'text',
        includeHistory: options.includeHistory !== false,
        enableAnimations: options.enableAnimations !== false,
        enableTTS: options.enableTTS !== false
    };

    // Validate ASR metadata structure if provided
    if (options.asrMetadata) {
        validated.asrMetadata = {
            detectedLanguage: options.asrMetadata.detectedLanguage || null,
            detectedEmotion: options.asrMetadata.detectedEmotion || 'neutral',
            detectedEvents: Array.isArray(options.asrMetadata.detectedEvents) ? options.asrMetadata.detectedEvents : [],
            confidence: typeof options.asrMetadata.confidence === 'number' ? options.asrMetadata.confidence : 1.0,
            source: options.asrMetadata.source || 'unknown'
        };
    }

    // Log validation results with detailed formatting
    const validationRequest = {
        operation: 'LangGraph Options Validation',
        originalOptionsCount: Object.keys(options).length,
        validatedOptionsCount: Object.keys(validated).length,
        hasASRMetadata: !!options.asrMetadata,
        hasInvalidValues: JSON.stringify(options) !== JSON.stringify({ ...options, ...validated })
    };

    logger.info('Options validation completed:', {
        language: `${options.language || 'default'} → ${validated.language}`,
        gender: `${options.gender || 'default'} → ${validated.gender}`,
        mood: `${options.mood || 'default'} → ${validated.mood}`,
        mediaType: `${options.mediaType || 'default'} → ${validated.mediaType}`,
        hasASRMetadata: !!validated.asrMetadata,
        validationApplied: validationRequest.hasInvalidValues
    });

    return validated;
}

/**
 * Memory management integration for LangGraph
 * Memory manager initialization is handled in core.js
 */

/**
 * Get memory context for LangGraph prompts
 * Note: This function will be called from core.js which manages the memory manager
 */
export async function getMemoryContext(sessionId, memoryManager, options = {}) {
    try {
        if (!memoryManager) {
            logger.warn('No memory manager provided');
            return { conversation_history: [], conversation_summary: '', memory_stats: {} };
        }

        // Handle LangChain memory manager
        if (memoryManager.getMemoryContext) {
            const context = await memoryManager.getMemoryContext(sessionId);
            return {
                ...context,
                memory_stats: memoryManager.getMemoryStats(),
                memory_type: 'langchain'
            };
        }

        // Handle Mem0 memory manager
        if (memoryManager.search) {
            const searchResults = await memoryManager.search('conversation context', sessionId, [sessionId]);
            return {
                conversation_history: searchResults.results || [],
                conversation_summary: '',
                memory_stats: await memoryManager.getStats(),
                memory_type: 'mem0'
            };
        }

        return { conversation_history: [], conversation_summary: '', memory_stats: {} };
    } catch (error) {
        logger.error('Error getting memory context:', error);
        return { conversation_history: [], conversation_summary: '', memory_stats: {} };
    }
}

/**
 * Add conversation to memory
 * Note: This function will be called from core.js which manages the memory manager
 */
export async function addToMemory(sessionId, userMessage, aiResponse, memoryManager, metadata = {}) {
    try {
        if (!memoryManager) {
            logger.warn('No memory manager provided for adding conversation');
            return false;
        }

        // Handle LangChain memory manager
        if (memoryManager.addConversationTurn) {
            await memoryManager.addConversationTurn(sessionId, userMessage, aiResponse, metadata);
            logger.debug('Added conversation to LangChain memory');
            return true;
        }

        // Handle Mem0 memory manager
        if (memoryManager.add) {
            const messages = [
                { role: 'user', content: userMessage },
                { role: 'assistant', content: aiResponse }
            ];
            await memoryManager.add(messages, sessionId);
            logger.debug('Added conversation to Mem0 memory');
            return true;
        }

        return false;
    } catch (error) {
        logger.error('Error adding to memory:', error);
        return false;
    }
}

/**
 * Export the prompt logger for use in other modules
 * Allows core.js and other components to use the same logging instance
 */
export function getLangGraphPromptLogger() {
    return promptLogger;
}

/**
 * Hierarchical logging integration for agent workflow management
 */
export async function logSystemPromptHierarchical(requestMetadata, normalizedInput, options) {
    try {
        // Get tools info for passing to logging system (don't log here)
        const toolsInfo = await getAvailableToolsInfo({
            includeDescriptions: true,
            includeExamples: true
        });

        // Create system prompt for logging (suppress individual logging since this is unified)
        const systemPrompt = await createLangGraphSystemPrompt({
            ...options,
            _suppressLogging: true
        });

        // Create pseudo-request object for compatibility with SystemPromptManager
        const request = {
            prompt: normalizedInput.text || 'LangGraph agent input',
            language: options.language || 'english',
            model: options.model || 'LangGraph-VLLM',
            temperature: options.temperature || 0.7,
            stream: !!options.stream
        };

        // Get memory context for unified logging
        const memoryContext = options.memoryContext || { conversation_history: [], conversation_summary: '', memory_stats: {} };

        // Use organized expandable logging like llm.ts (replaces scattered console.group calls)
        promptLogger.logSystemPrompt(
            `Unified LangGraph Agent Processing - ${requestMetadata.operation}`,
            request,
            systemPrompt,
            {
                streamProcessor: options.streamProcessor,
                ttsService: options.ttsService,
                skipTTS: options.skipTTS,
                asrMetadata: options.asrMetadata,
                sessionId: requestMetadata.sessionId,
                mediaType: requestMetadata.mediaType,
                backpressureEnabled: requestMetadata.backpressureEnabled,
                toolsAvailable: requestMetadata.toolsAvailable,
                toolNames: requestMetadata.toolNames,
                memoryContext: memoryContext,
                memoryEnabled: !!options.memoryManager,
                // Agent-specific metadata for organized logging
                inputPreview: normalizedInput.text?.substring(0, 30),
                hasTools: options.tools && options.tools.length > 0,
                toolCount: options.tools?.length || 0,
                memoryEntries: memoryContext.conversation_history?.length || 0,
                // Pass tools info to logging system instead of logging in langraph.js
                toolsInfo: toolsInfo
            }
        );

        // Brief tool availability check (single log line, no console groups)
        if (!options.tools || options.tools.length === 0) {
            logger.warn('⚠️ WARNING: No tools available! TTS functionality will not work.');
        }
    } catch (error) {
        logger.warn('Failed to log system prompt hierarchically:', error);
    }
}

/**
 * Log LangGraph response using consolidated SystemPromptManager approach
 */
export function logLangGraphResponse(operation, request, response, metadata = {}) {
    if (!promptLogger.options.enabled) return;

    // Use the SystemPromptManager for consistent, consolidated logging
    promptLogger.logLangGraphResponse(operation, request, response, metadata);
}