/**
 * <PERSON><PERSON> Bailian Chat Model
 * LangChain integration for <PERSON><PERSON>'s DashScope qwen-omni models with both HTTP and WebSocket (Realtime) API support
 */

import { BaseChatModel } from '@langchain/core/language_models/chat_models';
import { AIMessage, HumanMessage, SystemMessage } from '@langchain/core/messages';
import { createLogger, LogLevel } from '@/utils/logger';
import { MediaHandler } from './aliyun/media/MediaHandler';
import { RealtimeAudioManager } from '@/media/streaming/RealtimeAudioManager';
import { getEnvVar } from '@/config/env';
import { ALIYUN_AUDIO_CONFIG } from './aliyun/config/AliyunConfig';
import { AliyunRealtimeClient } from './aliyun/streaming/AliyunRealtimeClient.js';
// Add correct imports for prompts
import { buildSystemPrompt } from '../prompts/base.js';
import { LANGCHAIN_PROMPT_COMPONENTS } from '../prompts/index.js';
import OpenAI from 'openai';

export class AliyunBailianChatModel extends BaseChatModel {
    constructor(options = {}) {
        super(options);
        this.apiKey = options.apiKey || getEnvVar('VITE_DASHSCOPE_API_KEY', '');
        this.model = options.model || 'qwen-omni-turbo-realtime';

        // Determine API mode based on model name if not explicitly provided
        // qwen-omni-turbo-realtime models use WebSocket, qwen-omni-turbo models use HTTP
        const isRealtimeModel = this.model === 'qwen-omni-turbo-realtime' ||
            this.model?.includes('turbo-realtime');
        this.apiMode = options.apiMode || (isRealtimeModel ? 'websocket' : 'http'); // 'http' or 'websocket'
        this.modalities = options.modalities || ['text'];
        this.audioConfig = options.audioConfig || {
            voice: ALIYUN_AUDIO_CONFIG.defaultVoice,
            format: 'wav'
        };

        // Enhanced prompt support
        this.language = options.language || 'english';
        this.gender = options.gender || null;
        this.mood = options.mood || 'neutral';
        this.enablePrompts = options.enablePrompts !== false; // Default to true

        // Initialize logger with DEBUG level
        this.logger = createLogger('AliyunBailianChatModel', LogLevel.DEBUG);
        this.logger.debug('🔍 [ConnectionDebug] AliyunBailianChatModel initialized with DEBUG logging');

        // Initialize media handler for multimodal processing
        this.mediaHandler = new MediaHandler();

        // Initialize RealtimeAudioManager for audio streaming with centralized config
        this.realtimeAudioManager = new RealtimeAudioManager({
            sampleRate: ALIYUN_AUDIO_CONFIG.sampleRate,
            numChannels: ALIYUN_AUDIO_CONFIG.numChannels,
            bitDepth: ALIYUN_AUDIO_CONFIG.bitDepth,
            minIntervalMs: ALIYUN_AUDIO_CONFIG.minIntervalMs,
            enableDebugLogging: ALIYUN_AUDIO_CONFIG.enableDebugLogging,
            onError: (error) => this.logger.error('❌ [AudioManager] Error:', error)
        });

        // OpenAI client for both browser and server
        this.openai = null;

        // WebSocket for realtime mode handled by ConnectionManager
        this.realtimeSocket = null;
        this._sessionStabilized = false;

        // Realtime client handled by ConnectionManager directly
        this.realtimeClient = null;

        // Store bound tools for LangChain/LangGraph compatibility (v0.3 best practice)
        this.boundTools = [];

        // Bind play_audio as a default tool if tool calling is enabled and not already present
        if (options.tools || options.enableToolCalling) {
            const hasPlayAudio = (options.tools || []).some(tool => tool.name === 'play_audio');
            if (!hasPlayAudio) {
                // TODO: Define playAudioTool or remove this functionality
                // this.bindTools([playAudioTool]);
            }
        }

        this.logger.info(`[AliyunBailianChatModel] Model: ${this.model}, API Mode: ${this.apiMode}`);
        this.logger.info(`[AliyunBailianChatModel] Audio Config: ${ALIYUN_AUDIO_CONFIG.sampleRate}Hz, ${ALIYUN_AUDIO_CONFIG.bitDepth}-bit, ${ALIYUN_AUDIO_CONFIG.numChannels} channel`);

        // Log API key validation
        if (!this.apiKey) {
            this.logger.error('❌ [ConnectionDebug] Missing API key - cannot initialize Aliyun model');
        } else if (this.apiKey.length < 10) {
            this.logger.warn(`⚠️ [ConnectionDebug] API key appears invalid (length=${this.apiKey.length})`);
        } else {
            this.logger.debug('✅ [ConnectionDebug] API key validation passed');
        }
    }

    // Check if realtime mode is active
    isRealtimeModeActive() {
        // Check if AliyunRealtimeClient is connected for realtime operations
        return this.apiMode === 'websocket' &&
            this.realtimeClient &&
            this.realtimeClient.isSessionReady();
    }

    // LangChain compatibility method
    _llmType() {
        return 'aliyun-bailian';
    }

    /**
     * LangChain v0.3 compliance: Return invocation parameters for tracing
     * This method is called by LangChain to log metadata in traces
     */
    invocationParams(options = {}) {
        return {
            model_name: this.model,
            api_mode: this.apiMode,
            modalities: this.modalities,
            audio_config: this.audioConfig,
            enable_prompts: this.enablePrompts,
            language: this.language,
            gender: this.gender,
            mood: this.mood,
            tools: options?.tools || this.boundTools,
            tool_choice: options?.tool_choice,
            temperature: options?.temperature,
            max_tokens: options?.max_tokens
        };
    }

    /**
     * Bind tools for tool calling (LangChain v0.3 pattern)
     * Replaces any existing tool with the same name
     */
    bindTools(tools) {
        if (!Array.isArray(tools)) return;
        tools.forEach(newTool => {
            const idx = this.boundTools.findIndex(t => t.name === newTool.name);
            if (idx !== -1) {
                this.boundTools[idx] = newTool;
            } else {
                this.boundTools.push(newTool);
            }
        });
    }

    // Initialize OpenAI SDK for both browser and server environments
    async _initializeOpenAI() {
        try {
            if (!this.apiKey) {
                throw new Error('Aliyun DashScope API key is required');
            }

            this.openai = new OpenAI({
                apiKey: this.apiKey,
                baseURL: 'https://dashscope.aliyuncs.com/compatible-mode/v1',
                dangerouslyAllowBrowser: true // Allow browser usage
            });

            this.logger.info('OpenAI SDK initialized for server-side use');
        } catch (error) {
            this.logger.error('Failed to initialize OpenAI SDK:', error);
            throw error;
        }
    }

    // Main invoke method
    async invoke(messages, options = {}) {
        this.logger.debug(`[AliyunBailianChatModel] invoke called. Model instance:`, this);
        this.logger.debug(`[AliyunBailianChatModel] invoke function:`, this.invoke);
        this.logger.debug(`[AliyunBailianChatModel] Invoking with ${messages.length} messages, apiMode: ${this.apiMode}`);
        this.logger.debug(`[AliyunBailianChatModel] invoke options:`, options);

        if (this.apiMode === 'websocket') {
            return this._invokeWebSocket(messages, options);
        } else {
            return this._invokeHTTP(messages, options);
        }
    }

    /**
     * Initialize realtime mode for WebSocket-based streaming
     * Uses ConnectionManager for direct Aliyun connection
     * @param {Object} callbacks - Callback functions for realtime events
     * @returns {Promise<boolean>} Success status
     */
    async initializeRealtimeMode(callbacks = {}) {
        try {
            this.logger.info('🎙️ Initializing realtime mode with direct Aliyun connection...');

            // Close existing client if any
            if (this.realtimeClient) {
                await this.realtimeClient.disconnect();
                this.realtimeClient = null;
            }

            // Create universal realtime client for Aliyun
            this.realtimeClient = new AliyunRealtimeClient({
                apiKey: this.apiKey,
                model: this.model,
                modalities: this.modalities,
                voice: this.audioConfig.voice,
                endpoint: 'wss://dashscope.aliyuncs.com/api-ws/v1/realtime',
                enableDebugLogging: true
            });

            // Set up event handlers for universal realtime client
            this.realtimeClient.on('sessionReady', (session) => {
                this.logger.info('✅ [SessionDebug] Session ready for audio streaming');
                if (callbacks.onSessionReady) callbacks.onSessionReady(session);
            });

            this.realtimeClient.on('audioResponse', (audioData) => {
                if (callbacks.onAudioReceived) callbacks.onAudioReceived(audioData);
            });

            this.realtimeClient.on('error', (error) => {
                this.logger.error('❌ [RealtimeDebug] Connection error:', error);
                if (callbacks.onError) callbacks.onError(error);
            });

            this.realtimeClient.on('close', () => {
                this.logger.info('🔌 [RealtimeDebug] Connection closed');
            });

            // Connect to the service
            const connected = await this.realtimeClient.connect();
            if (!connected) {
                throw new Error('Failed to connect to realtime service');
            }

            // Session will be initialized automatically by AliyunRealtimeClient

            this.logger.info('✅ [ConnectionDebug] Direct Aliyun connection initialized successfully');
            return true;

        } catch (error) {
            this.logger.error('❌ [ConnectionDebug] Failed to initialize realtime mode:', error);
            this.realtimeClient = null;
            return false;
        }
    }

    /**
     * Send audio to realtime connection (optimized for new architecture)
     * @param {ArrayBuffer|Uint8Array} audioData - Audio data to send
     * @param {string} format - Audio format ('uint8array', 'arraybuffer', 'base64')
     * @returns {Promise<boolean>} Success status
     */
    async sendRealtimeAudio(audioData, format = 'uint8array') {
        if (!this.isRealtimeModeActive()) {
            this.logger.warn('❌ Cannot send realtime audio: Realtime mode not active');
            return false;
        }

        try {
            const success = await this.realtimeClient.sendAudio(audioData, format);
            if (success) {
                this.logger.debug('✅ [AudioDebug] Audio sent successfully');
            }
            return success;
        } catch (error) {
            this.logger.error('❌ [AudioDebug] Error sending audio:', error);
            return false;
        }
    }

    /**
     * Commit realtime audio buffer to trigger response
     * @returns {Promise<boolean>} Success status
     */
    async commitRealtimeAudio() {
        if (!this.isRealtimeModeActive()) {
            this.logger.warn('❌ [AudioDebug] Cannot commit audio: realtime mode not active');
            return false;
        }

        try {
            const success = await this.realtimeClient.commitAudio();
            if (success) {
                this.logger.debug('✅ [AudioDebug] Audio committed successfully');
            }
            return success;
        } catch (error) {
            this.logger.error('❌ [AudioDebug] Error committing audio:', error);
            return false;
        }
    }

    // Close realtime mode
    closeRealtimeMode() {
        if (this.realtimeClient) {
            this.realtimeClient.disconnect();
            this.realtimeClient = null;
        }

        // Legacy cleanup
        if (this.realtimeSocket) {
            this.realtimeSocket.close();
            this.realtimeSocket = null;
        }

        this._cleanupStreamingAudio();
    }

    /**
     * Clear realtime audio buffer
     * @returns {Promise<boolean>} Success status
     */
    async clearRealtimeAudio() {
        if (!this.isRealtimeModeActive()) {
            this.logger.warn('❌ [AudioDebug] Cannot clear audio: realtime mode not active');
            return false;
        }

        try {
            const success = await this.realtimeClient.clearAudio();
            if (success) {
                this.logger.debug('✅ [AudioDebug] Audio buffer cleared');
            }
            return success;
        } catch (error) {
            this.logger.error('❌ [AudioDebug] Error clearing audio:', error);
            return false;
        }
    }

    // HTTP mode implementation with enhanced message processing and prompts
    async _invokeHTTP(messages, options = {}) {
        this.logger.debug(`[AliyunBailianChatModel] _invokeHTTP called. Messages:`, messages);
        this.logger.debug(`[AliyunBailianChatModel] _invokeHTTP options:`, options);
        this.logger.debug(`[AliyunBailianChatModel] Processing ${messages.length} messages for HTTP API`);

        // 🧪 DEBUG: Comprehensive HTTP invoke debugging
        this.logger.debug('[🔍 DEBUG] _invokeHTTP entry point:', {
            messagesCount: messages.length,
            optionsKeys: Object.keys(options),
            hasOpenAI: !!this.openai,
            openAIInstance: this.openai,
            apiKey: this.apiKey ? '[REDACTED]' : 'MISSING',
            model: this.model,
            modalities: this.modalities,
            audioConfig: this.audioConfig,
            timestamp: new Date().toISOString()
        });

        try {
            // Ensure OpenAI client is initialized
            if (!this.openai) {
                this.logger.debug('[🔍 DEBUG] OpenAI client not initialized, calling _initializeOpenAI()');
                await this._initializeOpenAI();
                this.logger.debug('[🔍 DEBUG] _initializeOpenAI() completed, openai client:', !!this.openai);
            } else {
                this.logger.debug('[🔍 DEBUG] OpenAI client already initialized');
            }

            if (!this.openai) {
                this.logger.error('[🔍 DEBUG] CRITICAL: OpenAI client is still null after initialization!');
                throw new Error('Failed to initialize OpenAI client');
            }

            this.logger.debug('[🔍 DEBUG] OpenAI client verified, proceeding with message processing');
        } catch (error) {
            this.logger.error('[🔍 DEBUG] Error during OpenAI client initialization:', error);
            throw error;
        }

        // Prepare system prompt with context
        let systemPrompt = '';
        if (this.enablePrompts) {
            systemPrompt = buildSystemPrompt(this.language, this.gender, this.mood);

            // Add media context if needed
            const hasAudio = messages.some(m => m.extraModalities?.audioUrl || m.input_audio);
            const hasVideo = messages.some(m => m.extraModalities?.videoUrl);
            const hasImages = messages.some(m => m.extraModalities?.imageUrl || m.extraModalities?.images);

            if (hasAudio) {
                systemPrompt += ' ' + LANGCHAIN_PROMPT_COMPONENTS.MEDIA_PROCESSING.audio;
            }
            if (hasVideo) {
                systemPrompt += ' ' + LANGCHAIN_PROMPT_COMPONENTS.MEDIA_PROCESSING.video;
            }
            if (hasImages) {
                systemPrompt += ' ' + LANGCHAIN_PROMPT_COMPONENTS.MEDIA_PROCESSING.image;
            }
        }

        // Process and format messages using LangChain best practices
        const formattedMessages = await Promise.all(messages.map(async m => {
            // Use instanceof checks for LangChain message types (LangChain v0.3 best practice)
            let messageRole;
            if (m instanceof HumanMessage) {
                messageRole = 'user';
            } else if (m instanceof AIMessage) {
                messageRole = 'assistant';
            } else if (m instanceof SystemMessage) {
                messageRole = 'system';
            } else if (typeof m.role === 'string') {
                // Fallback for plain objects
                messageRole = m.role === 'user' ? 'user'
                    : m.role === 'assistant' ? 'assistant'
                        : m.role === 'system' ? 'system'
                            : 'user';
            } else {
                // Default fallback
                messageRole = 'user';
            }

            let messageContent = '';
            let contentArray = null;

            if (typeof m.content === 'string') {
                messageContent = m.content;
            } else if (Array.isArray(m.content)) {
                // Handle multimodal content array (Qwen-Omni format)
                contentArray = [];
                let textParts = [];

                for (const item of m.content) {
                    if (typeof item === 'string') {
                        textParts.push(item);
                    } else if (item.type === 'text') {
                        textParts.push(item.text || '');
                    } else if (item.type === 'image_url') {
                        contentArray.push({
                            type: 'image_url',
                            image_url: item.image_url
                        });
                        textParts.push('[Image provided]');
                    } else if (item.type === 'input_audio') {
                        // Qwen-Omni format for audio input
                        contentArray.push({
                            type: 'input_audio',
                            input_audio: item.input_audio
                        });
                        textParts.push('[Audio provided]');
                    } else if (item.type === 'video_url') {
                        // Qwen-Omni format for video input
                        contentArray.push({
                            type: 'video_url',
                            video_url: item.video_url
                        });
                        textParts.push('[Video provided]');
                    } else if (item.type === 'audio_url') {
                        textParts.push('[Audio provided]');
                    }
                }

                // Combine text parts
                messageContent = textParts.filter(Boolean).join(' ');

                // If we have text, add it to the content array
                if (messageContent && contentArray.length > 0) {
                    contentArray.unshift({ type: 'text', text: messageContent });
                } else if (contentArray.length === 0) {
                    // No multimodal content, just use text
                    contentArray = null;
                }
            }

            // Handle input_audio in HTTP mode - provide meaningful response
            if (m.input_audio) {
                this.logger.warn('⚠️ Audio input detected in HTTP mode - adding explanatory text');
                messageContent = messageContent || "I received audio input, but I'm currently in HTTP mode which doesn't support audio processing. Please provide text input or switch to realtime mode for audio support.";
            }

            // Ensure we have some content
            if (!messageContent || messageContent.trim().length === 0) {
                if (messageRole === 'user') {
                    messageContent = "Hello"; // Default user message
                } else {
                    return null; // Skip empty non-user messages
                }
            }

            // Return message with appropriate content format
            if (contentArray) {
                // Use multimodal content array from m.content processing
                return {
                    role: messageRole,
                    content: contentArray
                };
            } else if (m.extraModalities && Object.keys(m.extraModalities).length > 0) {
                // Handle legacy extraModalities format
                const legacyContentArray = [{ type: 'text', text: messageContent }];

                // Add single image
                if (m.extraModalities.imageUrl) {
                    legacyContentArray.push({
                        type: 'image_url',
                        image_url: { url: m.extraModalities.imageUrl }
                    });
                }

                // Add multiple images
                if (m.extraModalities.images?.length > 0) {
                    m.extraModalities.images.forEach(imageUrl => {
                        legacyContentArray.push({
                            type: 'image_url',
                            image_url: { url: imageUrl }
                        });
                    });
                }

                return {
                    role: messageRole,
                    content: legacyContentArray
                };
            } else {
                // Simple text message
                return {
                    role: messageRole,
                    content: messageContent
                };
            }
        }));

        // Filter out null messages and ensure we have at least one message
        let validMessages = formattedMessages.filter(Boolean);

        // IMPORTANT: Per Qwen-Omni documentation, filter out system messages when audio modality is enabled
        if (this.modalities.includes('audio')) {
            const systemMessageCount = validMessages.filter(m => m.role === 'system').length;
            validMessages = validMessages.filter(m => m.role !== 'system');
            if (systemMessageCount > 0) {
                this.logger.info(`[AliyunBailianChatModel] Filtered out ${systemMessageCount} system message(s) due to audio modality (per Qwen-Omni docs)`);
            }
        }

        // CRITICAL: Ensure we always have at least one message
        if (validMessages.length === 0) {
            this.logger.warn('No valid messages found, adding default user message');
            validMessages = [{ role: 'user', content: 'Hello' }];
        }

        // Add system message if prompts are enabled and we have system prompt
        // IMPORTANT: Per Qwen-Omni documentation, system messages are ignored when audio modality is enabled
        // Only add system message if audio modality is NOT enabled
        if (this.enablePrompts && systemPrompt && !validMessages.some(m => m.role === 'system') && !this.modalities.includes('audio')) {
            validMessages.unshift({ role: 'system', content: systemPrompt });
            this.logger.debug('[AliyunBailianChatModel] Added system message (audio modality not enabled)');
        } else if (this.enablePrompts && systemPrompt && this.modalities.includes('audio')) {
            this.logger.info('[AliyunBailianChatModel] Skipping system message due to audio modality (per Qwen-Omni docs)');
        }

        this.logger.debug(`[AliyunBailianChatModel] Prepared ${validMessages.length} valid messages for API`);

        // Use OpenAI SDK directly for better compatibility
        if (!this.openai) {
            await this._initializeOpenAI();
        }

        if (!this.openai) {
            throw new Error('Failed to initialize OpenAI SDK');
        }

        try {
            // Prepare tools for API: convert to OpenAI-compatible format, strip func
            let apiTools = undefined;
            if (options.tools && Array.isArray(options.tools) && options.tools.length > 0) {
                apiTools = options.tools.map(tool => ({
                    type: 'function',
                    function: {
                        name: tool.name,
                        description: tool.description || '',
                        parameters: tool.schema || {}
                    }
                }));
            } else if (this.boundTools && this.boundTools.length > 0) {
                apiTools = this.boundTools.map(tool => ({
                    type: 'function',
                    function: {
                        name: tool.name,
                        description: tool.description || '',
                        parameters: tool.schema || {}
                    }
                }));
            }

            const requestParams = {
                model: this.model,
                messages: validMessages,
                temperature: options.temperature || 0.7,
                max_tokens: options.max_tokens || 2000,
                stream: true,
                stream_options: { include_usage: true },
                ...(this.modalities.includes('audio') && {
                    modalities: ['text', 'audio'],
                    audio: {
                        voice: this.audioConfig.voice || ALIYUN_AUDIO_CONFIG.defaultVoice,
                        format: this.audioConfig.format || 'wav'
                    }
                }),
                ...(apiTools && apiTools.length > 0 && {
                    tools: apiTools,
                    tool_choice: options.tool_choice || 'auto'
                })
            };

            // Log tool information for debugging
            if (requestParams.tools && requestParams.tools.length > 0) {
                this.logger.debug(`[AliyunBailianChatModel] Including ${requestParams.tools.length} tools:`,
                    requestParams.tools.map(t => t.function?.name || t.name || 'unnamed'));
            }

            this.logger.debug(`[AliyunBailianChatModel] Making request with ${requestParams.messages.length} messages`);
            this.logger.debug(`[AliyunBailianChatModel] Request params:`, JSON.stringify(requestParams, null, 2));

            // 🧪 DEBUG: Network request debugging
            this.logger.debug('[🔍 DEBUG] About to make HTTP request to OpenAI/Aliyun API:', {
                hasOpenAIClient: !!this.openai,
                requestParamsKeys: Object.keys(requestParams),
                messagesCount: requestParams.messages.length,
                hasTools: !!(requestParams.tools && requestParams.tools.length > 0),
                hasModalities: !!requestParams.modalities,
                hasAudio: !!requestParams.audio,
                timestamp: new Date().toISOString()
            });

            this.logger.debug('[🔍 DEBUG] Calling this.openai.chat.completions.create() NOW...');
            const completion = await this.openai.chat.completions.create(requestParams);
            this.logger.debug(`[🔍 DEBUG] this.openai.chat.completions.create() returned:`, {
                hasCompletion: !!completion,
                completionType: typeof completion,
                isAsyncIterable: completion && typeof completion[Symbol.asyncIterator] === 'function',
                timestamp: new Date().toISOString()
            });
            this.logger.debug(`[AliyunBailianChatModel] openai.chat.completions.create called`);

            let content = '';
            let audioBase64 = '';
            let usage = null;
            let chunkCount = 0;

            for await (const chunk of completion) {
                chunkCount++;
                this.logger.debug(`[AliyunBailianChatModel] Processing chunk ${chunkCount}:`, JSON.stringify(chunk, null, 2));

                if (chunk.error) {
                    throw new Error('Aliyun API error: ' + JSON.stringify(chunk.error));
                }

                if (Array.isArray(chunk.choices) && chunk.choices.length > 0) {
                    const choice = chunk.choices[0];
                    this.logger.debug(`[AliyunBailianChatModel] Choice delta:`, JSON.stringify(choice.delta, null, 2));

                    // Collect text content
                    if (choice.delta?.content) {
                        content += choice.delta.content;
                        this.logger.debug(`[AliyunBailianChatModel] Added content: "${choice.delta.content}", total length: ${content.length}`);
                    }

                    // Collect audio data
                    if (choice.delta?.audio?.data) {
                        audioBase64 += choice.delta.audio.data;
                        this.logger.debug(`[AliyunBailianChatModel] Added audio data: ${choice.delta.audio.data.length} chars, total: ${audioBase64.length}`);
                        this.logger.debug(`[AliyunBailianChatModel] Audio chunk preview: ${choice.delta.audio.data.slice(0, 100)}`);
                    }
                }

                // Collect usage information
                if (chunk.usage) {
                    usage = chunk.usage;
                    this.logger.debug(`[AliyunBailianChatModel] Usage info:`, JSON.stringify(usage, null, 2));
                }
            }

            this.logger.info(`[AliyunBailianChatModel] Final response: ${content.length} chars text, ${audioBase64.length} chars audio, ${chunkCount} chunks processed`);
            this.logger.debug(`[AliyunBailianChatModel] Final content: "${content}"`);
            this.logger.debug(`[AliyunBailianChatModel] Final audioBase64 preview: ${audioBase64.slice(0, 100)}`);
            this.logger.debug(`[AliyunBailianChatModel] Request params sent to API:`, JSON.stringify(requestParams, null, 2));

            // Handle qwen-omni-turbo behavior: when audio is requested, it often returns only audio without text
            // This is the expected behavior for audio-focused responses
            if (!content && audioBase64 && this.modalities.includes('audio')) {
                this.logger.info('[AliyunBailianChatModel] Audio response generated without text content - this is expected behavior for qwen-omni-turbo');
                // Get the last user message to create a contextual response
                const lastUserMessage = validMessages.filter(msg => msg.role === 'user').pop();
                const userInput = lastUserMessage?.content || '';

                // Create a meaningful response based on the context
                if (userInput.toLowerCase().includes('hello') || userInput.toLowerCase().includes('nihao')) {
                    content = 'Hello! I have generated an audio response for you.';
                } else if (userInput.toLowerCase().includes('say') || userInput.toLowerCase().includes('speak')) {
                    content = 'I have generated the audio response as requested.';
                } else {
                    const truncatedInput = userInput.length > 50 ? userInput.substring(0, 50) + '...' : userInput;
                    content = `I have processed your message and generated an audio response. The audio contains my reply to: "${truncatedInput}"`;
                }
            } else if (!audioBase64 && this.modalities.includes('audio')) {
                this.logger.warn('[AliyunBailianChatModel] No audio output received despite audio modality being requested');
            }

            const result = {
                generations: [{
                    text: content,
                    audio: audioBase64,
                    message: new AIMessage({ content }),
                    usage
                }]
            };

            // After receiving a generation with an audio field, automatically call play_audio tool if present
            const gen = result?.generations?.[0];
            if (gen && gen.audio && (this.boundTools || []).some(t => t.name === 'play_audio')) {
                const playAudio = (this.boundTools || []).find(t => t.name === 'play_audio');
                if (playAudio && typeof playAudio.func === 'function') {
                    playAudio.func({ audio: gen.audio, format: (this.audioConfig && this.audioConfig.format) || 'wav' });
                }
            }

            // After receiving a generation, handle tool calls and audio
            this.handleToolCalls(gen);
            this.logger.debug(`[AliyunBailianChatModel] Returning result:`, result);
            return result;

        } catch (error) {
            this.logger.error('[AliyunBailianChatModel] HTTP API error:', error);
            throw error;
        }
    }

    // --- Internal WebSocket logic ---
    async _invokeWebSocket(messages) {
        await this.initializeRealtimeMode();

        // DON'T send session.update here - it's now handled in session.created event
        // Wait for session.updated to ensure proper configuration
        await new Promise((resolve) => {
            const onSessionUpdated = (event) => {
                const msg = JSON.parse(event.data);
                if (msg.type === 'session.updated') {
                    this.realtimeSocket.removeEventListener('message', onSessionUpdated);
                    resolve();
                }
            };
            this.realtimeSocket.addEventListener('message', onSessionUpdated);
        });
        // Prepare multi-modal content for user message
        const last = messages[messages.length - 1];
        let multimodal = await this.mediaHandler.processMultimodal({ text: last.content, ...last.extraModalities });
        let contentArr = [];
        if (multimodal.text) contentArr.push({ type: 'input_text', text: multimodal.text });
        if (multimodal.audio) contentArr.push({ type: 'input_audio', input_audio: multimodal.audio });
        if (multimodal.images) {
            for (let img of multimodal.images) {
                contentArr.push({ type: 'image_url', image_url: img });
            }
        }
        if (multimodal.video) contentArr.push({ type: 'video_url', video_url: multimodal.video });
        const textMsg = {
            event_id: `event_${Date.now()}`,
            type: 'conversation.item.create',
            item: { type: 'message', role: 'user', content: contentArr }
        };
        this.realtimeSocket.send(JSON.stringify(textMsg));
        return new Promise((resolve, reject) => {
            let responseText = '';
            let audioBase64 = '';
            this.realtimeSocket.onmessage = (event) => {
                const msg = JSON.parse(event.data);
                if (msg.type === 'response.content_part.added' && msg.part?.type === 'text') {
                    responseText += msg.part.text;
                }
                if (msg.type === 'response.content_part.added' && msg.part?.type === 'audio') {
                    audioBase64 += msg.part.audio;
                }
                if (msg.type === 'response.done') {
                    resolve({ generations: [{ text: responseText, audio: audioBase64, message: new AIMessage({ content: responseText }) }] });
                }
                if (msg.type === 'error') reject(new Error(msg.error?.message || 'WebSocket error'));
            };
        });
    }

    /**
     * Handle binary audio messages (PCM data blobs)
     */
    _handleBinaryAudioMessage(audioBlob, callbacks = {}) {
        this.logger.debug('[AliyunBailianChatModel] Processing binary audio blob:', {
            size: audioBlob.size,
            type: audioBlob.type
        });

        // Convert blob to ArrayBuffer for audio processing
        audioBlob.arrayBuffer().then(async arrayBuffer => {
            try {
                // Use enhanced audio processing from audio.ts
                const {
                    base64ToBlobEnhanced,
                    detectAudioFormatFromBase64,
                    playBase64Audio
                } = await import('../../media/modality/audio.ts');

                // Convert ArrayBuffer to base64 for format detection
                const uint8Array = new Uint8Array(arrayBuffer);
                const base64Audio = btoa(String.fromCharCode.apply(null, Array.from(uint8Array)));

                // Detect audio format
                const formatInfo = detectAudioFormatFromBase64(base64Audio);
                this.logger.debug('[AliyunBailianChatModel] Audio format detected:', formatInfo);

                if (formatInfo.isComplete && formatInfo.format === 'complete_wav') {
                    // Complete WAV file - can play directly
                    this.logger.info('🔊 Playing complete WAV audio from server');
                    await playBase64Audio(base64Audio, 'wav', { logger: this.logger });
                } else if (formatInfo.format === 'raw_pcm') {
                    // Raw PCM data - convert to WAV and play
                    this.logger.info('🔊 Converting and playing raw PCM audio from server');
                    const audioBlob = base64ToBlobEnhanced(base64Audio, 'audio/wav');
                    const base64Wav = await new Promise((resolve) => {
                        const reader = new FileReader();
                        reader.onload = () => {
                            const result = reader.result.split(',')[1]; // Remove data:audio/wav;base64, prefix
                            resolve(result);
                        };
                        reader.readAsDataURL(audioBlob);
                    });
                    await playBase64Audio(base64Wav, 'wav', { logger: this.logger });
                } else {
                    this.logger.warn('[AliyunBailianChatModel] Unknown audio format, attempting fallback playback');
                    await playBase64Audio(base64Audio, 'wav', { logger: this.logger });
                }

                // Call callback if provided
                if (typeof callbacks.onAudioReceived === 'function') {
                    callbacks.onAudioReceived(arrayBuffer);
                }

            } catch (error) {
                this.logger.error('[AliyunBailianChatModel] Failed to process and play binary audio:', error);

                // Fallback: create simple base64 and call callback
                if (typeof callbacks.onAudioReceived === 'function') {
                    callbacks.onAudioReceived(arrayBuffer);
                }
            }
        }).catch(error => {
            this.logger.error('[AliyunBailianChatModel] Failed to process binary audio blob:', error);
        });
    }

    /**
     * Handle incoming WebSocket message from Aliyun service
     */
    _handleRealtimeMessage(event, callbacks = {}) {
        this.logger.debug('[AliyunBailianChatModel] _handleRealtimeMessage event:', event);

        let message;

        // Handle binary (audio) messages differently from text (JSON) messages
        if (event.data instanceof Blob) {
            this.logger.debug('[AliyunBailianChatModel] Received Blob in WebSocket message, reading as text...');

            // Read the blob as text to handle JSON-encoded messages
            const reader = new FileReader();
            reader.onload = () => {
                const text = reader.result;
                this.logger.debug('[AliyunBailianChatModel] Blob read as text:', text);

                try {
                    // Try to parse as JSON first (many Aliyun messages come as Blob but contain JSON)
                    message = JSON.parse(text);
                    this.logger.debug('[AliyunBailianChatModel] Processing WebSocket message:', {
                        type: message.type,
                        messageKeys: Object.keys(message)
                    });
                    this._processWebSocketMessage(message, callbacks);
                } catch (e) {
                    // If not JSON, handle as binary audio data
                    this._handleBinaryAudioMessage(event.data, callbacks);
                }
            };
            reader.readAsText(event.data);
            return;
        } else if (typeof event.data === 'string') {
            try {
                message = JSON.parse(event.data);
                this._processWebSocketMessage(message, callbacks);
            } catch (e) {
                this.logger.error('Error parsing WebSocket message:', e);
            }
        }
    }

    /**
     * Process parsed WebSocket message
     */
    _processWebSocketMessage(msg, callbacks = {}) {
        // Enhanced debug logging with timestamp to track message flow
        this.logger.debug('🔍 [MessageDebug] WebSocket event received:', {
            type: msg.type,
            hasEventId: !!msg.event_id,
            timestamp: new Date().toISOString()
        });

        // Process message based on type
        switch (msg.type) {
            case 'session.created':
                this.logger.info('✅ [SessionDebug] Realtime session created:', {
                    sessionId: msg.session?.id,
                    model: msg.session?.model,
                    inputAudioFormat: msg.session?.input_audio_format,
                    outputAudioFormat: msg.session?.output_audio_format,
                    turnDetection: msg.session?.turn_detection,
                    voice: msg.session?.voice
                });

                // Store session data for diagnostic purposes
                this._lastSessionCreated = msg.session;

                // Notify client code about session creation
                if (typeof callbacks.onSessionCreated === 'function') {
                    callbacks.onSessionCreated(msg);
                }

                // Session configuration is now handled automatically by AliyunRealtimeClient
                // The experimental approach of skipping manual session.update prevents 1011 errors

                // Session stabilization logic to prevent 1011 errors
                this._sessionStabilized = false;
                setTimeout(() => {
                    this.logger.debug('✅ [SessionDebug] Session ready for audio after initialization delay');
                    this._sessionStabilized = true;

                    // Also notify RealtimeAudioManager about session readiness
                    if (this.realtimeAudioManager) {
                        this.realtimeAudioManager.resetSession();
                    }

                    if (typeof callbacks.onSessionReady === 'function') {
                        callbacks.onSessionReady(msg);
                    }
                }, 1000); // Reduced to 1 second to match working examples
                break;

            case 'session.updated':
                this.logger.info('✅ [SessionDebug] Session configuration updated successfully:', {
                    sessionId: msg.session?.id,
                    turnDetection: msg.session?.turn_detection,
                    voice: msg.session?.voice,
                    timestamp: new Date().toISOString()
                });

                // Mark session as ready for audio transmission
                this._sessionStabilized = true;
                this.logger.debug('📤 [SessionDebug] Session marked as stabilized and ready for audio');

                // Notify RealtimeAudioManager that session is ready
                if (this.realtimeAudioManager) {
                    this.realtimeAudioManager.resetSession();
                    this.logger.debug('📤 [SessionDebug] RealtimeAudioManager session reset');
                }

                // Notify client code about session update
                if (typeof callbacks.onSessionUpdated === 'function') {
                    callbacks.onSessionUpdated(msg);
                    this.logger.debug('📤 [SessionDebug] onSessionUpdated callback called');
                }
                break;

            case 'input_audio_buffer.speech_started':
                this.logger.debug('🎙️ [VADEvent] Speech detected by server VAD');

                if (typeof callbacks.onVoiceActivityDetected === 'function') {
                    callbacks.onVoiceActivityDetected({
                        type: 'speech_start',
                        timestamp: Date.now()
                    });
                }
                break;

            case 'input_audio_buffer.speech_stopped':
                this.logger.debug('🎙️ [VADEvent] Speech ended (silence detected by server VAD)');

                if (typeof callbacks.onVoiceActivityStopped === 'function') {
                    callbacks.onVoiceActivityStopped({
                        type: 'speech_end',
                        timestamp: Date.now()
                    });
                }
                break;

            case 'input_audio_transcription.transcript':
            case 'conversation.item.input_audio_transcription.completed':
                const transcript = msg.transcript ||
                    (msg.conversation_item?.content?.[0]?.text) ||
                    (msg.item?.content?.[0]?.text) || '';

                if (transcript) {
                    this.logger.debug('📝 [Transcript]', transcript);

                    if (typeof callbacks.onTranscriptReceived === 'function') {
                        callbacks.onTranscriptReceived(transcript);
                    }
                }
                break;

            case 'response.audio_delta':
                this._handleStreamingAudioDelta(msg.delta?.audio || msg.audio || '');
                break;

            case 'error':
                const errorMessage = msg.error?.message || 'Unknown error';
                const errorCode = msg.error?.code || 'UNKNOWN_ERROR';

                this.logger.error(`❌ [Aliyun Error] ${errorCode}: ${errorMessage}`);

                if (typeof callbacks.onError === 'function') {
                    callbacks.onError(new Error(`Aliyun API Error (${errorCode}): ${errorMessage}`));
                }
                break;

            default:
                // Just log unknown messages for debugging
                this.logger.debug(`📝 [Unknown Message] Type: ${msg.type}`);
                break;
        }
    }


    /**
     * Handle streaming audio delta for real-time playback
     * Uses the StreamingAudioProcessor from audio.ts for Qwen-Omni 方式2 pattern
     */
    async _handleStreamingAudioDelta(audioDelta) {
        try {
            // Initialize streaming processor if not already done
            if (!this._streamingProcessor) {
                const { createStreamingAudioProcessor } = await import('../../media/modality/audio.ts');
                this._streamingProcessor = createStreamingAudioProcessor({
                    sampleRate: ALIYUN_AUDIO_CONFIG.sampleRate,
                    channels: ALIYUN_AUDIO_CONFIG.numChannels,
                    bitDepth: ALIYUN_AUDIO_CONFIG.bitDepth,
                    logger: this.logger
                });
                this.logger.debug('🔊 Streaming audio processor initialized for real-time playback');
            }

            // Process the audio delta chunk
            await this._streamingProcessor.processStreamingChunk(audioDelta);

        } catch (error) {
            this.logger.error('Error handling streaming audio delta:', error);
        }
    }

    /**
     * Clean up streaming resources
     */
    _cleanupStreamingAudio() {
        if (this._streamingProcessor) {
            this._streamingProcessor.endStream();
            this._streamingProcessor = null;
            this.logger.debug('🔊 Streaming audio processor cleaned up');
        }
    }

    /**
     * Execute tool calls from a generation (LangGraph/LC v0.3 pattern)
     * @param {object} generation - LLM generation with tool_calls and/or audio
     * @returns {Array} Array of tool call results
     */
    handleToolCalls(generation) {
        const results = [];
        if (!generation) return results;
        // Handle explicit tool_calls
        if (Array.isArray(generation.tool_calls)) {
            for (const call of generation.tool_calls) {
                const tool = (this.boundTools || []).find(t => t.name === call.name);
                if (tool && typeof tool.func === 'function') {
                    results.push(tool.func(call.args));
                }
            }
        }
        // Handle implicit audio playback
        if (generation.audio && (this.boundTools || []).some(t => t.name === 'play_audio')) {
            const playAudio = (this.boundTools || []).find(t => t.name === 'play_audio');
            if (playAudio && typeof playAudio.func === 'function') {
                results.push(playAudio.func({ audio: generation.audio, format: (this.audioConfig && this.audioConfig.format) || 'wav' }));
            }
        }
        return results;
    }
}

export default AliyunBailianChatModel;