/**
 * Universal Realtime Streaming Client
 * Base class for implementing client-to-server realtime audio/video streaming
 * for any provider (Aliyun, OpenAI, Azure, etc.)
 */

import { createLogger, LogLevel } from '@/utils/logger';

/**
 * Connection states for any realtime streaming provider
 */
export const StreamingState = {
    DISCONNECTED: 'disconnected',
    CONNECTING: 'connecting', 
    CONNECTED: 'connected',
    AUTHENTICATED: 'authenticated',
    SESSION_READY: 'session_ready',
    STREAMING: 'streaming',
    ERROR: 'error',
    CLOSING: 'closing'
};

/**
 * Standard close codes across providers
 */
export const StreamingCloseCodes = {
    1000: 'Normal closure',
    1006: 'Abnormal closure (connection lost)',
    1011: 'Server internal error',
    4001: 'Authentication failed', 
    4008: 'Rate limit exceeded',
    4009: 'Audio format error'
};

/**
 * Base configuration interface for all providers
 */
export const createBaseConfig = () => ({
    // Authentication
    apiKey: '',
    
    // Connection
    endpoint: '',
    model: '',
    
    // Audio configuration
    audioConfig: {
        sampleRate: 16000,
        bitDepth: 16,
        channels: 1,
        format: 'pcm16'
    },
    
    // Rate limiting
    rateLimiting: {
        enabled: true,
        maxChunksPerSecond: 5,
        minIntervalMs: 200
    },
    
    // Connection management
    connectionTimeout: 10000,
    maxReconnectAttempts: 3,
    reconnectBackoffMs: 1000,
    
    // Debugging
    enableDebugLogging: false
});

/**
 * Universal RealtimeStreamingClient
 * Abstract base class for implementing realtime streaming with any provider
 */
export class RealtimeStreamingClient {
    constructor(config = {}) {
        this.config = { ...createBaseConfig(), ...config };
        this.logger = createLogger('RealtimeStreamingClient', 
            this.config.enableDebugLogging ? LogLevel.DEBUG : LogLevel.INFO);
        
        // Connection state
        this.socket = null;
        this.state = StreamingState.DISCONNECTED;
        this.connectionStrategy = this._determineConnectionStrategy();
        
        // Session management
        this.sessionId = null;
        this.sessionStabilized = false;
        
        // Rate limiting
        this.lastAudioSendTime = 0;
        this.audioQueue = [];
        this.isProcessingQueue = false;
        
        // Connection management
        this.reconnectAttempts = 0;
        this.connectionTimeout = null;
        
        // Event handlers
        this.eventHandlers = {
            stateChange: [],
            message: [],
            sessionReady: [],
            audioResponse: [],
            error: [],
            close: []
        };
        
        // Statistics
        this.stats = {
            connectionsAttempted: 0,
            connectionsSuccessful: 0,
            messagesReceived: 0,
            messagesSent: 0,
            audioChunksSent: 0,
            reconnections: 0,
            lastConnectTime: null,
            uptime: 0
        };
        
        this.logger.debug('RealtimeStreamingClient initialized', {
            provider: this.config.provider || 'unknown',
            strategy: this.connectionStrategy,
            endpoint: this.config.endpoint
        });
    }
    
    /**
     * Abstract method: Must be implemented by subclasses
     * Build WebSocket URL with provider-specific parameters
     */
    buildWebSocketUrl() {
        throw new Error('buildWebSocketUrl() must be implemented by subclass');
    }
    
    /**
     * Abstract method: Must be implemented by subclasses
     * Get WebSocket connection options (headers, protocols, etc.)
     */
    getConnectionOptions() {
        throw new Error('getConnectionOptions() must be implemented by subclass');
    }
    
    /**
     * Abstract method: Must be implemented by subclasses
     * Handle provider-specific message processing
     */
    processProviderMessage(message) {
        throw new Error('processProviderMessage() must be implemented by subclass');
    }
    
    /**
     * Abstract method: Must be implemented by subclasses
     * Initialize session with provider-specific configuration
     */
    async initializeSession(sessionConfig = {}) {
        throw new Error('initializeSession() must be implemented by subclass');
    }
    
    /**
     * Establish WebSocket connection using client-to-server strategy
     */
    async connect() {
        if (this.state === StreamingState.CONNECTED || this.state === StreamingState.CONNECTING) {
            this.logger.warn('Connection already established or in progress');
            return true;
        }
        
        this._setState(StreamingState.CONNECTING);
        this.stats.connectionsAttempted++;
        
        try {
            const WSClass = await this._getWebSocketClass();
            const wsUrl = this.buildWebSocketUrl();
            const options = this.getConnectionOptions();
            
            this.logger.info(`🔌 Connecting directly to ${this.config.provider || 'provider'}:`, 
                wsUrl.replace(this.config.apiKey || '', '[REDACTED]'));
            
            // Create WebSocket with provider-specific options
            if (this.connectionStrategy === 'browser') {
                // Browser: May need proxy for CORS or use provider SDK
                this.socket = await this._createBrowserConnection(WSClass, wsUrl, options);
            } else {
                // Node.js: Direct connection with headers
                this.socket = new WSClass(wsUrl, options);
            }
            
            // Set connection timeout
            this.connectionTimeout = setTimeout(() => {
                if (this.state === StreamingState.CONNECTING) {
                    this._handleConnectionTimeout();
                }
            }, this.config.connectionTimeout);
            
            // Setup WebSocket handlers
            this._setupWebSocketHandlers();
            
            return new Promise((resolve, reject) => {
                this._connectionPromise = { resolve, reject };
            });
            
        } catch (error) {
            this.logger.error('❌ Connection attempt failed:', error);
            this._setState(StreamingState.ERROR);
            return false;
        }
    }
    
    /**
     * Send audio data with rate limiting
     */
    async sendAudio(audioData, format = 'base64') {
        if (!this.isSessionReady()) {
            this.logger.warn('❌ Cannot send audio: session not ready');
            return false;
        }
        
        // Add to queue if rate limiting is enabled
        if (this.config.rateLimiting.enabled) {
            this.audioQueue.push({ audioData, format, timestamp: Date.now() });
            this._processAudioQueue();
            return true;
        }
        
        // Send immediately if rate limiting disabled
        return this._sendAudioImmediate(audioData, format);
    }
    
    /**
     * Check if session is ready for streaming
     */
    isSessionReady() {
        return this.state === StreamingState.SESSION_READY && 
               this.sessionStabilized && 
               this.socket && 
               this.socket.readyState === 1;
    }
    
    /**
     * Check if connection is active
     */
    isConnected() {
        return this.socket && this.socket.readyState === 1;
    }
    
    /**
     * Gracefully disconnect
     */
    async disconnect(code = 1000, reason = 'Client disconnect') {
        if (this.state === StreamingState.DISCONNECTED || this.state === StreamingState.CLOSING) {
            return;
        }
        
        this.logger.info('🔌 Disconnecting from realtime stream');
        this._setState(StreamingState.CLOSING);
        
        this._clearTimers();
        this._clearAudioQueue();
        
        if (this.socket && this.socket.readyState === 1) {
            try {
                this.socket.close(code, reason);
            } catch (error) {
                this.logger.warn('Error closing WebSocket:', error);
            }
        }
        
        this._cleanup();
        this._setState(StreamingState.DISCONNECTED);
    }
    
    /**
     * Add event listener
     */
    on(event, handler) {
        if (this.eventHandlers[event]) {
            this.eventHandlers[event].push(handler);
        }
    }
    
    /**
     * Remove event listener  
     */
    off(event, handler) {
        if (this.eventHandlers[event]) {
            const index = this.eventHandlers[event].indexOf(handler);
            if (index > -1) {
                this.eventHandlers[event].splice(index, 1);
            }
        }
    }
    
    /**
     * Get connection statistics
     */
    getStats() {
        return {
            ...this.stats,
            currentState: this.state,
            sessionId: this.sessionId,
            connectionStrategy: this.connectionStrategy,
            queueLength: this.audioQueue.length,
            successRate: this.stats.connectionsAttempted > 0
                ? (this.stats.connectionsSuccessful / this.stats.connectionsAttempted) * 100
                : 0
        };
    }
    
    // Private methods
    
    /**
     * Determine connection strategy based on environment
     */
    _determineConnectionStrategy() {
        const isBrowser = typeof window !== 'undefined' && window.WebSocket;
        
        // Check if provider supports direct browser connections
        if (isBrowser && this.config.supportsBrowserDirectConnection) {
            return 'browser-direct';
        } else if (isBrowser) {
            return 'browser'; // May need proxy
        } else {
            return 'node'; // Direct connection
        }
    }
    
    /**
     * Get WebSocket class for environment
     */
    async _getWebSocketClass() {
        if (typeof window !== 'undefined' && window.WebSocket) {
            return window.WebSocket;
        }
        
        try {
            const { default: WebSocket } = await import('ws');
            return WebSocket;
        } catch (error) {
            throw new Error('WebSocket not available. Install ws package for Node.js environments.');
        }
    }
    
    /**
     * Create browser connection (may use proxy or provider SDK)
     */
    async _createBrowserConnection(WSClass, wsUrl, options) {
        if (this.config.supportsBrowserDirectConnection) {
            // Direct connection supported
            return new WSClass(wsUrl);
        } else {
            // Use proxy or provider SDK
            return this._createProxyConnection(WSClass, wsUrl, options);
        }
    }
    
    /**
     * Create proxy connection for browser CORS limitations
     */
    async _createProxyConnection(WSClass, wsUrl, options) {
        // Default proxy implementation - subclasses can override
        const proxyUrl = this._buildProxyUrl(wsUrl);
        return new WSClass(proxyUrl);
    }
    
    /**
     * Build proxy URL (default implementation)
     */
    _buildProxyUrl(originalUrl) {
        // Default proxy logic - subclasses should override
        const url = new URL(originalUrl);
        return `ws://localhost:2994/ws?${url.searchParams.toString()}`;
    }
    
    /**
     * Setup WebSocket event handlers
     */
    _setupWebSocketHandlers() {
        this.socket.onopen = (event) => this._handleConnectionOpen(event);
        this.socket.onmessage = (event) => this._handleMessage(event);
        this.socket.onerror = (error) => this._handleError(error);
        this.socket.onclose = (event) => this._handleClose(event);
    }
    
    /**
     * Handle connection open
     */
    _handleConnectionOpen(event) {
        this._clearTimers();
        this._setState(StreamingState.CONNECTED);
        this.stats.connectionsSuccessful++;
        this.stats.lastConnectTime = Date.now();
        this.reconnectAttempts = 0;
        
        this.logger.info('✅ WebSocket connection established');
        
        if (this._connectionPromise) {
            this._connectionPromise.resolve(true);
            this._connectionPromise = null;
        }
        
        // For some providers (like Aliyun), wait for server to initiate session
        // For others (like OpenAI), we need to initialize session ourselves
        if (this.provider !== 'Aliyun') {
            this.initializeSession().catch(error => {
                this.logger.error('❌ Session initialization failed:', error);
            });
        } else {
            this.logger.debug('🔌 [AliyunFix] Waiting for server to send session.created (prevents 1011 errors)');
        }
    }
    
    /**
     * Handle incoming messages
     */
    _handleMessage(event) {
        this.stats.messagesReceived++;
        
        try {
            // Handle both text and binary messages
            let messageData;
            if (event.data instanceof Blob) {
                event.data.text().then(text => {
                    messageData = JSON.parse(text);
                    this._processMessage(messageData, event);
                });
                return;
            } else if (typeof event.data === 'string') {
                messageData = JSON.parse(event.data);
            } else {
                messageData = event.data;
            }
            
            this._processMessage(messageData, event);
            
        } catch (error) {
            this.logger.error('❌ Error processing message:', error);
        }
    }
    
    /**
     * Process parsed message
     */
    _processMessage(messageData, originalEvent) {
        // Log message for debugging
        this.logger.debug('📥 Message received:', {
            type: messageData.type || 'unknown',
            eventId: messageData.event_id || 'none'
        });
        
        // Let subclass handle provider-specific processing
        const result = this.processProviderMessage(messageData);
        
        // Emit to event handlers
        this.eventHandlers.message.forEach(handler => {
            try {
                handler(messageData, originalEvent);
            } catch (error) {
                this.logger.error('Error in message handler:', error);
            }
        });
        
        return result;
    }
    
    /**
     * Handle WebSocket errors
     */
    _handleError(error) {
        this.logger.error('❌ WebSocket error:', error);
        
        this.eventHandlers.error.forEach(handler => {
            try {
                handler(error);
            } catch (handlerError) {
                this.logger.error('Error in error handler:', handlerError);
            }
        });
        
        if (this._connectionPromise) {
            this._connectionPromise.reject(error);
            this._connectionPromise = null;
        }
    }
    
    /**
     * Handle connection close
     */
    _handleClose(event) {
        this._clearTimers();
        
        const closeReason = StreamingCloseCodes[event.code] || `Unknown (${event.code})`;
        
        this.logger.warn('🔌 Connection closed:', {
            code: event.code,
            reason: event.reason || closeReason,
            wasClean: event.wasClean
        });
        
        this._cleanup();
        this._setState(StreamingState.DISCONNECTED);
        
        this.eventHandlers.close.forEach(handler => {
            try {
                handler(event);
            } catch (error) {
                this.logger.error('Error in close handler:', error);
            }
        });
        
        // Auto-reconnect logic
        if (this._shouldAutoReconnect(event.code)) {
            this._attemptReconnect();
        }
    }
    
    /**
     * Handle connection timeout
     */
    _handleConnectionTimeout() {
        this.logger.error('❌ Connection timeout');
        this._setState(StreamingState.ERROR);
        
        if (this.socket) {
            this.socket.close();
        }
        
        if (this._connectionPromise) {
            this._connectionPromise.reject(new Error('Connection timeout'));
            this._connectionPromise = null;
        }
    }
    
    /**
     * Process audio queue with rate limiting
     */
    async _processAudioQueue() {
        if (this.isProcessingQueue || this.audioQueue.length === 0) {
            return;
        }
        
        this.isProcessingQueue = true;
        
        while (this.audioQueue.length > 0) {
            const now = Date.now();
            const timeSinceLastSend = now - this.lastAudioSendTime;
            
            if (timeSinceLastSend < this.config.rateLimiting.minIntervalMs) {
                const waitTime = this.config.rateLimiting.minIntervalMs - timeSinceLastSend;
                await new Promise(resolve => setTimeout(resolve, waitTime));
            }
            
            const audioItem = this.audioQueue.shift();
            if (audioItem) {
                await this._sendAudioImmediate(audioItem.audioData, audioItem.format);
                this.lastAudioSendTime = Date.now();
            }
        }
        
        this.isProcessingQueue = false;
    }
    
    /**
     * Send audio immediately (bypassing queue)
     */
    async _sendAudioImmediate(audioData, format) {
        if (!this.isConnected()) {
            this.logger.warn('❌ Cannot send audio: not connected');
            return false;
        }
        
        try {
            // Subclasses should override this method
            const success = await this._sendAudioToProvider(audioData, format);
            if (success) {
                this.stats.audioChunksSent++;
            }
            return success;
        } catch (error) {
            this.logger.error('❌ Error sending audio:', error);
            return false;
        }
    }
    
    /**
     * Abstract method: Send audio to provider
     */
    async _sendAudioToProvider(audioData, format) {
        throw new Error('_sendAudioToProvider() must be implemented by subclass');
    }
    
    /**
     * Determine if should auto-reconnect
     */
    _shouldAutoReconnect(code) {
        const noReconnectCodes = [4001, 4003, 4004]; // Auth failures
        return !noReconnectCodes.includes(code) && 
               this.reconnectAttempts < this.config.maxReconnectAttempts;
    }
    
    /**
     * Attempt reconnection with backoff
     */
    async _attemptReconnect() {
        this.reconnectAttempts++;
        this.stats.reconnections++;
        
        const delay = Math.min(
            this.config.reconnectBackoffMs * Math.pow(2, this.reconnectAttempts - 1), 
            10000
        );
        
        this.logger.info(`🔄 Attempting reconnection ${this.reconnectAttempts}/${this.config.maxReconnectAttempts} in ${delay}ms`);
        
        setTimeout(async () => {
            try {
                await this.connect();
            } catch (error) {
                this.logger.error('❌ Reconnection failed:', error);
            }
        }, delay);
    }
    
    /**
     * Clear all timers
     */
    _clearTimers() {
        if (this.connectionTimeout) {
            clearTimeout(this.connectionTimeout);
            this.connectionTimeout = null;
        }
    }
    
    /**
     * Clear audio queue
     */
    _clearAudioQueue() {
        this.audioQueue = [];
        this.isProcessingQueue = false;
    }
    
    /**
     * Cleanup resources
     */
    _cleanup() {
        this._clearTimers();
        this._clearAudioQueue();
        this.socket = null;
        this.sessionId = null;
        this.sessionStabilized = false;
    }
    
    /**
     * Set state and notify handlers
     */
    _setState(newState) {
        const oldState = this.state;
        this.state = newState;
        
        if (oldState !== newState) {
            this.logger.debug(`State transition: ${oldState} → ${newState}`);
            
            this.eventHandlers.stateChange.forEach(handler => {
                try {
                    handler(newState, oldState);
                } catch (error) {
                    this.logger.error('Error in state change handler:', error);
                }
            });
        }
    }
}

export default RealtimeStreamingClient;