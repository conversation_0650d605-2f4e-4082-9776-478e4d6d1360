/**
 * <PERSON><PERSON>-Omni Real-time Models - Direct Connection Architecture
 * Simplified architecture with ConnectionManager for direct Aliyun API connection
 */

// Core Aliyun Components
export { AliyunBailianChatModel } from '../AliyunBailianChatModel.js';
export { ConnectionManager } from './connection/ConnectionManager.js';
export { MediaHandler } from './media/MediaHandler.js';

// Configuration and utilities
export * from './types/index.js';
export * from './config/AliyunConfig.js';

// Import for default export
import { AliyunBailianChatModel } from '../AliyunBailianChatModel.js';
import { ConnectionManager } from './connection/ConnectionManager.js';
import { MediaHandler } from './media/MediaHandler.js';

// Default export for convenience
export default {
    // Core components
    AliyunBailianChatModel,
    ConnectionManager,
    MediaHandler
}; 