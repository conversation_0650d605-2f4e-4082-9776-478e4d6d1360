/**
 * Shared Types and Interfaces for Aliyun Bailian Chat Model Modules
 * Centralizes all type definitions used across the modular architecture
 */

/**
 * WebSocket connection states
 */
export const ConnectionState = {
    DISCONNECTED: 'disconnected',
    CONNECTING: 'connecting',
    CONNECTED: 'connected',
    AUTHENTICATED: 'authenticated',
    READY: 'ready',
    ERROR: 'error',
    CLOSING: 'closing'
};

/**
 * Session states
 */
export const SessionState = {
    NONE: 'none',
    CREATING: 'creating',
    CREATED: 'created',
    UPDATING: 'updating',
    READY: 'ready',
    ERROR: 'error'
};

/**
 * VAD (Voice Activity Detection) states
 */
export const VADState = {
    IDLE: 'idle',
    LISTENING: 'listening',
    SPEECH_DETECTED: 'speech_detected',
    SPEECH_ENDED: 'speech_ended',
    TIMEOUT: 'timeout'
};

/**
 * Aliyun API event types
 */
export const AliyunEventType = {
    // Session events
    SESSION_CREATED: 'session.created',
    SESSION_UPDATED: 'session.updated',

    // Audio buffer events
    INPUT_AUDIO_BUFFER_APPEND: 'input_audio_buffer.append',
    INPUT_AUDIO_BUFFER_COMMIT: 'input_audio_buffer.commit',
    INPUT_AUDIO_BUFFER_COMMITTED: 'input_audio_buffer.committed',
    INPUT_AUDIO_BUFFER_COMMITED: 'input_audio_buffer.commited', // Handle typo in docs
    INPUT_AUDIO_BUFFER_CLEAR: 'input_audio_buffer.clear',
    INPUT_AUDIO_BUFFER_CLEARED: 'input_audio_buffer.cleared',
    INPUT_AUDIO_BUFFER_SPEECH_STARTED: 'input_audio_buffer.speech_started',
    INPUT_AUDIO_BUFFER_SPEECH_STOPPED: 'input_audio_buffer.speech_stopped',

    // Image buffer events
    INPUT_IMAGE_BUFFER_APPEND: 'input_image_buffer.append',

    // Conversation events
    CONVERSATION_ITEM_CREATE: 'conversation.item.create',
    CONVERSATION_ITEM_CREATED: 'conversation.item.created',

    // Response events
    RESPONSE_CREATE: 'response.create',
    RESPONSE_CREATED: 'response.created',
    RESPONSE_DONE: 'response.done',
    RESPONSE_AUDIO_DELTA: 'response.audio.delta',
    RESPONSE_AUDIO_DONE: 'response.audio.done',
    RESPONSE_TEXT_DELTA: 'response.text.delta',
    RESPONSE_TEXT_DONE: 'response.text.done',
    RESPONSE_CONTENT_PART_ADDED: 'response.content_part.added',
    RESPONSE_CONTENT_PART_DONE: 'response.content_part.done',
    RESPONSE_OUTPUT_ITEM_ADDED: 'response.output_item.added',
    RESPONSE_OUTPUT_ITEM_DONE: 'response.output_item.done',

    // Error events
    ERROR: 'error'
};

/**
 * Configuration interface for AliyunBailianChatModel
 */
export const createDefaultConfig = () => ({
    // API Configuration
    apiKey: '',
    model: 'qwen-omni-turbo-realtime',
    realtimeEndpoint: 'wss://dashscope.aliyuncs.com/api-ws/v1/realtime',

    // Model parameters
    temperature: 0.7,
    maxTokens: 2048,

    // Connection options
    useProxy: typeof window !== 'undefined', // Default to proxy in browser
    proxyEndpoint: null, // Auto-determined

    // Session options
    streaming: true,
    enableTranscription: false,
    requireServerVAD: true,
    vadTimeoutMs: 3000,

    // Audio configuration
    audioConfig: {
        sampleRate: 16000,
        channels: 1,
        bitDepth: 16,
        format: 'pcm16'
    },

    // Rate limiting
    maxAudioPerSecond: 8,
    minAudioIntervalMs: 125,
    maxAudioTokensPerSecond: 10,
    maxImagesPerSecond: 2,
    minImageIntervalMs: 500,

    // Callbacks
    onConnectionStateChange: null,
    onSessionStateChange: null,
    onVADStateChange: null,
    onError: null,
    onAudioReceived: null,
    onTranscriptReceived: null,
    onVoiceActivityDetected: null,
    onVoiceActivityStopped: null
});

/**
 * Media processing configuration
 */
export const createMediaConfig = () => ({
    // Audio requirements (Aliyun specific)
    audio: {
        sampleRate: 16000, // 16kHz for input
        channels: 1,       // Mono
        bitDepth: 16,      // PCM16
        encoding: 'base64'
    },

    // Image requirements (Aliyun specific)  
    image: {
        formats: ['jpg', 'jpeg'], // JPG/JPEG only
        maxSizeKB: 500,          // 500KB max
        encoding: 'base64',
        rateLimit: 2             // 2fps
    },

    // Video processing (converted to image frames)
    video: {
        frameRate: 2,            // 2fps as per Aliyun docs
        maxFrames: 30,
        quality: 0.8
    }
});

/**
 * Rate limiting configuration
 */
export const createRateLimitConfig = () => ({
    audio: {
        maxChunksPerSecond: 8,
        minIntervalMs: 125,
        maxTokensPerSecond: 10,
        windowSizeMs: 1000,
        maxQueueSize: 50
    },

    image: {
        maxImagesPerSecond: 2,
        minIntervalMs: 500,
        windowSizeMs: 1000,
        maxQueueSize: 10
    }
});

/**
 * Error recovery configuration
 */
export const createErrorRecoveryConfig = () => ({
    maxRetries: 3,
    retryDelayMs: 2000,
    exponentialBackoff: true,
    maxRetryDelayMs: 10000,

    // Error code specific recovery
    errorRecoveryStrategies: {
        1011: 'session_config_recovery', // Server internal error
        1006: 'connection_retry',        // Abnormal closure
        4001: 'auth_refresh',           // Authentication failed
        4008: 'rate_limit_backoff',     // Rate limit exceeded
        4009: 'audio_format_fix'        // Audio format error
    }
});

/**
 * Session configuration modes
 */
export const SessionMode = {
    SERVER_VAD: 'server_vad',      // Server detects speech automatically
    PUSH_TO_TALK: 'push_to_talk'   // Manual commit required
};

/**
 * Supported voices
 */
export const SupportedVoices = {
    CHELSIE: 'Chelsie',
    SERENA: 'Serena',
    ETHAN: 'Ethan',
    CHERRY: 'Cherry'
};

/**
 * Gender-based voice mapping
 */
export const VoiceGenderMap = {
    female: SupportedVoices.SERENA,
    male: SupportedVoices.ETHAN,
    f: SupportedVoices.SERENA,
    m: SupportedVoices.ETHAN
};

/**
 * WebSocket close codes and their meanings
 */
export const WebSocketCloseCodes = {
    1000: 'Normal closure',
    1006: 'Abnormal closure (connection lost)',
    1011: 'Server internal error',
    4001: 'Authentication failed',
    4008: 'Rate limit exceeded',
    4009: 'Audio format error'
};

/**
 * Utility function to generate event IDs
 */
export const generateEventId = () => `event_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

/**
 * Utility function to create error objects
 */
export const createError = (type, message, code = null, details = {}) => ({
    type,
    message,
    code,
    details,
    timestamp: Date.now()
});

/**
 * Utility function to validate configuration
 */
export const validateConfig = (config) => {
    const errors = [];

    if (!config.apiKey) {
        errors.push('API key is required');
    }

    if (!config.model) {
        errors.push('Model name is required');
    }

    if (!config.realtimeEndpoint) {
        errors.push('Realtime endpoint is required');
    }

    if (config.temperature < 0 || config.temperature > 2) {
        errors.push('Temperature must be between 0 and 2');
    }

    if (config.maxTokens < 1) {
        errors.push('Max tokens must be positive');
    }

    return {
        isValid: errors.length === 0,
        errors
    };
}; 