/**
 * <PERSON><PERSON>-Omni Realtime Streaming Client
 * Implementation of RealtimeStreamingClient for Aliyun DashScope API
 * Supports both browser and Node.js environments with optimized rate limiting
 */

import { RealtimeStreamingClient, StreamingState } from '../../base/RealtimeStreamingClient.js';
import { ALIYUN_AUDIO_CONFIG, ALIYUN_VAD_CONFIG, createEvent, createSessionUpdateEvent, getRealtimeClientConfig } from '../config/AliyunConfig.js';
import { getDownloadServerPort } from '@/utils/portManager.js';

/**
 * Aliyun-specific realtime streaming client
 */
export class AliyunRealtimeClient extends RealtimeStreamingClient {
    constructor(config = {}) {
        // Get centralized Aliyun configuration
        const baseConfig = getRealtimeClientConfig(config);

        const aliyunConfig = {
            provider: 'Aliyun',
            endpoint: 'wss://dashscope.aliyuncs.com/api-ws/v1/realtime',
            supportsBrowserDirectConnection: false, // Browsers cannot set Authorization header in WebSocket handshake

            // Use centralized configurations
            audioConfig: {
                ...baseConfig.audioConfig,
                format: 'pcm16'
            },

            // Rate limiting from centralized config
            rateLimiting: {
                enabled: true,
                maxChunksPerSecond: ALIYUN_AUDIO_CONFIG.maxChunksPerSecond,
                minIntervalMs: ALIYUN_AUDIO_CONFIG.minIntervalMs
            },

            // Aliyun-specific settings from centralized config
            model: baseConfig.model,
            modalities: ['text', 'audio'],
            voice: ALIYUN_AUDIO_CONFIG.defaultVoice,
            vadConfig: baseConfig.vadConfig,

            ...config
        };

        super(aliyunConfig);

        // Aliyun-specific state
        this.sessionConfig = null;
        this.pendingAudioCommit = false;

        this.logger.info('AliyunRealtimeClient initialized', {
            model: this.config.model,
            voice: this.config.voice,
            rateLimiting: this.config.rateLimiting
        });
    }

    /**
     * Build Aliyun WebSocket URL
     */
    buildWebSocketUrl() {
        const params = new URLSearchParams({
            model: this.config.model
        });

        return `${this.config.endpoint}?${params.toString()}`;
    }

    /**
     * Get Aliyun connection options
     */
    getConnectionOptions() {
        if (this.connectionStrategy === 'node') {
            // Direct connection with Authorization header
            return {
                headers: {
                    'Authorization': `Bearer ${this.config.apiKey}`
                }
            };
        }

        // Browser will use proxy
        return {};
    }

    /**
     * Create proxy connection for browser
     */
    async _createProxyConnection(WSClass, wsUrl, options) {
        const serverPort = getDownloadServerPort();
        const proxyUrl = `ws://localhost:${serverPort}/ws?model=${this.config.model}`;

        this.logger.debug('Using Aliyun proxy connection:', proxyUrl);
        return new WSClass(proxyUrl);
    }

    /**
     * Process Aliyun-specific messages
     */
    processProviderMessage(message) {
        const { type, session, event_id } = message;

        this.logger.debug('📥 Aliyun message:', {
            type,
            eventId: event_id,
            hasSession: !!session
        });

        switch (type) {
            case 'session.created':
                return this._handleSessionCreated(message);

            case 'session.updated':
                return this._handleSessionUpdated(message);

            case 'input_audio_buffer.speech_started':
                return this._handleSpeechStarted(message);

            case 'input_audio_buffer.speech_stopped':
                return this._handleSpeechStopped(message);

            case 'input_audio_buffer.committed':
                return this._handleAudioCommitted(message);

            case 'response.created':
                return this._handleResponseCreated(message);

            case 'response.audio.delta':
                return this._handleAudioResponse(message);

            case 'response.text.delta':
                return this._handleTextResponse(message);

            case 'response.done':
                return this._handleResponseDone(message);

            default:
                this.logger.debug('📋 Unhandled message type:', type);
                return true;
        }
    }

    /**
     * Initialize Aliyun session (wait for server session.created)
     */
    async initializeSession(sessionConfig = {}) {
        try {
            // Wait for server to send session.created automatically
            this.logger.debug('🔌 [SessionInit] Waiting for server session.created event...');
            return true;

        } catch (error) {
            this.logger.error('❌ Session initialization failed:', error);
            return false;
        }
    }

    /**
     * Send audio to Aliyun API with optimal base64 encoding
     */
    async _sendAudioToProvider(audioData, format = 'base64') {
        try {
            let base64Audio;

            if (format === 'base64') {
                base64Audio = audioData;
            } else if (format === 'uint8array' || audioData instanceof Uint8Array) {
                base64Audio = this._arrayBufferToBase64(audioData);
            } else if (format === 'arraybuffer' || audioData instanceof ArrayBuffer) {
                base64Audio = this._arrayBufferToBase64(new Uint8Array(audioData));
            } else {
                throw new Error(`Unsupported audio format: ${format}`);
            }

            // Create and send audio append event
            const audioEvent = createEvent('input_audio_buffer.append', {
                audio: base64Audio
            });

            const success = this._sendMessage(audioEvent);

            if (success) {
                this.logger.debug('✅ [AudioDebug] Audio chunk sent successfully', {
                    eventId: audioEvent.event_id,
                    dataSize: base64Audio.length,
                    timestamp: new Date().toISOString()
                });
            }

            return success;

        } catch (error) {
            this.logger.error('❌ [AudioDebug] Error sending audio:', error);
            return false;
        }
    }

    /**
     * Send audio commit to trigger response
     */
    async commitAudio() {
        if (this.pendingAudioCommit) {
            this.logger.debug('⏳ Audio commit already pending');
            return true;
        }

        try {
            this.pendingAudioCommit = true;

            const commitEvent = createEvent('input_audio_buffer.commit');
            const success = this._sendMessage(commitEvent);

            if (success) {
                this.logger.debug('✅ Audio buffer committed', {
                    eventId: commitEvent.event_id
                });
            }

            return success;

        } catch (error) {
            this.logger.error('❌ Error committing audio:', error);
            return false;
        }
    }

    /**
     * Clear audio buffer
     */
    async clearAudio() {
        try {
            const clearEvent = createEvent('input_audio_buffer.clear');
            const success = this._sendMessage(clearEvent);

            if (success) {
                this.logger.debug('✅ Audio buffer cleared', {
                    eventId: clearEvent.event_id
                });
            }

            return success;

        } catch (error) {
            this.logger.error('❌ Error clearing audio:', error);
            return false;
        }
    }

    /**
     * Create response manually
     */
    async createResponse(responseConfig = {}) {
        try {
            const defaultConfig = {
                instructions: "You are a helpful assistant.",
                modalities: this.config.modalities,
                voice: this.config.voice
            };

            const responseEvent = createEvent('response.create', {
                response: { ...defaultConfig, ...responseConfig }
            });

            const success = this._sendMessage(responseEvent);

            if (success) {
                this.logger.debug('✅ Response creation requested', {
                    eventId: responseEvent.event_id
                });
            }

            return success;

        } catch (error) {
            this.logger.error('❌ Error creating response:', error);
            return false;
        }
    }

    // Private message handlers

    _handleSessionCreated(message) {
        const { session } = message;
        this.sessionId = session.id;

        this.logger.info('✅ [SessionDebug] Aliyun session created:', {
            sessionId: this.sessionId,
            model: session.model,
            modalities: session.modalities,
            voice: session.voice,
            inputFormat: session.input_audio_format,
            outputFormat: session.output_audio_format,
            turnDetection: session.turn_detection
        });

        // Store server's default configuration
        this.sessionConfig = session;

        // PYTHON-COMPATIBLE: Send session.update exactly like working Python implementation
        this.logger.debug('📋 [SessionDebug] Sending session.update like Python (this WORKS)...');

        try {
            // Step 1: Create session configuration exactly like Python implementation
            const sessionConfig = {
                "modalities": ["text", "audio"],
                "voice": this.config.voice,
                "input_audio_format": "pcm16",
                "output_audio_format": "pcm16",
                "input_audio_transcription": {
                    "model": "gummy-realtime-v1"
                },
                "turn_detection": {
                    "type": "server_vad",
                    "threshold": 0.8,
                    "prefix_padding_ms": 500,
                    "silence_duration_ms": 1500
                }
            };

            // Step 2: Create session.update event exactly like Python's update_session method
            const sessionUpdateEvent = {
                "type": "session.update",
                "session": sessionConfig
            };

            // Step 3: Add event_id right before sending, exactly like Python's send_event method
            sessionUpdateEvent.event_id = `event_${Math.floor(Date.now())}`;

            // Step 4: Send the event
            this._sendMessage(sessionUpdateEvent);
            this.logger.debug('📤 [SessionDebug] session.update sent - matching Python behavior');

        } catch (error) {
            this.logger.error('❌ Failed to send session.update:', error);
            // Continue anyway - fallback to server default
        }

        // Don't set ready state yet - wait for session.updated response like Python

        return true;
    }

    _handleSessionUpdated(message) {
        const { session } = message;

        this.logger.info('📋 [SessionDebug] Server sent session.updated - Python flow working!', {
            sessionId: session.id,
            turnDetection: session.turn_detection,
            voice: session.voice,
            modalities: session.modalities
        });

        // Store updated configuration
        this.sessionConfig = session;

        // Now set ready state - this is the Python flow that works!
        this.sessionStabilized = true;
        this._setState(StreamingState.SESSION_READY);

        this.logger.info('✅ [SessionDebug] Session ready after session.updated (Python pattern)');

        // Emit session ready event
        this.eventHandlers.sessionReady.forEach(handler => {
            try {
                handler(session);
            } catch (error) {
                this.logger.error('Error in sessionReady handler:', error);
            }
        });

        return true;
    }

    _handleSpeechStarted(message) {
        this.logger.debug('🎙️ Speech started detected by server VAD');
        return true;
    }

    _handleSpeechStopped(message) {
        this.logger.debug('🛑 Speech stopped detected by server VAD');
        return true;
    }

    _handleAudioCommitted(message) {
        this.logger.debug('✅ Audio buffer committed by server');
        this.pendingAudioCommit = false;
        return true;
    }

    _handleResponseCreated(message) {
        this.logger.debug('📝 Response created by server:', {
            responseId: message.response?.id
        });
        return true;
    }

    _handleAudioResponse(message) {
        const { delta } = message;

        if (delta) {
            // Emit audio response to handlers
            this.eventHandlers.audioResponse.forEach(handler => {
                try {
                    handler(delta, message);
                } catch (error) {
                    this.logger.error('Error in audioResponse handler:', error);
                }
            });
        }

        return true;
    }

    _handleTextResponse(message) {
        const { delta } = message;

        this.logger.debug('📝 Text response delta:', {
            text: delta || '[no text]'
        });

        return true;
    }

    _handleResponseDone(message) {
        this.logger.debug('✅ Response completed by server');
        return true;
    }

    // Utility methods

    /**
     * Browser-compatible base64 encoding for audio data
     */
    _arrayBufferToBase64(uint8Array) {
        let binary = '';
        const len = uint8Array.byteLength;
        for (let i = 0; i < len; i++) {
            binary += String.fromCharCode(uint8Array[i]);
        }
        return btoa(binary);
    }

    /**
     * Send message through WebSocket
     */
    _sendMessage(message) {
        if (!this.isConnected()) {
            this.logger.warn('❌ Cannot send message: not connected');
            return false;
        }

        try {
            const messageStr = JSON.stringify(message);
            this.socket.send(messageStr);
            this.stats.messagesSent++;

            this.logger.debug('📤 Message sent:', {
                type: message.type,
                eventId: message.event_id,
                size: messageStr.length
            });

            return true;

        } catch (error) {
            this.logger.error('❌ Failed to send message:', error);
            return false;
        }
    }
}

export default AliyunRealtimeClient;