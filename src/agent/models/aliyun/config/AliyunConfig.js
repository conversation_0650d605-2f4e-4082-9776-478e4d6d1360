/**
 * Centralized configuration for <PERSON><PERSON> services
 * This file contains all configuration parameters for Aliyun services
 * to ensure consistency across the codebase.
 * 
 * IMPORTANT: This is the single source of truth for Aliyun audio configuration.
 * All components (MediaCaptureManager, ConnectionManager, etc.) should use this config.
 */

/**
 * Utility function to generate event IDs
 * Matches Python's implementation: "event_" + str(int(time.time() * 1000))
 * @returns {string} Unique event ID in format event_timestamp
 */
export const generateEventId = () => `event_${Math.floor(Date.now())}`;

/**
 * WebSocket connection states
 */
export const ConnectionState = {
    DISCONNECTED: 'disconnected',
    CONNECTING: 'connecting',
    CONNECTED: 'connected',
    AUTHENTICATED: 'authenticated',
    READY: 'ready',
    ERROR: 'error',
    CLOSING: 'closing'
};

/**
 * Session states
 */
export const SessionState = {
    NONE: 'none',
    CREATING: 'creating',
    CREATED: 'created',
    UPDATING: 'updating',
    READY: 'ready',
    ERROR: 'error'
};

/**
 * VAD (Voice Activity Detection) states
 */
export const VADState = {
    IDLE: 'idle',
    LISTENING: 'listening',
    SPEECH_DETECTED: 'speech_detected',
    SPEECH_ENDED: 'speech_ended',
    TIMEOUT: 'timeout'
};

/**
 * Aliyun API event types
 */
export const AliyunEventType = {
    // Session events
    SESSION_CREATED: 'session.created',
    SESSION_UPDATED: 'session.updated',

    // Audio buffer events
    INPUT_AUDIO_BUFFER_APPEND: 'input_audio_buffer.append',
    INPUT_AUDIO_BUFFER_COMMIT: 'input_audio_buffer.commit',
    INPUT_AUDIO_BUFFER_COMMITTED: 'input_audio_buffer.committed',
    INPUT_AUDIO_BUFFER_CLEAR: 'input_audio_buffer.clear',
    INPUT_AUDIO_BUFFER_CLEARED: 'input_audio_buffer.cleared',
    INPUT_AUDIO_BUFFER_SPEECH_STARTED: 'input_audio_buffer.speech_started',
    INPUT_AUDIO_BUFFER_SPEECH_STOPPED: 'input_audio_buffer.speech_stopped',

    // Image buffer events
    INPUT_IMAGE_BUFFER_APPEND: 'input_image_buffer.append',

    // Response events
    RESPONSE_CREATE: 'response.create',
    RESPONSE_CREATED: 'response.created',
    RESPONSE_DONE: 'response.done',
    RESPONSE_AUDIO_DELTA: 'response.audio.delta',
    RESPONSE_AUDIO_DONE: 'response.audio.done',
    RESPONSE_TEXT_DELTA: 'response.text.delta',
    RESPONSE_TEXT_DONE: 'response.text.done'
};

/**
 * Audio configuration for Aliyun Qwen-Omni API
 * CRITICAL: Based on WORKING Python implementation in debug/vad_mode.py
 * Python uses RATE = 24000 (24kHz) and CHUNK = 3200 - this configuration WORKS
 * Note: Despite API docs mentioning 16kHz, the working implementation uses 24kHz
 */
export const ALIYUN_AUDIO_CONFIG = {
    // PCM requirements matching WORKING Python implementation (debug/vad_mode.py)
    sampleRate: 24000,      // 24kHz sample rate (matches Python working implementation)
    bitDepth: 16,           // 16-bit PCM (required by Aliyun API)
    numChannels: 1,         // Mono audio (required by Aliyun API)
    channels: 1,            // Alias for numChannels (for compatibility)

    // Rate limiting to prevent 1011 errors (matches Python implementation)
    minIntervalMs: 200,     // 200ms = 5 chunks/sec (matches Python asyncio.sleep(0.2))
    maxChunksPerSecond: 5,  // Maximum audio chunks per second

    // Audio format settings
    inputFormat: 'pcm16',   // PCM 16-bit format for input
    outputFormat: 'pcm16',  // PCM 16-bit format for output
    format: 'pcm16',        // Alias for compatibility

    // Audio processing settings (matches Python vad_mode.py exactly)
    chunkSize: 3200,        // Audio chunk size in samples (matches Python CHUNK = 3200)
    chunkDurationMs: 200,   // Duration of each chunk in milliseconds

    // Audio context settings for Web Audio API
    audioContextConfig: {
        sampleRate: 24000,  // CRITICAL: Must match working Python implementation
        echoCancellation: true,
        noiseSuppression: true,
        autoGainControl: true,
    },

    // Default voice settings
    defaultVoice: 'Chelsie',
    supportedVoices: ['Chelsie', 'Serena', 'Ethan', 'Cherry'],

    // Debug settings
    enableDebugLogging: true
};

/**
 * VAD (Voice Activity Detection) configuration for Aliyun Qwen-Omni API
 * Based on official API specification and debug/omni_realtime_client.py
 */
export const ALIYUN_VAD_CONFIG = {
    // VAD settings matching API specification
    type: 'server_vad',
    threshold: 0.8,           // Range: [-1.0, 1.0], matches omni_realtime_client.py (changed from 0.1 to 0.8)
    prefix_padding_ms: 500,   // Pre-speech padding (matches omni_realtime_client.py)
    silence_duration_ms: 1500, // Post-speech silence detection (changed from 900 to 1500 to match Python)
    create_response: true,    // Auto-generate responses
    interrupt_response: true  // Enable barge-in functionality
};

/**
 * Session configuration for Aliyun Qwen-Omni API
 * @param {Object} config - Custom configuration overrides
 * @returns {Object} Valid session configuration
 */
export function buildValidTurnDetection(config = {}) {
    return {
        type: config.type || ALIYUN_VAD_CONFIG.type,
        threshold: config.threshold !== undefined ? config.threshold : ALIYUN_VAD_CONFIG.threshold,
        prefix_padding_ms: config.prefixPaddingMs || ALIYUN_VAD_CONFIG.prefix_padding_ms,
        silence_duration_ms: config.silenceDurationMs || ALIYUN_VAD_CONFIG.silence_duration_ms,
        create_response: config.createResponse !== false,
        interrupt_response: config.interruptResponse !== false
    };
}

/**
 * Build a valid session configuration for Aliyun Qwen-Omni API
 * @param {Object} session - Custom session configuration overrides
 * @returns {Object} Valid session configuration
 */
export function buildValidSessionConfig(session = {}) {
    return {
        modalities: session.modalities || ['text', 'audio'],
        voice: session.voice || ALIYUN_AUDIO_CONFIG.defaultVoice,
        input_audio_format: session.input_audio_format || ALIYUN_AUDIO_CONFIG.inputFormat,
        output_audio_format: session.output_audio_format || ALIYUN_AUDIO_CONFIG.outputFormat,
        input_audio_transcription: session.input_audio_transcription || {
            model: 'gummy-realtime-v1'
        },
        turn_detection: session.turn_detection || buildValidTurnDetection(),
        tools: session.tools || [],
        tool_choice: session.tool_choice || 'auto',
        temperature: session.temperature || 0.8,
        max_response_output_tokens: session.max_response_output_tokens || 'inf'
    };
}

/**
 * Creates a properly formatted session.update event following the pattern in omni_realtime_client.py
 * This is the recommended way to create session update events for the Aliyun API
 * @param {Object} sessionConfig - Session configuration overrides
 * @returns {Object} Properly formatted session.update event
 */
export function createSessionUpdateEvent(sessionConfig = {}) {
    return {
        event_id: generateEventId(),
        type: 'session.update',
        session: buildValidSessionConfig(sessionConfig)
    };
}

/**
 * Creates a properly formatted event object for Aliyun API, following Python's pattern:
 * 1. Create event with type and data
 * 2. Add event_id right before sending
 * 
 * @param {string} type - Event type
 * @param {Object} data - Event data
 * @returns {Object} Formatted event object
 */
export function createEvent(type, data = {}) {
    // Step 1: Create event with type and data
    const event = {
        type,
        ...data
    };

    // Step 2: Add event_id right before sending
    event.event_id = generateEventId();

    return event;
}

/**
 * Helper function to get audio configuration for MediaCaptureManager
 * Converts Aliyun config to MediaCaptureManager format
 * @param {Object} overrides - Custom configuration overrides
 * @returns {Object} MediaCaptureManager compatible audio configuration
 */
export function getMediaCaptureAudioConfig(overrides = {}) {
    return {
        audio: {
            sampleRate: overrides.sampleRate || ALIYUN_AUDIO_CONFIG.sampleRate,
            echoCancellation: overrides.echoCancellation !== undefined ?
                overrides.echoCancellation : ALIYUN_AUDIO_CONFIG.audioContextConfig.echoCancellation,
            noiseSuppression: overrides.noiseSuppression !== undefined ?
                overrides.noiseSuppression : ALIYUN_AUDIO_CONFIG.audioContextConfig.noiseSuppression,
            autoGainControl: overrides.autoGainControl !== undefined ?
                overrides.autoGainControl : ALIYUN_AUDIO_CONFIG.audioContextConfig.autoGainControl,
            channels: overrides.channels || ALIYUN_AUDIO_CONFIG.channels,
            bitDepth: overrides.bitDepth || ALIYUN_AUDIO_CONFIG.bitDepth
        },
        vadMode: overrides.vadMode || 'server',
        vadConfig: {
            threshold: overrides.vadThreshold || ALIYUN_VAD_CONFIG.threshold,
            silenceDurationMs: overrides.silenceDurationMs || ALIYUN_VAD_CONFIG.silence_duration_ms,
            createResponse: overrides.createResponse !== undefined ?
                overrides.createResponse : ALIYUN_VAD_CONFIG.create_response,
            interruptResponse: overrides.interruptResponse !== undefined ?
                overrides.interruptResponse : ALIYUN_VAD_CONFIG.interrupt_response
        },
        targetSampleRate: overrides.targetSampleRate || ALIYUN_AUDIO_CONFIG.sampleRate,
        targetBitDepth: overrides.targetBitDepth || ALIYUN_AUDIO_CONFIG.bitDepth,
        targetChannels: overrides.targetChannels || ALIYUN_AUDIO_CONFIG.numChannels,
        minIntervalMs: overrides.minIntervalMs || ALIYUN_AUDIO_CONFIG.minIntervalMs
    };
}

/**
 * Helper function to get realtime client configuration
 * @param {Object} overrides - Custom configuration overrides
 * @returns {Object} Realtime client compatible configuration
 */
export function getRealtimeClientConfig(overrides = {}) {
    return {
        audioConfig: {
            sampleRate: overrides.sampleRate || ALIYUN_AUDIO_CONFIG.sampleRate,
            channels: overrides.channels || ALIYUN_AUDIO_CONFIG.channels,
            bitDepth: overrides.bitDepth || ALIYUN_AUDIO_CONFIG.bitDepth,
            echoCancellation: overrides.echoCancellation !== undefined ?
                overrides.echoCancellation : ALIYUN_AUDIO_CONFIG.audioContextConfig.echoCancellation,
            noiseSuppression: overrides.noiseSuppression !== undefined ?
                overrides.noiseSuppression : ALIYUN_AUDIO_CONFIG.audioContextConfig.noiseSuppression,
            autoGainControl: overrides.autoGainControl !== undefined ?
                overrides.autoGainControl : ALIYUN_AUDIO_CONFIG.audioContextConfig.autoGainControl
        },
        vadConfig: {
            threshold: overrides.vadThreshold || ALIYUN_VAD_CONFIG.threshold,
            prefixPaddingMs: overrides.prefixPaddingMs || ALIYUN_VAD_CONFIG.prefix_padding_ms,
            silenceDurationMs: overrides.silenceDurationMs || ALIYUN_VAD_CONFIG.silence_duration_ms,
            createResponse: overrides.createResponse !== undefined ?
                overrides.createResponse : ALIYUN_VAD_CONFIG.create_response,
            interruptResponse: overrides.interruptResponse !== undefined ?
                overrides.interruptResponse : ALIYUN_VAD_CONFIG.interrupt_response
        },
        model: overrides.model || 'qwen-omni-turbo-realtime',
        vadMode: overrides.vadMode || 'server'
    };
}

/**
 * Helper function to validate audio parameters against Aliyun requirements
 * @param {Object} audioConfig - Audio configuration to validate
 * @returns {Object} Validation result with isValid flag and errors
 */
export function validateAudioConfig(audioConfig) {
    const errors = [];
    const warnings = [];

    // Check sample rate
    if (audioConfig.sampleRate !== ALIYUN_AUDIO_CONFIG.sampleRate) {
        errors.push(`Sample rate must be ${ALIYUN_AUDIO_CONFIG.sampleRate}Hz, got ${audioConfig.sampleRate}Hz`);
    }

    // Check bit depth
    if (audioConfig.bitDepth !== ALIYUN_AUDIO_CONFIG.bitDepth) {
        errors.push(`Bit depth must be ${ALIYUN_AUDIO_CONFIG.bitDepth}-bit, got ${audioConfig.bitDepth}-bit`);
    }

    // Check channels
    if (audioConfig.channels !== ALIYUN_AUDIO_CONFIG.channels &&
        audioConfig.numChannels !== ALIYUN_AUDIO_CONFIG.numChannels) {
        errors.push(`Audio must be mono (1 channel), got ${audioConfig.channels || audioConfig.numChannels} channels`);
    }

    // Check VAD threshold range
    if (audioConfig.vadThreshold !== undefined &&
        (audioConfig.vadThreshold < -1.0 || audioConfig.vadThreshold > 1.0)) {
        warnings.push(`VAD threshold should be in range [-1.0, 1.0], got ${audioConfig.vadThreshold}`);
    }

    return {
        isValid: errors.length === 0,
        errors,
        warnings
    };
}

export default {
    ALIYUN_AUDIO_CONFIG,
    ALIYUN_VAD_CONFIG,
    ConnectionState,
    SessionState,
    VADState,
    AliyunEventType,
    generateEventId,
    buildValidTurnDetection,
    buildValidSessionConfig,
    createSessionUpdateEvent,
    createEvent,
    getMediaCaptureAudioConfig,
    getRealtimeClientConfig,
    validateAudioConfig
}; 