/**
 * Connection Manager for Aliyun Bailian Chat Model
 * Handles WebSocket connection lifecycle, authentication, and connection state management
 */

import { createLogger } from '../../../../utils/logger.js';
import { getDownloadServerPort } from '../../../../utils/portManager.js';
import {
    ConnectionState,
    createEvent
} from '../config/AliyunConfig.js';

/**
 * WebSocket close codes and their meanings
 */
export const WebSocketCloseCodes = {
    1000: 'Normal closure',
    1006: 'Abnormal closure (connection lost)',
    1011: 'Server internal error',
    4001: 'Authentication failed',
    4008: 'Rate limit exceeded',
    4009: 'Audio format error'
};

/**
 * Utility function to create error objects
 */
export const createError = (type, message, code = null, details = {}) => ({
    type,
    message,
    code,
    details,
    timestamp: Date.now()
});

/**
 * Default configuration for ConnectionManager
 */
export const createDefaultConfig = () => ({
    apiKey: '',
    model: 'qwen-omni-turbo-realtime',
    realtimeEndpoint: 'wss://dashscope.aliyuncs.com/api-ws/v1/realtime'
});

/**
 * ConnectionManager manages WebSocket connections to Aliyun Bailian realtime API
 * Direct connection to Aliyun API endpoints
 */
export class ConnectionManager {
    constructor(config = {}) {
        this.logger = createLogger('AliyunConnectionManager');
        this.config = { ...createDefaultConfig(), ...config };

        // Connection state
        this.socket = null;
        this.state = ConnectionState.DISCONNECTED;
        this.connectionMethod = null;

        // Connection management
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 3;
        this.connectionTimeout = null;
        this.heartbeatInterval = null;

        // Event handlers
        this.eventHandlers = {
            stateChange: [],
            message: [],
            error: [],
            close: []
        };

        // Statistics
        this.stats = {
            connectionsAttempted: 0,
            connectionsSuccessful: 0,
            messagesReceived: 0,
            messagesSent: 0,
            reconnections: 0,
            lastConnectTime: null,
            totalUptime: 0
        };

        this.logger.debug('ConnectionManager initialized', {
            realtimeEndpoint: this.config.realtimeEndpoint,
            model: this.config.model
        });
    }

    /**
     * Establish WebSocket connection to Aliyun realtime API
     * @param {Object} options - Connection options
     * @returns {Promise<boolean>} Success status
     */
    async connect(options = {}) {
        if (this.state === ConnectionState.CONNECTED || this.state === ConnectionState.CONNECTING) {
            this.logger.warn('Connection already established or in progress');
            return true;
        }

        this._setState(ConnectionState.CONNECTING);
        this.stats.connectionsAttempted++;

        try {
            // Get WebSocket class (dynamic import for Node.js if needed)
            const WSClass = await this._getWebSocketClass();

            // Determine connection method and URL
            const { wsUrl, method } = this._buildConnectionUrl();
            this.connectionMethod = method;

            this.logger.info(`🔌 Connecting to Aliyun WebSocket via ${method}:`,
                wsUrl.replace(this.config.apiKey || '', '[REDACTED]'));

            // Create WebSocket connection
            if (method === 'proxy') {
                // Browser environment - connecting to local proxy
                this.socket = new WSClass(wsUrl);
            } else {
                // Node.js environment - direct connection with Authorization header
                this.socket = new WSClass(wsUrl, {
                    headers: {
                        'Authorization': `Bearer ${this.config.apiKey}`
                    }
                });
            }

            // Set up connection timeout
            this.connectionTimeout = setTimeout(() => {
                if (this.state === ConnectionState.CONNECTING) {
                    this._handleConnectionTimeout();
                }
            }, 10000);

            // Configure WebSocket event handlers
            this._setupWebSocketHandlers();

            return new Promise((resolve, reject) => {
                // Store resolve/reject for connection handlers
                this._connectionPromise = { resolve, reject };
            });

        } catch (error) {
            this.logger.error('❌ Connection attempt failed:', error);
            this._setState(ConnectionState.ERROR);
            return false;
        }
    }

    /**
     * Close WebSocket connection gracefully
     * @param {number} code - Close code (default: 1000 normal closure)
     * @param {string} reason - Close reason
     */
    async disconnect(code = 1000, reason = 'Client disconnect') {
        if (this.state === ConnectionState.DISCONNECTED || this.state === ConnectionState.CLOSING) {
            return;
        }

        this.logger.info('🔌 Disconnecting from Aliyun WebSocket');
        this._setState(ConnectionState.CLOSING);

        // Clear timers
        this._clearTimers();

        // Close WebSocket if exists
        if (this.socket) {
            try {
                if (this.socket.readyState === 1 /* WebSocket.OPEN */) {
                    this.socket.close(code, reason);
                }
            } catch (error) {
                this.logger.warn('Error closing WebSocket:', error);
            }
        }

        // Clean up
        this._cleanup();
        this._setState(ConnectionState.DISCONNECTED);
    }

    /**
     * Send message through WebSocket connection
     * @param {Object|string} message - Message to send
     * @returns {boolean} Success status
     */
    send(message) {
        if (this.state !== ConnectionState.CONNECTED || !this.socket) {
            this.logger.warn('❌ Cannot send message: WebSocket not connected');
            return false;
        }

        try {
            const messageStr = typeof message === 'string' ? message : JSON.stringify(message);
            this.socket.send(messageStr);
            this.stats.messagesSent++;

            this.logger.debug('📤 Message sent', {
                type: message.type || 'unknown',
                eventId: message.event_id || 'none',
                size: messageStr.length
            });

            return true;
        } catch (error) {
            this.logger.error('❌ Failed to send message:', error);
            return false;
        }
    }

    /**
     * Send formatted event through WebSocket connection
     * @param {string} type - Event type
     * @param {Object} data - Event data
     * @returns {boolean} Success status
     */
    sendEvent(type, data = {}) {
        if (!this.isConnected()) {
            this.logger.warn(`❌ Cannot send ${type}: WebSocket not connected`);
            return false;
        }

        try {
            // Create properly formatted event
            const event = createEvent(type, data);

            // Send via WebSocket
            this.send(event);

            this.logger.debug(`📤 [EventDebug] Sent ${type} event:`, {
                eventId: event.event_id,
                type: event.type,
                timestamp: new Date().toISOString()
            });

            return true;
        } catch (error) {
            this.logger.error(`❌ [EventDebug] Error sending ${type} event:`, error);
            return false;
        }
    }

    /**
     * Send audio buffer append event
     * @param {string} audioBase64 - Base64 encoded audio data
     * @returns {boolean} Success status
     */
    sendAudioBufferAppend(audioBase64) {
        return this.sendEvent('input_audio_buffer.append', { audio: audioBase64 });
    }

    /**
     * Send audio buffer commit event
     * @returns {boolean} Success status
     */
    sendAudioBufferCommit() {
        return this.sendEvent('input_audio_buffer.commit');
    }

    /**
     * Send audio buffer clear event
     * @returns {boolean} Success status
     */
    sendAudioBufferClear() {
        return this.sendEvent('input_audio_buffer.clear');
    }

    /**
     * Send response create event
     * @param {Object} responseConfig - Response configuration
     * @returns {boolean} Success status
     */
    sendResponseCreate(responseConfig = {}) {
        const defaultConfig = {
            instructions: "You are a helpful assistant.",
            modalities: ["text", "audio"]
        };

        return this.sendEvent('response.create', {
            response: { ...defaultConfig, ...responseConfig }
        });
    }

    /**
     * Send response cancel event
     * @returns {boolean} Success status
     */
    sendResponseCancel() {
        return this.sendEvent('response.cancel');
    }

    /**
     * Send session update event
     * @param {Object} sessionConfig - Session configuration
     * @returns {boolean} Success status
     */
    sendSessionUpdate(sessionConfig) {
        // Import here to avoid circular dependency
        const { createSessionUpdateEvent } = require('../config/AliyunConfig.js');
        const sessionUpdateEvent = createSessionUpdateEvent(sessionConfig);
        return this.send(sessionUpdateEvent);
    }

    /**
     * Check if connection is active and ready
     * @returns {boolean} True if connected and ready
     */
    isConnected() {
        return this.state === ConnectionState.CONNECTED &&
            this.socket &&
            this.socket.readyState === 1; /* WebSocket.OPEN */
    }

    /**
     * Get current connection state
     * @returns {string} Current connection state
     */
    getState() {
        return this.state;
    }

    /**
     * Get connection statistics
     * @returns {Object} Connection statistics
     */
    getStats() {
        return {
            ...this.stats,
            currentState: this.state,
            connectionMethod: this.connectionMethod,
            successRate: this.stats.connectionsAttempted > 0
                ? (this.stats.connectionsSuccessful / this.stats.connectionsAttempted) * 100
                : 0
        };
    }

    /**
     * Add event listener
     * @param {string} event - Event type (stateChange, message, error, close)
     * @param {Function} handler - Event handler function
     */
    on(event, handler) {
        if (this.eventHandlers[event]) {
            this.eventHandlers[event].push(handler);
        }
    }

    /**
     * Remove event listener
     * @param {string} event - Event type
     * @param {Function} handler - Event handler function to remove
     */
    off(event, handler) {
        if (this.eventHandlers[event]) {
            const index = this.eventHandlers[event].indexOf(handler);
            if (index > -1) {
                this.eventHandlers[event].splice(index, 1);
            }
        }
    }

    /**
     * Test connection capabilities
     * @returns {Promise<Object>} Test results
     */
    async testConnection() {
        this.logger.info('🔍 Testing Aliyun WebSocket connection capabilities...');

        const results = {
            direct: { success: false, error: null, details: {} },
            environment: this._getEnvironmentInfo()
        };

        // Test direct connection to Aliyun API
        results.direct = await this._testDirectConnection();

        // Set recommendation
        results.recommendation = results.direct.success ? 'direct' : null;

        this.logger.info('📊 Connection test results:', results);
        return results;
    }

    // Private methods

    /**
     * Get WebSocket class (browser or Node.js)
     * @returns {Promise<Function>} WebSocket constructor
     * @private
     */
    async _getWebSocketClass() {
        if (typeof window !== 'undefined' && window.WebSocket) {
            return window.WebSocket;
        }

        // Node.js environment - dynamic import
        try {
            const { default: WebSocket } = await import('ws');
            return WebSocket;
        } catch (error) {
            throw new Error('WebSocket not available. Install ws package for Node.js environments.');
        }
    }

    /**
     * Build WebSocket connection URL - uses proxy in browser, direct in Node.js
     * @returns {Object} { wsUrl, method }
     * @private
     */
    _buildConnectionUrl() {
        const isBrowser = typeof window !== 'undefined' && window.WebSocket;

        if (isBrowser) {
            // Browser environment - connect to local proxy which handles authentication
            // The proxy adds the Authorization header and forwards to Aliyun
            // Get the actual server port from the port manager
            const serverPort = getDownloadServerPort();
            const proxyUrl = `ws://localhost:${serverPort}/ws?model=${this.config.model}`;
            return {
                wsUrl: proxyUrl,
                method: 'proxy'
            };
        } else {
            // Node.js environment - direct connection with Authorization header
            return {
                wsUrl: `${this.config.realtimeEndpoint}?model=${this.config.model}`,
                method: 'direct'
            };
        }
    }

    /**
     * Setup WebSocket event handlers
     * @private
     */
    _setupWebSocketHandlers() {
        this.socket.onopen = (event) => {
            this._handleConnectionOpen(event);
        };

        this.socket.onmessage = (event) => {
            this._handleMessage(event);
        };

        this.socket.onerror = (error) => {
            this._handleError(error);
        };

        this.socket.onclose = (event) => {
            this._handleClose(event);
        };
    }

    /**
     * Handle WebSocket connection open
     * @param {Event} event - Open event
     * @private
     */
    _handleConnectionOpen(event) {
        this._clearTimers();
        this._setState(ConnectionState.CONNECTED);
        this.stats.connectionsSuccessful++;
        this.stats.lastConnectTime = Date.now();
        this.reconnectAttempts = 0;

        this.logger.info('✅ WebSocket connection established successfully');

        // Start heartbeat if configured
        this._startHeartbeat();

        // Resolve connection promise
        if (this._connectionPromise) {
            this._connectionPromise.resolve(true);
            this._connectionPromise = null;
        }
    }

    /**
     * Handle WebSocket message
     * @param {MessageEvent} event - Message event
     * @private
     */
    _handleMessage(event) {
        this.stats.messagesReceived++;

        // Emit message event to all handlers
        this.eventHandlers.message.forEach(handler => {
            try {
                handler(event);
            } catch (error) {
                this.logger.error('Error in message handler:', error);
            }
        });
    }

    /**
     * Handle WebSocket error
     * @param {Error} error - Error event
     * @private
     */
    _handleError(error) {
        this.logger.error('❌ WebSocket error:', error);

        const errorObj = createError('websocket_error', error.message || 'WebSocket error', null, {
            connectionMethod: this.connectionMethod,
            currentState: this.state
        });

        // Emit error event to all handlers
        this.eventHandlers.error.forEach(handler => {
            try {
                handler(errorObj);
            } catch (handlerError) {
                this.logger.error('Error in error handler:', handlerError);
            }
        });

        if (this._connectionPromise) {
            this._connectionPromise.reject(error);
            this._connectionPromise = null;
        }
    }

    /**
     * Handle WebSocket close
     * @param {CloseEvent} event - Close event
     * @private
     */
    _handleClose(event) {
        this._clearTimers();

        const closeReason = WebSocketCloseCodes[event.code] || `Unknown (${event.code})`;

        this.logger.warn('🔌 WebSocket connection closed:', {
            code: event.code,
            reason: event.reason || closeReason,
            wasClean: event.wasClean,
            connectionMethod: this.connectionMethod
        });

        // Handle specific error codes
        this._handleCloseCode(event.code);

        // Clean up
        this._cleanup();
        this._setState(ConnectionState.DISCONNECTED);

        // Emit close event
        this.eventHandlers.close.forEach(handler => {
            try {
                handler(event);
            } catch (error) {
                this.logger.error('Error in close handler:', error);
            }
        });

        // Auto-reconnect logic for certain error codes
        if (this._shouldAutoReconnect(event.code)) {
            this._attemptReconnect();
        }
    }

    /**
     * Handle connection timeout
     * @private
     */
    _handleConnectionTimeout() {
        this.logger.error('❌ WebSocket connection timeout');
        this._setState(ConnectionState.ERROR);

        if (this.socket) {
            this.socket.close();
        }

        if (this._connectionPromise) {
            this._connectionPromise.reject(new Error('Connection timeout'));
            this._connectionPromise = null;
        }
    }

    /**
     * Handle specific WebSocket close codes
     * @param {number} code - Close code
     * @private
     */
    _handleCloseCode(code) {
        switch (code) {
            case 1011: // Server internal error
                this.logger.error('💡 Server internal error (1011). Possible causes:');
                this.logger.error('   → Rate limiting: slow down request frequency');
                this.logger.error('   → Session configuration issues');
                this.logger.error('   → API key permissions');
                break;
            case 1006: // Abnormal closure
                this.logger.error('💡 Abnormal closure (1006). Check:');
                this.logger.error('   → Network connectivity');
                this.logger.error('   → Firewall restrictions');
                break;
            case 4001: // Authentication failed
                this.logger.error('💡 Authentication failed (4001). Check:');
                this.logger.error('   → API key validity');
                this.logger.error('   → API key permissions for realtime models');
                break;
            case 4008: // Rate limit
                this.logger.error('💡 Rate limit exceeded (4008). Reduce:');
                this.logger.error('   → Audio chunk frequency');
                this.logger.error('   → Image sending rate');
                break;
        }
    }

    /**
     * Determine if auto-reconnect should be attempted
     * @param {number} code - Close code
     * @returns {boolean} True if should auto-reconnect
     * @private
     */
    _shouldAutoReconnect(code) {
        // Don't auto-reconnect for authentication or client errors
        const noReconnectCodes = [4001, 4003, 4004];
        return !noReconnectCodes.includes(code) &&
            this.reconnectAttempts < this.maxReconnectAttempts;
    }

    /**
     * Attempt to reconnect with exponential backoff
     * @private
     */
    async _attemptReconnect() {
        this.reconnectAttempts++;
        this.stats.reconnections++;

        const delay = Math.min(1000 * Math.pow(2, this.reconnectAttempts - 1), 10000);

        this.logger.info(`🔄 Attempting reconnection ${this.reconnectAttempts}/${this.maxReconnectAttempts} in ${delay}ms`);

        setTimeout(async () => {
            try {
                await this.connect();
            } catch (error) {
                this.logger.error('❌ Reconnection attempt failed:', error);
            }
        }, delay);
    }

    /**
     * Start heartbeat to keep connection alive
     * @private
     */
    _startHeartbeat() {
        // Heartbeat implementation could be added here if needed
        // For now, Aliyun WebSocket doesn't require explicit heartbeat
    }

    /**
     * Clear all timers
     * @private
     */
    _clearTimers() {
        if (this.connectionTimeout) {
            clearTimeout(this.connectionTimeout);
            this.connectionTimeout = null;
        }
        if (this.heartbeatInterval) {
            clearInterval(this.heartbeatInterval);
            this.heartbeatInterval = null;
        }
    }

    /**
     * Cleanup connection resources
     * @private
     */
    _cleanup() {
        this._clearTimers();
        this.socket = null;
        this.connectionMethod = null;
    }

    /**
     * Set connection state and notify handlers
     * @param {string} newState - New connection state
     * @private
     */
    _setState(newState) {
        const oldState = this.state;
        this.state = newState;

        if (oldState !== newState) {
            this.logger.debug(`Connection state: ${oldState} → ${newState}`);

            // Emit state change event
            this.eventHandlers.stateChange.forEach(handler => {
                try {
                    handler(newState, oldState);
                } catch (error) {
                    this.logger.error('Error in state change handler:', error);
                }
            });
        }
    }

    /**
     * Test direct connection
     * @returns {Promise<Object>} Test result
     * @private
     */
    async _testDirectConnection() {
        try {
            const WSClass = await this._getWebSocketClass();
            const directUrl = `${this.config.realtimeEndpoint}?model=${encodeURIComponent(this.config.model)}`;
            const testSocket = new WSClass(directUrl, {
                headers: {
                    'Authorization': `Bearer ${this.config.apiKey}`
                }
            });

            return new Promise((resolve) => {
                const timeout = setTimeout(() => {
                    testSocket.close();
                    resolve({
                        success: false,
                        error: 'Timeout',
                        details: { endpoint: directUrl.replace(this.config.apiKey || '', '[REDACTED]') }
                    });
                }, 5000);

                testSocket.onopen = () => {
                    clearTimeout(timeout);
                    testSocket.close();
                    resolve({
                        success: true,
                        details: { endpoint: directUrl.replace(this.config.apiKey || '', '[REDACTED]') }
                    });
                };

                testSocket.onerror = (error) => {
                    clearTimeout(timeout);
                    resolve({
                        success: false,
                        error: error.message || 'Connection failed',
                        details: { endpoint: directUrl.replace(this.config.apiKey || '', '[REDACTED]') }
                    });
                };
            });
        } catch (error) {
            return {
                success: false,
                error: error.message,
                details: {}
            };
        }
    }

    /**
     * Get environment information
     * @returns {Object} Environment details
     * @private
     */
    _getEnvironmentInfo() {
        return {
            isBrowser: typeof window !== 'undefined',
            hasWebSocket: typeof WebSocket !== 'undefined',
            userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : 'Node.js'
        };
    }
} 