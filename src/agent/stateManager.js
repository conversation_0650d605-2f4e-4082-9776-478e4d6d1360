/**
 * Avatar State Manager
 * Centralized state management using LangGraph agent for unified state control
 */

import { createLogger } from '../utils/logger.js';

/**
 * Avatar State Manager
 * Replaces scattered state logic in TalkingAvatar with centralized LangGraph-based state control
 */
export class AvatarStateManager {
    constructor(agentService, options = {}) {
        this.logger = createLogger('AvatarStateManager');
        this.agentService = agentService;

        // State configuration
        this.states = {
            IDLE: 'idle',
            LISTENING: 'listening',
            PROCESSING: 'processing',
            SPEAKING: 'speaking',
            ERROR: 'error'
        };

        // Current state tracking
        this.currentState = this.states.IDLE;
        this.previousState = null;
        this.stateHistory = [];

        // State callbacks
        this.callbacks = {
            onStateChange: options.onStateChange || (() => { }),
            onListeningStart: options.onListeningStart || (() => { }),
            onListeningStop: options.onListeningStop || (() => { }),
            onSpeakingStart: options.onSpeakingStart || (() => { }),
            onSpeakingStop: options.onSpeakingStop || (() => { }),
            onProcessingStart: options.onProcessingStart || (() => { }),
            onProcessingStop: options.onProcessingStop || (() => { }),
            onError: options.onError || (() => { })
        };

        // UI elements
        this.ui = {
            statusElement: options.statusElement || null,
            updateUI: options.updateUI || (() => { })
        };

        // Barge-in handling
        this.bargeInEnabled = options.bargeInEnabled !== false;
        this.lastBargeInTime = null;
        this.bargeInCooldown = options.bargeInCooldown || 500; // ms

        // Bind to agent service state updates
        if (this.agentService && this.agentService.stateCallbacks) {
            this.agentService.stateCallbacks.onStateUpdate = this._handleAgentStateUpdate.bind(this);
            this.agentService.stateCallbacks.onModeChange = this._handleModeChange.bind(this);
        }

        this.logger.info('Avatar State Manager initialized with LangGraph integration');
    }

    /**
     * Set avatar state and handle transitions through LangGraph
     */
    async setState(newState, metadata = {}) {
        try {
            const sessionId = metadata.sessionId || 'default';
            const oldState = this.currentState;

            // Validate state
            if (!Object.values(this.states).includes(newState)) {
                this.logger.warn(`Invalid state: ${newState}, defaulting to idle`);
                newState = this.states.IDLE;
            }

            // Skip if no change
            if (oldState === newState && !metadata.force) {
                return;
            }

            // Check for valid transitions
            if (!this.canTransitionTo(newState)) {
                this.logger.warn(`Invalid state transition: ${oldState} → ${newState}`);
                return;
            }

            this.logger.info(`State transition: ${oldState} → ${newState}`, metadata);

            // Update state tracking
            this.previousState = oldState;
            this.currentState = newState;
            this.stateHistory.push({
                from: oldState,
                to: newState,
                timestamp: Date.now(),
                metadata
            });

            // Limit history size
            if (this.stateHistory.length > 50) {
                this.stateHistory = this.stateHistory.slice(-50);
            }

            // Update agent service state through LangGraph
            if (this.agentService) {
                await this.agentService.setAvatarMode(newState, sessionId);
            }

            // Handle state-specific logic
            await this._handleStateTransition(oldState, newState, metadata);

            // Notify callbacks
            this.callbacks.onStateChange(newState, oldState, metadata);

            // Update UI
            this._updateUI(newState, metadata);

        } catch (error) {
            this.logger.error('Error setting state:', error);
            this.callbacks.onError(error);
        }
    }

    /**
     * Handle agent service state updates from LangGraph
     */
    _handleAgentStateUpdate(agentState, sessionId) {
        try {
            // Map agent state to local state
            const localState = agentState.mode || this.states.IDLE;

            // Update local state without triggering agent update (avoid loops)
            if (localState !== this.currentState) {
                const oldState = this.currentState;
                this.currentState = localState;

                // Add to history
                this.stateHistory.push({
                    from: oldState,
                    to: localState,
                    timestamp: Date.now(),
                    metadata: { source: 'agent', sessionId, agentState }
                });

                // Handle transition logic
                this._handleStateTransition(oldState, localState, { source: 'agent' });

                // Notify callbacks
                this.callbacks.onStateChange(localState, oldState, { source: 'agent' });

                // Update UI
                this._updateUI(localState, { source: 'agent' });
            }

        } catch (error) {
            this.logger.error('Error handling agent state update:', error);
        }
    }

    /**
     * Handle mode changes from LangGraph agent
     */
    _handleModeChange(mode, sessionId) {
        this.logger.debug(`Mode change from agent: ${mode} (session: ${sessionId})`);

        // Additional mode-specific logic can be added here
        switch (mode) {
            case 'processing':
                this.callbacks.onProcessingStart();
                break;
            case 'speaking':
                this.callbacks.onSpeakingStart();
                break;
            case 'listening':
                this.callbacks.onListeningStart();
                break;
            case 'idle':
                // Call appropriate stop callbacks based on previous state
                if (this.previousState === 'speaking') {
                    this.callbacks.onSpeakingStop();
                } else if (this.previousState === 'listening') {
                    this.callbacks.onListeningStop();
                } else if (this.previousState === 'processing') {
                    this.callbacks.onProcessingStop();
                }
                break;
        }
    }

    /**
     * Handle state-specific transition logic
     */
    async _handleStateTransition(fromState, toState, metadata) {
        // Handle exit logic for old state
        switch (fromState) {
            case this.states.LISTENING:
                this.callbacks.onListeningStop();
                break;
            case this.states.SPEAKING:
                this.callbacks.onSpeakingStop();
                break;
            case this.states.PROCESSING:
                this.callbacks.onProcessingStop();
                break;
        }

        // Handle entry logic for new state
        switch (toState) {
            case this.states.LISTENING:
                this.callbacks.onListeningStart();
                break;
            case this.states.SPEAKING:
                this.callbacks.onSpeakingStart();
                break;
            case this.states.PROCESSING:
                this.callbacks.onProcessingStart();
                break;
            case this.states.ERROR:
                this.callbacks.onError(metadata.error || 'Unknown error');
                break;
        }
    }

    /**
     * Start listening mode with LangGraph state management
     */
    async startListening(sessionId = 'default') {
        // Check if already listening
        if (this.currentState === this.states.LISTENING) {
            this.logger.info('Already in listening state');
            return;
        }

        // Handle barge-in if currently speaking
        if (this.currentState === this.states.SPEAKING) {
            await this.handleBargeIn(sessionId);
        }

        await this.setState(this.states.LISTENING, { sessionId });
    }

    /**
     * Stop listening mode
     */
    async stopListening(sessionId = 'default') {
        if (this.currentState === this.states.LISTENING) {
            await this.setState(this.states.IDLE, { sessionId });
        }
    }

    /**
     * Start speaking mode
     */
    async startSpeaking(sessionId = 'default') {
        await this.setState(this.states.SPEAKING, { sessionId });
    }

    /**
     * Stop speaking mode
     */
    async stopSpeaking(sessionId = 'default') {
        if (this.currentState === this.states.SPEAKING) {
            await this.setState(this.states.IDLE, { sessionId });
        }
    }

    /**
     * Start processing mode
     */
    async startProcessing(sessionId = 'default') {
        await this.setState(this.states.PROCESSING, { sessionId });
    }

    /**
     * Handle barge-in interruption with cooldown protection
     */
    async handleBargeIn(sessionId = 'default') {
        if (!this.bargeInEnabled) {
            this.logger.debug('Barge-in disabled, ignoring request');
            return;
        }

        const now = Date.now();

        // Cooldown protection
        if (this.lastBargeInTime && (now - this.lastBargeInTime) < this.bargeInCooldown) {
            this.logger.debug('Barge-in cooldown active, ignoring request');
            return;
        }

        this.lastBargeInTime = now;

        this.logger.info('Handling barge-in interruption');

        // Use agent service to handle barge-in through LangGraph
        if (this.agentService) {
            await this.agentService.handleBargeIn(sessionId);
        }

        // Set to listening state
        await this.setState(this.states.LISTENING, {
            sessionId,
            bargeIn: true,
            timestamp: now
        });
    }

    /**
     * Set error state
     */
    async setError(error, sessionId = 'default') {
        await this.setState(this.states.ERROR, {
            sessionId,
            error: error.message || error
        });
    }

    /**
     * Return to idle state
     */
    async returnToIdle(sessionId = 'default') {
        await this.setState(this.states.IDLE, { sessionId });
    }

    /**
     * Get current state information
     */
    getState() {
        return {
            current: this.currentState,
            previous: this.previousState,
            history: this.stateHistory.slice(-5), // Last 5 transitions
            // Backward compatibility flags
            isListening: this.currentState === this.states.LISTENING,
            isSpeaking: this.currentState === this.states.SPEAKING,
            isProcessing: this.currentState === this.states.PROCESSING,
            isIdle: this.currentState === this.states.IDLE,
            isError: this.currentState === this.states.ERROR
        };
    }

    /**
     * Get state history
     */
    getStateHistory(limit = 10) {
        return this.stateHistory.slice(-limit);
    }

    /**
     * Check if can transition to new state
     */
    canTransitionTo(newState) {
        const currentState = this.currentState;

        // Define allowed transitions
        const allowedTransitions = {
            [this.states.IDLE]: [this.states.LISTENING, this.states.PROCESSING, this.states.SPEAKING, this.states.ERROR],
            [this.states.LISTENING]: [this.states.IDLE, this.states.PROCESSING, this.states.SPEAKING, this.states.ERROR],
            [this.states.PROCESSING]: [this.states.IDLE, this.states.SPEAKING, this.states.LISTENING, this.states.ERROR],
            [this.states.SPEAKING]: [this.states.IDLE, this.states.LISTENING, this.states.ERROR],
            [this.states.ERROR]: [this.states.IDLE, this.states.LISTENING, this.states.PROCESSING, this.states.SPEAKING]
        };

        return allowedTransitions[currentState]?.includes(newState) || false;
    }

    /**
     * Update UI based on state
     */
    _updateUI(state, metadata) {
        const statusMessages = {
            [this.states.IDLE]: 'Ready',
            [this.states.LISTENING]: 'Listening...',
            [this.states.PROCESSING]: 'Processing...',
            [this.states.SPEAKING]: 'Speaking...',
            [this.states.ERROR]: 'Error'
        };

        const message = statusMessages[state] || 'Unknown State';

        // Update status element if available
        if (this.ui.statusElement) {
            this.ui.statusElement.textContent = message;
        }

        // Call custom UI update function
        if (this.ui.updateUI) {
            this.ui.updateUI(state, message, metadata);
        }

        this.logger.debug(`UI updated: ${state} - ${message}`);
    }

    /**
     * Add state change callback
     */
    onStateChange(callback) {
        if (typeof callback === 'function') {
            this.callbacks.onStateChange = callback;
        }
    }

    /**
     * Add listening callbacks
     */
    onListening(startCallback, stopCallback) {
        if (typeof startCallback === 'function') {
            this.callbacks.onListeningStart = startCallback;
        }
        if (typeof stopCallback === 'function') {
            this.callbacks.onListeningStop = stopCallback;
        }
    }

    /**
     * Add speaking callbacks
     */
    onSpeaking(startCallback, stopCallback) {
        if (typeof startCallback === 'function') {
            this.callbacks.onSpeakingStart = startCallback;
        }
        if (typeof stopCallback === 'function') {
            this.callbacks.onSpeakingStop = stopCallback;
        }
    }

    /**
     * Add processing callbacks
     */
    onProcessing(startCallback, stopCallback) {
        if (typeof startCallback === 'function') {
            this.callbacks.onProcessingStart = startCallback;
        }
        if (typeof stopCallback === 'function') {
            this.callbacks.onProcessingStop = stopCallback;
        }
    }

    /**
     * Add error callback
     */
    onError(callback) {
        if (typeof callback === 'function') {
            this.callbacks.onError = callback;
        }
    }

    /**
     * Dispose resources
     */
    dispose() {
        // Clear callbacks
        Object.keys(this.callbacks).forEach(key => {
            this.callbacks[key] = () => { };
        });

        // Clear agent service callbacks
        if (this.agentService && this.agentService.stateCallbacks) {
            this.agentService.stateCallbacks.onStateUpdate = () => { };
            this.agentService.stateCallbacks.onModeChange = () => { };
        }

        this.stateHistory = [];
        this.agentService = null;
        this.logger.info('Avatar State Manager disposed');
    }
}

export default AvatarStateManager; 