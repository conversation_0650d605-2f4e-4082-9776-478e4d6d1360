/**
 * Mem0 Memory Integration for TalkingHead
 * This module provides memory capabilities for the TalkingHead agent using Mem0
 */

// Dynamic import will be used in the initialize method to prevent build errors
import { endpoints } from '@/config/endpoints';

class AvatarMemory {
    constructor() {
        this.memory = null;
        this.initialized = false;
        this.config = {
            maxTokens: 1000,
            temperature: 0.7,
            maxMemories: 100,
            memoryDecay: 0.1,
            defaultSearchLimit: 3
        };
    }

    /**
     * Initialize the memory system
     * @returns {Promise<boolean>} Success status
     */
    async initialize() {
        try {
            // Dynamic import to prevent build errors when mem0ai is not installed
            const { MemoryClient } = await import('mem0ai');

            this.memory = new MemoryClient(
                {
                    apiKey: import.meta.env.VITE_MEM0_API_KEY,
                    host: endpoints.mem0
                });
            this.initialized = true;
            console.log('[AvatarMemory] Initialized Mem0 memory system');
            return true;
        } catch (error) {
            if (error.message.includes('Cannot resolve module')) {
                console.warn('[AvatarMemory] mem0ai package not available - memory features disabled');
            } else {
                console.error('[AvatarMemory] Failed to initialize:', error);
            }
            return false;
        }
    }

    /**
     * Add new memories to the system
     * @param {Array} messages - Array of message objects with role and content
     * @param {string} user_id - User identifier
     * @returns {Promise<boolean>} Success status
     */
    async add(messages, user_id) {
        if (!this.initialized || !this.memory) {
            console.warn('[AvatarMemory] Memory system not initialized');
            return false;
        }

        try {
            await this.memory.add(messages,
                {
                    user_id: user_id,
                    version: 'v2'
                });
            console.log(`[AvatarMemory] Added ${messages.length} memories for user ${user_id}`);
            return true;
        } catch (error) {
            console.error('[AvatarMemory] Failed to add memories:', error);
            return false;
        }
    }

    /**
     * Search for relevant memories using v2 API
     * @param {string} query - Search query
     * @param {string} user_id - User identifier
     * @param {string[]} agent_ids - Agent identifiers
     * @param {Object} options - Search options
     * @param {Object} options.filters - Additional filters for the search
     * @returns {Promise<Object>} Search results
     */
    async search(query, user_id, agent_ids, options = {}) {
        if (!this.initialized || !this.memory) {
            console.warn('[AvatarMemory] Memory system not initialized');
            return { results: [] };
        }

        try {
            const defaultFilters = {
                OR: [
                    { user_id: user_id },
                    { agent_id: { in: agent_ids } }
                ]
            };

            const searchParams = {
                api_version: 'v2',
                filters: {
                    ...defaultFilters,
                    ...(options.filters || {})
                },
            };

            const results = await this.memory.search(query, searchParams);
            console.log(`[AvatarMemory] Retrieved ${results.results.length} memories for query: ${query}`);
            return results;
        } catch (error) {
            console.error('[AvatarMemory] Failed to search memories:', error);
            return { results: [] };
        }
    }

    /**
     * Clear memories for a user
     * @param {string} user_id - User identifier
     * @returns {Promise<boolean>} Success status
     */
    async clear(user_id) {
        if (!this.initialized || !this.memory) {
            console.warn('[AvatarMemory] Memory system not initialized');
            return false;
        }

        try {
            await this.memory.clear(user_id);
            console.log(`[AvatarMemory] Cleared memories for user ${user_id}`);
            return true;
        } catch (error) {
            console.error('[AvatarMemory] Failed to clear memories:', error);
            return false;
        }
    }

    /**
     * Get memory statistics
     * @returns {Promise<Object>} Memory statistics
     */
    async getStats() {
        if (!this.initialized || !this.memory) {
            console.warn('[AvatarMemory] Memory system not initialized');
            return {};
        }

        try {
            const stats = await this.memory.getStats();
            return stats;
        } catch (error) {
            console.error('[AvatarMemory] Failed to get memory stats:', error);
            return {};
        }
    }

    /**
     * Update memory configuration
     * @param {Object} newConfig - New configuration options
     */
    updateConfig(newConfig) {
        this.config = {
            ...this.config,
            ...newConfig
        };
        console.log('[AvatarMemory] Updated configuration:', this.config);
    }
}

// Create singleton instance
const avatarMemory = new AvatarMemory();

// Make the memory system globally accessible
if (typeof window !== 'undefined') {
    window.avatarMemory = avatarMemory;
}

export default avatarMemory;