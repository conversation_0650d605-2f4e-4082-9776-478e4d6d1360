/**
 * LangChain Memory Management for Hologram Software
 * 
 * This module provides memory management functionality using LangChain.js
 * including conversation history, session management, and context persistence.
 */

import { BufferMemory, ConversationSummaryMemory } from 'langchain/memory';
import { ChatMessageHistory } from 'langchain/stores/message/in_memory';
import { HumanMessage, AIMessage, SystemMessage } from '@langchain/core/messages';
import { createLogger } from '../../utils/logger.js';

/**
 * LangChain-based Memory Manager
 * Handles conversation history, session state, and context management
 */
export class LangChainMemoryManager {
    constructor(options = {}) {
        this.logger = createLogger('LangChainMemoryManager');

        // Configuration
        this.maxHistoryLength = options.maxHistoryLength || 10;
        this.summarizeAfter = options.summarizeAfter || 20;
        this.enableSummary = options.enableSummary !== false;

        // Session storage
        this.sessions = new Map(); // sessionId -> BufferMemory
        this.summaryMemories = new Map(); // sessionId -> ConversationSummaryMemory
        this.messageHistories = new Map(); // sessionId -> ChatMessageHistory

        // Performance tracking
        this.stats = {
            totalSessions: 0,
            activeSessionss: 0,
            totalMessages: 0,
            memoryCleanups: 0
        };

        this.logger.info('LangChain Memory Manager initialized');
    }

    /**
     * Get or create memory for a session
     * @param {string} sessionId - Session identifier
     * @param {Object} options - Memory configuration options
     * @returns {BufferMemory} - LangChain BufferMemory instance
     */
    getSessionMemory(sessionId, options = {}) {
        if (!sessionId) {
            throw new Error('Session ID is required');
        }

        if (!this.sessions.has(sessionId)) {
            this.createSessionMemory(sessionId, options);
            this.stats.totalSessions++;
        }

        this.stats.activeSessionss = this.sessions.size;
        return this.sessions.get(sessionId);
    }

    /**
     * Create new memory for a session
     * @param {string} sessionId - Session identifier
     * @param {Object} options - Memory configuration options
     * @private
     */
    createSessionMemory(sessionId, options = {}) {
        // Create message history
        const messageHistory = new ChatMessageHistory();
        this.messageHistories.set(sessionId, messageHistory);

        // Create buffer memory
        const bufferMemory = new BufferMemory({
            chatHistory: messageHistory,
            memoryKey: 'conversation_history',
            returnMessages: true,
            k: options.maxMessages || this.maxHistoryLength
        });

        this.sessions.set(sessionId, bufferMemory);

        // Create summary memory if enabled
        if (this.enableSummary) {
            const summaryMemory = new ConversationSummaryMemory({
                chatHistory: messageHistory,
                memoryKey: 'conversation_summary',
                returnMessages: true
            });
            this.summaryMemories.set(sessionId, summaryMemory);
        }

        this.logger.debug(`Created memory for session: ${sessionId}`);
    }

    /**
     * Add conversation turn to session memory
     * @param {string} sessionId - Session identifier
     * @param {string} userMessage - User's message
     * @param {string} aiResponse - AI's response
     * @param {Object} metadata - Additional metadata
     */
    async addConversationTurn(sessionId, userMessage, aiResponse, metadata = {}) {
        if (!sessionId || !userMessage || !aiResponse) {
            throw new Error('Session ID, user message, and AI response are required');
        }

        const memory = this.getSessionMemory(sessionId);
        const messageHistory = this.messageHistories.get(sessionId);

        // Add messages to history
        await messageHistory.addMessage(new HumanMessage(userMessage));
        await messageHistory.addMessage(new AIMessage(aiResponse));

        // Update stats
        this.stats.totalMessages += 2;

        // Check if we need to summarize
        const messages = await messageHistory.getMessages();
        if (this.enableSummary && messages.length > this.summarizeAfter) {
            await this.summarizeConversation(sessionId);
        }

        this.logger.debug(`Added conversation turn to session: ${sessionId}`);
    }

    /**
     * Get conversation history for a session
     * @param {string} sessionId - Session identifier
     * @param {Object} options - Retrieval options
     * @returns {Array} - Array of LangChain messages
     */
    async getConversationHistory(sessionId, options = {}) {
        if (!sessionId) {
            return [];
        }

        const messageHistory = this.messageHistories.get(sessionId);
        if (!messageHistory) {
            return [];
        }

        const messages = await messageHistory.getMessages();

        // Apply filtering if requested
        if (options.limit) {
            return messages.slice(-options.limit);
        }

        if (options.messageType) {
            return messages.filter(msg => msg._getType() === options.messageType);
        }

        return messages;
    }

    /**
     * Get memory context for LangChain templates
     * @param {string} sessionId - Session identifier
     * @returns {Object} - Memory context for templates
     */
    async getMemoryContext(sessionId) {
        if (!sessionId) {
            return { conversation_history: [], conversation_summary: '' };
        }

        const memory = this.getSessionMemory(sessionId);
        const memoryContext = await memory.loadMemoryVariables({});

        // Add summary if available
        if (this.enableSummary && this.summaryMemories.has(sessionId)) {
            const summaryMemory = this.summaryMemories.get(sessionId);
            const summaryContext = await summaryMemory.loadMemoryVariables({});
            memoryContext.conversation_summary = summaryContext.conversation_summary || '';
        }

        return memoryContext;
    }

    /**
     * Summarize conversation when it gets too long
     * @param {string} sessionId - Session identifier
     * @private
     */
    async summarizeConversation(sessionId) {
        if (!this.summaryMemories.has(sessionId)) {
            return;
        }

        const summaryMemory = this.summaryMemories.get(sessionId);
        const messageHistory = this.messageHistories.get(sessionId);

        try {
            // Get current messages
            const messages = await messageHistory.getMessages();

            // Keep recent messages, summarize older ones
            const keepRecent = this.maxHistoryLength;
            const toSummarize = messages.slice(0, -keepRecent);
            const toKeep = messages.slice(-keepRecent);

            if (toSummarize.length > 0) {
                // Create summary of older messages
                await summaryMemory.saveContext(
                    { input: 'Summarize this conversation' },
                    { output: 'Conversation summarized' }
                );

                // Clear old messages and keep recent ones
                await messageHistory.clear();
                for (const message of toKeep) {
                    await messageHistory.addMessage(message);
                }

                this.logger.debug(`Summarized conversation for session: ${sessionId}`);
            }
        } catch (error) {
            this.logger.error(`Error summarizing conversation for session ${sessionId}:`, error);
        }
    }

    /**
     * Clear memory for a session
     * @param {string} sessionId - Session identifier
     */
    async clearSession(sessionId) {
        if (!sessionId) {
            return;
        }

        // Clear buffer memory
        if (this.sessions.has(sessionId)) {
            const memory = this.sessions.get(sessionId);
            await memory.clear();
            this.sessions.delete(sessionId);
        }

        // Clear message history
        if (this.messageHistories.has(sessionId)) {
            const messageHistory = this.messageHistories.get(sessionId);
            await messageHistory.clear();
            this.messageHistories.delete(sessionId);
        }

        // Clear summary memory
        if (this.summaryMemories.has(sessionId)) {
            this.summaryMemories.delete(sessionId);
        }

        this.stats.activeSessionss = this.sessions.size;
        this.stats.memoryCleanups++;

        this.logger.debug(`Cleared memory for session: ${sessionId}`);
    }

    /**
     * Cleanup old sessions based on criteria
     * @param {Object} options - Cleanup options
     */
    async cleanupOldSessions(options = {}) {
        const maxAge = options.maxAge || 24 * 60 * 60 * 1000; // 24 hours
        const maxSessions = options.maxSessions || 100;
        const now = Date.now();

        // This is a simplified cleanup - in production you'd track session timestamps
        if (this.sessions.size > maxSessions) {
            const sessionsToRemove = this.sessions.size - maxSessions;
            const sessionIds = Array.from(this.sessions.keys()).slice(0, sessionsToRemove);

            for (const sessionId of sessionIds) {
                await this.clearSession(sessionId);
            }

            this.logger.info(`Cleaned up ${sessionsToRemove} old sessions`);
        }
    }

    /**
     * Get memory statistics
     * @returns {Object} - Memory usage statistics
     */
    getMemoryStats() {
        return {
            ...this.stats,
            activeSessions: this.sessions.size,
            totalMessageHistories: this.messageHistories.size,
            totalSummaryMemories: this.summaryMemories.size
        };
    }

    /**
     * Export session data for persistence
     * @param {string} sessionId - Session identifier
     * @returns {Object} - Serializable session data
     */
    async exportSession(sessionId) {
        if (!sessionId || !this.messageHistories.has(sessionId)) {
            return null;
        }

        const messageHistory = this.messageHistories.get(sessionId);
        const messages = await messageHistory.getMessages();

        return {
            sessionId,
            messages: messages.map(msg => ({
                type: msg._getType(),
                content: msg.content,
                timestamp: Date.now()
            })),
            exportedAt: Date.now()
        };
    }

    /**
     * Import session data from persistence
     * @param {Object} sessionData - Serialized session data
     */
    async importSession(sessionData) {
        if (!sessionData || !sessionData.sessionId) {
            throw new Error('Invalid session data');
        }

        const { sessionId, messages } = sessionData;

        // Create new session
        this.createSessionMemory(sessionId);
        const messageHistory = this.messageHistories.get(sessionId);

        // Restore messages
        for (const msgData of messages) {
            let message;
            switch (msgData.type) {
                case 'human':
                    message = new HumanMessage(msgData.content);
                    break;
                case 'ai':
                    message = new AIMessage(msgData.content);
                    break;
                case 'system':
                    message = new SystemMessage(msgData.content);
                    break;
                default:
                    continue;
            }
            await messageHistory.addMessage(message);
        }

        this.logger.debug(`Imported session: ${sessionId} with ${messages.length} messages`);
    }
}

// Export singleton instance
export const langChainMemoryManager = new LangChainMemoryManager();

// Export class for custom instances
export default LangChainMemoryManager;