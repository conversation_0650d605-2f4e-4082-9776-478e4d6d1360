/**
 * Unified Route Registration System
 * 
 * This file consolidates all API and proxy route registration to make the system
 * more maintainable and avoid route conflicts or missing registrations.
 */

import { Router, Request, Response } from 'express';
import llmRouter from './llm';
import { setupAssetRoutes } from './assets';

// Import proxy functions
import { aliyunChatProxy } from '../middleware/proxy';

const router = Router();

/**
 * Register all API routes under /api prefix
 * This ensures consistent URL structure and easy maintenance
 */
function registerApiRoutes(router: Router): void {
    console.log('[Routes] Registering API routes...');

    // LLM API route (supports multimodal, audio, tools)
    router.use('/api/llm', llmRouter);

    // Aliyun chat proxy route (HTTP proxy for chat completions)
    router.post('/api/aliyun-chat', aliyunChatProxy);

    console.log('[Routes] API routes registered: /api/llm, /api/aliyun-chat');
}

/**
 * Register all proxy routes under /proxy prefix
 * This separates proxy endpoints from direct API endpoints
 */
function registerProxyRoutes(router: Router): void {
    console.log('[Routes] Registering proxy routes...');

    // Proxy status endpoint
    router.get('/proxy/status', (req: Request, res: Response) => {
        res.json({
            status: 'ok',
            timestamp: new Date().toISOString(),
            websocket_proxy: '/ws',
            available_proxies: [
                '/api/llm',
                '/api/aliyun-chat',
                '/ws'
            ]
        });
    });

    console.log('[Routes] Proxy routes registered: /proxy/status');
}

/**
 * Register asset and file management routes
 */
function registerAssetRoutes(router: Router): void {
    console.log('[Routes] Registering asset routes...');
    setupAssetRoutes(router);
    console.log('[Routes] Asset routes registered');
}

/**
 * Register backward compatibility routes
 * These routes ensure existing frontend code continues to work
 */
function registerCompatibilityRoutes(router: Router): void {
    console.log('[Routes] Registering compatibility routes...');

    // Legacy /llm route - redirect to /api/llm
    router.use('/llm', llmRouter);

    // Legacy /proxy-status route - redirect to /proxy/status
    router.get('/proxy-status', (req: Request, res: Response) => {
        res.json({
            status: 'ok',
            timestamp: new Date().toISOString(),
            websocket_proxy: '/ws',
            notice: 'This endpoint is deprecated. Use /proxy/status instead.'
        });
    });

    console.log('[Routes] Compatibility routes registered: /llm, /proxy-status');
}

/**
 * Register all routes in the correct order
 */
function registerAllRoutes(): Router {
    console.log('[Routes] Starting unified route registration...');

    // Register routes in order of specificity (most specific first)
    registerApiRoutes(router);
    registerProxyRoutes(router);
    registerAssetRoutes(router);
    registerCompatibilityRoutes(router);

    console.log('[Routes] All routes registered successfully');

    // Add a catch-all route for debugging unmatched requests - use a simpler pattern
    router.use((req: Request, res: Response) => {
        console.warn(`[Routes] Unmatched route: ${req.method} ${req.originalUrl}`);
        res.status(404).json({
            error: 'Route not found',
            method: req.method,
            path: req.originalUrl,
            available_endpoints: [
                'GET /proxy/status',
                'POST /api/llm',
                'POST /api/aliyun-chat',
                'GET /assets/meshes',
                'POST /models/upload',
                '... and more asset routes'
            ]
        });
    });

    return router;
}

// Export the configured router
export default registerAllRoutes(); 