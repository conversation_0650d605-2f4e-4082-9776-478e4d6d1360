import { Request, Response, RequestHandler } from 'express';
import path from 'path';
import fs from 'fs';
import { safeDecodeURIComponent, isPathSafe, normalizeChinesePath, isValidPath } from '../../utils/pathUtils';

/**
 * <PERSON><PERSON> for deleting a specific file
 */
export const deleteFileHandler: RequestHandler = async (req: Request, res: Response): Promise<void> => {
  try {
    // Get the file path from the query parameter
    const filePath = req.query.path as string;
    if (!filePath) {
      return res.status(400).json({ error: 'Missing file path parameter' });
    }

    // Normalize the file path using our enhanced utility function
    const normalizedFilePath = normalizeChinesePath(filePath);
    console.log(`[AssetRoutes] Deleting specific file: ${normalizedFilePath}`);

    // Validate path format to prevent directory traversal
    if (!isValidPath(normalizedFilePath)) {
      console.warn(`[AssetRoutes] Invalid path format for deletion: ${normalizedFilePath}`);

      // Try a more permissive approach for paths with Chinese characters
      if (/[\u4e00-\u9fff]/.test(normalizedFilePath)) {
        console.log(`[AssetRoutes] Path contains Chinese characters, proceeding with caution: ${normalizedFilePath}`);
      } else {
        return res.status(400).json({
          error: 'Invalid path format',
          details: 'Path must contain only letters, numbers, underscores, forward slashes, and valid Unicode characters'
        });
      }
    }

    // Construct the full path
    const fullPath = path.join(process.cwd(), 'public', normalizedFilePath);
    console.log(`[AssetRoutes] Full path to delete: ${fullPath}`);

    // Ensure path doesn't escape public directory
    const publicPath = path.join(process.cwd(), 'public');

    // Simple check to ensure path is within public directory
    const relativePath = fullPath.replace(publicPath, '');
    if (relativePath.includes('..') || !fullPath.startsWith(publicPath)) {
      console.warn(`[AssetRoutes] Path safety check failed: "${fullPath}" is outside "${publicPath}"`);
      return res.status(400).json({
        error: 'Invalid path',
        details: 'Path must be within public directory'
      });
    }

    console.log(`[AssetRoutes] Path safety check passed: "${fullPath}" is within "${publicPath}"`);

    // Check if file exists
    if (!fs.existsSync(fullPath)) {
      console.error(`[AssetRoutes] File does not exist: ${fullPath}`);
      return res.status(404).json({ error: 'File not found' });
    }

    // Delete the file
    await fs.promises.unlink(fullPath);
    console.log(`[AssetRoutes] Successfully deleted file: ${fullPath}`);

    res.json({
      success: true,
      path: normalizedFilePath
    });
  } catch (error) {
    console.error('[AssetRoutes] Error deleting file:', error);
    res.status(500).json({ error: 'Failed to delete file' });
  }
};
