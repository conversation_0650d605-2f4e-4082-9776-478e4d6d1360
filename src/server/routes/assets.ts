/**
 * Asset Routes
 * Handles routes for asset management (meshes, models, etc.)
 */

import express, { Request, Response, NextFunction, Router, RequestHandler } from 'express';
import path from 'path';
import fs from 'fs';
import multer from 'multer';
import axios from 'axios';
import { exec } from 'child_process';
import { promisify } from 'util';
import { config } from '@/config/client';

import { getEnvVar } from '../../config/env';
import { safeDecodeURIComponent, isValidPath, isPathSafe } from '../../utils/pathUtils';
import { createGeneralDirHandler } from './createDirHandler';
import { deleteFileHandler } from './deleteFileHandler';
import { listFilesHandler } from './listFilesHandler';



// Get supported mesh extensions from environment variable or use defaults
const getSupportedMeshExtensions = (): string[] => {
  const envExtensions = getEnvVar('VITE_SUPPORTED_MESH_EXTENSIONS', '.glb,.fbx');
  if (envExtensions) {
    return envExtensions.split(',').map(ext =>
      ext.trim().toLowerCase().startsWith('.') ? ext.trim().toLowerCase() : `.${ext.trim().toLowerCase()}`
    );
  }
  return ['.glb', '.fbx']; // Default extensions if not specified
};

// Define the unified download handler for assets and models
const downloadHandler: RequestHandler = async (req: Request, res: Response): Promise<void> => {
  // Handle both query parameters (GET) and request body (POST)
  const isPostRequest = req.method === 'POST';

  if (isPostRequest) {
    // Handle model downloads (POST requests with JSON body)
    const { url, targetDir, filename, isArchive = false } = req.body;

    if (!url || !targetDir) {
      res.status(400).json({
        error: 'Missing parameters',
        details: 'Both url and targetDir are required'
      });
      return;
    }

    // For single file downloads, filename is required
    if (!isArchive && !filename) {
      res.status(400).json({
        error: 'Missing parameters',
        details: 'filename is required for single file downloads'
      });
      return;
    }

    console.log(`[AssetRoutes] Download request for ${url} to ${targetDir}${filename ? '/' + filename : ''}`);

    // Create target directory
    const fullTargetDir = path.join(process.cwd(), 'public', targetDir);
    fs.mkdirSync(fullTargetDir, { recursive: true });

    if (isArchive) {
      // Handle archive download and extraction
      // Create a temporary directory for the download
      const tempDir = path.join(process.cwd(), 'temp');
      fs.mkdirSync(tempDir, { recursive: true });

      // Generate a unique filename for the download
      const archiveFilename = path.basename(url);
      const tempFilePath = path.join(tempDir, archiveFilename);

      console.log(`[AssetRoutes] Downloading archive to temporary file: ${tempFilePath}`);

      try {
        // Download the archive file
        console.log(`[AssetRoutes] Starting download from ${url}`);
        const response = await axios.get(url, {
          responseType: 'stream',
          timeout: 30000, // 30 second timeout
          headers: {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
          }
        });

        const writer = fs.createWriteStream(tempFilePath);

        await new Promise<void>((resolve, reject) => {
          response.data.pipe(writer);
          writer.on('finish', resolve);
          writer.on('error', (err) => {
            console.error(`[AssetRoutes] Error writing to file: ${err.message}`);
            reject(err);
          });
        });

        console.log(`[AssetRoutes] Download complete, extracting to ${fullTargetDir}`);

        // Extract the tar.bz2 file
        const execAsync = promisify(exec);
        await execAsync(`tar -xjf ${tempFilePath} -C ${fullTargetDir}`);

        // Clean up the temporary file
        fs.unlinkSync(tempFilePath);

        console.log(`[AssetRoutes] Extraction complete`);

        res.json({
          success: true,
          targetDir: targetDir
        });
      } catch (error) {
        const downloadError = error as Error;
        console.error(`[AssetRoutes] Error downloading or extracting archive: ${downloadError.message}`);
        throw error;
      }
    } else {
      // Handle single file download
      // Full path for the target file
      const targetFilePath = path.join(fullTargetDir, filename);

      console.log(`[AssetRoutes] Downloading to file: ${targetFilePath}`);

      try {
        console.log(`[AssetRoutes] Starting download from ${url}`);
        const response = await axios.get(url, {
          responseType: 'stream',
          timeout: 30000, // 30 second timeout
          headers: {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
          }
        });

        const writer = fs.createWriteStream(targetFilePath);

        await new Promise<void>((resolve, reject) => {
          response.data.pipe(writer);
          writer.on('finish', resolve);
          writer.on('error', (err) => {
            console.error(`[AssetRoutes] Error writing to file: ${err.message}`);
            reject(err);
          });
        });

        console.log(`[AssetRoutes] File download complete: ${targetFilePath}`);

        res.json({
          success: true,
          targetDir: targetDir,
          filename: filename
        });
      } catch (error) {
        const downloadError = error as Error;
        console.error(`[AssetRoutes] Error downloading file: ${downloadError.message}`);
        throw error;
      }
    }
  } else {
    // Handle asset downloads (GET requests with query parameters)
    console.log('\n=== Asset Download Request ===');
    console.log('Time:', new Date().toISOString());
    console.log('Raw URL:', req.url);
    console.log('Original URL:', req.originalUrl);
    console.log('Query parameters:', req.query);

    const url = req.query.url as string;
    const destPath = req.query.destPath as string;

    if (!url || !destPath) {
      console.warn('[AssetRoutes] Missing url or destPath parameter');
      res.status(400).json({ error: 'Missing url or destPath parameter' });
      return;
    }

    try {
      const absoluteDestPath = path.join(process.cwd(), config.downloadConfig.assetsDir, destPath);
      const dir = path.dirname(absoluteDestPath);

      fs.mkdirSync(dir, { recursive: true });

      console.log(`[AssetRoutes] Downloading from ${url} to ${absoluteDestPath}`);

      let fileData;
      if (url.startsWith('data:')) {
        // Handle data URL
        const matches = url.match(/^data:([A-Za-z-+/]+);base64,(.+)$/);
        if (!matches || matches.length !== 3) {
          throw new Error('Invalid data URL format');
        }
        const [, mimeType, base64Data] = matches;
        fileData = Buffer.from(base64Data, 'base64');
      } else {
        // Handle regular URL
        const response = await axios.get(url, { responseType: 'stream' });
        if (!response.status || response.status >= 400) {
          console.error(`[AssetRoutes] Failed to fetch the file. Status: ${response.status}`);
          res.status(500).json({ error: `Failed to fetch the file. Status: ${response.status}` });
          return;
        }
        fileData = response.data;
      }

      // Write the file
      if (fileData instanceof Buffer) {
        // For data URLs, write directly
        fs.writeFileSync(absoluteDestPath, fileData);
        console.log(`[AssetRoutes] File written successfully: ${absoluteDestPath}`);
        res.json({ success: true, path: destPath });
      } else {
        // For streams, pipe to file
        const fileStream = fs.createWriteStream(absoluteDestPath);
        fileData.pipe(fileStream);

        fileStream.on('finish', () => {
          console.log(`[AssetRoutes] Download completed successfully: ${absoluteDestPath}`);
          res.json({ success: true, path: destPath });
        });

        fileStream.on('error', (err) => {
          console.error('[AssetRoutes] Write stream error:', err);
          res.status(500).json({ error: 'Failed to write the file' });
        });
      }
    } catch (error) {
      console.error('[AssetRoutes] Download error:', error);
      res.status(500).json({ error: 'Failed to download the file' });
    }
  }
};

// Handler for listing mesh files
const getMeshListHandler: RequestHandler = async (req: Request, res: Response): Promise<void> => {
  try {
    console.log(`[AssetRoutes] Handling GET request for mesh list: ${req.url}`);

    const meshesDir = path.join(process.cwd(), 'public/assets/meshes');
    console.log(`[AssetRoutes] Looking for meshes in directory: ${meshesDir}`);

    // Check if directory exists
    if (!fs.existsSync(meshesDir)) {
      console.error(`[AssetRoutes] Meshes directory does not exist: ${meshesDir}`);
      fs.mkdirSync(meshesDir, { recursive: true });
      console.log(`[AssetRoutes] Created meshes directory: ${meshesDir}`);
      res.json([]);
      return;
    }

    const files = fs.readdirSync(meshesDir);
    console.log(`[AssetRoutes] Found ${files.length} files in meshes directory`);

    const supportedExtensions = getSupportedMeshExtensions();
    console.log('[AssetRoutes] Supported mesh extensions:', supportedExtensions);

    const meshFiles = files.filter(file => {
      const lowerFile = file.toLowerCase();
      return supportedExtensions.some(ext => lowerFile.endsWith(ext));
    });

    console.log(`[AssetRoutes] Returning ${meshFiles.length} mesh files:`, meshFiles);
    res.json(meshFiles);
  } catch (error) {
    console.error('[AssetRoutes] Error listing mesh files:', error);
    res.status(500).json({ error: 'Failed to list mesh files' });
  }
};

// Handler for deleting assets
const deleteAssetHandler: RequestHandler = async (req: Request, res: Response): Promise<void> => {
  const assetPath = req.query.path as string;

  if (!assetPath) {
    res.status(400).json({ error: 'Missing asset path parameter' });
    return;
  }

  try {
    const assetTypes: Record<'meshes' | 'images' | 'videos' | 'seeds' | 'audio', string> = {
      meshes: config.downloadConfig.assetsDir + '/meshes',
      images: config.downloadConfig.assetsDir + '/images',
      videos: config.downloadConfig.assetsDir + '/videos',
      seeds: config.downloadConfig.assetsDir + '/seeds',
      audio: config.downloadConfig.assetsDir + '/audio'
    };

    // Define supported extensions for each asset type
    const supportedFormats: Record<'meshes' | 'images' | 'videos' | 'seeds' | 'audio', string[]> = {
      meshes: getSupportedMeshExtensions(),
      images: ['.png', '.jpg', '.jpeg'],
      videos: ['.mp4', '.webm'],
      seeds: ['.seed', '.json'],
      audio: ['.mp3', '.wav', '.ogg']
    };

    // Log supported formats for debugging
    console.log('[AssetRoutes] Supported asset formats:', supportedFormats);

    // Extract base name without extension for better matching
    const baseName = assetPath.replace(/\.[^/.]+$/, ''); // Remove extension if present
    console.log(`[AssetRoutes] Deleting assets with base name: ${baseName}`);

    // Track all deleted files
    const deletedFiles: string[] = [];

    // Delete regular asset files (mesh, image, video, seeds)
    const deletePromises = Object.entries(assetTypes).map(async ([type, dirPath]) => {
      const baseDir = path.join(process.cwd(), dirPath);
      if (!fs.existsSync(baseDir)) return;

      // Special handling for audio directory - check subdirectories too
      if (type === 'audio') {
        // First check the main audio directory
        await deleteMatchingFiles(baseDir, baseName, type, deletedFiles);

        // Then check all subdirectories in the audio folder
        try {
          const subdirs = await fs.promises.readdir(baseDir, { withFileTypes: true });
          for (const dirent of subdirs) {
            if (dirent.isDirectory()) {
              const subdirPath = path.join(baseDir, dirent.name);
              console.log(`[AssetRoutes] Checking audio subdirectory: ${subdirPath}`);
              await deleteMatchingFiles(subdirPath, baseName, `audio/${dirent.name}`, deletedFiles);
            }
          }
        } catch (err) {
          console.error(`[AssetRoutes] Error reading audio subdirectories:`, err);
        }
      } else {
        // Normal handling for other asset types
        await deleteMatchingFiles(baseDir, baseName, type, deletedFiles);
      }
    });

    await Promise.all(deletePromises);

    res.json({
      success: true,
      deleted: deletedFiles.length,
      files: deletedFiles.map(f => path.basename(f))
    });
  } catch (error) {
    console.error('[AssetRoutes] Delete asset error:', error);
    res.status(500).json({ error: 'Failed to delete asset' });
  }
};

// Helper function to delete matching files in a directory
async function deleteMatchingFiles(
  directory: string,
  baseName: string,
  assetType: string,
  deletedFiles: string[]
): Promise<void> {
  try {
    // Get all files in the directory
    const files = await fs.promises.readdir(directory);

    // For each file, check if it matches our baseName
    for (const file of files) {
      // Check if file matches the base name (either exact or contains)
      // Remove any extension for comparison
      const fileBaseName = file.replace(/\.[^/.]+$/, '');

      // Match if the file starts with the baseName or the baseName is in the file
      if (fileBaseName === baseName ||
        fileBaseName.startsWith(baseName + '_') ||
        fileBaseName.includes(`_${baseName}`) ||
        (assetType === 'seeds' && fileBaseName === baseName)) {

        const filePath = path.join(directory, file);
        try {
          await fs.promises.unlink(filePath);
          deletedFiles.push(filePath);
          console.log(`[AssetRoutes] Deleted ${assetType} file: ${filePath}`);
        } catch (err) {
          console.error(`[AssetRoutes] Error deleting ${filePath}:`, err);
        }
      }
    }
  } catch (err) {
    console.error(`[AssetRoutes] Error reading directory ${directory}:`, err);
  }
}

// Handler for serving model files
const serveModelHandler: RequestHandler = async (req: Request, res: Response): Promise<void> => {
  try {
    const modelName = req.query.model as string;
    if (!modelName) {
      res.status(400).json({ error: 'Missing model parameter' });
      return;
    }

    const modelPath = path.join(process.cwd(), 'public/models', modelName);
    if (fs.existsSync(modelPath)) {
      res.sendFile(modelPath);
    } else {
      res.status(404).json({ error: 'Model not found' });
    }
  } catch (error) {
    console.error('[AssetRoutes] Error serving model:', error);
    res.status(500).json({ error: 'Failed to serve model' });
  }
};

// Configure multer for model uploads with absolute path and subdirectories
const uploadModel = multer({
  storage: multer.diskStorage({
    destination: (req, file, cb) => {
      try {
        // Extract path from form field, not body
        console.log('[AssetRoutes] Multer processing file:', {
          fields: req.body,
          file: file.originalname
        });

        // Get path from the request - important to handle timing
        const getPath = () => {
          if (req.body && req.body.path) {
            return req.body.path;
          }
          // Default to mediapipe if no path specified
          return 'mediapipe';
        };

        const subdir = getPath();
        console.log('[AssetRoutes] Using directory:', subdir);

        // Clean and validate the path
        const cleanPath = subdir.replace(/^\/+|\/+$/g, '');
        const modelPath = path.join(process.cwd(), 'public/models', cleanPath);

        // Create directory if it doesn't exist
        fs.mkdirSync(modelPath, { recursive: true });
        console.log('[AssetRoutes] Storing model in:', modelPath);

        cb(null, modelPath);
      } catch (error) {
        console.error('[AssetRoutes] Error in multer destination:', error);
        cb(error as Error, '');
      }
    },
    filename: (_req, file, cb) => {
      cb(null, file.originalname);
    }
  }),
  limits: {
    fileSize: 50 * 1024 * 1024, // 50MB limit
    fields: 10,
    files: 1
  }
});

// Handler for creating directories
const createDirHandler: RequestHandler = async (req, res): Promise<void> => {
  try {
    const createDir = typeof req.body.createDir === 'boolean' ? req.body.createDir : true;
    const targetPath = req.query.path as string || req.body.path;  // Get the path from query or body

    // Decode the path using our utility function
    const decodedTargetPath = safeDecodeURIComponent(targetPath);

    console.log('[AssetRoutes] Create directory request:', {
      createDir,
      targetPath,
      decodedTargetPath,
      rawBody: req.body,
      createDirType: typeof req.body?.createDir
    });

    // Skip directory creation for upload endpoint
    if (decodedTargetPath === 'upload') {
      res.status(400).json({
        error: 'Invalid request',
        details: 'Use POST /models/upload for file uploads'
      });
      return;
    }

    // Validate path format
    // Allow Chinese characters and other Unicode characters
    if (!decodedTargetPath || !decodedTargetPath.match(/^[a-zA-Z0-9_/\u4e00-\u9fff\-]+$/)) {
      res.status(400).json({
        error: 'Invalid path format',
        details: 'Path must contain only letters, numbers, underscores, forward slashes, and valid Unicode characters'
      });
      return;
    }

    const dirPath = path.join(process.cwd(), 'public/models', decodedTargetPath);

    // Ensure path doesn't escape public/models directory
    const normalizedPath = path.normalize(dirPath);
    if (!normalizedPath.startsWith(path.join(process.cwd(), 'public/models'))) {
      res.status(400).json({
        error: 'Invalid path',
        details: 'Path must be within models directory'
      });
      return;
    }

    fs.mkdirSync(dirPath, { recursive: true });
    console.log(`[AssetRoutes] Created directory: ${dirPath}`);
    res.json({
      success: true,
      path: decodedTargetPath
    });
  } catch (error) {
    console.error('[AssetRoutes] Error creating model directory:', error);
    res.status(500).json({
      error: 'Failed to create directory',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Handler for uploading models
const uploadModelHandler: RequestHandler = (req, res) => {
  console.log('[AssetRoutes] Upload request received');
  console.log('[AssetRoutes] Request body:', req.body);

  uploadModel.single('model')(req, res, async (err) => {
    console.log('[AssetRoutes] Processing upload with multer');

    if (err) {
      console.error('[AssetRoutes] Multer error:', err);
      if (err instanceof multer.MulterError) {
        if (err.code === 'LIMIT_FILE_SIZE') {
          return res.status(400).json({
            error: 'File too large',
            details: 'Maximum file size is 50MB'
          });
        }
        return res.status(400).json({
          error: 'Upload error',
          details: err.message
        });
      }
      return res.status(500).json({
        error: 'Server error',
        details: err.message
      });
    }

    try {
      console.log('[AssetRoutes] Upload body:', req.body);
      console.log('[AssetRoutes] Upload file:', req.file);

      if (!req.file) {
        return res.status(400).json({
          error: 'Missing file',
          details: 'No file was uploaded'
        });
      }

      // Validate path
      const modelDir = req.body.path;
      console.log('[AssetRoutes] Model directory:', modelDir);

      if (!modelDir || !modelDir.match(/^[a-zA-Z0-9_-]+(\/[a-zA-Z0-9_-]+)*$/)) {
        return res.status(400).json({
          error: 'Invalid path',
          details: 'Path must contain only letters, numbers, underscores, hyphens, and forward slashes'
        });
      }

      console.log('[AssetRoutes] Model upload successful:', {
        filename: req.file.filename,
        destination: req.file.destination,
        path: modelDir,
        size: req.file.size
      });

      res.json({
        success: true,
        filename: req.file.filename,
        path: modelDir
      });
    } catch (error) {
      console.error('[AssetRoutes] Upload processing error:', error);
      res.status(500).json({
        error: 'Upload failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });
};

// Configure multer for general file uploads
const upload = multer({
  storage: multer.diskStorage({
    destination: (req, file, cb) => {
      try {
        console.log('[AssetRoutes] Multer destination called with file:', {
          fieldname: file.fieldname,
          originalname: file.originalname,
          mimetype: file.mimetype,
          size: file.size || 'unknown'
        });

        // Get path from the request - use a more robust approach
        let subdir = 'assets';

        // First try to get from the body
        if (req.body && req.body.path) {
          subdir = req.body.path;
          console.log('[AssetRoutes] Got path from request body:', subdir);
        }
        // Then try to get from the query parameters
        else if (req.query && req.query.path) {
          subdir = req.query.path as string;
          console.log('[AssetRoutes] Got path from query params:', subdir);
        }
        // Finally, check if it's in the URL
        else if (req.params && req.params.path) {
          subdir = req.params.path;
          console.log('[AssetRoutes] Got path from URL params:', subdir);
        }

        console.log('[AssetRoutes] Using directory:', subdir);

        // Clean and validate the path
        const cleanPath = subdir.replace(/^\/+|\/+$/g, '');
        const uploadPath = path.join(process.cwd(), 'public', cleanPath);

        // Create directory if it doesn't exist
        fs.mkdirSync(uploadPath, { recursive: true });
        console.log('[AssetRoutes] Storing file in:', uploadPath);

        cb(null, uploadPath);
      } catch (error) {
        console.error('[AssetRoutes] Error in multer destination:', error);
        cb(error as Error, '');
      }
    },
    filename: (req, file, cb) => {
      try {
        console.log('[AssetRoutes] Multer filename called with file:', {
          fieldname: file.fieldname,
          originalname: file.originalname,
          mimetype: file.mimetype
        });

        // Use name from form data if provided, otherwise use original filename
        let filename = file.originalname || `${Date.now()}-${Math.random().toString(36).substring(7)}`;

        if (req.body && req.body.name) {
          filename = req.body.name;
          console.log('[AssetRoutes] Using filename from request body:', filename);
        }

        console.log('[AssetRoutes] Using filename:', filename);
        cb(null, filename);
      } catch (error) {
        console.error('[AssetRoutes] Error in multer filename:', error);
        cb(error as Error, '');
      }
    }
  }),
  limits: {
    fileSize: 100 * 1024 * 1024, // 100MB limit (increased from 50MB)
    fieldSize: 100 * 1024 * 1024, // 100MB field size limit
    fields: 10,
    files: 1,
    parts: 20 // Increased parts limit
  }
});

// Handler for general file uploads
const uploadHandler: RequestHandler = (req, res) => {
  console.log('[AssetRoutes] Upload request received');
  console.log('[AssetRoutes] Request headers:', {
    'content-type': req.headers['content-type'],
    'content-length': req.headers['content-length'],
    'accept': req.headers['accept']
  });

  // Set higher timeout for large uploads
  req.setTimeout(300000); // 5 minutes
  res.setTimeout(300000); // 5 minutes

  // Use multer to handle the file upload
  upload.single('file')(req, res, async (err) => {
    console.log('[AssetRoutes] Processing upload with multer');

    if (err) {
      console.error('[AssetRoutes] Multer error:', err);
      if (err instanceof multer.MulterError) {
        if (err.code === 'LIMIT_FILE_SIZE') {
          return res.status(400).json({
            error: 'File too large',
            details: 'Maximum file size is 100MB'
          });
        } else if (err.code === 'LIMIT_UNEXPECTED_FILE') {
          return res.status(400).json({
            error: 'Unexpected file',
            details: 'Make sure the file field name is "file"'
          });
        } else if (err.code === 'LIMIT_PART_COUNT') {
          return res.status(400).json({
            error: 'Too many parts',
            details: 'The multipart form has too many parts'
          });
        } else if (err.code === 'LIMIT_FIELD_KEY') {
          return res.status(400).json({
            error: 'Field name too long',
            details: 'A field name is too long'
          });
        }
        return res.status(400).json({
          error: 'Upload error',
          details: `Multer error: ${err.code} - ${err.message}`
        });
      }
      return res.status(500).json({
        error: 'Server error',
        details: err.message
      });
    }

    try {
      // Log file details if available
      if (req.file) {
        console.log('[AssetRoutes] Upload file details:', {
          filename: req.file.filename,
          originalname: req.file.originalname,
          destination: req.file.destination,
          size: req.file.size,
          mimetype: req.file.mimetype
        });
      } else {
        console.warn('[AssetRoutes] No file in request after multer processing');
      }

      if (!req.file) {
        return res.status(400).json({
          error: 'Missing file',
          details: 'No file was uploaded or file was not properly processed'
        });
      }

      // Get the path from the request body or query
      let uploadPath = req.body?.path;

      if (!uploadPath && req.query?.path) {
        uploadPath = req.query.path as string;
      }

      console.log('[AssetRoutes] Upload path:', uploadPath);

      if (!uploadPath) {
        // Try to extract path from the destination
        const destPath = req.file.destination;
        if (destPath) {
          const publicDir = path.join(process.cwd(), 'public');
          if (destPath.startsWith(publicDir)) {
            uploadPath = destPath.substring(publicDir.length + 1);
            console.log('[AssetRoutes] Extracted path from destination:', uploadPath);
          }
        }

        if (!uploadPath) {
          return res.status(400).json({
            error: 'Missing path',
            details: 'No upload path specified'
          });
        }
      }

      // Clean up the path
      const cleanPath = uploadPath.replace(/^\/+|\/+$/g, '');

      // Validate path format - more permissive to allow audio subdirectories and Chinese characters
      if (!cleanPath.match(/^[a-zA-Z0-9_\u4e00-\u9fff-]+(\/[a-zA-Z0-9_\u4e00-\u9fff-]+)*$/)) {
        console.warn('[AssetRoutes] Path contains non-standard characters:', cleanPath);
        // Continue anyway but log the warning
      }

      // Ensure the directory exists before uploading
      try {
        const dirPath = path.join(process.cwd(), 'public', cleanPath.split('/').slice(0, -1).join('/'));
        console.log(`[AssetRoutes] Ensuring directory exists: ${dirPath}`);
        fs.mkdirSync(dirPath, { recursive: true });
      } catch (dirError) {
        console.error('[AssetRoutes] Error creating directory:', dirError);
        // Continue anyway, the upload might still work
      }

      console.log('[AssetRoutes] File upload successful:', {
        filename: req.file.filename,
        destination: req.file.destination,
        path: cleanPath,
        size: req.file.size,
        mimetype: req.file.mimetype
      });

      res.json({
        success: true,
        filename: req.file.filename,
        path: cleanPath
      });
    } catch (error) {
      console.error('[AssetRoutes] Upload processing error:', error);
      res.status(500).json({
        error: 'Upload failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });
};

// Handler for listing files in a directory is now imported from './listFilesHandler'

// Handler for creating any directory in public folder is now imported from './createDirHandler'

// Handler for deleting a specific file is now imported from './deleteFileHandler'

/**
 * Set up asset routes
 * @param router Express router
 */
export function setupAssetRoutes(router: Router): void {
  // Asset routes
  router.get('/assets/meshes', getMeshListHandler);
  router.get('/assets/meshes/', getMeshListHandler); // Add explicit trailing slash version
  router.get('/download', downloadHandler);

  // Use query parameters instead of path parameters for complex paths
  router.delete('/delete-asset', deleteAssetHandler);

  // Add route for listing files in a directory
  router.get('/list-files', listFilesHandler);

  // Add route for creating directories in public folder
  router.post('/create-dir', createGeneralDirHandler);

  // Add route for deleting a specific file
  router.delete('/delete-file', deleteFileHandler);

  // Model routes - be more specific to avoid conflicts
  router.post('/models/download', express.json(), downloadHandler);
  router.post('/models/download-extract', express.json(), (req: Request, res: Response, next: NextFunction) => {
    // Add isArchive flag to the request body
    req.body.isArchive = true;
    downloadHandler(req, res, next);
  });
  router.post('/models/download-file', express.json(), downloadHandler);
  router.post('/models/upload', uploadModelHandler);

  // Create directory route - use query parameters instead of path parameters
  router.post('/models/create-dir', express.json(), createDirHandler);

  // Serve model files - use query parameters for safety
  router.get('/models/serve', serveModelHandler);

  // General upload route
  router.post('/upload', uploadHandler);
}
