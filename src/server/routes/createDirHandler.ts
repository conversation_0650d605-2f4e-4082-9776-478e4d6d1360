import { Request, Response, RequestHandler } from 'express';
import path from 'path';
import fs from 'fs';
import { safeDecodeURIComponent, isValidPath, isPathSafe, normalizeChinesePath } from '../../utils/pathUtils';

/**
 * <PERSON><PERSON> for creating any directory in public folder
 */
export const createGeneralDirHandler: RequestHandler = async (req: Request, res: Response): Promise<void> => {
  try {
    // Get the directory path from the query parameter
    const dirPath = req.query.dir as string;
    if (!dirPath) {
      return res.status(400).json({ error: 'Missing directory path parameter' });
    }

    // Normalize the directory path using our enhanced utility function
    const normalizedDirPath = normalizeChinesePath(dirPath);
    console.log(`[AssetRoutes] Creating directory: ${normalizedDirPath}`);

    // Validate path format to prevent directory traversal
    if (!isValidPath(normalizedDirPath)) {
      console.warn(`[AssetRoutes] Invalid path format: ${normalizedDirPath}`);

      // Try a more permissive approach for paths with Chinese characters
      if (/[\u4e00-\u9fff]/.test(normalizedDirPath)) {
        console.log(`[AssetRoutes] Path contains Chinese characters, proceeding with caution: ${normalizedDirPath}`);
      } else {
        return res.status(400).json({
          error: 'Invalid path format',
          details: 'Path must contain only letters, numbers, underscores, forward slashes, and valid Unicode characters'
        });
      }
    }

    const fullPath = path.join(process.cwd(), 'public', normalizedDirPath);
    console.log(`[AssetRoutes] Full path: ${fullPath}`);

    // Ensure path doesn't escape public directory
    const publicPath = path.join(process.cwd(), 'public');

    // Simple check to ensure path is within public directory
    const relativePath = fullPath.replace(publicPath, '');
    if (relativePath.includes('..') || !fullPath.startsWith(publicPath)) {
      console.warn(`[AssetRoutes] Path safety check failed: "${fullPath}" is outside "${publicPath}"`);
      return res.status(400).json({
        error: 'Invalid path',
        details: 'Path must be within public directory'
      });
    }

    console.log(`[AssetRoutes] Path safety check passed: "${fullPath}" is within "${publicPath}"`);

    // Create directory
    fs.mkdirSync(fullPath, { recursive: true });
    console.log(`[AssetRoutes] Created directory: ${fullPath}`);

    res.json({
      success: true,
      path: normalizedDirPath
    });
  } catch (error) {
    console.error('[AssetRoutes] Error creating directory:', error);
    res.status(500).json({ error: 'Failed to create directory' });
  }
};
