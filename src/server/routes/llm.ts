// @ts-nocheck
import { Router, Request, Response } from 'express';
import { HumanMessage, AIMessage, SystemMessage } from '@langchain/core/messages';

const router = Router();

router.post('/', async (req: Request, res: Response) => {
    try {
        // Clone and sanitize the incoming request body to avoid logging input_audio
        const sanitizedBody = JSON.parse(JSON.stringify(req.body));

        // Redact top-level input_audio if present
        if (sanitizedBody.input_audio) {
            sanitizedBody.input_audio = '[REDACTED]';
        }

        // Redact input_audio in messages array if present
        if (Array.isArray(sanitizedBody.messages)) {
            sanitizedBody.messages = sanitizedBody.messages.map((msg: any) => {
                if (msg && msg.input_audio) {
                    return { ...msg, input_audio: '[REDACTED]' };
                }

                // Handle nested content arrays with input_audio
                if (msg && Array.isArray(msg.content)) {
                    return {
                        ...msg,
                        content: msg.content.map((contentItem: any) => {
                            if (contentItem && contentItem.input_audio) {
                                return { ...contentItem, input_audio: '[REDACTED]' };
                            }
                            return contentItem;
                        })
                    };
                }

                return msg;
            });
        }

        console.log('🔍 [LLM Route] Sanitized request body:', JSON.stringify(sanitizedBody, null, 2));

        // Extract and validate request parameters
        const { provider, model, messages, modalities, audioConfig, tools, tool_choice, toolChoice, ...otherOptions } = req.body;

        // CRITICAL: Validate required parameters
        if (!provider) {
            return res.status(400).json({
                error: 'Missing required parameter: provider',
                details: 'Request must include a provider field (e.g., "aliyun")'
            });
        }

        if (!model) {
            return res.status(400).json({
                error: 'Missing required parameter: model',
                details: 'Request must include a model field (e.g., "qwen-omni-turbo")'
            });
        }

        // CRITICAL: Validate messages array
        if (!messages) {
            return res.status(400).json({
                error: 'Missing required parameter: messages',
                details: 'Request must include a messages array with at least one message'
            });
        }

        if (!Array.isArray(messages)) {
            return res.status(400).json({
                error: 'Invalid messages parameter: must be an array',
                details: 'The messages field must be an array of message objects'
            });
        }

        if (messages.length === 0) {
            return res.status(400).json({
                error: 'Empty messages array',
                details: 'The messages array must contain at least one message. Aliyun API requires at least one user message.'
            });
        }

        // Validate that we have at least one valid message with content
        const validMessages = messages.filter((msg: any) => {
            return msg && (
                (typeof msg.content === 'string' && msg.content.trim().length > 0) ||
                (Array.isArray(msg.content) && msg.content.length > 0) ||
                msg.input_audio
            );
        });

        if (validMessages.length === 0) {
            return res.status(400).json({
                error: 'No valid messages found',
                details: 'All messages must have non-empty content or input_audio. Please provide at least one message with text or audio content.'
            });
        }

        console.log(`✅ [LLM Route] Validation passed: ${validMessages.length} valid messages out of ${messages.length} total`);

        // Log tool information if present
        if (tools && Array.isArray(tools) && tools.length > 0) {
            console.log(`🔧 [LLM Route] Tools detected: ${tools.length} tool(s) - ${tools.map(t => t.name).join(', ')}`);
            console.log(`🎯 [LLM Route] Tool choice: ${tool_choice || toolChoice || 'auto'}`);
        }

        // Convert messages to LangChain format for the model
        const langchainMessages = messages.map((msg: any) => {
            const role = msg.role;
            const content = msg.content;

            // Create appropriate LangChain message type
            if (role === 'system') {
                return new SystemMessage({ content });
            } else if (role === 'assistant') {
                return new AIMessage({ content });
            } else {
                // Default to HumanMessage for 'user' and any other roles
                const humanMsg = new HumanMessage({ content });

                // Preserve extra modalities and input_audio
                if (msg.input_audio) {
                    humanMsg.input_audio = msg.input_audio;
                }

                // Extract multimodal content for extraModalities
                if (Array.isArray(content)) {
                    const extraModalities: any = {};

                    content.forEach((item: any) => {
                        if (item.type === 'image_url') {
                            if (!extraModalities.images) extraModalities.images = [];
                            extraModalities.images.push(item.image_url.url);
                        } else if (item.type === 'audio_url') {
                            if (!extraModalities.audios) extraModalities.audios = [];
                            extraModalities.audios.push(item.audio_url.url);
                        }
                    });

                    if (Object.keys(extraModalities).length > 0) {
                        humanMsg.extraModalities = extraModalities;
                    }

                    // Extract text content
                    const textContent = content
                        .filter((item: any) => item.type === 'text')
                        .map((item: any) => item.text)
                        .join(' ');

                    if (textContent) {
                        humanMsg.content = textContent;
                    }
                }

                return humanMsg;
            }
        });

        console.log(`🔄 [LLM Route] Converted to ${langchainMessages.length} LangChain messages`);

        // Initialize model based on provider
        let model_instance: any;

        if (provider === 'aliyun') {
            const { AliyunBailianChatModel } = await import('../../agent/models/AliyunBailianChatModel.js');
            // @ts-ignore - AliyunBailianChatModel is a JavaScript class without type definitions
            model_instance = new AliyunBailianChatModel({
                model: model || 'qwen-omni-turbo-realtime',
                // Let the model determine apiMode based on model name
                // apiMode: determined automatically in constructor
                apiKey: process.env.VITE_DASHSCOPE_API_KEY || '',
                modalities: modalities || ['text'],
                audioConfig: audioConfig || { voice: 'Chelsie', format: 'wav' },
                enablePrompts: true, // Enable prompt integration
                language: otherOptions.language || 'english',
                gender: otherOptions.gender || null,
                mood: otherOptions.mood || 'neutral'
            } as any);
        } else {
            return res.status(400).json({
                error: 'Unsupported provider',
                details: `Provider "${provider}" is not supported. Currently supported providers: aliyun`
            });
        }

        // Helper function to convert custom tool format to OpenAI-compatible format
        const convertToolsToOpenAIFormat = (tools) => {
            if (!tools || !Array.isArray(tools)) return tools;

            return tools.map(tool => {
                // If already in OpenAI format (has type: 'function'), return as-is
                if (tool.type === 'function' && tool.function) {
                    return tool;
                }

                // Convert custom format to OpenAI format
                if (tool.name && tool.schema) {
                    return {
                        type: 'function',
                        function: {
                            name: tool.name,
                            description: tool.description || '',
                            parameters: tool.schema
                        }
                    };
                }

                // If neither format, return as-is (might be another valid format)
                return tool;
            });
        };

        // Prepare options for the model
        const modelOptions = {
            modalities: modalities || ['text'],
            audioConfig: audioConfig || { voice: 'Chelsie', format: 'wav' },
            // Include tool definitions if provided, converting to OpenAI format
            ...(tools && Array.isArray(tools) && tools.length > 0 && {
                tools: convertToolsToOpenAIFormat(tools),
                tool_choice: tool_choice || toolChoice || 'auto'
            }),
            ...otherOptions
        };

        console.log('🚀 [LLM Route] Calling model with options:', JSON.stringify(modelOptions, null, 2));
        console.log(`📨 [LLM Route] Sending ${langchainMessages.length} LangChain messages to ${provider} ${model}`);

        // Call the model
        const result = await model_instance.invoke(langchainMessages, modelOptions);

        // Transform response to expected format (updated for new model structure)
        const generation = result.generations?.[0];
        const response = {
            content: generation?.text || generation?.message?.content || '',
            audio: generation?.audio || null,
            message: generation?.message || null,
            metadata: {
                model: model || 'qwen-omni-turbo-realtime',
                provider,
                modalities: modelOptions.modalities,
                audioConfig: modelOptions.audioConfig,
                messageCount: langchainMessages.length,
                originalMessageCount: messages.length,
                usage: generation?.usage || null
            }
        };

        console.log('✅ [LLM Route] Response generated:', {
            hasContent: !!response.content,
            hasAudio: !!response.audio,
            audioLength: response.audio ? response.audio.length : 0,
            audioPreview: response.audio ? response.audio.slice(0, 100) : '',
            contentLength: response.content ? response.content.length : 0,
            hasUsage: !!response.metadata.usage
        });

        res.json(response);

    } catch (error: any) {
        console.error('❌ [LLM Route] Error:', error);

        // Enhanced error handling for common Aliyun API errors
        let errorMessage = error.message;
        let errorDetails = undefined;
        let statusCode = 500;

        if (error.message?.includes('is too short - \'messages\'')) {
            errorMessage = 'Empty or invalid messages array';
            errorDetails = 'The Aliyun API requires at least one valid message with content. Please ensure your messages array contains at least one message with text content.';
            statusCode = 400;
        } else if (error.message?.includes('API key')) {
            errorMessage = 'API authentication failed';
            errorDetails = 'Please check that VITE_DASHSCOPE_API_KEY is properly configured.';
            statusCode = 401;
        } else if (error.message?.includes('BadRequestError')) {
            statusCode = 400;
            errorDetails = 'The request format is invalid. Please check the message format and parameters.';
        } else if (error.message?.includes('rate limit') || error.message?.includes('quota')) {
            statusCode = 429;
            errorDetails = 'API rate limit exceeded. Please try again later.';
        }

        res.status(statusCode).json({
            error: errorMessage,
            details: errorDetails,
            stack: process.env.NODE_ENV === 'development' ? error.stack : undefined,
            timestamp: new Date().toISOString(),
            requestId: error.request_id || 'unknown'
        });
    }
});

export default router; 