/**
 * Proxy Middleware - Consolidated
 * 
 * This file now focuses solely on WebSocket proxy functionality.
 * HTTP route proxying is handled by the unified route system.
 */

import { Request, Response } from 'express';
import fetch from 'node-fetch';

// -----------------------------------------------------------------------------
// Aliyun Realtime WebSocket Proxy Integration
// -----------------------------------------------------------------------------
// <PERSON><PERSON><PERSON> cannot set the `Authorization` header in the WebSocket handshake, but
// Aliyun DashScope requires it.  Instead of running a standalone proxy script
// we expose the same functionality through the existing Express download
// server.  The proxy is mounted on the same HTTP server, re-using its port so
// the frontend can connect to `ws://<host>:<port>/ws?model=<MODEL>` without
// leaking the API key.

import type http from 'http';
import { WebSocketServer } from 'ws';
import WebSocket from 'ws';

/**
 * Attach Aliyun realtime WebSocket proxy to an existing HTTP server instance.
 * If the required API key is missing the proxy will not start.
 *
 * @param server Existing HTTP/S server returned by `http.createServer()`
 * @param path   WebSocket path to mount the proxy on. Default: `/ws`.
 */
export function attachAliyunRealtimeProxy(server: http.Server, path: string = '/ws') {
  // Check for API key from multiple environment variables for compatibility
  const API_KEY = process.env.VITE_DASHSCOPE_API_KEY || process.env.VITE_ALIYUN_API_KEY;
  if (!API_KEY) {
    console.warn('[AliyunProxy] ❌ Environment variable VITE_DASHSCOPE_API_KEY or VITE_ALIYUN_API_KEY is not set – realtime proxy disabled');
    return;
  }


  const DEFAULT_MODEL = 'qwen-omni-turbo-realtime';
  const upstreamEndpoint = (model: string) =>
    `wss://dashscope.aliyuncs.com/api-ws/v1/realtime?model=${encodeURIComponent(model)}`;

  const wss = new WebSocketServer({ server, path });
  console.log(`[AliyunProxy] 🚀 WebSocket proxy listening on ws://<host>:<port>${path}`);

  wss.on('connection', (client, req) => {
    const requestUrl = new URL(req.url || '', `http://${req.headers.host}`);
    const model = requestUrl.searchParams.get('model') || DEFAULT_MODEL;

    console.log(`[AliyunProxy] 🔌 Client connected – model: ${model}`);

    // Validate API key before creating upstream connection
    if (!API_KEY || API_KEY.length < 10) {
      console.error('[AliyunProxy] ❌ Invalid or missing API key');
      client.close(1008, 'Invalid API key configuration');
      return;
    }

    // Connection state management
    let currentUpstream: WebSocket | null = null;
    let connectionAttempts = 0;
    const maxRetries = 3;
    let retryTimer: NodeJS.Timeout | null = null;
    let isUpstreamReady = false;
    let messageQueue: string[] = [];

    const createUpstreamConnection = () => {
      connectionAttempts++;
      console.log(`[AliyunProxy] 🔗 Connecting to upstream (attempt ${connectionAttempts}/${maxRetries}): ${upstreamEndpoint(model)}`);

      const upstream = new WebSocket(upstreamEndpoint(model), {
        headers: {
          'Authorization': `Bearer ${API_KEY}`,
          'User-Agent': 'HologramSoftware-Proxy/1.0'
        },
        // Add connection timeout
        handshakeTimeout: 10000
      });

      currentUpstream = upstream;

      // Set up upstream event handlers
      upstream.on('open', () => {
        console.log(`[AliyunProxy] ✅ Upstream connection established for model: ${model}`);
        connectionAttempts = 0; // Reset attempts on successful connection
        isUpstreamReady = true;

        if (retryTimer) {
          clearTimeout(retryTimer);
          retryTimer = null;
        }

        // Send any queued messages
        while (messageQueue.length > 0 && upstream.readyState === WebSocket.OPEN) {
          const queuedMessage = messageQueue.shift();
          if (queuedMessage) {
            console.log(`[AliyunProxy] 📤 Sending queued message`);
            upstream.send(queuedMessage);
          }
        }
      });

      upstream.on('error', (err) => {
        console.error(`[AliyunProxy] ❌ Upstream connection failed (attempt ${connectionAttempts}):`, err.message);

        if (connectionAttempts < maxRetries && client.readyState === WebSocket.OPEN) {
          console.log(`[AliyunProxy] 🔄 Retrying connection in 2 seconds...`);
          retryTimer = setTimeout(() => {
            if (client.readyState === WebSocket.OPEN) {
              createUpstreamConnection();
            }
          }, 2000);
        } else {
          console.error('[AliyunProxy] 🔧 Max retries reached. Check API key and network connectivity');
          if (client.readyState === WebSocket.OPEN) {
            client.close(1011, 'Upstream connection failed after retries');
          }
        }
      });

      upstream.on('close', (code, reason) => {
        const reasonStr = reason.toString();

        // Enhanced logging for 1011 errors (internal server error)
        if (code === 1011) {
          console.error(`[AliyunProxy] ❌ 1011 Internal Server Error - likely rate limit violation:`, {
            code,
            reason: reasonStr,
            wasCurrentUpstream: upstream === currentUpstream,
            clientReadyState: client.readyState,
            connectionAttempts,
            maxRetries,
            messageQueueLength: messageQueue.length,
            troubleshooting: 'Check audio chunk rate (max 8/sec), API key validity, and network connectivity'
          });
        } else {
          console.log(`[AliyunProxy] 🔒 Upstream closed: ${code} ${reasonStr}`, {
            code,
            reason: reasonStr,
            wasCurrentUpstream: upstream === currentUpstream,
            clientReadyState: client.readyState,
            connectionAttempts,
            maxRetries,
            messageQueueLength: messageQueue.length
          });
        }
        isUpstreamReady = false;

        // Only close client if this was the current upstream and connection was established
        if (upstream === currentUpstream && client.readyState === WebSocket.OPEN) {
          // If this was a normal close or we've exhausted retries, close the client
          if (code !== 1006 || connectionAttempts >= maxRetries) {
            console.log(`[AliyunProxy] Closing client connection due to upstream close`);
            client.close(code, reasonStr);
          }
        }

        if (upstream === currentUpstream) {
          currentUpstream = null;
        }
      });

      upstream.on('message', (data) => {
        try {
          // Parse and log message for debugging (avoid logging audio data)
          const messageStr = data.toString();
          let messageObj;
          try {
            messageObj = JSON.parse(messageStr);
          } catch (e) {
            messageObj = { type: 'unparseable', size: messageStr.length };
          }

          // Only log important messages to avoid spam
          const importantTypes = [
            'session.created', 'session.updated', 'error',
            'input_audio_buffer.speech_started', 'input_audio_buffer.speech_stopped',
            'input_audio_buffer.committed', 'input_audio_buffer.commited', // Handle typo in official docs
            'response.created', 'response.done',
            'conversation.item.created',
            'conversation.item.input_audio_transcription.completed',
            'conversation.item.input_audio_transcription.failed'
          ];

          if (importantTypes.includes(messageObj.type)) {
            console.log(`[AliyunProxy] 📥 Upstream message: ${messageObj.type}`, {
              type: messageObj.type,
              hasEventId: !!messageObj.event_id,
              sessionId: messageObj.session?.id || messageObj.event_id,
              // Log error details for debugging
              ...(messageObj.type === 'error' && {
                errorType: messageObj.error?.type,
                errorCode: messageObj.error?.code,
                errorMessage: messageObj.error?.message,
                errorParam: messageObj.error?.param
              })
            });
          }

          if (client.readyState === WebSocket.OPEN) {
            client.send(data);
          }
        } catch (error) {
          console.error('[AliyunProxy] Error processing upstream message:', error instanceof Error ? error.message : String(error));
          // Still forward the message even if we can't parse it
          if (client.readyState === WebSocket.OPEN) {
            client.send(data);
          }
        }
      });

      return upstream;
    };

    // Set up client event handlers (only once per client connection)
    client.on('message', (data) => {
      try {
        // Parse and log message for debugging (avoid logging audio data)
        const messageStr = data.toString();
        let messageObj;
        try {
          messageObj = JSON.parse(messageStr);
        } catch (e) {
          messageObj = { type: 'unparseable', size: messageStr.length };
        }

        // Only log non-audio messages to avoid spam
        if (messageObj.type !== 'input_audio_buffer.append') {
          console.log(`[AliyunProxy] 📤 Client message: ${messageObj.type}`, {
            type: messageObj.type,
            hasEventId: !!messageObj.event_id,
            upstreamReady: isUpstreamReady,
            upstreamState: currentUpstream?.readyState
          });
        }

        if (currentUpstream && currentUpstream.readyState === WebSocket.OPEN && isUpstreamReady) {
          currentUpstream.send(data);
        } else if (currentUpstream && currentUpstream.readyState === WebSocket.CONNECTING) {
          // Queue message if upstream is still connecting
          console.log('[AliyunProxy] 📥 Queueing client message while upstream connects');
          messageQueue.push(messageStr);
        } else {
          console.warn('[AliyunProxy] ⚠️ Received client message but no active upstream connection', {
            messageType: messageObj.type,
            upstreamExists: !!currentUpstream,
            upstreamState: currentUpstream?.readyState,
            isUpstreamReady
          });
        }
      } catch (error) {
        console.error('[AliyunProxy] Error processing client message:', error instanceof Error ? error.message : String(error));
      }
    });

    client.on('close', () => {
      console.log('[AliyunProxy] 🔒 Client disconnected');
      if (retryTimer) {
        clearTimeout(retryTimer);
        retryTimer = null;
      }
      if (currentUpstream && currentUpstream.readyState !== WebSocket.CLOSED) {
        currentUpstream.close();
      }
      // Clear message queue
      messageQueue = [];
      isUpstreamReady = false;
    });

    client.on('error', (err) => {
      console.error('[AliyunProxy] ❌ Client error:', err.message);
      if (currentUpstream && currentUpstream.readyState !== WebSocket.CLOSED) {
        currentUpstream.close();
      }
    });

    // Start the initial connection
    createUpstreamConnection();
  });
}

/**
 * Secure HTTP proxy for Aliyun/OpenAI-compatible chat completions.
 * POST /api/aliyun-chat
 * Body: { model, messages, ... } (OpenAI-compatible payload)
 * Adds API key server-side, forwards to Aliyun, streams response.
 */
export async function aliyunChatProxy(req: Request, res: Response) {
  if (req.method !== 'POST') {
    res.status(405).json({ error: 'Method not allowed' });
    return;
  }
  const API_KEY = process.env.VITE_DASHSCOPE_API_KEY || process.env.VITE_ALIYUN_API_KEY;
  if (!API_KEY) {
    res.status(500).json({ error: 'Aliyun API key not configured' });
    return;
  }
  const aliyunEndpoint = 'https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions';
  try {
    // Forward the request body, add API key
    const aliyunRes = await fetch(aliyunEndpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${API_KEY}`
      },
      body: JSON.stringify(req.body),
    });
    // Stream the response back to the client
    res.status(aliyunRes.status);
    if (aliyunRes.body) {
      aliyunRes.body.pipe(res);
    } else {
      res.end();
    }
  } catch (err) {
    res.status(502).json({ error: 'Failed to proxy Aliyun chat', details: err instanceof Error ? err.message : String(err) });
  }
}
