/**
 * Server
 * Main server implementation for the application
 */

import express, { Request, Response, NextFunction } from 'express';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { config as dotenvConfig } from 'dotenv';

// Load environment variables from .env file BEFORE importing other modules
dotenvConfig({ path: path.resolve(process.cwd(), '.env') });

import { config } from '@/config/client';
import { createServer } from 'net';
import http from 'http';
import { updateActualPort } from '../utils/portManager.js';
import unifiedRoutes from './routes/index';
import { attachAliyunRealtimeProxy } from './middleware/proxy';

// Log loaded environment variables for debugging (excluding sensitive ones)
console.log('[Server] Loaded environment variables:', Object.keys(process.env)
  .filter(key => key.startsWith('VITE_'))
  .map(key => `${key}: ${key.includes('KEY') ? '[REDACTED]' : process.env[key]}`));

const __dirname = path.dirname(fileURLToPath(import.meta.url));

// Add this helper function at the top
function getServerContext() {
  const isLaunchScript = process.env.APP_NAME === 'viewer' && process.env.NODE_ENV === 'development';
  return {
    isLaunchScript,
    appName: process.env.APP_NAME,
    nodeEnv: process.env.NODE_ENV
  };
}

// Add this helper function near the top
function ensureDirectoriesExist() {
  // Delete the default audio directory if it exists
  const defaultAudioDir = path.join(__dirname, '../../public/assets/audio/default');
  if (fs.existsSync(defaultAudioDir)) {
    console.log(`[Server] Deleting default audio directory: ${defaultAudioDir}`);
    try {
      // Delete all files in the directory
      const files = fs.readdirSync(defaultAudioDir);
      for (const file of files) {
        fs.unlinkSync(path.join(defaultAudioDir, file));
      }
      // Delete the directory itself
      fs.rmdirSync(defaultAudioDir);
      console.log(`[Server] Successfully deleted default audio directory`);
    } catch (error) {
      console.error(`[Server] Error deleting default audio directory:`, error);
    }
  }

  const directories = [
    path.join(__dirname, '../../public/models'),
    path.join(__dirname, '../../public/models/mediapipe'),
    path.join(__dirname, '../../public/models/tts'),
    path.join(__dirname, '../../public/assets'),
    path.join(__dirname, '../../public/assets/meshes'),
    path.join(__dirname, '../../public/assets/characters'),
    path.join(__dirname, '../../public/assets/animations'),
    path.join(__dirname, '../../public/assets/images'),
    path.join(__dirname, '../../public/assets/videos'),
    path.join(__dirname, '../../public/assets/seeds'),
    path.join(__dirname, '../../public/assets/audio'),
    path.join(__dirname, '../../temp')
  ];

  directories.forEach(dir => {
    if (!fs.existsSync(dir)) {
      console.log(`[Server] Creating directory: ${dir}`);
      fs.mkdirSync(dir, { recursive: true });
    }
  });

  console.log('[Server] All required directories verified');
}

// Helper function to check if a port is available
async function findAvailablePort(startPort: number, maxAttempts: number = 10): Promise<number> {
  for (let attempt = 0; attempt < maxAttempts; attempt++) {
    const port = startPort + attempt;
    try {
      await new Promise<void>((resolve, reject) => {
        const server = createServer()
          .once('error', (err: NodeJS.ErrnoException) => {
            if (err.code === 'EADDRINUSE') {
              console.log(`[Server] Port ${port} is in use, trying next port`);
              reject(new Error(`Port ${port} is in use`));
            } else {
              reject(err);
            }
          })
          .once('listening', () => {
            server.close(() => resolve());
          })
          .listen(port);
      });
      // If we get here, the port is available
      return port;
    } catch (error) {
      // Port is in use, try next one
      if (attempt === maxAttempts - 1) {
        throw new Error(`Could not find an available port after ${maxAttempts} attempts`);
      }
    }
  }
  // This should never happen due to the throw in the loop, but TypeScript needs a return
  throw new Error('Could not find an available port');
}

/**
 * Start the download server
 * @returns HTTP server instance
 */
export async function startDownloadServer() {
  // Add this line at the start of the function
  ensureDirectoriesExist();

  const app = express();
  const serverContext = getServerContext();

  // Define CORS headers to use throughout the application
  const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, PATCH, OPTIONS',
    'Access-Control-Allow-Headers': '*',
    'Access-Control-Allow-Credentials': 'true'
  };

  // List of allowed origins for CORS
  const allowedOrigins = [
    'http://localhost:3002',
    'http://localhost:2994',
    'http://127.0.0.1:3002',
    'http://127.0.0.1:2994'
  ];

  // Add body parsing middleware before CORS and proxy middleware
  // Increase the limit to handle large payloads (e.g., audio/video data)
  app.use(express.json({ limit: '50mb' }));
  app.use(express.urlencoded({ limit: '50mb', extended: true }));

  // General handling for all multipart/form-data requests
  // Express doesn't parse multipart/form-data by default
  // We'll skip this middleware for /upload endpoints to avoid interfering with multer
  app.use((req, res, next) => {
    // Skip for upload endpoints - let multer handle these directly
    if (req.originalUrl.includes('/upload')) {
      console.log(`[Server] Skipping raw body capture for upload endpoint: ${req.originalUrl}`);
      return next();
    }

    if (req.headers['content-type']?.includes('multipart/form-data')) {
      console.log(`[Server] Detected multipart/form-data request to ${req.originalUrl}`);

      // Create a raw body buffer
      let rawData: Buffer[] = [];

      req.on('data', (chunk: Buffer) => {
        rawData.push(chunk);
      });

      req.on('end', () => {
        // Combine all chunks into a single buffer
        const rawBody = Buffer.concat(rawData);

        // Store the raw body on the request object
        (req as any).rawBody = rawBody;

        console.log(`[Server] Raw body captured: ${rawBody.length} bytes`);
        next();
      });

      return;
    }
    next();
  });

  // Add CORS middleware that handles both regular requests and OPTIONS requests
  app.use((req, res, next) => {
    // Check if the request origin is in our allowed list
    const origin = req.headers.origin;
    if (origin && allowedOrigins.includes(origin)) {
      // If it's an allowed origin, set it specifically
      res.header('Access-Control-Allow-Origin', origin);
    } else {
      // Otherwise use the wildcard
      res.header('Access-Control-Allow-Origin', '*');
    }

    // Apply other CORS headers to all responses
    res.header('Access-Control-Allow-Methods', corsHeaders['Access-Control-Allow-Methods']);
    res.header('Access-Control-Allow-Headers', corsHeaders['Access-Control-Allow-Headers']);
    res.header('Access-Control-Allow-Credentials', corsHeaders['Access-Control-Allow-Credentials']);

    // Handle OPTIONS requests
    if (req.method === 'OPTIONS') {
      // For debugging only
      if (req.originalUrl.includes('/letta-proxy/') || req.originalUrl.includes('/vllm-proxy/') ||
        req.originalUrl.includes('/sparktts-proxy/')) {
        console.log(`[Server] Handling OPTIONS request for ${req.originalUrl} from origin: ${origin}`);
      }
      res.status(200).end();
      return;
    }

    next();
  });

  // Serve static files from public directory BEFORE unified routes
  app.use('/assets', express.static(path.join(__dirname, '../../public/assets')));

  // Add a specific route for the meshes directory
  const meshesDir = path.join(__dirname, '../../public/assets/meshes');
  app.use('/assets/meshes', express.static(meshesDir));

  // Add route to serve models under /assets/models/ for compatibility with ModelDownloader
  const publicModelsDir = path.join(__dirname, '../../public/models');
  app.use('/assets/models', express.static(publicModelsDir));

  // Add MediaPipe static route
  const modelsDir = path.join(__dirname, '../../public/models');
  app.use('/models', express.static(modelsDir));

  // Add this near the top with other model routes
  const mediapipeDir = path.join(__dirname, '../../public/models/mediapipe');
  app.use('/models/mediapipe', express.static(mediapipeDir));

  // Add route for tts models
  const ttsDir = path.join(__dirname, '../../public/models/tts');
  app.use('/models/tts', express.static(ttsDir));

  // Use the unified route system (replaces individual route registrations)
  app.use('/', unifiedRoutes);

  // Get desired port from config with fallback ports defined in environment
  const desiredPort = config.downloadConfig.port;
  const fallbackStartPort = parseInt(process.env.VITE_DOWNLOAD_SERVER_FALLBACK_START_PORT || '3000');
  const maxPortAttempts = parseInt(process.env.VITE_DOWNLOAD_SERVER_MAX_PORT_ATTEMPTS || '10');

  // Find an available port, starting with the desired port
  let port: number;
  try {
    port = await findAvailablePort(desiredPort, 1); // Try the desired port first
  } catch (error) {
    console.warn(`[Server] Desired port ${desiredPort} is in use, trying fallback ports`);
    try {
      // If the desired port is taken, try fallback ports
      port = await findAvailablePort(fallbackStartPort, maxPortAttempts);
      console.log(`[Server] Using fallback port: ${port}`);
    } catch (fallbackError) {
      console.error('[Server] Failed to find an available port:', fallbackError);
      throw fallbackError;
    }
  }

  // Create HTTP server
  const server = http.createServer(app);

  // Attach the WebSocket proxy for Aliyun realtime API
  // REQUIRED: Browsers cannot set Authorization header in WebSocket handshake
  attachAliyunRealtimeProxy(server, '/ws');

  // Start the server with the available port
  server.listen(port, () => {
    console.log('\n=== Download Server Started ===');
    console.log(`Time: ${new Date().toISOString()}`);
    console.log(`Port: ${port} ${port !== desiredPort ? `(fallback from ${desiredPort})` : ''}`);
    console.log(`Launch Context: ${JSON.stringify(serverContext, null, 2)}`);
    console.log(`URL: http://localhost:${port}`);
    console.log(`Proxy Status: http://localhost:${port}/proxy-status`);
    console.log('===============================\n');

    // Store the actual port used in config for any components that need it
    (config.downloadConfig as any).actualPort = port;

    // Update port manager with the actual port
    updateActualPort(port);

    // Notify any frontend components that the port has changed
    // This is especially important if the server falls back to a different port
    if (typeof window !== 'undefined') {
      // We're in a browser environment - use dynamic import to avoid SSR issues
      import('../utils/portManager.js').then(({ updateActualPort }) => {
        updateActualPort(port);
      }).catch(err => {
        console.warn('[Server] Could not notify frontend of port change:', err.message);
      });
    }

    process.on('SIGINT', () => {
      console.log('Shutting down server...');
      server.close(() => {
        console.log('Server closed');
        process.exit(0);
      });
    });

    process.on('SIGTERM', () => {
      console.log('Received SIGTERM, shutting down server...');
      server.close(() => {
        console.log('Server closed');
        process.exit(0);
      });
    });
  });

  return server;
}

if (import.meta.url === `file://${process.argv[1]}`) {
  const serverContext = getServerContext();
  if (!serverContext.isLaunchScript) {
    console.warn('Warning: Server started directly, not through npm launch viewer dev');
  }
  startDownloadServer().catch((err) => {
    console.error('Failed to start server:', err);
    process.exit(1);
  });
}
