export interface AlgorithmRequest {
    inputData: any;
    algorithm: string;
    parameters?: Record<string, any>;
    seed?: number;
}

export interface AlgorithmResponse {
    success: boolean;
    result: any;
    error?: string;
    processingTime?: number;
}

export interface GradioResponse<T = any> {
    success: boolean;
    error?: string;
    data: T | null;
}

export type ConversionSource = 'text' | 'image' | 'doll' | 'tripo-doll';

export type FileInput = string | Blob | File | Buffer;

export interface AnyTo3DRequest {
    source: 'text' | 'image' | 'doll' | 'tripo-doll';
    input: string | FileInput;
    seed?: number;
    gender?: 'boy' | 'girl'; // Added for tripo-doll
    saveOptions?: {
        basePath?: string;
        paths?: {
            meshes: string;
            images: string;
            videos: string;
        };
        defaultSaveConfig?: {
            saveMesh?: boolean;
            saveImage?: boolean;
            saveVideo?: boolean;
        };
    };
    usePredict?: boolean;
}

export interface GradioResult {
    data: string | string[] | null;
    error?: string;
}

export interface AnyTo3DResponse {
    meshResult: string | null;
    imageResult: string | null;
    videoResult: string | null;
    riggingResult?: string | null; // Added for tripo-doll
    metadata?: {
        type?: string;
        gender?: string;
        [key: string]: any; // Allow for additional metadata properties
    };
}

export interface UserData {
    userId: string;
    sessionData: {
        timestamps: number[];
        gestures: string[];
        handTracking?: Record<string, any>;
        performanceMetrics?: Record<string, any>;
    };
    metadata?: {
        deviceInfo: string;
        sessionStartTime: number;
        sessionEndTime?: number;
    };
}

export interface StorageResponse {
    success: boolean;
    error?: string;
    timestamp: number;
}

// Speech Recognition types
export interface SpeechRecognitionRequest {
    audioData: any; // Audio data in a format acceptable by the Gradio API
    language?: string; // 'auto', 'zh', 'en', 'yue', 'ja', 'ko', 'nospeech'
    audioFormat?: string; // Optional format information
    durationSeconds?: number; // Optional duration information
}

export interface SpeechRecognitionResponse {
    text: string; // Transcribed text with emotion markers
    emotion: string | null; // Extracted emotion if available
    events: string[]; // Detected audio events
    language: string; // Detected language
}

// LLM types
export interface LLMRequest {
    prompt: string;
    model?: string;
    temperature?: number;
    maxTokens?: number;
    stream?: boolean;
    systemPrompt?: string;
    language?: string; // Added language property
    topP?: number;
    frequencyPenalty?: number;
    presencePenalty?: number;
    enableAudio?: boolean;
    audio?: {
        voice?: string;
        format?: string;
    };
    streamProcessor?: any; // Add streamProcessor to pass the existing instance
    ttsService?: any; // Add ttsService to pass the TTS service instance for streaming
    skipTTS?: boolean; // Skip TTS processing for text-only analysis
}

export interface LLMResponse {
    text: string;
    model: string;
    usage: {
        promptTokens: number;
        completionTokens: number;
        totalTokens: number;
    };
}

// Additional content for multimodal requests
export interface MultimodalContent {
    text?: string;
    imageUrls?: string[];
    audioUrls?: string[];
    videoFrames?: string[]; // Array of video frame data URLs for captured video frames
}

// Media Stream types for vLLM integration
export interface MediaStreamRequest {
    mediaData: Blob | File | Buffer;
    mediaType: 'audio' | 'video' | 'audio-video';
    sessionId: string;
    language?: string;
    model?: string;
    isQwenOmniDirectAudio?: boolean; // Flag to indicate direct audio input for Qwen Omni
    additionalContent?: MultimodalContent; // Additional content for multimodal requests
    ttsService?: any; // Optional TTS service instance to use instead of creating a new one
    streamProcessor?: any; // Optional stream processor instance to use instead of creating a new one
    asrMetadata?: {
        detectedLanguage?: string; // Language detected from ASR (zh, en, ja, etc.)
        detectedEmotion?: string;  // Emotion detected from ASR (happy, sad, angry, etc.)
        detectedEvents?: string[]; // Events detected from ASR (Speech, Laughter, etc.)
        confidence?: number;       // ASR confidence score
        processingTime?: number;   // ASR processing time
    };
    options?: {
        temperature?: number;
        maxTokens?: number;
        systemPrompt?: string;
        customPrompt?: string; // Custom prompt override
        stream?: boolean;
        topP?: number;
        frequencyPenalty?: number;
        presencePenalty?: number;
        enableAudio?: boolean;
        skipTTS?: boolean; // Flag to skip TTS processing (to avoid duplicate voice playback)
        audio?: {
            voice?: string;
            format?: string;
        };
        onProgress?: (data: {
            text: string;
            hasAudio: boolean;
            audioData: string | null;
            transcript: string;
        }) => void;
        onCompletion?: (data: {
            text: string;
            hasAudio: boolean;
            audioData: string;
            transcript: string;
            interrupted: boolean;
        }) => void;
    };
}

/**
 * Video frames request
 */
export interface VideoFramesRequest {
    videoFrames: string[];
    sessionId: string;
    language?: string;
    options?: {
        prompt?: string; // Text prompt to accompany the video
        stream?: boolean;
        systemPrompt?: string;
        enableAudio?: boolean;
        additionalContent?: MultimodalContent; // Additional content for multimodal requests
        audio?: {
            voice: string;
            format: string;
        };
    };
}

export interface MediaStreamResponse {
    input: {
        text: string;
        language: string;
        emotion?: string;
    };
    response: {
        text: string;
        emotion?: string;
    };
    sessionId: string;
}

// Text-to-Speech types
export interface TTSVoiceCloneRequest {
    text: string;
    promptText?: string;
    promptAudio?: any; // Audio data for voice cloning
    mode: 'clone';
}

export interface TTSVoiceCreateRequest {
    text: string;
    gender: 'male' | 'female';
    pitch: number; // 1-5
    speed: number; // 1-5
    mode: 'create';
}

export type TextToSpeechRequest = TTSVoiceCloneRequest | TTSVoiceCreateRequest;

export interface TextToSpeechResponse {
    audioUrl: string; // URL or path to the generated audio file
    duration?: number; // Optional duration of the audio in seconds
}