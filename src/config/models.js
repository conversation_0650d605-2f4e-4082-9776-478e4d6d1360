/**
 * Shared model configurations for all applications
 * This file contains configurations for models that can be used across different apps
 */

// MediaPipe model configurations
export const MEDIAPIPE_MODELS = {
    holistic: {
        modelPath: 'models/mediapipe/holistic_landmarker.task',
        fallbackPath: 'https://storage.googleapis.com/mediapipe-models/holistic_landmarker/holistic_landmarker/float16/latest/holistic_landmarker.task'
    },
    pose: {
        modelPath: 'models/mediapipe/pose_landmarker_lite.task',
        fallbackPath: 'https://storage.googleapis.com/mediapipe-models/pose_landmarker/pose_landmarker_lite/float16/1/pose_landmarker_lite.task'
    },
    hand: {
        modelPath: 'models/mediapipe/hand_landmarker.task',
        fallbackPath: 'https://storage.googleapis.com/mediapipe-models/hand_landmarker/hand_landmarker/float16/1/hand_landmarker.task'
    }
};

// SherpaOnnx model configurations
export const SHERPA_ONNX_MODELS = {
    models: {
        dolphin: {
            name: 'dolphin-base-ctc-multi-lang',
            modelPath: 'models/stt/dolphin-base-ctc-multi-lang',
            fallbackPath: 'https://github.com/k2-fsa/sherpa-onnx/releases/download/asr-models/sherpa-onnx-dolphin-base-ctc-multi-lang-int8-2025-04-02.tar.bz2',
            description: 'Multi-language model supporting Chinese, English, Japanese, Korean, and Cantonese',
            languages: ['zh', 'en', 'ja', 'ko', 'yue'],
            features: ['vad', 'language_id', 'streaming']
        },
        sensevoice: {
            name: 'sense-voice-zh-en-ja-ko-yue',
            modelPath: 'models/stt/sense-voice-zh-en-ja-ko-yue',
            fallbackPath: 'https://github.com/k2-fsa/sherpa-onnx/releases/download/asr-models/sherpa-onnx-sense-voice-zh-en-ja-ko-yue-2024-07-17.tar.bz2',
            description: 'SenseVoice model optimized for Chinese, English, Japanese, Korean, and Cantonese',
            languages: ['zh', 'en', 'ja', 'ko', 'yue'],
            features: ['vad', 'language_id', 'streaming']
        }
    },
    defaultModel: 'dolphin',
    vad: {
        enabled: true,
        threshold: 0.5,
        minSpeechDuration: 0.5,
        maxSpeechDuration: 30.0,
        minSilenceDuration: 0.5,
        sileroVad: {
            model: 'models/stt/vad/silero_vad.onnx',
            fallbackPath: 'https://github.com/k2-fsa/sherpa-onnx/releases/download/asr-models/silero_vad.onnx'
        }
    },
    languageId: {
        enabled: true,
        threshold: 0.5
    },
    streaming: {
        enabled: true,
        chunkSize: 0.1,  // seconds
        maxChunks: 100
    },
    keywordSpotting: {
        enabled: true,
        config: {
            model: 'models/kws/keyword_spotter.onnx',
            fallbackPath: 'https://github.com/k2-fsa/sherpa-onnx/releases/download/kws-models/keyword_spotter.onnx',
            keywords: ['hey', 'hi', 'hello'],
            threshold: 0.5,
            tailPadding: 0.4  // seconds
        },
        processing: {
            sampleRate: 16000,
            maxDuration: 30.0  // seconds
        }
    },
    server: {
        port: 6006,
        host: 'localhost'
    }
};

// Export all model configurations
export const MODEL_CONFIGS = {
    mediapipe: MEDIAPIPE_MODELS,
    sherpaOnnx: SHERPA_ONNX_MODELS
};

export default MODEL_CONFIGS;