// Client-side config
// Import environment utilities
import { isNodeEnvironment, isBrowserEnvironment } from '../media/utils/environment';
import { endpoints } from './endpoints';
import { createLogger } from '../utils/logger.js';

// Dedicated logger for Config (client)
const logger = createLogger('Config');

// Determine the current environment
const isServer = isNodeEnvironment();
const isBrowser = isBrowserEnvironment();

/**
 * Get an environment variable from the appropriate source based on environment
 * @param key The environment variable key (with or without VITE_ prefix)
 * @param defaultValue The default value to use if the environment variable is not defined or empty
 * @param logWarning Whether to log a warning if the environment variable is not defined
 * @returns The environment variable value or the default value
 */
const getEnvVar = (key: string, defaultValue: string, logWarning = true): string => {
    // Add VITE_ prefix if not already present
    const fullKey = key.startsWith('VITE_') ? key : `VITE_${key}`;

    let envValue: string | undefined;

    if (isServer) {
        // Server-side: use process.env
        envValue = process.env[fullKey];
        if (!envValue && logWarning) {
            console.warn(`[Config] Environment variable ${fullKey} not found in process.env`);
            logger.debug(`Environment variable ${fullKey} not found in process.env (using default)`);
        }
    } else if (isBrowser) {
        // Client-side: use import.meta.env
        try {
            // Try to access the environment variable directly
            // This should work because Vite replaces import.meta.env.* with the actual values at build time
            envValue = (import.meta.env as Record<string, any>)[fullKey];

            // If the value is undefined but we can see it in the list of keys, try again
            if (envValue === undefined && Object.keys(import.meta.env).includes(fullKey)) {
                // Try accessing it with bracket notation
                envValue = (import.meta.env as any)[fullKey];

                // If still undefined, try with direct property access
                if (envValue === undefined) {
                    // @ts-ignore - This is a workaround for Vite's environment variable handling
                    envValue = import.meta.env[fullKey];
                }
            }
        } catch (error) {
            console.error(`[Config] Error accessing import.meta.env[${fullKey}]:`, error);
            logger.error(`Error accessing import.meta.env[${fullKey}]`, error);
        }
    } else {
        // Unknown environment
        console.warn(`[Config] Unknown environment, cannot access ${fullKey}`);
        logger.warn(`Unknown environment, cannot access ${fullKey}`);
    }

    // Check if the environment variable is defined and not empty
    const isValidValue = envValue !== undefined && envValue !== null && envValue !== '';

    // Log if the environment variable is not defined or empty
    if (!isValidValue && logWarning) {
        console.warn(`[Config] Using default value for ${fullKey}: ${defaultValue}`);
        logger.debug(`Using default value for ${fullKey}: ${defaultValue}`);
    }

    // TypeScript needs this cast to ensure we're returning a string
    return isValidValue ? (envValue as string) : defaultValue;
};

/**
 * Get an environment variable as a boolean
 * @param key The environment variable key
 * @param defaultValue The default value to use if the environment variable is not defined or empty
 * @param logWarning Whether to log a warning if the environment variable is not defined
 * @returns The environment variable value as a boolean or the default value
 */
const getEnvVarAsBoolean = (key: string, defaultValue: boolean, logWarning = true): boolean => {
    const strValue = getEnvVar(key, defaultValue.toString(), logWarning);

    // Check for common boolean string values
    if (strValue.toLowerCase() === 'true' || strValue === '1') {
        return true;
    }

    if (strValue.toLowerCase() === 'false' || strValue === '0') {
        return false;
    }

    console.warn(`[Config] Environment variable ${key} is not a valid boolean: ${strValue}. Using default: ${defaultValue}`);
    return defaultValue;
};

// Log all available environment variables for debugging (excluding sensitive ones)
const logAvailableEnvVars = () => {
    if (!isBrowser) return; // Only log in browser environment

    console.log('[Config] Available environment variables in import.meta.env:');

    try {
        // Get all environment variables that start with VITE_
        const envVars = Object.keys(import.meta.env)
            .filter(key => key.startsWith('VITE_'))
            .map(key => {
                const value = (import.meta.env as any)[key];
                // Redact sensitive values
                const displayValue = key.includes('KEY') || key.includes('API') || key.includes('SECRET')
                    ? '[REDACTED]'
                    : value;
                // Check if the value is empty
                const isEmpty = value === undefined || value === null || value === '';
                // Return the key-value pair with a warning if empty
                return `${key}: ${displayValue}${isEmpty ? ' [EMPTY]' : ''}`;
            });

        if (envVars.length > 0) {
            console.log(envVars);

            // Log a summary of how many variables were found
            console.log(`[Config] Found ${envVars.length} environment variables with VITE_ prefix`);

            // Check for empty values
            const emptyVars = envVars.filter(v => v.includes('[EMPTY]'));
            if (emptyVars.length > 0) {
                console.warn(`[Config] Warning: ${emptyVars.length} environment variables are empty`);
            }
        } else {
            console.warn('[Config] No VITE_ environment variables found in import.meta.env');
            console.warn('[Config] This may indicate an issue with environment variable loading');
        }
    } catch (error) {
        console.error('[Config] Error logging environment variables:', error);
    }
};

// Log available environment variables (only in browser)
if (isBrowser) {
    // logAvailableEnvVars();
}

// Create a reactive config object
const config = {
    // Gradio endpoints
    gradioEndpoint: getEnvVar('VITE_GRADIO_Anyto3D_ENDPOINT', 'http://127.0.0.1:7860'),
    gradioASREndpoint: getEnvVar('VITE_ASR_ENDPOINT', 'http://127.0.0.1:7861'),

    // API endpoints - import from centralized endpoints config
    endpoints: {
        ...endpoints,
        downloadServer: `http://${getEnvVar('VITE_SERVER_HOST', 'localhost')}:${getEnvVar('VITE_DOWNLOAD_SERVER_PORT', '2994')}`
    },

    // LLM configuration
    llmConfig: {
        provider: getEnvVar('VITE_LLM_PROVIDER', 'vllm'),
        ollamaModel: getEnvVar('VITE_OLLAMA_DEFAULT_MODEL', 'qwen2.5:72b'),
        vllmModel: getEnvVar('VITE_LLM_MODEL', 'Qwen2.5-Omni-7B'),
        lettaPreferredModel: getEnvVar('VITE_LETTA_PREFERRED_MODEL', 'vllm/Qwen/Qwen2.5-Omni-7B'),
        lettaFallbackModel: getEnvVar('VITE_LETTA_FALLBACK_MODEL', 'letta/letta-free'),
        lettaEmbedding: getEnvVar('VITE_LETTA_EMBEDDING', 'letta/letta-free'),

        // Aliyun Bailian configuration
        aliyunApiKey: getEnvVar('VITE_DASHSCOPE_API_KEY', ''),
        aliyunModel: getEnvVar('VITE_ALIYUN_MODEL', 'qwen-omni-turbo-realtime'),
        aliyunUseProxy: getEnvVarAsBoolean('VITE_ALIYUN_USE_PROXY', true), // Browser environments should use proxy by default
        modelProvider: getEnvVar('VITE_MODEL_PROVIDER', 'vllm') // 'vllm' or 'aliyun'
    },

    // Download server configuration
    downloadConfig: {
        _port: parseInt(getEnvVar('VITE_DOWNLOAD_SERVER_PORT', '2994')),
        get port() {
            return this._port;
        },
        set port(value: number) {
            this._port = value;
        },
        assetsDir: getEnvVar('VITE_DOWNLOAD_ASSETS_DIR', 'public/assets'),
        supportedMeshExtensions: getEnvVar('VITE_SUPPORTED_MESH_EXTENSIONS', '.glb,.fbx,.obj,.gltf').split(',')
    },

    // Server configuration
    host: getEnvVar('VITE_SERVER_HOST', 'localhost'),
    port: parseInt(getEnvVar('VITE_SERVER_PORT', '3006')),

    // Model configuration
    modelConfig: {
        baseUrl: getEnvVar('VITE_ASSETS_PATH', '/assets'), // Default to '/assets' if not specified
        cacheDuration: parseInt(getEnvVar('VITE_MODEL_CACHE_DURATION', '86400000')), // 24 hours
        mediapipe: {
            holistic: {
                modelPath: 'models/mediapipe/holistic_landmarker.task',
                fallbackPath: 'https://storage.googleapis.com/mediapipe-models/holistic_landmarker/holistic_landmarker/float16/1/holistic_landmarker.task'
            },
            pose: {
                modelPath: 'models/mediapipe/pose_landmarker_lite.task',
                fallbackPath: 'https://storage.googleapis.com/mediapipe-models/pose_landmarker/pose_landmarker_lite/float16/1/pose_landmarker_lite.task'
            },
            hand: {
                modelPath: 'models/mediapipe/hand_landmarker.task',
                fallbackPath: 'https://storage.googleapis.com/mediapipe-models/hand_landmarker/hand_landmarker/float16/1/hand_landmarker.task'
            }
        }
    },

    // Debug configuration
    debug: getEnvVar('VITE_DEBUG_MODE', 'false') === 'true',
    appEnv: getEnvVar('VITE_APP_ENV', 'development')
};

export { config };
export type Config = typeof config;