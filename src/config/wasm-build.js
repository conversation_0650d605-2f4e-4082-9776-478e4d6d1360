/**
 * Configuration for building WebAssembly files from ONNX models
 * This file contains settings for the WASM build process
 */

// Emscripten SDK configuration
export const EMSDK_CONFIG = {
    version: '3.1.48',  // Recommended version for sherpa-onnx
    repository: 'https://github.com/emscripten-core/emsdk.git'
};

// sherpa-onnx configuration
export const SHERPA_ONNX_CONFIG = {
    repository: 'https://github.com/k2-fsa/sherpa-onnx.git',
    branch: 'master'  // Use the main branch
};

// Model configurations for WASM builds
export const WASM_BUILD_MODELS = {
    dolphin: {
        name: 'Dolphin Small CTC',
        modelType: 'dolphin',
        modelPath: 'tts/dolphin-small-ctc-multi-lang', // This path is relative to modelBaseDir
        modelUrl: 'https://github.com/k2-fsa/sherpa-onnx/releases/download/asr-models/sherpa-onnx-dolphin-small-ctc-multi-lang-int8-2025-04-02.tar.bz2',
        wasmOutputDir: 'public/models/tts/dolphin-small-ctc-multi-lang' // Now using the same directory as model files
    },
    senseVoice: {
        name: 'SenseVoice',
        modelType: 'senseVoice',
        modelPath: 'tts/sherpa-onnx-sense-voice-zh-en-ja-ko-yue-2024-07-17', // This path is relative to modelBaseDir
        modelUrl: 'https://github.com/k2-fsa/sherpa-onnx/releases/download/asr-models/sherpa-onnx-sense-voice-zh-en-ja-ko-yue-2024-07-17.tar.bz2',
        wasmOutputDir: 'public/models/tts/sherpa-onnx-sense-voice-zh-en-ja-ko-yue-2024-07-17' // Now using the same directory as model files
    }
};

// Build process configuration
export const BUILD_CONFIG = {
    tempDir: './temp/sherpa-onnx-build',  // Temporary directory for building (in current directory)
    keepBuildFiles: true,  // Keep build files for resuming and debugging
    outputBaseDir: 'public/models/tts',  // Base directory for WASM output - changed from wasm to tts
    modelBaseDir: 'public/models',  // Base directory for model files
    buildScript: 'scripts/build-sherpa-onnx-wasm.sh',  // Path to the build script
    defaultModel: 'dolphin',  // Default model to build
    buildTimeout: 3600000  // 60 minutes timeout for build process
};

// Export all configurations
export const WASM_BUILD_CONFIG = {
    emsdk: EMSDK_CONFIG,
    sherpaOnnx: SHERPA_ONNX_CONFIG,
    models: WASM_BUILD_MODELS,
    build: BUILD_CONFIG
};

export default WASM_BUILD_CONFIG;
