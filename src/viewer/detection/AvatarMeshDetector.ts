/**
 * AvatarMeshDetector - Centralized avatar mesh detection and analysis
 * Provides unified interface for detecting and analyzing avatar meshes across the application
 */

import { analyzeMesh } from '../../animation/AnimationUtils.js';
import { createLogger } from '../../utils/logger.js';

// Create logger for this module
const logger = createLogger('AvatarMeshDetector');

/**
 * Configuration options for avatar detection
 */
export interface AvatarDetectionOptions {
  // Detection behavior
  logSkeletonStructure?: boolean; // Whether to log skeleton structure for debugging
  includeDetailedAnalysis?: boolean; // Include detailed mesh analysis
  
  // Callbacks
  onDetectionComplete?: (result: AvatarDetectionResult) => void;
  onError?: (error: Error) => void;
}

/**
 * Result of avatar mesh detection
 */
export interface AvatarDetectionResult {
  found: boolean;
  meshType: string;
  hasMorphTargets: boolean;
  isRPM: boolean;
  isDoll: boolean;
  morphTargets: string[];
  capabilities?: {
    canAnimate: boolean;
    canLipSync: boolean;
    canBlink: boolean;
    canGesture: boolean;
    canMoveHead: boolean;
  };
  foundMesh?: any; // THREE.Mesh or THREE.SkinnedMesh
  armature?: any; // THREE.Object3D
  morphTargetDictionary?: any;
  morphTargetInfluences?: Float32Array;
}

/**
 * Centralized avatar mesh detector
 * Consolidates avatar detection logic from various parts of the application
 */
export class AvatarMeshDetector {
  private options: AvatarDetectionOptions;
  
  constructor(options: AvatarDetectionOptions = {}) {
    this.options = {
      logSkeletonStructure: false,
      includeDetailedAnalysis: true,
      ...options
    };
  }

  /**
   * Detect avatar mesh with skeleton and morph targets in a loaded object
   * @param loadedObject - The 3D object to analyze (THREE.Object3D)
   * @param objectViewer - Optional viewer reference for object lookup
   * @returns Detection results
   */
  detectAvatarMesh(loadedObject: any, objectViewer?: any): AvatarDetectionResult {
    logger.debug('Starting avatar mesh detection');

    // Handle both object ID strings and direct object references
    let targetObject = loadedObject;
    if (typeof loadedObject === 'string' && objectViewer?.objects?.get) {
      targetObject = objectViewer.objects.get(loadedObject);
    }

    if (!targetObject) {
      logger.warn('Object not found for avatar detection:', loadedObject);
      return this.createEmptyResult();
    }

    // Log skeleton structure if requested
    if (this.options.logSkeletonStructure) {
      this.logSkeletonStructure(targetObject);
    }

    // Use the unified analyzeMesh function from AnimationUtils
    const meshAnalysis: any = analyzeMesh(targetObject, null as any);

    // If no suitable mesh was found, return early
    if (!meshAnalysis.foundMesh) {
      logger.warn('No suitable mesh found in object');
      return this.createEmptyResult();
    }

    // Create comprehensive detection result
    const result: AvatarDetectionResult = {
      found: true,
      meshType: meshAnalysis.meshType,
      hasMorphTargets: meshAnalysis.hasMorphTargets,
      isRPM: meshAnalysis.isRPM,
      isDoll: meshAnalysis.isDoll,
      foundMesh: meshAnalysis.foundMesh,
      armature: meshAnalysis.armature,
      morphTargets: [],
      capabilities: meshAnalysis.capabilities
    };

    // Extract morph target information if available
    if (meshAnalysis.foundMesh?.morphTargetDictionary) {
      result.morphTargetDictionary = meshAnalysis.foundMesh.morphTargetDictionary;
      result.morphTargets = Object.keys(meshAnalysis.foundMesh.morphTargetDictionary);
    }

    if (meshAnalysis.foundMesh?.morphTargetInfluences) {
      result.morphTargetInfluences = meshAnalysis.foundMesh.morphTargetInfluences;
    }

    logger.debug('Avatar detection complete:', {
      meshType: result.meshType,
      hasMorphTargets: result.hasMorphTargets,
      morphTargetCount: result.morphTargets.length
    });

    // Call completion callback if provided
    if (this.options.onDetectionComplete) {
      try {
        this.options.onDetectionComplete(result);
      } catch (error) {
        logger.error('Error in detection completion callback:', error);
      }
    }

    return result;
  }

  /**
   * Analyze multiple objects for avatar meshes
   * @param objects - Array of objects to analyze
   * @returns Array of detection results
   */
  detectMultipleAvatarMeshes(objects: any[]): AvatarDetectionResult[] {
    return objects.map(obj => this.detectAvatarMesh(obj));
  }

  /**
   * Check if an object can be transformed to a talking avatar
   * @param object - Object to check
   * @returns True if object can be transformed
   */
  canTransformToTalkingHead(object: any): boolean {
    const result = this.detectAvatarMesh(object);
    return result.found && result.capabilities?.canAnimate === true;
  }

  /**
   * Get avatar capabilities summary
   * @param object - Object to analyze
   * @returns Capabilities summary
   */
  getAvatarCapabilities(object: any) {
    const result = this.detectAvatarMesh(object);
    return result.capabilities || {
      canAnimate: false,
      canLipSync: false,
      canBlink: false,
      canGesture: false,
      canMoveHead: false
    };
  }

  /**
   * Log the skeleton structure of a 3D object for debugging
   * @param object - The 3D object to analyze
   */
  private logSkeletonStructure(object: any): void {
    if (!object) return;
    
    logger.debug('Logging skeleton structure');
    
    // Use debugBones utility if available
    try {
      const { debugBones } = require('../../animation/AnimationUtils.js');
      const bones = debugBones(object);
      if (bones.length > 0) {
        logger.debug(`Found ${bones.length} bones in skeleton`);
      }
    } catch (error) {
      logger.warn('Could not load debugBones utility:', error);
    }
  }

  /**
   * Create an empty detection result
   */
  private createEmptyResult(): AvatarDetectionResult {
    return {
      found: false,
      meshType: 'unknown',
      hasMorphTargets: false,
      isRPM: false,
      isDoll: false,
      morphTargets: [],
      capabilities: {
        canAnimate: false,
        canLipSync: false,
        canBlink: false,
        canGesture: false,
        canMoveHead: false
      }
    };
  }

  /**
   * Update detection options
   * @param newOptions - New options to merge
   */
  updateOptions(newOptions: Partial<AvatarDetectionOptions>): void {
    this.options = { ...this.options, ...newOptions };
  }

  /**
   * Get current options
   */
  getOptions(): AvatarDetectionOptions {
    return { ...this.options };
  }
}

/**
 * Default avatar mesh detector instance
 */
export const defaultAvatarDetector = new AvatarMeshDetector();

/**
 * Factory function to create a new avatar detector with specific options
 * @param options - Configuration options
 * @returns New AvatarMeshDetector instance
 */
export function createAvatarDetector(options: AvatarDetectionOptions = {}): AvatarMeshDetector {
  return new AvatarMeshDetector(options);
}

/**
 * Convenience function for quick avatar detection
 * @param object - Object to detect
 * @param options - Optional detection options
 * @returns Detection result
 */
export function detectAvatarMesh(object: any, options?: AvatarDetectionOptions): AvatarDetectionResult {
  const detector = options ? new AvatarMeshDetector(options) : defaultAvatarDetector;
  return detector.detectAvatarMesh(object);
}
