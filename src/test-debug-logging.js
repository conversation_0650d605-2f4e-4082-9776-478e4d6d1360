/**
 * Test script to enable debug logging and test LangGraph agent
 */

import { createLogger, setLogLevel, LogLevel } from './utils/logger.js';
import { LangGraphAgentService } from './agent/core.js';

// Enable debug logging globally
setLogLevel(LogLevel.DEBUG);

const logger = createLogger('TestScript');

async function testAgent() {
    logger.debug('Starting LangGraph agent test...');

    try {
        const agentService = new LangGraphAgentService({
            autoRegisterTools: false, // Disable tools for simple test
            temperature: 0.7
        });

        logger.debug('Initializing agent service...');
        await agentService.initialize();

        logger.debug('Testing simple response...');
        const response = await agentService.generateResponse("Hello, can you tell me about yourself?", {
            stream: false // Test non-streaming first
        });

        logger.debug('Response received:', response);

        logger.debug('Testing streaming response...');
        const streamGenerator = agentService.generateResponse("What can you do for me?", {
            stream: true
        });

        for await (const chunk of streamGenerator) {
            logger.debug('Stream chunk received:', chunk);
        }

        logger.debug('Test completed successfully');

    } catch (error) {
        logger.error('Test failed:', error);
    }
}

// Run the test
testAgent(); 