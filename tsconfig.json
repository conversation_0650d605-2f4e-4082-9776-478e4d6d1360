{
    "compilerOptions": {
        "types": [
            "node",
            "vite/client"
        ],
        "jsx": "react-jsx",
        "target": "ES2020",
        "useDefineForClassFields": true,
        "lib": [
            "ES2020",
            "DOM",
            "DOM.Iterable"
        ],
        "strict": true,
        "module": "ESNext",
        "esModuleInterop": true,
        "allowSyntheticDefaultImports": true,
        "skipLibCheck": true,
        "moduleResolution": "Node",
        "strictPropertyInitialization": false,
        "emitDecoratorMetadata": true,
        "experimentalDecorators": true,
        "baseUrl": ".",
        "paths": {
            "@/*": ["src/*"],
            "@apps/*": ["app/*"],
        },
        "resolveJsonModule": true,
        "allowJs": true,
        "noEmit": true
    },
    "include": [
        "src/**/*.ts",
        "src/**/*.tsx",
        "src/**/*.js",
        "src/**/*.jsx"
, "references/viewer_.js"    ],
    "references": [
        {
            "path": "./tsconfig.node.json"
        }
    ]
}