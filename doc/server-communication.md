# Server Communication Module Documentation

## Overview

The server communication module provides a robust interface for interacting with remote services, handling algorithm processing requests, and managing user data storage. It implements error handling and type safety for reliable communication. The module includes integration with Gradio for any-to-3D model generation.

## Module Structure

```
src/server/
├── types.ts           # Type definitions and interfaces
├── config.ts          # Configuration and environment settings
├── api.ts            # Core API and Gradio communication handlers
├── storage.ts        # User data storage management
└── algorithms.ts     # Remote algorithm processing
```

## Core Components

### 1. API Handler (`api.ts`)

Manages HTTP communication with both the main server and Gradio service.

#### Key Features:

- SSH-based authentication for main server
- Algorithm processing and data storage
- Gradio client integration for 3D generation
- Generic request handling with TypeScript generics
- Comprehensive error handling

#### Basic Usage Example:

```typescript
import { api } from "./server/api";

// Check server health
const isHealthy = await api.checkServerHealth();

// Process an algorithm
const result = await api.processAlgorithm({
  algorithm: "handTracking",
  inputData: frameData,
});

// Store user data
await api.storeUserData({
  userId: "user123",
  sessionData: {
    timestamps: [Date.now()],
    gestures: ["punch"],
  },
});
```

### 2. Algorithm Processing

Handles remote algorithm execution and processing:

```typescript
// Process hand tracking
const result = await api.processAlgorithm({
  algorithm: "handTracking",
  inputData: frameData,
  parameters: {
    confidence: 0.8,
    maxHands: 2,
  },
});

if (result.success) {
  const { result, processingTime } = result;
  // Use the processed data
}
```

### 3. Storage Management

Manages user data persistence:

```typescript
// Save user session data
const response = await api.storeUserData({
  userId: "user123",
  sessionData: {
    timestamps: [Date.now()],
    gestures: ["punch"],
    handTracking: trackingResult,
  },
  metadata: {
    deviceInfo: "browser-info",
    sessionStartTime: Date.now(),
  },
});
```

### 4. Gradio Integration

The module provides a type-safe wrapper around Gradio's API for 3D model generation from multiple sources:

```typescript
// Convert text to 3D
const textResult = await api.anyTo3D({
  source: "text",
  input: "A beautiful watch",
  seed: 42,
});

// Convert image to 3D
const imageResult = await api.anyTo3D({
  source: "image",
  input: "path/to/image.jpg",
  seed: 42,
});

// Convert doll to 3D
const dollResult = await api.anyTo3D({
  source: "doll",
  input: "path/to/doll.glb",
  seed: 42,
});

// Helper functions for individual steps
const imageGen = await api.generateFromText("A beautiful watch", 42);
const meshGen = await api.generateFromImage("path/to/image.jpg", 42);
const dollGen = await api.generateFromDoll("path/to/doll.glb", 42);
const mesh = await api.downloadMesh("text");
```

## Type Definitions

### Core Types

```typescript
// Algorithm processing
interface AlgorithmRequest {
  inputData: any;
  algorithm: string;
  parameters?: Record<string, any>;
}

interface AlgorithmResponse {
  success: boolean;
  result: any;
  error?: string;
  processingTime?: number;
}

// User data storage
interface UserData {
  userId: string;
  sessionData: {
    timestamps: number[];
    gestures: string[];
    handTracking?: Record<string, any>;
    performanceMetrics?: Record<string, any>;
  };
  metadata?: {
    deviceInfo: string;
    sessionStartTime: number;
    sessionEndTime?: number;
  };
}

interface StorageResponse {
  success: boolean;
  error?: string;
  timestamp: number;
}

// 3D Generation types
type ConversionSource = "text" | "image" | "doll";

interface AnyTo3DRequest {
  source: ConversionSource;
  input: string;
  seed?: number;
}

interface AnyTo3DResponse {
  imageResult?: string; // Present for text-to-3D
  videoResult?: string; // Present for text-to-3D
  meshResult: string; // Always present
}

// Generic response wrapper
interface GradioResponse<T> {
  success: boolean;
  data: T;
  error?: string;
}
```

### Gradio Endpoints

```typescript
const ENDPOINTS = {
  text: {
    toImage: "/text_to_image",
    toMesh: "/update_mesh_text",
    download: "/download_text_mesh",
  },
  image: {
    toMesh: "/image_to_mesh",
    download: "/download_image_mesh",
  },
  doll: {
    toMesh: "/image_to_doll",
    download: "/download_doll_mesh",
  },
};
```

## Configuration

### Environment Variables

```env
VITE_SERVER_HOST=*************
VITE_SERVER_PORT=20208
VITE_SSH_KEY_PATH=/path/to/ssh/key
VITE_GRADIO_Anyto3D_ENDPOINT=http://*************:20210
```

### Server Configuration (`config.ts`)

```typescript
{
    host: "localhost",
    port: 20208,
    sshKeyPath: "/path/to/key",
    gradioEndpoint: "http://*************:20210",
    endpoints: {
        algorithm: "/api/algorithm",
        storage: "/api/storage",
        health: "/api/health"
    }
}
```

## Asset Organization

The module organizes generated assets into appropriate subfolders under the `/assets` directory:

```
/assets/
├── images/        # Generated images from text-to-image
├── videos/        # Generated videos from text/mesh processing
├── meshes/        # Generated 3D models (GLB format)
├── textures/      # Generated textures for 3D models
└── backgrounds/   # Environment maps and backgrounds
```

### Asset Paths Configuration

```typescript
const ASSET_PATHS = {
  images: "/assets/images",
  videos: "/assets/videos",
  meshes: "/assets/meshes",
  textures: "/assets/textures",
  backgrounds: "/assets/backgrounds",
};
```

### File Naming Convention

Generated files follow a consistent naming pattern:

- Images: `text_${timestamp}.png`
- Videos: `text_${timestamp}.mp4`
- Meshes: `${source}_${timestamp}.glb` (where source is 'text', 'image', or 'doll')
- Textures: `${modelId}_${textureType}.png`
- Backgrounds: `${name}.hdr`

### Usage Example

```typescript
// Generated assets will be organized automatically
const result = await api.anyTo3D({
  source: "text",
  input: "A beautiful watch",
  seed: 42,
});

if (result.success) {
  const {
    imageResult, // /assets/images/text_1234567890.png
    videoResult, // /assets/videos/text_1234567890.mp4
    meshResult, // /assets/meshes/text_1234567890.glb
  } = result.data;
}

// Individual asset generation
const imageGen = await api.generateFromText("A beautiful watch", 42);
// Result: /assets/images/text_1234567890.png

const meshGen = await api.generateFromImage("/assets/images/input.jpg", 42);
// Result: /assets/meshes/image_1234567890.glb

const dollGen = await api.generateFromDoll("/assets/meshes/input.glb", 42);
// Result: /assets/meshes/doll_1234567890.glb
```

## Error Handling

The module implements comprehensive error handling through the `APIError` class:

```typescript
class APIError extends Error {
  constructor(message: string, public statusCode?: number) {
    super(message);
    this.name = "APIError";
  }
}
```

Error scenarios handled:

- Network errors
- Authentication failures
- Invalid responses
- Algorithm processing errors
- Storage failures
- Gradio service errors
- Type conversion errors

## Best Practices

1. **Error Handling**

   - Always check the `success` flag before using response data
   - Handle both API and Gradio errors appropriately
   - Provide meaningful error messages

2. **Type Safety**

   - Use TypeScript interfaces for request/response types
   - Handle null/undefined cases properly
   - Validate data before sending to server

3. **Configuration**

   - Use environment variables for sensitive data
   - Configure timeouts and retry attempts
   - Set appropriate CORS and security headers

4. **Performance**
   - Implement request caching when appropriate
   - Handle concurrent requests properly
   - Monitor API response times

## Example Usage

Here's a complete example showing how to use various features:

```typescript
import { api } from "./server/api";

async function processUserSession(userId: string) {
  try {
    // Process hand tracking
    const trackingResult = await api.processAlgorithm({
      algorithm: "handTracking",
      inputData: frameData,
    });

    if (trackingResult.success) {
      // Store session data
      await api.storeUserData({
        userId,
        sessionData: {
          timestamps: [Date.now()],
          gestures: ["punch"],
          handTracking: trackingResult.result,
        },
      });

      // Generate 3D model if needed
      const modelResult = await api.anyTo3D({
        source: "text",
        input: "A beautiful watch",
        seed: 42,
      });

      if (modelResult.success) {
        const { meshResult, imageResult, videoResult } = modelResult.data;
        // Handle the generated assets
        return modelResult.data;
      }
    }
  } catch (error) {
    if (error instanceof APIError) {
      console.error(`API Error (${error.statusCode}):`, error.message);
    } else {
      console.error("Unexpected error:", error);
    }
  }
}
```

## Gradio API Information Display

### Running the Gradio API Info Script

The `display_api_info.py` script allows you to view the complete API information from your Gradio server.

#### Prerequisites

1. **Python Environment**
   - Python 3.7 or later
   - `gradio_client` package installed:
     ```bash
     pip install gradio_client
     ```

2. **Server Access**
   - Ensure the Gradio server is running at `http://*************:20210/`
   - Verify network connectivity to the server

#### Script Location
```
src/server/
└── display_api_info.py    # Gradio API info display script
```

#### Running the Script

1. **Direct Execution**
   ```bash
   python src/server/display_api_info.py
   ```

2. **With Environment Variables** (Optional)
   ```bash
   # Unix/Linux/macOS
   export GRADIO_SERVER_URL="http://*************:20210/"
   python src/server/display_api_info.py

   # Windows (Command Prompt)
   set GRADIO_SERVER_URL=http://*************:20210/
   python src/server/display_api_info.py

   # Windows (PowerShell)
   $env:GRADIO_SERVER_URL = "http://*************:20210/"
   python src/server/display_api_info.py
   ```

#### Expected Output

The script will display the complete API information from your Gradio server:

```
Connected to Gradio server at http://*************:20210/
API Information:
{
    "title": "Your Gradio App",
    "description": "A brief description of your app.",
    "inputs": [...],
    "outputs": [...],
    ...
}
```