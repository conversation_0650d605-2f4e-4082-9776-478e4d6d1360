# llmServer.md

# LLM Server Setup: Qwen2.5-Omni Streaming Chat with Integrated ASR & Video

## Goal

Set up a backend system for a web application enabling online avatar streaming chat. The system will handle:

1. Real-time audio streaming input.
2. **ASR**: Transcription using **Qwen2.5-Omni's built-in ASR capabilities** served via vLLM/Letta.
3. **Multi-modal Chat**: Text and video frame input processed by Qwen2.5-Omni.
4. **Streaming LLM Response**: Generating text responses using Qwen2.5-Omni via Letta.
5. **TTS**: Synthesizing the LLM text response into speech using a dedicated TTS service (supporting cloning if needed).
6. **Streaming Output**: Delivering the synthesized speech back to the client.

## Core Components & Technologies

- **LLM & Multi-modal & ASR**: Qwen2.5-Omni (e.g., `Qwen/Qwen2.5-Omni-7B-Chat` or a larger variant) served by **vLLM** for efficient inference. The goal is to leverage its integrated ASR and vision capabilities through the API.
- **Streaming Orchestration**: **Letta AI** acting as a middleware to manage streaming interactions with the vLLM-served Qwen model.
- **TTS**: A separate, high-quality TTS service (e.g., using XTTSv2, Coqui TTS, or another model capable of cloning/good quality) running on a dedicated GPU.
- **Orchestrator**: A central Python service (e.g., using FastAPI with WebSockets) to manage the workflow between the client, ASR/LLM (via Letta), and TTS services.

## GPU Allocation Strategy (Based on 9 GPUs)


| GPU Index | Component                 | Notes                                                                                                   |
| :-------- | :------------------------ | :------------------------------------------------------------------------------------------------------ |
| 0-7 (8x)  | **vLLM: Qwen2.5-Omni**    | Handles LLM inference, visual processing, and**integrated ASR**. Requires significant VRAM and compute. |
| 8 (1x)    | **Dedicated TTS Service** | Handles speech synthesis, potentially including voice cloning.                                          |

*Note: Adjust based on the specific Qwen2.5-Omni model size and observed performance bottlenecks.*

## Detailed Deployment Plan

### Phase 0: Prerequisites & Environment Setup

1. **System Updates & Drivers**:

   * Ensure your Linux distribution is up-to-date.
   * Install the latest NVIDIA drivers compatible with your GPUs and CUDA (e.g., CUDA 12.x recommended for recent models/vLLM features).
   * Install the CUDA Toolkit matching your driver version. Verify with `nvidia-smi` and `nvcc --version`.
2. **Python Environment Management (Using Conda)**:

   * It's recommended to use separate environments for vLLM/Letta, TTS, and the Orchestrator to manage dependencies.

   ```bash
   # Create environments (example names)
   conda create -n qwen_vllm_env python=3.10 -y # vLLM often works well with 3.10
   conda create -n tts_env python=3.10 -y
   conda create -n orchestrator_env python=3.10 -y

   # Activate the first environment for vLLM/Letta setup
   conda activate qwen_vllm_env
   ```
3. **Common Tools**: Install `git`, `wget`, `build-essential`. For C++ compilation issues (like with vLLM or dependencies):

   ```bash
   # Potentially needed for vLLM compilation if system GCC is too old
   conda install -c conda-forge "gxx>=9,<10"
   ```

### Phase 1: Deploy Qwen2.5-Omni Service with vLLM

1. **Activate Environment**:

   ```bash
   conda activate qwen_vllm_env
   ```
2. **Install vLLM**: Installing from source is often recommended for the latest features and compatibility.

   ```bash
   # Clone vLLM repository
   export MAX_JOBS=4 # Limits parallel build jobs

   git clone -b qwen2_omni_public https://github.com/fyabc/vllm.git
   cd vllm
   git checkout de8f43fbe9428b14d31ac5ec45d065cd3e5c3ee0
   pip install setuptools_scm torchdiffeq resampy x_transformers qwen-omni-utils accelerate
   pip install -r requirements/cuda.txt
   pip install --upgrade setuptools wheel
   conda install -c conda-forge libsndfile -y
   pip install ninja cmake
   conda install -c conda-forge "gxx>=9,<10" -y 
   conda install -c conda-forge libsndfile -y
   pip install transformers==4.52.3
   pip install .
   ```

   Replace with

   ```bash
       -r common.txt

       numba == 0.60.0; python_version == '3.9' # v0.61 doesn't support Python 3.9. Required for N-gram speculative decoding
       numba == 0.61; python_version > '3.9'

       # Dependencies for NVIDIA GPUs
       ray[cgraph]>=2.43.0, !=2.44.* # Ray Compiled Graph, required for pipeline parallelism in V1.
       # These must be updated alongside torch
       xformers --index-url https://download.pytorch.org/whl/cu118
   ```

   ```bash
   pip install vllm>=0.8.5.post1
   pip install flashinfer-python -i https://flashinfer.ai/whl/cu121/torch2.6
   pip install flash-attn --no-build-isolation # for flash attention acceleration
   pip install .
   pip install git+https://github.com/huggingface/transformers

   # Install vLLM (use precompiled kernels if available, otherwise it builds)
   # Check vLLM docs for specific dependencies first (like ninja, cmake)
   pip install ninja cmake
   # If ninja build fails with OOM error:

   # If you encounter 'libsndfile.so' errors later (often with audio libs):
   # conda install -c conda-forge libsndfile
   ```
3. **Install Qwen Dependencies**:

   ```bash
   pip install transformers accelerate # Core HF libraries
   # Check Qwen2.5-Omni repo for any specific utils needed
   # pip install qwen-vl-utils # Example, check if Omni needs something similar
   ```
4. **Run vLLM OpenAI-Compatible Server for Qwen2.5-Omni**:

   * **Crucial**: Refer to the Qwen2.5-Omni (`https://github.com/QwenLM/Qwen2.5-Omni`) and vLLM documentation (`https://docs.vllm.ai/en/latest/serving/openai_compatible_server.html` and multi-modal sections) for the *exact* model identifier and any required flags to enable **Omni capabilities (Vision + potentially ASR)**. The command below is a template.
   * Set GPU visibility and model cache.

   ```bash
   # Set environment variables
   export CUDA_VISIBLE_DEVICES=0,1,4,5 # Use first 7 GPUs
   export HF_HOME=/remote-home/share # Set cache directory
   export HF_ENDPOINT=https://hf-mirror.com
   # export HF_PATHTOQWEN=/remote-home/share/hub/models--Qwen--Qwen2.5-Omni-7B
   export HF_PATHTOQWEN=Qwen/Qwen2.5-Omni-7B
   # export HF_PATHTOQWEN=Qwen/Qwen2.5-VL-72B-Instruct

   # Download Model if network is not ok, chekc [doc](https://hf-mirror.com/)
   # wget https://hf-mirror.com/hfd/hfd.sh
   # chmod a+x hfd.sh
   # ./hfd.sh Qwen/Qwen2.5-Omni-7B

   # pip install modelscope
   # modelscope download --model Cylingo/Xinyuan-LLM-14B-0428


   # Launch the server [doc](https://docs.vllm.ai/en/latest/serving/openai_compatible_server.html)
   # 24 G is not enough so using tensor parallelism
   vllm serve ${HF_PATHTOQWEN} \
       --port 8888 --host 0.0.0.0 \
       --dtype bfloat16 -tp 4 --gpu_memory_utilization 0.9 

   export CUDA_VISIBLE_DEVICES=0,1,2,3
   VLLM_USE_MODELSCOPE=true vllm serve ${HF_PATHTOQWEN} --port 8888 --host 0.0.0.0 \
   --dtype bfloat16 -tp 4 --gpu_memory_utilization 0.9 \
   --enable-auto-tool-choice \
   --tool-call-parser hermes

   export CUDA_VISIBLE_DEVICES=0,1,2,3,4
   SGLANG_USE_MODELSCOPE=true python -m sglang.launch_server --model-path Cylingo/Xinyuan-LLM-14B-0428 \
   --port 8888 --host 0.0.0.0 --tp-size 4

               # NOTE: to use the /chat/completions endpoint, you need to specify extra flags on vLLM startup
           # see: https://docs.vllm.ai/en/latest/getting_started/examples/openai_chat_completion_client_with_tools.html
           # e.g. "... --enable-auto-tool-choice --tool-call-parser hermes"
    # We are supporting streaming responses for OpenAI and backends that support /v1/chat/completions OpenAI-style endpoints with function calling support.

   # Keep this terminal running or use a process manager (screen, tmux, systemd)
   ```

   * **Note on ASR/Vision**: The ability to use Qwen's built-in ASR and vision via vLLM's OpenAI API depends heavily on vLLM's integration support. You **must** verify from their documentation how to format API requests to send audio/video data and trigger the desired modality. If direct ASR support isn't available this way, you'll need a separate ASR service (like Phase 1 in your original draft).

### Phase 2: Configure and Run Letta AI

1. **Activate Environment** (can often share with vLLM if dependencies don't clash):

   ```bash
   conda activate qwen_vllm_env
   ```
2. **Install Letta AI**: Installing from source might be needed for latest provider features.

   ```bash
   conda create -n letta python=3.12 -y
   conda activate letta
    # Install poetry if you haven't already
   python3 -m pip install --user pipx
   python3 -m pipx ensurepath
   pipx install poetry --force

   # Clone Letta repo (optional, can also pip install)
   git clone https://github.com/letta-ai/letta.git
   cd letta
   vim ~/.config/pypoetry/config.toml
   git checkout remotes/origin/release-0.7.0
   ```

poetry source add --priority=primary mirrors https://pypi.tuna.tsinghua.edu.cn/simple/

poetry install --all-extras # Installs letta and dependencies

or mem0:https://app.mem0.ai/dashboard/get-started for api

export MEMGRAPH_URI

```
3. **Configure Letta for vLLM Provider**: Letta needs to know where the vLLM server is and which model to use. Refer to `https://docs.letta.com/guides/server/providers/vllm`. This is typically done via environment variables:

   ```bash

   ```
4. **Run Letta Server**: Refer to `https://docs.letta.com/guides/agents/streaming`.

   ```bash

   export VLLM_API_BASE="http://0.0.0.0:8888/v1"
   # Run the Letta server (usually on a different port)
   poetry run letta server --host 0.0.0.0 --port 80 # Add --ade if needed for development features
   # Setup remote server ADE [guide](https://docs.letta.com/guides/server/remote)
   # at your local machine terminal:
   ssh -L 8283:0.0.0:80 -p 20092 root@***********
   # clean 8283 first: 
   lsof -i :8283


   # Keep this terminal running or use a process manager
   ```
   The Orchestrator will communicate with Letta on `http://localhost:8283`.

### Phase 3: Deploy Dedicated TTS Service

1. **Activate Environment**:

   ```bash
   conda activate tts_env
   ```
2. **Choose and Install TTS**: Example using XTTSv2 (powerful, good cloning):

   ```bash
   pip install TTS fastapi uvicorn websockets pydub # pydub might be needed for audio handling
   ```
   * XTTSv2 downloads models on first run. Ensure internet access or download manually.
3. **Create TTS Server (`tts_server.py`)**[doc](https://github.com/HuiResearch/Fast-Spark-TTS):

   ```bash
   vgit clone https://github.com/HuiResearch/FlashTTS.git
   cd FlashTTS


   conda create -n fastsparktts python=3.12 -y
   conda activate fastsparktts
   pip install --upgrade pip
   pip install vllm
   pip install flashtts==0.17


   wget https://hf-mirror.com/hfd/hfd.sh
   chmod a+x hfd.sh
   ./hfd.sh SparkAudio/Spark-TTS-0.5B
   ./hfd.sh canopylabs/3b-zh-pretrain-research_release
   huggingface-cli download ByteDance/MegaTTS3 --local-dir

   git clone https://www.modelscope.cn/SparkAudio/Spark-TTS-0.5B.git
   pip install modelscope
   modelscope download --model SparkAudio/Spark-TTS-0.5B --local_dir ./Spark-TTS-0.5B 

   modelscope download --model canopylabs/orpheus-3b-0.1-ft

   CUDA_VISIBLE_DEVICES=1
   flashtts serve \
   -m Spark-TTS-0.5B \
   -b vllm \
   --role_dir data/roles \
   --llm_device cuda \
   --tokenizer_device cuda \
   --detokenizer_device cuda \
   --wav2vec_attn_implementation sdpa \
   --llm_attn_implementation sdpa \
   --torch_dtype "bfloat16" \
   --max_length 32768 \
   --llm_gpu_memory_utilization 0.9 \
   --host 0.0.0.0 \
   --port 8000
   # --port 83 

   export CUDA_VISIBLE_DEVICES=3
       flashtts serve \
       --model_path orpheus-3b-0.1-ft \
       --backend vllm \
       --snac_path snac_24khz \
       --lang english \
       --llm_device cuda \
       --detokenizer_device cuda \
       --llm_attn_implementation sdpa \
       --torch_dtype "float16" \
       --max_length 8192 \
       --llm_gpu_memory_utilization 0.6 \
       --host 0.0.0.0 \
       --port 83

   # index-tts-vllm


   wget https://hf-mirror.com/hfd/hfd.sh
   chmod a+x hfd.sh
   ./hfd.sh IndexTeam/IndexTTS-1.5
   VLLM_USE_V1=0 python api_server.py --model_dir IndexTeam/IndexTTS-1.5 --port 83 --gpu_memory_utilization 0.8
   ```
   * **Important:** This TTS server example is basic. For production, you'll need robust error handling, proper async implementation, and potentially chunked streaming generation/sending for lower latency. Voice cloning requires providing a `speaker_wav`.
4. **Run TTS Server**:

   ```bash
    # Use the last GPU
   python tts_server.py # Or use: uvicorn tts_server:app --host 0.0.0.0 --port 8002
   ```

### Phase 4: Create the Orchestrator Service

1. **Activate Environment**:

   ```bash
   export SENSEVOICE_DEVICE=cuda:2
   fastapi run --port 84
   ```
2. **Install Dependencies**:

   ```bash
   pip install fastapi uvicorn websockets httpx aiohttp # httpx/aiohttp for async requests to Letta/TTS
   ```
3. **Create Orchestrator Server (`orchestrator.py`)**: This is the central hub.

   ```python
   # orchestrator.py (Conceptual Outline)
   import asyncio
   from fastapi import FastAPI, WebSocket, WebSocketDisconnect
   import websockets # Use websockets library for client connections to Letta/TTS
   import httpx # Or aiohttp for making calls if Letta/TTS use HTTP endpoints
   import base64 # If sending video frames as base64

   app = FastAPI()

   LETTA_WS_URL = "ws://localhost:8283/..." # Check Letta docs for the correct agent streaming WS endpoint
   TTS_WS_URL = "ws://localhost:8002/ws/tts"

   # Define how to structure messages for Qwen-Omni via Letta
   # This depends HEAVILY on how vLLM exposes Omni capabilities and how Letta wraps it.
   # You MUST consult Qwen/vLLM/Letta docs for the correct API format.
   def format_asr_request(audio_chunk):
       # Placeholder: How should audio be sent for Qwen's ASR via Letta/vLLM?
       # return {"type": "audio", "data": base64.b64encode(audio_chunk).decode('utf-8'), "encoding": "base64"}
       # Or maybe Letta handles raw bytes directly?
       return audio_chunk # Or {"task": "asr", "audio": audio_chunk} -> CHECK DOCS!

   def format_llm_request(text, video_frame_base64=None):
       # Placeholder: How to send text and optional video frame?
       content = [{"type": "text", "text": text}]
       if video_frame_base64:
           content.append({"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{video_frame_base64}"}})
       # The exact structure depends on vLLM's OpenAI API implementation for multi-modal
       return {"model": "Qwen/Qwen2.5-Omni-Chat", "messages": [{"role": "user", "content": content}]} # Example structure

   @app.websocket("/ws/chat")
   async def websocket_chat_endpoint(client_websocket: WebSocket):
       await client_websocket.accept()
       print("Client connected.")

       letta_conn = None
       tts_conn = None

       try:
           # Establish connections to downstream services
           # Use websockets library directly for more control over streaming
           letta_conn = await websockets.connect(LETTA_WS_URL) # Replace with correct Letta WS endpoint
           tts_conn = await websockets.connect(TTS_WS_URL)
           print("Connected to Letta and TTS services.")

           # Start listener tasks for responses from Letta and TTS
           # Letta -> Client (Text or direct audio if Letta integrates TTS?)
           async def listen_letta():
                try:
                   async for message in letta_conn:
                       # Process message from Letta (might be ASR result or LLM response)
                       # This logic depends heavily on Letta's streaming protocol
                       print(f"Received from Letta: {message[:100]}...") # Debug
                       # If it's ASR text -> Forward to LLM part
                       # If it's LLM text -> Forward to TTS
                       # If it's final result -> maybe send status to client?
                       # Example: Assuming Letta sends LLM text chunks
                       await tts_conn.send(message) # Send text chunk to TTS
                except websockets.exceptions.ConnectionClosedOK:
                    print("Letta connection closed normally.")
                except Exception as e:
                    print(f"Error listening to Letta: {e}")

           # TTS -> Client (Audio Stream)
           async def listen_tts():
                try:
                   async for message in tts_conn:
                       # Message should be audio bytes
                       await client_websocket.send_bytes(message)
                except websockets.exceptions.ConnectionClosedOK:
                    print("TTS connection closed normally.")
                except Exception as e:
                    print(f"Error listening to TTS: {e}")

           # Run listener tasks concurrently
           listener_tasks = asyncio.gather(listen_letta(), listen_tts())

           # Handle messages from Client
           while True:
               message = await client_websocket.receive()

               if "bytes" in message:
                   audio_chunk = message["bytes"]
                   # Send audio chunk to Letta for ASR
                   # Format depends on Letta/vLLM API for Qwen ASR
                   asr_payload = format_asr_request(audio_chunk)
                   await letta_conn.send(asr_payload) # Or send via specific Letta method

               elif "text" in message:
                    # Could be user typing text, or potentially control messages
                    text_input = message["text"]
                    video_frame_base64 = message.get("video_frame") # Optional video frame

                    # Send text (and maybe video) to Letta for LLM processing
                    llm_payload = format_llm_request(text_input, video_frame_base64)
                    # Structure likely needs conversion to Letta's expected format
                    await letta_conn.send(str(llm_payload)) # Send as string/JSON

       except WebSocketDisconnect:
           print("Client disconnected.")
       except websockets.exceptions.ConnectionClosedError as e:
           print(f"Connection closed unexpectedly: {e}")
       except Exception as e:
           print(f"Orchestrator Error: {e}")
           await client_websocket.close(code=1011)
       finally:
           if letta_conn: await letta_conn.close()
           if tts_conn: await tts_conn.close()
           if 'listener_tasks' in locals() and listener_tasks: listener_tasks.cancel()
           print("Cleaned up connections.")


   if __name__ == "__main__":
       import uvicorn
       # Note: Running uvicorn directly might not be ideal for websockets library usage within FastAPI
       # Consider structuring with separate processes or using FastAPI's WebSocket support more deeply
       # if sticking purely to FastAPI patterns.
       uvicorn.run(app, host="0.0.0.0", port=8080)

   ```
   * **Critical**: This Orchestrator outline is highly conceptual. The exact implementation details (`LETTA_WS_URL`, message formats `format_asr_request`, `format_llm_request`, how Letta streams back ASR vs LLM results) **depend entirely on the Letta AI agent's implementation and the vLLM API's specifics for Qwen-Omni**. You will need to adapt this significantly based on documentation and experimentation.
4. **Run Orchestrator Server**:

   ```bash
   # No specific GPU needed, runs on CPU
   python orchestrator.py # Or use: uvicorn orchestrator:app --host 0.0.0.0 --port 8080
   ```

### Phase 5: Client Implementation (Brief Considerations)

* Use JavaScript `WebSocket` API to connect to the Orchestrator (`ws://your_server_ip:8080/ws/chat`).
* Use `navigator.mediaDevices.getUserMedia` to get audio/video streams.
* Use `MediaRecorder` API (for audio) or `Canvas`/`ImageCapture` (for video) to capture chunks/frames.
* Send audio data as binary (`ws.send(blob)`).
* Send text input as JSON string (`ws.send(JSON.stringify({text: "hello"}))`).
* Send video frames (e.g., base64 encoded JPEG) along with text or as separate messages (`ws.send(JSON.stringify({text: "describe this", video_frame: base64data}))`).
* Receive audio bytes from the WebSocket (`onmessage` event, check `event.data` type). Use `AudioContext` and `decodeAudioData` (or libraries like `pcm-player`) to play back the received audio stream seamlessly.
* Update avatar lip-sync based on received audio or potential viseme data (if TTS provides it).

### Phase 6: Testing, Tuning & Deployment

1. **Incremental Testing**: Test each component individually (vLLM API, Letta connection, TTS server, Orchestrator logic) before full end-to-end testing.
2. **API Verification**: Use tools like `curl`, Postman, or Python scripts (`requests`, `websockets`) to directly interact with the vLLM, Letta, and TTS endpoints to confirm expected behavior and data formats. **This is crucial for debugging the multi-modal and ASR aspects.**
3. **Performance Profiling**: Use `nvidia-smi`, `htop`, and logging within services to monitor GPU/CPU utilization, VRAM usage, and end-to-end latency.
4. **Tuning**: Adjust vLLM parameters (e.g., `max_num_seqs`, `gpu_memory_utilization`), Letta configurations, audio chunk sizes, video frame rates/resolutions to balance performance and quality.
5. **Process Management**: Use `systemd`, `supervisor`, or Docker Compose to manage the different services (vLLM, Letta, TTS, Orchestrator) for reliable deployment.
6. **Logging & Monitoring**: Implement robust logging in all services and consider using monitoring tools (e.g., Prometheus + Grafana) for production environments.

## FAQ / Notes

* **Qwen ASR/Vision via API**: The feasibility of using Qwen2.5-Omni's *integrated* ASR and vision through the vLLM OpenAI-compatible API is the biggest uncertainty. **Verify this capability and the required API format in the latest vLLM and Qwen documentation.** If not directly supported, you must revert to a separate ASR service.
* **Letta Agent Logic**: The Letta AI server acts as middleware. You might need to write a custom Letta *agent* (`Agent` class in Letta) to handle the specific logic of receiving audio, calling the vLLM ASR function, then calling the vLLM chat function with text/video, and streaming back results. The configuration shown assumes Letta transparently passes requests based on environment variables, which might be too simplistic.
* **Dependencies & Build Issues**:
  * `conda install -c conda-forge "gxx>=9,<10" -y`: Needed if C++ compilation fails due to old GCC.
  * `export MAX_JOBS=1`: Use if `ninja` build runs out of memory during vLLM installation.
  * `conda install -c conda-forge libsndfile`: Install if you see `OSError: cannot load library 'libsndfile.so'`.
* **Streaming Complexity**: True end-to-end, low-latency streaming requires careful handling of asynchronous operations, backpressure, and chunking/buffering in the client, orchestrator, and potentially within the TTS service.

---
