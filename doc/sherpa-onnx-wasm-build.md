# Building sherpa-onnx WebAssembly Files from ONNX Models

This document explains how to use the automated build system for creating WebAssembly (WASM) files from ONNX models for the sherpa-onnx speech recognition library.

## Overview

The WASM build system automates the process of:
1. Setting up Emscripten SDK
2. Cloning and building sherpa-onnx with WASM support
3. Downloading and preparing the ONNX model
4. Building the WASM files
5. Copying the output to the appropriate location in the project

## Prerequisites

- Node.js (v16 or later)
- Bash shell environment
- Git
- wget or curl
- CMake (version 3.14 or higher)
  - macOS: `brew install cmake`
  - Ubuntu/Debian: `sudo apt install cmake`
  - Windows: Download from [cmake.org](https://cmake.org/download/)
- Internet connection to download dependencies

## Configuration

The WASM build system is configured in `src/config/wasm-build.js`. This file contains settings for:
- Emscripten SDK version
- sherpa-onnx repository URL
- Model URLs and paths
- Output directories

## Building WASM Files

### Using the Sherpa-ONNX Manager

The simplest way to build WASM files is to use the Sherpa-ONNX Manager script:

```bash
# Build the default model (dolphin)
node scripts/sherpa-onnx-manager.js build

# Build a specific model
node scripts/sherpa-onnx-manager.js build --model dolphin
node scripts/sherpa-onnx-manager.js build --model senseVoice

# Build a custom model
node scripts/sherpa-onnx-manager.js build --model mymodel --url https://example.com/model.tar.bz2 --path tts/mymodel

# Force rebuild even if files exist
node scripts/sherpa-onnx-manager.js build --model dolphin --force

# List available models
node scripts/sherpa-onnx-manager.js list

# Clean up temporary build files
node scripts/sherpa-onnx-manager.js clean
```

### Using npm Scripts

You can also use the provided npm scripts:

```bash
# Build the default model (dolphin)
npm run build:wasm

# Build a specific model
npm run build:wasm:dolphin
npm run build:wasm:senseVoice

# Force rebuild even if files exist
npm run build:wasm -- --force
```

### Using the Build Script Directly

You can also run the build script directly:

```bash
# Build the default model
node scripts/build-wasm.js

# Build a specific model
node scripts/build-wasm.js --model dolphin
node scripts/build-wasm.js --model senseVoice

# Force rebuild even if files exist
node scripts/build-wasm.js --force

# Show help
node scripts/build-wasm.js --help
```

### Using the Shell Script Directly

For more advanced usage, you can run the shell script directly:

```bash
# Build the default model
bash scripts/build-sherpa-onnx-wasm.sh

# Build a specific model
bash scripts/build-sherpa-onnx-wasm.sh --model dolphin
bash scripts/build-sherpa-onnx-wasm.sh --model senseVoice

# Build a custom model
bash scripts/build-sherpa-onnx-wasm.sh --model mymodel --url https://example.com/model.tar.bz2 --path tts/mymodel

# Force rebuild even if files exist
bash scripts/build-sherpa-onnx-wasm.sh --force

# Show help
bash scripts/build-sherpa-onnx-wasm.sh --help
```

## Output Files

The build process generates the following files:

- `public/models/wasm/{modelType}/sherpa-onnx-asr.js` - JavaScript interface
- `public/models/wasm/{modelType}/sherpa-onnx-asr.wasm` - WebAssembly binary
- `public/models/wasm/{modelType}/sherpa-onnx-asr.data` - Additional data files (if present)

It also copies the model files to:

- `public/models/{modelPath}/model.onnx` (or `model.int8.onnx` for Dolphin model)
- `public/models/{modelPath}/tokens.txt`

## Integration with sherpaOnnxLoader

The `sherpaOnnxLoader.js` has been updated to prioritize WASM files and provide clear instructions when they're missing:

1. When initializing, it first checks if custom WASM files exist
2. If WASM files are found, it checks if the model files exist
3. If WASM files are not found, it shows instructions for building them instead of downloading ONNX files
4. It provides clear error messages with build commands to help users resolve issues

This approach ensures that users are guided to build the WASM files first before attempting to use the speech recognition features.

## Troubleshooting

### CMake Not Found

The build script now checks for CMake before starting the build process. If CMake is not found, you'll see an error message like:

```
Error: CMake is not installed or not in PATH
Please install CMake before continuing.

On macOS, you can install it with:
  brew install cmake

On Ubuntu/Debian, you can install it with:
  sudo apt-get install cmake

On Windows, you can download it from:
  https://cmake.org/download/
```

Install CMake using the instructions provided and try again.

### Build Fails with Emscripten Error

If the build fails with an error related to Emscripten, try using a different version of Emscripten. You can modify the `EMSDK_VERSION` in `src/config/wasm-build.js`.

```bash
# Try with a specific version known to work
npm run build:wasm -- --force
```

### Model Download or Extraction Fails

If the model download or extraction fails, you might see errors like:

```
tar: Error opening archive: Failed to open 'sherpa-onnx-dolphin-small-ctc-multi-lang-int8-2025-04-02.tar.bz2'
mv: rename /model.int8.onnx to model.onnx: No such file or directory
```

To resolve this:

1. Manually download the model from the URL specified in `src/config/wasm-build.js`
2. Extract it to the appropriate location in `public/models/`
3. Try running the build again with the `--force` option

```bash
# Example for manually downloading and extracting the dolphin model
cd public/models/tts/dolphin-small-ctc-multi-lang
wget https://github.com/k2-fsa/sherpa-onnx/releases/download/asr-models/sherpa-onnx-dolphin-small-ctc-multi-lang-int8-2025-04-02.tar.bz2
tar xf sherpa-onnx-dolphin-small-ctc-multi-lang-int8-2025-04-02.tar.bz2
cp sherpa-onnx-dolphin-small-ctc-multi-lang-int8-2025-04-02/model.int8.onnx ./model.onnx
cp sherpa-onnx-dolphin-small-ctc-multi-lang-int8-2025-04-02/tokens.txt ./
```

### WASM Files Not Found

If the WASM files are not found, check that the output directories exist and have the correct permissions. You can also try forcing a rebuild with the `--force` option.

### File Name Mismatch

The sherpa-onnx build process may produce files with different names than expected. The build script has been updated to handle both naming conventions:

- `sherpa-onnx-asr.js` / `sherpa-onnx-asr.wasm` / `sherpa-onnx-asr.data`
- `sherpa-onnx-wasm-main-asr.js` / `sherpa-onnx-wasm-main-asr.wasm` / `sherpa-onnx-wasm-main-asr.data`

If you encounter a file name mismatch error, check the actual file names in the build output directory and update the script accordingly. The build log will show the contents of the build output directory to help diagnose the issue.

### Model Files Not Found in Public Directory

If the build completes successfully but the model files are not copied to the public directory, you may need to manually create the directories and copy the files:

```bash
# Create the model directory
mkdir -p public/models/tts/dolphin-small-ctc-multi-lang

# Copy the model files
cp temp/sherpa-onnx-build/dolphin/sherpa-onnx/wasm/asr/assets/model.onnx public/models/tts/dolphin-small-ctc-multi-lang/
cp temp/sherpa-onnx-build/dolphin/sherpa-onnx/wasm/asr/assets/tokens.txt public/models/tts/dolphin-small-ctc-multi-lang/

# Copy the WASM files to the same directory
cp temp/sherpa-onnx-build/dolphin/sherpa-onnx/build-wasm-simd-asr/install/bin/wasm/asr/sherpa-onnx-wasm-main-asr.js public/models/tts/dolphin-small-ctc-multi-lang/sherpa-onnx-asr.js
cp temp/sherpa-onnx-build/dolphin/sherpa-onnx/build-wasm-simd-asr/install/bin/wasm/asr/sherpa-onnx-wasm-main-asr.wasm public/models/tts/dolphin-small-ctc-multi-lang/sherpa-onnx-asr.wasm
cp temp/sherpa-onnx-build/dolphin/sherpa-onnx/build-wasm-simd-asr/install/bin/wasm/asr/sherpa-onnx-wasm-main-asr.data public/models/tts/dolphin-small-ctc-multi-lang/sherpa-onnx-asr.data
```

This issue can occur if the build script doesn't have the correct paths or if there are permission issues with the directories.

### Important Note on Directory Structure

All files (both model files and WASM files) **must** be placed in the `public/models/tts/` directory structure for the application to find them correctly. The directory structure is as follows:

```
public/models/
└── tts/
    ├── dolphin-small-ctc-multi-lang/
    │   ├── model.onnx
    │   ├── tokens.txt
    │   ├── sherpa-onnx-asr.js
    │   ├── sherpa-onnx-asr.wasm
    │   └── sherpa-onnx-asr.data
    └── sherpa-onnx-sense-voice-zh-en-ja-ko-yue-2024-07-17/
        ├── model.onnx
        ├── tokens.txt
        ├── sherpa-onnx-asr.js
        ├── sherpa-onnx-asr.wasm
        └── sherpa-onnx-asr.data
```

The application looks for both model files and WASM files in the same directory under `tts/`. This simplifies the file structure and makes it easier to manage the files.

### Directory Already Exists Error

If you encounter an error like:

```
fatal: destination path 'emsdk' already exists and is not an empty directory.
```

This means the build script is trying to clone a repository into a directory that already exists. The script has been updated to check if directories already exist and skip the clone step if they do. If you still encounter this error, you can try:

1. Removing the existing directory: `rm -rf temp/sherpa-onnx-build/dolphin/emsdk`
2. Running the build script again

Or, if you want to keep the existing directory:

1. Navigate to the directory: `cd temp/sherpa-onnx-build/dolphin/emsdk`
2. Update it manually: `git pull`
3. Continue the build process manually

### Build Script Permissions

If you get a permission denied error when running the build script, make sure it's executable:

```bash
chmod +x scripts/build-sherpa-onnx-wasm.sh
chmod +x scripts/build-wasm.js
```

## Advanced Usage

### Resuming Interrupted Builds

The build process now keeps all build files in a temporary directory (`./temp/sherpa-onnx-build/{modelType}`) for resuming interrupted builds and debugging purposes. If a build fails or is interrupted, you can:

1. Navigate to the build directory: `cd ./temp/sherpa-onnx-build/{modelType}`
2. Examine the state of the build
3. Make necessary fixes
4. Continue the build manually

For example, if the model download failed, you can manually download the model and place it in the correct location, then continue with the build process.

```bash
# Example of resuming a build for the dolphin model
cd ./temp/sherpa-onnx-build/dolphin/sherpa-onnx/wasm/asr/assets

# Manually download and extract the model
wget https://github.com/k2-fsa/sherpa-onnx/releases/download/asr-models/sherpa-onnx-dolphin-small-ctc-multi-lang-int8-2025-04-02.tar.bz2
tar xf sherpa-onnx-dolphin-small-ctc-multi-lang-int8-2025-04-02.tar.bz2

# Copy the model files
cp sherpa-onnx-dolphin-small-ctc-multi-lang-int8-2025-04-02/model.int8.onnx model.onnx
cp sherpa-onnx-dolphin-small-ctc-multi-lang-int8-2025-04-02/tokens.txt ./

# Return to the sherpa-onnx directory and continue the build
cd ../../../
./build-wasm-simd-asr.sh
```

### Adding a New Model

To add a new model:

1. Build the model using the Sherpa-ONNX Manager or build script:

```bash
node scripts/sherpa-onnx-manager.js build --model mymodel --url https://example.com/model.tar.bz2 --path tts/mymodel
```

2. Add the model configuration to `src/config/models.js`:

```javascript
export const MODEL_CONFIGS = {
  // ...
  sherpaOnnx: {
    // ...
    models: {
      // ...
      mymodel: {
        modelPath: 'tts/mymodel',
        fallbackPath: 'https://example.com/model.tar.bz2',
        configType: 'dolphin', // or 'sense-voice' depending on the model type
        sampleRate: 16000,
        featureDim: 80,
        // Other model-specific options
      }
    }
  }
};
```

3. Update the SherpaOnnxLoader class to support the new model (optional):

```bash
node scripts/update-sherpa-onnx-loader.js
```

This script makes the SherpaOnnxLoader class more generalizable to support custom models.

### Customizing the Build Process

You can customize the build process by modifying the `scripts/build-sherpa-onnx-wasm.sh` script. For example, you can change the build options for sherpa-onnx or add additional steps to the build process.

### Cleaning Up Build Files

If you want to clean up the build files after a successful build, you can use the Sherpa-ONNX Manager:

```bash
# Clean up files for a specific model
node scripts/sherpa-onnx-manager.js clean --model dolphin

# Clean up all build files
node scripts/sherpa-onnx-manager.js clean --model all
```

Or manually remove the directories:

```bash
# Remove files for a specific model
rm -rf ./temp/sherpa-onnx-build/{modelType}

# Remove all build files
rm -rf ./temp/sherpa-onnx-build
```

## New Scripts

This project includes several new scripts to make working with Sherpa-ONNX models easier:

### sherpa-onnx-manager.js

A unified script for managing Sherpa-ONNX models:

```bash
node scripts/sherpa-onnx-manager.js [command] [options]
```

Commands:
- `build`: Build a sherpa-onnx model
- `copy`: Copy model files to the correct location
- `list`: List available models
- `clean`: Clean up temporary build files

Options:
- `--model`: Model type (e.g., dolphin, senseVoice)
- `--url`: URL to download the model from (for custom models)
- `--path`: Path where model files will be stored (for custom models)
- `--force`: Force rebuild even if files exist
- `--help`: Show help message

### copy-wasm-files.js

A script to copy WebAssembly files to the correct location:

```bash
node scripts/copy-wasm-files.js [OPTIONS]
```

Options:
- `--model MODEL_TYPE`: Model type (e.g., dolphin, senseVoice)
- `--source SOURCE_DIR`: Source directory for build files
- `--dest DEST_DIR`: Destination directory

### update-sherpa-onnx-loader.js

A script to update the SherpaOnnxLoader class to support custom models:

```bash
node scripts/update-sherpa-onnx-loader.js
```

This script makes the SherpaOnnxLoader class more generalizable to support custom models and improves the createRecognizer function to handle different model types more flexibly.

## Resources

- [sherpa-onnx GitHub Repository](https://github.com/k2-fsa/sherpa-onnx)
- [Emscripten SDK](https://emscripten.org/)
- [WebAssembly](https://webassembly.org/)
