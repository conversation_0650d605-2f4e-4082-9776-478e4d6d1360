# UI Components Documentation

## Overview

The UI system is built with modularity and reusability in mind, providing a consistent user experience across the application. This document outlines the available components, their usage, and customization options.

## Component Library

### Basic Components

#### Button (`components/Button.js`)

```javascript
import { Button } from '../ui/components/Button';

// Basic usage
<Button onClick={() => console.log('Clicked!')}>
  Click Me
</Button>

// With variants
<Button variant="primary" size="large" disabled={false}>
  Primary Action
</Button>

// Available props
{
  variant: 'primary' | 'secondary' | 'ghost',
  size: 'small' | 'medium' | 'large',
  disabled: boolean,
  loading: boolean,
  icon?: ReactNode,
  onClick: () => void
}
```

#### Slider (`components/Slider.js`)

```javascript
import { Slider } from '../ui/components/Slider';

// Basic range slider
<Slider
  min={0}
  max={100}
  value={50}
  onChange={(value) => console.log('New value:', value)}
/>

// With custom step and labels
<Slider
  min={0}
  max={1}
  step={0.1}
  value={0.5}
  labels={['Min', 'Max']}
  onChange={(value) => updateOpacity(value)}
/>
```

#### Toggle (`components/Toggle.js`)

```javascript
import { Toggle } from '../ui/components/Toggle';

// Basic toggle
<Toggle
  checked={isEnabled}
  onChange={(checked) => setIsEnabled(checked)}
  label="Enable Feature"
/>

// With custom styling
<Toggle
  checked={isDarkMode}
  onChange={toggleTheme}
  label="Dark Mode"
  activeColor="#007AFF"
  size="large"
/>
```

#### Modal (`components/Modal.js`)

```javascript
import { Modal } from '../ui/components/Modal';

// Basic modal
<Modal
  isOpen={showModal}
  onClose={() => setShowModal(false)}
  title="Settings"
>
  <div>Modal content goes here</div>
</Modal>

// With custom actions
<Modal
  isOpen={showConfirm}
  onClose={() => setShowConfirm(false)}
  title="Confirm Action"
  actions={[
    { label: 'Cancel', onClick: () => setShowConfirm(false) },
    { label: 'Confirm', onClick: handleConfirm, variant: 'primary' }
  ]}
>
  <p>Are you sure you want to proceed?</p>
</Modal>
```

## UI Panels

### Settings Panel (`settingsPanel.js`)

A configurable settings panel for managing application settings with categories and different input types.

```javascript
import { SettingsPanel } from "../ui/settingsPanel";

// Basic usage
const settingsPanel = new SettingsPanel((settings) => {
  // Handle settings changes
  console.log("Settings updated:", settings);
});

// Available settings configuration
const settings = {
  "Gesture Detection": {
    punchVelocityThreshold: {
      value: 0.15,
      min: 0.05,
      max: 0.3,
      step: 0.01,
      type: "range",
    },
    punchDistanceThreshold: {
      value: 0.1,
      min: 0.05,
      max: 0.2,
      step: 0.01,
      type: "range",
    },
  },
  "Visual Effects": {
    showHandMesh: {
      value: true,
      type: "checkbox",
    },
  },
};
```

### Basic Settings (`settings.js`)

A simpler settings interface for basic application configuration.

```javascript
import { Settings } from "../ui/settings";

// Basic usage
const settings = new Settings();

// Toggle settings panel
settings.toggle();

// Add custom settings
settings.addSlider(panel, "Sensitivity", "sensitivity", 0.5, 0, 1, 0.1);
settings.addCheckbox(panel, "Debug Mode", "debugMode", false);
```

## Modules

### Text2mesh Module (`modules/Text2mesh/`)

A module for generating 3D models from text descriptions using the Gradio API. The module consists of several components working together to provide a seamless text-to-3D generation experience.

#### Main Component (`modules/Text2mesh/index.js`)

```javascript
import { Text2mesh } from "../ui/modules/Text2mesh";

// Basic usage
function App() {
  return <Text2mesh app={appInstance} />;
}

// Available props
{
  app: AppInstance; // The main application instance for asset loading
}
```

#### Input Component (`modules/Text2mesh/input.js`)

Handles text input and generation controls.

```javascript
import { Input } from '../ui/modules/Text2mesh/input';

// Basic usage
function GeneratorForm() {
  const handleGenerate = async (promptText) => {
    // Handle generation
  };

  return (
    <Input
      onGenerate={handleGenerate}
      isLoading={false}
    />
  );
}

// Available props
{
  onGenerate: (promptText: string) => void,
  isLoading?: boolean
}
```

#### Preview Component (`modules/Text2mesh/preview.js`)

Displays generated assets (images and videos).

```javascript
import { Preview } from '../ui/modules/Text2mesh/preview';

// Basic usage
function ResultPreview() {
  return (
    <Preview
      imageUrl="path/to/generated/image.png"
      videoUrl="path/to/generated/video.mp4"
    />
  );
}

// Available props
{
  imageUrl: string,
  videoUrl: string
}
```

## Styling and Themes

### Theme Configuration

The UI system uses CSS variables for consistent theming across components.

```css
/* Light theme */
:root {
  --primary-color: #007aff;
  --primary-color-light: rgba(0, 122, 255, 0.1);
  --border-color: #e2e8f0;
  --text-primary: #1a202c;
  --text-secondary: #718096;
  --background-color: #ffffff;
  --surface-color: #f7fafc;
  --input-background: #ffffff;
}

/* Dark theme */
@media (prefers-color-scheme: dark) {
  :root {
    --primary-color: #0a84ff;
    --primary-color-light: rgba(10, 132, 255, 0.1);
    --border-color: #2d3748;
    --text-primary: #ffffff;
    --text-secondary: #a0aec0;
    --background-color: #1a202c;
    --surface-color: #2d3748;
    --input-background: #2d3748;
  }
}
```

## Best Practices

1. **Module Organization**

   - Keep related components together in a module folder
   - Include module-specific styles in the module directory
   - Use index.js for the main module component
   - Break down complex components into smaller, focused ones

2. **Component Design**

   - Make components reusable and self-contained
   - Use TypeScript for better type safety
   - Implement proper prop validation
   - Handle loading and error states

3. **Styling**

   - Use CSS modules or scoped class names to avoid conflicts
   - Follow the BEM naming convention
   - Utilize theme variables for consistency
   - Support both light and dark themes

4. **Accessibility**

   - Include proper ARIA labels
   - Support keyboard navigation
   - Maintain sufficient color contrast
   - Provide text alternatives for images and videos

5. **Performance**
   - Lazy load components when possible
   - Optimize image and video loading
   - Use proper React hooks for state management
   - Implement proper cleanup in useEffect

## Example Usage

Here's a complete example combining multiple components:

```javascript
import { Button, Modal, Toggle, Slider } from "../ui/components";
import { Text2mesh } from "../ui/modules/Text2mesh";
import { SettingsPanel } from "../ui/settingsPanel";

function App({ app }) {
  const [showSettings, setShowSettings] = useState(false);

  const handleSettingsChange = (settings) => {
    // Apply settings
    console.log("Settings updated:", settings);
  };

  return (
    <div className="app">
      {/* Text2mesh Module */}
      <Text2mesh app={app} />

      {/* Settings Panel */}
      <Button onClick={() => setShowSettings(true)}>Open Settings</Button>

      <SettingsPanel
        isOpen={showSettings}
        onClose={() => setShowSettings(false)}
        onChange={handleSettingsChange}
        settings={{
          "Gesture Detection": {
            punchVelocityThreshold: {
              value: 0.15,
              min: 0.05,
              max: 0.3,
              step: 0.01,
              type: "range",
            },
          },
          "Visual Effects": {
            showHandMesh: {
              value: true,
              type: "checkbox",
            },
          },
        }}
      />
    </div>
  );
}
```
