# Fix for 1011 Internal Server Error in Aliyun WebSocket Connection

## Problem Description

The <PERSON>yun Qwen-Omni realtime service was consistently failing with a `1011 Internal Server Error` when trying to establish a WebSocket connection from our JavaScript client. The error would occur after the `session.created` event was received and our code attempted to send a `session.update` event.

Key observations:
- The Python implementation in `examples/aliyun/omni_realtime_client.py` worked correctly
- The JavaScript implementation in `src/agent/models/aliyun/streaming/AliyunRealtimeClient.js` failed with 1011 error
- Both implementations had subtly different `session.update` message formats

## Root Cause

After detailed investigation, we found three key differences between the Python (working) and JavaScript (failing) implementations:

1. **Message Structure**: The Python implementation used a direct JSON structure for the `session.update` event, while the JavaScript implementation used a helper function which created a slightly different structure.

2. **VAD Threshold**: The Python implementation used a threshold of `0.8` while JavaScript used `0.1`.

3. **Silence Duration**: The Python implementation used `1500ms` while JavaScript used `900ms`.

## Solution

We modified the `AliyunRealtimeClient.js` file to exactly match the format of the Python implementation:

1. Changed the message structure to match the Python implementation exactly
2. Updated the `threshold` parameter from `0.1` to `0.8`
3. Updated the `silence_duration_ms` parameter from `900` to `1500`

These changes were implemented in:
- `src/agent/models/aliyun/streaming/AliyunRealtimeClient.js` - Direct implementation changes
- `src/agent/models/aliyun/config/AliyunConfig.js` - Configuration updates for consistency

## Testing

We verified the fix through several approaches:
1. Unit tests with real API credentials
2. Direct testing with a custom test script
3. Integration with the main application

The tests verified that:
- The WebSocket connection is established successfully
- The `session.update` event is sent without causing a 1011 error
- Audio data can be sent and received without connection issues

## Conclusion

This fix ensures that our JavaScript implementation matches the working Python implementation exactly, preventing the 1011 Internal Server Error. The changes have been thoroughly tested and are now part of the main codebase.

The key insight was that the Aliyun WebSocket service expects a very specific format and parameter values for the `session.update` event, and any deviation (even seemingly small differences in parameters) leads to a server-side error. 