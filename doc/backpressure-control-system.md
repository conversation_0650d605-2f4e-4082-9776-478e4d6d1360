# Backpressure Control System for Streaming TTS

## Purpose

The backpressure control system regulates the flow of data through the streaming pipeline to prevent buffer overflow and ensure smooth, continuous playback. It acts as a feedback mechanism that slows down upstream components when downstream components cannot process data fast enough.

## Core Principles

1. **Balance**: Maintain balance between text generation, TTS processing, and audio playback
2. **Responsiveness**: React quickly to changing conditions
3. **Smoothness**: Avoid abrupt stops and starts
4. **Adaptivity**: Adjust thresholds based on observed performance
5. **Prevention**: Prevent issues before they occur rather than recovering after

## System Components

### 1. Monitoring

The backpressure system continuously monitors:

- **Audio Queue Size**: Number of audio chunks waiting to be played
- **Audio Buffer Duration**: Total seconds of audio in the queue
- **TTS Request Queue**: Number of pending TTS requests
- **Text Buffer Size**: Amount of text waiting to be processed
- **Processing Rates**: How quickly each component is processing data

### 2. Threshold Management

The system maintains several thresholds:

- **Warning Threshold**: When to start applying light backpressure
- **Critical Threshold**: When to pause upstream components
- **Resume Threshold**: When to resume normal operation
- **Force Resume Timeout**: Maximum time to wait before forcing resume

### 3. Control Actions

Based on monitored values and thresholds, the system can:

- **Pause LLM Processing**: Stop consuming tokens from the LLM stream
- **Throttle TTS Requests**: Limit the rate of new TTS requests
- **Adjust Chunk Size**: Request smaller text chunks when under pressure
- **Force Resume**: Resume processing after timeout to prevent deadlock

## Backpressure Levels

Instead of binary on/off backpressure, the system uses a graduated level system:

| Level | Description | Action |
|-------|-------------|--------|
| 0 | Normal operation | Process at full speed |
| 1-3 | Light pressure | Reduce chunk size, slow down processing |
| 4-7 | Moderate pressure | Pause new TTS requests, process existing queue |
| 8-10 | Critical pressure | Pause all upstream components until relief |

## Implementation Strategy

### 1. Metrics-Based Approach

Calculate backpressure level based on multiple metrics:

```javascript
calculateBackpressureLevel() {
    // Base level from audio queue size
    let level = Math.min(this.audioPlayer.playbackQueue.length, 5);
    
    // Add pressure based on audio duration in buffer
    const bufferDuration = this.audioPlayer.getTotalBufferedDuration();
    level += bufferDuration > this.maxBufferTime ? 3 : 
             bufferDuration > this.maxBufferTime * 0.7 ? 2 : 
             bufferDuration > this.maxBufferTime * 0.4 ? 1 : 0;
    
    // Add pressure based on pending TTS requests
    level += this.pendingTtsRequests > 3 ? 2 : 
             this.pendingTtsRequests > 1 ? 1 : 0;
    
    return Math.min(level, 10); // Cap at 10
}
```

### 2. Adaptive Thresholds

Adjust thresholds based on observed performance:

```javascript
adjustThresholds() {
    // If we've seen buffer underruns, reduce pressure sensitivity
    if (this.bufferUnderrunCount > 0) {
        this.pauseThreshold = Math.min(this.pauseThreshold + 1, 7);
        this.resumeThreshold = Math.min(this.resumeThreshold + 1, 5);
    }
    
    // If we've seen buffer overruns, increase pressure sensitivity
    if (this.bufferOverrunCount > 0) {
        this.pauseThreshold = Math.max(this.pauseThreshold - 1, 3);
        this.resumeThreshold = Math.max(this.resumeThreshold - 1, 1);
    }
    
    // Reset counters
    this.bufferUnderrunCount = 0;
    this.bufferOverrunCount = 0;
}
```

### 3. Hysteresis Control

Implement hysteresis to prevent rapid oscillation between paused and resumed states:

```javascript
applyBackpressure(level) {
    // Only pause if we cross the pause threshold from below
    if (level >= this.pauseThreshold && this.currentLevel < this.pauseThreshold) {
        this.pauseUpstream();
        this.isPaused = true;
        this.pauseStartTime = Date.now();
    }
    
    // Only resume if we drop below resume threshold from above
    if (level <= this.resumeThreshold && this.currentLevel > this.resumeThreshold) {
        this.resumeUpstream();
        this.isPaused = false;
    }
    
    // Force resume if paused too long
    if (this.isPaused && (Date.now() - this.pauseStartTime > this.forceResumeTimeout)) {
        this.resumeUpstream();
        this.isPaused = false;
        this.forceResumeCount++;
    }
    
    this.currentLevel = level;
}
```

## Coordination with Other Components

### 1. Text Chunker Coordination

```javascript
// In text chunker
getTargetChunkSize() {
    const backpressureLevel = this.backpressureController.getCurrentLevel();
    
    // Reduce chunk size under pressure
    if (backpressureLevel >= 7) return this.minLength;
    if (backpressureLevel >= 4) return this.maxLength * 0.5;
    if (backpressureLevel >= 1) return this.maxLength * 0.7;
    
    return this.maxLength;
}
```

### 2. TTS Request Management

```javascript
// In TTS service
async sendTtsRequest(text) {
    const backpressureLevel = this.backpressureController.getCurrentLevel();
    
    // Limit concurrent requests based on backpressure
    const maxConcurrent = backpressureLevel >= 7 ? 1 : 
                          backpressureLevel >= 4 ? 2 : 3;
                          
    // Wait until we're below the limit
    while (this.pendingRequests >= maxConcurrent) {
        await new Promise(resolve => setTimeout(resolve, 100));
        
        // Check for timeout
        if (Date.now() - this.startWaitTime > this.maxWaitTime) {
            // Force proceed after timeout
            break;
        }
    }
    
    // Proceed with request
    this.pendingRequests++;
    try {
        return await this.actualSendRequest(text);
    } finally {
        this.pendingRequests--;
    }
}
```

## Advanced Features

### 1. Predictive Backpressure

Anticipate future buffer state based on current trends:

```javascript
predictFutureBackpressure() {
    // Calculate rate of change in buffer size
    const bufferGrowthRate = (this.currentBufferSize - this.previousBufferSize) / this.measurementInterval;
    
    // Predict future buffer size
    const predictedBufferSize = this.currentBufferSize + (bufferGrowthRate * this.predictionWindow);
    
    // Apply early backpressure if growth trend is concerning
    if (predictedBufferSize > this.criticalThreshold && bufferGrowthRate > 0) {
        this.applyPreemptiveBackpressure();
    }
}
```

### 2. Content-Aware Adjustment

Adjust backpressure based on content type:

```javascript
adjustForContentType(contentType) {
    switch(contentType) {
        case 'dialogue':
            // Dialogue needs more responsive chunking
            this.pauseThreshold = 4;
            this.resumeThreshold = 2;
            break;
        case 'narrative':
            // Narrative can use larger chunks
            this.pauseThreshold = 5;
            this.resumeThreshold = 3;
            break;
        case 'technical':
            // Technical content needs careful chunking
            this.pauseThreshold = 3;
            this.resumeThreshold = 1;
            break;
    }
}
```

### 3. Recovery Strategies

Implement strategies to recover from extreme conditions:

```javascript
handleExtremeBackpressure() {
    if (this.currentLevel >= 9 && this.durationAtHighLevel > 5000) {
        // Emergency measures
        this.clearNonEssentialBuffers();
        this.prioritizeCriticalContent();
        this.temporarilyReduceAudioQuality();
    }
}
```

## Monitoring and Debugging

### 1. Visualization

Provide visual feedback on backpressure state:

```javascript
updateBackpressureVisualization() {
    const element = document.getElementById('backpressureIndicator');
    
    // Update color based on level
    element.style.backgroundColor = this.currentLevel >= 8 ? 'red' : 
                                   this.currentLevel >= 4 ? 'yellow' : 'green';
    
    // Update size based on level
    element.style.width = `${this.currentLevel * 10}%`;
    
    // Update text
    document.getElementById('backpressureLevel').textContent = this.currentLevel;
}
```

### 2. Logging

Log key events for debugging:

```javascript
logBackpressureEvent(action, level, reason) {
    console.log(`[BackpressureController] ${action} due to ${reason} (level: ${level}/10)`);
    
    // Add to event history for analysis
    this.eventHistory.push({
        timestamp: Date.now(),
        action,
        level,
        reason,
        bufferState: {
            audioQueueSize: this.audioPlayer.playbackQueue.length,
            audioDuration: this.audioPlayer.getTotalBufferedDuration(),
            pendingTtsRequests: this.pendingTtsRequests,
            textBufferSize: this.textBufferSize
        }
    });
}
```

## Best Practices

1. **Tune Parameters Carefully**: Start conservative and adjust based on real-world performance
2. **Test with Various Content**: Different content types may require different backpressure settings
3. **Monitor User Experience**: The ultimate goal is smooth playback without interruptions
4. **Avoid Deadlocks**: Always include timeout mechanisms to prevent permanent pauses
5. **Graceful Degradation**: System should maintain basic functionality even under extreme pressure
