# Text Chunking Algorithm for Streaming TTS

## Purpose

The text chunking algorithm is responsible for breaking a continuous stream of text from an LLM into meaningful segments that can be sent to a TTS service. The goal is to create chunks that:

1. Maintain linguistic integrity (complete sentences, phrases, or logical units)
2. Are appropriately sized for efficient TTS processing
3. Result in natural-sounding speech with appropriate pauses
4. Prevent buffer overflow or starvation

## Chunking Hierarchy

The algorithm should follow a hierarchical approach to chunking, prioritizing natural linguistic boundaries:

1. **Complete Sentences** (Score: 8-10)
   - Ends with `.`, `!`, `?`, followed by space or newline
   - Complete quotes with proper punctuation
   - Complete numbered or bulleted list items

2. **Clauses** (Score: 6-8)
   - Separated by `;`, `:`, or `—`
   - Independent clauses joined by conjunctions (and, but, or)

3. **Phrases** (Score: 4-6)
   - Separated by commas
   - Natural phrase boundaries (prepositional phrases, noun phrases)
   - Dialog segments

4. **Word Groups** (Score: 2-4)
   - Logical groupings of 3-5 words
   - Respect syntactic units

5. **Word Boundaries** (Score: 1-2)
   - Only as a last resort
   - Never split within a word

## Algorithm Design

### Input
- Continuous stream of text tokens from LLM
- Current buffer state
- Configuration parameters

### Output
- Chunks of text ready for TTS processing
- Metadata about chunk type and boundaries

### Process Flow

1. **Buffer Accumulation**
   - Add incoming tokens to text buffer
   - Track buffer size and time since last chunk

2. **Boundary Detection**
   - Scan buffer for linguistic boundaries
   - Score each potential boundary
   - Consider both local context and global position

3. **Chunk Selection**
   - Select highest-scoring boundary that meets size constraints
   - If no suitable boundary found, continue accumulating (up to timeout)

4. **Timeout Handling**
   - If timeout occurs, find best available boundary
   - If no good boundary, use word boundary as fallback
   - Never split within a word

5. **Context Preservation**
   - Maintain context between chunks
   - Ensure proper word boundaries across chunks
   - Handle special cases (quotes, parentheses, etc.)

## Scoring System

The scoring system evaluates potential chunk boundaries:

```
Score = BaseScore + ContextBonus - PenaltyFactors
```

Where:

- **BaseScore**: Determined by boundary type (sentence: 8-10, clause: 6-8, etc.)
- **ContextBonus**: Additional points for:
  - Complete thought or idea (+1)
  - Natural pause in speech (+0.5)
  - Balanced chunk size (+0.5)
- **PenaltyFactors**: Deductions for:
  - Splitting related content (-1 to -3)
  - Creating very short chunks (-0.5 to -2)
  - Breaking established patterns (-0.5)

## Implementation Considerations

### 1. Lookahead Buffer

Maintain a lookahead buffer to make better chunking decisions:
- Scan ahead to find optimal boundaries
- Consider multiple potential boundaries before deciding
- Balance immediate needs with future optimal chunking

### 2. Adaptive Sizing

Adjust chunk size based on:
- Current audio buffer state (smaller chunks when buffer is full)
- Content type (narrative vs. dialogue vs. technical)
- Speaking rate and rhythm

### 3. Special Case Handling

Implement special handling for:
- Quotations and dialogue
- Parenthetical expressions
- Lists and enumerations
- Code or technical content
- Non-English text and multilingual content

### 4. Backpressure Integration

Coordinate with backpressure system:
- Produce smaller chunks when backpressure is high
- Create larger, more complete chunks when backpressure is low
- Adjust timeout thresholds based on system state

## Optimization Techniques

### 1. Pre-chunking Analysis

When possible, analyze text patterns before chunking:
- Identify sentence structure patterns
- Detect content type (narrative, dialogue, technical)
- Recognize list structures or special formatting

### 2. Learning from Feedback

Improve chunking over time:
- Track successful vs. problematic chunks
- Adjust scoring weights based on observed quality
- Learn from user feedback or interruptions

### 3. Language-Specific Rules

Implement language-specific chunking rules:
- Different punctuation conventions
- Syntactic structures
- Natural pause points

## Testing and Evaluation

Evaluate chunking quality using:
1. **Linguistic Coherence**: Do chunks represent complete thoughts?
2. **Audio Quality**: Does the resulting audio sound natural?
3. **System Performance**: Does chunking prevent buffer issues?
4. **User Experience**: Does the speech flow naturally without awkward pauses?

## Example Implementation

```javascript
class EnhancedSentenceChunker {
    constructor(options = {}) {
        this.buffer = "";
        this.minLength = options.minLength || 20;
        this.maxLength = options.maxLength || 150;
        this.timeoutMs = options.timeoutMs || 300;
        this.lastChunkTime = Date.now();
        this.lookaheadSize = options.lookaheadSize || 50;
    }

    addText(text) {
        this.buffer += text;
        return this.extractChunkIfReady();
    }

    extractChunkIfReady() {
        // Check if we have enough text to consider chunking
        if (this.buffer.length < this.minLength) {
            return null;
        }

        // Check for timeout
        const timeInBuffer = Date.now() - this.lastChunkTime;
        const isTimeout = timeInBuffer > this.timeoutMs;

        // Find best boundary
        const boundaries = this.findPotentialBoundaries();
        
        // Sort by score (highest first)
        boundaries.sort((a, b) => b.score - a.score);
        
        // Select best boundary that meets criteria
        const selectedBoundary = this.selectBestBoundary(boundaries, isTimeout);
        
        if (selectedBoundary) {
            // Extract chunk up to the selected boundary
            const chunk = this.buffer.substring(0, selectedBoundary.position + 1);
            this.buffer = this.buffer.substring(selectedBoundary.position + 1);
            this.lastChunkTime = Date.now();
            
            return {
                text: chunk,
                type: selectedBoundary.type,
                score: selectedBoundary.score
            };
        }
        
        // If timeout occurred but no good boundary found, force a chunk at word boundary
        if (isTimeout && this.buffer.length > this.maxLength) {
            return this.forceChunkAtWordBoundary();
        }
        
        return null;
    }

    findPotentialBoundaries() {
        // Implementation details for finding and scoring boundaries
        // ...
    }

    selectBestBoundary(boundaries, isTimeout) {
        // Implementation details for selecting the best boundary
        // ...
    }

    forceChunkAtWordBoundary() {
        // Implementation details for forcing a chunk at word boundary
        // ...
    }
}
```
