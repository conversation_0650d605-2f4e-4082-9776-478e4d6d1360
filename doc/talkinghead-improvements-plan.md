# TalkingHead Improvements Plan

## Current Issues

1. **Lack of True Streaming**: The current implementation accumulates all audio chunks before playing, leading to high latency.
2. **Difficulty with Interruption**: It's hard to interrupt the system when it's speaking a long response.
3. **Voice Change Errors**: Changing voices during speech can cause errors.

## Proposed Improvements

### 1. Add processAudioChunk Method to TalkingHead

```javascript
/**
 * Process a single audio chunk for streaming playback
 * @param {Uint8Array} chunk - Audio chunk data
 * @returns {Promise<void>}
 */
async processAudioChunk(chunk) {
    // If we're not already playing, initialize the audio context and source
    if (!this.audioContext) {
        this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
    }
    
    // If we don't have a source node or we're not playing, create one
    if (!this.sourceNode || !this.isPlaying) {
        this.sourceNode = this.audioContext.createBufferSource();
        this.sourceNode.connect(this.audioContext.destination);
        this.isPlaying = true;
    }
    
    // Decode the audio chunk
    try {
        const audioBuffer = await this.audioContext.decodeAudioData(chunk.buffer.slice(0));
        
        // Play the audio
        const newSource = this.audioContext.createBufferSource();
        newSource.buffer = audioBuffer;
        newSource.connect(this.audioContext.destination);
        newSource.start();
        
        // Update lip sync based on the audio data
        this._updateLipSync(audioBuffer);
    } catch (error) {
        console.error('Error decoding audio chunk:', error);
    }
}
```

### 2. Improve Stop Method for Better Interruption

```javascript
/**
 * Stop speaking and reset state
 */
stopSpeaking() {
    if (this.sourceNode) {
        try {
            this.sourceNode.stop();
        } catch (e) {
            // Ignore errors when stopping
        }
        this.sourceNode = null;
    }
    
    if (this.audioContext) {
        try {
            this.audioContext.close();
        } catch (e) {
            // Ignore errors when closing
        }
        this.audioContext = null;
    }
    
    this.isPlaying = false;
    this._resetLipSync();
}
```

### 3. Add Support for Streaming Lip Sync

```javascript
/**
 * Update lip sync based on audio data
 * @param {AudioBuffer} audioBuffer - Audio buffer to analyze
 * @private
 */
_updateLipSync(audioBuffer) {
    // Get audio data from the buffer
    const audioData = audioBuffer.getChannelData(0);
    
    // Calculate amplitude
    let sum = 0;
    for (let i = 0; i < audioData.length; i++) {
        sum += Math.abs(audioData[i]);
    }
    const amplitude = sum / audioData.length;
    
    // Map amplitude to mouth openness
    const mouthOpenness = Math.min(1, amplitude * 10);
    
    // Update viseme
    this._updateViseme(mouthOpenness);
}
```

### 4. Implementation Steps

1. Add the `processAudioChunk` method to TalkingHead
2. Improve the `stopSpeaking` method for better interruption
3. Add support for streaming lip sync
4. Update the SparkTTSService to use the new streaming methods
5. Test with long responses and interruptions
