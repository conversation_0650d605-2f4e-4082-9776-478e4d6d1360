# Mobile Proxy Architecture

This document outlines the architecture for the mobile proxy system, which allows a mobile device to act as a controller for the Looking Glass display.

## Overview

The mobile proxy architecture enables a mobile device to capture audio/video input and send it to the host machine, which then processes the input and displays the results on the Looking Glass display. This architecture supports two modes:

1. **Proxy Mode**: When a mobile device is connected, it streams audio from its microphone to the host machine.
2. **Standalone Mode**: When no mobile device is connected, the host machine uses its own microphone for audio input.

## Components

### Host Machine

- **ConnectionManager**: Manages WebSocket connections between the host machine and mobile devices
- **TalkingAvatar**: Handles avatar animation and speech synthesis
- **LocalMediaManager**: Captures audio from the host machine's microphone in standalone mode
- **LLM Service**: Processes text input and generates responses

### Mobile Device

- **MobileController**: Provides a user interface for controlling the Looking Glass display
- **MediaStreamManager**: Captures audio/video from the mobile device and streams it to the host machine

## Data Flow

### Audio Streaming

#### Proxy Mode (Mobile Device Connected)
1. Mobile device captures audio through its microphone
2. Audio is streamed to the host machine via WebSocket
3. Host machine processes the audio using speech recognition
4. Recognized text is sent to the LLM service
5. LLM response is sent back to the host machine
6. Host machine synthesizes speech and animates the avatar

#### Standalone Mode (No Mobile Device)
1. Host machine captures audio through its microphone
2. Audio is processed using speech recognition
3. Recognized text is sent to the LLM service
4. LLM response is used to synthesize speech and animate the avatar

## UI Components

### Host Machine UI
- **Listen Button**: Activates speech recognition (using either mobile or host microphone)
- **Language Selector**: Allows switching between supported languages
- **Status Indicator**: Shows the current mode (proxy or standalone)

### Mobile Device UI
- **Microphone Button**: Activates the mobile device's microphone
- **Camera Button**: Activates the mobile device's camera (optional)
- **Controls**: Provides touch controls for manipulating the 3D object

## Mode Switching

The system automatically switches between proxy and standalone modes based on device connectivity:

1. When a mobile device connects, the system switches to proxy mode
2. When a mobile device disconnects, the system reverts to standalone mode

## Implementation Details

### Speech Recognition
- Both modes use the same speech recognition service
- The source of the audio stream changes based on the active mode

### LLM Integration
- The LLM service is configured to use Qwen2.5-Omni as the default model
- The service is accessed through the letta-vllm provider

### WebSocket Communication
- WebSocket server runs on port 3000
- Mobile devices connect to the host machine using a QR code
- Audio data is streamed in chunks to minimize latency
