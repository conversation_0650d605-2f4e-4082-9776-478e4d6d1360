# 1011 Error Resolution: <PERSON><PERSON>wen-Omni Realtime WebSocket Service

## Executive Summary

This document details the resolution of the 1011 Internal Server Error encountered with the Aliyun Qwen-Omni realtime WebSocket service. Through systematic analysis of logs and comparison with Python reference implementations, we identified and fixed multiple issues related to audio format, transmission timing, and session initialization.

## Issue Description

The WebSocket connection to <PERSON><PERSON>'s Qwen-Omni realtime service was failing with a 1011 Internal Server Error immediately after session creation, approximately 108ms after sending the first audio chunk. This error occurred consistently and prevented any successful voice interaction with the AI model.

## Root Causes Identified

1. **Audio Sample Rate Mismatch**: JavaScript implementation was using 16kHz audio when <PERSON>yun Qwen-Omni expects 24kHz for realtime mode
2. **Session Initialization Timing**: Audio was being sent immediately after session creation before the server was ready
3. **Audio Transmission Rate**: JavaScript was sending chunks every 125ms (8/second) while Python reference used 200ms (5/second)
4. **Session Readiness Validation**: No validation was performed to ensure session was ready before sending audio
5. **Audio Format Validation**: No validation of audio format compatibility with server expectations

## Analysis Process

### Log Analysis

The error logs revealed:

```
[AliyunBailianChatModel] ✅ [SessionDebug] Realtime session created: {sessionId: 'sess_E6nbLj8xELrmsrJs2Gf3F', model: 'qwen-omni-turbo-realtime', inputAudioFormat: 'pcm16', outputAudioFormat: 'pcm16', turnDetection: {…}, …}

[AliyunBailianChatModel] 🔊 [AudioBuffer] Processed audio chunk: 10984 bytes

[AliyunBailianChatModel] ❌ [ConnectionDebug] 1011 Internal Server Error detected: {code: 1011, reason: 'Internal server error', wasClean: true, timestamp: '2025-07-13T13:19:41.840Z', audioChunksTracking: {…}}
```

The error occurred only 108ms after session creation, suggesting that the server wasn't ready to process audio or there was an incompatibility with the audio format.

### Python Reference Implementation Comparison

We examined the Python reference implementations (`vad_mode.py` and `omni_realtime_client.py`) and found key differences:

1. Python uses 24kHz sample rate:
   ```python
   RATE = 24000  # 采样率 24kHz
   ```

2. Python uses 200ms interval between audio chunks:
   ```python
   # FIXED: Use conservative rate limiting to prevent 1011 errors
   # Previous 0.05s (50ms) = 20fps caused server overload
   # Using 0.2s (200ms) = 5fps for ultra-conservative reliability
   await asyncio.sleep(0.2)
   ```

3. Python initializes session before sending any audio:
   ```python
   await realtime_client.connect()
   # Start message handling and microphone recording after connection
   message_handler = asyncio.create_task(realtime_client.handle_messages())
   streaming_task = asyncio.create_task(start_microphone_streaming(realtime_client))
   ```

## Implemented Fixes

### 1. Updated Sample Rate

Changed audio sample rate from 16kHz to 24kHz in `MediaCaptureManager.ts`:

```typescript
// Before
private targetSampleRate: number = 16000; // Aliyun Qwen-Omni requirement

// After
private targetSampleRate: number = 24000; // FIXED: Use 24kHz to match Python example and Aliyun requirements
```

And in `TalkingAvatarAdapter.js`:

```javascript
// Before
sampleRate: 16000,

// After
sampleRate: 24000, // FIXED: Use 24kHz to match Python example and Aliyun requirements
```

### 2. Added Session Initialization Delay

Added a 500ms delay after session creation before allowing audio transmission in `AliyunBailianChatModel.js`:

```javascript
// Mark session as ready and record timestamp
if (this._audioRateTracker) {
    this._audioRateTracker.sessionCreatedAt = Date.now();

    // Wait 500ms before allowing audio to be sent
    setTimeout(() => {
        this.logger.debug('✅ [SessionDebug] Session ready for audio after initialization delay');
        this._audioRateTracker.sessionReady = true;
    }, 500);
}
```

### 3. Changed Audio Chunk Interval

Changed audio chunk transmission interval from 125ms to 200ms in `AliyunBailianChatModel.js`:

```javascript
// Before
minIntervalMs: 125, // Enforce 125ms minimum interval (8 chunks/sec max)

// After
minIntervalMs: 200, // FIXED: Use 200ms interval (5 chunks/sec) like Python example
```

And in `TalkingAvatarAdapter.js`:

```javascript
// Before
}, 125); // Send every 125ms (8 times per second)

// After
}, 200); // FIXED: Send every 200ms (5 times per second) to match Python example
```

### 4. Added Session Readiness Validation

Added validation to ensure session is ready before sending audio in `AliyunBailianChatModel.js`:

```javascript
// Check if session is ready for audio
if (!this._audioRateTracker.sessionReady) {
    const sessionAge = this._audioRateTracker.sessionCreatedAt ?
        Date.now() - this._audioRateTracker.sessionCreatedAt : 0;

    this.logger.debug(`⏱️ [SessionDebug] Session not ready for audio yet (age: ${sessionAge}ms), skipping chunk`);
    return false;
}
```

### 5. Enhanced Error Logging and Diagnostics

Added categorized logging prefixes for better diagnostics:

- `[ConnectionDebug]`: WebSocket connection events
- `[SessionDebug]`: Session management events
- `[RateMonitor]`: Audio transmission rate monitoring
- `[AudioBuffer]`: Audio processing events
- `[VADDebug]`: Voice activity detection events
- `[ErrorDebug]`: Error diagnostics

## Verification

After implementing these fixes, the WebSocket connection remained stable and the 1011 error no longer occurred. Voice interactions with the Qwen-Omni model now function correctly, with proper audio streaming and response generation.

## Lessons Learned

1. **Reference Implementation Importance**: Always compare with official reference implementations when working with external APIs
2. **Rate Limiting**: Conservative rate limiting is essential for real-time streaming APIs
3. **Format Validation**: Explicitly validate and match expected audio formats
4. **Initialization Timing**: Allow sufficient time for server-side session initialization
5. **Enhanced Logging**: Categorized, detailed logging is crucial for diagnosing complex WebSocket issues

## Future Recommendations

1. Add automated tests to verify audio format compliance
2. Implement adaptive rate limiting based on network conditions
3. Add automatic reconnection with exponential backoff for transient errors
4. Create a monitoring dashboard for realtime WebSocket performance metrics
5. Document API requirements and limitations for future developers
