# Media Module Migration Plan

This document outlines the plan for migrating from the old `MediaStreamManager.js` to the new media module architecture.

## Current State

The current implementation uses:
- `src/utils/MediaStreamManager.js`: Handles media capture and streaming
- `src/utils/audioProcessor.ts`: Processes audio data for vLLM

## New Architecture

The new architecture organizes media-related code into a dedicated module:
- `src/media/capture/MediaCaptureManager.ts`: Handles media capture
- `src/media/processing/AudioProcessor.ts`: Processes audio data
- `src/media/processing/VideoProcessor.ts`: Processes video data
- `src/media/streaming/StreamingManager.ts`: Handles media streaming
- `src/media/utils/mediaTypes.ts`: Shared types and utilities

## Migration Steps

### Phase 1: Prepare for Migration (Completed)

1. ✅ Create the new media module structure
2. ✅ Implement the core components
3. ✅ Update server-side code to use the new components
4. ✅ Remove redundant files

### Phase 2: Client-Side Migration

1. Identify all components that use `MediaStreamManager.js`
2. Create a compatibility layer to ease migration
3. Update each component to use the new media module
4. Test each component thoroughly

### Phase 3: Complete Migration

1. Remove the compatibility layer
2. Remove `MediaStreamManager.js`
3. Update documentation
4. Final testing

## Component Migration Plan

### TalkingAvatar Component

The `talkingavatar.js` component currently uses `MediaStreamManager.js` for:
- Capturing audio from the microphone
- Processing audio data
- Streaming audio to the LLM service

Migration steps:
1. Create a new `TalkingAvatarMediaManager` class that uses the new media module
2. Update the component to use the new manager
3. Test with various devices and browsers

### Mobile Controller

The mobile controller uses `MediaStreamManager.js` for:
- Capturing video from the camera
- Streaming video to the host

Migration steps:
1. Create a new `MobileMediaManager` class that uses the new media module
2. Update the mobile controller to use the new manager
3. Test with various mobile devices

## Testing Plan

1. Test media capture on different browsers and devices
2. Test audio processing with various audio formats
3. Test video processing with various video formats
4. Test streaming with different network conditions
5. Test integration with vLLM and Qwen2.5-Omni

## Timeline

- Phase 1: Completed
- Phase 2: 1-2 weeks
- Phase 3: 1 week

Total estimated time: 2-3 weeks
