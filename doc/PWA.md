## Building as PWA

### PWA Setup and Installation

1. **Build for Production**

   ```bash
   # Set the app name (e.g., viewer)
   export APP_NAME=viewer

   # Build the project
   npm run build

   # The build output will be in dist/viewer

   # Serve the production build (replace viewer with your app name)
   serve -s dist/viewer
   ```

2. **Install on Mobile Device**

   a. **Android**:

   - Open Chrome and navigate to your local server (e.g., `http://192.168.1.100:3000`)
   - Tap the menu (⋮) in Chrome
   - Select "Add to Home Screen"
   - Follow the prompts to install

   b. **iOS**:

   - Open Safari and navigate to your local server
   - Tap the Share button
   - Select "Add to Home Screen"
   - Tap "Add" to install

### PWA Configuration

The app includes necessary PWA files in the app directory (e.g., `src/apps/viewer/`):

```
src/apps/viewer/
├── manifest.json        # PWA manifest
├── service-worker.js    # Service worker for offline support
├── viewer.html         # Main HTML file
└── viewer.js          # Main application code
```

Example manifest.json configuration for the viewer app:

```json
{
  "name": "3D Model Viewer",
  "short_name": "Viewer",
  "start_url": "/",
  "display": "standalone",
  "background_color": "#ffffff",
  "theme_color": "#000000",
  "icons": [
    {
      "src": "/assets/icons/icon-192x192.png",
      "sizes": "192x192",
      "type": "image/png"
    },
    {
      "src": "/assets/icons/icon-512x512.png",
      "sizes": "512x512",
      "type": "image/png"
    }
  ]
}
```

### PWA Features

- ✨ Offline support via service worker
- 📱 Installable on mobile devices
- 🔄 Background sync for model loading
- 🔒 HTTPS required for production
- 📱 Responsive 3D viewer design
- 🎯 Native app-like experience

### Testing PWA Features

1. **Development Testing**

   ```bash
   # Start dev server with specific app
   export APP_NAME=viewer
   npm run dev
   ```

2. **Production Testing**

   ```bash
   # Build and serve specific app
   export APP_NAME=viewer
   npm run build
   serve -s dist/viewer
   ```

3. **Offline Testing**

   ```bash
   # Use Chrome DevTools
   # 1. Open DevTools (F12)
   # 2. Go to Application > Service Workers
   # 3. Check "Offline"
   # 4. Test model viewing and caching
   ```

4. **Lighthouse Audit**

   ```bash
   # Install Lighthouse
   npm install -g lighthouse

   # Run audit (replace with your production URL)
   lighthouse https://your-url.com
   ```

5. **PWA Checklist**
   - Verify manifest.json matches your app configuration
   - Confirm service worker registration in viewer.js
   - Test offline model viewing functionality
   - Check app installation process
   - Validate app icons are present in assets
   - Ensure proper caching of 3D models and resources
