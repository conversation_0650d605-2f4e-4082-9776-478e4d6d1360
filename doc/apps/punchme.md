# PunchMe Application Documentation

## Overview

PunchMe is an interactive 3D motion-controlled fighting game that combines computer vision with 3D character animation. Using MediaPipe for pose detection and Babylon.js for 3D rendering, it creates an immersive fighting experience.

## Architecture

### Core Engine Module
- **SceneManager**: Handles Babylon.js scene initialization and rendering
- **PhysicsSystem**: Manages physics simulation and collision detection

### Input & Detection Module
- **InputManager**: Handles camera input and video stream processing
- **PoseDetector**: Processes MediaPipe pose detection
- **HandPoseDetector**: Processes hand pose detection

### Character Module
- **CharacterManager**: Manages character loading and configuration
- **CharacterController**: Handles character animation and IK solving
- **BoneMapper**: Maps MediaPipe landmarks to character skeleton
- **GameStateManager**: Manages game state and character health

### Combat & Effects Module
- **CombatSystem**: Handles combat logic and collision responses
- **PunchEffectsManager**: Manages visual effects and particle systems

### UI Module
- **UIManager**: Handles UI elements and health bars
- **DebugVisualizer**: Provides debugging visualization tools

## Technical Implementation

### Pose Detection System
```javascript
// MediaPipe configuration
const poseConfig = {
    modelComplexity: 1,
    smoothLandmarks: true,
    minDetectionConfidence: 0.5,
    minTrackingConfidence: 0.5
};

// Landmark processing
function processPoseLandmarks(results) {
    if (!results.poseLandmarks) return;
    
    // Process pose data
    const normalizedLandmarks = normalizePoseData(results.poseLandmarks);
    return validatePoseData(normalizedLandmarks);
}
```

### Character Animation System
```javascript
class CharacterController {
    constructor(skeleton, boneMap) {
        this.skeleton = skeleton;
        this.boneMap = boneMap;
        this.ikSolver = new FABRIKSolver();
    }

    updatePose(landmarks) {
        // Map landmarks to skeleton
        const mappedPose = this.mapLandmarksToSkeleton(landmarks);
        
        // Apply IK solving
        this.ikSolver.solve(mappedPose);
        
        // Update skeleton
        this.updateSkeletonPose();
    }
}
```

### Physics Integration
```javascript
class PhysicsSystem {
    constructor(scene) {
        this.scene = scene;
        this.physicsPlugin = new CannonJSPlugin();
        this.scene.enablePhysics(new Vector3(0, -9.81, 0), this.physicsPlugin);
    }

    createCharacterCollider(character, options) {
        return new CapsuleCollider(character, options);
    }

    handleCollision(collider, target) {
        // Process collision events
    }
}
```

## Setup and Configuration

### Installation
```bash
npm install
npm run dev
```

### Development Environment
- Node.js 14+
- Babylon.js 5.0+
- MediaPipe 0.8+

### Required Assets
- Character Models (GLB/GLTF format)
- Textures and Materials
- Effect Particles

## Usage Guidelines

### Basic Controls
1. Allow camera access when prompted
2. Stand in frame, ensuring full body visibility
3. Use natural punching motions
4. Monitor health bars and game state

### Debug Mode
- Press Ctrl+Shift+I to toggle debug layer
- Use D key to toggle pose visualization
- Use P key to toggle physics debug view

### Performance Optimization
- Adjust MediaPipe confidence thresholds
- Configure character LOD levels
- Optimize particle effect counts

## Troubleshooting

### Common Issues
1. Camera Access
   - Check browser permissions
   - Verify camera hardware
   - Ensure proper lighting

2. Performance
   - Reduce visual effects
   - Lower pose detection complexity
   - Adjust physics simulation steps

3. Animation
   - Verify character rigging
   - Check bone mapping configuration
   - Adjust IK solver parameters

### Error Handling
```javascript
try {
    await this.initializeTracking();
} catch (error) {
    console.error('Tracking initialization failed:', error);
    this.showErrorMessage('Failed to initialize tracking. Please refresh.');
}
```

## Further Development

### Planned Features
- Multiple character support
- Advanced combo system
- Network multiplayer
- Custom character loading

### Contributing
- Follow coding standards
- Write unit tests
- Document API changes
- Use feature branches
