# Viewer Application Documentation

## Overview

The Viewer application is a powerful 3D model viewer with integrated any-to-3D conversion capabilities. It supports multiple input types (text, image, doll) and provides a user-friendly interface for generating and viewing 3D models.

## Features

- 🎯 Real-time 3D model viewing with Three.js
- 🔄 Any-to-3D conversion support
- 📝 Text-to-3D generation
- 🖼️ Image-to-3D conversion
- 🎎 Doll-to-3D transformation
- 🎨 Interactive UI with preview support
- 🔮 Looking Glass holographic display integration

## Usage

### Basic Setup

```javascript
import Viewer from "./apps/viewer/viewer";

// Initialize the viewer
const container = document.getElementById("viewer-container");
const viewer = new Viewer(container);
await viewer.initViewer();
```

### Converting Content to 3D

1. **Using the UI**

   - Click the "Convert to 3D" button in the bottom-right corner
   - Select conversion type (text/image/doll)
   - Input your content:
     - Text: Enter description
     - Image: Upload image file
     - Doll: Upload GLB file
   - Click "Generate" to start conversion

2. **Programmatic Usage**

   ```javascript
   // Text to 3D
   await viewer.generate3DModel("text", "A beautiful watch");

   // Image to 3D
   await viewer.generate3DModel("image", imageDataUrl);

   // Doll to 3D
   await viewer.generate3DModel("doll", dollFileUrl);
   ```

### UI Components

#### Conversion Modal

```html
<div class="conversion-modal">
  <div class="conversion-content">
    <!-- Source Selection -->
    <select>
      <option value="text">Text to 3D</option>
      <option value="image">Image to 3D</option>
      <option value="doll">Doll to 3D</option>
    </select>

    <!-- Input Area -->
    <div class="input-area">
      <textarea
        class="text-input"
        placeholder="Enter description..."
      ></textarea>
      <input type="file" class="file-input" accept="image/*,.glb" />
      <div class="preview"></div>
    </div>

    <!-- Action Buttons -->
    <button class="generate-button">Generate</button>
    <button class="cancel-button">Cancel</button>
  </div>
</div>
```

### Supported File Types

- **Images**: PNG, JPEG, WebP
- **3D Models**: GLB
- **Text**: Any descriptive text

### Model Viewing

The viewer provides several features for interacting with 3D models:

- 🔄 Rotation: Click and drag to rotate
- 🔍 Zoom: Mouse wheel to zoom in/out
- 🖐️ Pan: Right-click and drag to pan
- 🎮 Looking Glass: Toggle holographic display mode

## API Reference

### Constructor

```javascript
const viewer = new Viewer(container);
```

### Methods

#### `initViewer()`

Initializes the viewer and sets up the 3D environment.

```javascript
await viewer.initViewer();
```

#### `generate3DModel(source, input)`

Generates a 3D model from the provided input.

```javascript
await viewer.generate3DModel("text", "A golden watch");
```

Parameters:

- `source`: 'text' | 'image' | 'doll'
- `input`: string (text description or file URL)

#### `loadModel(modelPath)`

Loads a 3D model from the specified path.

```javascript
await viewer.loadModel("/assets/meshes/model.glb");
```

#### `dispose()`

Cleans up resources and removes event listeners.

```javascript
viewer.dispose();
```

## Styling

The viewer comes with a comprehensive set of CSS styles for the UI components. Key classes include:

```css
.conversion-modal    /* Main modal container */
/* Main modal container */
/* Main modal container */
/* Main modal container */
.conversion-content  /* Modal content wrapper */
.input-area         /* Input fields container */
.text-input         /* Text input field */
.file-input         /* File upload input */
.preview            /* Preview area */
.generate-button    /* Generation button */
.cancel-button      /* Cancel button */
.convert-button; /* Main conversion trigger */
```

## Events

The viewer emits various events that you can listen to:

```javascript
viewer.addEventListener("error", (event) => {
  console.error("Viewer error:", event.detail);
});

viewer.addEventListener("modelLoaded", (event) => {
  console.log("Model loaded:", event.detail);
});
```

## Error Handling

The viewer includes comprehensive error handling:

```javascript
try {
  await viewer.generate3DModel("text", "A complex shape");
} catch (error) {
  console.error("Generation failed:", error);
  // Handle error appropriately
}
```

## Best Practices

1. **Resource Management**

   - Always call `dispose()` when removing the viewer
   - Clear unused models from cache
   - Handle file uploads efficiently

2. **Performance**

   - Use appropriate file formats
   - Optimize model complexity
   - Consider mobile device limitations

3. **User Experience**
   - Provide clear feedback during generation
   - Show loading states
   - Handle errors gracefully

## Examples

### Basic Integration

```javascript
// Initialize viewer
const container = document.getElementById("viewer-container");
const viewer = new Viewer(container);
await viewer.initViewer();

// Handle conversion button click
document.getElementById("convertBtn").onclick = () => {
  // Show conversion UI
  viewer.showConversionUI();
};
```

### Custom Error Handling

```javascript
viewer.addEventListener("error", (event) => {
  const errorDisplay = document.getElementById("error-display");
  errorDisplay.textContent = event.detail.message;
  errorDisplay.style.display = "block";
  setTimeout(() => {
    errorDisplay.style.display = "none";
  }, 3000);
});
```

### Advanced Usage

```javascript
// Custom model loading with progress
viewer.loadModel("/path/to/model.glb", {
  onProgress: (progress) => {
    updateProgressBar(progress);
  },
  onComplete: () => {
    showSuccessMessage();
  },
});
```
