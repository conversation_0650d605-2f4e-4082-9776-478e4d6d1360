# Input Module Documentation

## Overview
The input module handles camera input, pose/gesture detection, and provides a unified interface for pose data access.

## Architecture

```
/app/punchme/input/
├── InputManager.js         # Camera and frame management
├── PoseDetector.js        # MediaPipe pose detection
├── PoseValidator.js       # Pose data validation
├── HandPoseDetector.js    # Hand pose detection
└── index.js              # Module exports
```

## Key Components

### InputManager
- Camera initialization and frame capture
- Frame scheduling and processing pipeline
- Event handling and UI coordination
- Device selection and configuration

### PoseDetector
- MediaPipe model initialization
- Combined pose and hand detection
- 3D skeleton conversion
- Debug visualization support

### PoseValidator
- Pose data validation and filtering
- Quality metrics calculation
- Model comparison utilities
- Historical data analysis

### HandPoseDetector
- Hand pose classification
- Finger curl calculation
- Gesture recognition
- 3D hand orientation tracking

## Integration Example

```javascript
import { InputManager, PoseDetector } from './input';

class MyApp {
    async initialize() {
        // Create detector and manager
        const poseDetector = new PoseDetector();
        const inputManager = new InputManager(poseDetector, uiManager);
        
        // Initialize and start
        await inputManager.start();
        
        // Subscribe to pose updates
        inputManager.onPoseResults((results) => {
            // Handle pose + hand data
            const { landmarks, handPoses } = results;
            // Process results...
        });
    }
}
```

## Configuration

### Video Settings
```javascript
{
    width: 640,
    height: 480,
    frameRate: 30
}
```

### Pose Detection
```javascript
{
    modelType: "POSE",
    minDetectionConfidence: 0.5,
    minTrackingConfidence: 0.5
}
```

### Hand Detection
```javascript
{
    numHands: 2,
    minHandDetectionConfidence: 0.5,
    minHandPresenceConfidence: 0.5
}
```
