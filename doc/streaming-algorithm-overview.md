# Streaming Algorithm Overview

## Core Components and Flow

The streaming system consists of several key components that work together to provide a seamless experience:

1. **LLM Stream Processor** (`LLMStreamProcessor`)
2. **Text Chunker** (`SentenceChunker`)
3. **Backpressure Controller**
4. **TTS Service**
5. **Audio Player** (`StreamingAudioPlayer`)

### Data Flow

```
┌─────────────┐     ┌─────────────┐     ┌─────────────┐     ┌─────────────┐
│             │     │             │     │             │     │             │
│    LLM      │────▶│    Text     │────▶│    TTS      │────▶│    Audio    │
│  Stream     │     │  Chunker    │     │  Service    │     │   Player    │
│             │     │             │     │             │     │             │
└─────────────┘     └─────────────┘     └─────────────┘     └─────────────┘
                           │                   │                   │
                           │                   │                   │
                           ▼                   ▼                   ▼
                    ┌─────────────────────────────────────────────────────┐
                    │                                                     │
                    │              Backpressure Controller                │
                    │                                                     │
                    └─────────────────────────────────────────────────────┘
```

### Component Interaction Details

#### 1. LLM Stream Processor
- Receives token stream from LLM
- Maintains conversation context
- Handles stream completion and errors
- Coordinates with Text Chunker

#### 2. Text Chunker
- Accumulates tokens into semantic units
- Applies linguistic rules for chunking
- Maintains chunking state
- Sends ready chunks to TTS

#### 3. TTS Service
- Receives text chunks
- Generates audio in real-time
- Streams audio chunks to player
- Handles voice selection and parameters

#### 4. Audio Player
- Manages audio context and buffers
- Schedules playback of chunks
- Handles audio device output
- Reports playback status

#### 5. Backpressure Controller
- Monitors buffer levels
- Adjusts processing rates
- Prevents buffer overflow
- Maintains system stability

### Buffer Management

```
┌─────────────────────────────────────────────────────────────┐
│                     Text Processing Pipeline                 │
├─────────────┬─────────────┬─────────────┬─────────────┬─────┤
│  LLM Input  │ Text Buffer │ TTS Input   │ Audio Buffer│Output│
│   Buffer    │             │   Buffer    │             │Buffer│
├─────────────┼─────────────┼─────────────┼─────────────┼─────┤
│    < 1s     │   < 2s      │   < 1s      │   < 3s      │ < 1s │
└─────────────┴─────────────┴─────────────┴─────────────┴─────┘
```

### Backpressure Control Flow

```
┌─────────────┐     ┌─────────────┐     ┌─────────────┐
│  Monitor    │     │  Calculate  │     │   Apply     │
│  Buffers    │────▶│  Pressure   │────▶│  Control    │
└─────────────┘     └─────────────┘     └─────────────┘
        │                 │                   │
        │                 │                   │
        ▼                 ▼                   ▼
┌─────────────────────────────────────────────────────┐
│                                                     │
│              Buffer State Feedback                  │
│                                                     │
└─────────────────────────────────────────────────────┘
```

## Key Principles for Optimal Streaming

### 1. Linguistic Integrity

Text should be chunked at natural linguistic boundaries:
- Complete sentences (highest priority)
- Phrases or clauses (medium priority)
- Word groups (lower priority)
- Never split within a word

### 2. Balanced Buffer Management

- **Text Buffer**: Accumulates tokens from the LLM until a meaningful chunk can be formed
- **Audio Buffer**: Stores decoded audio chunks ready for playback
- **Balance**: The rate of text chunking should roughly match the rate of audio playback

### 3. Intelligent Backpressure

Backpressure should:
- Prevent buffer overflow by temporarily pausing upstream components
- Be applied gradually rather than binary on/off
- Consider both queue size (number of items) and total duration (seconds of audio)
- Adapt to changing conditions (network speed, processing time)

### 4. Graceful Timeout Handling

When a timeout occurs (text has been in buffer too long):
- Find the most natural breaking point near the current position
- Prefer word boundaries over arbitrary character positions
- Maintain context between chunks to ensure coherence

## Common Issues and Solutions

### Issue 1: Incomplete Stories

**Causes:**
- LLM stream ends prematurely due to excessive backpressure
- Final text fragments not being sent to TTS

**Solutions:**
- Ensure final text buffer is always processed, even if small
- Implement proper stream completion detection and handling
- Add a completion phase that waits for all audio to finish playing

### Issue 2: Mid-Sentence Pauses

**Causes:**
- Poor chunking decisions (timeout-based rather than linguistic)
- Lack of lookahead in chunking algorithm

**Solutions:**
- Enhance chunker to prioritize linguistic boundaries
- Implement smarter timeout handling with context awareness
- Use a scoring system that heavily penalizes breaking within sentences

### Issue 3: Buffer Imbalance

**Causes:**
- TTS generating audio faster than it can be played
- Fixed thresholds for backpressure regardless of content

**Solutions:**
- Dynamic backpressure thresholds based on audio duration
- Adaptive chunking size based on current buffer state
- Limit concurrent TTS requests to prevent queue flooding

## Optimal Algorithm Parameters

### Text Chunking

- **Minimum Chunk Size**: 10-20 characters (for responsiveness)
- **Maximum Chunk Size**: 100-150 characters (for manageability)
- **Timeout**: 300-500ms (balance between waiting for complete sentences and responsiveness)

### Backpressure Control

- **Queue Size Threshold**: 2-3 items (start applying backpressure)
- **Maximum Buffer Time**: 5-8 seconds of audio (rather than fixed item count)
- **Force Resume Timeout**: 3-5 seconds (prevent deadlocks)

### Audio Player

- **Buffer Size**: 0.5-1.0 seconds (for smooth playback)
- **Decode Ahead**: 1-2 chunks (balance between memory usage and readiness)

## Implementation Best Practices

1. **Monitoring and Visualization**:
   - Track buffer levels (text and audio)
   - Visualize chunking decisions and backpressure events
   - Log key metrics for debugging

2. **Graceful Degradation**:
   - Handle network issues and service failures
   - Provide fallback mechanisms for audio playback
   - Implement recovery strategies for interrupted streams

3. **Adaptive Parameters**:
   - Adjust chunking parameters based on content type
   - Modify backpressure thresholds based on observed performance
   - Learn from past streaming sessions to optimize future ones
