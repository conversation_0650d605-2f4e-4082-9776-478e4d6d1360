# Streaming Best Practices for Real-Time Conversational AI

## System Architecture Overview

```
┌─────────────────────────────────────────────────────────────────────────┐
│                              Client (Browser)                           │
│                                                                         │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌──────────┐ │
│  │             │    │             │    │             │    │          │ │
│  │  Microphone │───▶│  STT Client │───▶│  LLM Client │───▶│  TTS     │ │
│  │             │    │             │    │             │    │  Player  │ │
│  └─────────────┘    └─────────────┘    └─────────────┘    └──────────┘ │
└─────────────────────────────────────────────────────────────────────────┘
         │                     │                    │              │
         │                     │                    │              │
         ▼                     ▼                    ▼              ▼
┌─────────────────────────────────────────────────────────────────────────┐
│                              Backend Proxy                              │
│                                                                         │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌──────────┐ │
│  │             │    │             │    │             │    │          │ │
│  │  STT Proxy  │───▶│  LLM Proxy  │───▶│  TTS Proxy  │───▶│  Audio   │ │
│  │             │    │             │    │             │    │  Router  │ │
│  └─────────────┘    └─────────────┘    └─────────────┘    └──────────┘ │
└─────────────────────────────────────────────────────────────────────────┘
         │                     │                    │              │
         │                     │                    │              │
         ▼                     ▼                    ▼              ▼
┌─────────────────────────────────────────────────────────────────────────┐
│                              External Services                          │
│                                                                         │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐                 │
│  │             │    │             │    │             │                 │
│  │  STT        │    │  LLM        │    │  TTS        │                 │
│  │  Service    │    │  Service    │    │  Service    │                 │
│  └─────────────┘    └─────────────┘    └─────────────┘                 │
└─────────────────────────────────────────────────────────────────────────┘
```

## 1. Core Principles of Effective Streaming

### 1.1 Reduced Perceived Latency

*   **Start Fast, Continue Smoothly**: Begin processing/playback as soon as the first chunk of content (audio input, LLM text, TTS audio) is available. Don't wait for complete inputs or outputs.
*   **Immediate Processing & Forwarding**: Process and forward data chunks through the pipeline (Mic -> STT -> LLM -> TTS -> Speaker) immediately upon generation or arrival. Avoid unnecessary buffering between stages.
*   **Parallel Processing**: Enable different parts of the pipeline to work concurrently (e.g., TTS synthesizes the first sentence while LLM generates the second).

### 1.2 Natural Conversation Flow

*   **Human-Like Timing**: Mimic natural conversation pacing. Start TTS quickly after LLM begins generating.
*   **Incremental Output**: Present LLM text and TTS audio incrementally as they are generated.
*   **Continuous Interaction & Barge-In**: Allow users to interrupt the system's speech (barge-in) by continuously listening for user input (requires STT during TTS playback).

### 1.3 Resource Efficiency

*   **Optimized Memory Usage**: Process data in streams to avoid holding large text or audio buffers in memory.
*   **Bandwidth Management**: Use efficient codecs (e.g., Opus for audio) and send only necessary data.
*   **Graceful Degradation**: Maintain core functionality under load or poor network conditions.

## 2. Component Interaction Details

### 2.1 Client-Side Components

```
┌─────────────────────────────────────────────────────────────┐
│                     Client-Side Pipeline                     │
├─────────────┬─────────────┬─────────────┬─────────────┬─────┤
│  Microphone │  STT Client │  LLM Client │  TTS Client │Audio│
│  Capture    │             │             │             │Player│
├─────────────┼─────────────┼─────────────┼─────────────┼─────┤
│  WebSocket  │  WebSocket  │  HTTP/SSE   │  WebSocket  │WebRTC│
│  Outbound   │  Inbound    │  Inbound    │  Inbound    │Audio │
└─────────────┴─────────────┴─────────────┴─────────────┴─────┘
```

### 2.2 Backend Proxy Components

```
┌─────────────────────────────────────────────────────────────┐
│                     Backend Proxy Pipeline                   │
├─────────────┬─────────────┬─────────────┬─────────────┬─────┤
│  STT Proxy  │  LLM Proxy  │  TTS Proxy  │  Audio      │Error│
│             │             │             │  Router     │Handler│
├─────────────┼─────────────┼─────────────┼─────────────┼─────┤
│  WebSocket  │  HTTP/SSE   │  WebSocket  │  WebSocket  │Logging│
│  Handler    │  Handler    │  Handler    │  Router     │      │
└─────────────┴─────────────┴─────────────┴─────────────┴─────┘
```

### 2.3 Service Integration

```
┌─────────────────────────────────────────────────────────────┐
│                     Service Integration                      │
├─────────────┬─────────────┬─────────────┬─────────────┬─────┤
│  STT        │  LLM        │  TTS        │  Audio      │Error│
│  Service    │  Service    │  Service    │  Service    │Recovery│
├─────────────┼─────────────┼─────────────┼─────────────┼─────┤
│  WebSocket  │  HTTP/SSE   │  WebSocket  │  WebSocket  │Retry │
│  Client     │  Client     │  Client     │  Client     │Logic │
└─────────────┴─────────────┴─────────────┴─────────────┴─────┘
```

## 3. Implementation Guidelines

### 3.1 Microphone Audio Input (Client -> Proxy -> STT)

*   **Capture**: Use `navigator.mediaDevices.getUserMedia({ audio: true })`.
*   **Chunking**: Use `MediaRecorder` (e.g., `audio/webm;codecs=opus`) to get audio `Blob`s periodically (e.g., every 500ms).
*   **Streaming**: Send these `Blob`s immediately to the Backend Proxy via WebSocket.
*   **Backend STT Handling (Proxy)**: Receive audio blobs, stream them to the STT service. Receive text transcriptions back.

### 3.2 LLM Text Streaming (Proxy -> TTS)

*   **Receive Stream**: Process the LLM's SSE stream token by token in the Backend Proxy.
*   **Semantic Chunking & *Immediate* Dispatch**: Accumulate tokens into a temporary buffer. As soon as a meaningful chunk is formed (e.g., sentence end `[.!?\n]`, phrase boundary `,;:`, or length threshold `~50-100 chars`), **immediately** send that chunk to the TTS Service. **Do not wait for the `[DONE]` signal from the LLM to send buffered text.**
*   **Clear Buffer**: Once a chunk is sent to TTS, clear it from the temporary buffer.
*   **End of Stream**: When `[DONE]` is received, send any small remaining text in the buffer to TTS and signal the end of text input to the TTS service (if its API requires it).

### 3.3 TTS Audio Streaming (TTS -> Proxy -> Client)

*   **Receive Audio Stream (Proxy)**: Receive audio chunks (e.g., MP3, Opus) from the TTS service.
*   **Forward Immediately (Proxy -> Client)**: Forward these audio chunks via WebSocket to the correct client without delay.
*   **Playback (Client)**: Use a `StreamingAudioPlayer` (single persistent `AudioContext`). Decode received audio chunks (`ArrayBuffer`).
*   **Scheduled Playback**: Schedule the decoded `AudioBuffer` using `audioContext.currentTime` and a `scheduledTime` tracker to ensure seamless playback. Maintain a small playback buffer (`minBufferTime`, `maxBufferTime`) to handle network jitter while minimizing latency.

### 3.4 Barge-In Support

*   **Continuous Listening (Client/STT)**: Requires the STT service/client logic to process microphone input even while TTS audio is playing.
*   **Interruption Signal**: When user speech is detected (by STT), send a signal to the Backend Proxy.
*   **Cancellation (Proxy)**: Upon receiving the interrupt signal:
    *   Stop sending further text chunks to TTS.
    *   Signal the TTS service to stop generation (if API supports it).
    *   Signal the Client (via WebSocket) to stop audio playback (`StreamingAudioPlayer.stopAll()`).
    *   Optionally, signal the LLM to stop generation (if needed and API supports it).
*   **Context Preservation**: The proxy should retain the conversation context so the LLM can respond coherently to the user's interruption.

## 4. Buffer Management and Backpressure

### 4.1 Buffer Types and Sizes

```
┌─────────────────────────────────────────────────────────────┐
│                     Buffer Management                        │
├─────────────┬─────────────┬─────────────┬─────────────┬─────┤
│  Input      │  Processing │  Output     │  Playback   │Total │
│  Buffer     │  Buffer     │  Buffer     │  Buffer     │Memory│
├─────────────┼─────────────┼─────────────┼─────────────┼─────┤
│  500ms      │  1s         │  1s         │  2s         │ 4.5s │
└─────────────┴─────────────┴─────────────┴─────────────┴─────┘
```

### 4.2 Backpressure Control

```
┌─────────────────────────────────────────────────────────────┐
│                     Backpressure Control                     │
├─────────────┬─────────────┬─────────────┬─────────────┬─────┤
│  Monitor    │  Calculate  │  Apply      │  Adjust      │Reset│
│  Levels     │  Pressure   │  Control    │  Parameters  │State│
├─────────────┼─────────────┼─────────────┼─────────────┼─────┤
│  Continuous │  Dynamic    │  Gradual    │  Adaptive    │Auto │
└─────────────┴─────────────┴─────────────┴─────────────┴─────┘
```

## 5. Error Handling and Recovery

### 5.1 Error Types and Recovery

```
┌─────────────────────────────────────────────────────────────┐
│                     Error Handling                           │
├─────────────┬─────────────┬─────────────┬─────────────┬─────┤
│  Network    │  Service    │  Client     │  Data       │State│
│  Errors     │  Errors     │  Errors     │  Errors     │Errors│
├─────────────┼─────────────┼─────────────┼─────────────┼─────┤
│  Retry      │  Fallback   │  Reconnect  │  Skip       │Reset │
└─────────────┴─────────────┴─────────────┴─────────────┴─────┘
```

### 5.2 Recovery Strategies

*   **Network Errors**: Implement exponential backoff for retries
*   **Service Errors**: Use fallback services or cached responses
*   **Client Errors**: Attempt reconnection with state preservation
*   **Data Errors**: Skip corrupted chunks and continue streaming
*   **State Errors**: Reset to last known good state

## 6. Performance Monitoring

### 6.1 Key Metrics

```
┌─────────────────────────────────────────────────────────────┐
│                     Performance Metrics                      │
├─────────────┬─────────────┬─────────────┬─────────────┬─────┤
│  Latency    │  Throughput │  Buffer     │  Error      │QoS  │
│             │             │  Usage      │  Rate       │     │
├─────────────┼─────────────┼─────────────┼─────────────┼─────┤
│  < 100ms    │  > 1MB/s    │  < 80%      │  < 1%       │High │
└─────────────┴─────────────┴─────────────┴─────────────┴─────┘
```

### 6.2 Monitoring Implementation

*   **Real-time Monitoring**: Track key metrics in real-time
*   **Alerting**: Set up alerts for critical thresholds
*   **Logging**: Maintain detailed logs for debugging
*   **Visualization**: Provide real-time visualization of system state