server {
    listen 80;
    server_name localhost;

    location / {
        root /usr/share/nginx/html;
        index index.html;
        try_files $uri $uri/ /index.html;
    }

    # Enable CORS for MediaPipe models
    location /models/ {
        alias /usr/share/nginx/html/models/;
        add_header 'Access-Control-Allow-Origin' '*';
        add_header 'Access-Control-Allow-Methods' 'GET, OPTIONS';
        add_header 'Access-Control-Allow-Headers' '*';
    }

    # Enable HTTPS upgrade
    if ($http_x_forwarded_proto = "http") {
        return 301 https://$host$request_uri;
    }
} 