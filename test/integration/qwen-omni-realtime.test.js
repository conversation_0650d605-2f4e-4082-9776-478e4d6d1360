/**
 * Qwen-Omni Real-time Integration Tests
 * Comprehensive test suite for the complete real-time multimodal system
 * Tests server proxy, client, workflow, session management, and VAD integration
 */

import { describe, it, beforeEach, afterEach, beforeAll, afterAll, expect, vi } from 'vitest';
import { createServer } from 'http';
import { WebSocketServer } from 'ws';
import WebSocket from 'ws';

// Import components to test
import {
    QwenOmniRealtimeClient,
    QwenOmniRealtimeWorkflow,
    QwenOmniSessionManager,
} from '../../src/agent/models/aliyun/index.js';
import { StreamingAudioProcessor } from '../../src/media/modality/audio.js';

// Mock implementations
const mockLogger = {
    info: vi.fn(),
    debug: vi.fn(),
    warn: vi.fn(),
    error: vi.fn()
};

// Mock Web Audio API
const mockAudioContext = {
    createMediaStreamSource: vi.fn(() => ({
        connect: vi.fn()
    })),
    createAnalyser: vi.fn(() => ({
        connect: vi.fn(),
        disconnect: vi.fn(),
        fftSize: 2048,
        smoothingTimeConstant: 0.3
    })),
    createScriptProcessor: vi.fn(() => ({
        connect: vi.fn(),
        disconnect: vi.fn(),
        onaudioprocess: null
    })),
    createGain: vi.fn(() => ({
        connect: vi.fn(),
        disconnect: vi.fn(),
        gain: { value: 1 }
    })),
    destination: {},
    sampleRate: 24000,
    audioWorklet: {
        addModule: vi.fn(() => Promise.resolve())
    },
    close: vi.fn()
};

// Mock MediaStream
const mockMediaStream = {
    getTracks: vi.fn(() => []),
    getAudioTracks: vi.fn(() => []),
    addTrack: vi.fn(),
    removeTrack: vi.fn()
};

// Test server for WebSocket connections
let testServer;
let testWss;
let testPort;

describe('Qwen-Omni Real-time Integration Tests', () => {
    beforeAll(async () => {
        // Setup test WebSocket server
        testServer = createServer();
        testWss = new WebSocketServer({ server: testServer });

        // Start server on random port
        await new Promise((resolve) => {
            testServer.listen(0, () => {
                testPort = testServer.address().port;
                resolve();
            });
        });

        // Mock WebSocket server behavior
        testWss.on('connection', (ws, request) => {
            console.log('Test WebSocket connection established');

            // Handle different message types
            ws.on('message', (data) => {
                try {
                    const message = JSON.parse(data.toString());
                    handleTestMessage(ws, message);
                } catch (error) {
                    console.error('Error parsing message:', error);
                }
            });

            // Send initial session created message
            setTimeout(() => {
                ws.send(JSON.stringify({
                    type: 'session.created',
                    session: { id: 'test-session' }
                }));
            }, 100);
        });

        // Mock global objects
        global.AudioContext = vi.fn(() => mockAudioContext);
        global.AudioWorkletNode = vi.fn(() => ({
            connect: vi.fn(),
            disconnect: vi.fn(),
            port: {
                onmessage: null,
                postMessage: vi.fn()
            }
        }));
        // Mock navigator in a compatible way
        if (typeof global.navigator === 'undefined') {
            global.navigator = {};
        }
        global.navigator.mediaDevices = {
            getUserMedia: vi.fn(() => Promise.resolve(mockMediaStream))
        };
        global.WebSocket = WebSocket;
    });

    afterAll(async () => {
        // Cleanup test server
        if (testWss) {
            testWss.close();
        }
        if (testServer) {
            await new Promise((resolve) => {
                testServer.close(resolve);
            });
        }
    });

    describe('QwenOmniRealtimeClient', () => {
        let client;

        beforeEach(() => {
            client = new QwenOmniRealtimeClient({
                serverUrl: `ws://localhost:${testPort}/qwen-omni-ws`,
                sessionId: 'test-client-session'
            });
        });

        afterEach(async () => {
            if (client && client.isConnectedToService()) {
                await client.disconnect();
            }
        });

        it('should connect to WebSocket server successfully', async () => {
            const connected = await client.connect();
            expect(client.isConnectedToService()).toBe(true);
            expect(client.getState()).toBe('idle');
        });

        it('should handle state transitions correctly', async () => {
            const stateChanges = [];

            client = new QwenOmniRealtimeClient({
                serverUrl: `ws://localhost:${testPort}/qwen-omni-ws`,
                sessionId: 'test-state-session',
                onStateChange: (state) => {
                    stateChanges.push(state);
                }
            });

            await client.connect();
            await client.startListening();

            // Should transition: idle -> listening
            expect(stateChanges).toContain('listening');
            expect(client.getState()).toBe('listening');

            await client.stopListening();
            expect(client.getState()).toBe('idle');
        });

        it('should handle VAD events properly', async () => {
            const vadEvents = [];

            client = new QwenOmniRealtimeClient({
                serverUrl: `ws://localhost:${testPort}/qwen-omni-ws`,
                sessionId: 'test-vad-session',
                onVADEvent: (event) => {
                    vadEvents.push(event);
                }
            });

            await client.connect();
            await client.startListening();

            // Simulate VAD events from server
            const mockSocket = client.socket;
            mockSocket.dispatchEvent(new MessageEvent('message', {
                data: JSON.stringify({
                    type: 'input_audio_buffer.speech_started'
                })
            }));

            mockSocket.dispatchEvent(new MessageEvent('message', {
                data: JSON.stringify({
                    type: 'input_audio_buffer.speech_stopped'
                })
            }));

            // Wait for events to be processed
            await new Promise(resolve => setTimeout(resolve, 100));

            expect(vadEvents.length).toBeGreaterThan(0);
        });

        it('should handle audio streaming', async () => {
            const audioData = [];

            client = new QwenOmniRealtimeClient({
                serverUrl: `ws://localhost:${testPort}/qwen-omni-ws`,
                sessionId: 'test-audio-session',
                onAudioReceived: (data) => {
                    audioData.push(data);
                }
            });

            await client.connect();

            // Simulate audio data from server
            const mockAudioBase64 = Buffer.from(new Array(1024).fill(0)).toString('base64');
            const mockSocket = client.socket;
            mockSocket.dispatchEvent(new MessageEvent('message', {
                data: JSON.stringify({
                    type: 'response.audio.delta',
                    delta: mockAudioBase64
                })
            }));

            await new Promise(resolve => setTimeout(resolve, 100));
            expect(audioData.length).toBeGreaterThan(0);
        });

        it('should handle interruptions (barge-in)', async () => {
            await client.connect();
            await client.startListening();

            // Test interruption
            await client.cancelResponse();
            expect(client.getState()).toBe('listening');
        });

        it('should handle text messages', async () => {
            await client.connect();

            // Send text message
            await client.sendTextMessage('Hello, how are you?');

            // Should transition to speaking state
            expect(client.getState()).toBe('speaking');
        });

        it('should cleanup resources on disconnect', async () => {
            await client.connect();
            await client.startListening();

            const initialState = client.getState();
            expect(initialState).toBe('listening');

            await client.disconnect();
            expect(client.isConnectedToService()).toBe(false);
            expect(client.getState()).toBe('ended');
        });
    });

    describe('QwenOmniRealtimeWorkflow', () => {
        let workflow;

        beforeEach(() => {
            workflow = new QwenOmniRealtimeWorkflow({
                onStateChange: vi.fn(),
                onVADEvent: vi.fn(),
                onError: vi.fn()
            });
        });

        afterEach(async () => {
            if (workflow && workflow.isWorkflowActive()) {
                await workflow.stopRealtime();
            }
            if (workflow) {
                workflow.dispose();
            }
        });

        it('should initialize workflow correctly', () => {
            expect(workflow).toBeDefined();
            expect(workflow.isWorkflowActive()).toBe(false);
        });

        it('should start real-time workflow', async () => {
            const sessionId = 'test-workflow-session';

            // Mock the realtime client connection
            vi.spyOn(workflow, 'initializeRealtimeNode').mockResolvedValue({
                currentState: 'idle',
                realtimeClient: {},
                streamingActive: true
            });

            const result = await workflow.startRealtime(sessionId);

            expect(workflow.isWorkflowActive()).toBe(true);
            expect(result).toBeDefined();
        });

        it('should handle workflow state transitions', async () => {
            const sessionId = 'test-state-workflow';

            // Mock workflow nodes
            vi.spyOn(workflow, 'initializeRealtimeNode').mockResolvedValue({
                currentState: 'idle',
                streamingActive: true
            });
            vi.spyOn(workflow, 'startListeningNode').mockResolvedValue({
                currentState: 'listening',
                vadState: 'active'
            });

            await workflow.startRealtime(sessionId);

            // Test state retrieval
            const state = await workflow.getWorkflowState(sessionId);
            expect(state).toBeDefined();
        });

        it('should handle interruptions in workflow', async () => {
            const sessionId = 'test-interrupt-workflow';

            // Mock workflow setup
            workflow.realtimeClient = {
                cancelResponse: vi.fn().mockResolvedValue()
            };
            workflow.isActive = true;

            // Test interruption handling
            await workflow.handleInterruption(sessionId);

            expect(workflow.realtimeClient.cancelResponse).toHaveBeenCalled();
        });

        it('should cleanup workflow resources', async () => {
            const sessionId = 'test-cleanup-workflow';

            workflow.realtimeClient = {
                disconnect: vi.fn().mockResolvedValue()
            };
            workflow.streamingProcessor = {
                dispose: vi.fn()
            };
            workflow.isActive = true;

            await workflow.stopRealtime(sessionId);

            expect(workflow.realtimeClient.disconnect).toHaveBeenCalled();
            expect(workflow.streamingProcessor.dispose).toHaveBeenCalled();
            expect(workflow.isWorkflowActive()).toBe(false);
        });
    });

    describe('QwenOmniSessionManager', () => {
        let sessionManager;

        beforeEach(() => {
            sessionManager = new QwenOmniSessionManager({
                maxConcurrentSessions: 5,
                autoCleanup: false // Disable for testing
            });
        });

        afterEach(async () => {
            if (sessionManager) {
                await sessionManager.cleanup();
            }
        });

        it('should create and manage sessions', async () => {
            const sessionId = 'test-session-1';

            // Mock workflow creation
                vi.mock('../../src/agent/models/aliyun/workflows/QwenOmniRealtimeWorkflow.js', () => ({
        QwenOmniRealtimeWorkflow: vi.fn(() => ({
                    startRealtime: vi.fn().mockResolvedValue(),
                    stopRealtime: vi.fn().mockResolvedValue(),
                    dispose: vi.fn()
                })),
                createQwenOmniRealtimeWorkflow: vi.fn(() => ({
                    startRealtime: vi.fn().mockResolvedValue(),
                    stopRealtime: vi.fn().mockResolvedValue(),
                    dispose: vi.fn()
                }))
            }));

            const session = await sessionManager.createSession(sessionId);

            expect(session.sessionId).toBe(sessionId);
            expect(sessionManager.isSessionActive(sessionId)).toBe(true);
            expect(sessionManager.getSessionCount()).toBe(1);
        });

        it('should handle session state tracking', async () => {
            const sessionId = 'test-state-session';

            const session = await sessionManager.createSession(sessionId);

            // Test state retrieval
            const sessionInfo = sessionManager.getSession(sessionId);
            expect(sessionInfo).toBeDefined();
            expect(sessionInfo.sessionId).toBe(sessionId);
            expect(sessionInfo.isActive).toBe(true);
        });

        it('should handle session interruptions', async () => {
            const sessionId = 'test-interrupt-session';

            await sessionManager.createSession(sessionId);

            // Test interruption
            const result = await sessionManager.interruptSession(sessionId);
            expect(result).toBe(true);

            const session = sessionManager.getSession(sessionId);
            expect(session.interruptionCount).toBe(1);
        });

        it('should enforce concurrent session limits', async () => {
            const maxSessions = 2;
            sessionManager = new QwenOmniSessionManager({
                maxConcurrentSessions: maxSessions,
                autoCleanup: false
            });

            // Create maximum allowed sessions
            for (let i = 0; i < maxSessions; i++) {
                await sessionManager.createSession(`session-${i}`);
            }

            // Try to create one more session (should fail)
            await expect(
                sessionManager.createSession('overflow-session')
            ).rejects.toThrow('Maximum number of concurrent sessions reached');
        });

        it('should provide session metrics', async () => {
            const sessionId = 'test-metrics-session';

            await sessionManager.createSession(sessionId);

            const metrics = sessionManager.getSessionMetrics(sessionId);
            expect(metrics).toBeDefined();
            expect(metrics.sessionId).toBe(sessionId);
            expect(metrics.duration).toBeGreaterThanOrEqual(0);

            const aggregateMetrics = sessionManager.getAggregateMetrics();
            expect(aggregateMetrics.totalSessions).toBe(1);
            expect(aggregateMetrics.activeSessions).toBe(1);
        });

        it('should cleanup expired sessions', async () => {
            const sessionId = 'test-expired-session';

            // Create session with short timeout
            sessionManager = new QwenOmniSessionManager({
                sessionTimeout: 100, // 100ms
                idleTimeout: 50,     // 50ms
                autoCleanup: false
            });

            await sessionManager.createSession(sessionId);
            expect(sessionManager.isSessionActive(sessionId)).toBe(true);

            // Wait for session to expire
            await new Promise(resolve => setTimeout(resolve, 150));

            const cleaned = await sessionManager.cleanupExpiredSessions();
            expect(cleaned).toBe(1);
            expect(sessionManager.isSessionActive(sessionId)).toBe(false);
        });

        it('should handle session events', async () => {
            const events = [];

            sessionManager.on('sessionCreated', (event) => events.push(event));
            sessionManager.on('sessionStopped', (event) => events.push(event));
            sessionManager.on('stateChanged', (event) => events.push(event));

            const sessionId = 'test-events-session';
            await sessionManager.createSession(sessionId);
            await sessionManager.stopSession(sessionId);

            expect(events.length).toBeGreaterThan(0);
            expect(events.some(e => e.sessionId === sessionId)).toBe(true);
        });
    });

    describe('QwenOmniVAD', () => {
        let vad;

        beforeEach(() => {
            vad = new QwenOmniVAD({
                sessionId: 'test-vad-session',
                mode: 'client',
                threshold: 0.3
            });
        });

        afterEach(() => {
            if (vad) {
                vad.dispose();
            }
        });

        it('should initialize VAD correctly', async () => {
            await vad.initialize(mockAudioContext, mockMediaStream);

            expect(vad.isActive).toBe(true);
            expect(vad.currentState).toBe('listening');
        });

        it('should process audio data and detect speech', () => {
            // Create mock audio data with speech-like characteristics
            const speechAudioData = new Float32Array(1024);
            for (let i = 0; i < speechAudioData.length; i++) {
                speechAudioData[i] = Math.sin(i * 0.1) * 0.5; // Simulated speech
            }

            const events = [];
            vad.on('speechStart', (event) => events.push(event));
            vad.on('speechEnd', (event) => events.push(event));

            // Simulate VAD initialization
            vad.isActive = true;
            vad.currentState = 'listening';

            // Process speech audio
            vad.processAudioData(speechAudioData);

            // Process silence
            const silenceAudioData = new Float32Array(1024).fill(0.01);
            vad.processAudioData(silenceAudioData);

            expect(vad.getState().isActive).toBe(true);
        });

        it('should handle server VAD events', () => {
            const events = [];
            vad.on('speechStart', (event) => events.push(event));
            vad.on('speechEnd', (event) => events.push(event));

            // Simulate server VAD events
            vad.handleServerVADEvent({
                type: 'speech_start',
                timestamp: Date.now()
            });

            vad.handleServerVADEvent({
                type: 'speech_end',
                timestamp: Date.now()
            });

            expect(events.length).toBe(2);
        });

        it('should update configuration dynamically', () => {
            const initialThreshold = vad.config.threshold;
            const newThreshold = 0.7;

            vad.updateConfig({ threshold: newThreshold });

            expect(vad.config.threshold).toBe(newThreshold);
            expect(vad.adaptiveThreshold).toBe(newThreshold);
        });

        it('should provide accurate statistics', () => {
            vad.isActive = true;
            vad.stats.speechEvents = 5;
            vad.stats.totalSpeechTime = 10000;
            vad.stats.totalSilenceTime = 5000;

            const stats = vad.getStats();

            expect(stats.speechEvents).toBe(5);
            expect(stats.totalSpeechTime).toBe(10000);
            expect(stats.efficiency).toBeGreaterThanOrEqual(0);
        });

        it('should handle adaptive thresholding', () => {
            vad.config.adaptiveThreshold = true;
            vad.isActive = true;

            // Simulate audio with varying levels
            const audioLevels = [0.1, 0.2, 0.4, 0.6, 0.3, 0.15];

            audioLevels.forEach(level => {
                const audioData = new Float32Array(1024).fill(level);
                vad.processAudioData(audioData);
            });

            // Adaptive threshold should have been updated
            expect(vad.stats.adaptiveThresholdHistory.length).toBeGreaterThan(0);
        });
    });

    describe('StreamingAudioProcessor Integration', () => {
        let processor;

        beforeEach(() => {
            processor = new StreamingAudioProcessor({
                sampleRate: 24000,
                channels: 1,
                bitDepth: 16,
                logger: mockLogger
            });
        });

        afterEach(() => {
            if (processor) {
                processor.dispose();
            }
        });

        it('should process audio chunks in real-time', async () => {
            const chunkSize = 1024;
            const audioChunk = Buffer.alloc(chunkSize);

            // Fill with test audio data
            for (let i = 0; i < chunkSize; i++) {
                audioChunk[i] = Math.floor(Math.sin(i * 0.1) * 127);
            }

            await processor.processChunk(audioChunk);
            expect(processor.isProcessing()).toBe(true);
        });

        it('should handle multiple audio formats', async () => {
            const formats = ['pcm16', 'wav', 'mp3'];

            for (const format of formats) {
                const testData = Buffer.alloc(1024);

                try {
                    await processor.processChunk(testData, format);
                    // Should not throw for supported formats
                } catch (error) {
                    // Some formats might not be supported, that's okay
                    expect(error.message).toContain('format');
                }
            }
        });

        it('should maintain audio continuity', async () => {
            const chunks = [];

            // Generate multiple chunks
            for (let i = 0; i < 5; i++) {
                const chunk = Buffer.alloc(1024);
                chunk.fill(i * 10); // Different pattern for each chunk
                chunks.push(chunk);
            }

            // Process chunks sequentially
            for (const chunk of chunks) {
                await processor.processChunk(chunk);
            }

            // Audio should maintain continuity
            expect(processor.getPlaybackPosition()).toBeGreaterThan(0);
        });
    });

    describe('End-to-End Integration', () => {
        let sessionManager;
        let client;
        let workflow;

        beforeEach(() => {
            sessionManager = new QwenOmniSessionManager({
                autoCleanup: false
            });
        });

        afterEach(async () => {
            if (client) {
                await client.disconnect();
            }
            if (workflow) {
                await workflow.stopRealtime();
                workflow.dispose();
            }
            if (sessionManager) {
                await sessionManager.cleanup();
            }
        });

        it('should handle complete conversation flow', async () => {
            const sessionId = 'e2e-conversation-session';
            const events = [];

            // Create session with event tracking
            const session = await sessionManager.createSession(sessionId, {
                onStateChange: (state) => events.push({ type: 'state', state }),
                onVADEvent: (event) => events.push({ type: 'vad', event }),
                onTranscriptReceived: (transcript) => events.push({ type: 'transcript', transcript })
            });

            expect(session.sessionId).toBe(sessionId);
            expect(sessionManager.isSessionActive(sessionId)).toBe(true);

            // Simulate conversation events
            sessionManager.emit('stateChanged', { sessionId, state: 'listening' });
            sessionManager.emit('vadEvent', { sessionId, event: { type: 'speech_start' } });
            sessionManager.emit('transcriptReceived', { sessionId, transcript: 'Hello' });
            sessionManager.emit('vadEvent', { sessionId, event: { type: 'speech_end' } });
            sessionManager.emit('stateChanged', { sessionId, state: 'speaking' });

            // Wait for events to propagate
            await new Promise(resolve => setTimeout(resolve, 100));

            // Verify session metrics
            const metrics = sessionManager.getSessionMetrics(sessionId);
            expect(metrics).toBeDefined();
            expect(metrics.sessionId).toBe(sessionId);

            // Stop session
            await sessionManager.stopSession(sessionId);
            expect(sessionManager.isSessionActive(sessionId)).toBe(false);
        });

        it('should handle multiple concurrent sessions', async () => {
            const sessionIds = ['concurrent-1', 'concurrent-2', 'concurrent-3'];
            const sessions = [];

            // Create multiple sessions
            for (const sessionId of sessionIds) {
                const session = await sessionManager.createSession(sessionId);
                sessions.push(session);
            }

            expect(sessionManager.getSessionCount()).toBe(sessionIds.length);

            // Verify all sessions are active
            for (const sessionId of sessionIds) {
                expect(sessionManager.isSessionActive(sessionId)).toBe(true);
            }

            // Get aggregate metrics
            const aggregateMetrics = sessionManager.getAggregateMetrics();
            expect(aggregateMetrics.totalSessions).toBe(sessionIds.length);
            expect(aggregateMetrics.activeSessions).toBe(sessionIds.length);

            // Stop all sessions
            for (const sessionId of sessionIds) {
                await sessionManager.stopSession(sessionId);
            }

            expect(sessionManager.getSessionCount()).toBe(0);
        });

        it('should handle error recovery', async () => {
            const sessionId = 'error-recovery-session';
            const errors = [];

            const session = await sessionManager.createSession(sessionId, {
                onError: (error) => errors.push(error)
            });

            // Simulate errors
            sessionManager.emit('sessionError', {
                sessionId,
                error: new Error('Test error'),
                timestamp: Date.now()
            });

            // Session should still be manageable
            expect(sessionManager.isSessionActive(sessionId)).toBe(true);

            const sessionInfo = sessionManager.getSession(sessionId);
            expect(sessionInfo.errorCount).toBe(1);
        });
    });
});

// Helper function to handle test WebSocket messages
function handleTestMessage(ws, message) {
    console.log('Received test message:', message.type);

    switch (message.type) {
        case 'session.update':
            ws.send(JSON.stringify({
                type: 'session.updated',
                session: message.session
            }));
            break;

        case 'input_audio_buffer.append':
            // Echo back VAD events
            setTimeout(() => {
                ws.send(JSON.stringify({
                    type: 'input_audio_buffer.speech_started'
                }));
            }, 50);

            setTimeout(() => {
                ws.send(JSON.stringify({
                    type: 'input_audio_buffer.speech_stopped'
                }));
            }, 200);
            break;

        case 'input_audio_buffer.commit':
            ws.send(JSON.stringify({
                type: 'input_audio_buffer.committed'
            }));
            break;

        case 'response.create':
            ws.send(JSON.stringify({
                type: 'response.created'
            }));

            // Send mock audio response
            setTimeout(() => {
                const mockAudio = Buffer.from(new Array(1024).fill(0)).toString('base64');
                ws.send(JSON.stringify({
                    type: 'response.audio.delta',
                    delta: mockAudio
                }));
            }, 100);

            setTimeout(() => {
                ws.send(JSON.stringify({
                    type: 'response.done'
                }));
            }, 300);
            break;

        case 'response.cancel':
            ws.send(JSON.stringify({
                type: 'response.cancelled'
            }));
            break;

        case 'conversation.item.create':
            ws.send(JSON.stringify({
                type: 'conversation.item.created',
                item: message.item
            }));
            break;

        default:
            console.log('Unhandled message type:', message.type);
    }
} 