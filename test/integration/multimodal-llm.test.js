/**
 * Test suite for multimodal integration with LangGraph tools
 * Tests the default use case: audio + video input → LLM → audio output + tool calls
 * Updated to focus on WebSocket realtime API for qwen-omni-turbo-realtime models
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { AliyunBailianChatModel } from '@/agent/models/AliyunBailianChatModel.js';
import {
    normalizeInput,
    processMultimodal,
    validateMultimodalInput,
    MULTIMODAL_LIMITS
} from '@/media/utils/multimodalUtils.js';

// Mock WebSocket for testing - focused on multimodal scenarios
class MockMultimodalWebSocket {
    constructor(url) {
        this.url = url;
        this.readyState = MockMultimodalWebSocket.OPEN;
        this.messages = [];

        // Simulate connection
        setTimeout(() => {
            if (this.onopen) this.onopen();
        }, 10);
    }

    static get OPEN() { return 1; }
    static get CLOSED() { return 3; }

    send(data) {
        this.messages.push(JSON.parse(data));

        // Simulate server responses based on message type
        const message = JSON.parse(data);
        setTimeout(() => {
            this._simulateServerResponse(message);
        }, 50);
    }

    close() {
        this.readyState = MockMultimodalWebSocket.CLOSED;
        if (this.onclose) this.onclose({ code: 1000, reason: 'Normal closure' });
    }

    _simulateServerResponse(clientMessage) {
        if (!this.onmessage) return;

        switch (clientMessage.type) {
            case 'session.update':
                // Simulate session.created with tool support
                this.onmessage({
                    data: JSON.stringify({
                        event_id: 'event_test_123',
                        type: 'session.created',
                        session: {
                            id: 'sess_test_123',
                            object: 'realtime.session',
                            model: 'qwen-omni-turbo-realtime-latest',
                            modalities: ['text', 'audio'],
                            voice: 'Serena',
                            input_audio_format: 'pcm16',
                            output_audio_format: 'pcm16',
                            turn_detection: {
                                type: 'server_vad',
                                threshold: 0.5,
                                silence_duration_ms: 800
                            },
                            tools: clientMessage.session.tools || [],
                            tool_choice: 'auto'
                        }
                    })
                });
                break;

            case 'input_audio_buffer.append':
                // Simulate VAD detection for multimodal input
                this.onmessage({
                    data: JSON.stringify({
                        event_id: 'event_vad_start',
                        type: 'input_audio_buffer.speech_started',
                        audio_start_ms: 100,
                        item_id: 'item_test_audio'
                    })
                });

                // Auto-commit after detecting speech for tool call demo
                setTimeout(() => {
                    this.onmessage({
                        data: JSON.stringify({
                            event_id: 'event_vad_stop',
                            type: 'input_audio_buffer.speech_stopped',
                            audio_end_ms: 2000,
                            item_id: 'item_test_audio'
                        })
                    });

                    // Followed by commit
                    setTimeout(() => {
                        this.onmessage({
                            data: JSON.stringify({
                                event_id: 'event_audio_committed',
                                type: 'input_audio_buffer.committed',
                                item_id: 'item_test_audio'
                            })
                        });

                        // Generate response with tool call
                        this._generateMultimodalResponse();
                    }, 100);
                }, 500);
                break;

            case 'input_image_buffer.append':
                // Acknowledge image frame for multimodal processing
                console.log('Test: Multimodal image frame received');
                break;

            case 'conversation.item.create':
                // Simulate response for explicit text-based tool call requests
                this._generateMultimodalResponse();
                break;

            default:
                console.log(`Test: Unhandled event ${clientMessage.type}`);
        }
    }

    _generateMultimodalResponse() {
        // Simulate response creation
        this.onmessage({
            data: JSON.stringify({
                event_id: 'event_response_created',
                type: 'response.created',
                response: {
                    id: 'resp_test_123',
                    status: 'in_progress',
                    modalities: ['text', 'audio']
                }
            })
        });

        // Simulate tool call detection and execution
        setTimeout(() => {
            this.onmessage({
                data: JSON.stringify({
                    event_id: 'event_tool_call',
                    type: 'response.content_part.added',
                    response_id: 'resp_test_123',
                    item_id: 'item_test_resp',
                    part: {
                        type: 'function',
                        name: 'trigger_animation',
                        call_id: 'call_test_123',
                        arguments: '{"animation": "wave", "duration": 2}'
                    }
                })
            });
        }, 100);

        // Simulate audio response with tool confirmation
        setTimeout(() => {
            this.onmessage({
                data: JSON.stringify({
                    event_id: 'event_audio_delta',
                    type: 'response.audio.delta',
                    response_id: 'resp_test_123',
                    item_id: 'item_test_resp',
                    delta: 'dGVzdCBhdWRpbyBkYXRh' // base64 "test audio data"
                })
            });
        }, 200);

        // Simulate text response
        setTimeout(() => {
            this.onmessage({
                data: JSON.stringify({
                    event_id: 'event_text_delta',
                    type: 'response.text.delta',
                    response_id: 'resp_test_123',
                    item_id: 'item_test_resp',
                    delta: 'I\'ll trigger the wave animation for you!'
                })
            });
        }, 250);

        // Complete response
        setTimeout(() => {
            this.onmessage({
                data: JSON.stringify({
                    event_id: 'event_response_done',
                    type: 'response.done',
                    response: {
                        id: 'resp_test_123',
                        status: 'completed',
                        output: [{
                            content: [{
                                type: 'text',
                                text: 'I\'ll trigger a wave animation for you!'
                            }, {
                                type: 'audio',
                                audio: 'base64_audio_response_data'
                            }]
                        }]
                    }
                })
            });
        }, 300);
    }
}

// Mock global WebSocket
global.WebSocket = MockMultimodalWebSocket;
global.window = { WebSocket: MockMultimodalWebSocket };

describe('Aliyun Qwen-Omni Multimodal WebSocket Integration', () => {
    let model;
    let mockCallbacks;

    beforeEach(() => {
        // Mock environment variables
        process.env.VITE_DASHSCOPE_API_KEY = 'test-api-key-12345';
        process.env.VITE_ALIYUN_MODEL = 'qwen-omni-turbo-realtime-latest';

        // Create mock callbacks
        mockCallbacks = {
            onVoiceActivityDetected: vi.fn(),
            onVoiceActivityStopped: vi.fn(),
            onAudioReceived: vi.fn(),
            onTranscriptReceived: vi.fn(),
            onToolCall: vi.fn(),
            onError: vi.fn()
        };

        // Create model instance
        model = new AliyunBailianChatModel({
            temperature: 0.7,
            maxTokens: 2048
        });
    });

    afterEach(() => {
        if (model) {
            model.closeRealtimeMode();
        }
        vi.clearAllMocks();
    });

    describe('Multimodal Input Processing', () => {
        it('should send audio data in correct format', async () => {
            // Initialize realtime mode
            const success = await model.initializeRealtimeMode(mockCallbacks);
            expect(success).toBe(true);

            // Create test audio data (PCM16 format)
            const audioData = new Float32Array([0.1, 0.2, -0.1, -0.2]);
            const result = model.sendRealtimeAudio(audioData);

            expect(result).toBe(true);

            // Wait for WebSocket processing
            await new Promise(resolve => setTimeout(resolve, 100));

            // Check if VAD was triggered
            expect(mockCallbacks.onVoiceActivityDetected).toHaveBeenCalled();
        });

        it('should send video frames according to Aliyun specifications', async () => {
            const success = await model.initializeRealtimeMode(mockCallbacks);
            expect(success).toBe(true);

            // Create test image data (Base64 JPEG)
            const testImageBase64 = 'dGVzdCBpbWFnZSBkYXRh'; // base64 "test image data"
            const result = model.sendRealtimeImage(testImageBase64);

            expect(result).toBe(true);
        });

        it('should handle multimodal input (audio + video) correctly', async () => {
            const success = await model.initializeRealtimeMode(mockCallbacks);
            expect(success).toBe(true);

            // Test multimodal data
            const multimodalData = {
                audio: new Float32Array([0.1, 0.2, -0.1, -0.2]),
                images: [
                    'dGVzdCBpbWFnZSBkYXRhMQ==', // base64 "test image data1"
                    'dGVzdCBpbWFnZSBkYXRhMg=='  // base64 "test image data2"
                ]
            };

            const result = await model.sendRealtimeMultimodal(multimodalData);
            expect(result).toBe(true);

            // Wait for processing
            await new Promise(resolve => setTimeout(resolve, 600));

            // Should have triggered VAD
            expect(mockCallbacks.onVoiceActivityDetected).toHaveBeenCalled();
        });
    });

    describe('LangGraph Tool Integration', () => {
        it('should bind tools correctly for LangGraph compatibility', () => {
            const testTools = [
                {
                    name: 'trigger_animation',
                    description: 'Trigger avatar animation',
                    schema: {
                        type: 'object',
                        properties: {
                            animation: { type: 'string' },
                            duration: { type: 'number' }
                        },
                        required: ['animation']
                    }
                },
                {
                    name: 'play_audio',
                    description: 'Play audio file',
                    schema: {
                        type: 'object',
                        properties: {
                            file: { type: 'string' },
                            volume: { type: 'number' }
                        },
                        required: ['file']
                    }
                }
            ];

            const boundModel = model.bindTools(testTools);

            expect(boundModel).toBeInstanceOf(AliyunBailianChatModel);
            expect(boundModel.boundTools).toHaveLength(2);
            expect(boundModel.boundTools[0].function.name).toBe('trigger_animation');
            expect(boundModel.boundTools[1].function.name).toBe('play_audio');
        });

        it('should include tools in realtime session configuration', async () => {
            const testTools = [
                {
                    name: 'trigger_animation',
                    description: 'Trigger avatar animation',
                    schema: {
                        type: 'object',
                        properties: {
                            animation: { type: 'string' },
                            duration: { type: 'number' }
                        }
                    }
                }
            ];

            const boundModel = model.bindTools(testTools);
            const success = await boundModel.initializeRealtimeMode(mockCallbacks);

            expect(success).toBe(true);
            expect(boundModel.realtimeSession?.tools).toBeDefined();
        });

        it('should handle tool calls in responses', async () => {
            const testTools = [
                {
                    name: 'trigger_animation',
                    description: 'Trigger avatar animation',
                    schema: {
                        type: 'object',
                        properties: {
                            animation: { type: 'string' },
                            duration: { type: 'number' }
                        }
                    }
                }
            ];

            const boundModel = model.bindTools(testTools);
            const success = await boundModel.initializeRealtimeMode(mockCallbacks);
            expect(success).toBe(true);

            // Send text that should trigger animation tool
            const result = boundModel.sendRealtimeText('Please animate the avatar with a wave gesture');
            expect(result).toBe(true);

            // Wait for response processing
            await new Promise(resolve => setTimeout(resolve, 300));

            // Should have received tool call
            expect(mockCallbacks.onToolCall).toHaveBeenCalledWith({
                id: 'call_test_123',
                name: 'trigger_animation',
                arguments: '{"animation": "wave", "duration": 2}'
            });

            // Should also have received audio response
            expect(mockCallbacks.onAudioReceived).toHaveBeenCalledWith('dGVzdCBhdWRpbyBkYXRh');
        });
    });

    describe('Server VAD Integration', () => {
        it('should configure server VAD according to Aliyun specifications', async () => {
            const success = await model.initializeRealtimeMode(mockCallbacks);
            expect(success).toBe(true);

            expect(model.realtimeSession?.turn_detection?.type).toBe('server_vad');
            expect(model.realtimeSession?.turn_detection?.threshold).toBe(0.5);
            expect(model.realtimeSession?.turn_detection?.silence_duration_ms).toBe(800);
        });

        it('should support push-to-talk mode switching', async () => {
            const success = await model.initializeRealtimeMode(mockCallbacks);
            expect(success).toBe(true);

            // Switch to push-to-talk
            const pushToTalkResult = model.enablePushToTalkMode();
            expect(pushToTalkResult).toBe(true);

            // Switch back to server VAD
            const serverVADResult = model.enableServerVADMode();
            expect(serverVADResult).toBe(true);
        });

        it('should handle audio buffer operations', async () => {
            const success = await model.initializeRealtimeMode(mockCallbacks);
            expect(success).toBe(true);

            // Test audio commit (for push-to-talk mode)
            const commitResult = model.commitRealtimeAudio();
            expect(commitResult).toBe(true);

            // Test audio buffer clear
            const clearResult = model.clearRealtimeAudioBuffer();
            expect(clearResult).toBe(true);
        });
    });

    describe('Error Handling', () => {
        it('should handle WebSocket connection errors gracefully', async () => {
            // Mock WebSocket to fail
            const originalWebSocket = global.WebSocket;
            global.WebSocket = class extends MockWebSocket {
                constructor(url) {
                    super(url);
                    setTimeout(() => {
                        if (this.onerror) {
                            this.onerror(new Error('Connection failed'));
                        }
                    }, 20);
                }
            };

            const success = await model.initializeRealtimeMode(mockCallbacks);
            expect(success).toBe(false);
            expect(mockCallbacks.onError).toHaveBeenCalled();

            // Restore original WebSocket
            global.WebSocket = originalWebSocket;
        });

        it('should validate audio format requirements', () => {
            // Test invalid audio data
            expect(() => {
                model.sendRealtimeAudio(null);
            }).not.toThrow(); // Should handle gracefully and return false

            expect(() => {
                model.sendRealtimeAudio(undefined);
            }).not.toThrow();
        });
    });

    describe('Performance Optimization', () => {
        it('should respect video frame rate limits (2fps for Aliyun)', async () => {
            const success = await model.initializeRealtimeMode(mockCallbacks);
            expect(success).toBe(true);

            const startTime = Date.now();
            const multimodalData = {
                audio: new Float32Array([0.1, 0.2]),
                images: ['aW1hZ2UxZGF0YQ==', 'aW1hZ2UyZGF0YQ==', 'aW1hZ2UzZGF0YQ=='] // 3 frames
            };

            await model.sendRealtimeMultimodal(multimodalData);
            const endTime = Date.now();

            // Should take at least 1 second for 3 frames at 2fps (500ms delay between frames)
            expect(endTime - startTime).toBeGreaterThan(1000);
        });

        it('should handle large video frame validation', () => {
            // Create a large base64 string (simulating >500KB frame)
            const largeFrame = 'a'.repeat(700000); // ~700KB when base64 decoded

            // Should log warning but not throw
            expect(() => {
                model._prepareVideoFramesForAliyun?.([largeFrame]);
            }).not.toThrow();
        });
    });
});

// New test section for multimodal utilities
describe('Multimodal Processing Utilities', () => {
    describe('Input Normalization', () => {
        it('should normalize text input correctly', () => {
            const result = normalizeInput('Hello world');

            expect(result.text).toBe('Hello world');
            expect(result.audio).toBeNull();
            expect(result.video).toBeNull();
            expect(result.metadata.inputType).toBe('text');
            expect(result.metadata.isMultimodal).toBe(false);
        });

        it('should normalize audio input correctly', () => {
            const audioData = new Float32Array([0.1, 0.2, 0.3]);
            const result = normalizeInput(audioData);

            expect(result.text).toBeNull();
            expect(result.audio).toBeInstanceOf(Float32Array);
            expect(result.video).toBeNull();
            expect(result.metadata.inputType).toBe('audio');
            expect(result.metadata.isMultimodal).toBe(false);
        });

        it('should normalize multimodal input correctly', () => {
            const input = {
                text: 'Describe this',
                audio: new Float32Array([0.1, 0.2]),
                video: ['base64frame1', 'base64frame2']
            };

            const result = normalizeInput(input);

            expect(result.text).toBe('Describe this');
            expect(result.audio).toBeInstanceOf(Float32Array);
            expect(result.video).toHaveLength(2);
            expect(result.metadata.inputType).toBe('multimodal');
            expect(result.metadata.isMultimodal).toBe(true);
        });

        it('should handle video streaming context', () => {
            const options = {
                currentVideoFrames: ['frame1', 'frame2'],
                enableVideoInput: true,
                isVideoStreaming: true
            };

            const result = normalizeInput('Hello', options);

            expect(result.video).toHaveLength(2);
            expect(result.video).toEqual(['frame1', 'frame2']);
        });
    });

    describe('Input Validation', () => {
        it('should validate valid multimodal input', () => {
            const input = {
                text: 'Hello',
                audio: new Float32Array([0.1, 0.2]),
                video: ['validframe'],
                metadata: { inputType: 'multimodal', isMultimodal: true }
            };

            const result = validateMultimodalInput(input);

            expect(result.isValid).toBe(true);
            expect(result.errors).toHaveLength(0);
        });

        it('should detect oversized video frames', () => {
            const largeFrame = 'a'.repeat(1000 * 1000); // Simulate large frame
            const input = {
                video: [largeFrame],
                metadata: { inputType: 'video' }
            };

            const result = validateMultimodalInput(input);

            expect(result.warnings.length).toBeGreaterThan(0);
            expect(result.warnings[0]).toContain('exceeds recommended limit');
        });
    });

    describe('Multimodal Processing', () => {
        it('should process multimodal content correctly', () => {
            const normalizedInput = {
                text: 'Hello',
                audio: new Float32Array([0.1, 0.2, 0.3]),
                video: ['frame1', 'frame2'],
                metadata: { inputType: 'multimodal', isMultimodal: true }
            };

            const result = processMultimodal(normalizedInput);

            expect(result.text).toBe('Hello');
            expect(result.videoFrames).toHaveLength(2);
            expect(result.audioData).toBeInstanceOf(Float32Array);
            expect(result.audioBuffer).toBeDefined();
            expect(typeof result.audioBuffer).toBe('string');
        });

        it('should handle audio-only processing', () => {
            const normalizedInput = {
                text: null,
                audio: new Float32Array([0.1, 0.2]),
                video: null,
                metadata: { inputType: 'audio', isMultimodal: false }
            };

            const result = processMultimodal(normalizedInput);

            expect(result.audioData).toBeInstanceOf(Float32Array);
            expect(result.audioBuffer).toBeDefined();
            expect(result.videoFrames).toBeNull();
        });
    });

    describe('Format Constants', () => {
        it('should have correct multimodal limits', () => {
            expect(MULTIMODAL_LIMITS.MAX_VIDEO_FRAME_SIZE_KB).toBe(500);
            expect(MULTIMODAL_LIMITS.RECOMMENDED_FRAME_RATE).toBe(2);
            expect(MULTIMODAL_LIMITS.AUDIO_FORMAT.SAMPLE_RATE).toBe(24000);
            expect(MULTIMODAL_LIMITS.AUDIO_FORMAT.CHANNELS).toBe(1);
            expect(MULTIMODAL_LIMITS.AUDIO_FORMAT.BIT_DEPTH).toBe(16);
        });
    });
});

describe('Configuration Validation', () => {
    it('should use correct default model for Qwen-Omni', () => {
        const model = new AliyunBailianChatModel();
        expect(model.model).toBe('qwen-omni-turbo-realtime-latest');
    });

    it('should use correct WebSocket endpoint', () => {
        const model = new AliyunBailianChatModel();
        expect(model.realtimeEndpoint).toBe('wss://dashscope.aliyuncs.com/api-ws/v1/realtime');
    });

    it('should support all required voice options', () => {
        const supportedVoices = ['Chelsie', 'Serena', 'Ethan', 'Cherry'];

        supportedVoices.forEach(voice => {
            const model = new AliyunBailianChatModel();
            expect(() => model._setupRealtimeSession?.({ voice })).not.toThrow();
        });
    });
});
