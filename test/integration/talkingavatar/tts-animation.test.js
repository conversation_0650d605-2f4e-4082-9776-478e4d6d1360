/**
 * TTS Animation Integration Tests
 * Simplified tests for TTS services and avatar animation integration
 * Tests TalkingAvatar mesh filename matching with SparkTTS role integration
 */

import { vi, describe, test, expect, beforeEach } from 'vitest';

// Mock services with simplified logic
const mockSparkTTSService = {
    getRoles: vi.fn(() => Promise.resolve(['alice', 'bob', 'emma_character', 'john_avatar', 'sarah_model'])),
    setClonedVoice: vi.fn((roleName) => {
        const roles = ['alice', 'bob', 'emma_character', 'john_avatar', 'sarah_model'];
        return Promise.resolve({
            success: true,
            voiceType: roles.includes(roleName) ? 'cloned' : 'regular',
            message: `Voice setup for ${roleName}`
        });
    }),
    speak: vi.fn((text, options = {}) => {
        const { roleName, useClonedVoice = false } = options;
        return Promise.resolve({
            success: true,
            audioData: new ArrayBuffer(1024),
            duration: text.length * 0.08,
            voiceType: useClonedVoice ? 'cloned' : 'regular',
            voice: useClonedVoice ? `${roleName}_cloned` : 'default',
            metadata: { roleName, useClonedVoice, processingTime: 150 }
        });
    })
};

const mockRegularTTSService = {
    synthesizeSpeech: vi.fn(({ text, voice = 'default' }) => {
        return Promise.resolve({
            success: true,
            audioData: new ArrayBuffer(1024),
            duration: text.length * 0.08,
            voiceType: 'regular',
            voice,
            metadata: { processingTime: 100 }
        });
    })
};

// Helper function to create fresh avatar instances
const createMockTalkingAvatar = () => ({
    meshFilename: null,
    ttsService: null,
    isInitialized: false,
    currentRole: null,
    voiceType: null,

    loadMesh: vi.fn(function (filename) {
        this.meshFilename = filename;
        return Promise.resolve({ success: true, filename });
    }),

    getMeshFilename: vi.fn(function () {
        return this.meshFilename;
    }),

    _initializeTTSService: vi.fn(async function () {
        if (!this.meshFilename) {
            this.ttsService = mockRegularTTSService;
            this.voiceType = 'regular';
            this.isInitialized = true;
            return;
        }

        // Extract character name from mesh filename
        const characterName = this.meshFilename.replace(/\.(glb|fbx|obj)$/i, '');

        try {
            const roles = await mockSparkTTSService.getRoles();

            if (roles.includes(characterName)) {
                const result = await mockSparkTTSService.setClonedVoice(characterName);

                if (result.success && result.voiceType === 'cloned') {
                    this.ttsService = mockSparkTTSService;
                    this.currentRole = characterName;
                    this.voiceType = 'cloned';
                } else {
                    this.ttsService = mockRegularTTSService;
                    this.voiceType = 'regular';
                }
            } else {
                this.ttsService = mockRegularTTSService;
                this.voiceType = 'regular';
            }
        } catch (error) {
            this.ttsService = mockRegularTTSService;
            this.voiceType = 'regular';
        }

        this.isInitialized = true;
    }),

    speak: vi.fn(async function (text, options = {}) {
        if (!this.isInitialized || !this.ttsService) {
            throw new Error('TalkingAvatar not initialized');
        }

        if (this.voiceType === 'cloned' && this.currentRole) {
            return await this.ttsService.speak(text, {
                roleName: this.currentRole,
                useClonedVoice: true,
                ...options
            });
        } else {
            return await this.ttsService.synthesizeSpeech({
                text,
                voice: options.voice || 'default',
                ...options
            });
        }
    }),

    animatemouth: vi.fn(function (audioData) {
        const duration = audioData.duration || 1.0;
        const frames = Math.floor(duration * 30); // 30 FPS

        return Promise.resolve({
            success: true,
            animation: {
                type: 'mouth',
                frames,
                duration,
                keyframes: Array(frames).fill().map((_, i) => ({
                    time: i / 30,
                    value: Math.sin(i * 0.5) * 0.5 + 0.5
                }))
            }
        });
    })
});

describe('TTS Animation Integration Tests', () => {
    let mockTalkingAvatar;

    beforeEach(() => {
        vi.clearAllMocks();
        mockTalkingAvatar = createMockTalkingAvatar();
    });

    describe('Mesh Filename to Role Matching', () => {
        test('should match simple mesh filename to SparkTTS role', async () => {
            await mockTalkingAvatar.loadMesh('alice.glb');
            await mockTalkingAvatar._initializeTTSService();

            expect(mockTalkingAvatar.meshFilename).toBe('alice.glb');
            expect(mockSparkTTSService.getRoles).toHaveBeenCalled();
            expect(mockSparkTTSService.setClonedVoice).toHaveBeenCalledWith('alice');
            expect(mockTalkingAvatar.voiceType).toBe('cloned');
            expect(mockTalkingAvatar.currentRole).toBe('alice');
        });

        test('should match complex mesh filename to SparkTTS role', async () => {
            await mockTalkingAvatar.loadMesh('emma_character.fbx');
            await mockTalkingAvatar._initializeTTSService();

            expect(mockSparkTTSService.setClonedVoice).toHaveBeenCalledWith('emma_character');
            expect(mockTalkingAvatar.voiceType).toBe('cloned');
            expect(mockTalkingAvatar.currentRole).toBe('emma_character');
        });

        test('should fall back to regular TTS when no role match found', async () => {
            await mockTalkingAvatar.loadMesh('unknown_character.glb');
            await mockTalkingAvatar._initializeTTSService();

            expect(mockSparkTTSService.getRoles).toHaveBeenCalled();
            expect(mockSparkTTSService.setClonedVoice).not.toHaveBeenCalled();
            expect(mockTalkingAvatar.voiceType).toBe('regular');
            expect(mockTalkingAvatar.ttsService).toBe(mockRegularTTSService);
        });

        test('should handle different file extensions', async () => {
            const testCases = [
                { filename: 'bob.glb', expectedRole: 'bob' },
                { filename: 'alice.fbx', expectedRole: 'alice' },
                { filename: 'emma_character.obj', expectedRole: 'emma_character' }
            ];

            for (const testCase of testCases) {
                const avatar = createMockTalkingAvatar();
                vi.clearAllMocks();

                await avatar.loadMesh(testCase.filename);
                await avatar._initializeTTSService();

                expect(mockSparkTTSService.setClonedVoice).toHaveBeenCalledWith(testCase.expectedRole);
            }
        });
    });

    describe('TTS and Animation Synchronization', () => {
        test('should generate audio and animation for cloned voice', async () => {
            await mockTalkingAvatar.loadMesh('alice.glb');
            await mockTalkingAvatar._initializeTTSService();

            const testText = 'Hello, I am Alice speaking with animation!';
            const speechResult = await mockTalkingAvatar.speak(testText);
            const animationResult = await mockTalkingAvatar.animatemouth(speechResult);

            expect(speechResult.success).toBe(true);
            expect(speechResult.voiceType).toBe('cloned');
            expect(speechResult.voice).toBe('alice_cloned');

            expect(animationResult.success).toBe(true);
            expect(animationResult.animation.type).toBe('mouth');
            expect(animationResult.animation.frames).toBeGreaterThan(0);
            expect(animationResult.animation.keyframes).toHaveLength(animationResult.animation.frames);
        });

        test('should generate audio and animation for regular voice', async () => {
            await mockTalkingAvatar.loadMesh('unknown_character.glb');
            await mockTalkingAvatar._initializeTTSService();

            const testText = 'Hello, using regular voice with animation!';
            const speechResult = await mockTalkingAvatar.speak(testText);
            const animationResult = await mockTalkingAvatar.animatemouth(speechResult);

            expect(speechResult.success).toBe(true);
            expect(speechResult.voiceType).toBe('regular');

            expect(animationResult.success).toBe(true);
            expect(animationResult.animation.type).toBe('mouth');
        });

        test('should handle animation timing based on audio duration', async () => {
            await mockTalkingAvatar.loadMesh('alice.glb');
            await mockTalkingAvatar._initializeTTSService();

            const shortText = 'Hi!';
            const longText = 'This is a much longer text that should result in a longer animation sequence.';

            const shortSpeech = await mockTalkingAvatar.speak(shortText);
            const shortAnimation = await mockTalkingAvatar.animatemouth(shortSpeech);

            const longSpeech = await mockTalkingAvatar.speak(longText);
            const longAnimation = await mockTalkingAvatar.animatemouth(longSpeech);

            expect(longAnimation.animation.duration).toBeGreaterThan(shortAnimation.animation.duration);
            expect(longAnimation.animation.frames).toBeGreaterThan(shortAnimation.animation.frames);
        });
    });

    describe('Error Handling', () => {
        test('should handle SparkTTS service errors gracefully', async () => {
            mockSparkTTSService.getRoles.mockRejectedValueOnce(new Error('SparkTTS service unavailable'));

            await mockTalkingAvatar.loadMesh('alice.glb');
            await mockTalkingAvatar._initializeTTSService();

            expect(mockTalkingAvatar.voiceType).toBe('regular');
            expect(mockTalkingAvatar.ttsService).toBe(mockRegularTTSService);

            const speechResult = await mockTalkingAvatar.speak('Fallback test');
            expect(speechResult.success).toBe(true);
            expect(speechResult.voiceType).toBe('regular');
        });

        test('should handle cloned voice setup failure', async () => {
            mockSparkTTSService.setClonedVoice.mockResolvedValueOnce({
                success: false,
                error: 'Voice cloning failed'
            });

            await mockTalkingAvatar.loadMesh('alice.glb');
            await mockTalkingAvatar._initializeTTSService();

            expect(mockTalkingAvatar.voiceType).toBe('regular');
            expect(mockTalkingAvatar.ttsService).toBe(mockRegularTTSService);
        });

        test('should handle uninitialized avatar speech attempt', async () => {
            await expect(mockTalkingAvatar.speak('Test')).rejects.toThrow('TalkingAvatar not initialized');
        });
    });

    describe('Performance', () => {
        test('should handle multiple avatar instances', async () => {
            const avatar1 = createMockTalkingAvatar();
            const avatar2 = createMockTalkingAvatar();

            await avatar1.loadMesh('alice.glb');
            await avatar1._initializeTTSService();

            await avatar2.loadMesh('bob.glb');
            await avatar2._initializeTTSService();

            expect(avatar1.currentRole).toBe('alice');
            expect(avatar2.currentRole).toBe('bob');

            const speech1 = await avatar1.speak('Hello from Alice');
            const speech2 = await avatar2.speak('Hello from Bob');

            expect(speech1.voice).toBe('alice_cloned');
            expect(speech2.voice).toBe('bob_cloned');
        });

        test('should handle rapid speech requests', async () => {
            await mockTalkingAvatar.loadMesh('alice.glb');
            await mockTalkingAvatar._initializeTTSService();

            const speeches = ['First', 'Second', 'Third'];
            const promises = speeches.map(text => mockTalkingAvatar.speak(text));
            const results = await Promise.all(promises);

            results.forEach((result) => {
                expect(result.success).toBe(true);
                expect(result.voiceType).toBe('cloned');
                expect(result.voice).toBe('alice_cloned');
            });
        });
    });

    describe('Talking Animation Termination', () => {
        test('should stop talking animation when TTS audio ends', async () => {
            // Create a more realistic mock TalkingAvatar with animation support
            const mockAnimator = {
                stopAnimation: vi.fn(),
                mixer: {
                    stopAllAction: vi.fn()
                }
            };

            const talkingAvatar = {
                ...createMockTalkingAvatar(),
                animator: mockAnimator,
                currentTalkingAnimation: null,
                streamingAudioPlayer: {
                    playbackQueue: []
                },

                _triggerRandomTalkingAnimation: vi.fn(async function () {
                    this.currentTalkingAnimation = {
                        file: 'Talking.fbx',
                        category: 'communication'
                    };
                    console.log('[Test] Triggered talking animation:', this.currentTalkingAnimation.file);
                }),

                _stopTalkingAnimation: vi.fn(async function () {
                    if (this.currentTalkingAnimation) {
                        console.log(`[Test] Stopping talking animation: ${this.currentTalkingAnimation.file}`);

                        // Clear the reference first
                        this.currentTalkingAnimation = null;

                        // Stop the FBX animation directly via the animator if available
                        if (this.animator && typeof this.animator.stopAnimation === 'function') {
                            console.log(`[Test] Calling animator.stopAnimation() to stop FBX animation`);
                            this.animator.stopAnimation();
                        } else if (this.animator && this.animator.mixer && typeof this.animator.mixer.stopAllAction === 'function') {
                            // Fallback: stop via mixer directly
                            console.log(`[Test] Calling mixer.stopAllAction() as fallback`);
                            this.animator.mixer.stopAllAction();
                        }
                    }
                }),

                _setSpeakingState: vi.fn(function (isSpeaking) {
                    this.isSpeaking = isSpeaking;
                    console.log(`[Test] Speaking state set to: ${isSpeaking}`);
                }),

                // Simulate the audio playback end callback
                simulateAudioPlaybackEnd: vi.fn(async function (metadata = {}) {
                    console.log('[Test] Audio playback ended:', metadata);

                    // Stop talking animation when audio chunk ends
                    if (this.currentTalkingAnimation) {
                        console.log(`[Test] Stopping talking animation after audio chunk ended`);
                        await this._stopTalkingAnimation();
                    }

                    // Only update UI if this was the last chunk in the queue
                    if (this.streamingAudioPlayer && this.streamingAudioPlayer.playbackQueue.length === 0) {
                        this._setSpeakingState(false);
                    }
                }),

                isSpeaking: false
            };

            // Test the animation start and stop flow

            // 1. Start talking animation when TTS begins
            await talkingAvatar._triggerRandomTalkingAnimation();
            expect(talkingAvatar.currentTalkingAnimation).not.toBeNull();
            expect(talkingAvatar.currentTalkingAnimation.file).toBe('Talking.fbx');
            expect(talkingAvatar._triggerRandomTalkingAnimation).toHaveBeenCalledOnce();

            // 2. Simulate TTS audio ending
            await talkingAvatar.simulateAudioPlaybackEnd({ text: 'Test speech completed' });

            // 3. Verify that talking animation was stopped
            expect(talkingAvatar._stopTalkingAnimation).toHaveBeenCalledOnce();
            expect(talkingAvatar.currentTalkingAnimation).toBeNull();
            expect(mockAnimator.stopAnimation).toHaveBeenCalledOnce();
            expect(talkingAvatar._setSpeakingState).toHaveBeenCalledWith(false);
        });

        test('should handle multiple audio chunks correctly', async () => {
            const mockAnimator = {
                stopAnimation: vi.fn(),
                mixer: { stopAllAction: vi.fn() }
            };

            const talkingAvatar = {
                ...createMockTalkingAvatar(),
                animator: mockAnimator,
                currentTalkingAnimation: null,
                streamingAudioPlayer: {
                    playbackQueue: ['chunk2', 'chunk3'] // Simulate more chunks remaining
                },

                _triggerRandomTalkingAnimation: vi.fn(async function () {
                    this.currentTalkingAnimation = { file: 'Talking.fbx', category: 'communication' };
                }),

                _stopTalkingAnimation: vi.fn(async function () {
                    if (this.currentTalkingAnimation) {
                        this.currentTalkingAnimation = null;
                        if (this.animator && typeof this.animator.stopAnimation === 'function') {
                            this.animator.stopAnimation();
                        }
                    }
                }),

                _setSpeakingState: vi.fn(function (isSpeaking) {
                    this.isSpeaking = isSpeaking;
                }),

                simulateAudioPlaybackEnd: vi.fn(async function (metadata = {}) {
                    // Stop talking animation when audio chunk ends
                    if (this.currentTalkingAnimation) {
                        await this._stopTalkingAnimation();
                    }

                    // Only update speaking state if this was the last chunk
                    if (this.streamingAudioPlayer && this.streamingAudioPlayer.playbackQueue.length === 0) {
                        this._setSpeakingState(false);
                    }
                }),

                isSpeaking: true
            };

            // Start talking animation
            await talkingAvatar._triggerRandomTalkingAnimation();
            expect(talkingAvatar.currentTalkingAnimation).not.toBeNull();

            // Simulate first chunk ending (more chunks remaining)
            await talkingAvatar.simulateAudioPlaybackEnd({ text: 'First chunk' });

            // Animation should be stopped even if more chunks remain
            expect(talkingAvatar._stopTalkingAnimation).toHaveBeenCalledOnce();
            expect(talkingAvatar.currentTalkingAnimation).toBeNull();
            expect(mockAnimator.stopAnimation).toHaveBeenCalledOnce();

            // But speaking state should not change since more chunks remain
            expect(talkingAvatar._setSpeakingState).not.toHaveBeenCalled();

            // Clear the queue to simulate last chunk
            talkingAvatar.streamingAudioPlayer.playbackQueue = [];
            await talkingAvatar.simulateAudioPlaybackEnd({ text: 'Last chunk' });

            // Now speaking state should be updated
            expect(talkingAvatar._setSpeakingState).toHaveBeenCalledWith(false);
        });

        test('should handle missing animator gracefully', async () => {
            const talkingAvatar = {
                ...createMockTalkingAvatar(),
                animator: null, // No animator available
                currentTalkingAnimation: null,
                streamingAudioPlayer: { playbackQueue: [] },

                _triggerRandomTalkingAnimation: vi.fn(async function () {
                    this.currentTalkingAnimation = { file: 'Talking.fbx', category: 'communication' };
                }),

                _stopTalkingAnimation: vi.fn(async function () {
                    if (this.currentTalkingAnimation) {
                        console.log(`[Test] Stopping talking animation: ${this.currentTalkingAnimation.file}`);
                        this.currentTalkingAnimation = null;

                        // Should handle missing animator gracefully
                        if (this.animator && typeof this.animator.stopAnimation === 'function') {
                            this.animator.stopAnimation();
                        } else {
                            console.warn('[Test] No animator available to stop talking animation');
                        }
                    }
                }),

                _setSpeakingState: vi.fn(function (isSpeaking) {
                    this.isSpeaking = isSpeaking;
                }),

                simulateAudioPlaybackEnd: vi.fn(async function (metadata = {}) {
                    if (this.currentTalkingAnimation) {
                        await this._stopTalkingAnimation();
                    }
                    if (this.streamingAudioPlayer && this.streamingAudioPlayer.playbackQueue.length === 0) {
                        this._setSpeakingState(false);
                    }
                }),

                isSpeaking: false
            };

            // Start and stop animation without errors
            await talkingAvatar._triggerRandomTalkingAnimation();
            expect(talkingAvatar.currentTalkingAnimation).not.toBeNull();

            // Should not throw error when stopping without animator
            await expect(talkingAvatar.simulateAudioPlaybackEnd()).resolves.not.toThrow();

            expect(talkingAvatar._stopTalkingAnimation).toHaveBeenCalledOnce();
            expect(talkingAvatar.currentTalkingAnimation).toBeNull();
            expect(talkingAvatar._setSpeakingState).toHaveBeenCalledWith(false);
        });
    });
});
