/**
 * TalkingAvatar Voice Cloning Preservation Tests
 * Tests the enhanced voice cloning system integration with TalkingAvatar
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { TEST_CONFIG, TestUtils, TEST_FIXTURES } from '../../src/agent/setup/test-config.js';

// Mock TalkingAvatar class for testing
class MockTalkingAvatar {
    constructor(options = {}) {
        this.ttsService = 'mockTTS';
        this.ttsServiceInstance = null;
        this.isUsingClonedVoice = false;
        this.currentRole = null;
        this.currentVoiceParams = null;
        this.voiceConfig = {
            currentLanguage: 'english',
            currentGender: 'male'
        };
        this.avatar = { name: 'test_avatar' };
        this.logger = {
            debug: vi.fn(),
            info: vi.fn(),
            warn: vi.fn(),
            error: vi.fn()
        };
    }

    async initialize() {
        this.ttsServiceInstance = new MockTTSService();
        await this.ttsServiceInstance.initialize();
        return true;
    }

    setVoice(language, gender, service, roleName, override) {
        this.voiceConfig.currentLanguage = language;
        this.voiceConfig.currentGender = gender;

        if (roleName && this.ttsServiceInstance) {
            this.ttsServiceInstance.setVoice({ role: roleName, gender });
            this.currentRole = roleName;
        }

        return this.voiceConfig;
    }

    async setClonedVoice(metadata, isPermanent = false) {
        if (!this.ttsServiceInstance) return false;

        return await this.ttsServiceInstance.setClonedVoiceFromMetadata(metadata, isPermanent);
    }
}

// Mock SparkTTS service
class MockTTSService {
    constructor() {
        this.isUsingClonedVoice = false;
        this.currentRole = null;
        this.endpoint = 'http://localhost:8080';
        this.temperature = 0.7;
        this.top_p = 0.9;
        this.top_k = 50;
        this.window_size = 512;

        this.defaultRoles = {
            male: '周杰伦',
            female: '陈鲁豫'
        };
    }

    async initialize() {
        return true;
    }

    async getRoles() {
        return {
            roles: ['周杰伦', '陈鲁豫', '刘德华', 'test_voice', 'alice_voice']
        };
    }

    async checkSpeakerExists(roleName) {
        // Simulate that some voices have audio files
        const voicesWithAudio = ['test_voice', 'alice_voice'];
        return voicesWithAudio.includes(roleName);
    }

    setVoice(config) {
        this.currentRole = config.role;
        this.isUsingClonedVoice = false;
        console.log(`[MockTTSService] Set regular voice: ${config.role}`);
    }

    async setClonedVoiceFromMetadata(metadata, isPermanent = false) {
        const roleName = metadata.roleName;

        // Check if we have reference audio
        if (metadata.reference_audio_file_path && metadata.favorite) {
            // Check if the voice file actually exists
            const hasAudioFile = await this.checkSpeakerExists(roleName);

            if (hasAudioFile) {
                // Use cloned voice
                this.isUsingClonedVoice = true;
                this.currentRole = roleName;
                console.log(`[MockTTSService] Using cloned voice: ${roleName}`);
                return true;
            } else {
                // Fallback to regular voice if available
                const rolesResponse = await this.getRoles();
                const availableRoles = rolesResponse.roles || [];

                if (availableRoles.includes(roleName)) {
                    this.setVoice({ role: roleName, gender: metadata.gender || 'male' });
                    console.log(`[MockTTSService] Fallback to regular voice: ${roleName}`);
                    return true;
                } else {
                    console.error(`[MockTTSService] Voice ${roleName} not available`);
                    return false;
                }
            }
        } else if (metadata.roleName) {
            // No audio file, try regular voice
            const rolesResponse = await this.getRoles();
            const availableRoles = rolesResponse.roles || [];

            if (availableRoles.includes(roleName)) {
                this.setVoice({ role: roleName, gender: metadata.gender || 'male' });
                console.log(`[MockTTSService] Using regular voice: ${roleName}`);
                return true;
            }
        }

        return false;
    }

    async useRegularVoice(roleName) {
        this.setVoice({ role: roleName, gender: 'male' });
        this.isUsingClonedVoice = false;
        return true;
    }
}

describe('TalkingAvatar Voice Cloning Preservation', () => {
    let talkingAvatar;
    let consoleLogSpy;
    let consoleErrorSpy;

    beforeEach(async () => {
        talkingAvatar = new MockTalkingAvatar();
        await talkingAvatar.initialize();

        consoleLogSpy = vi.spyOn(console, 'log').mockImplementation(() => { });
        consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => { });
    });

    afterEach(() => {
        vi.clearAllMocks();
        consoleLogSpy.mockRestore();
        consoleErrorSpy.mockRestore();
    });

    describe('Voice Cloning with Audio Files', () => {
        it('should use cloned voice when reference audio exists', async () => {
            const metadata = {
                roleName: 'test_voice',
                reference_audio_file_path: '/path/to/test_voice.wav',
                favorite: true,
                gender: 'male'
            };

            const result = await talkingAvatar.setClonedVoice(metadata, true);

            expect(result).toBe(true);
            expect(talkingAvatar.ttsServiceInstance.isUsingClonedVoice).toBe(true);
            expect(talkingAvatar.ttsServiceInstance.currentRole).toBe('test_voice');
            expect(consoleLogSpy).toHaveBeenCalledWith('[MockTTSService] Using cloned voice: test_voice');
        });

        it('should preserve cloned voice state across operations', async () => {
            // Set up cloned voice
            const metadata = {
                roleName: 'alice_voice',
                reference_audio_file_path: '/path/to/alice_voice.wav',
                favorite: true,
                gender: 'female'
            };

            await talkingAvatar.setClonedVoice(metadata, true);
            expect(talkingAvatar.ttsServiceInstance.isUsingClonedVoice).toBe(true);

            // Try to set regular voice without override - should preserve cloned voice
            talkingAvatar.setVoice('english', 'female', 'sparkTTS');
            expect(talkingAvatar.ttsServiceInstance.isUsingClonedVoice).toBe(true);
            expect(talkingAvatar.ttsServiceInstance.currentRole).toBe('alice_voice');
        });
    });

    describe('Voice Cloning Fallback Logic', () => {
        it('should fall back to regular voice when audio file missing but role exists', async () => {
            const metadata = {
                roleName: '周杰伦', // Available as regular voice
                reference_audio_file_path: '/path/to/missing_file.wav',
                favorite: true,
                gender: 'male'
            };

            const result = await talkingAvatar.setClonedVoice(metadata, false);

            expect(result).toBe(true);
            expect(talkingAvatar.ttsServiceInstance.isUsingClonedVoice).toBe(false);
            expect(talkingAvatar.ttsServiceInstance.currentRole).toBe('周杰伦');
            expect(consoleLogSpy).toHaveBeenCalledWith('[MockTTSService] Fallback to regular voice: 周杰伦');
        });

        it('should handle missing voice gracefully', async () => {
            const metadata = {
                roleName: 'nonexistent_voice',
                reference_audio_file_path: '/path/to/missing_file.wav',
                favorite: true,
                gender: 'male'
            };

            const result = await talkingAvatar.setClonedVoice(metadata, false);

            expect(result).toBe(false);
            expect(consoleErrorSpy).toHaveBeenCalledWith('[MockTTSService] Voice nonexistent_voice not available');
        });

        it('should use regular voice when no audio file specified', async () => {
            const metadata = {
                roleName: '陈鲁豫',
                gender: 'female'
            };

            const result = await talkingAvatar.setClonedVoice(metadata, false);

            expect(result).toBe(true);
            expect(talkingAvatar.ttsServiceInstance.isUsingClonedVoice).toBe(false);
            expect(talkingAvatar.ttsServiceInstance.currentRole).toBe('陈鲁豫');
            expect(consoleLogSpy).toHaveBeenCalledWith('[MockTTSService] Using regular voice: 陈鲁豫');
        });
    });

    describe('Voice Configuration Integration', () => {
        it('should update voice configuration correctly', async () => {
            // Test language and gender switching
            talkingAvatar.setVoice('chinese', 'female', 'sparkTTS', '陈鲁豫');

            expect(talkingAvatar.voiceConfig.currentLanguage).toBe('chinese');
            expect(talkingAvatar.voiceConfig.currentGender).toBe('female');
            expect(talkingAvatar.currentRole).toBe('陈鲁豫');
        });

        it('should handle voice override correctly', async () => {
            // Set up cloned voice first
            const metadata = {
                roleName: 'test_voice',
                reference_audio_file_path: '/path/to/test_voice.wav',
                favorite: true,
                gender: 'male'
            };

            await talkingAvatar.setClonedVoice(metadata, true);
            expect(talkingAvatar.ttsServiceInstance.isUsingClonedVoice).toBe(true);

            // Override with regular voice
            talkingAvatar.setVoice('english', 'male', 'sparkTTS', '周杰伦', true);

            // Should override the cloned voice
            expect(talkingAvatar.currentRole).toBe('周杰伦');
        });
    });

    describe('Service Integration', () => {
        it('should handle TTS service initialization', async () => {
            const newAvatar = new MockTalkingAvatar();
            expect(newAvatar.ttsServiceInstance).toBeNull();

            await newAvatar.initialize();
            expect(newAvatar.ttsServiceInstance).toBeDefined();
            expect(newAvatar.ttsServiceInstance.constructor.name).toBe('MockTTSService');
        });

        it('should handle service errors gracefully', async () => {
            // Mock service error
            talkingAvatar.ttsServiceInstance.setClonedVoiceFromMetadata = vi.fn().mockRejectedValue(new Error('Service error'));

            const metadata = {
                roleName: 'test_voice',
                favorite: true
            };

            const result = await talkingAvatar.setClonedVoice(metadata, false);
            expect(result).toBe(false);
        });
    });

    describe('Real API Integration', () => {
        it('should work with real API when available', async () => {
            const apiStatus = await TestUtils.checkAPIAvailability();

            if (!apiStatus.available) {
                console.warn('Skipping real API test - API not available');
                return;
            }

            // This would be a real integration test with actual SparkTTS service
            // For now, we'll just verify the test infrastructure works
            expect(apiStatus.available).toBe(true);
            expect(TEST_CONFIG.useRealAPI).toBeDefined();
        });
    });

    describe('Performance and Reliability', () => {
        it('should handle rapid voice switching', async () => {
            const voices = [
                { roleName: 'test_voice', gender: 'male' },
                { roleName: 'alice_voice', gender: 'female' },
                { roleName: '周杰伦', gender: 'male' },
                { roleName: '陈鲁豫', gender: 'female' }
            ];

            for (const voice of voices) {
                const result = await talkingAvatar.setClonedVoice(voice, false);
                expect(result).toBe(true);
                expect(talkingAvatar.ttsServiceInstance.currentRole).toBe(voice.roleName);
            }
        });

        it('should maintain state consistency', async () => {
            const metadata = {
                roleName: 'test_voice',
                reference_audio_file_path: '/path/to/test_voice.wav',
                favorite: true,
                gender: 'male'
            };

            await talkingAvatar.setClonedVoice(metadata, true);

            // Verify all state is consistent
            expect(talkingAvatar.ttsServiceInstance.isUsingClonedVoice).toBe(true);
            expect(talkingAvatar.ttsServiceInstance.currentRole).toBe('test_voice');
            expect(talkingAvatar.currentRole).toBe('test_voice');
        });
    });
});
