/**
 * TTS Animation Integration Tests
 * Tests the integration between TTS services and animation systems in TalkingAvatar
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { TEST_CONFIG, TestUtils } from '../../src/agent/setup/test-config.js';

// Mock animation system
class MockAnimator {
    constructor() {
        this.isRunning = false;
        this.currentState = 'idle';
        this.currentAnimation = null;
        this.mixer = {
            _actions: [],
            stopAllAction: vi.fn()
        };
    }

    initialize() {
        this.isRunning = true;
        return true;
    }

    setState(state) {
        this.currentState = state;
        console.log(`[MockAnimator] State changed to: ${state}`);
    }

    async playAnimation(category, file, duration = 0) {
        this.currentAnimation = { category, file, duration };
        console.log(`[MockAnimator] Playing animation: ${category}/${file} (duration: ${duration}ms)`);
        return true;
    }

    stopAnimation() {
        if (this.currentAnimation) {
            console.log(`[MockAnimator] Stopping animation: ${this.currentAnimation.file}`);
            this.currentAnimation = null;
        }
    }

    startAnimationLoop() {
        this.isRunning = true;
    }

    stopAnimationLoop() {
        this.isRunning = false;
    }

    ensureRunning() {
        if (!this.isRunning) {
            this.startAnimationLoop();
        }
    }

    dispose() {
        this.stopAnimationLoop();
        this.currentAnimation = null;
    }
}

// Mock StreamingAudioPlayer
class MockStreamingAudioPlayer {
    constructor() {
        this.isPlaying = false;
        this.playbackQueue = [];
        this.volume = 1.0;
        this.callbacks = {};
    }

    async initialize() {
        return true;
    }

    async queueAudio(audioData, metadata = {}) {
        this.playbackQueue.push({ audioData, metadata });

        // Simulate audio playback
        setTimeout(() => {
            this.isPlaying = true;
            if (this.callbacks.onPlaybackStart) {
                this.callbacks.onPlaybackStart(metadata);
            }

            // Simulate playback duration
            setTimeout(() => {
                this.isPlaying = false;
                this.playbackQueue.shift();

                if (this.callbacks.onPlaybackEnd) {
                    this.callbacks.onPlaybackEnd(metadata);
                }
            }, metadata.duration || 1000);
        }, 100);
    }

    stopAll() {
        this.isPlaying = false;
        this.playbackQueue = [];
        console.log('[MockStreamingAudioPlayer] Stopped all audio playback');
    }

    destroy() {
        this.stopAll();
    }

    setCallbacks(callbacks) {
        this.callbacks = callbacks;
    }
}

// Mock TTS Service
class MockTTSService {
    constructor() {
        this.isInitialized = false;
        this.currentRole = 'default_voice';
        this.isUsingClonedVoice = false;
        this.animator = null;
        this.streamingEnabled = true;
    }

    async initialize() {
        this.isInitialized = true;
        return true;
    }

    async speak(text, options = {}) {
        if (!this.isInitialized) {
            throw new Error('TTS service not initialized');
        }

        console.log(`[MockTTSService] Speaking: "${text.substring(0, 50)}..."`);

        // Simulate streaming TTS
        if (this.streamingEnabled && options.audioPlayer) {
            const chunks = this._chunkText(text);

            for (let i = 0; i < chunks.length; i++) {
                const chunk = chunks[i];
                const audioData = this._generateMockAudio(chunk);

                await options.audioPlayer.queueAudio(audioData, {
                    text: chunk,
                    chunkIndex: i,
                    totalChunks: chunks.length,
                    duration: chunk.length * 50 // Simulate duration based on text length
                });
            }
        }

        return { success: true, audioLength: text.length * 50 };
    }

    stop() {
        console.log('[MockTTSService] TTS stopped');
    }

    _chunkText(text) {
        // Simple chunking by sentences
        const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0);
        return sentences.length > 0 ? sentences : [text];
    }

    _generateMockAudio(text) {
        // Generate mock audio data
        const length = Math.max(1024, text.length * 10);
        return new Float32Array(length).fill(0).map(() => Math.random() * 0.1);
    }
}

// Mock TalkingAvatar for testing
class MockTalkingAvatar {
    constructor() {
        this.animator = new MockAnimator();
        this.streamingAudioPlayer = new MockStreamingAudioPlayer();
        this.ttsServiceInstance = new MockTTSService();
        this.isSpeaking = false;
        this.currentTalkingAnimation = null;
        this.isInitialized = false;

        // Bind methods
        this._setSpeakingState = this._setSpeakingState.bind(this);
        this._triggerRandomTalkingAnimation = this._triggerRandomTalkingAnimation.bind(this);
        this._stopTalkingAnimation = this._stopTalkingAnimation.bind(this);
    }

    async initialize() {
        await this.animator.initialize();
        await this.streamingAudioPlayer.initialize();
        await this.ttsServiceInstance.initialize();

        // Set up audio player callbacks
        this.streamingAudioPlayer.setCallbacks({
            onPlaybackStart: async (metadata) => {
                console.log('[MockTalkingAvatar] Audio playback started:', metadata);
                this._setSpeakingState(true);
                await this._triggerRandomTalkingAnimation();
            },
            onPlaybackEnd: async (metadata) => {
                console.log('[MockTalkingAvatar] Audio playback ended:', metadata);

                if (this.streamingAudioPlayer.playbackQueue.length === 0) {
                    this._setSpeakingState(false);
                    await this._stopTalkingAnimation();
                }
            }
        });

        this.isInitialized = true;
        return true;
    }

    async speak(text) {
        if (!this.isInitialized) {
            throw new Error('TalkingAvatar not initialized');
        }

        return await this.ttsServiceInstance.speak(text, {
            audioPlayer: this.streamingAudioPlayer
        });
    }

    async _setSpeakingState(isSpeaking) {
        if (this.isSpeaking === isSpeaking) return;

        this.isSpeaking = isSpeaking;

        if (this.animator) {
            if (isSpeaking) {
                this.animator.setState('speaking');
            } else {
                this.animator.setState('idle');
            }
        }
    }

    async _triggerRandomTalkingAnimation() {
        if (this.currentTalkingAnimation) {
            return; // Already have a talking animation
        }

        // Simulate random talking animation selection
        const talkingAnimations = [
            'speaking_casual',
            'speaking_enthusiastic',
            'speaking_explanation',
            'speaking_storytelling'
        ];

        const randomAnimation = talkingAnimations[Math.floor(Math.random() * talkingAnimations.length)];

        this.currentTalkingAnimation = {
            category: 'communication',
            file: randomAnimation,
            context: 'Auto-triggered talking animation during TTS playback'
        };

        await this.animator.playAnimation('communication', randomAnimation, 0);
        console.log(`[MockTalkingAvatar] Triggered talking animation: ${randomAnimation}`);
    }

    async _stopTalkingAnimation() {
        if (this.currentTalkingAnimation) {
            console.log(`[MockTalkingAvatar] Stopping talking animation: ${this.currentTalkingAnimation.file}`);
            this.animator.stopAnimation();
            this.currentTalkingAnimation = null;
        }
    }

    stopSpeaking() {
        this.ttsServiceInstance.stop();
        this.streamingAudioPlayer.stopAll();
        this._setSpeakingState(false);
        this._stopTalkingAnimation();
    }

    dispose() {
        this.stopSpeaking();
        this.animator.dispose();
        this.streamingAudioPlayer.destroy();
    }
}

describe('TTS Animation Integration Tests', () => {
    let talkingAvatar;
    let consoleLogSpy;

    beforeEach(async () => {
        talkingAvatar = new MockTalkingAvatar();
        await talkingAvatar.initialize();

        consoleLogSpy = vi.spyOn(console, 'log').mockImplementation(() => { });
    });

    afterEach(() => {
        if (talkingAvatar) {
            talkingAvatar.dispose();
        }

        vi.clearAllMocks();
        consoleLogSpy.mockRestore();
    });

    describe('Basic TTS Animation Integration', () => {
        it('should initialize all components correctly', async () => {
            expect(talkingAvatar.isInitialized).toBe(true);
            expect(talkingAvatar.animator.isRunning).toBe(true);
            expect(talkingAvatar.ttsServiceInstance.isInitialized).toBe(true);
        });

        it('should trigger speaking state and animation when TTS starts', async () => {
            const text = "Hello, this is a test of the TTS animation integration.";

            // Start speaking
            const speakPromise = talkingAvatar.speak(text);

            // Wait a bit for audio playback to start
            await new Promise(resolve => setTimeout(resolve, 150));

            // Verify speaking state
            expect(talkingAvatar.isSpeaking).toBe(true);
            expect(talkingAvatar.animator.currentState).toBe('speaking');
            expect(talkingAvatar.currentTalkingAnimation).toBeDefined();

            // Verify animation was triggered
            expect(talkingAvatar.currentTalkingAnimation.category).toBe('communication');
            expect(talkingAvatar.currentTalkingAnimation.file).toMatch(/speaking_/);

            await speakPromise;
        });

        it('should stop speaking state and animation when TTS ends', async () => {
            const text = "Short text.";

            // Start speaking
            const speakPromise = talkingAvatar.speak(text);

            // Wait for audio to start
            await new Promise(resolve => setTimeout(resolve, 150));
            expect(talkingAvatar.isSpeaking).toBe(true);

            // Wait for audio to complete
            await new Promise(resolve => setTimeout(resolve, 1200));

            // Verify speaking state stopped
            expect(talkingAvatar.isSpeaking).toBe(false);
            expect(talkingAvatar.animator.currentState).toBe('idle');
            expect(talkingAvatar.currentTalkingAnimation).toBeNull();

            await speakPromise;
        });
    });

    describe('Animation Lifecycle Management', () => {
        it('should not start new talking animation if one is already active', async () => {
            // Set up existing animation
            talkingAvatar.currentTalkingAnimation = {
                category: 'communication',
                file: 'existing_animation',
                context: 'Test animation'
            };

            // Try to trigger new animation
            await talkingAvatar._triggerRandomTalkingAnimation();

            // Should keep the existing animation
            expect(talkingAvatar.currentTalkingAnimation.file).toBe('existing_animation');
        });

        it('should properly clean up animations on stop', async () => {
            const text = "Test animation cleanup.";

            // Start speaking
            const speakPromise = talkingAvatar.speak(text);
            await new Promise(resolve => setTimeout(resolve, 150));

            // Verify animation started
            expect(talkingAvatar.currentTalkingAnimation).toBeDefined();

            // Stop speaking manually
            talkingAvatar.stopSpeaking();

            // Verify cleanup
            expect(talkingAvatar.isSpeaking).toBe(false);
            expect(talkingAvatar.currentTalkingAnimation).toBeNull();
            expect(talkingAvatar.animator.currentState).toBe('idle');

            await speakPromise;
        });
    });

    describe('Streaming TTS Integration', () => {
        it('should handle multiple audio chunks correctly', async () => {
            const text = "This is a longer text that will be split into multiple chunks. Each chunk should trigger appropriate animation states.";

            // Start speaking
            const speakPromise = talkingAvatar.speak(text);

            // Wait for first chunk to start
            await new Promise(resolve => setTimeout(resolve, 150));

            // Should have triggered speaking state
            expect(talkingAvatar.isSpeaking).toBe(true);
            expect(talkingAvatar.streamingAudioPlayer.playbackQueue.length).toBeGreaterThan(0);

            // Wait for all chunks to process
            await new Promise(resolve => setTimeout(resolve, 2000));

            // Should eventually return to idle
            expect(talkingAvatar.isSpeaking).toBe(false);
            expect(talkingAvatar.streamingAudioPlayer.playbackQueue.length).toBe(0);

            await speakPromise;
        });

        it('should maintain animation throughout streaming playback', async () => {
            const text = "Streaming test with animation persistence. The animation should continue throughout the entire playback.";

            let animationStates = [];
            const originalSetState = talkingAvatar.animator.setState;
            talkingAvatar.animator.setState = (state) => {
                animationStates.push(state);
                originalSetState.call(talkingAvatar.animator, state);
            };

            // Start speaking
            const speakPromise = talkingAvatar.speak(text);

            // Monitor animation states during playback
            await new Promise(resolve => setTimeout(resolve, 150));
            expect(animationStates).toContain('speaking');

            // Wait for completion
            await new Promise(resolve => setTimeout(resolve, 2000));
            expect(animationStates).toContain('idle');

            await speakPromise;
        });
    });

    describe('Error Handling', () => {
        it('should handle TTS initialization errors gracefully', async () => {
            const brokenAvatar = new MockTalkingAvatar();
            brokenAvatar.ttsServiceInstance.initialize = vi.fn().mockRejectedValue(new Error('TTS init failed'));

            const initResult = await brokenAvatar.initialize().catch(() => false);
            expect(initResult).toBe(false);
        });

        it('should handle animation errors gracefully', async () => {
            // Mock animation error
            talkingAvatar.animator.playAnimation = vi.fn().mockRejectedValue(new Error('Animation failed'));

            // Should not crash when animation fails
            await expect(talkingAvatar._triggerRandomTalkingAnimation()).resolves.not.toThrow();
        });

        it('should handle audio playback errors gracefully', async () => {
            // Mock audio error
            talkingAvatar.streamingAudioPlayer.queueAudio = vi.fn().mockRejectedValue(new Error('Audio failed'));

            // Should handle TTS errors gracefully
            await expect(talkingAvatar.speak("Test text")).rejects.toThrow();

            // State should remain consistent
            expect(talkingAvatar.isSpeaking).toBe(false);
        });
    });

    describe('State Synchronization', () => {
        it('should keep animation and speaking states synchronized', async () => {
            const text = "State synchronization test.";

            // Track state changes
            let speakingStates = [];
            let animationStates = [];

            const originalSetSpeakingState = talkingAvatar._setSpeakingState;
            talkingAvatar._setSpeakingState = (state) => {
                speakingStates.push(state);
                return originalSetSpeakingState.call(talkingAvatar, state);
            };

            const originalSetState = talkingAvatar.animator.setState;
            talkingAvatar.animator.setState = (state) => {
                animationStates.push(state);
                return originalSetState.call(talkingAvatar.animator, state);
            };

            // Execute speak cycle
            await talkingAvatar.speak(text);
            await new Promise(resolve => setTimeout(resolve, 1500));

            // Verify state synchronization
            expect(speakingStates).toContain(true);
            expect(speakingStates).toContain(false);
            expect(animationStates).toContain('speaking');
            expect(animationStates).toContain('idle');

            // Final states should be synchronized
            expect(talkingAvatar.isSpeaking).toBe(false);
            expect(talkingAvatar.animator.currentState).toBe('idle');
        });

        it('should handle rapid start/stop cycles correctly', async () => {
            // Start speaking
            const speakPromise1 = talkingAvatar.speak("First text");
            await new Promise(resolve => setTimeout(resolve, 100));

            // Stop and start again quickly
            talkingAvatar.stopSpeaking();
            const speakPromise2 = talkingAvatar.speak("Second text");

            // Should handle this gracefully
            expect(talkingAvatar.isSpeaking).toBe(false); // Stopped

            await new Promise(resolve => setTimeout(resolve, 200));
            expect(talkingAvatar.isSpeaking).toBe(true); // Restarted

            await Promise.allSettled([speakPromise1, speakPromise2]);
        });
    });

    describe('Performance Tests', () => {
        it('should handle rapid consecutive speak requests', async () => {
            const texts = [
                "First message",
                "Second message",
                "Third message",
                "Fourth message"
            ];

            const startTime = Date.now();

            // Queue multiple speak requests
            const promises = texts.map(text => talkingAvatar.speak(text));

            // Wait for all to complete
            await Promise.allSettled(promises);

            const endTime = Date.now();
            const duration = endTime - startTime;

            // Should complete within reasonable time
            expect(duration).toBeLessThan(10000); // 10 seconds max

            // Should end in idle state
            expect(talkingAvatar.isSpeaking).toBe(false);
            expect(talkingAvatar.animator.currentState).toBe('idle');
        });
    });

    describe('Real API Integration', () => {
        it('should work with real TTS services when available', async () => {
            const apiStatus = await TestUtils.checkAPIAvailability();

            if (!apiStatus.available) {
                console.warn('Skipping real API test - API not available');
                return;
            }

            // This would test with real TTS services
            // For now, just verify test infrastructure
            expect(TEST_CONFIG.useRealAPI).toBeDefined();
            expect(apiStatus.endpoint).toBeDefined();
        });
    });
}); 