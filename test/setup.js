/**
 * Test Setup Configuration for LangGraph Agent Tests
 * Provides global mocks, utilities, and test environment configuration
 */

import { vi } from 'vitest';

// Global test configuration
global.__TEST__ = true;
global.__VERSION__ = '1.0.0-test';

// Global fetch mock for Node.js environment
global.fetch = vi.fn();

// AbortSignal mock for timeout testing
global.AbortSignal = {
    timeout: vi.fn((ms) => ({
        aborted: false,
        addEventListener: vi.fn(),
        removeEventListener: vi.fn(),
        dispatchEvent: vi.fn()
    }))
};

// TextEncoder/TextDecoder for streaming tests
global.TextEncoder = TextEncoder;
global.TextDecoder = TextDecoder;

// Console mock for cleaner test output
const originalConsole = global.console;
global.console = {
    ...originalConsole,
    log: vi.fn(),
    debug: vi.fn(),
    info: vi.fn(),
    warn: vi.fn(),
    error: vi.fn()
};

// Global test utilities
global.testUtils = {
    /**
     * Create a mock LLM response
     * @param {string} content - Response content
     * @param {Object} options - Additional options
     * @returns {Object} Mock LLM response
     */
    createMockLLMResponse: (content, options = {}) => ({
        ok: true,
        status: 200,
        json: () => Promise.resolve({
            choices: [{
                message: {
                    content,
                    role: 'assistant'
                },
                index: 0,
                finish_reason: 'stop'
            }],
            usage: {
                prompt_tokens: options.promptTokens || 10,
                completion_tokens: options.completionTokens || content.length,
                total_tokens: (options.promptTokens || 10) + (options.completionTokens || content.length)
            },
            model: options.model || 'test-model',
            id: options.id || `test-${Date.now()}`,
            created: options.created || Math.floor(Date.now() / 1000),
            object: 'chat.completion'
        })
    }),

    /**
     * Create a mock streaming response
     * @param {Array<string>} chunks - Content chunks
     * @returns {Object} Mock streaming response
     */
    createMockStreamingResponse: (chunks) => {
        let chunkIndex = 0;

        const mockReader = {
            read: vi.fn().mockImplementation(() => {
                if (chunkIndex >= chunks.length) {
                    return Promise.resolve({ done: true, value: null });
                }

                const chunk = chunks[chunkIndex++];
                const sseData = `data: ${JSON.stringify({
                    choices: [{
                        delta: {
                            content: chunk
                        }
                    }]
                })}\n\n`;

                return Promise.resolve({
                    done: false,
                    value: new TextEncoder().encode(sseData)
                });
            }),
            releaseLock: vi.fn()
        };

        return {
            ok: true,
            status: 200,
            body: {
                getReader: () => mockReader
            }
        };
    },

    /**
     * Create a mock multimodal input
     * @param {Object} options - Input options
     * @returns {Object} Mock multimodal input
     */
    createMockMultimodalInput: (options = {}) => ({
        text: options.text || 'Test multimodal input',
        audio: options.audio || new ArrayBuffer(1024),
        mediaType: options.mediaType || 'audio/wav',
        contextualInformation: {
            currentEmotion: options.emotion || 'neutral',
            avatarState: {
                isAnimating: options.isAnimating || false,
                isSpeaking: options.isSpeaking || false
            },
            ...options.contextualInformation
        },
        asrMetadata: {
            detectedLanguage: options.language || 'english',
            detectedEmotion: options.detectedEmotion || 'neutral',
            confidence: options.confidence || 0.95,
            ...options.asrMetadata
        }
    }),

    /**
     * Create a mock agent configuration
     * @param {Object} overrides - Configuration overrides
     * @returns {Object} Mock agent configuration
     */
    createMockAgentConfig: (overrides = {}) => ({
        vllmEndpoint: 'http://test-endpoint:8000',
        temperature: 0.7,
        maxTokens: 2048,
        maxIterations: 10,
        enableMemory: true,
        maxRetries: 3,
        enableStructuredOutput: true,
        enableAdvancedStreaming: true,
        services: {
            ttsService: { speak: vi.fn() },
            audioPlayer: { play: vi.fn() },
            animationController: { triggerAnimation: vi.fn() }
        },
        ...overrides
    }),

    /**
     * Wait for async operations to complete
     * @param {number} ms - Milliseconds to wait
     * @returns {Promise} Promise that resolves after specified time
     */
    wait: (ms = 0) => new Promise(resolve => setTimeout(resolve, ms)),

    /**
     * Create a mock workflow state
     * @param {Object} options - State options
     * @returns {Object} Mock workflow state
     */
    createMockWorkflowState: (options = {}) => ({
        messages: options.messages || [],
        context: {
            totalToolCalls: options.totalToolCalls || 0,
            processingComplete: options.processingComplete || false,
            hadErrors: options.hadErrors || false,
            isMultimodal: options.isMultimodal || false,
            finalResponse: options.finalResponse || null,
            originalError: options.originalError || null,
            ...options.context
        },
        toolResults: options.toolResults || [],
        structuredOutput: options.structuredOutput || null,
        currentAnimation: options.currentAnimation || null,
        ttsQueue: options.ttsQueue || [],
        errorState: {
            fallbackUsed: options.fallbackUsed || false,
            retryCount: options.retryCount || 0,
            ...options.errorState
        },
        metadata: {
            sessionId: options.sessionId || 'test-session',
            startTime: options.startTime || Date.now(),
            processingSteps: options.processingSteps || [],
            ...options.metadata
        }
    })
};

// Setup global error handlers for cleaner test output
process.on('unhandledRejection', (reason) => {
    if (!global.__TEST_SUPPRESS_UNHANDLED_REJECTION__) {
        console.error('Unhandled Promise Rejection:', reason);
    }
});

process.on('uncaughtException', (error) => {
    if (!global.__TEST_SUPPRESS_UNCAUGHT_EXCEPTION__) {
        console.error('Uncaught Exception:', error);
    }
});

// Global beforeEach and afterEach for consistent test isolation
beforeEach(() => {
    // Clear all timers
    vi.clearAllTimers();

    // Reset all mocks
    vi.clearAllMocks();

    // Reset global fetch
    global.fetch.mockClear();
});

afterEach(() => {
    // Clear any pending timers
    vi.clearAllTimers();

    // Restore original implementations
    vi.restoreAllMocks();
});

// Export test utilities for explicit imports
export { testUtils }; 