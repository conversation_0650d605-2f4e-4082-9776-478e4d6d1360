# TalkingHead Module Tests

This directory contains tests for the TalkingHead module's source components, mirroring the structure of `src/modules/talkinghead/src/`.

## Test Files

### sparktts-enhanced-fallback.test.js
Tests the enhanced SparkTTS service fallback logic that uses `getRoles()` when no audio blob is provided for voice cloning. This test validates the service-level improvements for handling seed files with missing reference audio.

### seed-file-fallback.test.js  
Tests the UI-level fallback logic in the TalkingHeadUI class that handles seed files with no recorded reference audio by using `getRoles()` for voice setup. This test validates the enhanced `loadSavedVoice()` method.

## Related Components

These tests validate functionality in:
- `src/modules/talkinghead/src/ttsServices.js` (SparkTTS service)
- `src/modules/talkinghead/src/ui.js` (TalkingHeadUI class)
- `src/media/voiceCloning.js` (Voice cloning utilities)

## Test Coverage

- ✅ SparkTTS service fallback logic
- ✅ UI seed file handling with missing audio
- ✅ Regular voice fallback when cloned voice unavailable
- ✅ Error handling for getRoles() failures
- ✅ Gender-based default voice selection
