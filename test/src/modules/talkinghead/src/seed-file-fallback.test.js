/**
 * Test suite for seed file handling with no reference audio fallback logic
 * Tests the enhanced logic that uses getRole for voice setup when seed files exist
 * but contain no recorded reference audio.
 */

import { describe, test, expect, vi, beforeEach, afterEach } from 'vitest';

// Mock TalkingHeadUI class for testing
class MockTalkingHeadUI {
    constructor() {
        this.voiceBuffer = [];
        this.avatar = {
            avatar: { name: 'test_avatar' },
            voiceConfig: { currentGender: 'male' },
            ttsServiceInstance: {
                defaultRoles: { male: '周杰伦', female: '陈鲁豫' },
                getRoles: vi.fn(),
                useRegularVoice: vi.fn(),
                setVoice: vi.fn(),
                isUsingClonedVoice: false,
                currentRole: null
            },
            setClonedVoice: vi.fn(),
            currentClonedVoice: null
        };
    }

    debugLog(message, data) {
        console.log(`[MockTalkingHeadUI] ${message}`, data || '');
    }

    showNotification(message, isError = false) {
        console.log(`[MockTalkingHeadUI] ${isError ? 'ERROR: ' : 'INFO: '}${message}`);
    }

    updateActiveVoiceUI(roleName) {
        console.log(`[MockTalkingHeadUI] Updated UI to show active voice: ${roleName}`);
    }

    addVoiceBufferItem(voiceData) {
        this.voiceBuffer.push(voiceData);
        console.log(`[MockTalkingHeadUI] Added voice to buffer:`, voiceData);
    }

    /**
     * Enhanced loadSavedVoice method that includes the new fallback logic
     */
    async loadSavedVoiceWithFallback(seedData) {
        try {
            let voiceConfig;
            if (typeof seedData === 'string') {
                voiceConfig = JSON.parse(seedData);
            } else {
                voiceConfig = seedData;
            }

            this.debugLog('Found saved voice configuration:', voiceConfig);

            if (voiceConfig) {
                const hasRealVoiceData = voiceConfig.reference_audio_file_path;
                const isFavorite = voiceConfig.favorite === true;

                this.debugLog(`Voice config has real data: ${hasRealVoiceData}, is favorite: ${isFavorite}`);

                if (hasRealVoiceData || isFavorite) {
                    const voiceData = {
                        reference_audio_file_path: voiceConfig.reference_audio_file_path,
                        roleName: voiceConfig.roleName,
                        timestamp: voiceConfig.timestamp || Date.now(),
                        text: voiceConfig.text || "Saved voice",
                        reference_text: voiceConfig.reference_text || voiceConfig.text || "Saved voice",
                        favorite: isFavorite
                    };

                    this.addVoiceBufferItem(voiceData);
                    this.debugLog('Added saved voice to buffer:', voiceData);

                    if (voiceConfig.roleName && this.avatar.setClonedVoice && isFavorite) {
                        try {
                            let audioFilePath = voiceConfig.reference_audio_file_path;
                            let audioFileExists = false;

                            // Simulate checking if audio file exists
                            if (audioFilePath) {
                                // For testing, we'll simulate different scenarios
                                audioFileExists = !audioFilePath.includes('missing');
                                this.debugLog(`Checked audio file existence: ${audioFilePath}, exists: ${audioFileExists}`);
                            }

                            // Enhanced fallback logic
                            if (!audioFileExists) {
                                this.debugLog(`No valid audio file found for favorite voice. Checking if role exists as regular voice: ${voiceConfig.roleName}`);

                                let regularVoiceAvailable = false;
                                try {
                                    if (this.avatar.ttsServiceInstance && typeof this.avatar.ttsServiceInstance.getRoles === 'function') {
                                        const rolesResponse = await this.avatar.ttsServiceInstance.getRoles();
                                        const availableRoles = rolesResponse?.roles || [];
                                        regularVoiceAvailable = availableRoles.includes(voiceConfig.roleName);
                                        
                                        this.debugLog(`Available roles from getRole: [${availableRoles.join(', ')}]`);
                                        this.debugLog(`Role '${voiceConfig.roleName}' exists as regular voice: ${regularVoiceAvailable}`);
                                    }
                                } catch (error) {
                                    this.debugLog(`Error checking available roles: ${error.message}`);
                                }

                                if (regularVoiceAvailable) {
                                    await this.avatar.setClonedVoice(voiceConfig, false);
                                    this.showNotification(`Favorite voice file not found. Using regular voice: ${voiceConfig.roleName}`);
                                    this.debugLog(`Successfully set regular voice for role: ${voiceConfig.roleName}`);
                                } else {
                                    this.debugLog(`Role '${voiceConfig.roleName}' not available as regular voice. Using default voice instead.`);
                                    
                                    const defaultRole = this.avatar.voiceConfig?.currentGender === 'female' ? 
                                        this.avatar.ttsServiceInstance?.defaultRoles?.female : 
                                        this.avatar.ttsServiceInstance?.defaultRoles?.male;
                                    
                                    if (defaultRole && this.avatar.ttsServiceInstance) {
                                        if (typeof this.avatar.ttsServiceInstance.useRegularVoice === 'function') {
                                            await this.avatar.ttsServiceInstance.useRegularVoice(defaultRole);
                                            this.debugLog(`Fell back to default voice: ${defaultRole}`);
                                        }
                                    }

                                    this.showNotification(`Voice '${voiceConfig.roleName}' not available. Using default voice instead.`, true);
                                }
                            } else {
                                await this.avatar.setClonedVoice(voiceConfig, true);
                            }

                            this.avatar.currentClonedVoice = voiceConfig.roleName;
                            this.updateActiveVoiceUI(voiceConfig.roleName);
                            this.showNotification('Loaded saved favorite voice configuration');
                            this.debugLog('Applied saved favorite voice configuration');
                        } catch (error) {
                            console.error('[MockTalkingHeadUI] Error applying cloned voice:', error);
                            this.debugLog('Falling back to default voice');
                        }
                    }
                }
            }
        } catch (error) {
            console.error('[MockTalkingHeadUI] Error loading saved voice:', error);
        }
    }
}

describe('Seed File Fallback Logic Tests', () => {
    let ui;
    let logSpy;

    beforeEach(() => {
        ui = new MockTalkingHeadUI();
        logSpy = vi.spyOn(console, 'log');
    });

    afterEach(() => {
        vi.restoreAllMocks();
    });

    test('should use regular voice when seed file exists but audio file is missing and role is available', async () => {
        // Setup: Mock getRoles to return available roles including the test role
        ui.avatar.ttsServiceInstance.getRoles.mockResolvedValue({
            success: true,
            roles: ['周杰伦', '陈鲁豫', 'test_role']
        });

        // Create seed data with missing audio file
        const seedData = {
            roleName: 'test_role',
            reference_audio_file_path: '/assets/audio/missing_file.mp3', // File doesn't exist
            favorite: true,
            timestamp: Date.now(),
            text: 'Test voice'
        };

        await ui.loadSavedVoiceWithFallback(seedData);

        // Verify getRoles was called to check available voices
        expect(ui.avatar.ttsServiceInstance.getRoles).toHaveBeenCalled();

        // Verify setClonedVoice was called with isPermanent=false (fallback mode)
        expect(ui.avatar.setClonedVoice).toHaveBeenCalledWith(seedData, false);

        // Check logging messages - use toHaveBeenCalledWith for individual messages
        expect(logSpy).toHaveBeenCalledWith('[MockTalkingHeadUI] Available roles from getRole: [周杰伦, 陈鲁豫, test_role]', '');
        expect(logSpy).toHaveBeenCalledWith('[MockTalkingHeadUI] Role \'test_role\' exists as regular voice: true', '');
        expect(logSpy).toHaveBeenCalledWith('[MockTalkingHeadUI] Successfully set regular voice for role: test_role', '');
    });

    test('should fall back to default voice when role is not available as regular voice', async () => {
        // Setup: Mock getRoles to return roles that don't include the test role
        ui.avatar.ttsServiceInstance.getRoles.mockResolvedValue({
            success: true,
            roles: ['周杰伦', '陈鲁豫'] // test_role is not available
        });

        const seedData = {
            roleName: 'unavailable_role',
            reference_audio_file_path: '/assets/audio/missing_file.mp3',
            favorite: true,
            timestamp: Date.now(),
            text: 'Test voice'
        };

        await ui.loadSavedVoiceWithFallback(seedData);

        // Verify getRoles was called
        expect(ui.avatar.ttsServiceInstance.getRoles).toHaveBeenCalled();

        // Verify useRegularVoice was called with default role
        expect(ui.avatar.ttsServiceInstance.useRegularVoice).toHaveBeenCalledWith('周杰伦');

        // Check logging messages - use toHaveBeenCalledWith for individual messages
        expect(logSpy).toHaveBeenCalledWith('[MockTalkingHeadUI] Role \'unavailable_role\' not available as regular voice. Using default voice instead.', '');
        expect(logSpy).toHaveBeenCalledWith('[MockTalkingHeadUI] Fell back to default voice: 周杰伦', '');
    });

    test('should use cloned voice when audio file exists', async () => {
        const seedData = {
            roleName: 'test_role',
            reference_audio_file_path: '/assets/audio/valid_file.mp3', // File exists
            favorite: true,
            timestamp: Date.now(),
            text: 'Test voice'
        };

        await ui.loadSavedVoiceWithFallback(seedData);

        // Verify getRoles was not called since audio file exists
        expect(ui.avatar.ttsServiceInstance.getRoles).not.toHaveBeenCalled();

        // Verify setClonedVoice was called with isPermanent=true (normal mode)
        expect(ui.avatar.setClonedVoice).toHaveBeenCalledWith(seedData, true);

        // Check that UI was updated
        expect(ui.avatar.currentClonedVoice).toBe('test_role');
    });

    test('should handle getRoles error gracefully', async () => {
        // Setup: Mock getRoles to throw an error
        ui.avatar.ttsServiceInstance.getRoles.mockRejectedValue(new Error('TTS service unavailable'));

        const seedData = {
            roleName: 'test_role',
            reference_audio_file_path: '/assets/audio/missing_file.mp3',
            favorite: true,
            timestamp: Date.now(),
            text: 'Test voice'
        };

        await ui.loadSavedVoiceWithFallback(seedData);

        // Verify error was logged
        expect(logSpy).toHaveBeenCalledWith('[MockTalkingHeadUI] Error checking available roles: TTS service unavailable', '');

        // Verify default voice fallback was used
        expect(ui.avatar.ttsServiceInstance.useRegularVoice).toHaveBeenCalledWith('周杰伦');
    });

    test('should use female default voice for female avatar', async () => {
        // Change avatar gender to female
        ui.avatar.voiceConfig.currentGender = 'female';

        ui.avatar.ttsServiceInstance.getRoles.mockResolvedValue({
            success: true,
            roles: ['周杰伦', '陈鲁豫'] // unavailable_role not in list
        });

        const seedData = {
            roleName: 'unavailable_role',
            reference_audio_file_path: '/assets/audio/missing_file.mp3',
            favorite: true,
            timestamp: Date.now(),
            text: 'Test voice'
        };

        await ui.loadSavedVoiceWithFallback(seedData);

        // Verify female default voice was used
        expect(ui.avatar.ttsServiceInstance.useRegularVoice).toHaveBeenCalledWith('陈鲁豫');
        expect(logSpy).toHaveBeenCalledWith('[MockTalkingHeadUI] Fell back to default voice: 陈鲁豫', '');
    });

    test('should handle non-favorite voices correctly', async () => {
        const seedData = {
            roleName: 'test_role',
            reference_audio_file_path: '/assets/audio/test_file.mp3',
            favorite: false, // Not a favorite voice
            timestamp: Date.now(),
            text: 'Test voice'
        };

        await ui.loadSavedVoiceWithFallback(seedData);

        // Verify setClonedVoice was not called for non-favorite voices
        expect(ui.avatar.setClonedVoice).not.toHaveBeenCalled();

        // Voice should still be added to buffer
        expect(ui.voiceBuffer).toHaveLength(1);
        expect(ui.voiceBuffer[0].roleName).toBe('test_role');
    });
});
