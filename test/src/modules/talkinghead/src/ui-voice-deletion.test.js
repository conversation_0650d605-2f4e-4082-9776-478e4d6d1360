/**
 * Test suite for TalkingHead UI Voice Deletion functionality
 * Tests the enhanced voice deletion including remote speaker deletion and seed file updates
 */

import { describe, test, expect, vi, beforeEach, afterEach } from 'vitest';

// Mock TalkingHead UI class
class MockTalkingHeadUI {
    constructor() {
        this.voiceBuffer = [];
        this.avatar = {
            avatar: { name: 'test-avatar' },
            ttsServiceInstance: {
                deleteSpeaker: vi.fn(),
                useRegularVoice: vi.fn()
            },
            currentClonedVoice: null,
            voiceConfig: {
                currentGender: 'male',
                currentLanguage: 'chinese'
            },
            setVoice: vi.fn()
        };
        this.debugLog = vi.fn();
        this.showNotification = vi.fn();
        this.updateActiveVoiceUI = vi.fn();
    }

    /**
     * Delete all audio files associated with a voice and remove speaker from remote TTS service
     * @param {Object} voiceData - The voice data containing file paths
     */
    async deleteVoiceFiles(voiceData) {
        const filesToDelete = new Set();

        // Collect all possible file paths
        if (voiceData.audioUrl) {
            filesToDelete.add(voiceData.audioUrl);
        }

        if (voiceData.reference_audio_file_path) {
            filesToDelete.add(voiceData.reference_audio_file_path);
        }

        if (voiceData.reference_audio_file && typeof voiceData.reference_audio_file === 'string') {
            filesToDelete.add(voiceData.reference_audio_file);
        }

        // First, delete the speaker from the remote TTS service if it exists
        if (voiceData.roleName && this.avatar?.ttsServiceInstance?.deleteSpeaker) {
            try {
                console.log(`[TalkingHeadUI] Deleting speaker from TTS service: ${voiceData.roleName}`);
                const deleteResult = await this.avatar.ttsServiceInstance.deleteSpeaker(voiceData.roleName);

                if (deleteResult.success) {
                    console.log(`[TalkingHeadUI] Successfully deleted speaker from TTS service: ${voiceData.roleName}`);
                } else {
                    console.warn(`[TalkingHeadUI] Failed to delete speaker from TTS service: ${deleteResult.error}`);
                }
            } catch (error) {
                console.error(`[TalkingHeadUI] Error deleting speaker from TTS service:`, error);
                // Continue with file deletion even if remote deletion fails
            }
        }

        // Get the download server URL
        const port = global.window?.downloadServerPort || 2994;
        const host = global.window?.config?.host || 'localhost';
        const downloadServerUrl = `http://${host}:${port}`;

        // Delete each file
        for (const filePath of filesToDelete) {
            if (!filePath) continue;

            try {
                console.log(`[TalkingHeadUI] Deleting voice file: ${filePath}`);

                // Use the delete-file endpoint
                const deleteResponse = await fetch(`${downloadServerUrl}/delete-file?path=${encodeURIComponent(filePath)}`, {
                    method: 'DELETE'
                });

                if (deleteResponse.ok) {
                    console.log(`[TalkingHeadUI] Successfully deleted voice file: ${filePath}`);
                } else {
                    console.warn(`[TalkingHeadUI] Failed to delete voice file ${filePath}: ${deleteResponse.statusText}`);
                }
            } catch (error) {
                console.warn(`[TalkingHeadUI] Error deleting voice file ${filePath}:`, error);
            }
        }
    }

    async removeVoiceBufferItem(voiceData) {
        try {
            this.debugLog('Removing voice buffer item', voiceData);

            // Remove from DOM (mocked)
            const item = { remove: vi.fn() };

            // Remove from array
            this.voiceBuffer = this.voiceBuffer.filter(v => v.timestamp !== voiceData.timestamp);

            // Enhanced file deletion - delete all associated audio files
            await this.deleteVoiceFiles(voiceData);

            // If this was a favorited voice, also remove it from the seed file
            if (voiceData.favorite) {
                await this.removeFavoriteVoiceFromSeed(voiceData);
            }

            this.debugLog('Voice buffer item removed successfully');
            this.showNotification(`Voice "${voiceData.roleName}" deleted successfully`);
        } catch (error) {
            console.error('[TalkingHeadUI] Error removing voice buffer item:', error);
            this.showNotification('Failed to remove voice from buffer', true);
        }
    }

    async removeFavoriteVoiceFromSeed(voiceData) {
        try {
            // Mock cache utilities
            const mockCacheUtils = {
                storeSeed: vi.fn().mockResolvedValue({ success: true }),
                getStoredSeed: vi.fn().mockResolvedValue(JSON.stringify({
                    roleName: voiceData.roleName,
                    favorite: true
                })),
                generateCacheKey: vi.fn().mockResolvedValue('test-cache-key')
            };

            // Simulate clearing seed data
            const seedData = {
                roleName: null,
                favorite: false,
                reference_audio_file_path: null,
                timestamp: Date.now(),
                meshName: this.avatar.avatar.name
            };

            await mockCacheUtils.storeSeed('test-cache-key', JSON.stringify(seedData));

            // Reset to default voice if this was the active voice
            if (this.avatar.currentClonedVoice === voiceData.roleName) {
                if (this.avatar.ttsServiceInstance && typeof this.avatar.ttsServiceInstance.useRegularVoice === 'function') {
                    await this.avatar.ttsServiceInstance.useRegularVoice();
                } else {
                    const gender = this.avatar.voiceConfig?.currentGender || 'male';
                    const language = this.avatar.voiceConfig?.currentLanguage || 'chinese';
                    this.avatar.setVoice(language, gender);
                }

                this.avatar.currentClonedVoice = null;
                this.updateActiveVoiceUI(null);
            }
        } catch (error) {
            console.error('[TalkingHeadUI] Error removing favorite voice from seed:', error);
        }
    }
}

describe('TalkingHead UI Voice Deletion', () => {
    let talkingHeadUI;
    let mockFetch;

    beforeEach(() => {
        // Mock fetch globally
        mockFetch = vi.fn();
        global.fetch = mockFetch;

        // Mock window object
        global.window = {
            downloadServerPort: 2994,
            config: {
                host: 'localhost'
            }
        };

        // Create TalkingHeadUI instance
        talkingHeadUI = new MockTalkingHeadUI();
    });

    afterEach(() => {
        vi.clearAllMocks();
    });

    describe('deleteVoiceFiles', () => {
        test('should delete speaker from TTS service when roleName exists', async () => {
            const voiceData = {
                roleName: 'test-speaker',
                audioUrl: 'http://localhost:2994/audio/test.wav',
                reference_audio_file_path: '/path/to/audio.wav'
            };

            // Mock successful TTS service deletion
            mockTtsService.deleteSpeaker.mockResolvedValue({
                success: true,
                result: { message: 'Speaker deleted successfully' }
            });

            // Mock successful file deletion
            mockFetch.mockResolvedValue({
                ok: true
            });

            await talkingHeadUI.deleteVoiceFiles(voiceData);

            // Verify TTS service deletion was called
            expect(mockTtsService.deleteSpeaker).toHaveBeenCalledWith('test-speaker');

            // Verify file deletion calls
            expect(mockFetch).toHaveBeenCalledWith(
                'http://localhost:2994/delete-file?path=http%3A%2F%2Flocalhost%3A2994%2Faudio%2Ftest.wav',
                { method: 'DELETE' }
            );
            expect(mockFetch).toHaveBeenCalledWith(
                'http://localhost:2994/delete-file?path=%2Fpath%2Fto%2Faudio.wav',
                { method: 'DELETE' }
            );
        });

        test('should continue with file deletion even if TTS service deletion fails', async () => {
            const voiceData = {
                roleName: 'test-speaker',
                audioUrl: 'http://localhost:2994/audio/test.wav'
            };

            // Mock TTS service deletion failure
            mockTtsService.deleteSpeaker.mockRejectedValue(new Error('Network error'));

            // Mock successful file deletion
            mockFetch.mockResolvedValue({ ok: true });

            await talkingHeadUI.deleteVoiceFiles(voiceData);

            // Verify TTS service deletion was attempted
            expect(mockTtsService.deleteSpeaker).toHaveBeenCalledWith('test-speaker');

            // Verify file deletion still proceeded
            expect(mockFetch).toHaveBeenCalledWith(
                'http://localhost:2994/delete-file?path=http%3A%2F%2Flocalhost%3A2994%2Faudio%2Ftest.wav',
                { method: 'DELETE' }
            );
        });

        test('should handle missing TTS service gracefully', async () => {
            const voiceData = {
                roleName: 'test-speaker',
                audioUrl: 'http://localhost:2994/audio/test.wav'
            };

            // Remove TTS service
            talkingHeadUI.avatar.ttsServiceInstance = null;

            // Mock successful file deletion
            mockFetch.mockResolvedValue({ ok: true });

            await talkingHeadUI.deleteVoiceFiles(voiceData);

            // Verify file deletion still proceeded
            expect(mockFetch).toHaveBeenCalledWith(
                'http://localhost:2994/delete-file?path=http%3A%2F%2Flocalhost%3A2994%2Faudio%2Ftest.wav',
                { method: 'DELETE' }
            );
        });

        test('should handle missing deleteSpeaker method gracefully', async () => {
            const voiceData = {
                roleName: 'test-speaker',
                audioUrl: 'http://localhost:2994/audio/test.wav'
            };

            // Remove deleteSpeaker method
            delete mockTtsService.deleteSpeaker;

            // Mock successful file deletion
            mockFetch.mockResolvedValue({ ok: true });

            await talkingHeadUI.deleteVoiceFiles(voiceData);

            // Verify file deletion still proceeded
            expect(mockFetch).toHaveBeenCalledWith(
                'http://localhost:2994/delete-file?path=http%3A%2F%2Flocalhost%3A2994%2Faudio%2Ftest.wav',
                { method: 'DELETE' }
            );
        });

        test('should handle file deletion errors gracefully', async () => {
            const voiceData = {
                roleName: 'test-speaker',
                audioUrl: 'http://localhost:2994/audio/test.wav'
            };

            // Mock successful TTS service deletion
            mockTtsService.deleteSpeaker.mockResolvedValue({ success: true });

            // Mock file deletion failure
            mockFetch.mockResolvedValue({
                ok: false,
                statusText: 'Not Found'
            });

            // Should not throw error
            await expect(talkingHeadUI.deleteVoiceFiles(voiceData)).resolves.toBeUndefined();

            expect(mockTtsService.deleteSpeaker).toHaveBeenCalledWith('test-speaker');
        });

        test('should collect all file paths for deletion', async () => {
            const voiceData = {
                roleName: 'test-speaker',
                audioUrl: 'http://localhost:2994/audio/test.wav',
                reference_audio_file_path: '/path/to/reference.wav',
                reference_audio_file: 'http://localhost:2994/audio/reference.wav'
            };

            // Mock successful operations
            mockTtsService.deleteSpeaker.mockResolvedValue({ success: true });
            mockFetch.mockResolvedValue({ ok: true });

            await talkingHeadUI.deleteVoiceFiles(voiceData);

            // Verify all file deletion calls (should be 3 unique files)
            expect(mockFetch).toHaveBeenCalledTimes(3);
            expect(mockFetch).toHaveBeenCalledWith(
                expect.stringContaining('delete-file'),
                { method: 'DELETE' }
            );
        });
    });

    describe('removeVoiceBufferItem', () => {
        beforeEach(() => {
            // Mock DOM elements
            global.document.querySelector = vi.fn((selector) => {
                if (selector.includes('data-timestamp')) {
                    return {
                        remove: vi.fn()
                    };
                }
                return null;
            });

            // Spy on deleteVoiceFiles and removeFavoriteVoiceFromSeed
            talkingHeadUI.deleteVoiceFiles = vi.fn();
            talkingHeadUI.removeFavoriteVoiceFromSeed = vi.fn();
        });

        test('should remove voice from DOM and buffer array', async () => {
            const voiceData = {
                timestamp: 1234567890,
                roleName: 'test-voice',
                favorite: false
            };

            talkingHeadUI.voiceBuffer = [voiceData];

            await talkingHeadUI.removeVoiceBufferItem(voiceData);

            // Verify DOM removal
            expect(global.document.querySelector).toHaveBeenCalledWith('[data-timestamp="1234567890"]');

            // Verify array removal
            expect(talkingHeadUI.voiceBuffer).toHaveLength(0);

            // Verify file deletion was called
            expect(talkingHeadUI.deleteVoiceFiles).toHaveBeenCalledWith(voiceData);

            // Verify notification
            expect(talkingHeadUI.showNotification).toHaveBeenCalledWith(
                'Voice "test-voice" deleted successfully'
            );
        });

        test('should remove favorite voice from seed file', async () => {
            const voiceData = {
                timestamp: 1234567890,
                roleName: 'favorite-voice',
                favorite: true
            };

            talkingHeadUI.voiceBuffer = [voiceData];

            await talkingHeadUI.removeVoiceBufferItem(voiceData);

            // Verify favorite removal was called
            expect(talkingHeadUI.removeFavoriteVoiceFromSeed).toHaveBeenCalledWith(voiceData);

            // Verify file deletion was called
            expect(talkingHeadUI.deleteVoiceFiles).toHaveBeenCalledWith(voiceData);
        });

        test('should handle errors gracefully', async () => {
            const voiceData = {
                timestamp: 1234567890,
                roleName: 'test-voice',
                favorite: false
            };

            // Make deleteVoiceFiles throw an error
            talkingHeadUI.deleteVoiceFiles.mockRejectedValue(new Error('File deletion failed'));

            talkingHeadUI.voiceBuffer = [voiceData];

            await talkingHeadUI.removeVoiceBufferItem(voiceData);

            // Verify error notification
            expect(talkingHeadUI.showNotification).toHaveBeenCalledWith(
                'Failed to remove voice from buffer',
                true
            );
        });
    });

    describe('removeFavoriteVoiceFromSeed', () => {
        beforeEach(() => {
            // Mock the cache utilities import
            talkingHeadUI.mockCacheUtils = {
                storeSeed: vi.fn().mockResolvedValue({ success: true }),
                getStoredSeed: vi.fn(),
                generateCacheKey: vi.fn().mockResolvedValue('test-cache-key')
            };

            // Mock dynamic import
            vi.doMock('@/utils/cache.js', () => talkingHeadUI.mockCacheUtils);
        });

        test('should clear seed data for default avatar', async () => {
            const voiceData = {
                roleName: 'test-voice',
                favorite: true
            };

            // Set avatar to default
            talkingHeadUI.avatar.avatar.name = 'default';

            // Mock existing seed data
            talkingHeadUI.mockCacheUtils.getStoredSeed.mockResolvedValue(JSON.stringify({
                roleName: 'test-voice',
                favorite: true,
                reference_audio_file_path: '/path/to/audio.wav'
            }));

            await talkingHeadUI.removeFavoriteVoiceFromSeed(voiceData);

            // Verify seed data was cleared
            expect(talkingHeadUI.mockCacheUtils.storeSeed).toHaveBeenCalledWith(
                'default',
                expect.stringContaining('"favorite":false')
            );

            expect(talkingHeadUI.mockCacheUtils.storeSeed).toHaveBeenCalledWith(
                'default',
                expect.stringContaining('"roleName":null')
            );
        });

        test('should reset to default voice if removing active voice', async () => {
            const voiceData = {
                roleName: 'active-voice',
                favorite: true
            };

            // Set this voice as currently active
            talkingHeadUI.avatar.currentClonedVoice = 'active-voice';

            // Mock existing seed data
            talkingHeadUI.mockCacheUtils.getStoredSeed.mockResolvedValue(JSON.stringify({
                roleName: 'active-voice',
                favorite: true
            }));

            await talkingHeadUI.removeFavoriteVoiceFromSeed(voiceData);

            // Verify voice reset
            expect(mockTtsService.useRegularVoice).toHaveBeenCalled();
            expect(talkingHeadUI.avatar.currentClonedVoice).toBe(null);
            expect(talkingHeadUI.updateActiveVoiceUI).toHaveBeenCalledWith(null);
        });

        test('should fallback to setVoice if useRegularVoice not available', async () => {
            const voiceData = {
                roleName: 'active-voice',
                favorite: true
            };

            // Set this voice as currently active
            talkingHeadUI.avatar.currentClonedVoice = 'active-voice';

            // Remove useRegularVoice method
            delete mockTtsService.useRegularVoice;

            // Mock existing seed data
            talkingHeadUI.mockCacheUtils.getStoredSeed.mockResolvedValue(JSON.stringify({
                roleName: 'active-voice',
                favorite: true
            }));

            await talkingHeadUI.removeFavoriteVoiceFromSeed(voiceData);

            // Verify fallback to setVoice
            expect(talkingHeadUI.avatar.setVoice).toHaveBeenCalledWith('chinese', 'male');
            expect(talkingHeadUI.avatar.currentClonedVoice).toBe(null);
        });

        test('should handle seed storage errors gracefully', async () => {
            const voiceData = {
                roleName: 'test-voice',
                favorite: true
            };

            // Mock storage failure
            talkingHeadUI.mockCacheUtils.storeSeed.mockResolvedValue({
                success: false,
                error: 'Storage failed'
            });

            // Mock existing seed data
            talkingHeadUI.mockCacheUtils.getStoredSeed.mockResolvedValue(JSON.stringify({
                roleName: 'test-voice',
                favorite: true
            }));

            await talkingHeadUI.removeFavoriteVoiceFromSeed(voiceData);

            // Should not throw error
            expect(talkingHeadUI.showNotification).toHaveBeenCalledWith(
                'Warning: Voice configuration may persist between sessions',
                true
            );
        });
    });

    describe('Integration Tests', () => {
        test('should perform complete voice deletion workflow', async () => {
            // Create a voice item in the buffer
            const voiceData = {
                timestamp: Date.now(),
                roleName: 'integration-test-voice',
                favorite: true,
                audioUrl: 'http://localhost:2994/audio/test.wav',
                reference_audio_file_path: '/path/to/audio.wav'
            };

            talkingHeadUI.voiceBuffer = [voiceData];

            // Mock all dependencies
            mockTtsService.deleteSpeaker.mockResolvedValue({ success: true });
            mockFetch.mockResolvedValue({ ok: true });

            // Mock DOM element
            global.document.querySelector = vi.fn(() => ({ remove: vi.fn() }));

            // Spy on internal methods
            talkingHeadUI.deleteVoiceFiles = vi.fn();
            talkingHeadUI.removeFavoriteVoiceFromSeed = vi.fn();

            // Execute the deletion
            await talkingHeadUI.removeVoiceBufferItem(voiceData);

            // Verify complete workflow
            expect(talkingHeadUI.deleteVoiceFiles).toHaveBeenCalledWith(voiceData);
            expect(talkingHeadUI.removeFavoriteVoiceFromSeed).toHaveBeenCalledWith(voiceData);
            expect(talkingHeadUI.voiceBuffer).toHaveLength(0);
            expect(talkingHeadUI.showNotification).toHaveBeenCalledWith(
                'Voice "integration-test-voice" deleted successfully'
            );
        });

        test('should handle partial failures gracefully', async () => {
            const voiceData = {
                timestamp: Date.now(),
                roleName: 'partial-failure-voice',
                favorite: false,
                audioUrl: 'http://localhost:2994/audio/test.wav'
            };

            talkingHeadUI.voiceBuffer = [voiceData];

            // Mock TTS service failure but file deletion success
            mockTtsService.deleteSpeaker.mockRejectedValue(new Error('TTS error'));
            mockFetch.mockResolvedValue({ ok: true });

            // Mock DOM element
            global.document.querySelector = vi.fn(() => ({ remove: vi.fn() }));

            // Execute the deletion - should not throw
            await expect(talkingHeadUI.removeVoiceBufferItem(voiceData)).resolves.toBeUndefined();

            // Voice should still be removed from buffer
            expect(talkingHeadUI.voiceBuffer).toHaveLength(0);
        });
    });
});
