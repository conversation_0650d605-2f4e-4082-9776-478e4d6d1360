/**
 * Test suite for CameraManager
 * Tests the core camera management functionality
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';

// Mock MediaCaptureManager
class MockMediaCaptureManager {
    constructor() {
        this.isInitialized = false;
        this.isCapturing = false;
    }

    async initialize() {
        this.isInitialized = true;
        return true;
    }

    async startCapture() {
        if (!this.isInitialized) {
            throw new Error('Not initialized');
        }
        this.isCapturing = true;
        return true;
    }

    async captureFrame() {
        if (!this.isCapturing) {
            throw new Error('Not capturing');
        }
        return 'data:image/jpeg;base64,mockframedata';
    }

    stopCapture() {
        this.isCapturing = false;
    }

    dispose() {
        this.isInitialized = false;
        this.isCapturing = false;
    }
}

// Mock DOM
const mockContainer = {
    appendChild: vi.fn(),
    removeChild: vi.fn(),
    getBoundingClientRect: vi.fn().mockReturnValue({
        width: 800,
        height: 600,
        top: 0,
        left: 0
    })
};

global.document = {
    createElement: vi.fn().mockReturnValue({
        style: {},
        appendChild: vi.fn(),
        removeChild: vi.fn(),
        addEventListener: vi.fn(),
        getBoundingClientRect: vi.fn().mockReturnValue({
            width: 320,
            height: 240,
            top: 100,
            left: 100
        })
    }),
    addEventListener: vi.fn(),
    removeEventListener: vi.fn()
};

global.window = {
    addEventListener: vi.fn(),
    removeEventListener: vi.fn()
};

// Mock the MediaCaptureManager import
vi.mock('../../../../src/media/capture/MediaCaptureManager.ts', () => ({
    MediaCaptureManager: MockMediaCaptureManager
}));

describe('CameraManager', () => {
    let CameraManager;
    let cameraManager;

    beforeEach(async () => {
        // Import CameraManager dynamically to ensure mocks are applied
        const module = await import('../../../../src/media/core/CameraManager.js');
        CameraManager = module.CameraManager;

        cameraManager = new CameraManager(mockContainer, {
            defaultMode: 'corner',
            cornerSize: { width: 320, height: 240 },
            popupSize: { width: 640, height: 480 }
        });
    });

    afterEach(() => {
        if (cameraManager) {
            cameraManager.dispose();
        }
        vi.clearAllMocks();
    });

    describe('Constructor', () => {
        it('should initialize with default options', () => {
            expect(cameraManager.container).toBe(mockContainer);
            expect(cameraManager.options.defaultMode).toBe('corner');
            expect(cameraManager.options.cornerSize.width).toBe(320);
            expect(cameraManager.options.cornerSize.height).toBe(240);
        });

        it('should handle custom options', () => {
            const customManager = new CameraManager(mockContainer, {
                defaultMode: 'popup',
                cornerPosition: 'top-left',
                animationDuration: 500
            });

            expect(customManager.options.defaultMode).toBe('popup');
            expect(customManager.options.cornerPosition).toBe('top-left');
            expect(customManager.options.animationDuration).toBe(500);
        });

        it('should create MediaCaptureManager instance', () => {
            expect(cameraManager.mediaCaptureManager).toBeDefined();
        });
    });

    describe('Initialization', () => {
        it('should initialize successfully', async () => {
            const result = await cameraManager.initialize();

            expect(result).toBe(true);
            expect(cameraManager.isInitialized).toBe(true);
        });

        it('should not initialize twice', async () => {
            await cameraManager.initialize();

            const result = await cameraManager.initialize();
            expect(result).toBe(true); // Should return true but not reinitialize
        });

        it('should handle initialization failure', async () => {
            // Mock MediaCaptureManager initialization failure
            cameraManager.mediaCaptureManager.initialize = vi.fn().mockResolvedValue(false);

            const result = await cameraManager.initialize();
            expect(result).toBe(false);
            expect(cameraManager.isInitialized).toBe(false);
        });
    });

    describe('Camera Control', () => {
        beforeEach(async () => {
            await cameraManager.initialize();
        });

        it('should start camera successfully', async () => {
            const result = await cameraManager.startCamera();

            expect(result).toBe(true);
            expect(cameraManager.isActive).toBe(true);
        });

        it('should stop camera successfully', () => {
            cameraManager.isActive = true;
            cameraManager.stopCamera();

            expect(cameraManager.isActive).toBe(false);
        });

        it('should handle start camera failure', async () => {
            cameraManager.mediaCaptureManager.startCapture = vi.fn().mockResolvedValue(false);

            const result = await cameraManager.startCamera();
            expect(result).toBe(false);
            expect(cameraManager.isActive).toBe(false);
        });
    });

    describe('Photo Capture', () => {
        beforeEach(async () => {
            await cameraManager.initialize();
            await cameraManager.startCamera();
        });

        it('should capture photo successfully', async () => {
            const result = await cameraManager.capturePhoto();

            expect(result).toBe('data:image/jpeg;base64,mockframedata');
        });

        it('should handle capture when camera not active', async () => {
            cameraManager.isActive = false;

            await expect(cameraManager.capturePhoto()).rejects.toThrow('Camera not active');
        });

        it('should handle capture failure', async () => {
            cameraManager.mediaCaptureManager.captureFrame = vi.fn().mockResolvedValue(null);

            await expect(cameraManager.capturePhoto()).rejects.toThrow('Failed to capture frame');
        });
    });

    describe('Camera Modes', () => {
        beforeEach(async () => {
            await cameraManager.initialize();
            await cameraManager.startCamera();
        });

        it('should show corner view', async () => {
            await cameraManager.showCamera('corner');

            expect(cameraManager.currentMode).toBe('corner');
        });

        it('should show popup view', async () => {
            await cameraManager.showCamera('popup');

            expect(cameraManager.currentMode).toBe('popup');
        });

        it('should hide camera', async () => {
            await cameraManager.showCamera('corner');
            await cameraManager.hideCamera();

            // Camera should be hidden but mode might be preserved
            expect(cameraManager.isActive).toBe(true); // Camera still active, just hidden
        });

        it('should toggle between modes', async () => {
            cameraManager.currentMode = 'corner';
            await cameraManager.toggleMode();

            expect(cameraManager.currentMode).toBe('popup');

            await cameraManager.toggleMode();
            expect(cameraManager.currentMode).toBe('corner');
        });
    });

    describe('Getters', () => {
        beforeEach(async () => {
            await cameraManager.initialize();
        });

        it('should return video element', () => {
            const videoElement = cameraManager.getVideoElement();
            expect(videoElement).toBeDefined();
        });

        it('should return media stream', () => {
            const mediaStream = cameraManager.getMediaStream();
            expect(mediaStream).toBeDefined();
        });

        it('should return stream status', () => {
            const status = cameraManager.getStreamStatus();
            expect(status).toBeDefined();
            expect(typeof status.isActive).toBe('boolean');
        });
    });

    describe('Cleanup', () => {
        it('should dispose properly', async () => {
            await cameraManager.initialize();
            await cameraManager.startCamera();

            cameraManager.dispose();

            expect(cameraManager.isInitialized).toBe(false);
            expect(cameraManager.isActive).toBe(false);
        });

        it('should handle disposal without initialization', () => {
            const newManager = new CameraManager(mockContainer);

            expect(() => newManager.dispose()).not.toThrow();
        });
    });

    describe('Error Handling', () => {
        it('should handle missing container', () => {
            expect(() => new CameraManager(null)).not.toThrow();
        });

        it('should handle camera operations before initialization', async () => {
            const newManager = new CameraManager(mockContainer);

            const result = await newManager.startCamera();
            expect(result).toBe(false);
        });
    });
});
