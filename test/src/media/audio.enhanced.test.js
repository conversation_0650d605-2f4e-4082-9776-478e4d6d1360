/**
 * Enhanced Audio Processing Tests
 * Tests for improved format detection, PCM to WAV conversion, and audio validation
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import {
    detectAudioFormatFromBase64,
    convertPCMToWAVBlob,
    validateAudioBlob,
    base64ToBlobEnhanced,
    AudioResponseProcessor,
    base64ToUint8Array
} from '../../../src/media/modality/audio.ts';

// Mock logger
const mockLogger = {
    debug: vi.fn(),
    info: vi.fn(),
    warn: vi.fn(),
    error: vi.fn()
};

// Test data generators
function generateWAVHeader(sampleRate = 24000, channels = 1, bitDepth = 16, dataSize = 1000) {
    const header = new ArrayBuffer(44);
    const view = new DataView(header);

    const bytesPerSample = bitDepth / 8;
    const blockAlign = channels * bytesPerSample;
    const fileSize = 36 + dataSize;

    // RIFF header
    view.setUint32(0, 0x46464952, true);   // "RIFF"
    view.setUint32(4, fileSize, true);     // File size - 8
    view.setUint32(8, 0x45564157, true);   // "WAVE"

    // fmt chunk
    view.setUint32(12, 0x20746d66, true);  // "fmt "
    view.setUint32(16, 16, true);          // fmt chunk size
    view.setUint16(20, 1, true);           // PCM format
    view.setUint16(22, channels, true);    // Number of channels
    view.setUint32(24, sampleRate, true);  // Sample rate
    view.setUint32(28, sampleRate * blockAlign, true); // Byte rate
    view.setUint16(32, blockAlign, true);  // Block align
    view.setUint16(34, bitDepth, true);    // Bits per sample

    // data chunk
    view.setUint32(36, 0x61746164, true);  // "data"
    view.setUint32(40, dataSize, true);    // Data size

    return new Uint8Array(header);
}

function generatePCMData(samples = 500) {
    const pcmData = new Int16Array(samples);
    for (let i = 0; i < samples; i++) {
        // Generate a simple sine wave
        pcmData[i] = Math.sin(2 * Math.PI * 440 * i / 24000) * 32767;
    }
    return new Uint8Array(pcmData.buffer);
}

function uint8ArrayToBase64(uint8Array) {
    return btoa(String.fromCharCode.apply(null, Array.from(uint8Array)));
}

describe('Enhanced Audio Processing', () => {
    beforeEach(() => {
        vi.clearAllMocks();
    });

    describe('detectAudioFormatFromBase64', () => {
        it('should detect complete WAV files', () => {
            const wavHeader = generateWAVHeader();
            const pcmData = generatePCMData();
            const completeWAV = new Uint8Array(wavHeader.length + pcmData.length);
            completeWAV.set(wavHeader, 0);
            completeWAV.set(pcmData, wavHeader.length);

            const base64WAV = uint8ArrayToBase64(completeWAV);
            const result = detectAudioFormatFromBase64(base64WAV);

            expect(result.isComplete).toBe(true);
            expect(result.format).toBe('complete_wav');
            expect(result.needsHeaders).toBe(false);
            expect(result.audioInfo).toBeDefined();
            expect(result.audioInfo.sampleRate).toBe(24000);
            expect(result.audioInfo.channels).toBe(1);
            expect(result.audioInfo.bitDepth).toBe(16);
        });

        it('should detect raw PCM data', () => {
            const pcmData = generatePCMData();
            const base64PCM = uint8ArrayToBase64(pcmData);

            const result = detectAudioFormatFromBase64(base64PCM);

            expect(result.isComplete).toBe(false);
            expect(result.format).toBe('raw_pcm');
            expect(result.needsHeaders).toBe(true);
            expect(result.audioInfo).toBeUndefined();
        });

        it('should handle empty or invalid base64', () => {
            const result1 = detectAudioFormatFromBase64('');
            expect(result1.format).toBe('unknown');
            expect(result1.isComplete).toBe(false);

            const result2 = detectAudioFormatFromBase64('invalid-base64!@#');
            expect(result2.format).toBe('unknown');
            expect(result2.isComplete).toBe(false);
        });

        it('should handle partial headers gracefully', () => {
            // Only RIFF header, no WAVE
            const partialHeader = new Uint8Array([0x52, 0x49, 0x46, 0x46, 0x00, 0x00, 0x00, 0x00]);
            const base64Partial = uint8ArrayToBase64(partialHeader);

            const result = detectAudioFormatFromBase64(base64Partial);
            expect(result.isComplete).toBe(false);
            expect(result.format).toBe('raw_pcm');
        });
    });

    describe('convertPCMToWAVBlob', () => {
        it('should convert PCM data to valid WAV blob', async () => {
            const pcmData = generatePCMData(1000);
            const base64PCM = uint8ArrayToBase64(pcmData);

            const wavBlob = convertPCMToWAVBlob(base64PCM);

            expect(wavBlob).toBeInstanceOf(Blob);
            expect(wavBlob.type).toBe('audio/wav');
            expect(wavBlob.size).toBe(44 + pcmData.length); // Header + data

            // Verify the blob contains valid WAV data
            const arrayBuffer = await wavBlob.arrayBuffer();
            const view = new DataView(arrayBuffer);

            // Check RIFF header
            expect(view.getUint32(0, true)).toBe(0x46464952); // "RIFF"
            expect(view.getUint32(8, true)).toBe(0x45564157); // "WAVE"
            expect(view.getUint32(12, true)).toBe(0x20746d66); // "fmt "
            expect(view.getUint32(36, true)).toBe(0x61746164); // "data"

            // Check audio parameters
            expect(view.getUint16(22, true)).toBe(1); // channels
            expect(view.getUint32(24, true)).toBe(24000); // sample rate
            expect(view.getUint16(34, true)).toBe(16); // bit depth
        });

        it('should handle custom audio configuration', async () => {
            const pcmData = generatePCMData(500);
            const base64PCM = uint8ArrayToBase64(pcmData);

            const config = {
                sampleRate: 44100,
                numChannels: 2,
                bitDepth: 32
            };

            const wavBlob = convertPCMToWAVBlob(base64PCM, config);
            const arrayBuffer = await wavBlob.arrayBuffer();
            const view = new DataView(arrayBuffer);

            expect(view.getUint16(22, true)).toBe(2); // channels
            expect(view.getUint32(24, true)).toBe(44100); // sample rate
            expect(view.getUint16(34, true)).toBe(32); // bit depth
        });

        it('should handle empty PCM data', () => {
            const base64Empty = '';

            const wavBlob = convertPCMToWAVBlob(base64Empty);

            expect(wavBlob).toBeInstanceOf(Blob);
            expect(wavBlob.size).toBe(44); // Only header
        });

        it('should throw error for invalid base64', () => {
            expect(() => {
                convertPCMToWAVBlob('invalid-base64!@#');
            }).toThrow();
        });
    });

    describe('validateAudioBlob', () => {
        it('should validate correct WAV blob', async () => {
            const wavHeader = generateWAVHeader(24000, 1, 16, 1000);
            const pcmData = generatePCMData(500);
            const completeWAV = new Uint8Array(wavHeader.length + pcmData.length);
            completeWAV.set(wavHeader, 0);
            completeWAV.set(pcmData, wavHeader.length);

            const blob = new Blob([completeWAV], { type: 'audio/wav' });
            const result = await validateAudioBlob(blob);

            expect(result.isValid).toBe(true);
            expect(result.audioInfo).toBeDefined();
            expect(result.audioInfo.channels).toBe(1);
            expect(result.audioInfo.sampleRate).toBe(24000);
            expect(result.audioInfo.bitDepth).toBe(16);
            expect(result.audioInfo.duration).toBeGreaterThan(0);
        });

        it('should reject blob too small for WAV header', async () => {
            const smallBlob = new Blob([new Uint8Array(20)], { type: 'audio/wav' });
            const result = await validateAudioBlob(smallBlob);

            expect(result.isValid).toBe(false);
            expect(result.error).toContain('too small');
        });

        it('should reject invalid WAV structure', async () => {
            // Create blob with wrong headers
            const invalidWAV = new Uint8Array(44);
            invalidWAV[0] = 0x00; // Wrong RIFF header

            const blob = new Blob([invalidWAV], { type: 'audio/wav' });
            const result = await validateAudioBlob(blob);

            expect(result.isValid).toBe(false);
            expect(result.error).toContain('Invalid WAV structure');
        });

        it('should handle blob read errors gracefully', async () => {
            // Mock a blob that throws when reading
            const mockBlob = {
                size: 100,
                arrayBuffer: () => Promise.reject(new Error('Read failed'))
            };

            const result = await validateAudioBlob(mockBlob as Blob);

            expect(result.isValid).toBe(false);
            expect(result.error).toContain('Audio validation failed');
        });
    });

    describe('base64ToBlobEnhanced', () => {
        it('should handle complete WAV data correctly', () => {
            const wavHeader = generateWAVHeader();
            const pcmData = generatePCMData();
            const completeWAV = new Uint8Array(wavHeader.length + pcmData.length);
            completeWAV.set(wavHeader, 0);
            completeWAV.set(pcmData, wavHeader.length);

            const base64WAV = uint8ArrayToBase64(completeWAV);
            const blob = base64ToBlobEnhanced(base64WAV);

            expect(blob).toBeInstanceOf(Blob);
            expect(blob.type).toBe('audio/wav');
            expect(blob.size).toBe(completeWAV.length);
        });

        it('should convert raw PCM to WAV format', () => {
            const pcmData = generatePCMData();
            const base64PCM = uint8ArrayToBase64(pcmData);

            const blob = base64ToBlobEnhanced(base64PCM);

            expect(blob).toBeInstanceOf(Blob);
            expect(blob.type).toBe('audio/wav');
            expect(blob.size).toBe(44 + pcmData.length); // Header + PCM data
        });

        it('should handle unknown formats gracefully', () => {
            // Create some random binary data that doesn't look like audio
            const randomData = new Uint8Array(100);
            for (let i = 0; i < randomData.length; i++) {
                randomData[i] = Math.floor(Math.random() * 256);
            }

            const base64Random = uint8ArrayToBase64(randomData);
            const blob = base64ToBlobEnhanced(base64Random);

            expect(blob).toBeInstanceOf(Blob);
            expect(blob.type).toBe('audio/wav');
        });

        it('should throw error for invalid base64', () => {
            expect(() => {
                base64ToBlobEnhanced('invalid-base64!@#');
            }).toThrow();
        });
    });

    describe('AudioResponseProcessor Enhanced', () => {
        let processor;
        let mockAudio;
        let originalAudio;

        beforeEach(() => {
            processor = new AudioResponseProcessor({ logger: mockLogger });

            // Mock Audio constructor and methods
            mockAudio = {
                play: vi.fn().mockResolvedValue(undefined),
                addEventListener: vi.fn(),
                removeEventListener: vi.fn(),
                src: '',
                duration: 5.0,
                readyState: 4,
                networkState: 1,
                error: null,
                onloadeddata: null,
                oncanplaythrough: null,
                onended: null,
                onerror: null
            };

            originalAudio = global.Audio;
            global.Audio = vi.fn().mockImplementation(() => mockAudio);

            // Mock URL methods
            global.URL = {
                createObjectURL: vi.fn().mockReturnValue('blob:mock-url'),
                revokeObjectURL: vi.fn()
            };
        });

        afterEach(() => {
            global.Audio = originalAudio;
            vi.clearAllMocks();
        });

        it('should play audio with enhanced format detection', async () => {
            const pcmData = generatePCMData();
            const base64PCM = uint8ArrayToBase64(pcmData);

            // Simulate successful playback
            setTimeout(() => {
                if (mockAudio.onended) mockAudio.onended();
            }, 10);

            const result = await processor.playAudioData(base64PCM);

            expect(result.success).toBe(true);
            expect(result.duration).toBe(5.0);
            expect(mockLogger.debug).toHaveBeenCalledWith(
                expect.stringContaining('Audio format analysis'),
                expect.objectContaining({
                    format: 'raw_pcm',
                    isComplete: false,
                    needsHeaders: true
                })
            );
        });

        it('should handle audio decode errors with detailed logging', async () => {
            const pcmData = generatePCMData();
            const base64PCM = uint8ArrayToBase64(pcmData);

            // Simulate decode error
            mockAudio.error = {
                code: 3, // MEDIA_ERR_DECODE
                MEDIA_ERR_DECODE: 3,
                MEDIA_ERR_ABORTED: 1,
                MEDIA_ERR_NETWORK: 2,
                MEDIA_ERR_SRC_NOT_SUPPORTED: 4
            };

            setTimeout(() => {
                if (mockAudio.onerror) mockAudio.onerror(new Event('error'));
            }, 10);

            await expect(processor.playAudioData(base64PCM)).rejects.toThrow(
                'Audio playback failed (DECODE): Audio decode error'
            );

            expect(mockLogger.error).toHaveBeenCalledWith(
                'Audio playback failed:',
                expect.objectContaining({
                    errorCode: 'DECODE',
                    errorMessage: expect.stringContaining('decode error')
                })
            );
        });

        it('should handle unsupported format errors', async () => {
            const pcmData = generatePCMData();
            const base64PCM = uint8ArrayToBase64(pcmData);

            // Simulate format not supported error
            mockAudio.error = {
                code: 4, // MEDIA_ERR_SRC_NOT_SUPPORTED
                MEDIA_ERR_DECODE: 3,
                MEDIA_ERR_ABORTED: 1,
                MEDIA_ERR_NETWORK: 2,
                MEDIA_ERR_SRC_NOT_SUPPORTED: 4
            };

            setTimeout(() => {
                if (mockAudio.onerror) mockAudio.onerror(new Event('error'));
            }, 10);

            await expect(processor.playAudioData(base64PCM)).rejects.toThrow(
                'Audio playback failed (NOT_SUPPORTED): Audio format not supported'
            );
        });

        it('should handle play() method failures', async () => {
            const pcmData = generatePCMData();
            const base64PCM = uint8ArrayToBase64(pcmData);

            // Simulate play() rejection
            mockAudio.play.mockRejectedValue(new Error('NotAllowedError: play() failed'));

            await expect(processor.playAudioData(base64PCM)).rejects.toThrow(
                'Audio play failed: NotAllowedError: play() failed'
            );

            expect(mockLogger.error).toHaveBeenCalledWith(
                'Audio play() method failed:',
                expect.objectContaining({
                    error: 'NotAllowedError: play() failed',
                    errorName: 'Error'
                })
            );
        });

        it('should log validation warnings but continue playback', async () => {
            // Create invalid WAV-looking data
            const invalidData = new Uint8Array(100);
            const base64Invalid = uint8ArrayToBase64(invalidData);

            setTimeout(() => {
                if (mockAudio.onended) mockAudio.onended();
            }, 10);

            const result = await processor.playAudioData(base64Invalid);

            expect(result.success).toBe(true);
            expect(mockLogger.warn).toHaveBeenCalledWith(
                'Audio blob validation failed:',
                expect.objectContaining({
                    error: expect.stringContaining('Invalid WAV structure')
                })
            );
        });
    });

    describe('Integration Tests', () => {
        it('should handle complete end-to-end flow for PCM data', async () => {
            // Generate test PCM data
            const pcmData = generatePCMData(2000);
            const base64PCM = uint8ArrayToBase64(pcmData);

            // Step 1: Format detection
            const formatInfo = detectAudioFormatFromBase64(base64PCM);
            expect(formatInfo.format).toBe('raw_pcm');
            expect(formatInfo.needsHeaders).toBe(true);

            // Step 2: Enhanced blob creation
            const blob = base64ToBlobEnhanced(base64PCM);
            expect(blob.size).toBe(44 + pcmData.length);

            // Step 3: Validation
            const validation = await validateAudioBlob(blob);
            expect(validation.isValid).toBe(true);
            expect(validation.audioInfo.sampleRate).toBe(24000);
            expect(validation.audioInfo.channels).toBe(1);
            expect(validation.audioInfo.bitDepth).toBe(16);
        });

        it('should handle complete end-to-end flow for complete WAV data', async () => {
            // Generate complete WAV data
            const wavHeader = generateWAVHeader(44100, 2, 16, 4000);
            const pcmData = generatePCMData(2000);
            const completeWAV = new Uint8Array(wavHeader.length + pcmData.length);
            completeWAV.set(wavHeader, 0);
            completeWAV.set(pcmData, wavHeader.length);

            const base64WAV = uint8ArrayToBase64(completeWAV);

            // Step 1: Format detection
            const formatInfo = detectAudioFormatFromBase64(base64WAV);
            expect(formatInfo.format).toBe('complete_wav');
            expect(formatInfo.isComplete).toBe(true);
            expect(formatInfo.audioInfo.sampleRate).toBe(44100);
            expect(formatInfo.audioInfo.channels).toBe(2);

            // Step 2: Enhanced blob creation (should pass through)
            const blob = base64ToBlobEnhanced(base64WAV);
            expect(blob.size).toBe(completeWAV.length);

            // Step 3: Validation
            const validation = await validateAudioBlob(blob);
            expect(validation.isValid).toBe(true);
            expect(validation.audioInfo.sampleRate).toBe(44100);
            expect(validation.audioInfo.channels).toBe(2);
        });
    });
}); 