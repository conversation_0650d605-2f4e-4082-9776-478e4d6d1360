/**
 * Unit tests for RealtimeAudioManager
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { RealtimeAudioManager } from '../../../../src/media/streaming/RealtimeAudioManager';

// Mock dependencies
vi.mock('../../../../src/utils/logger.js', () => ({
    createLogger: () => ({
        debug: vi.fn(),
        info: vi.fn(),
        warn: vi.fn(),
        error: vi.fn(),
        setLogLevel: vi.fn()
    })
}));

vi.mock('../../../../src/media/modality/audio', () => ({
    processRealtimeAudio: vi.fn().mockImplementation(async () => ({
        success: true,
        base64Audio: 'mock-base64-audio-data',
        metadata: {
            inputType: 'ArrayBuffer',
            inputSize: 1000,
            outputSize: 1500,
            processingTimeMs: 10
        }
    }))
}));

describe('RealtimeAudioManager', () => {
    let audioManager;
    let mockAudioSendCallback;

    beforeEach(() => {
        // Create a fresh instance for each test
        audioManager = new RealtimeAudioManager({
            sampleRate: 24000,
            minIntervalMs: 200,
            enableDebugLogging: false
        });

        // Create a mock audio send callback
        mockAudioSendCallback = vi.fn().mockResolvedValue(true);
        audioManager.setSendAudioCallback(mockAudioSendCallback);

        // Reset mocks
        vi.clearAllMocks();

        // Mock timer functions
        vi.useFakeTimers();
    });

    afterEach(() => {
        vi.useRealTimers();
    });

    it('should initialize with default configuration', () => {
        const defaultManager = new RealtimeAudioManager();
        expect(defaultManager).toBeDefined();
    });

    it('should initialize with custom configuration', () => {
        const customManager = new RealtimeAudioManager({
            sampleRate: 48000,
            numChannels: 2,
            bitDepth: 24,
            minIntervalMs: 100
        });
        expect(customManager).toBeDefined();
    });

    it('should set audio send callback', () => {
        const callback = vi.fn();
        audioManager.setSendAudioCallback(callback);
        expect(audioManager.audioSendCallback).toBe(callback);
    });

    it('should initialize a new session', () => {
        const sessionState = audioManager.initSession();
        expect(sessionState.isActive).toBe(true);
        expect(sessionState.isReady).toBe(false);
        expect(sessionState.createdAt).toBeDefined();
    });

    it('should make session ready after delay', async () => {
        audioManager.initSession();
        expect(audioManager.getSessionState().isReady).toBe(false);

        // Advance timers to trigger the readiness timeout
        vi.advanceTimersByTime(1000);

        expect(audioManager.getSessionState().isReady).toBe(true);
    });

    it('should not send audio if no callback is set', async () => {
        // Create a new manager without a callback
        const noCallbackManager = new RealtimeAudioManager();
        const result = await noCallbackManager.processAndSendAudio(new ArrayBuffer(1000));
        expect(result).toBe(false);
    });

    it('should not send audio if session is not ready', async () => {
        audioManager.initSession();
        const result = await audioManager.processAndSendAudio(new ArrayBuffer(1000));
        expect(result).toBe(false);
        expect(mockAudioSendCallback).not.toHaveBeenCalled();
    });

    it('should process and send audio when session is ready', async () => {
        // Initialize session and make it ready
        audioManager.initSession();
        vi.advanceTimersByTime(1000);

        const mockAudioData = new ArrayBuffer(1000);
        const result = await audioManager.processAndSendAudio(mockAudioData);

        expect(result).toBe(true);
        expect(mockAudioSendCallback).toHaveBeenCalledWith('mock-base64-audio-data');
    });

    it('should enforce rate limiting between audio chunks', async () => {
        // Initialize session and make it ready
        audioManager.initSession();
        vi.advanceTimersByTime(1000);

        // Send first chunk
        const firstResult = await audioManager.processAndSendAudio(new ArrayBuffer(1000));
        expect(firstResult).toBe(true);
        expect(mockAudioSendCallback).toHaveBeenCalledTimes(1);

        // Try to send second chunk immediately (should wait due to rate limiting)
        const secondChunkPromise = audioManager.processAndSendAudio(new ArrayBuffer(1000));

        // Advance time by less than the minimum interval
        vi.advanceTimersByTime(100);

        // Second chunk should not be sent yet
        expect(mockAudioSendCallback).toHaveBeenCalledTimes(1);

        // Advance time to complete the minimum interval
        vi.advanceTimersByTime(100);

        // Resolve any pending promises
        await Promise.resolve();

        // Now the second chunk should be sent
        const secondResult = await secondChunkPromise;
        expect(secondResult).toBe(true);
        expect(mockAudioSendCallback).toHaveBeenCalledTimes(2);
    });

    it('should add extra delay for the first audio chunk', async () => {
        // Initialize session and make it ready
        audioManager.initSession();
        vi.advanceTimersByTime(1000);

        // Set the session creation time to simulate a recent session creation
        audioManager.audioRateTracker.sessionCreatedAt = Date.now() - 500;

        // Try to send the first chunk
        const chunkPromise = audioManager.processAndSendAudio(new ArrayBuffer(1000));

        // Advance time by less than the required extra delay
        vi.advanceTimersByTime(500);

        // First chunk should not be sent yet
        expect(mockAudioSendCallback).toHaveBeenCalledTimes(0);

        // Advance time to complete the extra delay
        vi.advanceTimersByTime(500);

        // Resolve any pending promises
        await Promise.resolve();

        // Now the chunk should be sent
        const result = await chunkPromise;
        expect(result).toBe(true);
        expect(mockAudioSendCallback).toHaveBeenCalledTimes(1);
    });

    it('should handle errors from audio send callback', async () => {
        // Set up a callback that throws an error
        const errorCallback = vi.fn().mockRejectedValue(new Error('Send error'));
        audioManager.setSendAudioCallback(errorCallback);

        // Initialize session and make it ready
        audioManager.initSession();
        vi.advanceTimersByTime(1000);

        // Try to send audio
        const result = await audioManager.processAndSendAudio(new ArrayBuffer(1000));

        // Should return false on error
        expect(result).toBe(false);
        expect(errorCallback).toHaveBeenCalledTimes(1);
    });

    it('should reset session state', () => {
        // Initialize and advance session
        audioManager.initSession();
        vi.advanceTimersByTime(1000);
        audioManager.audioRateTracker.totalChunks = 10;

        // Reset session
        audioManager.resetSession();

        // Check that session state was reset
        const sessionState = audioManager.getSessionState();
        expect(sessionState.isReady).toBe(false);
        expect(sessionState.createdAt).toBeNull();
        expect(audioManager.audioRateTracker.totalChunks).toBe(0);
    });

    it('should dispose resources', () => {
        // Set up manager with session and callback
        audioManager.initSession();
        audioManager.setSendAudioCallback(mockAudioSendCallback);

        // Dispose
        audioManager.dispose();

        // Check that resources were cleaned up
        expect(audioManager.audioSendCallback).toBeNull();
        expect(audioManager.audioRateTracker.sessionCreatedAt).toBeNull();
    });
}); 