/**
 * Test suite for NEW MODALITY structure verification
 * Verifies the reorganized media module exports and functionality after consolidation
 */

import { describe, it, expect } from 'vitest';

describe('Media Modality Structure Verification', () => {
    describe('Audio Modality Exports', () => {
        it('should export all audio processing functions from new modality structure', async () => {
            const {
                processRealtimeAudio,
                validateAudioData,
                createFallbackBase64Audio,
                convertFloat32ToWav,
                detectAudioFormat,
                base64ToBlob,
                blobToBase64,
                checkAudioBuffer,
                createFallbackTone,
                createAudioFromBlob,
                playAudioWithPromise,
                splitAudioIntoChunks,
                b64ToArrayBuffer,
                concatArrayBuffers,
                pcmToAudioBuffer,
                setReverb,
                setMixerGain,
                extractRoleNameFromAudioFile,
                prepareTTSRequestPayload
            } = await import('../../../src/media/modality/audio.js');

            // Test that all core audio functions are available
            expect(typeof processRealtimeAudio).toBe('function');
            expect(typeof validateAudioData).toBe('function');
            expect(typeof createFallbackBase64Audio).toBe('function');
            expect(typeof convertFloat32ToWav).toBe('function');
            expect(typeof detectAudioFormat).toBe('function');
            expect(typeof base64ToBlob).toBe('function');
            expect(typeof blobToBase64).toBe('function');
            expect(typeof checkAudioBuffer).toBe('function');
            expect(typeof createFallbackTone).toBe('function');
            expect(typeof createAudioFromBlob).toBe('function');
            expect(typeof playAudioWithPromise).toBe('function');
            expect(typeof splitAudioIntoChunks).toBe('function');
            expect(typeof b64ToArrayBuffer).toBe('function');
            expect(typeof concatArrayBuffers).toBe('function');
            expect(typeof pcmToAudioBuffer).toBe('function');
            expect(typeof setReverb).toBe('function');
            expect(typeof setMixerGain).toBe('function');
            expect(typeof extractRoleNameFromAudioFile).toBe('function');
            expect(typeof prepareTTSRequestPayload).toBe('function');
        });

        it('should export audio configuration constants', async () => {
            const {
                AudioFormat,
                DEFAULT_AUDIO_CONFIG,
                ALIYUN_AUDIO_CONFIG
            } = await import('../../../src/media/modality/audio.js');

            expect(AudioFormat).toBeDefined();
            expect(AudioFormat.WAV).toBe('wav');
            expect(AudioFormat.MP3).toBe('mp3');
            expect(DEFAULT_AUDIO_CONFIG).toBeDefined();
            expect(DEFAULT_AUDIO_CONFIG.sampleRate).toBe(24000);
            expect(ALIYUN_AUDIO_CONFIG).toBeDefined();
        });
    });

    describe('Video Modality Exports', () => {
        it('should export all video processing functions from new modality structure', async () => {
            const {
                processVideoForRealtime,
                extractFramesInBrowser,
                createVideoThumbnail,
                detectVideoFormat,
                validateVideoInput,
                compressVideoFrame
            } = await import('../../../src/media/modality/video.js');

            expect(typeof processVideoForRealtime).toBe('function');
            expect(typeof extractFramesInBrowser).toBe('function');
            expect(typeof createVideoThumbnail).toBe('function');
            expect(typeof detectVideoFormat).toBe('function');
            expect(typeof validateVideoInput).toBe('function');
            expect(typeof compressVideoFrame).toBe('function');
        });

        it('should export video configuration constants', async () => {
            const {
                VideoFormat,
                DEFAULT_VIDEO_CONFIG
            } = await import('../../../src/media/modality/video.js');

            expect(VideoFormat).toBeDefined();
            expect(VideoFormat.MP4).toBe('mp4');
            expect(VideoFormat.WEBM).toBe('webm');
            expect(DEFAULT_VIDEO_CONFIG).toBeDefined();
            expect(DEFAULT_VIDEO_CONFIG.frameRate).toBe(2);
        });
    });

    describe('Integration Tests', () => {
        it('should be able to process audio through new modality structure', async () => {
            const { processRealtimeAudio } = await import('../../../src/media/modality/audio.js');

            const testAudio = new Float32Array([0.1, 0.2, 0.3, 0.4]);
            const result = await processRealtimeAudio(testAudio);

            expect(result.success).toBe(true);
            expect(result.base64Audio).toBeDefined();
            expect(result.metadata).toBeDefined();
        });

        it('should validate video input correctly', async () => {
            const { validateVideoInput } = await import('../../../src/media/modality/video.js');

            // Test with valid string input
            const result1 = validateVideoInput('test.mp4');
            expect(result1.isValid).toBe(true);

            // Test with invalid input
            const result2 = validateVideoInput(null);
            expect(result2.isValid).toBe(false);
            expect(result2.error).toBeDefined();
        });

        it('should detect audio formats correctly', async () => {
            const { detectAudioFormat, AudioFormat } = await import('../../../src/media/modality/audio.js');

            // Test WAV format detection
            const wavHeader = new Uint8Array([0x52, 0x49, 0x46, 0x46]); // RIFF
            expect(detectAudioFormat(wavHeader)).toBe(AudioFormat.WAV);

            // Test MP3 format detection
            const mp3Header = new Uint8Array([0x49, 0x44, 0x33, 0x03]); // ID3v2.3
            expect(detectAudioFormat(mp3Header)).toBe(AudioFormat.MP3);

            // Test unknown format
            const unknownHeader = new Uint8Array([0x00, 0x00, 0x00, 0x00]);
            expect(detectAudioFormat(unknownHeader)).toBe(AudioFormat.UNKNOWN);
        });
    });

    describe('Cleanup Verification', () => {
        it('should have consolidated audio utilities', () => {
            // The old files should be removed or deprecated
            expect(true).toBe(true); // Placeholder test
        });

        it('should maintain backward compatibility through media index', async () => {
            // Test that important functions are still available through the main index
            const {
                processRealtimeAudio,
                convertFloat32ToWav,
                detectAudioFormat
            } = await import('../../../src/media/index.ts');

            expect(typeof processRealtimeAudio).toBe('function');
            expect(typeof convertFloat32ToWav).toBe('function');
            expect(typeof detectAudioFormat).toBe('function');
        });
    });
});
