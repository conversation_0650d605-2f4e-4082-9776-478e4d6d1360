/**
 * Test suite for NEW MODALITY structure verification
 * Verifies the reorganized media module exports and functionality after consolidation
 */

import { describe, it, expect } from 'vitest';

describe('Media Modality Structure Verification', () => {
    describe('Audio Modality Exports', () => {
        it('should export all audio processing functions from new modality structure', async () => {
            const {
                processRealtimeAudio,
                validateAudioData,
                createFallbackBase64Audio,
                convertFloat32ToWav,
                detectAudioFormat,
                base64ToBlob,
                blobToBase64,
                checkAudioBuffer,
                createFallbackTone,
                createAudioFromBlob,
                playAudioWithPromise,
                splitAudioIntoChunks,
                b64ToArrayBuffer,
                concatArrayBuffers,
                pcmToAudioBuffer,
                setReverb,
                setMixerGain,
                extractRoleNameFromAudioFile,
                prepareTTSRequestPayload
            } = await import('../../../src/media/modality/audio.js');

            // Test that all core audio functions are available
            expect(typeof processRealtimeAudio).toBe('function');
            expect(typeof validateAudioData).toBe('function');
            expect(typeof createFallbackBase64Audio).toBe('function');
            expect(typeof convertFloat32ToWav).toBe('function');
            expect(typeof detectAudioFormat).toBe('function');
            expect(typeof base64ToBlob).toBe('function');
            expect(typeof blobToBase64).toBe('function');
            expect(typeof checkAudioBuffer).toBe('function');
            expect(typeof createFallbackTone).toBe('function');
            expect(typeof createAudioFromBlob).toBe('function');
            expect(typeof playAudioWithPromise).toBe('function');
            expect(typeof splitAudioIntoChunks).toBe('function');
            expect(typeof b64ToArrayBuffer).toBe('function');
            expect(typeof concatArrayBuffers).toBe('function');
            expect(typeof pcmToAudioBuffer).toBe('function');
            expect(typeof setReverb).toBe('function');
            expect(typeof setMixerGain).toBe('function');
            expect(typeof extractRoleNameFromAudioFile).toBe('function');
            expect(typeof prepareTTSRequestPayload).toBe('function');
        });

        it('should export audio configuration constants', async () => {
            const {
                AudioFormat,
                DEFAULT_AUDIO_CONFIG,
                ALIYUN_AUDIO_CONFIG
            } = await import('../../../src/media/modality/audio.js');

            expect(AudioFormat).toBeDefined();
            expect(AudioFormat.WAV).toBe('wav');
            expect(AudioFormat.MP3).toBe('mp3');
            expect(DEFAULT_AUDIO_CONFIG).toBeDefined();
            expect(DEFAULT_AUDIO_CONFIG.sampleRate).toBe(24000);
            expect(ALIYUN_AUDIO_CONFIG).toBeDefined();
        });

        expect(typeof detectAudioFormat).toBe('function');
        expect(typeof convertFloat32ToWav).toBe('function');
        expect(typeof AudioFormat).toBe('object');
        expect(typeof DEFAULT_AUDIO_OPTIONS).toBe('object');
    });

    it('should export multimodal utilities', async () => {
        const {
            normalizeInput,
            convertAudioFormat,
            prepareVideoFrames,
            processMultimodal,
            validateMultimodalInput,
            MULTIMODAL_LIMITS
        } = await import('@/media/index.ts');

        expect(typeof normalizeInput).toBe('function');
        expect(typeof convertAudioFormat).toBe('function');
        expect(typeof prepareVideoFrames).toBe('function');
        expect(typeof processMultimodal).toBe('function');
        expect(typeof validateMultimodalInput).toBe('function');
        expect(typeof MULTIMODAL_LIMITS).toBe('object');
    });

    it('should export camera management', async () => {
        const { CameraManager } = await import('@/media/index.ts');

        expect(typeof CameraManager).toBe('function');
    });

    it('should export essential streaming utilities', async () => {
        const { processWithClientSideChunking } = await import('@/media/index.ts');

        expect(typeof processWithClientSideChunking).toBe('function');
    });
});

describe('Removed Duplicates Verification', () => {
    it('should not have convertFloat32ToWav in mediaUtils anymore', async () => {
        const mediaUtils = await import('@/media/utils/mediaUtils.ts');

        expect(mediaUtils.convertFloat32ToWav).toBeUndefined();
    });

    it('should have convertFloat32ToWav only in audioUtils', async () => {
        const audioUtils = await import('@/media/utils/audioUtils.js');

        expect(typeof audioUtils.convertFloat32ToWav).toBe('function');
    });
});

describe('Functionality Verification', () => {
    it('should process multimodal input correctly after cleanup', async () => {
        const { normalizeInput, processMultimodal } = await import('../src/media/index.ts');

        const input = {
            text: 'Test input',
            audio: new Float32Array([0.1, 0.2, 0.3]),
            video: ['frame1', 'frame2']
        };

        const normalized = normalizeInput(input);
        expect(normalized.text).toBe('Test input');
        expect(normalized.audio).toBeInstanceOf(Float32Array);
        expect(normalized.video).toEqual(['frame1', 'frame2']);

        const processed = processMultimodal(normalized);
        expect(processed.text).toBe('Test input');
        expect(processed.videoFrames).toEqual(['frame1', 'frame2']);
        expect(processed.audioData).toBeInstanceOf(Float32Array);
    });

    it('should validate multimodal input correctly', async () => {
        const { validateMultimodalInput } = await import('../src/media/index.ts');

        const validInput = {
            text: 'Valid input',
            audio: new Float32Array([0.1, 0.2]),
            video: ['validframe'],
            metadata: { inputType: 'multimodal', isMultimodal: true }
        };

        const result = validateMultimodalInput(validInput);
        expect(result.isValid).toBe(true);
        expect(result.errors).toHaveLength(0);
    });

    it('should detect audio format correctly', async () => {
        const { detectAudioFormat, AudioFormat } = await import('../src/media/index.ts');

        // Test WAV format detection
        const wavHeader = new Uint8Array([0x52, 0x49, 0x46, 0x46]); // "RIFF"
        const format = detectAudioFormat(wavHeader);
        expect(format).toBe(AudioFormat.WAV);
    });

    it('should convert audio to WAV format', async () => {
        const { convertFloat32ToWav } = await import('../src/media/index.ts');

        const audioSamples = new Float32Array([0.1, 0.2, 0.3, 0.4]);
        const wavBlob = convertFloat32ToWav(audioSamples, {
            sampleRate: 16000,
            numChannels: 1,
            bitDepth: 16
        });

        expect(wavBlob).toBeInstanceOf(Blob);
        expect(wavBlob.type).toBe('audio/wav');
    });
});

describe('Constants Verification', () => {
    it('should have correct multimodal limits', async () => {
        const { MULTIMODAL_LIMITS } = await import('../src/media/index.ts');

        expect(MULTIMODAL_LIMITS.MAX_VIDEO_FRAME_SIZE_KB).toBe(500);
        expect(MULTIMODAL_LIMITS.RECOMMENDED_FRAME_RATE).toBe(2);
        expect(MULTIMODAL_LIMITS.AUDIO_FORMAT.SAMPLE_RATE).toBe(24000);
        expect(MULTIMODAL_LIMITS.AUDIO_FORMAT.CHANNELS).toBe(1);
        expect(MULTIMODAL_LIMITS.AUDIO_FORMAT.BIT_DEPTH).toBe(16);
    });

    it('should have correct audio format constants', async () => {
        const { AudioFormat, DEFAULT_AUDIO_OPTIONS } = await import('../src/media/index.ts');

        expect(AudioFormat.WAV).toBe('wav');
        expect(AudioFormat.MP3).toBe('mp3');
        expect(AudioFormat.OGG).toBe('ogg');

        expect(DEFAULT_AUDIO_OPTIONS.sampleRate).toBe(22050);
        expect(DEFAULT_AUDIO_OPTIONS.numChannels).toBe(1);
        expect(DEFAULT_AUDIO_OPTIONS.bitDepth).toBe(16);
    });
});
});

describe('Media Directory Structure Verification', () => {
    it('should have the correct directory structure after cleanup', async () => {
        // The media directory should now only contain:
        // - README.md
        // - capture/ (MediaCaptureManager)
        // - core/ (CameraManager)
        // - index.ts (main exports)
        // - streaming/ (minimal utilities)
        // - utils/ (consolidated utilities)

        // This test just verifies that the imports work correctly
        // The actual directory structure verification is done by the successful imports above

        const mediaModule = await import('@/media/index.ts');

        // Verify we have the expected exports without the removed modules
        expect(mediaModule.CameraManager).toBeDefined();
        expect(mediaModule.normalizeInput).toBeDefined();
        expect(mediaModule.blobToBase64).toBeDefined();
        expect(mediaModule.processWithClientSideChunking).toBeDefined();

        // Verify removed modules are not accidentally exported
        expect(mediaModule.MediaStreamManagerCompat).toBeUndefined();
        expect(mediaModule.MultimodalStreamingService).toBeUndefined();
        expect(mediaModule.StreamingMediaSampleManager).toBeUndefined();
        expect(mediaModule.MediaSampler).toBeUndefined();
        expect(mediaModule.StreamingMediaSampler).toBeUndefined();
    });
});
