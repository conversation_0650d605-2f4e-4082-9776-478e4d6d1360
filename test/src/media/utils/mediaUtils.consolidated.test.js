/**
 * Consolidated Media Utils Test Suite
 * Merges audioUtils.test.js, buffer-overflow-fix.test.js, and buffer-overflow-fix-verification.test.js
 * for better organization and maintainability
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';

// Import everything from new modality structure
import {
    processRealtimeAudio,
    validateAudioData,
    createFallbackBase64Audio,
    convertFloat32ToWav,
    detectAudioFormat,
    AudioFormat,
    DEFAULT_AUDIO_CONFIG,
    base64ToBlob,
    blobToBase64,
    checkAudioBuffer,
    createFallbackTone,
    createAudioFromBlob,
    playAudioWithPromise
} from '../../../../src/media/modality/audio.js';

describe('Media Utils - Consolidated Test Suite', () => {
    describe('Audio Utilities Core Functions', () => {
        describe('convertFloat32ToWav', () => {
            it('should convert small Float32Array to WAV correctly', () => {
                const samples = new Float32Array([0.1, 0.2, 0.3, 0.4]);
                const wavBlob = convertFloat32ToWav(samples, {
                    sampleRate: 16000,
                    numChannels: 1,
                    bitDepth: 16
                });

                expect(wavBlob).toBeInstanceOf(Blob);
                expect(wavBlob.type).toBe('audio/wav');
                expect(wavBlob.size).toBeGreaterThan(44); // WAV header is 44 bytes
            });

            it('should handle empty Float32Array without buffer overflow', () => {
                const samples = new Float32Array(0);
                const wavBlob = convertFloat32ToWav(samples, {
                    sampleRate: 22050,
                    numChannels: 1,
                    bitDepth: 16
                });

                expect(wavBlob).toBeInstanceOf(Blob);
                expect(wavBlob.type).toBe('audio/wav');
                expect(wavBlob.size).toBe(44); // Just the WAV header
            });

            it('should handle large Float32Array without buffer overflow', () => {
                // Create a large audio buffer that might cause overflow
                const samples = new Float32Array(48000); // 1 second at 48kHz
                for (let i = 0; i < samples.length; i++) {
                    samples[i] = Math.sin(2 * Math.PI * 440 * i / 48000) * 0.5; // 440Hz tone
                }

                const wavBlob = convertFloat32ToWav(samples, {
                    sampleRate: 48000,
                    numChannels: 1,
                    bitDepth: 16
                });

                expect(wavBlob).toBeInstanceOf(Blob);
                expect(wavBlob.type).toBe('audio/wav');
                expect(wavBlob.size).toBe(44 + samples.length * 2); // Header + 16-bit samples
            });

            it('should use default parameters when none provided', () => {
                const samples = new Float32Array([0.1, 0.2]);
                const wavBlob = convertFloat32ToWav(samples);

                expect(wavBlob).toBeInstanceOf(Blob);
                expect(wavBlob.type).toBe('audio/wav');
                // Default: 44.1kHz, 1 channel, 16-bit
                expect(wavBlob.size).toBe(44 + samples.length * 2);
            });

            it('should handle extreme audio values without clipping errors', () => {
                const samples = new Float32Array([-1.0, -0.5, 0.0, 0.5, 1.0]);

                expect(() => {
                    const wavBlob = convertFloat32ToWav(samples, {
                        sampleRate: 44100,
                        numChannels: 1,
                        bitDepth: 16
                    });
                    expect(wavBlob.size).toBe(44 + samples.length * 2);
                }).not.toThrow();
            });
        });

        describe('detectAudioFormat', () => {
            it('should detect WAV format from header', () => {
                const wavHeader = new Uint8Array([0x52, 0x49, 0x46, 0x46]); // 'RIFF'
                const format = detectAudioFormat(wavHeader);
                expect(format).toBe(AudioFormat.WAV);
            });

            it('should detect MP3 format from header', () => {
                const mp3Header = new Uint8Array([0xFF, 0xFB, 0x90, 0x00]); // MP3 frame sync
                const format = detectAudioFormat(mp3Header);
                expect(format).toBe(AudioFormat.MP3);
            });

            it('should return UNKNOWN for unrecognized format', () => {
                const unknownHeader = new Uint8Array([0x00, 0x00, 0x00, 0x00]);
                const format = detectAudioFormat(unknownHeader);
                expect(format).toBe(AudioFormat.UNKNOWN);
            });
        });
    });

    describe('Buffer Overflow Fix Integration Tests', () => {
        describe('Original Issue Reproduction & Fix Verification', () => {
            it('should handle the exact scenario that caused buffer overflow in AliyunBailianChatModel', () => {
                // This reproduces the scenario where incorrect parameter names were used
                // causing the buffer size calculation to be wrong

                // Simulate real-time audio chunks that might be received
                const realtimeAudioChunks = [
                    new Float32Array([0.001, 0.002]),           // Very small chunk
                    new Float32Array([]),                        // Empty chunk
                    new Float32Array([0.1, 0.2, 0.3, 0.4]),    // Normal chunk
                    new Float32Array(new Array(1024).fill(0.5)) // Larger chunk
                ];

                // Test each chunk with the corrected parameters
                realtimeAudioChunks.forEach((chunk, index) => {
                    expect(() => {
                        const wavBlob = convertFloat32ToWav(chunk, {
                            sampleRate: 24000,
                            numChannels: 1,  // Fixed: was 'channels'
                            bitDepth: 16     // Fixed: was 'format: pcm16'
                        });

                        expect(wavBlob).toBeInstanceOf(Blob);
                        expect(wavBlob.type).toBe('audio/wav');

                        // Verify the blob size is correct
                        const expectedSize = 44 + (chunk.length * 2); // WAV header + 16-bit samples
                        expect(wavBlob.size).toBe(expectedSize);

                        console.log(`✅ Chunk ${index}: ${chunk.length} samples -> ${wavBlob.size} bytes`);
                    }, `Failed on chunk ${index} with ${chunk.length} samples`).not.toThrow();
                });
            });

            it('should work with corrected parameters (the fix)', () => {
                const audioData = new Float32Array([0.1, 0.2, 0.3, 0.4]);

                expect(() => {
                    const wavBlob = convertFloat32ToWav(audioData, {
                        sampleRate: 24000,
                        numChannels: 1,    // Correct parameter name (was 'channels')
                        bitDepth: 16       // Correct parameter name (was 'format: pcm16')
                    });

                    expect(wavBlob).toBeInstanceOf(Blob);
                    expect(wavBlob.type).toBe('audio/wav');
                    expect(wavBlob.size).toBe(44 + audioData.length * 2); // WAV header + samples
                }).not.toThrow();
            });

            it('should gracefully handle incorrect parameters without crashing', () => {
                // This tests the robustness - even with wrong parameter names,
                // the function should use defaults and not crash
                const audioData = new Float32Array([0.1, 0.2, 0.3, 0.4]);

                expect(() => {
                    const wavBlob = convertFloat32ToWav(audioData, {
                        sampleRate: 24000,
                        channels: 1,       // Incorrect parameter name (ignored, uses default)
                        format: 'pcm16'    // Incorrect parameter name (ignored, uses default)
                    });

                    expect(wavBlob).toBeInstanceOf(Blob);
                    expect(wavBlob.type).toBe('audio/wav');
                    // Should use default numChannels=1 and bitDepth=16
                    expect(wavBlob.size).toBe(44 + audioData.length * 2);
                }).not.toThrow();
            });

            it('should handle concurrent audio conversions without issues', async () => {
                // Test concurrent conversions like what might happen during real-time streaming
                const promises = [];

                for (let i = 0; i < 10; i++) {
                    const samples = new Float32Array(Math.floor(Math.random() * 1000) + 1);
                    samples.fill(Math.sin(i * 0.1)); // Fill with some audio data

                    const promise = new Promise((resolve) => {
                        const wavBlob = convertFloat32ToWav(samples, {
                            sampleRate: 24000,
                            numChannels: 1,
                            bitDepth: 16
                        });

                        resolve({
                            index: i,
                            inputLength: samples.length,
                            outputSize: wavBlob.size,
                            expectedSize: 44 + samples.length * 2
                        });
                    });

                    promises.push(promise);
                }

                const results = await Promise.all(promises);

                // Verify all conversions completed successfully
                results.forEach((result, index) => {
                    expect(result.outputSize).toBe(result.expectedSize);
                    console.log(`✅ Concurrent test ${index}: ${result.inputLength} samples -> ${result.outputSize} bytes`);
                });
            });
        });

        describe('Edge Cases That Could Cause Buffer Overflow', () => {
            const edgeCases = [
                {
                    name: 'Single sample',
                    data: new Float32Array([0.5]),
                    expectedSize: 44 + 2
                },
                {
                    name: 'Very large buffer',
                    data: new Float32Array(96000), // 2 seconds at 48kHz
                    expectedSize: 44 + (96000 * 2)
                },
                {
                    name: 'Maximum safe integer samples',
                    data: new Float32Array(Math.min(Number.MAX_SAFE_INTEGER / 8, 1000000)),
                    expectedSize: 44 + (Math.min(Number.MAX_SAFE_INTEGER / 8, 1000000) * 2)
                }
            ];

            edgeCases.forEach(({ name, data, expectedSize }) => {
                it(`should handle ${name} without buffer overflow`, () => {
                    // Fill with test data for non-trivial cases
                    if (data.length > 1) {
                        for (let i = 0; i < data.length; i++) {
                            data[i] = Math.sin(2 * Math.PI * 440 * i / 44100) * 0.5;
                        }
                    }

                    expect(() => {
                        const wavBlob = convertFloat32ToWav(data, {
                            sampleRate: 44100,
                            numChannels: 1,
                            bitDepth: 16
                        });

                        expect(wavBlob).toBeInstanceOf(Blob);
                        expect(wavBlob.type).toBe('audio/wav');
                        expect(wavBlob.size).toBe(expectedSize);
                    }).not.toThrow();
                });
            });

            it('should handle malformed input gracefully', () => {
                const malformedInputs = [
                    null,
                    undefined,
                    {},
                    [],
                    'not an array',
                    new Int32Array([1, 2, 3]) // Wrong typed array
                ];

                malformedInputs.forEach((input, index) => {
                    expect(() => {
                        convertFloat32ToWav(input, DEFAULT_AUDIO_CONFIG);
                    }).toThrow();
                });
            });
        });
    });

    describe('Realtime Audio Processor Integration', () => {
        describe('processRealtimeAudio', () => {
            it('should process Float32Array audio data successfully', async () => {
                const audioData = new Float32Array([0.1, 0.2, 0.3, 0.4]);

                const result = await processRealtimeAudio(audioData, {
                    sampleRate: 24000,
                    numChannels: 1,
                    bitDepth: 16,
                    enableDebugLogging: true
                });

                expect(result.success).toBe(true);
                expect(result.base64Audio).toBeDefined();
                expect(typeof result.base64Audio).toBe('string');
                expect(result.base64Audio.length).toBeGreaterThan(0);
            });

            it('should process ArrayBuffer audio data successfully', async () => {
                const int16Data = new Int16Array([1000, 2000, 3000, 4000]);
                const audioData = int16Data.buffer;

                const result = await processRealtimeAudio(audioData, {
                    enableDebugLogging: true
                });

                expect(result.success).toBe(true);
                expect(result.base64Audio).toBeDefined();
            });

            it('should handle base64 string input as-is', async () => {
                const base64Input = 'dGVzdA=='; // 'test' in base64

                const result = await processRealtimeAudio(base64Input);

                expect(result.success).toBe(true);
                expect(result.base64Audio).toBe(base64Input);
            });

            it('should handle processing errors gracefully', async () => {
                const invalidInput = { not: 'audio' };

                const result = await processRealtimeAudio(invalidInput);

                expect(result.success).toBe(false);
                expect(result.error).toBeDefined();
                expect(typeof result.error).toBe('string');
            });
        });

        describe('validateAudioData', () => {
            it('should validate different audio data types correctly', () => {
                const testCases = [
                    { data: new Float32Array([1, 2, 3]), expected: true },
                    { data: new Int16Array([1, 2, 3]), expected: true },
                    { data: new ArrayBuffer(8), expected: true },
                    { data: 'base64string', expected: true },
                    { data: null, expected: false },
                    { data: undefined, expected: false },
                    { data: new Float32Array(0), expected: false }, // Empty array
                    { data: new ArrayBuffer(0), expected: false },   // Empty buffer
                    { data: '', expected: false },                   // Empty string
                    { data: {}, expected: false },                   // Object
                ];

                testCases.forEach(({ data, expected }) => {
                    const result = validateAudioData(data);
                    expect(result.isValid).toBe(expected);

                    if (expected) {
                        expect(result.type).toBeDefined();
                    } else {
                        expect(result.error).toBeDefined();
                    }
                });
            });
        });

        describe('createFallbackBase64Audio', () => {
            it('should create base64 from ArrayBuffer', () => {
                const arrayBuffer = new ArrayBuffer(4);
                const view = new Uint8Array(arrayBuffer);
                view[0] = 65; // 'A'
                view[1] = 66; // 'B'
                view[2] = 67; // 'C'
                view[3] = 68; // 'D'

                const result = createFallbackBase64Audio(arrayBuffer);
                expect(result).toBe('QUJDRA=='); // 'ABCD' in base64
            });

            it('should handle Uint8Array directly', () => {
                const uint8Array = new Uint8Array([65, 66, 67, 68]);
                const result = createFallbackBase64Audio(uint8Array);
                expect(result).toBe('QUJDRA=='); // 'ABCD' in base64
            });

            it('should return empty string on error', () => {
                const result = createFallbackBase64Audio(null);
                expect(result).toBe('');
            });
        });
    });

    describe('Stress Testing & Memory Management', () => {
        it('should handle memory-intensive operations without leaks', async () => {
            const iterations = 50;
            const results = [];

            for (let i = 0; i < iterations; i++) {
                const size = Math.floor(Math.random() * 10000) + 1000; // 1K to 11K samples
                const audioData = new Float32Array(size);

                // Fill with audio data
                for (let j = 0; j < size; j++) {
                    audioData[j] = Math.sin(2 * Math.PI * 440 * j / 44100) * 0.5;
                }

                const result = await processRealtimeAudio(audioData, {
                    sampleRate: 44100,
                    numChannels: 1,
                    bitDepth: 16
                });

                expect(result.success).toBe(true);
                results.push({
                    iteration: i,
                    inputSize: size,
                    outputLength: result.base64Audio.length
                });

                // Force garbage collection hint (if available)
                if (global.gc) {
                    global.gc();
                }
            }

            // Verify all operations completed successfully
            expect(results.length).toBe(iterations);
            results.forEach(result => {
                expect(result.outputLength).toBeGreaterThan(0);
            });

            console.log(`✅ Completed ${iterations} memory-intensive audio processing operations`);
        });

        it('should handle rapid sequential processing', async () => {
            const rapidProcessingCount = 100;
            const audioData = new Float32Array(1000);
            audioData.fill(0.5);

            const promises = [];
            for (let i = 0; i < rapidProcessingCount; i++) {
                promises.push(processRealtimeAudio(audioData.slice(), {
                    enableDebugLogging: false // Disable logging for performance
                }));
            }

            const results = await Promise.all(promises);

            // All should succeed
            results.forEach((result, index) => {
                expect(result.success).toBe(true);
                expect(result.base64Audio).toBeDefined();
            });

            console.log(`✅ Completed ${rapidProcessingCount} rapid sequential audio processing operations`);
        });
    });
});
