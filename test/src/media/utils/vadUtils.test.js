import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';

vi.mock('@ricky0123/vad-web', () => ({
    VoiceActivityDetector: vi.fn().mockImplementation((source, options) => {
        return {
            start: vi.fn(),
            stop: vi.fn(),
            options
        };
    })
}));

import { setupClientVAD } from '../../../../src/media/utils/vadUtils';
import { VoiceActivityDetector } from '@ricky0123/vad-web';

describe('vadUtils', () => {
    afterEach(() => {
        vi.clearAllMocks();
    });

    it('should create a VoiceActivityDetector with correct callbacks', () => {
        const audioSource = {};
        const onSpeechStart = vi.fn();
        const onSpeechEnd = vi.fn();
        const vad = setupClientVAD(audioSource, { onSpeechStart, onSpeechEnd });
        expect(VoiceActivityDetector).toHaveBeenCalledWith(audioSource, expect.objectContaining({ onSpeechStart, onSpeechEnd }));
        expect(typeof vad.start).toBe('function');
        expect(typeof vad.stop).toBe('function');
    });

    it('should work with no options', () => {
        const audioSource = {};
        const vad = setupClientVAD(audioSource);
        expect(VoiceActivityDetector).toHaveBeenCalledWith(audioSource, expect.any(Object));
        expect(typeof vad.start).toBe('function');
        expect(typeof vad.stop).toBe('function');
    });
}); 