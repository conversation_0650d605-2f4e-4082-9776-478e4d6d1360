/**
 * Streaming Audio Tests
 * Tests for real-time streaming audio playback following Qwen-Omni 方式2 pattern
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import {
    StreamingAudioProcessor,
    createStreamingAudioProcessor,
    processStreamingAudioChunk,
    AudioResponseProcessor,
    detectAudioFormatFromBase64,
    convertPCMToWAVBlob
} from '../../../src/media/modality/audio.ts';

// Mock logger
const mockLogger = {
    debug: vi.fn(),
    info: vi.fn(),
    warn: vi.fn(),
    error: vi.fn()
};

// Mock Web Audio API
const mockAudioContext = {
    createBuffer: vi.fn(),
    createBufferSource: vi.fn(),
    currentTime: 0,
    sampleRate: 24000,
    destination: {}
};

const mockAudioBuffer = {
    duration: 0.1,
    getChannelData: vi.fn(() => new Float32Array(1024))
};

const mockBufferSource = {
    buffer: null,
    connect: vi.fn(),
    start: vi.fn(),
    onended: null
};

// Global mocks
global.AudioContext = vi.fn(() => mockAudioContext);
global.webkitAudioContext = vi.fn(() => mockAudioContext);
global.window = { AudioContext: global.AudioContext };
global.atob = vi.fn();

describe('StreamingAudioProcessor', () => {
    let processor;

    beforeEach(() => {
        vi.clearAllMocks();

        // Reset mocks
        mockAudioContext.createBuffer.mockReturnValue(mockAudioBuffer);
        mockAudioContext.createBufferSource.mockReturnValue(mockBufferSource);
        mockAudioContext.currentTime = 0;

        processor = new StreamingAudioProcessor({
            sampleRate: 24000,
            channels: 1,
            bitDepth: 16,
            logger: mockLogger
        });
    });

    afterEach(() => {
        processor?.endStream();
    });

    describe('Constructor and Initialization', () => {
        it('should initialize with default Qwen-Omni parameters', () => {
            const defaultProcessor = new StreamingAudioProcessor();

            expect(defaultProcessor.getStatus()).toEqual({
                isPlaying: false,
                queueLength: 0
            });
        });

        it('should initialize with custom parameters', () => {
            const customProcessor = new StreamingAudioProcessor({
                sampleRate: 16000,
                channels: 2,
                bitDepth: 32,
                logger: mockLogger
            });

            expect(customProcessor.getStatus()).toEqual({
                isPlaying: false,
                queueLength: 0
            });
        });

        it('should initialize Web Audio API in browser environment', () => {
            expect(global.AudioContext).toHaveBeenCalled();
        });
    });

    describe('Real-time Streaming Processing', () => {
        it('should process a single PCM chunk', async () => {
            const base64PCM = 'AAAAAAAAAAAAAAAA'; // Mock PCM data
            global.atob.mockReturnValue('\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00');

            await processor.processStreamingChunk(base64PCM);

            expect(mockLogger.debug).toHaveBeenCalledWith(
                'Processing streaming PCM chunk:',
                expect.objectContaining({
                    chunkLength: base64PCM.length,
                    isPlaying: false
                })
            );
        });

        it('should handle multiple consecutive chunks (Qwen-Omni streaming pattern)', async () => {
            const chunks = [
                'AAAAAAAAAAAAAAAA',
                'BBBBBBBBBBBBBBBB',
                'CCCCCCCCCCCCCCCC'
            ];

            global.atob.mockImplementation((b64) => {
                return new Array(12).fill('\x00').join('');
            });

            for (const chunk of chunks) {
                await processor.processStreamingChunk(chunk);
            }

            expect(mockLogger.debug).toHaveBeenCalledTimes(chunks.length);
            expect(mockAudioContext.createBuffer).toHaveBeenCalledTimes(chunks.length);
        });

        it('should schedule audio buffers for seamless playback', async () => {
            const base64PCM = 'AAAAAAAAAAAAAAAA';
            global.atob.mockReturnValue('\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00');

            await processor.processStreamingChunk(base64PCM);

            expect(mockBufferSource.connect).toHaveBeenCalledWith(mockAudioContext.destination);
            expect(mockBufferSource.start).toHaveBeenCalled();
        });

        it('should handle empty or invalid chunks gracefully', async () => {
            await processor.processStreamingChunk('');
            await processor.processStreamingChunk(null);
            await processor.processStreamingChunk(undefined);

            expect(mockLogger.error).not.toHaveBeenCalled();
        });
    });

    describe('Streaming State Management', () => {
        it('should track playing status correctly', async () => {
            expect(processor.getStatus().isPlaying).toBe(false);

            const base64PCM = 'AAAAAAAAAAAAAAAA';
            global.atob.mockReturnValue('\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00');

            await processor.processStreamingChunk(base64PCM);

            // Simulate audio start
            const status = processor.getStatus();
            expect(status).toEqual({
                isPlaying: expect.any(Boolean),
                queueLength: expect.any(Number)
            });
        });

        it('should end streaming session properly', () => {
            processor.endStream();

            const status = processor.getStatus();
            expect(status.isPlaying).toBe(false);
            expect(status.queueLength).toBe(0);
            expect(mockLogger.debug).toHaveBeenCalledWith('Audio streaming ended');
        });
    });

    describe('Error Handling', () => {
        it('should handle Web Audio API errors gracefully', async () => {
            mockAudioContext.createBuffer.mockImplementation(() => {
                throw new Error('Web Audio error');
            });

            const base64PCM = 'AAAAAAAAAAAAAAAA';
            global.atob.mockReturnValue('\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00');

            await processor.processStreamingChunk(base64PCM);

            expect(mockLogger.error).toHaveBeenCalledWith(
                'Error playing web audio chunk:',
                expect.any(Error)
            );
        });

        it('should handle invalid base64 data', async () => {
            global.atob.mockImplementation(() => {
                throw new Error('Invalid base64');
            });

            await processor.processStreamingChunk('invalid-base64');

            expect(mockLogger.error).toHaveBeenCalledWith(
                'Error playing web audio chunk:',
                expect.any(Error)
            );
        });
    });
});

describe('Helper Functions', () => {
    describe('createStreamingAudioProcessor', () => {
        it('should create a StreamingAudioProcessor instance', () => {
            const processor = createStreamingAudioProcessor({
                sampleRate: 24000,
                channels: 1,
                bitDepth: 16,
                logger: mockLogger
            });

            expect(processor).toBeInstanceOf(StreamingAudioProcessor);
            expect(processor.getStatus()).toEqual({
                isPlaying: false,
                queueLength: 0
            });
        });

        it('should use default parameters', () => {
            const processor = createStreamingAudioProcessor();
            expect(processor).toBeInstanceOf(StreamingAudioProcessor);
        });
    });

    describe('processStreamingAudioChunk', () => {
        it('should process chunk through processor', async () => {
            const processor = createStreamingAudioProcessor({ logger: mockLogger });
            const base64PCM = 'AAAAAAAAAAAAAAAA';

            global.atob.mockReturnValue('\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00');

            await processStreamingAudioChunk(processor, base64PCM);

            expect(mockLogger.debug).toHaveBeenCalledWith(
                'Processing streaming PCM chunk:',
                expect.objectContaining({
                    chunkLength: base64PCM.length
                })
            );
        });
    });
});

describe('AudioResponseProcessor with Streaming', () => {
    let processor;

    beforeEach(() => {
        vi.clearAllMocks();
        processor = new AudioResponseProcessor({ logger: mockLogger });
    });

    afterEach(() => {
        processor?.endStreaming();
    });

    describe('Streaming Mode', () => {
        it('should enable streaming mode', () => {
            processor.enableStreaming({
                sampleRate: 24000,
                channels: 1,
                bitDepth: 16
            });

            const status = processor.getStreamingStatus();
            expect(status.isStreaming).toBe(true);
            expect(status.isPlaying).toBe(false);
            expect(mockLogger.info).toHaveBeenCalledWith('Streaming audio processing enabled');
        });

        it('should process streaming chunks when enabled', async () => {
            processor.enableStreaming();

            global.atob.mockReturnValue('\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00');

            await processor.processStreamingChunk('AAAAAAAAAAAAAAAA');

            expect(mockLogger.debug).toHaveBeenCalledWith(
                'Processing streaming PCM chunk:',
                expect.any(Object)
            );
        });

        it('should warn when streaming not enabled', async () => {
            await processor.processStreamingChunk('AAAAAAAAAAAAAAAA');

            expect(mockLogger.warn).toHaveBeenCalledWith(
                'Streaming not enabled. Call enableStreaming() first.'
            );
        });

        it('should end streaming session', () => {
            processor.enableStreaming();
            processor.endStreaming();

            const status = processor.getStreamingStatus();
            expect(status.isStreaming).toBe(false);
            expect(mockLogger.info).toHaveBeenCalledWith('Streaming audio session ended');
        });
    });
});

describe('Integration Tests - Qwen-Omni Pattern', () => {
    it('should simulate complete Qwen-Omni streaming workflow', async () => {
        // Simulate Qwen-Omni response structure
        const mockQwenResponse = {
            choices: [
                { delta: { audio: { data: 'AAAAAAAAAAAAAAAA' } } },
                { delta: { audio: { data: 'BBBBBBBBBBBBBBBB' } } },
                { delta: { audio: { data: 'CCCCCCCCCCCCCCCC' } } },
                { usage: { total_tokens: 100 } }
            ]
        };

        const processor = createStreamingAudioProcessor({
            sampleRate: 24000,
            channels: 1,
            bitDepth: 16,
            logger: mockLogger
        });

        global.atob.mockImplementation(() => new Array(12).fill('\x00').join(''));

        // Simulate the Qwen-Omni streaming pattern
        for (const choice of mockQwenResponse.choices) {
            if (choice.delta?.audio?.data) {
                await processStreamingAudioChunk(processor, choice.delta.audio.data);
            } else if (choice.usage) {
                // End of stream
                processor.endStream();
            }
        }

        expect(mockLogger.debug).toHaveBeenCalledWith('Audio streaming ended');
        expect(processor.getStatus().isPlaying).toBe(false);
    });

    it('should handle format detection for streaming chunks', () => {
        // Test PCM vs WAV detection
        const pcmChunk = 'AAAAAAAAAAAAAAAA'; // Raw PCM
        const wavChunk = 'UklGRqYwBgBXQVZFZm10'; // WAV header

        const pcmInfo = detectAudioFormatFromBase64(pcmChunk);
        const wavInfo = detectAudioFormatFromBase64(wavChunk);

        expect(pcmInfo.format).toBe('raw_pcm');
        expect(pcmInfo.needsHeaders).toBe(true);

        expect(wavInfo.format).toBe('complete_wav');
        expect(wavInfo.needsHeaders).toBe(false);
    });

    it('should convert PCM to WAV for non-streaming playback', () => {
        const base64PCM = 'AAAAAAAAAAAAAAAA';

        const wavBlob = convertPCMToWAVBlob(base64PCM, {
            sampleRate: 24000,
            numChannels: 1,
            bitDepth: 16
        });

        expect(wavBlob).toBeInstanceOf(Blob);
        expect(wavBlob.type).toBe('audio/wav');
        expect(wavBlob.size).toBeGreaterThan(44); // WAV header + data
    });
});

describe('Node.js Environment Compatibility', () => {
    beforeEach(() => {
        delete global.window;
        delete global.AudioContext;
    });

    afterEach(() => {
        global.window = { AudioContext: vi.fn() };
        global.AudioContext = vi.fn();
    });

    it('should handle Node.js environment gracefully', async () => {
        const processor = new StreamingAudioProcessor({ logger: mockLogger });

        await processor.processStreamingChunk('AAAAAAAAAAAAAAAA');

        expect(mockLogger.debug).toHaveBeenCalledWith(
            'Node.js environment - streaming audio would use node-speaker'
        );
    });
}); 