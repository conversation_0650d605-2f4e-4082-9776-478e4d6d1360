# Agent WebSocket & Multimodal Tests

This directory contains tests specifically for the agent system's WebSocket connectivity and multimodal capabilities.

## Files

### `websocket-multimodal.test.js`

Comprehensive test suite for agent WebSocket and multimodal functionality:

- **Direct Connection Test**: Validates direct WebSocket connection to <PERSON>yun Qwen-Omni Realtime API
- **Proxy Connection Test**: Tests WebSocket connection through the local proxy server
- **Multimodal Environment Test**: Validates environment setup, file structure, and utility functions
- **Multimodal Flow Test**: Tests the complete agent multimodal workflow: **audio → LLM → audio + text → tool calling**

## Usage

```bash
# From project root, run all agent WebSocket tests
node test/src/agent/websocket-multimodal.test.js

# Run specific test categories
node test/src/agent/websocket-multimodal.test.js direct      # Direct Aliyun connection
node test/src/agent/websocket-multimodal.test.js proxy       # Proxy connection
node test/src/agent/websocket-multimodal.test.js multimodal  # Environment validation
node test/src/agent/websocket-multimodal.test.js flow        # Multimodal flow validation
node test/src/agent/websocket-multimodal.test.js all         # All tests (default)
```

## Multimodal Flow Test Details

The **flow** test validates the complete agent multimodal workflow based on Qwen-Omni Realtime API:

### Audio Input Processing

- Validates `input_audio_buffer.append` events for streaming audio input
- Tests `input_audio_buffer.commit` for manual audio submission
- Verifies server VAD (Voice Activity Detection) capabilities

### LLM Processing

- Tests session configuration for multimodal support (`["text", "audio"]`)
- Validates audio transcription with `gummy-realtime-v1` model
- Verifies streaming response generation

### Multimodal Output

- **Audio Output**: Tests `response.audio.delta` streaming audio generation
- **Text Output**: Validates `response.text.delta` streaming text generation
- **Audio Transcription**: Tests `response.audio_transcript.delta` for audio-to-text

### Tool Calling Integration

- Validates tool configuration in session setup
- Tests tool calling event structures
- Verifies integration with agent's tool system

### Expected Event Flow

The test validates the complete event sequence:

1. `session.created` / `session.updated` - Session initialization
2. `input_audio_buffer.speech_started` - Audio input detection
3. `input_audio_buffer.speech_stopped` - End of audio input
4. `conversation.item.created` - User message creation
5. `response.created` - Start of agent response
6. `response.audio.delta` + `response.text.delta` - Streaming output
7. `response.done` - Response completion

## Prerequisites

1. **Environment Variables** (set in `.env`):
   - `VITE_DASHSCOPE_API_KEY`: Aliyun API key for WebSocket connections
   - `VITE_DOWNLOAD_SERVER_PORT`: Local server port (default: 2994)

2. **Server Running** (for proxy tests):

   ```bash
   npm run launch viewer dev
   ```

## Test Results

The test suite provides comprehensive feedback:

- ✅ Success/failure status for each test
- ⏱️ Execution timing
- 📊 Detailed error information and diagnostics
- 💡 Troubleshooting recommendations

### Expected Outcomes

- **Direct Connection**: Should succeed with valid API key and network access
- **Proxy Connection**: Should succeed when local server is running and properly configured
- **Multimodal Tests**: Should pass all 17 environment and infrastructure checks
- **Multimodal Flow**: Should pass all 6 workflow validation checks

## Troubleshooting

### Direct Connection Fails

- Verify `VITE_DASHSCOPE_API_KEY` is set and valid
- Check network connectivity to `dashscope.aliyuncs.com`
- Ensure API key has proper permissions for realtime API

### Proxy Connection Fails

- Start server with `npm run launch viewer dev`
- Verify server is running on the expected port
- Check server logs for proxy middleware errors
- Ensure API key is being passed correctly through proxy

### Multimodal Tests Fail

- Verify project structure is complete
- Check all required environment variables are set
- Ensure critical files and directories exist

### Multimodal Flow Tests Fail

- Check that session configuration includes required multimodal fields
- Verify support for both `["text", "audio"]` modalities
- Ensure tool calling configuration is valid
- Validate audio format settings (`pcm16` for input/output)
