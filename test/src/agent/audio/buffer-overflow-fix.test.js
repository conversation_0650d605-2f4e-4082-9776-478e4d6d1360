/**
 * Agent Audio Buffer Overflow Fix Tests
 * Tests the AudioWorklet buffer overflow fix and related audio processing scenarios
 * Integrated into the agent test suite for proper organization
 */

import { describe, it, expect, vi } from 'vitest';
import { convertFloat32ToWav } from '../../../../src/media/utils/audioUtils.js';

describe('Agent Audio Processing - Buffer Overflow Fix', () => {

    describe('AudioWorklet Integration', () => {
        it('should simulate AudioWorkletProcessor output correctly', () => {
            // AudioWorklet processes Float32 input and converts to Int16
            const inputFloat32 = new Float32Array([0.1, 0.2, -0.1, -0.2, 0.5, -0.5]);

            // AudioWorklet converts to Int16Array
            const pcm16Data = new Int16Array(inputFloat32.length);
            for (let i = 0; i < inputFloat32.length; i++) {
                const sample = Math.max(-1, Math.min(1, inputFloat32[i]));
                pcm16Data[i] = sample < 0 ? sample * 0x8000 : sample * 0x7FFF;
            }

            // AudioWorklet sends .buffer (ArrayBuffer)
            const sentBuffer = pcm16Data.buffer;

            expect(sentBuffer).toBeInstanceOf(ArrayBuffer);
            expect(sentBuffer.byteLength).toBe(12); // 6 samples * 2 bytes per sample
        });

        it('should handle ArrayBuffer input from AudioWorklet (the fix)', () => {
            // Simulate the exact scenario that was causing buffer overflow
            const audioData = new ArrayBuffer(8); // Simulate small buffer
            const int16View = new Int16Array(audioData);
            int16View[0] = 0x1000;
            int16View[1] = -0x1000;
            int16View[2] = 0x2000;
            int16View[3] = -0x2000;

            // Handle different input types (the fix)
            let float32Audio;
            if (audioData instanceof ArrayBuffer) {
                // Convert ArrayBuffer (from AudioWorklet) to Float32Array
                const int16Data = new Int16Array(audioData);
                float32Audio = new Float32Array(int16Data.length);
                for (let i = 0; i < int16Data.length; i++) {
                    // Convert Int16 back to Float32 (-1.0 to 1.0 range)
                    float32Audio[i] = int16Data[i] / (int16Data[i] < 0 ? 0x8000 : 0x7FFF);
                }
            }

            // Convert to WAV (this was failing before the fix)
            expect(() => {
                const wavBlob = convertFloat32ToWav(float32Audio, {
                    sampleRate: 24000,
                    numChannels: 1,
                    bitDepth: 16
                });

                expect(wavBlob).toBeInstanceOf(Blob);
                expect(wavBlob.type).toBe('audio/wav');
                expect(wavBlob.size).toBe(44 + float32Audio.length * 2);
            }).not.toThrow();
        });

        it('should process Float32Array input directly', () => {
            const audioData = new Float32Array([0.1, 0.2, -0.1, -0.2]);

            expect(() => {
                const wavBlob = convertFloat32ToWav(audioData, {
                    sampleRate: 24000,
                    numChannels: 1,
                    bitDepth: 16
                });

                expect(wavBlob).toBeInstanceOf(Blob);
                expect(wavBlob.type).toBe('audio/wav');
                expect(wavBlob.size).toBe(44 + audioData.length * 2);
            }).not.toThrow();
        });
    });

    describe('Buffer Overflow Prevention', () => {
        it('should handle corrected parameters without buffer overflow', () => {
            const audioData = new Float32Array([0.1, 0.2, 0.3, 0.4]);

            expect(() => {
                const wavBlob = convertFloat32ToWav(audioData, {
                    sampleRate: 24000,
                    numChannels: 1,    // Correct parameter name
                    bitDepth: 16       // Correct parameter name
                });

                expect(wavBlob).toBeInstanceOf(Blob);
                expect(wavBlob.type).toBe('audio/wav');
                expect(wavBlob.size).toBe(44 + audioData.length * 2);
            }).not.toThrow();
        });

        it('should handle edge cases without buffer overflow', () => {
            const edgeCases = [
                { name: 'Empty audio', data: new Float32Array([]) },
                { name: 'Single sample', data: new Float32Array([0.5]) },
                { name: 'Very small chunk', data: new Float32Array([0.001, 0.002]) },
                { name: 'Large buffer', data: new Float32Array(4096).fill(0.1) }
            ];

            edgeCases.forEach(testCase => {
                expect(() => {
                    const wavBlob = convertFloat32ToWav(testCase.data, {
                        sampleRate: 24000,
                        numChannels: 1,
                        bitDepth: 16
                    });

                    const expectedSize = 44 + testCase.data.length * 2;
                    expect(wavBlob.size).toBe(expectedSize);
                }, `${testCase.name} should not throw`).not.toThrow();
            });
        });

        it('should handle concurrent operations without race conditions', async () => {
            const promises = [];

            for (let i = 0; i < 5; i++) {
                const audioData = new Float32Array(100 + i * 10).fill(Math.sin(i));

                const promise = new Promise((resolve) => {
                    try {
                        const wavBlob = convertFloat32ToWav(audioData, {
                            sampleRate: 24000,
                            numChannels: 1,
                            bitDepth: 16
                        });

                        resolve({
                            index: i,
                            success: true,
                            size: wavBlob.size,
                            originalLength: audioData.length
                        });
                    } catch (error) {
                        resolve({
                            index: i,
                            success: false,
                            error: error.message
                        });
                    }
                });

                promises.push(promise);
            }

            const results = await Promise.all(promises);

            // All operations should succeed
            results.forEach(result => {
                expect(result.success).toBe(true);
                expect(result.size).toBe(44 + result.originalLength * 2);
            });
        });
    });

    describe('Production Scenario Reproduction', () => {
        it('should reproduce and fix the original AliyunBailianChatModel scenario', () => {
            // This reproduces the scenario where incorrect parameter names were used
            // causing the buffer size calculation to be wrong
            const realtimeAudioChunks = [
                new Float32Array([0.001, 0.002]),           // Very small chunk
                new Float32Array([]),                        // Empty chunk
                new Float32Array([0.1, 0.2, 0.3, 0.4]),    // Normal chunk
                new Float32Array(new Array(1024).fill(0.5)) // Larger chunk
            ];

            // Test each chunk with the corrected parameters
            realtimeAudioChunks.forEach((chunk, index) => {
                expect(() => {
                    const wavBlob = convertFloat32ToWav(chunk, {
                        sampleRate: 24000,
                        numChannels: 1,  // Fixed: was 'channels'
                        bitDepth: 16     // Fixed: was 'format: pcm16'
                    });

                    expect(wavBlob).toBeInstanceOf(Blob);
                    expect(wavBlob.type).toBe('audio/wav');
                }, `Chunk ${index} should process without errors`).not.toThrow();
            });
        });

        it('should handle malformed input gracefully', () => {
            const malformedInputs = [
                null,
                undefined,
                'invalid',
                {},
                []
            ];

            malformedInputs.forEach(input => {
                expect(() => {
                    convertFloat32ToWav(input, {
                        sampleRate: 24000,
                        numChannels: 1,
                        bitDepth: 16
                    });
                }).toThrow(); // Should throw an error for invalid input
            });
        });

        it('should process realistic WebRTC audio chunks', () => {
            // Simulate realistic WebRTC audio chunk sizes
            const typicalChunkSizes = [128, 256, 512, 1024, 2048];

            typicalChunkSizes.forEach(chunkSize => {
                const audioChunk = new Float32Array(chunkSize);
                // Fill with realistic audio data (sine wave)
                for (let i = 0; i < chunkSize; i++) {
                    audioChunk[i] = Math.sin(2 * Math.PI * 440 * i / 48000) * 0.5;
                }

                expect(() => {
                    const wavBlob = convertFloat32ToWav(audioChunk, {
                        sampleRate: 48000, // Common WebRTC sample rate
                        numChannels: 1,
                        bitDepth: 16
                    });

                    expect(wavBlob.size).toBe(44 + chunkSize * 2);
                }, `Chunk size ${chunkSize} should process correctly`).not.toThrow();
            });
        });
    });

    describe('Memory Management', () => {
        it('should handle memory-intensive operations without leaks', () => {
            // Test with large audio buffers that could cause memory issues
            const largeBufferSizes = [8192, 16384, 32768];

            largeBufferSizes.forEach(size => {
                const largeAudioBuffer = new Float32Array(size).fill(0.1);

                expect(() => {
                    const wavBlob = convertFloat32ToWav(largeAudioBuffer, {
                        sampleRate: 44100,
                        numChannels: 1,
                        bitDepth: 16
                    });

                    expect(wavBlob.size).toBe(44 + size * 2);
                    // Blob should be properly created without memory issues
                    expect(wavBlob).toBeInstanceOf(Blob);
                }, `Large buffer size ${size} should not cause memory issues`).not.toThrow();
            });
        });

        it('should handle rapid sequential processing', () => {
            // Simulate rapid audio processing that could overwhelm the system
            const iterations = 50;
            const audioData = new Float32Array(512).fill(0.2);

            for (let i = 0; i < iterations; i++) {
                expect(() => {
                    const wavBlob = convertFloat32ToWav(audioData, {
                        sampleRate: 16000,
                        numChannels: 1,
                        bitDepth: 16
                    });

                    expect(wavBlob.size).toBe(44 + audioData.length * 2);
                }, `Iteration ${i} should complete successfully`).not.toThrow();
            }
        });
    });
});
