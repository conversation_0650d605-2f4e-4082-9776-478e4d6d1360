#!/usr/bin/env node

/**
 * Test Runner for Agent Issue Resolution
 * Runs all tests related to VAD cleanup and multi-modal support issues
 */

import { spawn } from 'child_process';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Test configurations
const TEST_CONFIGS = {
    vadCleanup: {
        name: 'VAD Cleanup & Resource Management',
        file: 'vad-cleanup.test.js',
        description: 'Tests for VAD not stopping and stack overflow issues'
    },
    multiModalQwen: {
        name: 'Multi-Modal Qwen-Omni API',
        file: 'multimodal-qwen.test.js',
        description: 'Tests for new multi-modal API format support'
    },
    voiceReply: {
        name: 'Voice Reply Generation',
        file: 'voice-reply.test.js',
        description: 'Tests for audio output generation from LLM API'
    },
    existing: {
        name: 'Existing Agent Tests',
        files: [
            'core/index.test.js',
            'core/tools.test.js',
            'stream/langchain-streaming.test.js'
        ],
        description: 'Existing agent test suites'
    }
};

class TestRunner {
    constructor() {
        this.results = {
            passed: 0,
            failed: 0,
            total: 0,
            details: []
        };
        this.startTime = Date.now();
    }

    async runTest(testFile, testName) {
        console.log(`\n🧪 Running ${testName}...`);
        console.log(`📁 File: ${testFile}`);
        console.log('-'.repeat(80));

        return new Promise((resolve) => {
            const testPath = path.join(__dirname, testFile);
            const child = spawn('npx', ['vitest', 'run', testPath, '--reporter=verbose'], {
                stdio: ['pipe', 'pipe', 'pipe'],
                shell: true
            });

            let stdout = '';
            let stderr = '';

            child.stdout.on('data', (data) => {
                stdout += data.toString();
                process.stdout.write(data);
            });

            child.stderr.on('data', (data) => {
                stderr += data.toString();
                process.stderr.write(data);
            });

            child.on('close', (code) => {
                const success = code === 0;
                const result = {
                    name: testName,
                    file: testFile,
                    success,
                    code,
                    stdout,
                    stderr,
                    duration: Date.now() - this.startTime
                };

                this.results.total++;
                if (success) {
                    this.results.passed++;
                    console.log(`✅ ${testName} PASSED`);
                } else {
                    this.results.failed++;
                    console.log(`❌ ${testName} FAILED (exit code: ${code})`);
                }

                this.results.details.push(result);
                resolve(result);
            });
        });
    }

    async runAllTests() {
        console.log('🚀 Starting Agent Issue Resolution Tests');
        console.log('='.repeat(80));

        // Run VAD cleanup tests
        await this.runTest(
            TEST_CONFIGS.vadCleanup.file,
            TEST_CONFIGS.vadCleanup.name
        );

        // Run multi-modal tests
        await this.runTest(
            TEST_CONFIGS.multiModalQwen.file,
            TEST_CONFIGS.multiModalQwen.name
        );

        // Run voice reply tests
        await this.runTest(
            TEST_CONFIGS.voiceReply.file,
            TEST_CONFIGS.voiceReply.name
        );

        // Run existing tests to ensure no regressions
        console.log('\n📋 Running existing tests to check for regressions...');
        for (const file of TEST_CONFIGS.existing.files) {
            try {
                await this.runTest(file, `Existing: ${file}`);
            } catch (error) {
                console.log(`⚠️  Could not run ${file}: ${error.message}`);
            }
        }

        this.generateReport();
    }

    generateReport() {
        const duration = Date.now() - this.startTime;

        console.log('\n' + '='.repeat(80));
        console.log('📊 TEST SUMMARY REPORT');
        console.log('='.repeat(80));

        console.log(`⏱️  Total Duration: ${(duration / 1000).toFixed(2)}s`);
        console.log(`✅ Passed: ${this.results.passed}`);
        console.log(`❌ Failed: ${this.results.failed}`);
        console.log(`📈 Total: ${this.results.total}`);
        console.log(`📊 Success Rate: ${((this.results.passed / this.results.total) * 100).toFixed(1)}%`);

        console.log('\n📋 DETAILED RESULTS:');
        console.log('-'.repeat(80));

        this.results.details.forEach(result => {
            const status = result.success ? '✅ PASS' : '❌ FAIL';
            console.log(`${status} ${result.name}`);
            console.log(`   📁 ${result.file}`);
            if (!result.success) {
                console.log(`   ⚠️  Exit code: ${result.code}`);
                if (result.stderr) {
                    console.log(`   🔍 Error: ${result.stderr.split('\n')[0]}`);
                }
            }
        });

        // Issue-specific recommendations
        console.log('\n🎯 ISSUE RESOLUTION STATUS:');
        console.log('-'.repeat(80));

        const vadTest = this.results.details.find(r => r.name.includes('VAD Cleanup'));
        const multiModalTest = this.results.details.find(r => r.name.includes('Multi-Modal'));
        const voiceReplyTest = this.results.details.find(r => r.name.includes('Voice Reply'));

        console.log('\n1️⃣ VAD Cleanup & Stack Overflow Issues:');
        if (vadTest?.success) {
            console.log('   ✅ Tests pass - Ready to implement fixes');
            console.log('   💡 Next: Apply VAD cleanup patterns to main code');
        } else {
            console.log('   ❌ Tests need fixing first');
            console.log('   💡 Review test mocks and assertions');
        }

        console.log('\n2️⃣ Multi-Modal API Support:');
        if (multiModalTest?.success) {
            console.log('   ✅ Tests pass - Ready to implement multi-modal support');
            console.log('   💡 Next: Update AliyunBailianChatModel message formatting');
        } else {
            console.log('   ❌ Tests need fixing first');
            console.log('   💡 Review multi-modal message structure');
        }

        console.log('\n3️⃣ Voice Reply Generation:');
        if (voiceReplyTest?.success) {
            console.log('   ✅ Tests pass - Voice replies working correctly');
            console.log('   💡 Audio output is being generated successfully');
        } else {
            console.log('   ❌ Voice reply tests failing');
            console.log('   💡 Check audio modality configuration and response handling');
        }

        // Implementation readiness
        if (this.results.failed === 0) {
            console.log('\n🎉 ALL TESTS PASS - READY FOR IMPLEMENTATION!');
            console.log('📋 Implementation Order:');
            console.log('   1. Implement VAD cleanup fixes');
            console.log('   2. Add multi-modal message support');
            console.log('   3. Verify voice reply audio generation');
            console.log('   4. Update UI to handle multiple modalities');
            console.log('   5. Test with real API calls');
        } else {
            console.log('\n⚠️  TESTS NEED ATTENTION BEFORE IMPLEMENTATION');
            console.log('📋 Fix failed tests first, then proceed with implementation');
        }

        return this.results;
    }

    async runSpecificTest(testType) {
        console.log(`🎯 Running specific test: ${testType}`);

        if (testType === 'vad' && TEST_CONFIGS.vadCleanup) {
            await this.runTest(
                TEST_CONFIGS.vadCleanup.file,
                TEST_CONFIGS.vadCleanup.name
            );
        } else if (testType === 'multimodal' && TEST_CONFIGS.multiModalQwen) {
            await this.runTest(
                TEST_CONFIGS.multiModalQwen.file,
                TEST_CONFIGS.multiModalQwen.name
            );
        } else if (testType === 'voice' && TEST_CONFIGS.voiceReply) {
            await this.runTest(
                TEST_CONFIGS.voiceReply.file,
                TEST_CONFIGS.voiceReply.name
            );
        } else {
            console.log(`❌ Unknown test type: ${testType}`);
            console.log('Available types: vad, multimodal, voice');
            return;
        }

        this.generateReport();
    }
}

// CLI interface
async function main() {
    const args = process.argv.slice(2);
    const testType = args[0];

    const runner = new TestRunner();

    if (testType && testType !== 'all') {
        await runner.runSpecificTest(testType);
    } else {
        await runner.runAllTests();
    }

    // Exit with appropriate code
    process.exit(runner.results.failed > 0 ? 1 : 0);
}

// Handle uncaught errors
process.on('uncaughtException', (error) => {
    console.error('❌ Uncaught Exception:', error);
    process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
    console.error('❌ Unhandled Rejection at:', promise, 'reason:', reason);
    process.exit(1);
});

// Export for use as module
export { TestRunner, TEST_CONFIGS };

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
    main().catch(console.error);
}