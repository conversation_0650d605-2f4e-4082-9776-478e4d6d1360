/**
 * Multimodal Audio TDD Tests for Qwen-Omni
 * Test-driven development for audio input, audio+text output, and tool calling
 * Following the Aliyun Qwen-Omni documentation requirements
 */

import { describe, it, expect, beforeAll, afterAll, beforeEach, vi } from 'vitest';
import fetch from 'node-fetch';

// Test configuration
const TEST_CONFIG = {
    apiBaseUrl: process.env.API_BASE_URL || 'http://localhost:3020',
    timeout: 45000, // 45 seconds for complex multimodal operations
    useRealAPI: process.env.TEST_REAL_API === 'true'
};

// Sample audio data (Base64 encoded WAV header - valid but minimal)
const SAMPLE_AUDIO_BASE64 = 'UklGRiQEAABXQVZFZm10IBAAAAABAAEARKwAAIhYAQACABAAZGF0YQAEAAD//f7/AgAA//8AAAEA//8AAP//AAD//wAA//8AAP/+';

// Sample image data (1x1 pixel PNG)
const SAMPLE_IMAGE_BASE64 = 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAFm6h';

describe('Multimodal Audio TDD Tests', () => {
    beforeAll(async () => {
        if (!TEST_CONFIG.useRealAPI) {
            console.warn('⚠️ Multimodal audio tests require TEST_REAL_API=true to run against live API');
            return;
        }

        // Wait for server to be ready
        await new Promise(resolve => setTimeout(resolve, 2000));
    });

    describe('Audio Input Handling Tests (TDD)', () => {
        it('should handle audio input with base64 data in content array', async () => {
            if (!TEST_CONFIG.useRealAPI) {
                console.warn('Skipping real API test - set TEST_REAL_API=true to enable');
                return;
            }

            const response = await fetch(`${TEST_CONFIG.apiBaseUrl}/api/llm`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    provider: 'aliyun',
                    model: 'qwen-omni-turbo',
                    modalities: ['text', 'audio'],
                    audioConfig: { voice: 'Ethan', format: 'wav' },
                    messages: [{
                        role: 'user',
                        content: [
                            {
                                type: 'input_audio',
                                input_audio: {
                                    data: SAMPLE_AUDIO_BASE64,
                                    format: 'wav'
                                }
                            },
                            {
                                type: 'text',
                                text: 'Please process this audio and respond with both text and audio'
                            }
                        ]
                    }]
                })
            });

            expect(response.ok).toBe(true);
            const responseData = await response.json();

            // Should return both content and audio
            expect(responseData).toHaveProperty('content');
            expect(responseData).toHaveProperty('audio');
            expect(responseData).toHaveProperty('metadata');

            // Verify text content exists and mentions audio processing
            expect(typeof responseData.content).toBe('string');
            expect(responseData.content.length).toBeGreaterThan(0);

            // Verify audio content exists and is substantial
            expect(responseData.audio).toBeTruthy();
            expect(typeof responseData.audio).toBe('string');
            expect(responseData.audio.length).toBeGreaterThan(1000);

            // Verify metadata
            expect(responseData.metadata.provider).toBe('aliyun');
            expect(responseData.metadata.modalities).toContain('audio');
            expect(responseData.metadata.audioConfig.voice).toBe('Ethan');

            console.log('✅ Audio input processed successfully');
            console.log(`   Content length: ${responseData.content.length}`);
            console.log(`   Audio length: ${responseData.audio.length}`);
        }, TEST_CONFIG.timeout);

        it('should handle audio input with URL format', async () => {
            if (!TEST_CONFIG.useRealAPI) {
                console.warn('Skipping real API test - set TEST_REAL_API=true to enable');
                return;
            }

            // Using a public audio URL for testing
            const audioUrl = 'https://help-static-aliyun-doc.aliyuncs.com/file-manage-files/zh-CN/20250211/tixcef/cherry.wav';

            const response = await fetch(`${TEST_CONFIG.apiBaseUrl}/api/llm`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    provider: 'aliyun',
                    model: 'qwen-omni-turbo',
                    modalities: ['text', 'audio'],
                    audioConfig: { voice: 'Chelsie', format: 'wav' },
                    messages: [{
                        role: 'user',
                        content: [
                            {
                                type: 'input_audio',
                                input_audio: {
                                    data: audioUrl,
                                    format: 'wav'
                                }
                            },
                            {
                                type: 'text',
                                text: 'What is said in this audio?'
                            }
                        ]
                    }]
                })
            });

            expect(response.ok).toBe(true);
            const responseData = await response.json();

            // Should generate both text and audio response
            expect(responseData.content).toBeTruthy();
            expect(responseData.audio).toBeTruthy();
            expect(responseData.audio.length).toBeGreaterThan(1000);

            console.log('✅ Audio URL input processed successfully');
        }, TEST_CONFIG.timeout);

        it('should exclude system message when modalities include audio (per Qwen docs)', async () => {
            if (!TEST_CONFIG.useRealAPI) {
                console.warn('Skipping real API test - set TEST_REAL_API=true to enable');
                return;
            }

            const response = await fetch(`${TEST_CONFIG.apiBaseUrl}/api/llm`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    provider: 'aliyun',
                    model: 'qwen-omni-turbo',
                    modalities: ['text', 'audio'], // Audio modality should exclude system message
                    audioConfig: { voice: 'Ethan', format: 'wav' },
                    messages: [
                        {
                            role: 'system',
                            content: 'You are a helpful assistant. This system message should be ignored when audio is included.'
                        },
                        {
                            role: 'user',
                            content: 'Hello, please respond with audio'
                        }
                    ]
                })
            });

            expect(response.ok).toBe(true);
            const responseData = await response.json();

            // Should generate both text and audio despite system message
            expect(responseData.content).toBeTruthy();
            expect(responseData.audio).toBeTruthy();
            expect(responseData.audio.length).toBeGreaterThan(1000);

            // Response should be natural, not influenced by system message
            // (The AI should identify itself as Qwen, not follow system prompt role)
            const content = responseData.content.toLowerCase();

            console.log('✅ System message exclusion test passed');
            console.log(`   Generated content: ${responseData.content.substring(0, 100)}...`);
        }, TEST_CONFIG.timeout);
    });

    describe('Audio + Text Output Tests (TDD)', () => {
        it('should generate both text and audio for text-only input', async () => {
            if (!TEST_CONFIG.useRealAPI) {
                console.warn('Skipping real API test - set TEST_REAL_API=true to enable');
                return;
            }

            const response = await fetch(`${TEST_CONFIG.apiBaseUrl}/api/llm`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    provider: 'aliyun',
                    model: 'qwen-omni-turbo',
                    modalities: ['text', 'audio'],
                    audioConfig: { voice: 'Chelsie', format: 'wav' },
                    messages: [{
                        role: 'user',
                        content: 'Tell me a short joke and include it in both text and audio'
                    }]
                })
            });

            expect(response.ok).toBe(true);
            const responseData = await response.json();

            // Verify dual output
            expect(responseData).toHaveProperty('content');
            expect(responseData).toHaveProperty('audio');

            // Both should be substantial
            expect(typeof responseData.content).toBe('string');
            expect(responseData.content.length).toBeGreaterThan(10);
            expect(typeof responseData.audio).toBe('string');
            expect(responseData.audio.length).toBeGreaterThan(1000);

            // Audio config should be respected
            expect(responseData.metadata.audioConfig.voice).toBe('Chelsie');
            expect(responseData.metadata.audioConfig.format).toBe('wav');

            console.log('✅ Dual text+audio output generated successfully');
            console.log(`   Text: ${responseData.content.substring(0, 100)}...`);
            console.log(`   Audio size: ${responseData.audio.length} characters`);
        }, TEST_CONFIG.timeout);

        it('should respect different voice configurations', async () => {
            if (!TEST_CONFIG.useRealAPI) {
                console.warn('Skipping real API test - set TEST_REAL_API=true to enable');
                return;
            }

            const voices = ['Ethan', 'Chelsie'];
            const results = [];

            for (const voice of voices) {
                const response = await fetch(`${TEST_CONFIG.apiBaseUrl}/api/llm`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        provider: 'aliyun',
                        model: 'qwen-omni-turbo',
                        modalities: ['text', 'audio'],
                        audioConfig: { voice, format: 'wav' },
                        messages: [{
                            role: 'user',
                            content: `Say "Hello, I am ${voice}" with clear pronunciation`
                        }]
                    })
                });

                expect(response.ok).toBe(true);
                const responseData = await response.json();

                // Should generate audio for each voice
                expect(responseData.audio).toBeTruthy();
                expect(responseData.audio.length).toBeGreaterThan(1000);
                expect(responseData.metadata.audioConfig.voice).toBe(voice);

                results.push({
                    voice,
                    audioLength: responseData.audio.length,
                    content: responseData.content
                });

                // Small delay between requests
                await new Promise(resolve => setTimeout(resolve, 1000));
            }

            console.log('✅ Voice configuration test passed');
            results.forEach(result => {
                console.log(`   ${result.voice}: ${result.audioLength} chars audio, "${result.content.substring(0, 50)}..."`);
            });
        }, TEST_CONFIG.timeout * 2);

        it('should handle streaming properly and concatenate audio chunks', async () => {
            if (!TEST_CONFIG.useRealAPI) {
                console.warn('Skipping real API test - set TEST_REAL_API=true to enable');
                return;
            }

            const response = await fetch(`${TEST_CONFIG.apiBaseUrl}/api/llm`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    provider: 'aliyun',
                    model: 'qwen-omni-turbo',
                    modalities: ['text', 'audio'],
                    audioConfig: { voice: 'Ethan', format: 'wav' },
                    messages: [{
                        role: 'user',
                        content: 'Count from one to five slowly, making sure each number is clearly pronounced'
                    }]
                })
            });

            expect(response.ok).toBe(true);
            const responseData = await response.json();

            // Should have substantial audio for counting
            expect(responseData.audio).toBeTruthy();
            expect(responseData.audio.length).toBeGreaterThan(2000); // Longer audio for counting

            // Content should mention counting
            const content = responseData.content.toLowerCase();
            const hasNumbers = ['one', 'two', 'three', 'four', 'five', '1', '2', '3', '4', '5']
                .some(num => content.includes(num));
            expect(hasNumbers).toBe(true);

            console.log('✅ Streaming audio concatenation test passed');
            console.log(`   Audio length: ${responseData.audio.length} characters`);
            console.log(`   Content: ${responseData.content}`);
        }, TEST_CONFIG.timeout);
    });

    describe('Multimodal Input Combinations (TDD)', () => {
        it('should handle audio + image + text input (Note: Qwen docs say only one non-text modality per message)', async () => {
            if (!TEST_CONFIG.useRealAPI) {
                console.warn('Skipping real API test - set TEST_REAL_API=true to enable');
                return;
            }

            // According to Qwen docs: "无法在一个 User Message中输入多种非文本模态的数据"
            // So this test should either handle gracefully or we should split into separate messages

            const response = await fetch(`${TEST_CONFIG.apiBaseUrl}/api/llm`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    provider: 'aliyun',
                    model: 'qwen-omni-turbo',
                    modalities: ['text', 'audio'],
                    audioConfig: { voice: 'Chelsie', format: 'wav' },
                    messages: [
                        {
                            role: 'user',
                            content: [
                                {
                                    type: 'image_url',
                                    image_url: { url: `data:image/png;base64,${SAMPLE_IMAGE_BASE64}` }
                                },
                                {
                                    type: 'text',
                                    text: 'Describe this image'
                                }
                            ]
                        },
                        {
                            role: 'user',
                            content: [
                                {
                                    type: 'input_audio',
                                    input_audio: {
                                        data: SAMPLE_AUDIO_BASE64,
                                        format: 'wav'
                                    }
                                },
                                {
                                    type: 'text',
                                    text: 'And process this audio as well'
                                }
                            ]
                        }
                    ]
                })
            });

            expect(response.ok).toBe(true);
            const responseData = await response.json();

            // Should handle multiple modalities across separate messages
            expect(responseData.content).toBeTruthy();
            expect(responseData.audio).toBeTruthy();

            console.log('✅ Multi-modal input test passed (separate messages)');
        }, TEST_CONFIG.timeout);

        it('should properly format video + text input according to Qwen docs', async () => {
            if (!TEST_CONFIG.useRealAPI) {
                console.warn('Skipping real API test - set TEST_REAL_API=true to enable');
                return;
            }

            const videoUrl = 'https://help-static-aliyun-doc.aliyuncs.com/file-manage-files/zh-CN/20241115/cqqkru/1.mp4';

            const response = await fetch(`${TEST_CONFIG.apiBaseUrl}/api/llm`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    provider: 'aliyun',
                    model: 'qwen-omni-turbo',
                    modalities: ['text', 'audio'],
                    audioConfig: { voice: 'Ethan', format: 'wav' },
                    messages: [{
                        role: 'user',
                        content: [
                            {
                                type: 'video_url',
                                video_url: { url: videoUrl }
                            },
                            {
                                type: 'text',
                                text: 'Describe what happens in this video'
                            }
                        ]
                    }]
                })
            });

            expect(response.ok).toBe(true);
            const responseData = await response.json();

            // Should process video and return audio+text
            expect(responseData.content).toBeTruthy();
            expect(responseData.audio).toBeTruthy();

            console.log('✅ Video input processing test passed');
        }, TEST_CONFIG.timeout);
    });

    describe('Error Handling and Edge Cases (TDD)', () => {
        it('should handle invalid audio format gracefully', async () => {
            if (!TEST_CONFIG.useRealAPI) {
                console.warn('Skipping real API test - set TEST_REAL_API=true to enable');
                return;
            }

            const response = await fetch(`${TEST_CONFIG.apiBaseUrl}/api/llm`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    provider: 'aliyun',
                    model: 'qwen-omni-turbo',
                    modalities: ['text', 'audio'],
                    audioConfig: { voice: 'InvalidVoice', format: 'wav' },
                    messages: [{
                        role: 'user',
                        content: 'Test with invalid voice configuration'
                    }]
                })
            });

            // Should either work with fallback or return clear error
            if (!response.ok) {
                const errorData = await response.json();
                expect(errorData.error).toBeDefined();
            } else {
                const responseData = await response.json();
                // Should fallback to valid voice
                expect(responseData.audio).toBeTruthy();
            }

            console.log('✅ Invalid audio format handling test passed');
        }, TEST_CONFIG.timeout);

        it('should handle empty audio input gracefully', async () => {
            if (!TEST_CONFIG.useRealAPI) {
                console.warn('Skipping real API test - set TEST_REAL_API=true to enable');
                return;
            }

            const response = await fetch(`${TEST_CONFIG.apiBaseUrl}/api/llm`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    provider: 'aliyun',
                    model: 'qwen-omni-turbo',
                    modalities: ['text', 'audio'],
                    audioConfig: { voice: 'Chelsie', format: 'wav' },
                    messages: [{
                        role: 'user',
                        content: [
                            {
                                type: 'input_audio',
                                input_audio: {
                                    data: '', // Empty audio data
                                    format: 'wav'
                                }
                            },
                            {
                                type: 'text',
                                text: 'Please respond despite the empty audio'
                            }
                        ]
                    }]
                })
            });

            expect(response.ok).toBe(true);
            const responseData = await response.json();

            // Should handle gracefully and still generate response
            expect(responseData.content).toBeTruthy();
            expect(responseData.audio).toBeTruthy();

            console.log('✅ Empty audio input handling test passed');
        }, TEST_CONFIG.timeout);

        it('should validate OpenAI-compatible API parameters are correctly set', async () => {
            if (!TEST_CONFIG.useRealAPI) {
                console.warn('Skipping real API test - set TEST_REAL_API=true to enable');
                return;
            }

            const response = await fetch(`${TEST_CONFIG.apiBaseUrl}/api/llm`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    provider: 'aliyun',
                    model: 'qwen-omni-turbo',
                    modalities: ['text', 'audio'],
                    audioConfig: { voice: 'Ethan', format: 'wav' },
                    messages: [{
                        role: 'user',
                        content: 'Test OpenAI-compatible parameters: stream=true, modalities, audio config'
                    }]
                })
            });

            expect(response.ok).toBe(true);
            const responseData = await response.json();

            // Verify the response contains all expected fields for OpenAI compatibility
            expect(responseData).toHaveProperty('content');
            expect(responseData).toHaveProperty('audio');
            expect(responseData).toHaveProperty('metadata');

            // Check metadata for proper API configuration
            expect(responseData.metadata.modalities).toEqual(['text', 'audio']);
            expect(responseData.metadata.audioConfig).toEqual({ voice: 'Ethan', format: 'wav' });

            console.log('✅ OpenAI-compatible API parameters validation passed');
            console.log(`   Modalities: ${JSON.stringify(responseData.metadata.modalities)}`);
            console.log(`   Audio config: ${JSON.stringify(responseData.metadata.audioConfig)}`);
        }, TEST_CONFIG.timeout);
    });

    describe('Performance and Integration Tests (TDD)', () => {
        it('should handle concurrent audio+text requests efficiently', async () => {
            if (!TEST_CONFIG.useRealAPI) {
                console.warn('Skipping real API test - set TEST_REAL_API=true to enable');
                return;
            }

            const requests = [];
            const voices = ['Ethan', 'Chelsie'];

            for (let i = 0; i < 3; i++) {
                const voice = voices[i % voices.length];
                requests.push(
                    fetch(`${TEST_CONFIG.apiBaseUrl}/api/llm`, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({
                            provider: 'aliyun',
                            model: 'qwen-omni-turbo',
                            modalities: ['text', 'audio'],
                            audioConfig: { voice, format: 'wav' },
                            messages: [{
                                role: 'user',
                                content: `Concurrent test ${i + 1} with ${voice} voice`
                            }]
                        })
                    })
                );
            }

            const responses = await Promise.all(requests);
            const results = await Promise.all(responses.map(r => r.json()));

            // All should succeed
            responses.forEach(response => {
                expect(response.ok).toBe(true);
            });

            // All should have audio and text
            results.forEach((result, index) => {
                expect(result.content).toBeTruthy();
                expect(result.audio).toBeTruthy();
                expect(result.audio.length).toBeGreaterThan(1000);

                console.log(`   Request ${index + 1}: ${result.content.substring(0, 50)}... (${result.audio.length} chars audio)`);
            });

            console.log('✅ Concurrent requests test passed');
        }, TEST_CONFIG.timeout * 2);
    });
}); 