/**
 * Integration Test: LLM API Service End-to-End Flow
 * 
 * This test verifies that all LLM calls in the system now go through the server /llm route
 * instead of running models directly in the browser.
 */

import { describe, test, expect, beforeEach, afterEach, vi } from 'vitest';
import { llmAPI } from '../../../../src/media/api/llmAPI.ts';

// Mock environment
const mockEnv = {
    VITE_DASHSCOPE_API_KEY: 'test-api-key',
    VITE_MODEL_PROVIDER: 'aliyun',
    VITE_ALIYUN_MODEL: 'qwen-omni-turbo'
};

// Mock global environment
Object.keys(mockEnv).forEach(key => {
    process.env[key] = mockEnv[key];
});

describe('LLM API Service Integration Tests', () => {
    let mockFetch;
    let originalFetch;

    beforeEach(() => {
        // Setup mock fetch
        mockFetch = vi.fn(() =>
            Promise.resolve({
                ok: true,
                json: () => Promise.resolve({
                    content: 'Hello! I am doing well, thank you for asking.',
                    audio: 'UklGRiTYAABXQVZFZm10IBAAAAABAAEAwF0AAIC7AAACABAAZGF0YQDYAABYFlIX',
                    metadata: {
                        model: 'qwen-omni-turbo',
                        provider: 'aliyun',
                        modalities: ['text', 'audio'],
                        tools: ['animation_tool']
                    }
                })
            })
        );
        global.fetch = mockFetch;
    });

    afterEach(() => {
        global.fetch = originalFetch;
        vi.clearAllMocks();
    });

    test('should handle text-only input through server route', async () => {
        const messages = [
            { role: 'user', content: 'Hello, how are you?' }
        ];

        const options = {
            provider: 'aliyun',
            model: 'qwen-omni-turbo',
            modalities: ['text'],
            temperature: 0.7
        };

        const result = await llmAPI.invoke(messages, options);

        // Verify server route was called
        expect(mockFetch).toHaveBeenCalledTimes(1);
        expect(mockFetch).toHaveBeenCalledWith('http://localhost:2994/api/llm', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: expect.stringContaining('"provider":"aliyun"')
        });

        // Verify request structure
        const requestBody = JSON.parse(mockFetch.mock.calls[0][1].body);
        expect(requestBody.provider).toBe('aliyun');
        expect(requestBody.model).toBe('qwen-omni-turbo');
        expect(requestBody.messages[0].content).toBe('Hello, how are you?');
        expect(requestBody.modalities).toEqual(['text']);

        // Verify response format
        expect(result.content).toBe('Hello! I am doing well, thank you for asking.');
        expect(result.metadata).toBeDefined();
        expect(result.metadata.provider).toBe('aliyun');
    });

    test('should handle multimodal (text + audio) input through server route', async () => {
        const messages = [
            {
                role: 'user',
                content: 'Can you hear me?',
                input_audio: {
                    data: 'UklGRiTYAABXQVZFZm10IBAAAAABAAEAwF0AAIC7AAACABAAZGF0YQDYAABYFlIX',
                    format: 'wav'
                }
            }
        ];

        const options = {
            provider: 'aliyun',
            model: 'qwen-omni-turbo',
            modalities: ['text', 'audio'],
            audioConfig: { voice: 'Ethan', format: 'wav' },
            temperature: 0.7,
            max_tokens: 2000
        };

        const result = await llmAPI.invoke(messages, options);

        // Verify server route was called with audio data
        expect(mockFetch).toHaveBeenCalledTimes(1);

        const requestBody = JSON.parse(mockFetch.mock.calls[0][1].body);
        expect(requestBody.modalities).toEqual(['text', 'audio']);
        expect(requestBody.audioConfig.voice).toBe('Ethan');
        expect(requestBody.messages[0].input_audio).toBeDefined();
        expect(requestBody.messages[0].input_audio.data).toBe('UklGRiTYAABXQVZFZm10IBAAAAABAAEAwF0AAIC7AAACABAAZGF0YQDYAABYFlIX');

        // Verify response includes audio
        expect(result.content).toBe('Hello! I am doing well, thank you for asking.');
        expect(result.audio).toBe('UklGRiTYAABXQVZFZm10IBAAAAABAAEAwF0AAIC7AAACABAAZGF0YQDYAABYFlIX');
        expect(result.metadata.modalities).toContain('audio');
    });

    test('should handle tool calling through server route', async () => {
        const messages = [
            { role: 'user', content: 'Play some audio for me' }
        ];

        const options = {
            provider: 'aliyun',
            model: 'qwen-omni-turbo',
            tools: [
                {
                    name: 'play_audio',
                    description: 'Play audio response to the user',
                    schema: {
                        type: 'object',
                        properties: {
                            audio: { type: 'string', description: 'Base64 encoded audio data' },
                            format: { type: 'string', description: 'Audio format (wav, mp3, etc.)' }
                        },
                        required: ['audio', 'format']
                    }
                }
            ],
            tool_choice: 'auto'
        };

        const result = await llmAPI.invoke(messages, options);

        // Verify tools were sent to server
        const requestBody = JSON.parse(mockFetch.mock.calls[0][1].body);
        expect(requestBody.tools).toHaveLength(1);
        expect(requestBody.tools[0].name).toBe('play_audio');
        expect(requestBody.tools[0].schema.properties.audio).toBeDefined();
        expect(requestBody.tool_choice).toBe('auto');

        // Verify response
        expect(result.content).toBeDefined();
        expect(result.metadata.tools).toBeDefined();
    });

    test('should handle server errors gracefully', async () => {
        // Mock server error response
        mockFetch = vi.fn(() =>
            Promise.resolve({
                ok: false,
                status: 401,
                statusText: 'Unauthorized',
                text: () => Promise.resolve('API authentication failed')
            })
        );
        global.fetch = mockFetch;

        const messages = [{ role: 'user', content: 'Test error handling' }];

        await expect(llmAPI.invoke(messages)).rejects.toThrow(
            'LLM API request failed: 401 Unauthorized. API authentication failed'
        );
    });

    test('should handle network errors gracefully', async () => {
        // Mock network error
        mockFetch.mockRejectedValueOnce(new Error('Network connection failed'));

        const messages = [{ role: 'user', content: 'Test network error' }];

        await expect(llmAPI.invoke(messages)).rejects.toThrow('Network connection failed');
    });

    test('should format LangChain-style messages correctly', async () => {
        // Simulate LangChain message objects
        const messages = [
            {
                constructor: { name: 'SystemMessage' },
                content: 'You are a helpful assistant.',
                role: 'system'
            },
            {
                constructor: { name: 'HumanMessage' },
                content: 'Hello!',
                role: 'user'
            },
            {
                constructor: { name: 'AIMessage' },
                content: 'Hi there!',
                role: 'assistant'
            }
        ];

        await llmAPI.invoke(messages);

        const requestBody = JSON.parse(mockFetch.mock.calls[0][1].body);
        expect(requestBody.messages).toHaveLength(3);
        expect(requestBody.messages[0].role).toBe('system');
        expect(requestBody.messages[1].role).toBe('user');
        expect(requestBody.messages[2].role).toBe('assistant');
    });

    test('should preserve extra modalities in messages', async () => {
        const messages = [
            {
                role: 'user',
                content: 'Analyze this image',
                extraModalities: {
                    images: ['data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD...']
                }
            }
        ];

        await llmAPI.invoke(messages);

        const requestBody = JSON.parse(mockFetch.mock.calls[0][1].body);
        expect(requestBody.messages[0].extraModalities).toBeDefined();
        expect(requestBody.messages[0].extraModalities.images).toHaveLength(1);
    });

    test('should use default values for missing options', async () => {
        const messages = [{ role: 'user', content: 'Test defaults' }];

        await llmAPI.invoke(messages, {}); // Empty options

        const requestBody = JSON.parse(mockFetch.mock.calls[0][1].body);
        expect(requestBody.provider).toBe('aliyun');
        expect(requestBody.model).toBe('qwen-omni-turbo');
        expect(requestBody.modalities).toEqual(['text']);
        expect(requestBody.audioConfig.voice).toBe('Chelsie');
        expect(requestBody.temperature).toBe(0.7);
        expect(requestBody.stream).toBe(false);
    });
}); 