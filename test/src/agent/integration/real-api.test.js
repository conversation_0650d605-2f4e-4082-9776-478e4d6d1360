/**
 * Real API Integration Tests
 * Tests agent functionality with actual vLLM API calls and real checkpoints
 */

import { describe, it, expect, beforeAll, afterAll, beforeEach, afterEach, vi } from 'vitest';
import { LangGraphAgentService } from '@/agent/core.js';
import { fetchVllmApi } from '@/utils/apiProxy.ts';
import { selectAnimationTool } from '@/agent/tools/animation.js';

// Real test configuration
const REAL_CONFIG = {
    vllmEndpoint: process.env.VITE_VLLM_ENDPOINT || 'http://***********:20095',
    model: 'Qwen/Qwen2.5-Omni-7B',
    temperature: 0.7,
    maxTokens: 150,
    timeout: 15000 // 15 second timeout for real API calls
};

// Test data for real scenarios
const TEST_SCENARIOS = {
    simple: {
        input: 'Hello! Please respond briefly.',
        expectedKeywords: ['hello', 'hi', 'greetings']
    },
    animation: {
        input: 'Please wave hello to me!',
        expectedAnimation: 'wave',
        expectedKeywords: ['wave', 'hello']
    },
    conversation: {
        messages: [
            'Hi, my name is <PERSON>.',
            'What did I just tell you my name was?'
        ],
        expectedResponse: 'alice'
    }
};

describe('Real API Integration Tests', () => {
    let agentService;
    let apiAvailable = false;

    beforeAll(async () => {
        // Check if vLLM API is available
        try {
            const response = await fetchVllmApi('v1/models');
            apiAvailable = response.ok;

            if (apiAvailable) {
                console.log('✅ vLLM API is available for testing');
            } else {
                console.warn('⚠️ vLLM API not available - some tests will be skipped');
            }
        } catch (error) {
            console.warn('⚠️ vLLM API connection failed:', error.message);
            apiAvailable = false;
        }
    }, REAL_CONFIG.timeout);

    beforeEach(async () => {
        if (!apiAvailable) return;

        agentService = new LangGraphAgentService({
            vllmEndpoint: REAL_CONFIG.vllmEndpoint,
            model: REAL_CONFIG.model,
            temperature: REAL_CONFIG.temperature,
            maxTokens: REAL_CONFIG.maxTokens,
            services: {
                animationRegistry: {
                    'wave': { id: 'wave', name: 'Wave Animation' },
                    'nod': { id: 'nod', name: 'Nod Animation' },
                    'dance': { id: 'dance', name: 'Dance Animation' }
                }
            }
        });

        try {
            await agentService.initialize();
        } catch (error) {
            console.warn('Agent initialization failed:', error.message);
        }
    });

    afterEach(() => {
        if (agentService) {
            agentService.dispose();
        }
    });

    describe('API Connectivity', () => {
        it('should connect to vLLM API and list models', async () => {
            if (!apiAvailable) {
                console.warn('Skipping API connectivity test - API not available');
                return;
            }

            const response = await fetchVllmApi('v1/models');
            expect(response.ok).toBe(true);

            const data = await response.json();
            expect(data).toBeDefined();
            expect(data.data).toBeDefined();
            expect(Array.isArray(data.data)).toBe(true);

            // Should have at least one model
            expect(data.data.length).toBeGreaterThan(0);

            // Check if our test model is available
            const hasTestModel = data.data.some(model =>
                model.id.includes('Qwen') || model.id.includes('qwen')
            );
            expect(hasTestModel).toBe(true);
        }, REAL_CONFIG.timeout);

        it('should make successful chat completion requests', async () => {
            if (!apiAvailable) {
                console.warn('Skipping chat completion test - API not available');
                return;
            }

            const response = await fetchVllmApi('v1/chat/completions', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    model: REAL_CONFIG.model,
                    messages: [{ role: 'user', content: 'Say "test successful"' }],
                    max_tokens: 50,
                    temperature: 0.1
                })
            });

            expect(response.ok).toBe(true);

            const data = await response.json();
            expect(data.choices).toBeDefined();
            expect(data.choices.length).toBeGreaterThan(0);
            expect(data.choices[0].message.content).toBeDefined();

            const content = data.choices[0].message.content.toLowerCase();
            expect(content).toContain('test');
        }, REAL_CONFIG.timeout);
    });

    describe('Agent Service Integration', () => {
        it('should initialize agent service successfully', async () => {
            if (!apiAvailable || !agentService?._initialized) {
                console.warn('Skipping agent initialization test');
                return;
            }

            expect(agentService).toBeDefined();
            expect(agentService._initialized).toBe(true);
            expect(agentService.workflow).toBeDefined();
        });

        it('should handle simple text generation', async () => {
            if (!apiAvailable || !agentService?._initialized) {
                console.warn('Skipping text generation test');
                return;
            }

            const result = await agentService.generateResponse(
                TEST_SCENARIOS.simple.input,
                {
                    sessionId: 'test-simple',
                    useTools: false
                }
            );

            expect(result).toBeDefined();
            expect(result.success).toBe(true);
            expect(result.response).toBeDefined();
            expect(typeof result.response).toBe('string');
            expect(result.response.length).toBeGreaterThan(0);

            // Should contain some greeting-like response
            const response = result.response.toLowerCase();
            const hasGreeting = TEST_SCENARIOS.simple.expectedKeywords.some(
                keyword => response.includes(keyword)
            );
            expect(hasGreeting).toBe(true);
        }, REAL_CONFIG.timeout);

        it('should handle streaming responses', async () => {
            if (!apiAvailable || !agentService?._initialized) {
                console.warn('Skipping streaming test');
                return;
            }

            const chunks = [];
            let fullResponse = '';

            const result = await agentService.generateResponse(
                'Count from 1 to 3',
                {
                    sessionId: 'test-streaming',
                    useStreaming: true,
                    useTools: false,
                    onTextChunk: (data) => {
                        chunks.push(data);
                        fullResponse += data.content || '';
                    }
                }
            );

            expect(result.success).toBe(true);
            expect(chunks.length).toBeGreaterThan(0);
            expect(fullResponse.length).toBeGreaterThan(0);

            // Should contain numbers
            expect(/[123]/.test(fullResponse)).toBe(true);
        }, REAL_CONFIG.timeout);
    });

    describe('Tool Integration', () => {
        it('should execute animation tools successfully', async () => {
            if (!apiAvailable || !agentService?._initialized) {
                console.warn('Skipping tool integration test');
                return;
            }

            const result = await agentService.generateResponse(
                TEST_SCENARIOS.animation.input,
                {
                    sessionId: 'test-animation',
                    useTools: true,
                    useAnimation: true
                }
            );

            expect(result.success).toBe(true);
            expect(result.response).toBeDefined();

            // Check if animation was triggered
            if (result.toolResults && result.toolResults.length > 0) {
                const animationResult = result.toolResults.find(
                    tool => tool.tool === 'select_animation'
                );

                if (animationResult) {
                    expect(animationResult.result.success).toBe(true);
                    expect(animationResult.result.animationId).toBeDefined();
                }
            }
        }, REAL_CONFIG.timeout);

        it('should handle conversation context', async () => {
            if (!apiAvailable || !agentService?._initialized) {
                console.warn('Skipping conversation context test');
                return;
            }

            const sessionId = 'test-conversation';

            // First message
            const result1 = await agentService.generateResponse(
                TEST_SCENARIOS.conversation.messages[0],
                { sessionId, useTools: false }
            );

            expect(result1.success).toBe(true);

            // Second message - should remember the name
            const result2 = await agentService.generateResponse(
                TEST_SCENARIOS.conversation.messages[1],
                { sessionId, useTools: false }
            );

            expect(result2.success).toBe(true);
            expect(result2.response.toLowerCase()).toContain(
                TEST_SCENARIOS.conversation.expectedResponse
            );
        }, REAL_CONFIG.timeout * 2); // Double timeout for conversation test
    });

    describe('Error Handling', () => {
        it('should handle invalid model gracefully', async () => {
            if (!apiAvailable) {
                console.warn('Skipping error handling test');
                return;
            }

            const invalidAgent = new LangGraphAgentService({
                vllmEndpoint: REAL_CONFIG.vllmEndpoint,
                model: 'invalid-model-name',
                maxTokens: 50
            });

            try {
                await invalidAgent.initialize();
                const result = await invalidAgent.generateResponse('test');

                // Should handle error gracefully
                expect(result.success).toBe(false);
                expect(result.error).toBeDefined();
            } catch (error) {
                // Also acceptable to throw error
                expect(error).toBeDefined();
            } finally {
                invalidAgent.dispose();
            }
        }, REAL_CONFIG.timeout);

        it('should handle network timeouts gracefully', async () => {
            if (!apiAvailable) {
                console.warn('Skipping timeout test');
                return;
            }

            const timeoutAgent = new LangGraphAgentService({
                vllmEndpoint: REAL_CONFIG.vllmEndpoint,
                model: REAL_CONFIG.model,
                maxTokens: 1000, // Large response to potentially trigger timeout
                timeout: 1000 // Very short timeout
            });

            try {
                await timeoutAgent.initialize();
                const result = await timeoutAgent.generateResponse(
                    'Write a very long story about a magical kingdom...'
                );

                // Should either succeed quickly or fail gracefully
                expect(result).toBeDefined();
                if (!result.success) {
                    expect(result.error).toBeDefined();
                }
            } catch (error) {
                // Timeout errors are acceptable
                expect(error).toBeDefined();
            } finally {
                timeoutAgent.dispose();
            }
        }, REAL_CONFIG.timeout);
    });
});
