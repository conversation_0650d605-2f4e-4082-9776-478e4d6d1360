/**
 * Tool Calling with Audio Output TDD Tests
 * Test-driven development for tool calling that returns both tool results and audio responses
 * Following LangChain v0.3 best practices and Qwen-Omni capabilities
 */

import { describe, it, expect, beforeAll, afterAll, beforeEach, vi } from 'vitest';
import fetch from 'node-fetch';

// Test configuration
const TEST_CONFIG = {
    apiBaseUrl: process.env.API_BASE_URL || 'http://localhost:3020',
    timeout: 60000, // 60 seconds for tool calling + audio generation
    useRealAPI: process.env.TEST_REAL_API === 'true'
};

describe('Tool Calling with Audio Output TDD Tests', () => {
    beforeAll(async () => {
        if (!TEST_CONFIG.useRealAPI) {
            console.warn('⚠️ Tool calling audio tests require TEST_REAL_API=true to run against live API');
            return;
        }

        // Wait for server to be ready
        await new Promise(resolve => setTimeout(resolve, 2000));
    });

    describe('Tool Definition and Binding Tests (TDD)', () => {
        it('should accept tool definitions in LangChain format', async () => {
            if (!TEST_CONFIG.useRealAPI) {
                console.warn('Skipping real API test - set TEST_REAL_API=true to enable');
                return;
            }

            const weatherTool = {
                name: 'get_weather',
                description: 'Get current weather information for a location',
                schema: {
                    type: 'object',
                    properties: {
                        location: {
                            type: 'string',
                            description: 'The city and state, e.g. San Francisco, CA'
                        },
                        unit: {
                            type: 'string',
                            enum: ['celsius', 'fahrenheit'],
                            description: 'Temperature unit'
                        }
                    },
                    required: ['location']
                }
            };

            const response = await fetch(`${TEST_CONFIG.apiBaseUrl}/api/llm`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    provider: 'aliyun',
                    model: 'qwen-omni-turbo',
                    modalities: ['text', 'audio'],
                    audioConfig: { voice: 'Ethan', format: 'wav' },
                    tools: [weatherTool],
                    messages: [{
                        role: 'user',
                        content: 'What\'s the weather like in San Francisco? Please tell me in both text and audio.'
                    }]
                })
            });

            expect(response.ok).toBe(true);
            const responseData = await response.json();

            // Should recognize tool call intent
            expect(responseData).toHaveProperty('content');
            expect(responseData).toHaveProperty('audio');

            // Content should mention weather or tool usage
            const content = responseData.content.toLowerCase();
            const hasWeatherMention = content.includes('weather') || content.includes('san francisco') || content.includes('tool');
            expect(hasWeatherMention).toBe(true);

            // Should still generate audio response
            expect(responseData.audio).toBeTruthy();
            expect(responseData.audio.length).toBeGreaterThan(1000);

            console.log('✅ Tool definition acceptance test passed');
            console.log(`   Content: ${responseData.content.substring(0, 100)}...`);
        }, TEST_CONFIG.timeout);

        it('should handle multiple tool definitions', async () => {
            if (!TEST_CONFIG.useRealAPI) {
                console.warn('Skipping real API test - set TEST_REAL_API=true to enable');
                return;
            }

            const tools = [
                {
                    name: 'get_weather',
                    description: 'Get current weather information',
                    schema: {
                        type: 'object',
                        properties: {
                            location: { type: 'string', description: 'City name' }
                        },
                        required: ['location']
                    }
                },
                {
                    name: 'get_time',
                    description: 'Get current time in a timezone',
                    schema: {
                        type: 'object',
                        properties: {
                            timezone: { type: 'string', description: 'Timezone name' }
                        },
                        required: ['timezone']
                    }
                },
                {
                    name: 'tell_joke',
                    description: 'Tell a joke on a specific topic',
                    schema: {
                        type: 'object',
                        properties: {
                            topic: { type: 'string', description: 'Joke topic' }
                        }
                    }
                }
            ];

            const response = await fetch(`${TEST_CONFIG.apiBaseUrl}/api/llm`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    provider: 'aliyun',
                    model: 'qwen-omni-turbo',
                    modalities: ['text', 'audio'],
                    audioConfig: { voice: 'Chelsie', format: 'wav' },
                    tools: tools,
                    messages: [{
                        role: 'user',
                        content: 'Tell me a programming joke and also let me know the weather in New York'
                    }]
                })
            });

            expect(response.ok).toBe(true);
            const responseData = await response.json();

            // Should handle multiple tool context
            expect(responseData.content).toBeTruthy();
            expect(responseData.audio).toBeTruthy();

            console.log('✅ Multiple tool definitions test passed');
        }, TEST_CONFIG.timeout);
    });

    describe('Tool Call Generation Tests (TDD)', () => {
        it('should generate tool calls in response to user requests', async () => {
            if (!TEST_CONFIG.useRealAPI) {
                console.warn('Skipping real API test - set TEST_REAL_API=true to enable');
                return;
            }

            const animationTool = {
                name: 'trigger_animation',
                description: 'Trigger an animation with specific mood and duration',
                schema: {
                    type: 'object',
                    properties: {
                        mood: {
                            type: 'string',
                            enum: ['happy', 'sad', 'excited', 'calm', 'surprised'],
                            description: 'The emotional mood of the animation'
                        },
                        duration: {
                            type: 'number',
                            description: 'Duration in seconds',
                            minimum: 1,
                            maximum: 10
                        }
                    },
                    required: ['mood']
                }
            };

            const response = await fetch(`${TEST_CONFIG.apiBaseUrl}/api/llm`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    provider: 'aliyun',
                    model: 'qwen-omni-turbo',
                    modalities: ['text', 'audio'],
                    audioConfig: { voice: 'Ethan', format: 'wav' },
                    tools: [animationTool],
                    messages: [{
                        role: 'user',
                        content: 'Show me a happy animation for 3 seconds and explain what you\'re doing'
                    }]
                })
            });

            expect(response.ok).toBe(true);
            const responseData = await response.json();

            // Should mention animation or tool usage
            expect(responseData.content).toBeTruthy();
            const content = responseData.content.toLowerCase();
            const hasAnimationMention = content.includes('animation') || content.includes('happy') || content.includes('trigger');
            expect(hasAnimationMention).toBe(true);

            // Should generate audio explaining the action
            expect(responseData.audio).toBeTruthy();
            expect(responseData.audio.length).toBeGreaterThan(1000);

            console.log('✅ Tool call generation test passed');
            console.log(`   Content: ${responseData.content}`);
        }, TEST_CONFIG.timeout);

        it('should handle tool calls with audio input and audio output', async () => {
            if (!TEST_CONFIG.useRealAPI) {
                console.warn('Skipping real API test - set TEST_REAL_API=true to enable');
                return;
            }

            const transcriptionTool = {
                name: 'transcribe_audio',
                description: 'Transcribe audio content to text',
                schema: {
                    type: 'object',
                    properties: {
                        audio_data: { type: 'string', description: 'Base64 encoded audio data' },
                        language: { type: 'string', description: 'Expected language of the audio' }
                    },
                    required: ['audio_data']
                }
            };

            const sampleAudio = 'UklGRiQEAABXQVZFZm10IBAAAAABAAEARKwAAIhYAQACABAAZGF0YQAEAAD//f7/AgAA//8AAAEA//8AAP//AAD//wAA//8AAP/+';

            const response = await fetch(`${TEST_CONFIG.apiBaseUrl}/api/llm`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    provider: 'aliyun',
                    model: 'qwen-omni-turbo',
                    modalities: ['text', 'audio'],
                    audioConfig: { voice: 'Chelsie', format: 'wav' },
                    tools: [transcriptionTool],
                    messages: [{
                        role: 'user',
                        content: [
                            {
                                type: 'input_audio',
                                input_audio: {
                                    data: sampleAudio,
                                    format: 'wav'
                                }
                            },
                            {
                                type: 'text',
                                text: 'Please transcribe this audio and tell me what you found'
                            }
                        ]
                    }]
                })
            });

            expect(response.ok).toBe(true);
            const responseData = await response.json();

            // Should handle audio input and mention transcription
            expect(responseData.content).toBeTruthy();
            expect(responseData.audio).toBeTruthy();

            console.log('✅ Audio input + tool call + audio output test passed');
        }, TEST_CONFIG.timeout);
    });

    describe('Tool Execution Simulation Tests (TDD)', () => {
        it('should simulate tool execution and provide results with audio', async () => {
            if (!TEST_CONFIG.useRealAPI) {
                console.warn('Skipping real API test - set TEST_REAL_API=true to enable');
                return;
            }

            const calculatorTool = {
                name: 'calculate',
                description: 'Perform mathematical calculations',
                schema: {
                    type: 'object',
                    properties: {
                        expression: {
                            type: 'string',
                            description: 'Mathematical expression to evaluate (e.g., "2 + 2", "sqrt(16)")'
                        }
                    },
                    required: ['expression']
                }
            };

            const response = await fetch(`${TEST_CONFIG.apiBaseUrl}/api/llm`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    provider: 'aliyun',
                    model: 'qwen-omni-turbo',
                    modalities: ['text', 'audio'],
                    audioConfig: { voice: 'Ethan', format: 'wav' },
                    tools: [calculatorTool],
                    messages: [{
                        role: 'user',
                        content: 'Calculate 15 * 7 + 12 and explain the result to me with audio'
                    }]
                })
            });

            expect(response.ok).toBe(true);
            const responseData = await response.json();

            // Should mention calculation or math
            const content = responseData.content.toLowerCase();
            const hasMathMention = content.includes('calculate') || content.includes('math') ||
                content.includes('15') || content.includes('7') || content.includes('12') ||
                content.includes('result') || content.includes('answer');
            expect(hasMathMention).toBe(true);

            // Should provide audio explanation
            expect(responseData.audio).toBeTruthy();
            expect(responseData.audio.length).toBeGreaterThan(1000);

            console.log('✅ Tool execution simulation test passed');
            console.log(`   Content: ${responseData.content}`);
        }, TEST_CONFIG.timeout);

        it('should handle tool execution errors gracefully with audio explanation', async () => {
            if (!TEST_CONFIG.useRealAPI) {
                console.warn('Skipping real API test - set TEST_REAL_API=true to enable');
                return;
            }

            const faultyTool = {
                name: 'check_impossible',
                description: 'Check something that might fail',
                schema: {
                    type: 'object',
                    properties: {
                        query: { type: 'string', description: 'Query to check' }
                    },
                    required: ['query']
                }
            };

            const response = await fetch(`${TEST_CONFIG.apiBaseUrl}/api/llm`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    provider: 'aliyun',
                    model: 'qwen-omni-turbo',
                    modalities: ['text', 'audio'],
                    audioConfig: { voice: 'Chelsie', format: 'wav' },
                    tools: [faultyTool],
                    messages: [{
                        role: 'user',
                        content: 'Try to check if unicorns exist in the database and let me know what happens'
                    }]
                })
            });

            expect(response.ok).toBe(true);
            const responseData = await response.json();

            // Should handle gracefully and explain in audio
            expect(responseData.content).toBeTruthy();
            expect(responseData.audio).toBeTruthy();

            console.log('✅ Tool error handling test passed');
        }, TEST_CONFIG.timeout);
    });

    describe('Complex Tool Workflows Tests (TDD)', () => {
        it('should handle multi-step tool workflows with audio narration', async () => {
            if (!TEST_CONFIG.useRealAPI) {
                console.warn('Skipping real API test - set TEST_REAL_API=true to enable');
                return;
            }

            const tools = [
                {
                    name: 'search_database',
                    description: 'Search for information in the database',
                    schema: {
                        type: 'object',
                        properties: {
                            query: { type: 'string', description: 'Search query' },
                            category: { type: 'string', description: 'Category to search in' }
                        },
                        required: ['query']
                    }
                },
                {
                    name: 'format_results',
                    description: 'Format search results for display',
                    schema: {
                        type: 'object',
                        properties: {
                            data: { type: 'string', description: 'Raw data to format' },
                            format: { type: 'string', enum: ['json', 'table', 'list'], description: 'Output format' }
                        },
                        required: ['data']
                    }
                },
                {
                    name: 'send_notification',
                    description: 'Send a notification about the results',
                    schema: {
                        type: 'object',
                        properties: {
                            message: { type: 'string', description: 'Notification message' },
                            priority: { type: 'string', enum: ['low', 'medium', 'high'], description: 'Priority level' }
                        },
                        required: ['message']
                    }
                }
            ];

            const response = await fetch(`${TEST_CONFIG.apiBaseUrl}/api/llm`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    provider: 'aliyun',
                    model: 'qwen-omni-turbo',
                    modalities: ['text', 'audio'],
                    audioConfig: { voice: 'Ethan', format: 'wav' },
                    tools: tools,
                    messages: [{
                        role: 'user',
                        content: 'Search for "machine learning tutorials", format the results as a table, and send a notification about what you found. Explain each step you\'re taking.'
                    }]
                })
            });

            expect(response.ok).toBe(true);
            const responseData = await response.json();

            // Should mention the workflow steps
            const content = responseData.content.toLowerCase();
            const hasWorkflowMention = content.includes('search') || content.includes('format') ||
                content.includes('notification') || content.includes('step');
            expect(hasWorkflowMention).toBe(true);

            // Should provide detailed audio explanation
            expect(responseData.audio).toBeTruthy();
            expect(responseData.audio.length).toBeGreaterThan(2000); // Longer audio for complex workflow

            console.log('✅ Multi-step workflow test passed');
            console.log(`   Content: ${responseData.content.substring(0, 150)}...`);
        }, TEST_CONFIG.timeout);

        it('should handle tool calls with different voice configurations', async () => {
            if (!TEST_CONFIG.useRealAPI) {
                console.warn('Skipping real API test - set TEST_REAL_API=true to enable');
                return;
            }

            const weatherTool = {
                name: 'get_weather',
                description: 'Get weather information',
                schema: {
                    type: 'object',
                    properties: {
                        location: { type: 'string', description: 'Location name' }
                    },
                    required: ['location']
                }
            };

            const voices = ['Ethan', 'Chelsie'];
            const results = [];

            for (const voice of voices) {
                const response = await fetch(`${TEST_CONFIG.apiBaseUrl}/api/llm`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        provider: 'aliyun',
                        model: 'qwen-omni-turbo',
                        modalities: ['text', 'audio'],
                        audioConfig: { voice, format: 'wav' },
                        tools: [weatherTool],
                        messages: [{
                            role: 'user',
                            content: `Check the weather in Tokyo and explain it using ${voice} voice`
                        }]
                    })
                });

                expect(response.ok).toBe(true);
                const responseData = await response.json();

                expect(responseData.audio).toBeTruthy();
                expect(responseData.metadata.audioConfig.voice).toBe(voice);

                results.push({
                    voice,
                    audioLength: responseData.audio.length,
                    content: responseData.content.substring(0, 100)
                });

                // Small delay between requests
                await new Promise(resolve => setTimeout(resolve, 1000));
            }

            console.log('✅ Tool calls with different voices test passed');
            results.forEach(result => {
                console.log(`   ${result.voice}: ${result.audioLength} chars audio, "${result.content}..."`);
            });
        }, TEST_CONFIG.timeout * 2);
    });

    describe('LangChain Integration Tests (TDD)', () => {
        it('should work with LangChain tool binding format', async () => {
            if (!TEST_CONFIG.useRealAPI) {
                console.warn('Skipping real API test - set TEST_REAL_API=true to enable');
                return;
            }

            // LangChain-style tool definition
            const langchainTool = {
                name: 'langchain_test_tool',
                description: 'A test tool following LangChain conventions',
                schema: {
                    type: 'object',
                    properties: {
                        action: {
                            type: 'string',
                            description: 'The action to perform'
                        },
                        parameters: {
                            type: 'object',
                            description: 'Parameters for the action'
                        }
                    },
                    required: ['action']
                }
            };

            const response = await fetch(`${TEST_CONFIG.apiBaseUrl}/api/llm`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    provider: 'aliyun',
                    model: 'qwen-omni-turbo',
                    modalities: ['text', 'audio'],
                    audioConfig: { voice: 'Chelsie', format: 'wav' },
                    tools: [langchainTool],
                    toolChoice: 'auto', // LangChain-style tool choice
                    messages: [{
                        role: 'user',
                        content: 'Use the LangChain test tool to demonstrate integration'
                    }]
                })
            });

            expect(response.ok).toBe(true);
            const responseData = await response.json();

            // Should handle LangChain format
            expect(responseData.content).toBeTruthy();
            expect(responseData.audio).toBeTruthy();

            console.log('✅ LangChain integration test passed');
        }, TEST_CONFIG.timeout);

        it('should support streaming tool calls with audio generation', async () => {
            if (!TEST_CONFIG.useRealAPI) {
                console.warn('Skipping real API test - set TEST_REAL_API=true to enable');
                return;
            }

            const streamingTool = {
                name: 'generate_report',
                description: 'Generate a detailed report',
                schema: {
                    type: 'object',
                    properties: {
                        topic: { type: 'string', description: 'Report topic' },
                        length: { type: 'string', enum: ['short', 'medium', 'long'], description: 'Report length' }
                    },
                    required: ['topic']
                }
            };

            const response = await fetch(`${TEST_CONFIG.apiBaseUrl}/api/llm`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    provider: 'aliyun',
                    model: 'qwen-omni-turbo',
                    modalities: ['text', 'audio'],
                    audioConfig: { voice: 'Ethan', format: 'wav' },
                    tools: [streamingTool],
                    stream: true, // Enable streaming
                    messages: [{
                        role: 'user',
                        content: 'Generate a medium-length report about artificial intelligence and narrate the key points'
                    }]
                })
            });

            expect(response.ok).toBe(true);
            const responseData = await response.json();

            // Should handle streaming with tools
            expect(responseData.content).toBeTruthy();
            expect(responseData.audio).toBeTruthy();
            expect(responseData.audio.length).toBeGreaterThan(2000);

            console.log('✅ Streaming tool calls test passed');
        }, TEST_CONFIG.timeout);
    });
}); 