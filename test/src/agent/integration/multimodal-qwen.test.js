/**
 * Multi-Modal Qwen-Omni API Tests
 * Tests for the new multi-modal capabilities of Qwen-Omni API
 * Supporting audio + video + image + text in a single message
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { AliyunBailianChatModel } from '@/agent/models/AliyunBailianChatModel.js';
import { MediaHandler } from '@/agent/models/aliyun/media/MediaHandler.js';

// Mock dependencies
vi.mock('@/utils/logger.js', () => ({
    createLogger: vi.fn(() => ({
        info: vi.fn(),
        debug: vi.fn(),
        warn: vi.fn(),
        error: vi.fn(),
        setLogLevel: vi.fn()
    })),
    LogLevel: { DEBUG: 0, INFO: 1, WARN: 2, ERROR: 3, NONE: 4 }
}));

vi.mock('@/config/env.ts', () => ({
    getEnvVar: vi.fn((key, defaultValue) => {
        if (key === 'VITE_DASHSCOPE_API_KEY') return 'test-api-key';
        return defaultValue;
    })
}));

vi.mock('@/utils/portManager.js', () => ({
    getDownloadServerUrl: vi.fn(() => 'http://localhost:2994')
}));

describe('Multi-Modal Qwen-Omni API Tests', () => {
    let model;
    let mediaHandler;

    beforeEach(() => {
        vi.clearAllMocks();
        model = new AliyunBailianChatModel({
            apiMode: 'http',
            model: 'qwen-omni-turbo',
            modalities: ['text', 'audio', 'video']
        });
        mediaHandler = new MediaHandler();
    });

    afterEach(() => {
        if (model) {
            model.closeRealtimeMode();
        }
    });

    describe('Message Format Construction', () => {
        it('should construct message with text only', () => {
            const content = [];
            const text = 'Hello, how are you?';

            if (text) content.push({ type: 'text', text });

            expect(content).toEqual([
                { type: 'text', text: 'Hello, how are you?' }
            ]);
        });

        it('should construct message with image + text', () => {
            const content = [];
            const imageUrl = 'https://example.com/image.jpg';
            const text = 'What do you see in this image?';

            if (imageUrl) content.push({ type: 'image_url', image_url: { url: imageUrl } });
            if (text) content.push({ type: 'text', text });

            expect(content).toEqual([
                { type: 'image_url', image_url: { url: 'https://example.com/image.jpg' } },
                { type: 'text', text: 'What do you see in this image?' }
            ]);
        });

        it('should construct message with audio + text', () => {
            const content = [];
            const audioUrl = 'https://example.com/audio.wav';
            const text = 'What is said in this audio?';

            if (audioUrl) content.push({ type: 'audio_url', audio_url: { url: audioUrl } });
            if (text) content.push({ type: 'text', text });

            expect(content).toEqual([
                { type: 'audio_url', audio_url: { url: 'https://example.com/audio.wav' } },
                { type: 'text', text: 'What is said in this audio?' }
            ]);
        });

        it('should construct message with video + text', () => {
            const content = [];
            const videoUrl = 'https://example.com/video.mp4';
            const text = 'Describe what happens in this video';

            if (videoUrl) content.push({ type: 'video_url', video_url: { url: videoUrl } });
            if (text) content.push({ type: 'text', text });

            expect(content).toEqual([
                { type: 'video_url', video_url: { url: 'https://example.com/video.mp4' } },
                { type: 'text', text: 'Describe what happens in this video' }
            ]);
        });

        it('should construct message with audio + image + text (multiple modalities)', () => {
            const content = [];
            const imageUrl = 'https://example.com/image.jpg';
            const audioUrl = 'https://example.com/audio.wav';
            const text = 'Describe both the image and the audio';

            if (imageUrl) content.push({ type: 'image_url', image_url: { url: imageUrl } });
            if (audioUrl) content.push({ type: 'audio_url', audio_url: { url: audioUrl } });
            if (text) content.push({ type: 'text', text });

            expect(content).toEqual([
                { type: 'image_url', image_url: { url: 'https://example.com/image.jpg' } },
                { type: 'audio_url', audio_url: { url: 'https://example.com/audio.wav' } },
                { type: 'text', text: 'Describe both the image and the audio' }
            ]);
        });

        it('should construct message with audio + video + text', () => {
            const content = [];
            const audioUrl = 'https://example.com/audio.wav';
            const videoUrl = 'https://example.com/video.mp4';
            const text = 'Compare the audio and video content';

            if (audioUrl) content.push({ type: 'audio_url', audio_url: { url: audioUrl } });
            if (videoUrl) content.push({ type: 'video_url', video_url: { url: videoUrl } });
            if (text) content.push({ type: 'text', text });

            expect(content).toEqual([
                { type: 'audio_url', audio_url: { url: 'https://example.com/audio.wav' } },
                { type: 'video_url', video_url: { url: 'https://example.com/video.mp4' } },
                { type: 'text', text: 'Compare the audio and video content' }
            ]);
        });

        it('should construct message with all modalities: image + audio + video + text', () => {
            const content = [];
            const imageUrl = 'https://example.com/image.jpg';
            const audioUrl = 'https://example.com/audio.wav';
            const videoUrl = 'https://example.com/video.mp4';
            const text = 'Analyze all three media types together';

            if (imageUrl) content.push({ type: 'image_url', image_url: { url: imageUrl } });
            if (audioUrl) content.push({ type: 'audio_url', audio_url: { url: audioUrl } });
            if (videoUrl) content.push({ type: 'video_url', video_url: { url: videoUrl } });
            if (text) content.push({ type: 'text', text });

            expect(content).toEqual([
                { type: 'image_url', image_url: { url: 'https://example.com/image.jpg' } },
                { type: 'audio_url', audio_url: { url: 'https://example.com/audio.wav' } },
                { type: 'video_url', video_url: { url: 'https://example.com/video.mp4' } },
                { type: 'text', text: 'Analyze all three media types together' }
            ]);
        });
    });

    describe('Base64 Encoding Support', () => {
        it('should support base64 encoded audio', () => {
            const content = [];
            const audioBase64 = 'UklGRqYwBgBXQVZFZm10...'; // Sample base64
            const text = 'Transcribe this audio';

            if (audioBase64) {
                content.push({
                    type: 'audio_url',
                    audio_url: {
                        data: audioBase64,
                        format: 'wav'
                    }
                });
            }
            if (text) content.push({ type: 'text', text });

            expect(content).toEqual([
                {
                    type: 'audio_url',
                    audio_url: {
                        data: 'UklGRqYwBgBXQVZFZm10...',
                        format: 'wav'
                    }
                },
                { type: 'text', text: 'Transcribe this audio' }
            ]);
        });

        it('should support base64 encoded image', () => {
            const content = [];
            const imageBase64 = 'iVBORw0KGgoAAAANSUhEUgAA...'; // Sample base64
            const text = 'What is in this image?';

            if (imageBase64) {
                content.push({
                    type: 'image_url',
                    image_url: {
                        url: `data:image/png;base64,${imageBase64}`
                    }
                });
            }
            if (text) content.push({ type: 'text', text });

            expect(content).toEqual([
                {
                    type: 'image_url',
                    image_url: {
                        url: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...'
                    }
                },
                { type: 'text', text: 'What is in this image?' }
            ]);
        });
    });

    describe('AliyunBailianChatModel Integration', () => {
        it('should format multi-modal messages correctly for HTTP API', async () => {
            // Mock fetch for browser environment
            global.fetch = vi.fn().mockResolvedValue({
                json: () => Promise.resolve({
                    choices: [{
                        message: {
                            content: 'I can see an image and hear audio. The image shows...'
                        }
                    }]
                })
            });

            const messages = [{
                role: 'user',
                content: [
                    { type: 'image_url', image_url: { url: 'https://example.com/image.jpg' } },
                    { type: 'audio_url', audio_url: { url: 'https://example.com/audio.wav' } },
                    { type: 'text', text: 'Describe both the image and audio' }
                ]
            }];

            // Test message formatting in _invokeHTTP
            const formattedMessages = messages.map(m => {
                return { role: m.role, content: m.content };
            });

            expect(formattedMessages).toEqual([{
                role: 'user',
                content: [
                    { type: 'image_url', image_url: { url: 'https://example.com/image.jpg' } },
                    { type: 'audio_url', audio_url: { url: 'https://example.com/audio.wav' } },
                    { type: 'text', text: 'Describe both the image and audio' }
                ]
            }]);
        });

        it('should handle modality detection correctly', () => {
            const testCases = [
                {
                    input: { text: 'Hello' },
                    expected: ['text']
                },
                {
                    input: { text: 'Hello', imageUrl: 'image.jpg' },
                    expected: ['text', 'image']
                },
                {
                    input: { text: 'Hello', audioData: 'base64audio' },
                    expected: ['text', 'audio']
                },
                {
                    input: { text: 'Hello', videoUrl: 'video.mp4' },
                    expected: ['text', 'video']
                },
                {
                    input: {
                        text: 'Hello',
                        imageUrl: 'image.jpg',
                        audioData: 'base64audio'
                    },
                    expected: ['text', 'image', 'audio']
                }
            ];

            testCases.forEach(({ input, expected }) => {
                const detectedModalities = [];

                if (input.text) detectedModalities.push('text');
                if (input.imageUrl) detectedModalities.push('image');
                if (input.audioData) detectedModalities.push('audio');
                if (input.videoUrl) detectedModalities.push('video');

                expect(detectedModalities).toEqual(expected);
            });
        });
    });

    describe('MediaHandler Integration', () => {
        it('should process multi-modal input correctly', async () => {
            const mockProcessMultimodal = vi.fn().mockResolvedValue({
                text: 'Analyze this content',
                audio: { data: 'base64audio', format: 'wav' },
                images: [{ url: 'https://example.com/image.jpg' }],
                video: { url: 'https://example.com/video.mp4' }
            });

            mediaHandler.processMultimodal = mockProcessMultimodal;

            const input = {
                text: 'Analyze this content',
                audioFile: new Blob(['audio'], { type: 'audio/wav' }),
                imageFile: new File(['image'], 'image.jpg', { type: 'image/jpeg' }),
                videoUrl: 'https://example.com/video.mp4'
            };

            const result = await mediaHandler.processMultimodal(input);

            expect(mockProcessMultimodal).toHaveBeenCalledWith(input);
            expect(result).toEqual({
                text: 'Analyze this content',
                audio: { data: 'base64audio', format: 'wav' },
                images: [{ url: 'https://example.com/image.jpg' }],
                video: { url: 'https://example.com/video.mp4' }
            });
        });

        it('should convert processed media to content array format', async () => {
            const processedMedia = {
                text: 'Analyze this content',
                audio: { data: 'base64audio', format: 'wav' },
                images: [{ url: 'https://example.com/image.jpg' }],
                video: { url: 'https://example.com/video.mp4' }
            };

            const contentArray = [];

            if (processedMedia.text) {
                contentArray.push({ type: 'text', text: processedMedia.text });
            }
            if (processedMedia.audio) {
                contentArray.push({
                    type: 'audio_url',
                    audio_url: processedMedia.audio
                });
            }
            if (processedMedia.images) {
                processedMedia.images.forEach(img => {
                    contentArray.push({ type: 'image_url', image_url: img });
                });
            }
            if (processedMedia.video) {
                contentArray.push({ type: 'video_url', video_url: processedMedia.video });
            }

            expect(contentArray).toEqual([
                { type: 'text', text: 'Analyze this content' },
                { type: 'audio_url', audio_url: { data: 'base64audio', format: 'wav' } },
                { type: 'image_url', image_url: { url: 'https://example.com/image.jpg' } },
                { type: 'video_url', video_url: { url: 'https://example.com/video.mp4' } }
            ]);
        });
    });

    describe('API Request Formatting', () => {
        it('should format complete API request with multi-modal content', () => {
            const apiRequest = {
                model: 'qwen-omni-turbo',
                messages: [
                    {
                        role: 'system',
                        content: 'You are a helpful assistant that can analyze multiple types of media.'
                    },
                    {
                        role: 'user',
                        content: [
                            { type: 'image_url', image_url: { url: 'https://example.com/image.jpg' } },
                            { type: 'audio_url', audio_url: { url: 'https://example.com/audio.wav' } },
                            { type: 'text', text: 'What is the relationship between this image and audio?' }
                        ]
                    }
                ],
                stream: true,
                stream_options: { include_usage: true },
                modalities: ['text', 'audio'],
                audio: { voice: 'Chelsie', format: 'wav' }
            };

            expect(apiRequest.model).toBe('qwen-omni-turbo');
            expect(apiRequest.messages).toHaveLength(2);
            expect(apiRequest.messages[1].content).toHaveLength(3);
            expect(apiRequest.modalities).toEqual(['text', 'audio']);
            expect(apiRequest.stream).toBe(true);
        });

        it('should handle curl-compatible format as shown in documentation', () => {
            const curlEquivalent = {
                messages: [
                    {
                        role: 'system',
                        content: 'You are a helpful assistant.'
                    },
                    {
                        role: 'user',
                        content: [
                            {
                                type: 'image_url',
                                image_url: {
                                    url: 'https://modelscope.oss-cn-beijing.aliyuncs.com/resource/qwen.png'
                                }
                            },
                            {
                                type: 'audio_url',
                                audio_url: {
                                    url: 'https://qianwen-res.oss-cn-beijing.aliyuncs.com/Qwen2.5-Omni/cough.wav'
                                }
                            },
                            {
                                type: 'text',
                                text: 'What is the text in the illustrate and what is the sound in the audio?'
                            }
                        ]
                    }
                ]
            };

            // Validate the format matches the official documentation example
            const userMessage = curlEquivalent.messages[1];
            expect(userMessage.role).toBe('user');
            expect(Array.isArray(userMessage.content)).toBe(true);
            expect(userMessage.content).toHaveLength(3);

            const [imageContent, audioContent, textContent] = userMessage.content;
            expect(imageContent.type).toBe('image_url');
            expect(audioContent.type).toBe('audio_url');
            expect(textContent.type).toBe('text');
        });
    });

    describe('Error Handling', () => {
        it('should handle missing modality gracefully', () => {
            const content = [];
            const imageUrl = null; // Missing image
            const audioUrl = 'https://example.com/audio.wav';
            const text = 'Analyze the audio';

            // Only add non-null modalities
            if (imageUrl) content.push({ type: 'image_url', image_url: { url: imageUrl } });
            if (audioUrl) content.push({ type: 'audio_url', audio_url: { url: audioUrl } });
            if (text) content.push({ type: 'text', text });

            expect(content).toEqual([
                { type: 'audio_url', audio_url: { url: 'https://example.com/audio.wav' } },
                { type: 'text', text: 'Analyze the audio' }
            ]);
        });

        it('should validate content array is not empty', () => {
            const content = [];
            const imageUrl = null;
            const audioUrl = null;
            const text = null;

            if (imageUrl) content.push({ type: 'image_url', image_url: { url: imageUrl } });
            if (audioUrl) content.push({ type: 'audio_url', audio_url: { url: audioUrl } });
            if (text) content.push({ type: 'text', text });

            // Should have at least one content item
            expect(content.length).toBe(0);

            // In real implementation, this should trigger an error or default text
            const fallbackContent = content.length === 0
                ? [{ type: 'text', text: 'Please provide some content to analyze.' }]
                : content;

            expect(fallbackContent).toEqual([
                { type: 'text', text: 'Please provide some content to analyze.' }
            ]);
        });

        it('should handle invalid URLs gracefully', () => {
            const content = [];
            const invalidImageUrl = 'not-a-valid-url';
            const text = 'This has an invalid image URL';

            // In a real implementation, URL validation should occur
            const isValidUrl = (url) => {
                try {
                    new URL(url);
                    return true;
                } catch {
                    return false;
                }
            };

            if (invalidImageUrl && isValidUrl(invalidImageUrl)) {
                content.push({ type: 'image_url', image_url: { url: invalidImageUrl } });
            }
            if (text) content.push({ type: 'text', text });

            // Should only include valid content
            expect(content).toEqual([
                { type: 'text', text: 'This has an invalid image URL' }
            ]);
        });
    });

    describe('WebSocket Multi-Modal Support', () => {
        it('should format multi-modal content for WebSocket realtime API', async () => {
            const multimodalData = {
                text: 'Analyze this audio and image',
                audio: { data: 'base64audio', format: 'pcm16' },
                images: [{ url: 'https://example.com/image.jpg' }]
            };

            const contentArr = [];
            if (multimodalData.text) {
                contentArr.push({ type: 'input_text', text: multimodalData.text });
            }
            if (multimodalData.audio) {
                contentArr.push({ type: 'input_audio', input_audio: multimodalData.audio });
            }
            if (multimodalData.images) {
                for (let img of multimodalData.images) {
                    contentArr.push({ type: 'image_url', image_url: img });
                }
            }

            expect(contentArr).toEqual([
                { type: 'input_text', text: 'Analyze this audio and image' },
                { type: 'input_audio', input_audio: { data: 'base64audio', format: 'pcm16' } },
                { type: 'image_url', image_url: { url: 'https://example.com/image.jpg' } }
            ]);
        });

        it('should create proper WebSocket message structure', () => {
            const wsMessage = {
                event_id: `event_${Date.now()}`,
                type: 'conversation.item.create',
                item: {
                    type: 'message',
                    role: 'user',
                    content: [
                        { type: 'input_text', text: 'Describe the audio and image' },
                        { type: 'input_audio', input_audio: { data: 'base64audio', format: 'pcm16' } },
                        { type: 'image_url', image_url: { url: 'https://example.com/image.jpg' } }
                    ]
                }
            };

            expect(wsMessage.type).toBe('conversation.item.create');
            expect(wsMessage.item.role).toBe('user');
            expect(wsMessage.item.content).toHaveLength(3);
            expect(wsMessage.event_id).toMatch(/^event_\d+$/);
        });
    });
}); 