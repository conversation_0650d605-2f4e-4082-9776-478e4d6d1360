/**
 * Shared mock configuration for LangChain tests
 * This file provides common mocks used across all LangChain-related test files
 */

// Mock <PERSON> modules for testing
jest.mock('@langchain/openai', () => ({
    ChatOpenAI: jest.fn().mockImplementation(() => ({
        pipe: jest.fn().mockReturnThis(),
        stream: jest.fn(),
        invoke: jest.fn(),
        withConfig: jest.fn().mockReturnThis(),
        bindTools: jest.fn().mockReturnThis(),
        withStructuredOutput: jest.fn().mockReturnThis()
    }))
}));

jest.mock('@langchain/core/output_parsers', () => ({
    StringOutputParser: jest.fn().mockImplementation(() => ({
        pipe: jest.fn().mockReturnThis(),
    })),
    JsonOutputParser: jest.fn().mockImplementation(() => ({
        pipe: jest.fn().mockReturnThis(),
    }))
}));

jest.mock('@langchain/core/prompts', () => ({
    ChatPromptTemplate: {
        fromMessages: jest.fn().mockReturnValue({
            pipe: jest.fn().mockReturnThis(),
            partial: jest.fn().mockReturnThis(),
            formatMessages: jest.fn().mockResolvedValue([
                { _getType: () => 'system', content: 'System prompt' },
                { _getType: () => 'human', content: 'User message' }
            ])
        }),
        fromTemplate: jest.fn().mockReturnValue({
            pipe: jest.fn().mockReturnThis(),
        })
    },
    SystemMessagePromptTemplate: {
        fromTemplate: jest.fn().mockReturnValue({
            format: jest.fn().mockResolvedValue({ content: 'System message' })
        })
    },
    HumanMessagePromptTemplate: {
        fromTemplate: jest.fn().mockReturnValue({
            format: jest.fn().mockResolvedValue({ content: 'Human message' })
        })
    },
    FewShotChatMessagePromptTemplate: jest.fn().mockImplementation(() => ({
        pipe: jest.fn().mockReturnThis(),
    }))
}));

jest.mock('@langchain/core/runnables', () => ({
    RunnableSequence: {
        from: jest.fn().mockReturnValue({
            pipe: jest.fn().mockReturnThis(),
            stream: jest.fn(),
            invoke: jest.fn(),
        })
    }
}));

jest.mock('@langchain/core/messages', () => ({
    HumanMessage: jest.fn().mockImplementation((content) => ({ 
        content, 
        _getType: () => 'human' 
    })),
    SystemMessage: jest.fn().mockImplementation((content) => ({ 
        content, 
        _getType: () => 'system' 
    })),
    AIMessage: jest.fn().mockImplementation((content) => ({ 
        content, 
        _getType: () => 'ai' 
    }))
}));

jest.mock('@langchain/core/tools', () => ({
    tool: jest.fn()
}));

// Mock shared dependencies
jest.mock('../../src/agent/stream/LLMStreamHandler.js', () => ({
    LLMStreamHandler: jest.fn().mockImplementation(() => ({
        handleStreamResponse: jest.fn(),
        getReadyToolCalls: jest.fn().mockReturnValue(['call_123']),
        getAccumulatedToolCall: jest.fn().mockReturnValue({
            name: 'test_tool',
            args: '{"param":"value"}'
        }),
        stop: jest.fn(),
        reset: jest.fn(),
        waitForRelief: jest.fn().mockResolvedValue(true)
    }))
}));

jest.mock('../../src/utils/logger.js', () => ({
    createLogger: jest.fn(() => ({
        info: jest.fn(),
        warn: jest.fn(),
        error: jest.fn(),
        debug: jest.fn()
    }))
}));

// Export common test utilities
module.exports = {
    // Common test data
    mockStreamChunks: [
        { content: 'Hello' },
        { content: ' there!' },
        { content: ' How can I help you?' }
    ],
    
    mockToolCall: {
        id: 'call_123',
        type: 'function',
        function: {
            name: 'animation_selector',
            arguments: JSON.stringify({ animation: 'happy' })
        }
    },
    
    mockPromptMessages: [
        { _getType: () => 'system', content: 'You are a helpful assistant.' },
        { _getType: () => 'human', content: 'Hello!' }
    ]
};
