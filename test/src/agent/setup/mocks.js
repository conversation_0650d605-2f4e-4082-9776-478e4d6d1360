/**
 * Centralized mock setup for agent tests
 * This file contains all mocks used across agent test suites
 */

import { vi } from 'vitest';

// Mock <PERSON> modules for testing
vi.mock('@langchain/openai', () => ({
    ChatOpenAI: vi.fn().mockImplementation(() => ({
        pipe: vi.fn().mockReturnThis(),
        stream: vi.fn(),
        invoke: vi.fn(),
        withConfig: vi.fn().mockReturnThis(),
        bindTools: vi.fn().mockReturnThis(),
        withStructuredOutput: vi.fn().mockReturnThis()
    }))
}));

vi.mock('@langchain/core/output_parsers', () => ({
    StringOutputParser: vi.fn().mockImplementation(() => ({
        pipe: vi.fn().mockReturnThis(),
    })),
    JsonOutputParser: vi.fn().mockImplementation(() => ({
        pipe: vi.fn().mockReturnThis(),
    }))
}));

vi.mock('@langchain/core/prompts', () => ({
    ChatPromptTemplate: {
        fromMessages: vi.fn().mockReturnValue({
            pipe: vi.fn().mockReturnThis(),
            partial: vi.fn().mockReturnThis(),
        }),
        fromTemplate: vi.fn().mockReturnValue({
            pipe: vi.fn().mockReturnThis(),
        })
    },
    FewShotChatMessagePromptTemplate: vi.fn().mockImplementation(() => ({
        pipe: vi.fn().mockReturnThis(),
    }))
}));

vi.mock('@langchain/core/runnables', () => ({
    RunnableSequence: {
        from: vi.fn().mockReturnValue({
            pipe: vi.fn().mockReturnThis(),
            stream: vi.fn(),
            invoke: vi.fn(),
        })
    }
}));

vi.mock('@langchain/core/messages', () => ({
    HumanMessage: vi.fn().mockImplementation((content) => ({ content, _getType: () => 'human' })),
    SystemMessage: vi.fn().mockImplementation((content) => ({ content, _getType: () => 'system' })),
}));

vi.mock('@langchain/core/tools', () => ({
    tool: vi.fn()
}));

vi.mock('zod', () => ({
    z: {
        object: vi.fn().mockReturnValue({
            describe: vi.fn().mockReturnThis()
        }),
        string: vi.fn().mockReturnValue({
            describe: vi.fn().mockReturnThis(),
            optional: vi.fn().mockReturnValue({
                describe: vi.fn().mockReturnThis()
            })
        }),
        number: vi.fn().mockReturnValue({
            min: vi.fn().mockReturnValue({
                max: vi.fn().mockReturnValue({
                    optional: vi.fn().mockReturnValue({
                        describe: vi.fn().mockReturnThis()
                    })
                })
            }),
            describe: vi.fn().mockReturnThis(),
            optional: vi.fn().mockReturnValue({
                describe: vi.fn().mockReturnThis()
            })
        }),
        boolean: vi.fn().mockReturnValue({
            describe: vi.fn().mockReturnThis(),
            optional: vi.fn().mockReturnValue({
                describe: vi.fn().mockReturnThis()
            })
        })
    }
}));

// Mock config with vLLM prioritized for testing
vi.mock('../../../src/config/index.ts', () => ({
    config: {
        endpoints: {
            vllm: 'http://localhost:8000',
            letta: 'http://localhost:8080'
        },
        llm: {
            provider: 'vllm', // Force vLLM for testing
            vllm: {
                model: 'Qwen2.5-Omni-7B'
            },
            letta: {
                preferredModel: 'vllm/Qwen2.5-Omni-7B',
                fallbackModel: 'letta/letta-free',
                embedding: 'letta/letta-free'
            }
        }
    }
}));

// Mock agent dependencies
vi.mock('../../../src/agent/prompt.js', () => ({
    langChainPromptService: {
        createContextAwarePrompt: vi.fn().mockResolvedValue({
            pipe: vi.fn().mockReturnThis(),
        }),
        getConversationHistory: vi.fn().mockReturnValue([]),
        addConversationTurn: vi.fn(),
    }
}));

vi.mock('../../../src/animation/AnimationConfig.js', () => ({
    ANIMATION_REGISTRY: {
        greeting: { name: 'greeting', description: 'Greeting gesture' },
        happy: { name: 'happy', description: 'Happy expression' },
        confused: { name: 'confused', description: 'Confused expression' },
        thinking: { name: 'thinking', description: 'Thinking gesture' }
    }
}));

vi.mock('../../../src/agent/input.js', () => ({
    MultimodalInputProcessor: vi.fn().mockImplementation(() => ({
        processInput: vi.fn().mockResolvedValue({
            isMultimodal: false,
            langchainMessages: [],
            processingInstructions: ''
        }),
        getPrimaryModality: vi.fn().mockReturnValue('text')
    }))
}));

vi.mock('../../../src/agent/tools/index.js', () => ({
    toolManager: {
        getAllTools: vi.fn().mockReturnValue([]),
        getAvailableTools: vi.fn().mockReturnValue([]),
        hasTool: vi.fn().mockReturnValue(true),
        getTool: vi.fn().mockReturnValue({
            func: vi.fn().mockResolvedValue({ result: 'tool executed' })
        })
    },
    registerAnimationTool: vi.fn().mockReturnValue({
        name: 'animation_selector',
        description: 'Select animation based on context',
    })
}));

vi.mock('../../../src/agent/stream/LLMStreamHandler.js', () => ({
    LLMStreamHandler: vi.fn().mockImplementation(() => ({
        handleStreamResponse: vi.fn(),
        getReadyToolCalls: vi.fn().mockReturnValue(['call_123']),
        getAccumulatedToolCall: vi.fn().mockReturnValue({
            name: 'test_tool',
            args: '{"param":"value"}'
        }),
        stop: vi.fn()
    }))
}));

vi.mock('../../../src/utils/logger.js', () => ({
    createLogger: vi.fn(() => ({
        info: vi.fn(),
        warn: vi.fn(),
        error: vi.fn(),
        debug: vi.fn(),
        setLogLevel: vi.fn()
    })),
    LogLevel: {
        DEBUG: 'debug',
        INFO: 'info',
        WARN: 'warn',
        ERROR: 'error'
    }
}));

export const mockVllmConfig = {
    vllmEndpoint: 'http://localhost:8000',
    model: 'Qwen2.5-Omni-7B',
    temperature: 0.7,
    maxTokens: 2048
};

export const mockStreamResponse = {
    content: 'Test response',
    tool_calls: [{
        id: 'call_123',
        type: 'function',
        function: {
            name: 'animation_selector',
            arguments: '{"animation":"happy"}'
        }
    }]
};

export const mockMultimodalInput = {
    text: 'Hello, how are you?',
    images: [],
    audio: null
};
