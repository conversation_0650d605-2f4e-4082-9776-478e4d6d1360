/**
 * Mock endpoints configuration for testing
 */

export const endpoints = {
    letta: 'http://***********:20096/',
    vllm: 'http://***********:20095/',
    ollama: 'http://***********:20054/',
    speechRecog: 'http://***********:20100/',
    sparkTTS: 'http://***********:20099/',
    anyTo3D: 'http://*************:20210/',
    tripoDoll: 'http://***********:9123/',
    mem0: 'https://api.mem0.ai'
};

export const llmProvider = 'letta-vllm';
export const ollamaDefaultModel = 'qwen2.5:72b';
