/**
 * Mock configuration for testing
 * Provides the config object needed by agent/core.js during tests
 */

export const config = {
    AnyTo3DGradioEndpoint: 'http://*************:20210/',
    downloadConfig: {
        port: 2994,
        assetsDir: 'public/assets',
        actualPort: 2994
    },
    host: 'localhost',
    port: 3002,
    sshKeyPath: '',
    defaultSeed: 42,
    modelConfig: {
        baseUrl: '/assets',
        mediapipe: {
            holistic: {
                modelPath: 'models/mediapipe/holistic_landmarker.task',
                fallbackPath: 'https://storage.googleapis.com/mediapipe-models/holistic_landmarker/holistic_landmarker/float16/1/holistic_landmarker.task'
            },
            pose: {
                modelPath: 'models/mediapipe/pose_landmarker_lite.task',
                fallbackPath: 'https://storage.googleapis.com/mediapipe-models/pose_landmarker/pose_landmarker_lite/float16/1/pose_landmarker_lite.task'
            },
            hand: {
                modelPath: 'models/mediapipe/hand_landmarker.task',
                fallbackPath: 'https://storage.googleapis.com/mediapipe-models/hand_landmarker/hand_landmarker/float16/1/hand_landmarker.task'
            }
        }
    },
    endpoints: {
        letta: 'http://***********:20096/',
        vllm: 'http://***********:20095/',
        ollama: 'http://***********:20054/',
        speechRecog: 'http://***********:20100/',
        sparkTTS: 'http://***********:20099/',
        anyTo3D: 'http://*************:20210/',
        tripoDoll: 'http://***********:9123/',
        mem0: 'https://api.mem0.ai'
    },
    llm: {
        defaultModel: 'qwen2.5:72b',
        provider: 'vllm',
        vllm: {
            model: 'Qwen2.5-Omni-7B',
            temperature: 0.7,
            maxTokens: 2048,
            timeout: 30000
        },
        letta: {
            preferredModel: 'vllm/Qwen2.5-Omni-7B',
            fallbackModel: 'letta/letta-free',
            embedding: 'letta/letta-free'
        }
    }
};
