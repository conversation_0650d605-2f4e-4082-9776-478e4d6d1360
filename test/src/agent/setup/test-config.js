/**
 * Agent Test Configuration
 * Shared configuration for all agent tests with apiProxy.ts integration
 */

import { fetchLLMApi } from '@/utils/apiProxy.ts';

// Test environment configuration
export const TEST_CONFIG = {
    // Model settings
    model: 'Qwen/Qwen2.5-Omni-7B',
    temperature: 0.7,
    maxTokens: 150,

    // Test behavior
    timeout: 15000, // 15 second timeout for real API calls
    useRealAPI: process.env.TEST_REAL_API === 'true',
    skipSlowTests: process.env.SKIP_SLOW_TESTS === 'true',

    // Concurrency limits
    maxConcurrentRequests: 3,

    // Retry configuration
    maxRetries: 2,
    retryDelay: 1000,

    // API endpoints (via apiProxy)
    endpoints: {
        models: 'v1/models',
        chatCompletions: 'v1/chat/completions',
        embeddings: 'v1/embeddings'
    }
};

// Test utilities
export class TestUtils {
    /**
     * Check if real API is available for testing
     */
    static async checkAPIAvailability() {
        if (!TEST_CONFIG.useRealAPI) {
            return { available: false, reason: 'Real API testing disabled' };
        }

        try {
            const response = await fetchLLMApi(TEST_CONFIG.endpoints.models);
            if (response.ok) {
                return { available: true, reason: 'API is available' };
            } else {
                return { available: false, reason: `API returned ${response.status}` };
            }
        } catch (error) {
            return { available: false, reason: error.message };
        }
    }

    /**
     * Create a test LLM request with standard format
     */
    static createTestRequest(content, options = {}) {
        return {
            model: options.model || TEST_CONFIG.model,
            messages: [{ role: 'user', content }],
            stream: options.stream !== undefined ? options.stream : true,
            max_tokens: options.maxTokens || TEST_CONFIG.maxTokens,
            temperature: options.temperature || TEST_CONFIG.temperature
        };
    }

    /**
     * Make a test streaming request
     */
    static async makeStreamingRequest(content, options = {}) {
        const request = this.createTestRequest(content, { ...options, stream: true });

        const response = await fetchLLMApi(TEST_CONFIG.endpoints.chatCompletions, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(request)
        });

        if (!response.ok) {
            throw new Error(`API request failed: ${response.status} ${response.statusText}`);
        }

        return response;
    }

    /**
     * Make a test non-streaming request
     */
    static async makeNonStreamingRequest(content, options = {}) {
        const request = this.createTestRequest(content, { ...options, stream: false });

        const response = await fetchLLMApi(TEST_CONFIG.endpoints.chatCompletions, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(request)
        });

        if (!response.ok) {
            throw new Error(`API request failed: ${response.status} ${response.statusText}`);
        }

        return await response.json();
    }

    /**
     * Collect streaming chunks with timeout
     */
    static async collectStreamChunks(response, options = {}) {
        const maxChunks = options.maxChunks || 10;
        const timeout = options.timeout || TEST_CONFIG.timeout;

        const chunks = [];
        const reader = response.body.getReader();

        const timeoutPromise = new Promise((_, reject) => {
            setTimeout(() => reject(new Error('Stream timeout')), timeout);
        });

        try {
            while (chunks.length < maxChunks) {
                const readPromise = reader.read();
                const { done, value } = await Promise.race([readPromise, timeoutPromise]);

                if (done) break;

                if (value) {
                    chunks.push(value);
                }
            }
        } finally {
            reader.releaseLock();
        }

        return chunks;
    }

    /**
     * Create mock services for testing
     */
    static createMockServices() {
        return {
            ttsService: {
                speak: vi.fn().mockResolvedValue(true),
                stop: vi.fn(),
                setVoice: vi.fn()
            },
            audioPlayer: {
                play: vi.fn().mockResolvedValue(true),
                stop: vi.fn(),
                setVolume: vi.fn()
            },
            animationController: {
                playAnimation: vi.fn().mockResolvedValue(true),
                stopAnimation: vi.fn(),
                setMood: vi.fn()
            }
        };
    }

    /**
     * Wait for a condition with timeout
     */
    static async waitFor(condition, options = {}) {
        const timeout = options.timeout || 5000;
        const interval = options.interval || 100;
        const startTime = Date.now();

        while (Date.now() - startTime < timeout) {
            if (await condition()) {
                return true;
            }
            await new Promise(resolve => setTimeout(resolve, interval));
        }

        throw new Error(`Condition not met within ${timeout}ms`);
    }

    /**
     * Retry an operation with exponential backoff
     */
    static async retry(operation, options = {}) {
        const maxRetries = options.maxRetries || TEST_CONFIG.maxRetries;
        const baseDelay = options.baseDelay || TEST_CONFIG.retryDelay;

        let lastError;

        for (let attempt = 0; attempt <= maxRetries; attempt++) {
            try {
                return await operation();
            } catch (error) {
                lastError = error;

                if (attempt === maxRetries) {
                    break;
                }

                const delay = baseDelay * Math.pow(2, attempt);
                await new Promise(resolve => setTimeout(resolve, delay));
            }
        }

        throw lastError;
    }

    /**
     * Generate test session ID
     */
    static generateSessionId(prefix = 'test') {
        return `${prefix}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    }

    /**
     * Log test performance metrics
     */
    static logPerformance(testName, startTime, additionalMetrics = {}) {
        const duration = Date.now() - startTime;
        console.log(`[Performance] ${testName}: ${duration}ms`, additionalMetrics);
        return duration;
    }
}

// Test fixtures
export const TEST_FIXTURES = {
    // Simple test inputs
    simple: {
        greeting: 'Hello, how are you?',
        question: 'What is the capital of France?',
        instruction: 'Count to three',
        multimodal: 'Describe what you see in this image'
    },

    // Complex test inputs
    complex: {
        conversation: [
            'My name is Alice',
            'What is my name?'
        ],
        toolRequest: 'Show me a happy animation',
        longForm: 'Tell me a detailed story about a brave knight',
        codeRequest: 'Write a Python function to sort a list'
    },

    // Edge cases
    edgeCases: {
        empty: '',
        null: null,
        undefined: undefined,
        veryLong: 'A'.repeat(1000),
        specialChars: '!@#$%^&*()_+{}|:"<>?[]\\;\',./',
        unicode: '你好世界 🌍 안녕하세요',
        malformed: { invalidStructure: true }
    },

    // Expected responses patterns
    patterns: {
        greeting: /hello|hi|hey/i,
        question: /paris|france/i,
        counting: /one|two|three|1|2|3/i,
        error: /error|failed|sorry/i
    }
};

// Mock configurations
export const MOCK_CONFIG = {
    // LangGraph agent service mock
    agentService: {
        model: TEST_CONFIG.model,
        temperature: TEST_CONFIG.temperature,
        maxTokens: TEST_CONFIG.maxTokens,
        autoRegisterTools: true,
        useLangGraphStreaming: true
    },

    // Stream processor mock (now native in core service)
    streamProcessor: {
        streamMode: 'messages',
        isNative: true,
        processLangGraphStream: 'function'
    }
};

// Test environment setup/teardown helpers
export class TestEnvironment {
    static async setup() {
        // Check API availability
        const apiStatus = await TestUtils.checkAPIAvailability();

        if (!apiStatus.available && TEST_CONFIG.useRealAPI) {
            console.warn(`⚠️ Real API not available: ${apiStatus.reason}`);
            console.warn('Tests will run with limited functionality');
        }

        return {
            apiAvailable: apiStatus.available,
            reason: apiStatus.reason
        };
    }

    static async teardown() {
        // Cleanup any global test state
        console.log('Test environment cleanup complete');
    }

    static skipIfNoAPI(reason = 'API not available') {
        if (!TEST_CONFIG.useRealAPI) {
            return { skip: true, reason: 'Real API testing disabled' };
        }

        return { skip: false };
    }

    static skipIfSlow(reason = 'Slow test skipped') {
        if (TEST_CONFIG.skipSlowTests) {
            return { skip: true, reason };
        }

        return { skip: false };
    }
}

export default {
    TEST_CONFIG,
    TestUtils,
    TEST_FIXTURES,
    MOCK_CONFIG,
    TestEnvironment
}; 