/**
 * Mock environment utility for testing
 * Replaces import.meta.env with process.env for Jest compatibility
 */

export function getEnvVar(key, defaultValue = '') {
    return process.env[key] || defaultValue;
}

export function getEnvVarAsNumber(key, defaultValue = 0) {
    const value = process.env[key];
    return value ? parseInt(value, 10) : defaultValue;
}

export function logEnvironmentVariables() {
    // Mock implementation - no logging in tests
    return;
}
