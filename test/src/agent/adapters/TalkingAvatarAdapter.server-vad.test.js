/**
 * Comprehensive test suite for TalkingAvatarAdapter VAD integration
 * Tests server VAD integration, HTTP VAD to model.invoke flow, multimodal handling, and Qwen-Omni compliance
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { TalkingAvatarAdapter } from '@/agent/adapters/TalkingAvatarAdapter.js';

// Mock environment for testing
const mockEnv = {
    VITE_DASHSCOPE_API_KEY: 'test-api-key',
    VITE_MODEL_PROVIDER: 'aliyun',
    VITE_ALIYUN_MODEL: 'qwen-omni-turbo'
};

// Mock global environment (simplified approach)
globalThis.process = globalThis.process || { env: {} };
Object.assign(globalThis.process.env, mockEnv);

// Mock dependencies
vi.mock('@/media', () => ({
    MediaCaptureManager: vi.fn().mockImplementation((options) => ({
        options: options || {},
        startCapture: vi.fn().mockResolvedValue(true),
        stopCapture: vi.fn().mockResolvedValue(true),
        dispose: vi.fn(),
        getMediaStream: vi.fn().mockReturnValue({
            getAudioTracks: () => [{ label: 'Test Audio', enabled: true, readyState: 'live' }],
            getVideoTracks: () => [{ label: 'Test Video', enabled: true, readyState: 'live' }]
        }),
        getAudioTrack: vi.fn().mockReturnValue({ label: 'Test Audio', enabled: true, readyState: 'live' }),
        getVideoTrack: vi.fn().mockReturnValue({ label: 'Test Video', enabled: true, readyState: 'live' }),
        isAudioStreamingActive: vi.fn().mockReturnValue(true),
        vadMode: 'server',
        targetSampleRate: 16000
    }))
}));

vi.mock('@/agent/core.js', () => ({
    LangGraphAgentService: vi.fn().mockImplementation(() => ({
        initialize: vi.fn().mockResolvedValue(true),
        model: {
            apiMode: 'websocket',
            model: 'qwen-omni-realtime',
            isRealtimeModeActive: vi.fn().mockReturnValue(true),
            sendRealtimeAudio: vi.fn().mockResolvedValue(true),
            enableAutoRecovery: vi.fn(),
            invoke: vi.fn().mockResolvedValue({
                generations: [{
                    text: 'Test response',
                    audio: 'base64audiodata',
                    message: { content: 'Test response' }
                }]
            }),
            modalities: ['text', 'audio']
        }
    }))
}));

vi.mock('@/utils/logger.js', () => ({
    createLogger: vi.fn().mockReturnValue({
        info: vi.fn(),
        debug: vi.fn(),
        warn: vi.fn(),
        error: vi.fn(),
        setLogLevel: vi.fn()
    }),
    LogLevel: { DEBUG: 'DEBUG' }
}));

global.console = {
    log: vi.fn(),
    warn: vi.fn(),
    error: vi.fn(),
    debug: vi.fn()
};

describe('TalkingAvatarAdapter VAD Integration', () => {
    let adapter;
    let mockTalkingAvatar;
    let mockAgentService;
    let mockMediaCaptureManager;
    let mockModel;
    let mockFetch;
    let consoleWarnSpy;

    beforeEach(() => {
        // Mock TalkingAvatar
        mockTalkingAvatar = {
            isListening: false,
            isSpeaking: false,
            waitingForResponse: false,
            avatarMesh: {
                userData: { gender: 'male' }
            },
            _updateListeningUI: vi.fn(),
            _updateStatus: vi.fn(),
            _setSpeakingState: vi.fn(),
            onConversationUpdate: vi.fn(),
            onTranscriptReceived: vi.fn()
        };

        // Setup fetch mock for HTTP tests
        mockFetch = vi.fn(() =>
            Promise.resolve({
                ok: true,
                json: () => Promise.resolve({
                    content: 'Test response',
                    audio: 'base64audiodata',
                    metadata: { model: 'qwen-omni-turbo' }
                })
            })
        );
        global.fetch = mockFetch;

        // Mock model with invoke method for HTTP tests
        mockModel = {
            invoke: vi.fn().mockResolvedValue({
                generations: [{
                    text: 'Test response',
                    audio: 'base64audiodata',
                    message: { content: 'Test response' }
                }]
            }),
            apiMode: 'http',
            model: 'qwen-omni-turbo',
            modalities: ['text', 'audio']
        };

        // Mock agent service for HTTP tests
        mockAgentService = {
            model: mockModel
        };

        consoleWarnSpy = vi.spyOn(console, 'warn').mockImplementation();
    });

    afterEach(() => {
        if (adapter) {
            adapter.dispose?.();
            adapter = null;
        }
        vi.clearAllMocks();
        consoleWarnSpy.mockRestore();
        global.fetch = undefined;
    });

    describe('Server VAD Callback Setup Timing', () => {
        it('should create MediaCaptureManager with onAudioData callback from the start', async () => {
            adapter = new TalkingAvatarAdapter(mockTalkingAvatar, {
                aliyunModel: 'qwen-omni-realtime'
            });

            await adapter.initialize();

            // Mock the internal MediaCaptureManager import
            const { MediaCaptureManager } = await import('@/media');

            // Start realtime listening
            const result = await adapter.startRealtimeListening();

            expect(result).toBe(true);
            expect(MediaCaptureManager).toHaveBeenCalled();

            // Get the constructor call arguments
            const constructorArgs = MediaCaptureManager.mock.calls[0][0];

            // Verify that onAudioData callback was provided in constructor
            expect(constructorArgs).toHaveProperty('onAudioData');
            expect(typeof constructorArgs.onAudioData).toBe('function');
            expect(constructorArgs.vadMode).toBe('server');
        });

        it('should NOT show warning when onAudioData is set in constructor options', async () => {
            adapter = new TalkingAvatarAdapter(mockTalkingAvatar, {
                aliyunModel: 'qwen-omni-realtime'
            });

            await adapter.initialize();
            await adapter.startRealtimeListening();

            // Verify no warning about missing onAudioData callback
            expect(consoleWarnSpy).not.toHaveBeenCalledWith(
                expect.stringContaining('No onAudioData callback provided for server VAD streaming')
            );
        });

        it('should set up onAudioData callback to send PCM16 to WebSocket', async () => {
            adapter = new TalkingAvatarAdapter(mockTalkingAvatar, {
                aliyunModel: 'qwen-omni-realtime'
            });

            await adapter.initialize();

            // Mock the agent service model
            const mockSendRealtimeAudio = vi.fn();
            adapter.agentService.model.sendRealtimeAudio = mockSendRealtimeAudio;

            await adapter.startRealtimeListening();

            const { MediaCaptureManager } = await import('@/media');
            const constructorArgs = MediaCaptureManager.mock.calls[0][0];
            const onAudioData = constructorArgs.onAudioData;

            // Test the callback with mock PCM16 data
            const mockPCM16Buffer = new ArrayBuffer(1024);
            onAudioData(mockPCM16Buffer);

            // Verify it calls sendRealtimeAudio with the PCM16 data
            expect(mockSendRealtimeAudio).toHaveBeenCalledWith(mockPCM16Buffer);
        });
    });

    describe('HTTP VAD to Model Invoke Integration', () => {
        it('should call model.invoke when HTTP VAD speech ends', async () => {
            console.log('🧪 [INTEGRATION TEST] Testing HTTP VAD to model.invoke flow');

            // Create adapter with HTTP mode
            adapter = new TalkingAvatarAdapter(mockTalkingAvatar, {
                agentService: mockAgentService
            });
            adapter.agentService = mockAgentService;

            // Simulate the speech end flow that happens in MediaCaptureManager
            // This is the same data that would come from VAD
            const mockAudioData = {
                sampleRate: 48000,
                channelCount: 1,
                length: 48000, // 1 second of audio
                getChannelData: () => new Float32Array(48000).fill(0.1) // Fake audio data
            };

            // Mock WAV blob creation (this normally happens in MediaCaptureManager)
            const mockWavBlob = new Blob(['fake wav data'], { type: 'audio/wav' });
            mockWavBlob.arrayBuffer = () => Promise.resolve(new ArrayBuffer(1024));

            // Test the speech end processing logic directly
            console.log('🧪 [INTEGRATION TEST] Simulating speech end with audio data');

            try {
                // This simulates what happens in the onSpeechEnd callback
                const arrayBuffer = await mockWavBlob.arrayBuffer();
                const base64Audio = 'dGVzdCBhdWRpbyBkYXRh'; // "test audio data" in base64

                // Build the message exactly as TalkingAvatarAdapter does
                const message = {
                    role: 'user',
                    content: undefined // No text content, just audio
                };

                const invokeOptions = {
                    modalities: ['text', 'audio'],
                    audioConfig: { voice: 'Ethan', format: 'wav' },
                    stream: true,
                    stream_options: { include_usage: true }
                };

                console.log('🧪 [INTEGRATION TEST] Calling model.invoke with:', { message, invokeOptions });

                // Call model.invoke directly (this is what TalkingAvatarAdapter does)
                const result = await mockModel.invoke([message], invokeOptions);

                console.log('🧪 [INTEGRATION TEST] model.invoke result:', result);

                // Verify that model.invoke was called with correct parameters
                expect(mockModel.invoke).toHaveBeenCalledTimes(1);
                expect(mockModel.invoke).toHaveBeenCalledWith([message], invokeOptions);

                // Verify the call was successful
                expect(result).toBeDefined();
                expect(result.generations).toBeDefined();
                expect(result.generations[0].text).toBe('Test response');

                console.log('🧪 [INTEGRATION TEST] ✅ Model invoke call verified');

            } catch (error) {
                console.error('🧪 [INTEGRATION TEST] ❌ Test failed:', error);
                throw error;
            }
        });

        it('should handle model.invoke errors gracefully', async () => {
            console.log('🧪 [INTEGRATION TEST] Testing error handling in model.invoke');

            // Make model.invoke throw an error
            mockModel.invoke.mockRejectedValueOnce(new Error('Network timeout'));

            const message = { role: 'user', content: undefined };
            const invokeOptions = {
                modalities: ['text', 'audio'],
                audioConfig: { voice: 'Ethan', format: 'wav' }
            };

            try {
                await mockModel.invoke([message], invokeOptions);
                expect.fail('Expected error to be thrown');
            } catch (error) {
                expect(error.message).toBe('Network timeout');
                expect(mockModel.invoke).toHaveBeenCalledTimes(1);
                console.log('🧪 [INTEGRATION TEST] ✅ Error handling verified');
            }
        });

        it('should verify adapter has access to model and agentService', () => {
            console.log('🧪 [INTEGRATION TEST] Testing adapter configuration');

            adapter = new TalkingAvatarAdapter(mockTalkingAvatar, {
                agentService: mockAgentService
            });
            adapter.agentService = mockAgentService;

            expect(adapter).toBeDefined();
            expect(adapter.agentService).toBeDefined();
            expect(adapter.agentService.model).toBeDefined();
            expect(typeof adapter.agentService.model.invoke).toBe('function');

            console.log('🧪 [INTEGRATION TEST] Adapter configuration:', {
                hasAdapter: !!adapter,
                hasAgentService: !!adapter.agentService,
                hasModel: !!adapter.agentService?.model,
                hasInvoke: typeof adapter.agentService?.model?.invoke,
                modelApiMode: adapter.agentService?.model?.apiMode
            });

            console.log('🧪 [INTEGRATION TEST] ✅ Adapter configuration verified');
        });
    });

    describe('Multimodal Support (Qwen-Omni Compliance)', () => {
        it('should support audio + video capture for multimodal input', async () => {
            adapter = new TalkingAvatarAdapter(mockTalkingAvatar, {
                aliyunModel: 'qwen-omni-realtime',
                enableVideo: true
            });

            await adapter.initialize();
            await adapter.startRealtimeListening();

            const { MediaCaptureManager } = await import('@/media');
            const constructorArgs = MediaCaptureManager.mock.calls[0][0];

            // Verify both audio and video are configured
            expect(constructorArgs.audio).toBeDefined();
            expect(constructorArgs.video).toBeDefined();
        });

        it('should handle Qwen-Omni server_vad lifecycle events', async () => {
            adapter = new TalkingAvatarAdapter(mockTalkingAvatar, {
                aliyunModel: 'qwen-omni-realtime'
            });

            await adapter.initialize();

            // Verify realtime callbacks are set up for Qwen-Omni lifecycle
            expect(adapter.realtimeCallbacks).toBeDefined();
            expect(adapter.realtimeCallbacks.onVoiceActivityDetected).toBeDefined();
            expect(adapter.realtimeCallbacks.onVoiceActivityStopped).toBeDefined();
            expect(adapter.realtimeCallbacks.onTranscriptReceived).toBeDefined();
            expect(adapter.realtimeCallbacks.onAudioReceived).toBeDefined();
        });

        it('should handle input_audio_buffer.speech_started event', async () => {
            adapter = new TalkingAvatarAdapter(mockTalkingAvatar, {
                aliyunModel: 'qwen-omni-realtime'
            });

            await adapter.initialize();

            // Simulate server VAD speech started event
            adapter.realtimeCallbacks.onVoiceActivityDetected();

            // Verify TalkingAvatar state updates
            expect(mockTalkingAvatar._updateListeningUI).toHaveBeenCalledWith(true);
            expect(mockTalkingAvatar._updateStatus).toHaveBeenCalledWith('Listening (server VAD)...');
            expect(mockTalkingAvatar.isListening).toBe(true);
        });

        it('should handle input_audio_buffer.speech_stopped event', async () => {
            adapter = new TalkingAvatarAdapter(mockTalkingAvatar, {
                aliyunModel: 'qwen-omni-realtime'
            });

            await adapter.initialize();

            // Simulate server VAD speech stopped event
            adapter.realtimeCallbacks.onVoiceActivityStopped();

            // Verify processing status update
            expect(mockTalkingAvatar._updateStatus).toHaveBeenCalledWith('Processing speech (server committing audio)...');
        });

        it('should handle response.audio.delta streaming', async () => {
            adapter = new TalkingAvatarAdapter(mockTalkingAvatar, {
                aliyunModel: 'qwen-omni-realtime'
            });

            await adapter.initialize();

            // Mock processLLMResponseAudio
            const mockProcessLLMResponseAudio = vi.fn().mockResolvedValue({ audioPlayed: true });

            // Simulate receiving audio delta from Qwen-Omni
            const mockAudioData = new ArrayBuffer(1024);
            adapter.realtimeCallbacks.onAudioReceived(mockAudioData);

            // Verify speaking state is set
            expect(mockTalkingAvatar._setSpeakingState).toHaveBeenCalledWith(true);
            expect(mockTalkingAvatar._updateStatus).toHaveBeenCalledWith('Speaking (realtime audio)...');
        });
    });

    describe('WebSocket Mode Detection', () => {
        it('should use WebSocket mode for qwen-omni-realtime models', async () => {
            adapter = new TalkingAvatarAdapter(mockTalkingAvatar, {
                aliyunModel: 'qwen-omni-realtime'
            });

            await adapter.initialize();

            // Verify WebSocket mode is set
            expect(adapter.agentService.model.apiMode).toBe('websocket');
        });

        it('should call startRealtimeListening for WebSocket realtime models', async () => {
            adapter = new TalkingAvatarAdapter(mockTalkingAvatar, {
                aliyunModel: 'qwen-omni-realtime'
            });

            await adapter.initialize();

            // Mock startRealtimeListening
            const mockStartRealtimeListening = vi.fn().mockResolvedValue(true);
            adapter.startRealtimeListening = mockStartRealtimeListening;

            const result = await adapter.startListening();

            expect(mockStartRealtimeListening).toHaveBeenCalled();
            expect(result).toBe(true);
        });
    });

    describe('Resource Management', () => {
        it('should properly dispose MediaCaptureManager on stop', async () => {
            adapter = new TalkingAvatarAdapter(mockTalkingAvatar, {
                aliyunModel: 'qwen-omni-realtime'
            });

            await adapter.initialize();
            await adapter.startRealtimeListening();

            // Get the MediaCaptureManager instance
            const mockInstance = adapter.mediaCaptureManager;

            await adapter.stopRealtimeListening();

            // Verify proper cleanup
            expect(mockInstance.stopCapture).toHaveBeenCalled();
            expect(mockInstance.dispose).toHaveBeenCalled();
            expect(adapter.mediaCaptureManager).toBeNull();
        });

        it('should stop audio streaming when listening is stopped', async () => {
            adapter = new TalkingAvatarAdapter(mockTalkingAvatar, {
                aliyunModel: 'qwen-omni-realtime'
            });

            await adapter.initialize();
            await adapter.startRealtimeListening();

            adapter.isListeningStopped = true;

            // Simulate audio data processing when stopped
            const result = await adapter._processAudioData(new Float32Array([0.1, 0.2]));

            // Should not process audio when stopped
            expect(result).toBeUndefined();
        });
    });

    describe('Error Handling', () => {
        it('should handle MediaCaptureManager creation failures gracefully', async () => {
            // Mock MediaCaptureManager to throw error
            const { MediaCaptureManager } = await import('@/media');
            MediaCaptureManager.mockImplementationOnce(() => {
                throw new Error('MediaCaptureManager creation failed');
            });

            adapter = new TalkingAvatarAdapter(mockTalkingAvatar, {
                aliyunModel: 'qwen-omni-realtime'
            });

            await adapter.initialize();

            const result = await adapter.startRealtimeListening();

            expect(result).toBe(false);
            expect(mockTalkingAvatar._updateStatus).toHaveBeenCalledWith('Failed to start listening');
        });

        it('should handle audio processing errors gracefully', async () => {
            adapter = new TalkingAvatarAdapter(mockTalkingAvatar, {
                aliyunModel: 'qwen-omni-realtime'
            });

            await adapter.initialize();

            // Mock sendRealtimeAudio to throw error
            adapter.agentService.model.sendRealtimeAudio = vi.fn().mockRejectedValue(new Error('WebSocket error'));

            const result = await adapter._processAudioData(new Float32Array([0.1, 0.2]));

            expect(result).toBe(false);
        });
    });
}); 