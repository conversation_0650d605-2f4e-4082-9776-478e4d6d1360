/**
 * Functional Test for TTS Tools
 * Tests real-world scenarios and end-to-end functionality
 */

// Use shared mocks for LangChain components
require('../../setup/mocks');

import { vi } from 'vitest';

// Create more realistic mocks that simulate actual TTS behavior
const createRealisticTTSMock = () => {
    const audioQueue = [];
    let isProcessing = false;
    let processingStats = {
        totalRequests: 0,
        successfulRequests: 0,
        averageLatency: 0,
        lastRequestTime: null
    };

    return {
        synthesizeSpeech: vi.fn(async ({ text, voice = 'default', speed = 1.0, pitch = 1.0 }) => {
            processingStats.totalRequests++;
            processingStats.lastRequestTime = Date.now();

            // Simulate processing time based on text length
            const processingTime = Math.max(100, text.length * 2);
            await new Promise(resolve => setTimeout(resolve, processingTime));

            // Update average latency
            processingStats.averageLatency =
                (processingStats.averageLatency + processingTime) / 2;

            // Simulate occasional failures (5% failure rate)
            if (Math.random() < 0.05) {
                return {
                    success: false,
                    error: 'Temporary synthesis error'
                };
            }

            // Calculate realistic audio properties
            const estimatedDuration = text.length * 0.08; // ~12.5 words per second
            const audioSize = Math.floor(estimatedDuration * 16000 * 2); // 16kHz 16-bit

            processingStats.successfulRequests++;

            return {
                success: true,
                audioData: new ArrayBuffer(audioSize),
                format: voice.includes('premium') ? 'mp3' : 'wav',
                duration: estimatedDuration,
                voice: voice,
                metadata: {
                    processingTime,
                    speed,
                    pitch
                }
            };
        }),

        processAudioChunk: vi.fn(async (chunkData, options = {}) => {
            const { sequenceNumber = 0, isLast = false } = options;

            // Simulate chunk processing delay
            await new Promise(resolve => setTimeout(resolve, 50));

            audioQueue.push({
                data: chunkData,
                sequence: sequenceNumber,
                timestamp: Date.now()
            });

            if (isLast) {
                const totalDuration = audioQueue.length * 0.5; // 0.5 seconds per chunk
                return {
                    success: true,
                    processed: true,
                    completed: true,
                    totalDuration,
                    chunksProcessed: audioQueue.length
                };
            }

            return {
                success: true,
                processed: true,
                queuePosition: audioQueue.length
            };
        }),

        queueAudio: vi.fn(async ({ action }) => {
            switch (action) {
                case 'status':
                    return {
                        action: 'status',
                        queueLength: audioQueue.length,
                        isProcessing,
                        estimatedWaitTime: audioQueue.length * 0.5
                    };

                case 'clear':
                    const cleared = audioQueue.length;
                    audioQueue.length = 0;
                    return {
                        action: 'clear',
                        cleared
                    };

                case 'pause':
                    isProcessing = false;
                    return {
                        action: 'pause',
                        paused: true
                    };

                case 'resume':
                    isProcessing = true;
                    return {
                        action: 'resume',
                        resumed: true
                    };

                default:
                    return {
                        success: false,
                        error: 'Unknown queue action'
                    };
            }
        }),

        getServiceStatus: vi.fn(async () => {
            const successRate = processingStats.totalRequests > 0
                ? processingStats.successfulRequests / processingStats.totalRequests
                : 1.0;

            return {
                isReady: true,
                currentProvider: 'microsoft',
                performance: {
                    averageLatency: Math.round(processingStats.averageLatency),
                    successRate: Math.round(successRate * 100) / 100,
                    totalRequests: processingStats.totalRequests,
                    queueLength: audioQueue.length
                },
                capabilities: {
                    streaming: true,
                    voiceCloning: false,
                    realTime: true
                }
            };
        }),

        isReady: vi.fn(() => true),
        getSupportedVoices: vi.fn(() => [
            { id: 'sarah', name: 'Sarah', language: 'en-US', gender: 'female' },
            { id: 'david', name: 'David', language: 'en-US', gender: 'male' },
            { id: 'emma', name: 'Emma', language: 'en-GB', gender: 'female' },
            { id: 'premium-aria', name: 'Aria Premium', language: 'en-US', gender: 'female', premium: true }
        ])
    };
};

const mockTTSService = createRealisticTTSMock();

// Mock TTS service module
vi.mock('../../../../src/modules/talkinghead/src/ttsServices.js', () => ({
    TTSService: vi.fn().mockImplementation(() => mockTTSService)
}));

// Import TTS tools
import {
    textToSpeechTool,
    audioChunkProcessorTool,
    ttsQueueManagerTool,
    ttsServiceStatusTool
} from '@/agent/tools/tts.js';

describe('TTS Tools Functional Tests', () => {
    beforeEach(() => {
        // Reset mocks but keep the realistic behavior
        vi.clearAllMocks();
    });

    describe('Real-world Text-to-Speech Scenarios', () => {
        test('should handle short conversational response', async () => {
            const result = await textToSpeechTool.func({
                text: 'Hello! How can I help you today?'
            });

            expect(result.success).toBe(true);
            expect(result.audioInfo.duration).toBeGreaterThan(0);
            expect(result.audioInfo.size).toBeGreaterThan(0);
            expect(result.audioInfo.format).toBe('wav');
        });

        test('should handle long explanation text', async () => {
            const longText = `
                Artificial intelligence is a rapidly evolving field that encompasses 
                machine learning, natural language processing, computer vision, and 
                robotics. The development of AI systems requires careful consideration 
                of ethical implications, data privacy, and potential societal impacts. 
                As we continue to advance these technologies, it's crucial to maintain 
                a balance between innovation and responsible development practices.
            `.trim();

            const result = await textToSpeechTool.func({
                text: longText,
                voice: 'sarah',
                speed: 1.1
            });

            expect(result.success).toBe(true);
            expect(result.audioInfo.duration).toBeGreaterThan(10); // Long text should take more than 10 seconds
            expect(mockTTSService.synthesizeSpeech).toHaveBeenCalledWith({
                text: longText,
                voice: 'sarah',
                speed: 1.1,
                pitch: 1.0
            });
        });

        test('should handle premium voice selection', async () => {
            const result = await textToSpeechTool.func({
                text: 'This is a premium voice test.',
                voice: 'premium-aria'
            });

            expect(result.success).toBe(true);
            expect(result.audioInfo.format).toBe('mp3'); // Premium voices use MP3
        });

        test('should handle speech customization parameters', async () => {
            const result = await textToSpeechTool.func({
                text: 'Testing custom speech parameters.',
                voice: 'david',
                speed: 0.8,
                pitch: 1.2
            });

            expect(result.success).toBe(true);
            expect(mockTTSService.synthesizeSpeech).toHaveBeenCalledWith({
                text: 'Testing custom speech parameters.',
                voice: 'david',
                speed: 0.8,
                pitch: 1.2
            });
        });
    });

    describe('Streaming Audio Processing', () => {
        test('should process audio chunks in sequence', async () => {
            // Simulate streaming audio chunks
            const chunks = [
                new ArrayBuffer(512),
                new ArrayBuffer(512),
                new ArrayBuffer(256) // Last chunk smaller
            ];

            const results = [];

            for (let i = 0; i < chunks.length; i++) {
                const result = await audioChunkProcessorTool.func({
                    chunkData: chunks[i],
                    sequenceNumber: i + 1,
                    isLast: i === chunks.length - 1
                });
                results.push(result);
            }

            // Check all chunks processed successfully
            expect(results.every(r => r.success)).toBe(true);

            // Last chunk should indicate completion
            const lastResult = results[results.length - 1];
            expect(lastResult.message).toContain('completed');
            expect(lastResult.totalDuration).toBeGreaterThan(0);
        });

        test('should handle chunk processing errors gracefully', async () => {
            // Mock a chunk processing error
            mockTTSService.processAudioChunk.mockRejectedValueOnce(
                new Error('Chunk corruption detected')
            );

            const result = await audioChunkProcessorTool.func({
                chunkData: new ArrayBuffer(512),
                sequenceNumber: 1,
                isLast: false
            });

            expect(result.success).toBe(false);
            expect(result.error).toBe('Chunk corruption detected');
        });
    });

    describe('Queue Management Scenarios', () => {
        test('should manage TTS queue under load', async () => {
            // Simulate multiple TTS requests to fill queue
            const requests = [
                'First message to queue',
                'Second message to queue',
                'Third message to queue'
            ];

            // Process requests
            for (const text of requests) {
                await textToSpeechTool.func({ text });
            }

            // Check queue status
            const statusResult = await ttsQueueManagerTool.func({
                action: 'status'
            });

            expect(statusResult.success).toBe(true);
            expect(statusResult.queueLength).toBeGreaterThan(0);
            expect(statusResult.estimatedWaitTime).toContain('seconds');
        });

        test('should clear queue when requested', async () => {
            // Add items to queue first
            await textToSpeechTool.func({ text: 'Queue item 1' });
            await textToSpeechTool.func({ text: 'Queue item 2' });

            // Clear queue
            const clearResult = await ttsQueueManagerTool.func({
                action: 'clear'
            });

            expect(clearResult.success).toBe(true);
            expect(clearResult.itemsCleared).toBeGreaterThan(0);

            // Verify queue is empty
            const statusResult = await ttsQueueManagerTool.func({
                action: 'status'
            });

            expect(statusResult.queueLength).toBe(0);
        });

        test('should pause and resume queue processing', async () => {
            // Pause queue
            const pauseResult = await ttsQueueManagerTool.func({
                action: 'pause'
            });

            expect(pauseResult.success).toBe(true);
            expect(pauseResult.paused).toBe(true);

            // Resume queue
            const resumeResult = await ttsQueueManagerTool.func({
                action: 'resume'
            });

            expect(resumeResult.success).toBe(true);
            expect(resumeResult.resumed).toBe(true);
        });
    });

    describe('Service Status and Monitoring', () => {
        test('should provide comprehensive service status', async () => {
            // Generate some activity first
            await textToSpeechTool.func({ text: 'Status test message' });

            const result = await ttsServiceStatusTool.func({
                includeVoices: false
            });

            expect(result.success).toBe(true);
            expect(result.status.isReady).toBe(true);
            expect(result.status.currentProvider).toBe('microsoft');
            expect(result.status.performance).toMatchObject({
                averageLatency: expect.any(Number),
                successRate: expect.any(Number),
                totalRequests: expect.any(Number)
            });
        });

        test('should include voice information when requested', async () => {
            const result = await ttsServiceStatusTool.func({
                includeVoices: true
            });

            expect(result.success).toBe(true);
            expect(result.availableVoices).toBeDefined();
            expect(result.availableVoices.length).toBeGreaterThan(0);
            expect(result.availableVoices[0]).toMatchObject({
                id: expect.any(String),
                name: expect.any(String),
                language: expect.any(String),
                gender: expect.any(String)
            });
        });

        test('should track performance metrics accurately', async () => {
            // Generate multiple requests to test metrics
            const requests = [
                'Performance test 1',
                'Performance test 2',
                'Performance test 3'
            ];

            for (const text of requests) {
                await textToSpeechTool.func({ text });
            }

            const result = await ttsServiceStatusTool.func({});
            const performance = result.status.performance;

            expect(performance.totalRequests).toBeGreaterThanOrEqual(requests.length);
            expect(performance.successRate).toBeGreaterThan(0.8); // Should have high success rate
            expect(performance.averageLatency).toBeGreaterThan(0);
        });
    });

    describe('Error Handling and Recovery', () => {
        test('should handle service failures gracefully', async () => {
            // Mock service failure
            mockTTSService.isReady.mockReturnValueOnce(false);

            const result = await textToSpeechTool.func({
                text: 'Service failure test'
            });

            expect(result.success).toBe(false);
            expect(result.error).toContain('not available');
        });

        test('should provide helpful error messages', async () => {
            // Test various error scenarios
            const errorTests = [
                { text: '', expectedError: 'empty' },
                { text: 'Valid text', voice: 'invalid-voice', expectedError: 'voice' },
                { text: 'Valid text', speed: -1, expectedError: 'speed' }
            ];

            for (const testCase of errorTests) {
                const result = await textToSpeechTool.func(testCase);

                if (!result.success) {
                    expect(result.error).toBeDefined();
                    expect(typeof result.error).toBe('string');
                }
            }
        });
    });

    describe('Performance and Load Testing', () => {
        test('should handle concurrent TTS requests', async () => {
            const concurrentRequests = Array.from({ length: 5 }, (_, i) =>
                textToSpeechTool.func({
                    text: `Concurrent request ${i + 1}`,
                    voice: i % 2 === 0 ? 'sarah' : 'david'
                })
            );

            const results = await Promise.all(concurrentRequests);

            // All requests should complete
            expect(results.length).toBe(5);

            // Most should succeed (allowing for some random failures)
            const successCount = results.filter(r => r.success).length;
            expect(successCount).toBeGreaterThanOrEqual(4);
        });

        test('should maintain performance under load', async () => {
            const startTime = Date.now();

            // Process multiple requests sequentially
            for (let i = 0; i < 10; i++) {
                await textToSpeechTool.func({
                    text: `Load test message ${i + 1}`
                });
            }

            const endTime = Date.now();
            const totalTime = endTime - startTime;

            // Should complete within reasonable time (allowing for mock delays)
            expect(totalTime).toBeLessThan(10000); // 10 seconds max

            // Check performance metrics
            const statusResult = await ttsServiceStatusTool.func({});
            expect(statusResult.status.performance.totalRequests).toBeGreaterThanOrEqual(10);
        });
    });
});
