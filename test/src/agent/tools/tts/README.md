# TTS Tools Test Suite

This directory contains comprehensive tests for the Text-to-Speech (TTS) tools integration with the LangChain agent system.

## Test Files

### `tts-tools.test.js`
Unit tests for individual TTS tools:
- **textToSpeechTool**: Tests text-to-speech conversion functionality
- **audioChunkProcessorTool**: Tests audio chunk processing and streaming
- **ttsQueueManagerTool**: Tests TTS queue management and backpressure control
- **ttsServiceStatusTool**: Tests TTS service status monitoring

### `tts-functional.test.js`
Functional tests that simulate real-world usage scenarios:
- End-to-end TTS workflows
- Performance and latency testing
- Error recovery and retry mechanisms
- Service availability scenarios
- Realistic audio processing chains

### `tts-integration.test.js`
Integration tests with the agent core system:
- Agent initialization with TTS tools
- Tool execution flow through LangChain
- Streaming integration with tool calls
- Service dependency management
- Multi-tool workflow coordination

### `index.test.js`
Test suite orchestrator that imports all TTS tests in the correct order.

## Running Tests

### Run all TTS tests:
```bash
npm test test/agent/tools/tts/
```

### Run specific test file:
```bash
npm test test/agent/tools/tts/tts-tools.test.js
npm test test/agent/tools/tts/tts-functional.test.js
npm test test/agent/tools/tts/tts-integration.test.js
```

### Run with coverage:
```bash
npm run test:coverage -- test/agent/tools/tts/
```

## Test Coverage

The test suite covers:
- ✅ Tool schema validation
- ✅ Individual tool functionality
- ✅ Error handling and edge cases
- ✅ Service integration
- ✅ Streaming and backpressure
- ✅ Performance monitoring
- ✅ Multi-tool workflows
- ✅ Agent core integration

## Mock Dependencies

All tests use mocked dependencies to ensure isolation:
- TTS services (Microsoft, ElevenLabs, Spark)
- Audio players and processors
- LangChain components
- Agent core services

## Test Architecture

```
TTS Tools Test Suite
├── Unit Tests (tts-tools.test.js)
│   ├── Schema validation
│   ├── Individual tool logic
│   └── Error scenarios
├── Functional Tests (tts-functional.test.js)
│   ├── End-to-end workflows
│   ├── Performance testing
│   └── Real-world scenarios
└── Integration Tests (tts-integration.test.js)
    ├── Agent system integration
    ├── Service dependencies
    └── Multi-tool coordination
```

## Key Test Scenarios

1. **Basic TTS functionality**: Convert text to speech with various voices and parameters
2. **Streaming audio processing**: Handle audio chunks in real-time with proper sequencing
3. **Queue management**: Control TTS processing queue and handle backpressure
4. **Service monitoring**: Check TTS service status and performance metrics
5. **Error recovery**: Handle service failures and implement retry mechanisms
6. **Agent integration**: Execute TTS tools through LangChain agent system
7. **Multi-step workflows**: Coordinate multiple TTS tools in complex scenarios

## Debugging

To debug tests:
1. Set `LOG_LEVEL=debug` environment variable
2. Use `console.log` statements in test files
3. Check mock call counts and arguments
4. Verify service state and dependencies
