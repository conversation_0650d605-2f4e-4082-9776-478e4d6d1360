/**
 * Integration Test for TTS Tools with LangGraph Agent Core
 * Tests the complete flow from LangGraph agent to TTS service execution via ToolNode
 */

import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest';

// Mock logger
vi.mock('@/utils/logger.js', () => ({
    createLogger: vi.fn(() => ({
        info: vi.fn(),
        debug: vi.fn(),
        warn: vi.fn(),
        error: vi.fn(),
        setLogLevel: vi.fn()
    })),
    LogLevel: { DEBUG: 0, INFO: 1, WARN: 2, ERROR: 3, NONE: 4 }
}));

// Mock API proxy
vi.mock('@/utils/apiProxy.js', () => ({
    fetchVllmApi: vi.fn()
}));

// Mock TTS service
const mockTTSService = {
    speak: vi.fn().mockResolvedValue({ success: true, duration: 2000 }),
    synthesizeSpeech: vi.fn(),
    processAudioChunk: vi.fn(),
    getServiceStatus: vi.fn(),
    queueAudio: vi.fn(),
    isReady: vi.fn().mockReturnValue(true),
    getSupportedVoices: vi.fn().mockReturnValue([
        { id: 'voice1', name: 'Voice 1', language: 'en-US' }
    ]),
    getRoles: vi.fn().mockResolvedValue(['alice', 'bob', 'emma_character', 'john_avatar']),
    setClonedVoice: vi.fn(),
    constructor: { name: 'MockTTSService' }
};

// Mock audio player
const mockAudioPlayer = {
    play: vi.fn(),
    playChunk: vi.fn().mockResolvedValue(true),
    enqueue: vi.fn(),
    getState: vi.fn().mockReturnValue('idle')
};

// Mock animation controller
const mockAnimationController = {
    triggerAnimation: vi.fn().mockResolvedValue({ success: true })
};

// Import classes after mocks
import { LangGraphAgentService } from '@/agent/core.js';
import { agentTTSTool } from '@/agent/tools/tts.js';

describe('TTS Tools Integration with LangGraph Agent Core', () => {
    let agentService;

    beforeEach(async () => {
        vi.clearAllMocks();

        // Initialize LangGraph agent service with TTS services
        agentService = new LangGraphAgentService({
            autoRegisterTools: true,
            services: {
                ttsService: mockTTSService,
                audioPlayer: mockAudioPlayer,
                animationController: mockAnimationController
            }
        });

        await agentService.initialize();
    });

    afterEach(() => {
        if (agentService && typeof agentService.dispose === 'function') {
            agentService.dispose();
        }
    });

    describe('Agent Initialization with TTS Tools', () => {
        it('should initialize agent with TTS tools available via ToolNode', async () => {
            expect(agentService.toolNode).toBeDefined();
            expect(agentService.tools).toBeDefined();
            expect(agentService.model).toBeDefined();

            // Verify TTS tools are registered
            const toolNames = agentService.tools.map(t => t.name);
            expect(toolNames).toContain('speak_response');
        });

        it('should register TTS tools when service is available', async () => {
            expect(agentService.services.ttsService).toBeDefined();
            expect(agentService.services.ttsService.isReady()).toBe(true);

            // Verify tools include TTS functionality
            const ttsTools = agentService.tools.filter(t =>
                t.name === 'speak_response' ||
                t.name === 'streamingAgentTTS' ||
                t.name === 'ttsHealthMonitor'
            );
            expect(ttsTools.length).toBeGreaterThan(0);
        });

        it('should inject services into ToolNode for tool access', async () => {
            expect(agentService.services.ttsService).toBe(mockTTSService);
            expect(agentService.services.audioPlayer).toBe(mockAudioPlayer);

            // Verify services are passed to ToolNode
            expect(typeof agentService.toolNode.invoke).toBe('function');
        });
    });

    describe('Tool Execution Flow via ToolNode', () => {
        it('should execute TTS tool when called by LLM through ToolNode', async () => {
            // Mock LangGraph state with TTS tool call
            const mockInput = {
                messages: [{
                    role: 'assistant',
                    content: 'I will speak this message',
                    tool_calls: [{
                        name: 'speak_response',
                        args: {
                            text: 'Hello, this is a test message',
                            options: { voice: 'voice1' }
                        },
                        id: 'call_123',
                        type: 'tool_call'
                    }]
                }]
            };

            // Execute via ToolNode
            const result = await agentService.toolNode.invoke(mockInput);

            // Verify ToolNode execution
            expect(result).toBeDefined();
            expect(result.messages).toBeDefined();
            expect(Array.isArray(result.messages)).toBe(true);

            // Verify TTS service was called
            expect(mockTTSService.speak).toHaveBeenCalledWith(
                'Hello, this is a test message',
                expect.objectContaining({
                    stream: true,
                    handleStreamInternally: true
                })
            );
        });

        it('should handle TTS tool errors gracefully through ToolNode', async () => {
            // Mock TTS service error
            mockTTSService.speak.mockRejectedValueOnce(new Error('TTS service unavailable'));

            const mockInput = {
                messages: [{
                    role: 'assistant',
                    content: '',
                    tool_calls: [{
                        name: 'speak_response',
                        args: { text: 'Error test message' },
                        id: 'call_456',
                        type: 'tool_call'
                    }]
                }]
            };

            // Should not throw - ToolNode handles errors gracefully
            const result = await agentService.toolNode.invoke(mockInput);
            expect(result).toBeDefined();

            // Reset mock for other tests
            mockTTSService.speak.mockResolvedValue({ success: true });
        });

        it('should pass services correctly to TTS tool via config', async () => {
            let capturedConfig;

            // Spy on the actual tool execution to capture config
            const originalTool = agentService.tools.find(t => t.name === 'speak_response');
            if (originalTool) {
                const originalFunc = originalTool.func || originalTool._function;
                const spyFunc = vi.fn().mockImplementation(async (input, config) => {
                    capturedConfig = config;
                    return { success: true, text: input.text };
                });

                if (originalTool.func) {
                    originalTool.func = spyFunc;
                } else if (originalTool._function) {
                    originalTool._function = spyFunc;
                }
            }

            const mockInput = {
                messages: [{
                    role: 'assistant',
                    content: '',
                    tool_calls: [{
                        name: 'speak_response',
                        args: { text: 'Config test' },
                        id: 'call_config',
                        type: 'tool_call'
                    }]
                }]
            };

            await agentService.toolNode.invoke(mockInput);

            // Verify services were passed in config
            if (capturedConfig) {
                expect(capturedConfig.ttsService).toBe(mockTTSService);
                expect(capturedConfig.audioPlayer).toBe(mockAudioPlayer);
            }
        });
    });

    describe('Streaming Integration with ToolNode', () => {
        it('should handle streaming TTS with tool calls via LangGraph workflow', async () => {
            // Mock LLM to return response with tool calls
            const mockLLMResponse = {
                content: 'I will speak this streaming message',
                tool_calls: [{
                    name: 'speak_response',
                    args: { text: 'Streaming test message' },
                    id: 'stream_call_1',
                    type: 'tool_call'
                }]
            };

            agentService.model.invoke = vi.fn().mockResolvedValue(mockLLMResponse);

            // Test complete workflow with streaming disabled for simplicity
            const result = await agentService.generateResponse('Test streaming', {
                sessionId: 'test-stream',
                stream: false
            });

            expect(result).toBeDefined();
            expect(typeof result).toBe('string');
            expect(agentService.model.invoke).toHaveBeenCalled();
        });

        it('should handle multiple tool calls in sequence through ToolNode', async () => {
            const mockInput = {
                messages: [{
                    role: 'assistant',
                    content: '',
                    tool_calls: [
                        {
                            name: 'speak_response',
                            args: { text: 'First message' },
                            id: 'call_1',
                            type: 'tool_call'
                        },
                        {
                            name: 'speak_response',
                            args: { text: 'Second message' },
                            id: 'call_2',
                            type: 'tool_call'
                        }
                    ]
                }]
            };

            const result = await agentService.toolNode.invoke(mockInput);

            expect(result).toBeDefined();
            expect(result.messages).toBeDefined();
            expect(result.messages.length).toBeGreaterThanOrEqual(2); // One ToolMessage per tool call
        });
    });

    describe('Error Handling and Resilience', () => {
        it('should handle TTS service not available', async () => {
            // Create agent without TTS service
            const agentWithoutTTS = new LangGraphAgentService({
                autoRegisterTools: true,
                services: {
                    ttsService: null,
                    audioPlayer: mockAudioPlayer
                }
            });

            await agentWithoutTTS.initialize();

            const mockInput = {
                messages: [{
                    role: 'assistant',
                    content: '',
                    tool_calls: [{
                        name: 'speak_response',
                        args: { text: 'No TTS test' },
                        id: 'no_tts_call',
                        type: 'tool_call'
                    }]
                }]
            };

            // Should not throw but handle gracefully
            const result = await agentWithoutTTS.toolNode.invoke(mockInput);
            expect(result).toBeDefined();

            agentWithoutTTS.dispose();
        });

        it('should handle audio player errors gracefully', async () => {
            mockAudioPlayer.playChunk.mockRejectedValueOnce(new Error('Audio playback failed'));

            const mockInput = {
                messages: [{
                    role: 'assistant',
                    content: '',
                    tool_calls: [{
                        name: 'speak_response',
                        args: { text: 'Audio error test' },
                        id: 'audio_error_call',
                        type: 'tool_call'
                    }]
                }]
            };

            const result = await agentService.toolNode.invoke(mockInput);
            expect(result).toBeDefined();

            // Reset mock
            mockAudioPlayer.playChunk.mockResolvedValue(true);
        });
    });
});
