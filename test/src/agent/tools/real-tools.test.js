/**
 * Real Tools Integration Tests
 * Tests LangChain tools with actual functionality and real checkpoints
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { selectAnimationTool, listAnimationsTool } from '@/agent/tools/animation.js';
import { ttsProcessingChainTool, ttsHealthMonitorTool } from '@/agent/tools/tts.js';
import { toolManager } from '@/agent/tools/index.js';

// Real test configuration
const TEST_CONFIG = {
    timeout: 10000,
    animationRegistry: {
        'wave': {
            id: 'wave',
            name: 'Wave Animation',
            description: 'A friendly waving gesture',
            duration: 2000,
            category: 'greeting'
        },
        'nod': {
            id: 'nod',
            name: 'Nod Animation',
            description: 'A nodding gesture for agreement',
            duration: 1500,
            category: 'response'
        },
        'dance': {
            id: 'dance',
            name: 'Dance Animation',
            description: 'A fun dancing movement',
            duration: 5000,
            category: 'entertainment'
        },
        'thinking': {
            id: 'thinking',
            name: 'Thinking Animation',
            description: 'A thoughtful pose',
            duration: 3000,
            category: 'emotion'
        }
    },
    mockTTSService: {
        isAvailable: () => true,
        speak: vi.fn().mockResolvedValue({ success: true, audioData: 'mock-audio' }),
        getVoices: vi.fn().mockResolvedValue(['voice1', 'voice2']),
        getStatus: vi.fn().mockResolvedValue({ status: 'ready', queue: 0 })
    }
};

describe('Real Tools Integration Tests', () => {
    beforeEach(() => {
        // Reset mocks
        vi.clearAllMocks();

        // Setup global animation registry for tests
        global.animationRegistry = TEST_CONFIG.animationRegistry;
    });

    afterEach(() => {
        // Cleanup
        delete global.animationRegistry;
    });

    describe('Animation Tools', () => {
        describe('selectAnimationTool', () => {
            it('should have correct LangChain tool schema', () => {
                expect(selectAnimationTool.name).toBe('select_animation');
                expect(selectAnimationTool.description).toBeDefined();
                expect(selectAnimationTool.schema).toBeDefined();
                expect(selectAnimationTool.schema.type).toBe('object');
                expect(selectAnimationTool.schema.properties.animationId).toBeDefined();
            });

            it('should select valid animation successfully', async () => {
                const result = await selectAnimationTool.invoke({
                    animationId: 'wave',
                    reasoningContext: 'User said hello'
                });

                expect(result.success).toBe(true);
                expect(result.animationId).toBe('wave');
                expect(result.animation).toEqual(TEST_CONFIG.animationRegistry.wave);
                expect(result.reasoning).toBeDefined();
                expect(result.timestamp).toBeDefined();
            });

            it('should handle invalid animation gracefully', async () => {
                const result = await selectAnimationTool.invoke({
                    animationId: 'invalid-animation',
                    reasoningContext: 'Testing invalid animation'
                });

                expect(result.success).toBe(false);
                expect(result.error).toBeDefined();
                expect(result.error).toContain('not found');
                expect(result.availableAnimations).toBeDefined();
                expect(Array.isArray(result.availableAnimations)).toBe(true);
            });

            it('should provide semantic animation suggestions', async () => {
                const result = await selectAnimationTool.invoke({
                    animationId: 'greeting',
                    reasoningContext: 'User wants a greeting animation'
                });

                // Should either find a greeting animation or suggest alternatives
                if (result.success) {
                    expect(['wave', 'nod']).toContain(result.animationId);
                } else {
                    expect(result.suggestions).toBeDefined();
                    expect(Array.isArray(result.suggestions)).toBe(true);
                }
            });

            it('should handle missing reasoning context', async () => {
                const result = await selectAnimationTool.invoke({
                    animationId: 'wave'
                    // No reasoningContext provided
                });

                expect(result.success).toBe(true);
                expect(result.reasoning).toBeDefined();
                expect(result.reasoning).toContain('No specific context');
            });
        });

        describe('listAnimationsTool', () => {
            it('should have correct LangChain tool schema', () => {
                expect(listAnimationsTool.name).toBe('list_animations');
                expect(listAnimationsTool.description).toBeDefined();
                expect(listAnimationsTool.schema).toBeDefined();
            });

            it('should list all available animations', async () => {
                const result = await listAnimationsTool.invoke({});

                expect(result.success).toBe(true);
                expect(result.animations).toBeDefined();
                expect(Array.isArray(result.animations)).toBe(true);
                expect(result.animations.length).toBe(Object.keys(TEST_CONFIG.animationRegistry).length);

                // Check that each animation has required properties
                result.animations.forEach(animation => {
                    expect(animation.id).toBeDefined();
                    expect(animation.name).toBeDefined();
                    expect(animation.description).toBeDefined();
                    expect(animation.category).toBeDefined();
                });
            });

            it('should filter animations by category', async () => {
                const result = await listAnimationsTool.invoke({
                    category: 'greeting'
                });

                expect(result.success).toBe(true);
                expect(result.animations).toBeDefined();
                expect(result.animations.length).toBeGreaterThan(0);

                // All returned animations should be in the greeting category
                result.animations.forEach(animation => {
                    expect(animation.category).toBe('greeting');
                });
            });

            it('should handle invalid category gracefully', async () => {
                const result = await listAnimationsTool.invoke({
                    category: 'invalid-category'
                });

                expect(result.success).toBe(true);
                expect(result.animations).toBeDefined();
                expect(result.animations.length).toBe(0);
                expect(result.availableCategories).toBeDefined();
            });

            it('should search animations by keyword', async () => {
                const result = await listAnimationsTool.invoke({
                    searchTerm: 'wave'
                });

                expect(result.success).toBe(true);
                expect(result.animations).toBeDefined();

                // Should find the wave animation
                const waveAnimation = result.animations.find(anim => anim.id === 'wave');
                expect(waveAnimation).toBeDefined();
            });
        });
    });

    describe('TTS Tools', () => {
        beforeEach(() => {
            // Setup mock TTS service
            global.ttsService = TEST_CONFIG.mockTTSService;
        });

        afterEach(() => {
            delete global.ttsService;
        });

        describe('ttsProcessingChainTool', () => {
            it('should have correct LangChain tool schema', () => {
                expect(ttsProcessingChainTool.name).toBe('tts_processing_chain');
                expect(ttsProcessingChainTool.description).toBeDefined();
                expect(ttsProcessingChainTool.schema).toBeDefined();
                expect(ttsProcessingChainTool.schema.properties.text).toBeDefined();
            });

            it('should process text to speech successfully', async () => {
                const result = await ttsProcessingChainTool.invoke({
                    text: 'Hello, this is a test message',
                    voice: 'default',
                    speed: 1.0
                });

                expect(result.success).toBe(true);
                expect(result.audioData).toBeDefined();
                expect(result.text).toBe('Hello, this is a test message');
                expect(result.voice).toBe('default');
                expect(result.processingTime).toBeDefined();

                // Verify TTS service was called
                expect(TEST_CONFIG.mockTTSService.speak).toHaveBeenCalledWith(
                    'Hello, this is a test message',
                    expect.objectContaining({ voice: 'default', speed: 1.0 })
                );
            });

            it('should handle empty text gracefully', async () => {
                const result = await ttsProcessingChainTool.invoke({
                    text: '',
                    voice: 'default'
                });

                expect(result.success).toBe(false);
                expect(result.error).toBeDefined();
                expect(result.error).toContain('empty');
            });

            it('should handle TTS service errors', async () => {
                // Mock TTS service to throw error
                TEST_CONFIG.mockTTSService.speak.mockRejectedValueOnce(
                    new Error('TTS service unavailable')
                );

                const result = await ttsProcessingChainTool.invoke({
                    text: 'Test message',
                    voice: 'default'
                });

                expect(result.success).toBe(false);
                expect(result.error).toBeDefined();
                expect(result.error).toContain('TTS service unavailable');
            });

            it('should validate voice parameters', async () => {
                const result = await ttsProcessingChainTool.invoke({
                    text: 'Test message',
                    voice: 'invalid-voice',
                    speed: 3.0 // Invalid speed
                });

                // Should either succeed with fallback or fail gracefully
                if (!result.success) {
                    expect(result.error).toBeDefined();
                } else {
                    // Should use fallback values
                    expect(result.voice).toBeDefined();
                    expect(result.speed).toBeLessThanOrEqual(2.0);
                }
            });
        });

        describe('ttsHealthMonitorTool', () => {
            it('should have correct LangChain tool schema', () => {
                expect(ttsHealthMonitorTool.name).toBe('tts_health_monitor');
                expect(ttsHealthMonitorTool.description).toBeDefined();
                expect(ttsHealthMonitorTool.schema).toBeDefined();
            });

            it('should check TTS service status', async () => {
                const result = await ttsHealthMonitorTool.invoke({});

                expect(result.success).toBe(true);
                expect(result.status).toBeDefined();
                expect(result.isAvailable).toBe(true);
                expect(result.queueLength).toBeDefined();
                expect(typeof result.queueLength).toBe('number');

                // Verify health check was performed
                expect(TEST_CONFIG.mockTTSService.getStatus).toHaveBeenCalled();
            });

            it('should handle unavailable TTS service', async () => {
                // Mock service as unavailable
                TEST_CONFIG.mockTTSService.isAvailable.mockReturnValueOnce(false);

                const result = await ttsHealthMonitorTool.invoke({});

                expect(result.success).toBe(true);
                expect(result.isAvailable).toBe(false);
                expect(result.status).toContain('unavailable');
            });

            it('should provide detailed diagnostics', async () => {
                const result = await ttsHealthMonitorTool.invoke({
                    detailed: true
                });

                expect(result.success).toBe(true);
                expect(result.diagnostics).toBeDefined();
                expect(result.diagnostics.voices).toBeDefined();
                expect(Array.isArray(result.diagnostics.voices)).toBe(true);
            });
        });
    });

    describe('Tool Manager Integration', () => {
        it('should register tools correctly', () => {
            const allTools = toolManager.getAllTools();

            expect(Array.isArray(allTools)).toBe(true);
            expect(allTools.length).toBeGreaterThan(0);

            // Check that animation tools are registered
            const animationTools = allTools.filter(tool =>
                tool.name.includes('animation')
            );
            expect(animationTools.length).toBeGreaterThan(0);
        });

        it('should provide tool by name', () => {
            const animationTool = toolManager.getTool('select_animation');
            expect(animationTool).toBeDefined();
            expect(animationTool.name).toBe('select_animation');
        });

        it('should list tools by category', () => {
            const animationTools = toolManager.getToolsByCategory('animation');
            expect(Array.isArray(animationTools)).toBe(true);
            expect(animationTools.length).toBeGreaterThan(0);
        });

        it('should validate tool schemas', () => {
            const allTools = toolManager.getAllTools();

            allTools.forEach(tool => {
                expect(tool.name).toBeDefined();
                expect(tool.description).toBeDefined();
                expect(tool.schema).toBeDefined();
                expect(typeof tool.invoke).toBe('function');
            });
        });
    });

    describe('Performance', () => {
        it('should execute animation tools quickly', async () => {
            const startTime = Date.now();

            const result = await selectAnimationTool.invoke({
                animationId: 'wave',
                reasoningContext: 'Performance test'
            });

            const duration = Date.now() - startTime;

            expect(result.success).toBe(true);
            expect(duration).toBeLessThan(100); // Should complete within 100ms
        });

        it('should handle concurrent tool executions', async () => {
            const promises = [
                selectAnimationTool.invoke({ animationId: 'wave' }),
                selectAnimationTool.invoke({ animationId: 'nod' }),
                listAnimationsTool.invoke({}),
                listAnimationsTool.invoke({ category: 'greeting' })
            ];

            const results = await Promise.all(promises);

            results.forEach(result => {
                expect(result.success).toBe(true);
            });
        });
    });
});
