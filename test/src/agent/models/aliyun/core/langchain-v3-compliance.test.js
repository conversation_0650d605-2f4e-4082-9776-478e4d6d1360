/**
 * LangChain v0.3 Compliance Test for AliyunBailianChatModel
 * Tests the newly implemented bindTools, invocationParams, and other v0.3 features
 */

import { describe, it, expect, beforeAll, afterAll } from 'vitest';
import { AliyunBailianChatModel } from '../../../src/agent/models/AliyunBailianChatModel.js';
import { HumanMessage, AIMessage, SystemMessage } from '@langchain/core/messages';

// Test configuration
const TEST_CONFIG = {
    useRealAPI: process.env.TEST_REAL_API === 'true',
    timeout: 30000
};

describe('LangChain v0.3 Compliance Tests', () => {
    let model;

    beforeAll(() => {
        model = new AliyunBailianChatModel({
            model: 'qwen-omni-turbo',
            apiMode: 'http',
            modalities: ['text', 'audio'],
            audioConfig: { voice: 'Ethan', format: 'wav' },
            enablePrompts: true
        });
    });

    describe('Core LangChain v0.3 Methods', () => {
        it('should have invocationParams method that returns proper metadata', () => {
            const params = model.invocationParams({ temperature: 0.8, max_tokens: 1500 });

            expect(params).toBeDefined();
            expect(params.model_name).toBe('qwen-omni-turbo');
            expect(params.api_mode).toBe('http');
            expect(params.modalities).toEqual(['text', 'audio']);
            expect(params.audio_config).toEqual({ voice: 'Ethan', format: 'wav' });
            expect(params.enable_prompts).toBe(true);
            expect(params.tools).toEqual([]);
            expect(params.temperature).toBe(0.8);
            expect(params.max_tokens).toBe(1500);
        });

        it('should have bindTools method that creates a new model instance', () => {
            const tools = [
                {
                    name: 'get_weather',
                    description: 'Get weather information',
                    schema: {
                        type: 'object',
                        properties: {
                            location: { type: 'string', description: 'City name' }
                        },
                        required: ['location']
                    }
                },
                {
                    name: 'calculate',
                    description: 'Perform calculations',
                    parameters: {
                        type: 'object',
                        properties: {
                            expression: { type: 'string', description: 'Math expression' }
                        },
                        required: ['expression']
                    }
                }
            ];

            const boundModel = model.bindTools(tools);

            // Should return a new instance
            expect(boundModel).not.toBe(model);
            expect(boundModel).toBeInstanceOf(AliyunBailianChatModel);

            // Original model should not have tools
            expect(model.boundTools).toEqual([]);

            // New model should have formatted tools
            expect(boundModel.boundTools).toHaveLength(2);
            expect(boundModel.boundTools[0]).toEqual({
                type: 'function',
                function: {
                    name: 'get_weather',
                    description: 'Get weather information',
                    parameters: {
                        type: 'object',
                        properties: {
                            location: { type: 'string', description: 'City name' }
                        },
                        required: ['location']
                    }
                }
            });
            expect(boundModel.boundTools[1]).toEqual({
                type: 'function',
                function: {
                    name: 'calculate',
                    description: 'Perform calculations',
                    parameters: {
                        type: 'object',
                        properties: {
                            expression: { type: 'string', description: 'Math expression' }
                        },
                        required: ['expression']
                    }
                }
            });
        });

        it('should handle different tool formats in bindTools', () => {
            // Test OpenAI format (should pass through)
            const openAITool = {
                type: 'function',
                function: {
                    name: 'test_tool',
                    description: 'Test tool',
                    parameters: { type: 'object', properties: {} }
                }
            };

            const boundModel1 = model.bindTools([openAITool]);
            expect(boundModel1.boundTools[0]).toEqual(openAITool);

            // Test minimal format
            const minimalTool = {
                name: 'minimal_tool',
                description: 'Minimal tool'
            };

            const boundModel2 = model.bindTools([minimalTool]);
            expect(boundModel2.boundTools[0]).toEqual({
                type: 'function',
                function: {
                    name: 'minimal_tool',
                    description: 'Minimal tool',
                    parameters: {
                        type: 'object',
                        properties: {},
                        required: []
                    }
                }
            });
        });

        it('should preserve configuration in bound model', () => {
            const customModel = new AliyunBailianChatModel({
                model: 'qwen-omni-turbo',
                apiMode: 'http',
                modalities: ['text'],
                audioConfig: { voice: 'Chelsie', format: 'wav' },
                language: 'chinese',
                gender: 'female',
                mood: 'friendly'
            });

            const boundModel = customModel.bindTools([{
                name: 'test',
                description: 'test'
            }]);

            expect(boundModel.model).toBe('qwen-omni-turbo');
            expect(boundModel.apiMode).toBe('http');
            expect(boundModel.modalities).toEqual(['text']);
            expect(boundModel.audioConfig).toEqual({ voice: 'Chelsie', format: 'wav' });
            expect(boundModel.language).toBe('chinese');
            expect(boundModel.gender).toBe('female');
            expect(boundModel.mood).toBe('friendly');
        });
    });

    describe('Tool Integration Tests', () => {
        it('should include bound tools in invocationParams', () => {
            const tools = [{
                name: 'test_tool',
                description: 'Test tool',
                schema: { type: 'object', properties: {} }
            }];

            const boundModel = model.bindTools(tools);
            const params = boundModel.invocationParams();

            expect(params.tools).toHaveLength(1);
            expect(params.tools[0].function.name).toBe('test_tool');
        });

        if (TEST_CONFIG.useRealAPI) {
            it('should work with bound tools in real API calls', async () => {
                const tools = [{
                    name: 'get_weather',
                    description: 'Get current weather information',
                    schema: {
                        type: 'object',
                        properties: {
                            location: { type: 'string', description: 'City name' }
                        },
                        required: ['location']
                    }
                }];

                const boundModel = model.bindTools(tools);
                const messages = [
                    new HumanMessage({
                        content: 'What is the weather like in Tokyo? Please provide both text and audio response.'
                    })
                ];

                const result = await boundModel.invoke(messages);

                expect(result).toBeDefined();
                expect(result.generations).toHaveLength(1);
                expect(result.generations[0].text).toBeTruthy();
                expect(result.generations[0].audio).toBeTruthy();
                expect(result.generations[0].audio.length).toBeGreaterThan(1000);

                console.log('✅ Bound tools real API test passed');
                console.log(`   Text: ${result.generations[0].text.substring(0, 100)}...`);
                console.log(`   Audio length: ${result.generations[0].audio.length} chars`);
            }, TEST_CONFIG.timeout);
        } else {
            it.skip('should work with bound tools in real API calls (set TEST_REAL_API=true to enable)', () => { });
        }
    });

    describe('LangChain Message Compatibility', () => {
        it('should properly handle instanceof checks for message types', () => {
            // This tests the fix for the _getType() issue
            const messages = [
                new SystemMessage({ content: 'You are a helpful assistant.' }),
                new HumanMessage({ content: 'Hello' }),
                new AIMessage({ content: 'Hi there!' })
            ];

            // This should not throw errors (testing the instanceof fixes)
            expect(() => {
                model._invokeHTTP(messages, {});
            }).not.toThrow();
        });

        it('should handle audio modality system message exclusion', async () => {
            const audioModel = new AliyunBailianChatModel({
                model: 'qwen-omni-turbo',
                apiMode: 'http',
                modalities: ['text', 'audio'],
                enablePrompts: true
            });

            const messages = [
                new SystemMessage({ content: 'System prompt that should be excluded' }),
                new HumanMessage({ content: 'Test message' })
            ];

            // Should not throw and should handle system message exclusion properly
            if (TEST_CONFIG.useRealAPI) {
                const result = await audioModel.invoke(messages);
                expect(result).toBeDefined();
            }
        });
    });

    describe('Error Handling and Edge Cases', () => {
        it('should handle empty tools array', () => {
            const boundModel = model.bindTools([]);
            expect(boundModel.boundTools).toEqual([]);
        });

        it('should handle invalid tool format gracefully', () => {
            const invalidTools = [
                { invalidField: 'test' },
                null,
                undefined,
                'string tool'
            ];

            // Should not throw
            expect(() => {
                model.bindTools(invalidTools.filter(Boolean));
            }).not.toThrow();
        });

        it('should maintain immutability when binding tools multiple times', () => {
            const tool1 = { name: 'tool1', description: 'First tool' };
            const tool2 = { name: 'tool2', description: 'Second tool' };

            const bound1 = model.bindTools([tool1]);
            const bound2 = bound1.bindTools([tool2]);

            expect(model.boundTools).toEqual([]);
            expect(bound1.boundTools).toHaveLength(1);
            expect(bound2.boundTools).toHaveLength(1);
            expect(bound2.boundTools[0].function.name).toBe('tool2');
        });
    });
}); 