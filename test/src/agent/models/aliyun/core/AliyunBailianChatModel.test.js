/** * Core tests for AliyunBailianChatModel * Basic functionality tests - main test logic has been distributed to specialized test files *  * See also: * - AliyunBailianChatModel.integration.test.js - Real API integration tests * - AliyunBailianChatModel.audio.test.js - Audio processing tests * - AliyunBailianChatModel.voice.test.js - Voice selection tests * - AliyunBailianChatModel.websocket.test.js - WebSocket management tests * - AliyunBailianChatModel.utils.test.js - Utility functions and helpers */import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'; import { AliyunBailianChatModel } from '@/agent/models/AliyunBailianChatModel.js'; import { HumanMessage, AIMessage } from '@langchain/core/messages'; describe('AliyunBailianChatModel - Core Tests', () => { let model; let mockLogger; let mockWebSocket; beforeEach(() => {        // Mock logger        mockLogger = {            info: vi.fn(),            debug: vi.fn(),            warn: vi.fn(),            error: vi.fn(),            setLogLevel: vi.fn()        };        // Mock WebSocket        mockWebSocket = {            send: vi.fn(),            close: vi.fn(),            readyState: 1, // OPEN            addEventListener: vi.fn(),            removeEventListener: vi.fn(),            url: 'wss://dashscope.aliyuncs.com/api-ws/v1/realtime'        };        // Mock WebSocket constructor        global.WebSocket = vi.fn(() => mockWebSocket);        model = new AliyunBailianChatModel({            apiKey: 'sk-test-key-for-unit-tests',            model: 'qwen-omni-turbo-realtime-latest'        });        model.logger = mockLogger;    });    afterEach(() => {        vi.clearAllTimers();        vi.clearAllMocks();    });    describe('Model Initialization', () => {        it('should create model instance with correct defaults', () => {            expect(model).toBeInstanceOf(AliyunBailianChatModel);            expect(model.apiKey).toBe('sk-test-key-for-unit-tests');            expect(model.model).toBe('qwen-omni-turbo-realtime-latest');            expect(model.streaming).toBe(true);        });        it('should handle custom configuration', () => {            const customModel = new AliyunBailianChatModel({                apiKey: 'custom-key',                model: 'qwen-omni-turbo-realtime',                useProxy: true,                maxAudioPerSecond: 10            });            expect(customModel.apiKey).toBe('custom-key');            expect(customModel.model).toBe('qwen-omni-turbo-realtime');            expect(customModel.useProxy).toBe(true);        });    });    describe('Realtime Mode Basic Operations', () => {        it('should detect realtime mode status correctly', () => {            // Initially inactive            expect(model.isRealtimeModeActive()).toBe(false);            // Set up active state            model.realtimeSocket = mockWebSocket;            model.realtimeSession = { id: 'test-session' };            expect(model.isRealtimeModeActive()).toBe(true);            // Clean up            model.closeRealtimeMode();            expect(model.isRealtimeModeActive()).toBe(false);        });        it('should handle WebSocket connection status', () => {            expect(model.isWebSocketConnected()).toBe(false);            model.realtimeSocket = mockWebSocket;            expect(model.isWebSocketConnected()).toBe(true);            model.realtimeSocket = null;            expect(model.isWebSocketConnected()).toBe(false);        });    });    describe('Message Processing', () => {        it('should handle basic message invoke', async () => {            // Mock the invoke method            const mockInvoke = vi.spyOn(model, 'invoke');            mockInvoke.mockResolvedValue(new AIMessage({ content: 'test response' }));            const messages = [new HumanMessage({ content: 'test message' })];            const result = await model.invoke(messages);            expect(result).toBeInstanceOf(AIMessage);            expect(result.content).toBe('test response');        });        it('should handle streaming when realtime is active', async () => {            model.realtimeSocket = mockWebSocket;            model.realtimeSession = { id: 'test-session' };            model._sessionStabilized = true;            // Mock streaming method            const streamSpy = vi.spyOn(model, '_streamViaWebSocket');            streamSpy.mockImplementation(async function* () {                yield { text: 'chunk1', message: new AIMessage({ content: 'chunk1' }) };                yield { text: 'chunk2', message: new AIMessage({ content: 'chunk2' }) };            });            const messages = [new HumanMessage({ content: 'stream test' })];            const streamGenerator = await model.stream(messages);            const chunks = [];            for await (const chunk of streamGenerator) {                chunks.push(chunk);            }            expect(chunks).toHaveLength(2);            expect(chunks[0].text).toBe('chunk1');            expect(chunks[1].text).toBe('chunk2');        });    });    describe('Cleanup and Error Handling', () => {        it('should clean up resources properly', () => {            model.realtimeSocket = mockWebSocket;            model.realtimeSession = { id: 'test' };            model.closeRealtimeMode();            expect(mockWebSocket.close).toHaveBeenCalled();            expect(model.realtimeSocket).toBe(null);            expect(model.realtimeSession).toBe(null);        });        it('should handle cleanup when already closed', () => {            expect(() => model.closeRealtimeMode()).not.toThrow();        });        it('should handle invalid API keys gracefully', () => {            const invalidModel = new AliyunBailianChatModel({                apiKey: '',                model: 'qwen-omni-turbo-realtime'            });            expect(invalidModel).toBeInstanceOf(AliyunBailianChatModel);            expect(invalidModel.apiKey).toBe('');        });    });});