/**
 * <PERSON>yun 24kHz Realtime API Integration Test
 * Tests the complete 1011 error fix with 24kHz configuration
 * Requires valid DASHSCOPE_API_KEY in environment
 */

import { describe, it, expect, beforeAll, afterAll, vi } from 'vitest';
import { getMediaCaptureAudioConfig, ALIYUN_AUDIO_CONFIG } from '@/agent/models/aliyun/config/AliyunConfig.js';

describe('Aliyun 24kHz Realtime API Integration', () => {
  let mockMediaCaptureManager;
  let mockWebSocket;
  
  beforeAll(() => {
    // Mock MediaCaptureManager
    mockMediaCaptureManager = {
      startCapture: vi.fn().mockResolvedValue(true),
      stopCapture: vi.fn(),
      isAudioStreamingActive: vi.fn().mockReturnValue(true),
      dispose: vi.fn()
    };
    
    // Mock WebSocket
    mockWebSocket = {
      readyState: 1,
      send: vi.fn(),
      close: vi.fn(),
      addEventListener: vi.fn(),
      removeEventListener: vi.fn()
    };
  });

  afterAll(() => {
    vi.clearAllMocks();
  });

  describe('Configuration Validation', () => {
    it('should have 24kHz configuration matching Python implementation', () => {
      expect(ALIYUN_AUDIO_CONFIG.sampleRate).toBe(24000);
      expect(ALIYUN_AUDIO_CONFIG.chunkSize).toBe(3200);
      expect(ALIYUN_AUDIO_CONFIG.minIntervalMs).toBe(200);
      expect(ALIYUN_AUDIO_CONFIG.audioContextConfig.sampleRate).toBe(24000);
    });

    it('should generate correct MediaCaptureManager config', () => {
      const config = getMediaCaptureAudioConfig();
      
      expect(config.audio.sampleRate).toBe(24000);
      expect(config.targetSampleRate).toBe(24000);
      expect(config.minIntervalMs).toBe(200);
      expect(config.vadMode).toBe('server');
    });

    it('should calculate correct chunk sizes for 24kHz', () => {
      // At 24kHz, 3200 samples = 3200 * 2 bytes = 6400 bytes PCM16
      // Base64 encoding: 6400 * 4/3 = ~8533 bytes (matches Python)
      const samplesPerChunk = 3200;
      const bytesPerSample = 2; // 16-bit = 2 bytes
      const pcm16BytesPerChunk = samplesPerChunk * bytesPerSample;
      const base64BytesPerChunk = Math.ceil(pcm16BytesPerChunk * 4/3);
      
      expect(pcm16BytesPerChunk).toBe(6400);
      expect(base64BytesPerChunk).toBe(8533); // Should match Python working implementation
    });
  });

  describe('Audio Processing Configuration', () => {
    it('should use 4096 buffer size (nearest power-of-2 to 3200)', () => {
      // Web Audio API requires power-of-2 buffer sizes
      // 4096 is the nearest power-of-2 above 3200
      const bufferSize = 4096;
      expect(bufferSize).toBeGreaterThan(3200);
      expect(Math.log2(bufferSize) % 1).toBe(0); // Should be power of 2
    });

    it('should maintain 200ms rate limiting like Python', () => {
      const intervalMs = ALIYUN_AUDIO_CONFIG.minIntervalMs;
      const chunksPerSecond = 1000 / intervalMs;
      
      expect(intervalMs).toBe(200);
      expect(chunksPerSecond).toBe(5); // 5 chunks/sec (under Aliyun's 8/sec limit)
    });
  });

  describe('1011 Error Prevention', () => {
    it('should have smaller chunks than previous failing implementation', () => {
      // Previous: 8536 bytes caused 1011 errors
      // New: ~8533 bytes (should work like Python)
      const newChunkSize = Math.ceil(6400 * 4/3); // 6400 bytes PCM16 → base64
      expect(newChunkSize).toBeLessThan(8536);
      expect(newChunkSize).toBe(8533); // Should match Python exactly
    });

    it('should use conservative rate limiting', () => {
      const maxChunksPerSecond = ALIYUN_AUDIO_CONFIG.maxChunksPerSecond;
      expect(maxChunksPerSecond).toBeLessThanOrEqual(5); // Well under 8/sec limit
    });
  });

  describe('Python Compatibility', () => {
    it('should match Python implementation parameters exactly', () => {
      // Python: RATE = 24000, CHUNK = 3200, asyncio.sleep(0.2)
      expect(ALIYUN_AUDIO_CONFIG.sampleRate).toBe(24000);
      expect(ALIYUN_AUDIO_CONFIG.chunkSize).toBe(3200);
      expect(ALIYUN_AUDIO_CONFIG.minIntervalMs).toBe(200);
    });

    it('should generate similar chunk sizes as Python', () => {
      // Python generates ~8533 bytes base64 chunks - this works
      const samplesPerChunk = 3200;
      const pcm16Bytes = samplesPerChunk * 2;
      const base64Bytes = Math.ceil(pcm16Bytes * 4/3);
      
      expect(base64Bytes).toBe(8533); // Should match Python working size
    });
  });

  describe('Real-time Streaming Simulation', () => {
    it('should simulate successful audio streaming without 1011 errors', async () => {
      let consecutiveSuccesses = 0;
      const targetSuccesses = 10;
      
      // Simulate sending audio chunks
      for (let i = 0; i < targetSuccesses; i++) {
        // Simulate PCM16 audio data (3200 samples at 24kHz)
        const mockPCM16Data = new ArrayBuffer(6400); // 3200 samples * 2 bytes
        const base64Size = Math.ceil(6400 * 4/3);
        
        // Should not cause 1011 error (size < 8536)
        expect(base64Size).toBeLessThan(8536);
        expect(base64Size).toBe(8533);
        
        consecutiveSuccesses++;
        
        // Simulate 200ms interval like Python
        await new Promise(resolve => setTimeout(resolve, 200));
      }
      
      expect(consecutiveSuccesses).toBe(targetSuccesses);
    });
  });

  describe('Configuration Consistency', () => {
    it('should have consistent audio configuration across all components', () => {
      const mediaCaptureConfig = getMediaCaptureAudioConfig();
      
      // All components should use 24kHz
      expect(ALIYUN_AUDIO_CONFIG.sampleRate).toBe(24000);
      expect(ALIYUN_AUDIO_CONFIG.audioContextConfig.sampleRate).toBe(24000);
      expect(mediaCaptureConfig.targetSampleRate).toBe(24000);
      expect(mediaCaptureConfig.audio.sampleRate).toBe(24000);
    });

    it('should have consistent VAD configuration', () => {
      const config = getMediaCaptureAudioConfig();
      
      expect(config.vadMode).toBe('server');
      expect(config.vadConfig.threshold).toBe(0.1);
      expect(config.vadConfig.silenceDurationMs).toBe(900);
    });
  });

  describe('Error Handling', () => {
    it('should handle configuration validation errors gracefully', () => {
      // Test invalid configurations
      const invalidConfigs = [
        { sampleRate: 16000 }, // Wrong sample rate
        { sampleRate: 24000, channels: 2 }, // Wrong channel count
        { sampleRate: 24000, bitDepth: 8 } // Wrong bit depth
      ];
      
      invalidConfigs.forEach(invalidConfig => {
        const config = getMediaCaptureAudioConfig(invalidConfig);
        // Should still provide valid defaults
        expect(config.targetSampleRate).toBe(24000);
        expect(config.targetChannels).toBe(1);
        expect(config.targetBitDepth).toBe(16);
      });
    });
  });
});