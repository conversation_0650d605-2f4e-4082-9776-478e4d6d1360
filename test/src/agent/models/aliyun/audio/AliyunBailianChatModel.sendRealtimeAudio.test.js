/**
 * Tests for AliyunBailianChatModel sendRealtimeAudio functionality
 * Tests the new method that was added to fix TypeError: this.agentService.model.sendRealtimeAudio is not a function
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { AliyunBailianChatModel } from '../../../../../src/agent/models/AliyunBailianChatModel.js';

// Mock WebSocket
global.WebSocket = vi.fn(() => ({
    readyState: 1, // OPEN
    send: vi.fn(),
    close: vi.fn(),
    addEventListener: vi.fn(),
    removeEventListener: vi.fn()
}));

// Mock audio processing module
vi.mock('../../../../src/media/modality/audio.ts', () => ({
    processRealtimeAudio: vi.fn(),
    createStreamingAudioProcessor: vi.fn(() => ({
        processStreamingChunk: vi.fn(),
        endStream: vi.fn(),
        getStatus: vi.fn(() => ({ isPlaying: false, queueLength: 0 }))
    }))
}));

// Mock media handler
vi.mock('../../../../src/agent/models/aliyun/media/MediaHandler.js', () => ({
    MediaHandler: vi.fn(() => ({
        processMultimodal: vi.fn()
    }))
}));

// Mock port manager
vi.mock('../../../../src/utils/portManager.js', () => ({
    getDownloadServerUrl: vi.fn(() => 'http://localhost:2994'),
    getDownloadServerPort: vi.fn(() => 2994)
}));

// Mock TTS tools
vi.mock('../../../../src/agent/tools/tts.js', () => ({
    playAudioTool: {
        name: 'play_audio',
        func: vi.fn()
    }
}));

// Mock OpenAI
vi.mock('openai', () => ({
    default: vi.fn()
}));

// Mock prompts
vi.mock('../../../../src/agent/prompts/index.js', () => ({
    LANGCHAIN_PROMPT_COMPONENTS: {}
}));

vi.mock('../../../../src/agent/prompts/base.js', () => ({
    BASE_PROMPT_COMPONENTS: {},
    buildSystemPrompt: vi.fn(() => 'test prompt')
}));

// Mock LangChain core
vi.mock('@langchain/core/language_models/chat_models', () => ({
    BaseChatModel: class MockBaseChatModel {
        constructor() { }
    }
}));

vi.mock('@langchain/core/messages', () => ({
    AIMessage: vi.fn(),
    HumanMessage: vi.fn(),
    SystemMessage: vi.fn()
}));

// Mock logger
vi.mock('../../../../src/utils/logger.js', () => ({
    createLogger: vi.fn(() => ({
        debug: vi.fn(),
        info: vi.fn(),
        warn: vi.fn(),
        error: vi.fn(),
        setLogLevel: vi.fn()
    })),
    LogLevel: {
        DEBUG: 'DEBUG',
        INFO: 'INFO',
        WARN: 'WARN',
        ERROR: 'ERROR'
    }
}));

describe('AliyunBailianChatModel - sendRealtimeAudio', () => {
    let model;
    let mockWebSocket;
    let mockProcessRealtimeAudio;

    beforeEach(async () => {
        // Reset all mocks
        vi.clearAllMocks();

        // Setup mock WebSocket
        mockWebSocket = {
            readyState: WebSocket.OPEN,
            send: vi.fn(),
            close: vi.fn(),
            addEventListener: vi.fn(),
            removeEventListener: vi.fn()
        };
        global.WebSocket.mockReturnValue(mockWebSocket);

        // Setup mock audio processing
        const { processRealtimeAudio } = await import('../../../../../src/media/modality/audio.ts');
        mockProcessRealtimeAudio = processRealtimeAudio;
        mockProcessRealtimeAudio.mockResolvedValue({
            success: true,
            base64Audio: 'UklGRiQAAABXQVZFZm10IBAAAAABAAEARKwAAIhYAQACABAAZGF0YQAAAAA='
        });

        // Create model instance
        model = new AliyunBailianChatModel({
            apiKey: 'test-key',
            model: 'qwen-omni-turbo-realtime',
            apiMode: 'websocket'
        });

        // Mock realtime connection as active
        model.realtimeSocket = mockWebSocket;
        model._sessionStabilized = true;
    });

    afterEach(() => {
        vi.restoreAllMocks();
    });

    describe('Method Availability', () => {
        it('should have sendRealtimeAudio method available', () => {
            expect(typeof model.sendRealtimeAudio).toBe('function');
        });

        it('should be accessible from TalkingAvatarAdapter pattern', async () => {
            // Simulate the call pattern from TalkingAvatarAdapter
            const agentService = { model };

            expect(typeof agentService.model.sendRealtimeAudio).toBe('function');

            // Should not throw the TypeError anymore
            const mockAudioData = new ArrayBuffer(1024);
            const result = await agentService.model.sendRealtimeAudio(mockAudioData);
            expect(typeof result).toBe('boolean');
        });
    });

    describe('WebSocket Mode Validation', () => {
        it('should return false when not in WebSocket mode', async () => {
            model.apiMode = 'http';

            const audioData = new ArrayBuffer(1024);
            const result = await model.sendRealtimeAudio(audioData);

            expect(result).toBe(false);
        });

        it('should return false when realtime mode is not active', async () => {
            model._sessionStabilized = false;

            const audioData = new ArrayBuffer(1024);
            const result = await model.sendRealtimeAudio(audioData);

            expect(result).toBe(false);
        });

        it('should process audio when in correct mode', async () => {
            const audioData = new ArrayBuffer(1024);
            const result = await model.sendRealtimeAudio(audioData);

            expect(result).toBe(true);
            expect(mockProcessRealtimeAudio).toHaveBeenCalledWith(audioData, {
                sampleRate: 24000,
                numChannels: 1,
                bitDepth: 16,
                enableDebugLogging: false
            });
        });
    });

    describe('Audio Processing', () => {
        it('should handle Float32Array input', async () => {
            const audioData = new Float32Array(1024);
            for (let i = 0; i < audioData.length; i++) {
                audioData[i] = Math.sin(2 * Math.PI * 440 * i / 24000); // 440Hz tone
            }

            const result = await model.sendRealtimeAudio(audioData);

            expect(result).toBe(true);
            expect(mockProcessRealtimeAudio).toHaveBeenCalledWith(audioData, expect.any(Object));
        });

        it('should handle Int16Array input', async () => {
            const audioData = new Int16Array(1024);
            for (let i = 0; i < audioData.length; i++) {
                audioData[i] = Math.floor(Math.sin(2 * Math.PI * 440 * i / 24000) * 32767);
            }

            const result = await model.sendRealtimeAudio(audioData);

            expect(result).toBe(true);
        });

        it('should handle ArrayBuffer input (common from audio worklets)', async () => {
            const audioData = new ArrayBuffer(2048); // 1024 samples * 2 bytes/sample
            const int16View = new Int16Array(audioData);

            for (let i = 0; i < int16View.length; i++) {
                int16View[i] = Math.floor(Math.sin(2 * Math.PI * 440 * i / 24000) * 32767);
            }

            const result = await model.sendRealtimeAudio(audioData);

            expect(result).toBe(true);
        });

        it('should return false when audio processing fails', async () => {
            mockProcessRealtimeAudio.mockResolvedValueOnce({
                success: false,
                error: 'Invalid audio format'
            });

            const audioData = new ArrayBuffer(1024);
            const result = await model.sendRealtimeAudio(audioData);

            expect(result).toBe(false);
        });

        it('should handle audio processing errors gracefully', async () => {
            mockProcessRealtimeAudio.mockRejectedValueOnce(new Error('Processing failed'));

            const audioData = new ArrayBuffer(1024);
            const result = await model.sendRealtimeAudio(audioData);

            expect(result).toBe(false);
        });
    });

    describe('WebSocket Message Sending', () => {
        it('should send correct audio buffer append event', async () => {
            const audioData = new ArrayBuffer(1024);
            await model.sendRealtimeAudio(audioData);

            expect(mockWebSocket.send).toHaveBeenCalledWith(
                expect.stringContaining('input_audio_buffer.append')
            );

            const sentMessage = JSON.parse(mockWebSocket.send.mock.calls[0][0]);
            expect(sentMessage).toMatchObject({
                type: 'input_audio_buffer.append',
                audio: expect.any(String),
                event_id: expect.stringMatching(/^event_\d+$/)
            });
        });

        it('should include processed base64 audio in message', async () => {
            const expectedBase64 = 'UklGRiQAAABXQVZFZm10IBAAAAABAAEARKwAAIhYAQACABAAZGF0YQAAAAA=';
            mockProcessRealtimeAudio.mockResolvedValueOnce({
                success: true,
                base64Audio: expectedBase64
            });

            const audioData = new ArrayBuffer(1024);
            await model.sendRealtimeAudio(audioData);

            const sentMessage = JSON.parse(mockWebSocket.send.mock.calls[0][0]);
            expect(sentMessage.audio).toBe(expectedBase64);
        });

        it('should generate unique event IDs', async () => {
            const audioData = new ArrayBuffer(1024);

            await model.sendRealtimeAudio(audioData);
            await model.sendRealtimeAudio(audioData);

            expect(mockWebSocket.send).toHaveBeenCalledTimes(2);

            const message1 = JSON.parse(mockWebSocket.send.mock.calls[0][0]);
            const message2 = JSON.parse(mockWebSocket.send.mock.calls[1][0]);

            expect(message1.event_id).not.toBe(message2.event_id);
        });
    });

    describe('Real-world Usage Patterns', () => {
        it('should handle rapid consecutive calls (streaming scenario)', async () => {
            const chunkSize = 800; // 50ms at 16kHz
            const chunks = [];

            // Create multiple audio chunks
            for (let i = 0; i < 10; i++) {
                const chunk = new Float32Array(chunkSize);
                for (let j = 0; j < chunkSize; j++) {
                    chunk[j] = Math.sin(2 * Math.PI * 440 * (i * chunkSize + j) / 24000);
                }
                chunks.push(chunk);
            }

            // Send all chunks rapidly
            const results = await Promise.all(
                chunks.map(chunk => model.sendRealtimeAudio(chunk))
            );

            // All should succeed
            expect(results.every(result => result === true)).toBe(true);
            expect(mockWebSocket.send).toHaveBeenCalledTimes(10);
        });

        it('should work with TalkingAvatarAdapter AudioWorklet pattern', async () => {
            // Simulate the exact pattern from TalkingAvatarAdapter worklet
            const pcm16Chunk = new Int16Array(800); // 50ms at 16kHz
            for (let i = 0; i < pcm16Chunk.length; i++) {
                const sample = Math.sin(2 * Math.PI * 440 * i / 16000);
                pcm16Chunk[i] = sample < 0 ? sample * 0x8000 : sample * 0x7FFF;
            }

            // This is the exact call pattern from TalkingAvatarAdapter.js:849
            const success = await model.sendRealtimeAudio(pcm16Chunk.buffer);

            expect(success).toBe(true);
            expect(mockProcessRealtimeAudio).toHaveBeenCalledWith(
                pcm16Chunk.buffer,
                expect.objectContaining({
                    sampleRate: 24000,
                    numChannels: 1,
                    bitDepth: 16
                })
            );
        });

        it('should handle edge case of very small audio chunks', async () => {
            const tinyChunk = new Float32Array(1); // Single sample
            tinyChunk[0] = 0.5;

            const result = await model.sendRealtimeAudio(tinyChunk);

            expect(result).toBe(true); // Should handle gracefully
        });

        it('should handle edge case of very large audio chunks', async () => {
            const largeChunk = new Float32Array(48000); // 2 seconds at 24kHz
            for (let i = 0; i < largeChunk.length; i++) {
                largeChunk[i] = Math.sin(2 * Math.PI * 440 * i / 24000);
            }

            const result = await model.sendRealtimeAudio(largeChunk);

            expect(result).toBe(true); // Should handle large chunks
        });
    });

    describe('Integration with Audio Message Handling', () => {
        it('should integrate with blob message handling', async () => {
            // Test that the new audio processing works with blob messages
            const mockAudioBlob = new Blob([new Uint8Array(1024)], { type: 'audio/pcm' });

            const mockEvent = {
                data: mockAudioBlob
            };

            // This should not throw an error anymore
            expect(() => {
                model._handleRealtimeMessage(mockEvent, {});
            }).not.toThrow();
        });

        it('should integrate with streaming audio delta handling', async () => {
            // Test streaming audio delta processing
            const mockAudioDelta = 'UklGRiQAAABXQVZFZm10IBAAAAABAAEARKwAAIhYAQACABAAZGF0YQAAAAA=';

            // This should initialize streaming processor
            await model._handleStreamingAudioDelta(mockAudioDelta);

            expect(model._streamingProcessor).toBeDefined();
        });
    });
}); 