import { describe, it, expect } from 'vitest';
import { AliyunBailianChatModel } from '@/agent/models/AliyunBailianChatModel.js';
import { HumanMessage } from '@langchain/core/messages';

const REAL_API_KEY = process.env.VITE_DASHSCOPE_API_KEY || 'sk-3bf66c1191e3488da916ef3e09e0eaa3';

describe('AliyunBailianChatModel - Real HTTP and Realtime', () => {
    if (!REAL_API_KEY) {
        it('skips real tests if no API key is set', () => {
            expect(true).toBe(true);
        });
        return;
    }

    it('should return real message from HTTP API', async () => {
        const model = new AliyunBailianChatModel({
            apiKey: REAL_API_KEY,
            model: 'qwen-plus'
        });
        const messages = [new HumanMessage({ content: 'Hello, who are you?' })];
        const result = await model.invoke(messages);
        console.log('HTTP result:', result);
        expect(result.generations[0].text).toBeTruthy();
    }, 20000);

    it('should return real message from WebSocket API', async () => {
        const model = new AliyunBailianChatModel({
            apiKey: REAL_API_KEY,
            model: 'qwen-plus-realtime'
        });
        const messages = [new HumanMessage({ content: 'Hello, who are you?' })];
        const result = await model.invoke(messages);
        console.log('WebSocket result:', result);
        expect(result.generations[0].text).toBeTruthy();
    }, 20000);
}); 