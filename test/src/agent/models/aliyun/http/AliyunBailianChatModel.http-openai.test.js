import { describe, it, expect } from 'vitest';
import OpenAI from 'openai';

const apiKey = process.env.VITE_DASHSCOPE_API_KEY;
if (!apiKey) {
    throw new Error('VITE_DASHSCOPE_API_KEY not set');
}

const openai = new OpenAI({
    apiKey,
    baseURL: 'https://dashscope.aliyuncs.com/compatible-mode/v1',
});

describe('Aliyun Qwen-Omni HTTP via OpenAI SDK', () => {
    it('should stream a text-only response', async () => {
        const completion = await openai.chat.completions.create({
            model: 'qwen-omni-turbo',
            messages: [
                { role: 'user', content: '你是谁？' },
            ],
            stream: true,
            stream_options: { include_usage: true },
            modalities: ['text'],
        });
        let content = '';
        for await (const chunk of completion) {
            if (Array.isArray(chunk.choices) && chunk.choices.length > 0) {
                if (chunk.choices[0].delta?.content) {
                    content += chunk.choices[0].delta.content;
                    process.stdout.write(chunk.choices[0].delta.content);
                }
            }
        }
        expect(content.length).toBeGreaterThan(0);
    }, 20000);

    it('should stream a multimodal (image+text) response', async () => {
        const completion = await openai.chat.completions.create({
            model: 'qwen-omni-turbo',
            messages: [
                {
                    role: 'user',
                    content: [
                        {
                            type: 'image_url',
                            image_url: { url: 'https://help-static-aliyun-doc.aliyuncs.com/file-manage-files/zh-CN/20241022/emyrja/dog_and_girl.jpeg' },
                        },
                        { type: 'text', text: '图中描绘的是什么景象？' },
                    ],
                },
            ],
            stream: true,
            stream_options: { include_usage: true },
            modalities: ['text'],
        });
        let content = '';
        for await (const chunk of completion) {
            if (Array.isArray(chunk.choices) && chunk.choices.length > 0) {
                if (chunk.choices[0].delta?.content) {
                    content += chunk.choices[0].delta.content;
                    process.stdout.write(chunk.choices[0].delta.content);
                }
            }
        }
        expect(content.length).toBeGreaterThan(0);
    }, 20000);
}); 