/**
 * <PERSON>yun WebSocket Connection Test
 * Tests the realtime connection to <PERSON>yun Qwen-Omni service
 * Specifically verifies that session.update doesn't cause 1011 error
 */

import { AliyunRealtimeClient } from '../../../../../src/agent/models/aliyun/streaming/AliyunRealtimeClient.js';

describe('Aliyun WebSocket Connection Test', () => {
    let client;

    // Get API key from environment variables
    const apiKey = process.env.VITE_DASHSCOPE_API_KEY || process.env.VITE_ALIYUN_API_KEY;

    // Force test to run with environment variable
    const skipTests = !apiKey;

    console.log(`API Key available: ${!!apiKey}, length: ${apiKey ? apiKey.length : 0}`);
    console.log(`Test will ${skipTests ? 'be SKIPPED' : 'RUN'}`);

    // Setup and teardown
    beforeEach(() => {
        if (skipTests) {
            console.warn('Skipping tests: No API key found');
            return;
        }

        client = new AliyunRealtimeClient({
            api<PERSON><PERSON>,
            model: 'qwen-omni-turbo-realtime',
            voice: '<PERSON><PERSON><PERSON>',
            enableDebugLogging: true
        });

        console.log('Test client initialized with API key');
    });

    afterEach(async () => {
        if (client) {
            await client.disconnect();
            console.log('Test client disconnected');
        }
    });

    // Tests
    test('should connect and receive session.created event', async () => {
        if (skipTests) {
            console.log('TEST SKIPPED - No API key available');
            return;
        }

        console.log('Running test: should connect and receive session.created event');

        // Track events
        const events = [];
        client.on('message', (msg) => {
            console.log(`Received message type: ${msg.type}`);
            events.push(msg);
        });

        // Set up sessionReady promise
        const sessionReadyPromise = new Promise((resolve) => {
            client.on('sessionReady', (session) => {
                console.log('Session ready event received!');
                resolve(session);
            });
        });

        // Set up error handler
        client.on('error', (error) => {
            console.error('WebSocket error:', error);
        });

        // Connect with timeout
        console.log('Attempting to connect...');
        const connected = await client.connect();
        console.log(`Connected: ${connected}`);
        expect(connected).toBe(true);

        // Wait for session to be ready
        console.log('Waiting for session ready...');
        const session = await Promise.race([
            sessionReadyPromise,
            new Promise((_, reject) => setTimeout(() => reject(new Error('Session ready timeout')), 5000))
        ]);

        console.log('Session ready response received');

        // Verify session is created and stabilized
        expect(session).toBeDefined();
        expect(client.isSessionReady()).toBe(true);
        expect(client.sessionId).toBeTruthy();
        console.log(`Session ID: ${client.sessionId}`);

        // Verify events
        const hasSessionCreated = events.some(e => e.type === 'session.created');
        const hasSessionUpdated = events.some(e => e.type === 'session.updated');

        console.log(`Events received: ${events.length}`);
        console.log(`Has session.created: ${hasSessionCreated}`);
        console.log(`Has session.updated: ${hasSessionUpdated}`);

        expect(hasSessionCreated).toBe(true);
        expect(hasSessionUpdated).toBe(true);
    }, 10000); // 10 second timeout for this test

    test('should send audio without 1011 error', async () => {
        if (skipTests) {
            console.log('TEST SKIPPED - No API key available');
            return;
        }

        console.log('Running test: should send audio without 1011 error');

        // Connect
        console.log('Attempting to connect...');
        const connected = await client.connect();
        console.log(`Connected: ${connected}`);
        expect(connected).toBe(true);

        // Wait for session to be ready
        console.log('Waiting for session ready...');
        await new Promise((resolve, reject) => {
            const timeout = setTimeout(() => reject(new Error('Session ready timeout')), 5000);

            client.on('sessionReady', (session) => {
                console.log(`Session ready! Session ID: ${client.sessionId}`);
                clearTimeout(timeout);
                resolve(session);
            });

            client.on('error', (error) => {
                console.error('WebSocket error:', error);
            });
        });

        console.log('Creating audio sample...');

        // Create small PCM16 audio sample (silence)
        const sampleLength = 3200; // 3200 samples
        const sampleBuffer = new ArrayBuffer(sampleLength * 2); // 16-bit = 2 bytes per sample
        const view = new Int16Array(sampleBuffer);
        // Fill with silence (zeros)
        for (let i = 0; i < sampleLength; i++) {
            view[i] = 0;
        }

        console.log(`Audio sample created: ${sampleLength} samples, ${sampleBuffer.byteLength} bytes`);

        // Send audio
        console.log('Sending audio...');
        const success = await client.sendAudio(new Uint8Array(sampleBuffer), 'uint8array');
        console.log(`Audio sent: ${success}`);
        expect(success).toBe(true);

        // Wait for potential 1011 error (which should not happen)
        console.log('Waiting 1 second to check for 1011 error...');
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Connection should still be active
        const stillConnected = client.isConnected();
        console.log(`Still connected: ${stillConnected}`);
        expect(stillConnected).toBe(true);

        // Commit audio
        console.log('Committing audio...');
        const committed = await client.commitAudio();
        console.log(`Audio committed: ${committed}`);
        expect(committed).toBe(true);

        // Wait for potential response
        console.log('Waiting for potential response...');
        await new Promise(resolve => setTimeout(resolve, 2000));
        console.log('Test complete - no 1011 error occurred!');
    }, 15000); // 15 second timeout
}); 