/**
 * <PERSON>yun WebSocket Real API Test
 * Tests the realtime connection to <PERSON>yun Qwen-Omni service with real API key
 * Specifically verifies that session.update doesn't cause 1011 error
 * 
 * This test will only run if VITE_DASHSCOPE_API_KEY is set in the environment
 */

import { describe, test, expect, beforeEach, afterEach } from 'vitest';
import { AliyunRealtimeClient } from '../../../../../src/agent/models/aliyun/streaming/AliyunRealtimeClient.js';

describe('Aliyun WebSocket Real API Test', () => {
    let client;

    // Get API key from environment
    const apiKey = process.env.VITE_DASHSCOPE_API_KEY || process.env.VITE_ALIYUN_API_KEY;
    const skipTests = !apiKey;

    console.log(`API Key available: ${!!apiKey}, length: ${apiKey ? apiKey.length : 0}`);
    console.log(`Tests will ${skipTests ? 'be SKIPPED' : 'RUN'}`);

    // Setup and teardown
    beforeEach(() => {
        if (skipTests) {
            console.log('Skipping tests: No API key found');
            return;
        }

        console.log('Creating test client with API key...');
        client = new AliyunRealtimeClient({
            apiKey,
            model: 'qwen-omni-turbo-realtime',
            voice: 'Chelsie',
            enableDebugLogging: true
        });
    });

    afterEach(async () => {
        if (client) {
            console.log('Cleaning up test client...');
            await client.disconnect();
        }
    });

    // Test case that verifies the fix for 1011 error
    test('should connect and update session without 1011 error', async () => {
        // Skip if no API key
        if (skipTests) {
            console.log('TEST SKIPPED - No API key');
            return;
        }

        console.log('Running test: should connect and update session without 1011 error');

        // Setup event handlers
        const events = [];
        const messageHandler = (msg) => {
            console.log(`Received message: ${msg.type}`);
            events.push(msg);
        };
        client.on('message', messageHandler);

        // Setup session ready promise
        const sessionReadyPromise = new Promise((resolve) => {
            client.on('sessionReady', (session) => {
                console.log('Session ready event received!');
                resolve(session);
            });
        });

        // Connect
        console.log('Connecting to Aliyun...');
        const connected = await client.connect();
        console.log(`Connected: ${connected}`);
        expect(connected).toBe(true);

        // Wait for session to be ready (max 10 seconds)
        console.log('Waiting for session ready...');
        let session;
        try {
            session = await Promise.race([
                sessionReadyPromise,
                new Promise((_, reject) => setTimeout(() => reject(new Error('Session ready timeout')), 10000))
            ]);
            console.log('Session ready!');
        } catch (error) {
            console.error('Error waiting for session ready:', error);
            throw error;
        }

        // Check session ID
        expect(session).toBeDefined();
        expect(client.sessionId).toBeTruthy();
        console.log(`Session ID: ${client.sessionId}`);

        // Verify events
        const hasSessionCreated = events.some(e => e.type === 'session.created');
        const hasSessionUpdated = events.some(e => e.type === 'session.updated');

        console.log(`Events received: ${events.length}`);
        console.log(`Has session.created: ${hasSessionCreated}`);
        console.log(`Has session.updated: ${hasSessionUpdated}`);

        expect(hasSessionCreated).toBe(true);
        expect(hasSessionUpdated).toBe(true);

        // Send audio and verify connection stays alive
        console.log('Creating audio sample...');
        const sampleLength = 3200; // Match the sample size from Python
        const sampleBuffer = new ArrayBuffer(sampleLength * 2);
        const view = new Int16Array(sampleBuffer);

        // Fill with silence
        for (let i = 0; i < sampleLength; i++) {
            view[i] = 0;
        }

        // Send audio
        console.log('Sending audio...');
        const success = await client.sendAudio(new Uint8Array(sampleBuffer), 'uint8array');
        expect(success).toBe(true);

        // Wait and check for 1011 error (should not occur)
        console.log('Waiting 1 second to check for 1011 error...');
        await new Promise(resolve => setTimeout(resolve, 1000));

        const stillConnected = client.isConnected();
        console.log(`Still connected: ${stillConnected}`);
        expect(stillConnected).toBe(true);

        // Commit audio
        console.log('Committing audio...');
        const committed = await client.commitAudio();
        expect(committed).toBe(true);

        // Success - no 1011 error encountered!
        console.log('Test passed - no 1011 error occurred!');
    }, 20000); // 20 second timeout
}); 