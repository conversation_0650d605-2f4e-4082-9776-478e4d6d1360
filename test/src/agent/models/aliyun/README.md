# Aliyun Tests

This directory contains tests for the Aliyun DashScope integration, primarily focusing on the `AliyunBailianChatModel` and `AliyunRealtimeClient` classes.

## Test Organization

The tests are organized into the following categories:

### Core Functionality Tests
- `AliyunBailianChatModel.test.js` - Core functionality of the ChatModel
- `aliyun-api-validation.test.js` - API validation and parameter testing
- `langchain-v3-compliance.test.js` - LangChain v3 compatibility tests

### WebSocket Connection Tests
- `aliyun-websocket.test.js` - Basic WebSocket connection tests (mock)
- `aliyun-websocket.real-api.test.js` - WebSocket connection with real API (requires API key)
- `aliyun-1011-debug.test.js` - Specific tests for the 1011 error fix

### Audio Streaming Tests
- `AliyunBailianChatModel.sendRealtimeAudio.test.js` - Audio streaming functionality
- `aliyun-24khz-realtime.test.js` - 24kHz audio format testing
- `chunk-size-validation.test.js` - Audio chunk size validation

### HTTP API Tests
- `AliyunBailianChatModel.http-openai.test.js` - OpenAI compatibility layer tests
- `AliyunBailianChatModel.http-realtime.test.js` - HTTP realtime mode tests
- `aliyun-real-api-test.js` - Full integration tests with real API

### Tool Integration Tests
- `tts-tool-default.test.js` - Text-to-speech tool integration

## Running Tests

### Mock Tests
These tests don't require an API key and can be run with:
```
npm test -- test/src/agent/models/aliyun
```

### Real API Tests
These tests require an Aliyun API key and can be run with:
```
VITE_DASHSCOPE_API_KEY=your_api_key npm run test:real-api -- test/src/agent/models/aliyun/aliyun-websocket.real-api.test.js
```

### Verification Tests
To verify specific fixes (like the 1011 error fix), use:
```
VITE_DASHSCOPE_API_KEY=your_api_key npm run test:verify-aliyun-fix
```

## Test Utilities
- `verify-fix.js` - Standalone script to verify the 1011 error fix
- `direct-test.js` - Direct test implementation for debugging

## Related Documentation
- `doc/aliyun-1011-error-fix.md` - Documentation for the 1011 error fix
- `examples/aliyun/omni_realtime_client.py` - Reference Python implementation
