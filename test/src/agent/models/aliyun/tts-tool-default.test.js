import { describe, it, expect, vi } from 'vitest';
import { AliyunBailianChatModel } from '@/agent/models/AliyunBailianChatModel';

// Simulate a base64-encoded WAV string (short, valid header)
const SAMPLE_AUDIO_BASE64 = 'UklGRiQEAABXQVZFZm10IBAAAAABAAEARKwAAIhYAQACABAAZGF0YQAEAAD//f7/AgAA//8AAAEA//8AAP//AAD//wAA//8AAP/+';

describe('AliyunBailianChatModel - Default Play Audio Tool', () => {
    it('should include play_audio as a default tool and call it when audio is present', async () => {
        // Arrange: create model
        const model = new AliyunBailianChatModel({
            model: 'qwen-omni-turbo',
            apiMode: 'http',
            modalities: ['text', 'audio'],
            audioConfig: { voice: 'Ethan', format: 'wav' }
        });

        // Spy/mock the playAudio function
        const playAudioMock = vi.fn();
        model.bindTools([
            {
                name: 'play_audio',
                description: 'Play a base64-encoded audio string as audio output',
                schema: {
                    type: 'object',
                    properties: {
                        audio: { type: 'string', description: 'Base64-encoded audio data' },
                        format: { type: 'string', description: 'Audio format (e.g., wav, mp3)' }
                    },
                    required: ['audio', 'format']
                },
                func: playAudioMock
            }
        ]);

        // Simulate a generation with audio and tool_calls
        const generation = {
            text: 'Hello!',
            audio: SAMPLE_AUDIO_BASE64,
            tool_calls: [
                {
                    name: 'play_audio',
                    args: { audio: SAMPLE_AUDIO_BASE64, format: 'wav' },
                    type: 'tool_call',
                    id: 'call_playaudio1'
                }
            ]
        };

        // Act: call handleToolCalls directly
        model.handleToolCalls(generation);

        // Assert: The playAudio tool was called with correct args
        expect(playAudioMock).toHaveBeenCalled();
        const callArgs = playAudioMock.mock.calls[0][0];
        expect(callArgs.audio).toMatch(/^[A-Za-z0-9+/=]+$/); // base64
        expect(callArgs.format).toBe('wav');
    });
}); 