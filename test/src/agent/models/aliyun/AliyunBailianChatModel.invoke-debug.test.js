/**
 * TDD Debug Test: LLM API Service HTTP Request Verification
 * 
 * This test verifies that the LLM API service makes actual HTTP requests to the /llm server route.
 */

import { describe, test, expect, beforeEach, afterEach, vi } from 'vitest';
import { llmAPI } from '../../../../../src/media/api/llmAPI.ts';

// Mock fetch globally
const originalFetch = global.fetch;

describe('LLM API Service - HTTP Request Debug', () => {
    let mockFetch;

    beforeEach(() => {
        // Setup mock fetch
        mockFetch = vi.fn(() =>
            Promise.resolve({
                ok: true,
                json: () => Promise.resolve({
                    content: 'Test response from server',
                    audio: null,
                    metadata: { model: 'qwen-omni-turbo', provider: 'aliyun' }
                }),
                text: () => Promise.resolve(JSON.stringify({
                    content: 'Test response from server',
                    audio: null,
                    metadata: { model: 'qwen-omni-turbo', provider: 'aliyun' }
                }))
            })
        );
        global.fetch = mockFetch;
    });

    afterEach(() => {
        global.fetch = originalFetch;
        vi.clearAllMocks();
    });

    test('should make HTTP request to /llm route when invoke is called', async () => {
        const messages = [
            { role: 'user', content: 'Hello, can you hear me?' }
        ];

        const options = {
            provider: 'aliyun',
            model: 'qwen-omni-turbo',
            modalities: ['text', 'audio'],
            audioConfig: { voice: 'Ethan', format: 'wav' }
        };

        const result = await llmAPI.invoke(messages, options);

        // Verify fetch was called
        expect(mockFetch).toHaveBeenCalledTimes(1);

        // Verify the request was made to the correct endpoint
        expect(mockFetch).toHaveBeenCalledWith('http://localhost:2994/api/llm', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: expect.stringContaining('"provider":"aliyun"')
        });

        // Verify the request body contains expected data
        const callArgs = mockFetch.mock.calls[0];
        const requestBody = JSON.parse(callArgs[1].body);

        expect(requestBody.provider).toBe('aliyun');
        expect(requestBody.model).toBe('qwen-omni-turbo');
        expect(requestBody.messages).toHaveLength(1);
        expect(requestBody.messages[0].role).toBe('user');
        expect(requestBody.messages[0].content).toBe('Hello, can you hear me?');
        expect(requestBody.modalities).toEqual(['text', 'audio']);
        expect(requestBody.audioConfig.voice).toBe('Ethan');

        // Verify the response is properly formatted
        expect(result).toBeDefined();
        expect(result.content).toBe('Test response from server');
        expect(result.audio).toBe(null);
        expect(result.metadata).toBeDefined();
    });

    test('should handle audio input correctly', async () => {
        const messages = [
            {
                role: 'user',
                content: 'Test with audio',
                input_audio: { data: 'base64audio', format: 'wav' }
            }
        ];

        const options = {
            modalities: ['text', 'audio'],
            audioConfig: { voice: 'Serena', format: 'wav' }
        };

        await llmAPI.invoke(messages, options);

        const callArgs = mockFetch.mock.calls[0];
        const requestBody = JSON.parse(callArgs[1].body);

        expect(requestBody.messages[0].input_audio).toEqual({ data: 'base64audio', format: 'wav' });
    });

    test('should handle tools correctly', async () => {
        const messages = [
            { role: 'user', content: 'Call a tool for me' }
        ];

        const options = {
            tools: [
                {
                    name: 'play_audio',
                    description: 'Play audio response',
                    schema: {
                        type: 'object',
                        properties: {
                            audio: { type: 'string' },
                            format: { type: 'string' }
                        }
                    }
                }
            ],
            tool_choice: 'auto'
        };

        await llmAPI.invoke(messages, options);

        const callArgs = mockFetch.mock.calls[0];
        const requestBody = JSON.parse(callArgs[1].body);

        expect(requestBody.tools).toHaveLength(1);
        expect(requestBody.tools[0].name).toBe('play_audio');
        expect(requestBody.tool_choice).toBe('auto');
    });

    test('should handle fetch errors gracefully', async () => {
        // Mock failed response
        mockFetch = vi.fn(() =>
            Promise.resolve({
                ok: false,
                status: 500,
                statusText: 'Internal Server Error',
                text: () => Promise.resolve('Server error')
            })
        );
        global.fetch = mockFetch;

        const messages = [{ role: 'user', content: 'Test error handling' }];

        await expect(llmAPI.invoke(messages)).rejects.toThrow(
            'LLM API request failed: 500 Internal Server Error. Server error'
        );
    });

    test('should test connection method', async () => {
        const connectionResult = await llmAPI.testConnection();

        expect(mockFetch).toHaveBeenCalledWith('http://localhost:2994/api/llm', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: expect.stringContaining('"provider":"aliyun"')
        });

        expect(connectionResult).toBe(true);
    });
}); 