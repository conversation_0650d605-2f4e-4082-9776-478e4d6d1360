/**
 * Aliyun Realtime API Authorized Test
 * Tests actual Aliyun qwen-omni-turbo-realtime API with real API key
 * This test verifies if 1011 errors are caused by authorization, configuration, or implementation issues
 */

import { describe, it, expect, beforeAll, afterAll } from 'vitest';
import { getEnvVar } from '@/config/env';
import { ALIYUN_AUDIO_CONFIG, createEvent } from '@/agent/models/aliyun/config/AliyunConfig.js';

describe('Aliyun Realtime API Authorized Tests', () => {
  let apiKey;
  let apiAvailable = false;
  let testResults = [];

  beforeAll(async () => {
    // Get API key from environment
    apiKey = getEnvVar('VITE_DASHSCOPE_API_KEY', '');
    
    if (!apiKey) {
      console.warn('⚠️  No DASHSCOPE_API_KEY found - skipping authorized tests');
      return;
    }
    
    if (apiKey.length < 10) {
      console.warn('⚠️  DASHSCOPE_API_KEY appears invalid - skipping authorized tests');
      return;
    }
    
    apiAvailable = true;
    console.log('✅ API key found, running authorized tests...');
  });

  afterAll(() => {
    if (testResults.length > 0) {
      console.log('\n📊 Test Results Summary:');
      testResults.forEach(result => console.log(`- ${result}`));
    }
  });

  describe('API Authorization Tests', () => {
    it('should validate API key format and length', () => {
      if (!apiAvailable) {
        console.warn('Skipping API key validation - no key available');
        return;
      }

      expect(apiKey).toBeDefined();
      expect(apiKey.length).toBeGreaterThan(10);
      expect(typeof apiKey).toBe('string');
      
      // API keys typically have certain patterns
      const hasValidFormat = apiKey.length > 20 && !apiKey.includes(' ');
      expect(hasValidFormat).toBe(true);
      
      testResults.push('API key format validation: PASSED');
    });

    it('should test WebSocket connection with Authorization header', async () => {
      if (!apiAvailable) {
        console.warn('Skipping WebSocket connection test - no API key');
        return;
      }

      const wsUrl = 'wss://dashscope.aliyuncs.com/api-ws/v1/realtime?model=qwen-omni-turbo-realtime';
      
      return new Promise((resolve, reject) => {
        const timeout = setTimeout(() => {
          ws?.close();
          testResults.push('WebSocket connection: TIMEOUT (30s)');
          resolve(); // Don't reject - this gives us info about the timeout
        }, 30000);

        const ws = new WebSocket(wsUrl);
        let connectionState = 'CONNECTING';
        
        ws.onopen = () => {
          connectionState = 'CONNECTED';
          console.log('✅ WebSocket connected - Authorization likely succeeded');
          
          // Note: Browsers can't set Authorization headers, so this will likely fail
          // But the connection attempt itself tells us about network/basic auth issues
          
          clearTimeout(timeout);
          ws.close();
          testResults.push('WebSocket connection: CONNECTED (but no auth header support)');
          resolve();
        };

        ws.onmessage = (event) => {
          const data = JSON.parse(event.data);
          console.log('📥 Received message:', data.type);
          
          if (data.type === 'session.created') {
            console.log('✅ Session created successfully!');
            testResults.push('Session creation: SUCCESS');
          } else if (data.type === 'error') {
            console.log('❌ Error received:', data);
            testResults.push(`Session creation: ERROR (${data.error?.message || 'unknown'})`);
          }
        };

        ws.onerror = (error) => {
          console.log('❌ WebSocket error:', error);
          clearTimeout(timeout);
          testResults.push('WebSocket connection: ERROR');
          resolve();
        };

        ws.onclose = (event) => {
          console.log('🔌 WebSocket closed:', { code: event.code, reason: event.reason });
          clearTimeout(timeout);
          
          if (event.code === 1011) {
            testResults.push('WebSocket close: 1011 INTERNAL SERVER ERROR (this is our problem!)');
          } else if (event.code === 1006) {
            testResults.push('WebSocket close: 1006 ABNORMAL CLOSURE (network/auth issue)');
          } else if (event.code === 1000) {
            testResults.push('WebSocket close: 1000 NORMAL CLOSURE');
          } else {
            testResults.push(`WebSocket close: ${event.code} (${event.reason || 'unknown'})`);
          }
          
          resolve();
        };
      });
    }, 35000); // 35 second timeout

    it('should test HTTP API access for basic connectivity', async () => {
      if (!apiAvailable) {
        console.warn('Skipping HTTP API test - no API key');
        return;
      }

      try {
        // Test basic HTTP API to verify credentials and account access
        const response = await fetch('https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${apiKey}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            model: 'qwen-turbo', // Use simpler model for basic test
            input: {
              messages: [
                { role: 'user', content: 'Hello' }
              ]
            },
            parameters: {
              max_tokens: 10
            }
          })
        });

        if (response.ok) {
          console.log('✅ HTTP API access successful');
          testResults.push('HTTP API access: SUCCESS (credentials valid)');
          
          const data = await response.json();
          if (data.output) {
            testResults.push('HTTP API response: VALID (account functional)');
          }
        } else if (response.status === 401) {
          console.log('❌ HTTP API: 401 Unauthorized');
          testResults.push('HTTP API access: 401 UNAUTHORIZED (bad API key)');
        } else if (response.status === 403) {
          console.log('❌ HTTP API: 403 Forbidden');
          testResults.push('HTTP API access: 403 FORBIDDEN (account/model access denied)');
        } else {
          console.log('❌ HTTP API error:', response.status, response.statusText);
          testResults.push(`HTTP API access: ${response.status} ${response.statusText}`);
        }

      } catch (error) {
        console.log('❌ HTTP API request failed:', error.message);
        testResults.push(`HTTP API access: ERROR (${error.message})`);
      }
    }, 15000);
  });

  describe('Configuration Analysis', () => {
    it('should verify our configuration matches working Python implementation', () => {
      const ourConfig = {
        sampleRate: ALIYUN_AUDIO_CONFIG.sampleRate,
        chunkSize: ALIYUN_AUDIO_CONFIG.chunkSize,
        minInterval: ALIYUN_AUDIO_CONFIG.minIntervalMs,
        format: ALIYUN_AUDIO_CONFIG.inputFormat
      };

      const pythonConfig = {
        sampleRate: 24000,  // RATE = 24000
        chunkSize: 3200,    // CHUNK = 3200  
        minInterval: 200,   // asyncio.sleep(0.2)
        format: 'pcm16'     // paInt16
      };

      console.log('Configuration comparison:');
      console.log('Our config:', ourConfig);
      console.log('Python config:', pythonConfig);

      expect(ourConfig).toEqual(pythonConfig);
      testResults.push('Configuration matching: PERFECT MATCH with Python');
    });

    it('should calculate exact chunk sizes', () => {
      const samples = 3200;
      const pcm16Bytes = samples * 2; // 16-bit = 2 bytes per sample
      const base64Bytes = Math.ceil(pcm16Bytes * 4/3);
      
      console.log('Chunk size calculation:');
      console.log(`${samples} samples → ${pcm16Bytes} PCM16 bytes → ${base64Bytes} base64 bytes`);
      
      expect(pcm16Bytes).toBe(6400);
      expect(base64Bytes).toBe(8534); // Not 8533 as previously calculated
      
      testResults.push(`Chunk size: ${base64Bytes} bytes (should work if ≤ Python size)`);
    });
  });

  describe('Diagnostic Recommendations', () => {
    it('should provide debugging recommendations based on test results', () => {
      console.log('\n🔍 Debugging Recommendations:');
      
      if (!apiAvailable) {
        console.log('1. ❌ Set VITE_DASHSCOPE_API_KEY environment variable');
        console.log('2. ❌ Get valid Aliyun DashScope API key from console.aliyun.com');
        testResults.push('Primary issue: NO API KEY');
        return;
      }

      console.log('1. ✅ API key is available');
      console.log('2. ✅ Configuration matches Python implementation');
      console.log('3. 🔍 Run HTTP API test to verify credentials');
      console.log('4. 🔍 Run WebSocket test to check realtime API access');
      console.log('5. 🔍 Check if qwen-omni-turbo-realtime model requires special access');
      console.log('6. 🔍 Verify account has sufficient quota/credits');
      console.log('7. 🔍 Test from different network (possible regional restrictions)');
      
      testResults.push('Diagnostic: All configuration correct, likely auth/quota issue');
    });

    it('should suggest next steps based on 1011 error pattern', () => {
      console.log('\n💡 1011 Error Analysis:');
      console.log('- Pattern: Connection succeeds → session.created → first audio chunk → 1011 error');
      console.log('- This suggests: Authentication OK, but audio processing fails');
      console.log('- Possible causes:');
      console.log('  • Account lacks access to qwen-omni-turbo-realtime model');  
      console.log('  • Quota/rate limits exceeded (even for first request)');
      console.log('  • Model is in maintenance/unavailable');
      console.log('  • Regional restrictions on realtime API');
      console.log('  • Audio format still slightly incompatible despite matching Python');
      
      console.log('\n🎯 Next Steps:');
      console.log('1. Test with Python client using same API key');
      console.log('2. Contact Aliyun support about qwen-omni-turbo-realtime access');
      console.log('3. Try different model (if available)'); 
      console.log('4. Check Aliyun console for quota/usage limits');
      
      testResults.push('1011 pattern: Auth OK, audio processing fails → Account/quota issue likely');
    });
  });
});