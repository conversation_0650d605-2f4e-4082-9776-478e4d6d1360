/**
 * Direct test for Aliyun WebSocket Connection
 * Tests the realtime connection to <PERSON>yun Qwen-Omni service without test framework
 * Run with: node test/src/agent/models/aliyun/direct-test.js
 */

import { AliyunRealtimeClient } from '../../../../../src/agent/models/aliyun/streaming/AliyunRealtimeClient.js';

// Get API key from environment variables
const apiKey = process.env.VITE_DASHSCOPE_API_KEY || process.env.VITE_ALIYUN_API_KEY;

if (!apiKey) {
    console.error('❌ ERROR: No API key found in environment variables');
    console.error('Please run with: VITE_DASHSCOPE_API_KEY=your_key node test/src/agent/models/aliyun/direct-test.js');
    process.exit(1);
}

console.log('✅ API key found, length:', apiKey.length);

async function runTest() {
    console.log('📋 Starting direct test of AliyunRealtimeClient');

    // Initialize client
    const client = new AliyunRealtimeClient({
        apiK<PERSON>,
        model: 'qwen-omni-turbo-realtime',
        voice: '<PERSON><PERSON><PERSON>',
        enableDebugLogging: true
    });

    console.log('✅ Client initialized');

    // Track events
    const events = [];
    client.on('message', (msg) => {
        console.log(`📥 Received message:`, JSON.stringify(msg, null, 2));
        events.push(msg);
    });

    // Handle errors
    client.on('error', (error) => {
        console.error('❌ WebSocket error:', error);
    });

    // Handle close events
    client.on('close', (event) => {
        console.log('🔒 Connection closed:', event);
    });

    // Connect
    console.log('🔌 Connecting to Aliyun...');
    const connected = await client.connect();
    console.log(`🔌 Connected: ${connected}`);

    if (!connected) {
        console.error('❌ Failed to connect');
        process.exit(1);
    }

    // Wait for session ready
    console.log('⏳ Waiting for session ready...');
    await new Promise((resolve, reject) => {
        const timeout = setTimeout(() => {
            console.error('❌ Session ready timeout');
            reject(new Error('Session ready timeout'));
        }, 10000);

        client.on('sessionReady', (session) => {
            console.log('✅ Session ready!', JSON.stringify(session, null, 2));
            console.log(`✅ Session ID: ${client.sessionId}`);
            clearTimeout(timeout);
            resolve();
        });
    });

    // Log session status
    console.log(`✅ Session is ready: ${client.isSessionReady()}`);
    console.log(`✅ Session ID: ${client.sessionId}`);

    // Create audio sample
    console.log('📝 Creating audio sample...');
    const sampleLength = 3200; // 3200 samples
    const sampleBuffer = new ArrayBuffer(sampleLength * 2); // 16-bit = 2 bytes per sample
    const view = new Int16Array(sampleBuffer);
    // Fill with silence (zeros)
    for (let i = 0; i < sampleLength; i++) {
        view[i] = 0;
    }

    console.log(`📝 Audio sample created: ${sampleLength} samples, ${sampleBuffer.byteLength} bytes`);

    // Send audio
    console.log('📤 Sending audio...');
    const success = await client.sendAudio(new Uint8Array(sampleBuffer), 'uint8array');
    console.log(`📤 Audio sent: ${success}`);

    // Wait to check for potential 1011 error
    console.log('⏳ Waiting 1 second to check for 1011 error...');
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Check connection
    const stillConnected = client.isConnected();
    console.log(`🔌 Still connected: ${stillConnected}`);

    if (!stillConnected) {
        console.error('❌ Connection lost - possible 1011 error!');
        process.exit(1);
    }

    // Commit audio
    console.log('📤 Committing audio...');
    const committed = await client.commitAudio();
    console.log(`📤 Audio committed: ${committed}`);

    // Wait for potential response
    console.log('⏳ Waiting for potential response...');
    await new Promise(resolve => setTimeout(resolve, 3000));

    // Disconnect
    console.log('🔌 Disconnecting...');
    await client.disconnect();
    console.log('🔌 Disconnected');

    console.log('✅ Test completed successfully - no 1011 error occurred!');
    process.exit(0);
}

// Run the test
runTest().catch(error => {
    console.error('❌ Test failed with error:', error);
    process.exit(1);
}); 