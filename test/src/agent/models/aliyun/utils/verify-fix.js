/**
 * Verify fix for <PERSON>yun WebSocket 1011 Error
 * 
 * Simple script to verify our fix directly.
 * Run with: npm run test:verify-fix
 */

// Import required modules
import { AliyunRealtimeClient } from '../../../../../src/agent/models/aliyun/streaming/AliyunRealtimeClient.js';

// Simple test function
async function verifyFix() {
    // Get API key
    const apiKey = process.env.VITE_DASHSCOPE_API_KEY || process.env.VITE_ALIYUN_API_KEY;

    if (!apiKey) {
        console.error('❌ No API key found in environment variables');
        console.error('Please set VITE_DASHSCOPE_API_KEY before running this test');
        return false;
    }

    console.log('✅ API key found, length:', apiKey.length);

    // Create client
    const client = new AliyunRealtimeClient({
        apiKey,
        model: 'qwen-omni-turbo-realtime',
        voice: '<PERSON><PERSON><PERSON>',
        enableDebugLogging: true
    });

    console.log('✅ Client initialized');

    // Setup event handlers
    client.on('message', (msg) => {
        console.log(`📥 Received message type: ${msg.type}`);
    });

    client.on('error', (error) => {
        console.error('❌ WebSocket error:', error);
    });

    client.on('close', (event) => {
        console.log(`🔒 Connection closed:`, event);
    });

    // Connect
    console.log('🔌 Connecting...');
    try {
        const connected = await client.connect();
        console.log(`🔌 Connected: ${connected}`);

        if (!connected) {
            console.error('❌ Failed to connect');
            return false;
        }

        // Wait for session ready
        console.log('⏳ Waiting for session ready...');
        await new Promise((resolve, reject) => {
            const timeout = setTimeout(() => {
                reject(new Error('Session ready timeout'));
            }, 10000);

            client.on('sessionReady', () => {
                console.log(`✅ Session ready! ID: ${client.sessionId}`);
                clearTimeout(timeout);
                resolve();
            });
        });

        // Create audio sample (silence)
        const sampleLength = 3200; // 3200 samples
        const sampleBuffer = new ArrayBuffer(sampleLength * 2); // 16-bit = 2 bytes per sample
        const view = new Int16Array(sampleBuffer);
        for (let i = 0; i < sampleLength; i++) {
            view[i] = 0;
        }

        console.log(`📝 Audio sample created: ${sampleLength} samples`);

        // Send audio
        console.log('📤 Sending audio...');
        const success = await client.sendAudio(new Uint8Array(sampleBuffer), 'uint8array');
        console.log(`📤 Audio sent: ${success}`);

        // Wait to check for 1011 error
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Check connection status
        const stillConnected = client.isConnected();
        console.log(`🔌 Still connected: ${stillConnected}`);

        if (!stillConnected) {
            console.error('❌ Connection lost - possible 1011 error!');
            return false;
        }

        // Commit audio
        console.log('📤 Committing audio...');
        const committed = await client.commitAudio();
        console.log(`📤 Audio committed: ${committed}`);

        // Wait for response
        await new Promise(resolve => setTimeout(resolve, 3000));

        // Clean up
        await client.disconnect();
        console.log('🔌 Disconnected');

        console.log('✅ Test successful - no 1011 error occurred!');
        return true;
    } catch (error) {
        console.error('❌ Test failed with error:', error);
        if (client) {
            try {
                await client.disconnect();
            } catch (e) {
                // Ignore disconnect errors
            }
        }
        return false;
    }
}

// Run the test when invoked directly
const isMainModule = import.meta.url === `file://${process.argv[1]}`;
if (isMainModule) {
    verifyFix().then(success => {
        process.exit(success ? 0 : 1);
    });
}

export { verifyFix }; 