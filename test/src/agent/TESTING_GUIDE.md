# TESTING GUIDE

## Test Organization (2025-07)

- **Unit Tests (Model logic, compliance):**
  - `test/src/agent/models/langchain-v3-compliance.test.js`
- **Integration Tests (API, tool calling, multimodal, streaming):**
  - `test/src/agent/integration/tool-calling-audio-tdd.test.js`
  - `test/src/agent/integration/multimodal-audio-tdd.test.js`
  - `test/src/agent/integration/multimodal-qwen.test.js` (legacy)
- **Voice Reply System:**
  - `test/src/agent/voice-reply.test.js`

## Notes
- Prefer adding new model/unit tests to `models/`
- Prefer adding new integration/multimodal tests to `integration/`
- `multimodal-qwen.test.js` is legacy; merge unique cases into TDD tests if needed
- All tests should be run with `TEST_REAL_API=true` for full coverage

## 🎯 Issues Being Tested

### 1. VAD (Voice Activity Detection) Cleanup Issues
- **Problem**: VAD not stopping when listen button is turned off
- **Problem**: Maximum call stack size exceeded in `onSpeechEnd`
- **Root Cause**: Improper resource cleanup and recursive event handlers

### 2. Multi-Modal API Support
- **Problem**: Limited support for multiple modalities in single message
- **Opportunity**: New Qwen-Omni API supports audio + video + image + text
- **Goal**: Update message formatting to support new capabilities

## 📁 Test Files

### `vad-cleanup.test.js`
Comprehensive tests for VAD and audio resource management:

- **VAD Lifecycle Management**: Proper start/stop with resource cleanup
- **Audio Capture Management**: AudioContext and ScriptProcessor cleanup
- **Speech Event Handling**: Prevention of recursive calls and stack overflow
- **UI Integration**: Listen button integration and rapid toggling
- **Event Listener Cleanup**: Memory leak prevention
- **Error Recovery**: Graceful handling of failures

### `multimodal-qwen.test.js`
Tests for new multi-modal Qwen-Omni API format:

- **Message Format Construction**: All combinations of modalities
- **Base64 Encoding Support**: Local file handling
- **API Integration**: HTTP and WebSocket message formatting
- **Error Handling**: Missing modalities and validation
- **MediaHandler Integration**: File processing and conversion

### `run-issue-tests.js`
Test runner with comprehensive reporting:

- **Automated Execution**: Runs all issue-related tests
- **Progress Reporting**: Real-time test status
- **Summary Reports**: Pass/fail rates and recommendations
- **Implementation Readiness**: Next steps based on results

## 🚀 Running Tests

### Quick Start
```bash
# From project root
cd test/src/agent

# Run all issue resolution tests
node run-issue-tests.js

# Run specific test categories
node run-issue-tests.js vad        # VAD cleanup tests only
node run-issue-tests.js multimodal # Multi-modal tests only
```

### Using Package Scripts
```bash
# Add to package.json scripts:
"test:agent-issues": "node test/src/agent/run-issue-tests.js",
"test:vad": "node test/src/agent/run-issue-tests.js vad",
"test:multimodal": "node test/src/agent/run-issue-tests.js multimodal"

# Then run:
npm run test:agent-issues
npm run test:vad
npm run test:multimodal
```

### Individual Test Files
```bash
# Run with vitest directly
npx vitest run test/src/agent/vad-cleanup.test.js
npx vitest run test/src/agent/multimodal-qwen.test.js

# With watch mode for development
npx vitest watch test/src/agent/vad-cleanup.test.js
```

## 📊 Understanding Test Results

### Success Indicators
- ✅ **All tests pass**: Ready for implementation
- 📊 **100% success rate**: High confidence in solutions
- 🎯 **Specific issue coverage**: Each problem area tested

### Failure Indicators
- ❌ **Test failures**: Review and fix before implementation
- ⚠️ **Mock issues**: Validate test setup and dependencies
- 🔍 **Assertion errors**: Logic needs refinement

### Report Sections

#### Test Summary
```
📊 TEST SUMMARY REPORT
⏱️  Total Duration: 15.23s
✅ Passed: 45
❌ Failed: 0
📈 Total: 45
📊 Success Rate: 100.0%
```

#### Issue Resolution Status
```
🎯 ISSUE RESOLUTION STATUS

1️⃣ VAD Cleanup & Stack Overflow Issues:
   ✅ Tests pass - Ready to implement fixes
   💡 Next: Apply VAD cleanup patterns to main code

2️⃣ Multi-Modal API Support:
   ✅ Tests pass - Ready to implement multi-modal support
   💡 Next: Update AliyunBailianChatModel message formatting
```

#### Implementation Readiness
```
🎉 ALL TESTS PASS - READY FOR IMPLEMENTATION!
📋 Implementation Order:
   1. Implement VAD cleanup fixes
   2. Add multi-modal message support
   3. Update UI to handle multiple modalities
   4. Test with real API calls
```

## 🔧 Implementation Strategy

### Phase 1: VAD Cleanup (If Tests Pass)

1. **Update MediaCaptureManager**:
   ```javascript
   async stopCapture() {
       // Cleanup VAD instance
       if (this.vadInstance) {
           await this.vadInstance.stop();
           this.vadInstance = null;
       }
       
       // Cleanup audio resources
       if (this.scriptProcessor) {
           this.scriptProcessor.disconnect();
           this.scriptProcessor = null;
       }
       
       if (this.audioContext) {
           await this.audioContext.close();
           this.audioContext = null;
       }
       
       // Clear buffers and state
       this.audioBuffer = [];
       this._isVADActive = false;
       this._speechStarted = false;
   }
   ```

2. **Add Speech End Protection**:
   ```javascript
   onSpeechEnd() {
       if (this._processingSpeechEnd) {
           return; // Prevent recursive calls
       }
       
       this._processingSpeechEnd = true;
       
       try {
           // Process audio without re-triggering
           this.processAudioBuffer();
       } finally {
           this._processingSpeechEnd = false;
       }
   }
   ```

3. **Update UI Listen Button**:
   ```javascript
   async stopListening() {
       this.isListening = false;
       await this.mediaCapture.stopCapture();
       
       // Remove event listeners
       this._cleanupEventListeners();
   }
   ```

### Phase 2: Multi-Modal Support (If Tests Pass)

1. **Update Message Construction**:
   ```javascript
   _formatMultiModalMessage(input) {
       const content = [];
       
       if (input.imageUrl) {
           content.push({ 
               type: 'image_url', 
               image_url: { url: input.imageUrl } 
           });
       }
       
       if (input.audioData) {
           content.push({ 
               type: 'audio_url', 
               audio_url: { 
                   data: input.audioData, 
                   format: 'wav' 
               } 
           });
       }
       
       if (input.videoUrl) {
           content.push({ 
               type: 'video_url', 
               video_url: { url: input.videoUrl } 
           });
       }
       
       if (input.text) {
           content.push({ type: 'text', text: input.text });
       }
       
       return content;
   }
   ```

2. **Update AliyunBailianChatModel**:
   ```javascript
   async invoke(messages, options = {}) {
       const formattedMessages = messages.map(m => {
           if (Array.isArray(m.content)) {
               // Multi-modal content already formatted
               return { role: m.role, content: m.content };
           } else {
               // Single modality - convert to array format
               return { role: m.role, content: m.content };
           }
       });
       
       // Rest of the method...
   }
   ```

## 🧪 Test Development Guidelines

### Adding New Tests

1. **Follow Existing Patterns**:
   - Use vitest framework
   - Mock external dependencies
   - Test both success and failure cases

2. **Test Structure**:
   ```javascript
   describe('Feature Category', () => {
       beforeEach(() => {
           // Setup
       });
       
       describe('Specific Functionality', () => {
           it('should handle expected case', () => {
               // Test implementation
           });
           
           it('should handle error case', () => {
               // Error handling test
           });
       });
   });
   ```

3. **Mock Strategy**:
   - Mock external APIs and services
   - Use realistic data in mocks
   - Test both mock and integration scenarios

### Debugging Failed Tests

1. **Check Mock Setup**:
   - Verify all dependencies are mocked
   - Ensure mock implementations match expected interfaces

2. **Review Assertions**:
   - Check expected vs actual values
   - Validate test logic and assumptions

3. **Isolate Issues**:
   - Run tests individually
   - Use `console.log` for debugging
   - Check test environment setup

## 📋 Next Steps After Tests Pass

1. **Implement VAD Fixes**: Apply cleanup patterns from tests
2. **Add Multi-Modal Support**: Update message formatting
3. **Update UI Components**: Support multiple modality selection
4. **Integration Testing**: Test with real API calls
5. **User Testing**: Validate fixes resolve original issues
6. **Documentation**: Update API docs and usage guides

## ⚠️ Important Notes

- **Run tests before implementation**: Validate approach first
- **Fix failing tests immediately**: Don't proceed with broken tests
- **Maintain test coverage**: Add tests for new functionality
- **Regular test runs**: Include in CI/CD pipeline
- **Real API testing**: Supplement with integration tests

## 🔗 Related Documentation

- [Agent Testing Overview](README.md)
- [VAD Integration Guide](../../../src/media/README.md)
- [Multi-Modal API Documentation](../../../doc/qwen-omni-api.md)
- [Testing Best Practices](../../README.md) 