/**
 * LangGraph Agent Streaming Tests
 * Tests the LangGraph agent streaming implementation using real vLLM API calls via apiProxy.ts
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { LangGraphAgentService } from '@/agent/core.js';
import { fetchLLMApi } from '@/utils/apiProxy.ts';

// Test configuration - use real endpoints for integration testing
const TEST_CONFIG = {
    model: 'Qwen/Qwen2.5-Omni-7B',
    temperature: 0.7,
    maxTokens: 100, // Keep small for fast tests
    timeout: 15000, // 15 second timeout
    useRealAPI: process.env.TEST_REAL_API === 'true'
};

describe('LangGraph Agent Streaming Tests', () => {
    let agentService;
    let testCallbacks;

    beforeEach(async () => {
        // Setup test callbacks to capture streaming events
        testCallbacks = {
            onTextChunk: vi.fn(),
            onToolCall: vi.fn(),
            onComplete: vi.fn(),
            onError: vi.fn(),
            onConversationUpdate: vi.fn()
        };

        // Create agent service with streaming enabled by default
        agentService = new LangGraphAgentService({
            model: TEST_CONFIG.model,
            temperature: TEST_CONFIG.temperature,
            maxTokens: TEST_CONFIG.maxTokens,
            autoRegisterTools: true,
            useLangGraphStreaming: true,
            services: {
                onConversationUpdate: testCallbacks.onConversationUpdate
            },
            // Enhanced streaming configuration
            streamingMode: 'messages',
            onStateUpdate: testCallbacks.onStateUpdate,
            onModeChange: testCallbacks.onModeChange,
            onConversationUpdate: testCallbacks.onConversationUpdate
        });

        // Initialize for real API testing
        if (TEST_CONFIG.useRealAPI) {
            try {
                await agentService.initialize();
            } catch (error) {
                console.warn('Could not initialize agent service:', error.message);
            }
        }
    });

    afterEach(() => {
        if (agentService) {
            agentService.dispose();
        }
        vi.clearAllMocks();
    });

    describe('Agent Service Streaming', () => {
        it('should initialize with streaming enabled by default', () => {
            expect(agentService).toBeDefined();
            expect(agentService.streamProcessor).toBeDefined();
            expect(agentService.tools).toBeDefined();
            expect(agentService.memoryManager).toBeDefined();
        });

        it('should use LangGraph createReactAgent for tool integration', () => {
            expect(agentService.agent).toBeDefined();
            expect(Array.isArray(agentService.tools)).toBe(true);
        });

        it('should have LangGraph streaming integrated', () => {
            expect(agentService.streamProcessor).toBeDefined();
            expect(agentService.streamProcessor.isNative).toBe(true);
            expect(typeof agentService.processLangGraphStream).toBe('function');
        });
    });

    describe('Real vLLM API Integration via apiProxy', () => {
        it('should connect to vLLM API successfully', async () => {
            if (!TEST_CONFIG.useRealAPI) {
                console.warn('Skipping real API test - set TEST_REAL_API=true to enable');
                return;
            }

            try {
                const response = await fetchLLMApi('v1/models');
                expect(response.ok).toBe(true);

                const data = await response.json();
                expect(data).toBeDefined();
                expect(data.data || data.models).toBeDefined();
            } catch (error) {
                console.warn('vLLM API not available for testing:', error.message);
            }
        }, TEST_CONFIG.timeout);

        it('should make streaming API calls successfully', async () => {
            if (!TEST_CONFIG.useRealAPI) {
                console.warn('Skipping streaming API test - set TEST_REAL_API=true to enable');
                return;
            }

            try {
                const response = await fetchLLMApi('v1/chat/completions', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        model: TEST_CONFIG.model,
                        messages: [{ role: 'user', content: 'Say hello' }],
                        stream: true,
                        max_tokens: 50
                    })
                });

                expect(response.ok).toBe(true);
                expect(response.body).toBeDefined();

                // Test that we can read from the stream
                const reader = response.body.getReader();
                const { done, value } = await reader.read();

                expect(done).toBe(false);
                expect(value).toBeDefined();

                reader.releaseLock();
            } catch (error) {
                console.warn('vLLM streaming API not available for testing:', error.message);
            }
        }, TEST_CONFIG.timeout);
    });

    describe('LangGraph Agent Streaming Workflow', () => {
        it('should handle simple text input streaming', async () => {
            if (!TEST_CONFIG.useRealAPI || !agentService._initialized) {
                console.warn('Skipping test - API not available or service not initialized');
                return;
            }

            try {
                const input = 'Hello, how are you?';
                const options = {
                    sessionId: 'test-session',
                    // stream is true by default
                };

                const streamGenerator = await agentService.generateResponse(input, options);
                expect(streamGenerator).toBeDefined();
                expect(typeof streamGenerator[Symbol.asyncIterator]).toBe('function');

                const streamResults = [];
                for await (const chunk of streamGenerator) {
                    streamResults.push(chunk);

                    // Verify chunk structure
                    expect(chunk).toHaveProperty('type');

                    // Break after a few chunks to avoid long test
                    if (streamResults.length >= 3) break;
                }

                expect(streamResults.length).toBeGreaterThan(0);

            } catch (error) {
                console.warn('Streaming test failed:', error.message);
            }
        }, TEST_CONFIG.timeout);

        it('should handle non-streaming invoke when explicitly requested', async () => {
            if (!TEST_CONFIG.useRealAPI || !agentService._initialized) {
                console.warn('Skipping test - API not available or service not initialized');
                return;
            }

            try {
                const input = 'Say hello briefly';
                const options = {
                    sessionId: 'test-session-2',
                    stream: false // Explicitly disable streaming
                };

                const result = await agentService.generateResponse(input, options);

                expect(result).toBeDefined();
                expect(typeof result).toBe('string');
                expect(result.length).toBeGreaterThan(0);

            } catch (error) {
                console.warn('Non-streaming test failed:', error.message);
            }
        }, TEST_CONFIG.timeout);

        it('should handle tool calling in streaming mode', async () => {
            if (!TEST_CONFIG.useRealAPI || !agentService._initialized) {
                console.warn('Skipping tool test - API not available or service not initialized');
                return;
            }

            try {
                const input = 'Show me a greeting animation';
                const options = {
                    sessionId: 'test-tools',
                    configurable: { useTools: true }
                };

                const events = [];
                const streamGenerator = await agentService.generateResponse(input, options);

                for await (const chunk of streamGenerator) {
                    events.push(chunk);

                    // Look for tool-related events
                    if (chunk.type === 'tool_start') {
                        expect(chunk.data).toBeDefined();
                        expect(chunk.data.name).toBeDefined();
                    } else if (chunk.type === 'tool_end') {
                        expect(chunk.data).toBeDefined();
                        expect(chunk.data.name).toBeDefined();
                        expect(chunk.data.output).toBeDefined();
                    }

                    // Break after reasonable number of events
                    if (events.length >= 10) break;
                }

                expect(events.length).toBeGreaterThan(0);

            } catch (error) {
                console.warn('Tool calling test failed:', error.message);
            }
        }, TEST_CONFIG.timeout);
    });

    describe('Memory and Context Integration', () => {
        it('should maintain conversation context across streaming calls', async () => {
            if (!TEST_CONFIG.useRealAPI || !agentService._initialized) {
                console.warn('Skipping context test - API not available');
                return;
            }

            try {
                const sessionId = 'test-context-session';

                // First message
                const firstInput = 'My name is Alice';
                const firstOptions = { sessionId, stream: false };
                const firstResponse = await agentService.generateResponse(firstInput, firstOptions);
                expect(firstResponse).toBeDefined();

                // Second message referencing first
                const secondInput = 'What is my name?';
                const secondOptions = { sessionId };

                const events = [];
                const streamGenerator = await agentService.generateResponse(secondInput, secondOptions);

                for await (const chunk of streamGenerator) {
                    events.push(chunk);
                    if (events.length >= 5) break;
                }

                expect(events.length).toBeGreaterThan(0);

            } catch (error) {
                console.warn('Context test failed:', error.message);
            }
        }, TEST_CONFIG.timeout * 2);
    });

    describe('LangGraph Native Streaming', () => {
        it('should use LangGraph native streaming capabilities', async () => {
            if (!TEST_CONFIG.useRealAPI || !agentService._initialized) {
                console.warn('Skipping LangGraph streaming test - API not available');
                return;
            }

            try {
                const input = 'Tell me a short story';
                const options = { sessionId: 'langgraph-test' };

                // Should use LangGraph's native streaming
                const streamGenerator = await agentService.generateResponse(input, options);
                expect(streamGenerator).toBeDefined();

                let chunkCount = 0;
                for await (const chunk of streamGenerator) {
                    chunkCount++;
                    // Verify LangGraph chunk format
                    expect(chunk).toHaveProperty('type');
                    if (chunkCount >= 3) break; // Don't run forever
                }

                expect(chunkCount).toBeGreaterThan(0);

            } catch (error) {
                console.warn('LangGraph streaming test failed:', error.message);
            }
        }, TEST_CONFIG.timeout);
    });

    describe('Error Handling and Recovery', () => {
        it('should handle malformed input gracefully', async () => {
            try {
                const malformedInput = { invalidStructure: true };
                const options = { sessionId: 'error-test' };

                const result = await agentService.generateResponse(malformedInput, options);

                // Should either handle gracefully or throw appropriate error
                expect(result).toBeDefined();

            } catch (error) {
                // Error is acceptable for malformed input
                expect(error).toBeDefined();
            }
        });

        it('should handle network failures gracefully', async () => {
            // Create service with invalid endpoint
            const invalidService = new LangGraphAgentService({
                vllmEndpoint: 'http://invalid-endpoint:9999',
                model: TEST_CONFIG.model
            });

            try {
                await invalidService.initialize();
            } catch (error) {
                // Network error is expected
                expect(error).toBeDefined();
            }

            invalidService.dispose();
        });
    });

    describe('Performance Monitoring', () => {
        it('should provide streaming performance metrics', () => {
            if (agentService.streamProcessor) {
                const status = agentService.streamProcessor;
                expect(status).toHaveProperty('streamMode');
                expect(status).toHaveProperty('isNative');
                expect(status.streamMode).toBe('messages');
                expect(status.isNative).toBe(true);
            }
        });

        it('should track resource usage during streaming', async () => {
            if (!TEST_CONFIG.useRealAPI || !agentService._initialized) {
                console.warn('Skipping performance test - API not available');
                return;
            }

            try {
                const startTime = Date.now();
                const input = 'Count to three';
                const options = { sessionId: 'performance-test' };

                const events = [];
                const streamGenerator = await agentService.generateResponse(input, options);

                for await (const chunk of streamGenerator) {
                    events.push(chunk);
                    if (events.length >= 5) break;
                }

                const duration = Date.now() - startTime;

                expect(events.length).toBeGreaterThan(0);
                expect(duration).toBeLessThan(TEST_CONFIG.timeout);

                // Log performance metrics
                console.log(`Streaming performance: ${events.length} events in ${duration}ms`);

            } catch (error) {
                console.warn('Performance test failed:', error.message);
            }
        }, TEST_CONFIG.timeout);
    });
});
