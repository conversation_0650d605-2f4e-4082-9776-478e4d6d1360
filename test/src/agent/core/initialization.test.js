/**
 * Test suite for LangChain Core Service - Initialization Tests
 * Tests initialization, configuration, and setup functionality
 */

// Import mocks
require('../setup/mocks');

// Import test utilities
const { mockVllmConfig } = require('../setup/mocks');

// Import the class to test
import { LangChainCoreService } from '@/agent/core.js';

describe('LangChainCoreService - Initialization', () => {
    let coreService;

    beforeEach(() => {
        jest.clearAllMocks();
        coreService = new LangChainCoreService();
    });

    afterEach(() => {
        if (coreService && typeof coreService.cleanup === 'function') {
            coreService.cleanup();
        }
    });

    describe('Default Configuration', () => {
        test('should initialize with default vLLM configuration', () => {
            expect(coreService).toBeDefined();
            expect(coreService.vllmEndpoint).toBe(mockVllmConfig.vllmEndpoint);
            expect(coreService.model).toBe(mockVllmConfig.model);
            expect(coreService.temperature).toBe(mockVllmConfig.temperature);
            expect(coreService.maxTokens).toBe(mockVllmConfig.maxTokens);
        });

        test('should use vLLM as default provider', () => {
            expect(coreService.vllmEndpoint).toContain('localhost:8000');
            expect(coreService.model).toBe('Qwen2.5-Omni-7B');
        });

        test('should initialize required components', () => {
            expect(coreService.llm).toBeDefined();
            expect(coreService.stringOutputParser).toBeDefined();
            expect(coreService.jsonOutputParser).toBeDefined();
            expect(coreService.multimodalProcessor).toBeDefined();
            expect(coreService.chain).toBeDefined();
        });
    });

    describe('Custom Configuration', () => {
        test('should accept custom vLLM endpoint and model', () => {
            const customOptions = {
                vllmEndpoint: 'http://custom-vllm:9000',
                model: 'custom-model',
                temperature: 0.5,
                maxTokens: 1024
            };

            const customService = new LangChainCoreService(customOptions);

            expect(customService.vllmEndpoint).toBe(customOptions.vllmEndpoint);
            expect(customService.model).toBe(customOptions.model);
            expect(customService.temperature).toBe(customOptions.temperature);
            expect(customService.maxTokens).toBe(customOptions.maxTokens);
        });

        test('should validate configuration parameters', () => {
            const invalidOptions = {
                temperature: 2.0, // Invalid: > 1.0
                maxTokens: -100   // Invalid: negative
            };

            expect(() => {
                new LangChainCoreService(invalidOptions);
            }).not.toThrow(); // Service should handle invalid params gracefully
        });
    });

    describe('Tool Initialization', () => {
        test('should initialize animation tools', () => {
            expect(coreService.animationTool).toBeDefined();
            expect(coreService.llmWithTools).toBeDefined();
        });

        test('should register all available tools', () => {
            // Mock returns empty array, so this tests the structure
            expect(typeof coreService.initializeTools).toBe('function');
        });
    });

    describe('Configuration Validation', () => {
        test('should prioritize vLLM configuration for testing', () => {
            // Ensure vLLM is the primary provider
            expect(coreService.vllmEndpoint).toBeTruthy();
            expect(coreService.vllmEndpoint).toMatch(/localhost|127\.0\.0\.1/);
        });

        test('should have proper fallback configuration', () => {
            const service = new LangChainCoreService({});
            expect(service.vllmEndpoint).toBeDefined();
            expect(service.model).toBeDefined();
        });
    });

    describe('Error Handling', () => {
        test('should handle initialization errors gracefully', () => {
            // This test ensures that even with mocked dependencies,
            // the service initializes without throwing
            expect(() => {
                new LangChainCoreService();
            }).not.toThrow();
        });

        test('should handle missing configuration gracefully', () => {
            const service = new LangChainCoreService(null);
            expect(service).toBeDefined();
        });
    });
});
