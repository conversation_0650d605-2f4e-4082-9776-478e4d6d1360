/**
 * Specific test for ToolNode TTS Integration Fix
 * Verifies that TTS tools are actually executed via LangGraph's ToolNode
 */

import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest';

// Mock logger
vi.mock('../../../src/utils/logger.js', () => ({
    createLogger: vi.fn(() => ({
        info: vi.fn(),
        debug: vi.fn(),
        warn: vi.fn(),
        error: vi.fn(),
        setLogLevel: vi.fn()
    })),
    LogLevel: { DEBUG: 0, INFO: 1, WARN: 2, ERROR: 3, NONE: 4 }
}));

// Mock API proxy
vi.mock('../../../src/utils/apiProxy.js', () => ({
    fetchVllmApi: vi.fn()
}));

import { LangGraphAgentService } from '@/agent/core.js';

describe('ToolNode TTS Integration Fix', () => {
    let agentService;
    let mockTTSService;
    let mockAudioPlayer;

    beforeEach(async () => {
        vi.clearAllMocks();

        // Create mock TTS service with detailed tracking
        mockTTSService = {
            speak: vi.fn().mockImplementation(async (text, options) => {
                console.log(`🔊 Mock TTS called with: "${text}"`);
                return { success: true, duration: 1000 };
            }),
            constructor: { name: 'MockTTSService' }
        };

        mockAudioPlayer = {
            playChunk: vi.fn().mockResolvedValue(true)
        };

        agentService = new LangGraphAgentService({
            autoRegisterTools: true,
            services: {
                ttsService: mockTTSService,
                audioPlayer: mockAudioPlayer
            }
        });

        await agentService.initialize();
    });

    afterEach(() => {
        if (agentService?.dispose) {
            agentService.dispose();
        }
    });

    describe('Core TTS Tool Execution', () => {
        it('should execute speak_response tool when LLM makes tool call', async () => {
            console.log('🧪 Testing ToolNode TTS execution...');

            // Create mock input that simulates LLM generating a tool call
            const mockInput = {
                messages: [{
                    role: 'assistant',
                    content: 'I will speak this message',
                    tool_calls: [{
                        name: 'speak_response',
                        args: { text: 'Hello, this is a test message' },
                        id: 'test_call_123',
                        type: 'tool_call'
                    }]
                }]
            };

            console.log('📝 Input prepared:', JSON.stringify(mockInput, null, 2));

            // Execute via ToolNode
            const result = await agentService.toolNode.invoke(mockInput);

            console.log('📤 ToolNode result:', result);
            console.log('🔍 TTS service calls:', mockTTSService.speak.mock.calls);

            // Verify tool execution
            expect(result).toBeDefined();
            expect(result.messages).toBeDefined();

            // THIS IS THE KEY TEST: Verify TTS service was actually called
            expect(mockTTSService.speak).toHaveBeenCalledWith(
                'Hello, this is a test message',
                expect.objectContaining({
                    stream: true,
                    handleStreamInternally: true
                })
            );

            console.log('✅ TTS tool execution verified!');
        });

        it('should route to ToolNode when agent generates tool calls', async () => {
            console.log('🧪 Testing LangGraph routing to ToolNode...');

            // Mock the LLM to return a response with tool calls
            const mockLLMResponse = {
                content: 'I will speak this for you',
                tool_calls: [{
                    name: 'speak_response',
                    args: { text: 'Routing test message' },
                    id: 'route_test',
                    type: 'tool_call'
                }]
            };

            agentService.model.invoke = vi.fn().mockResolvedValue(mockLLMResponse);

            // Test the routing logic
            const state = {
                messages: [
                    { content: 'User message' },
                    mockLLMResponse  // This should trigger tool execution
                ]
            };

            const route = agentService._routeAfterGeneration(state);
            console.log('🎯 Route decision:', route);

            expect(route).toBe('executeTools');
            console.log('✅ Routing to ToolNode verified!');
        });

        it('should inject services into ToolNode for tool access', async () => {
            console.log('🧪 Testing service injection...');

            // Verify services are properly configured
            expect(agentService.services.ttsService).toBe(mockTTSService);
            expect(agentService.services.audioPlayer).toBe(mockAudioPlayer);

            // Test that ToolNode has access to services
            const mockInput = {
                messages: [{
                    role: 'assistant',
                    content: '',
                    tool_calls: [{
                        name: 'speak_response',
                        args: { text: 'Service injection test' },
                        id: 'service_test',
                        type: 'tool_call'
                    }]
                }]
            };

            await agentService.toolNode.invoke(mockInput);

            // Verify TTS service was called (proving service injection worked)
            expect(mockTTSService.speak).toHaveBeenCalledWith(
                'Service injection test',
                expect.objectContaining({
                    stream: true,
                    handleStreamInternally: true
                })
            );

            console.log('✅ Service injection verified!');
        });
    });

    describe('Integration with Agent Workflow', () => {
        it('should complete full workflow: generate → route → execute TTS', async () => {
            console.log('🧪 Testing complete TTS workflow...');

            // Mock LLM to generate response with TTS tool call
            const mockLLMResponse = {
                content: 'I will speak this response',
                tool_calls: [{
                    name: 'speak_response',
                    args: { text: 'Complete workflow test' },
                    id: 'workflow_test',
                    type: 'tool_call'
                }]
            };

            agentService.model.invoke = vi.fn().mockResolvedValue(mockLLMResponse);

            // Execute complete workflow (non-streaming for simplicity)
            const result = await agentService.generateResponse('Say hello', {
                sessionId: 'workflow-test',
                stream: false
            });

            console.log('🎯 Workflow result:', result);

            // Verify the complete flow worked
            expect(result).toBeDefined();
            expect(typeof result).toBe('string');
            expect(agentService.model.invoke).toHaveBeenCalled();

            // Wait a bit for async tool execution
            await new Promise(resolve => setTimeout(resolve, 100));

            console.log('🔍 Final TTS calls:', mockTTSService.speak.mock.calls);

            console.log('✅ Complete workflow verified!');
        });

        it('should handle errors gracefully and not break the workflow', async () => {
            console.log('🧪 Testing error handling...');

            // Mock TTS service to throw error
            mockTTSService.speak.mockRejectedValueOnce(new Error('TTS unavailable'));

            const mockInput = {
                messages: [{
                    role: 'assistant',
                    content: '',
                    tool_calls: [{
                        name: 'speak_response',
                        args: { text: 'Error test' },
                        id: 'error_test',
                        type: 'tool_call'
                    }]
                }]
            };

            // Should not throw
            const result = await agentService.toolNode.invoke(mockInput);
            expect(result).toBeDefined();

            // Reset TTS mock for other tests
            mockTTSService.speak.mockResolvedValue({ success: true });

            console.log('✅ Error handling verified!');
        });
    });

    describe('Verification Tests', () => {
        it('should have ToolNode properly configured', () => {
            console.log('🧪 Verifying ToolNode configuration...');

            expect(agentService.toolNode).toBeDefined();
            expect(typeof agentService.toolNode.invoke).toBe('function');

            // Verify tools are registered
            const toolNames = agentService.tools.map(t => t.name);
            expect(toolNames).toContain('speak_response');

            console.log('🛠️ Available tools:', toolNames);
            console.log('✅ ToolNode configuration verified!');
        });

        it('should demonstrate the fix: TTS is actually called', async () => {
            console.log('🎯 DEMONSTRATING THE FIX: TTS should be called!');

            const testMessage = 'This message should be spoken aloud';

            const mockInput = {
                messages: [{
                    role: 'assistant',
                    content: 'I will speak for you',
                    tool_calls: [{
                        name: 'speak_response',
                        args: { text: testMessage },
                        id: 'fix_demo',
                        type: 'tool_call'
                    }]
                }]
            };

            // Clear previous calls
            mockTTSService.speak.mockClear();

            // Execute
            await agentService.toolNode.invoke(mockInput);

            // THE FIX: This should now be true (was false before the fix)
            const ttsWasCalled = mockTTSService.speak.mock.calls.length > 0;

            console.log('🔊 TTS was called:', ttsWasCalled);
            console.log('📞 Number of TTS calls:', mockTTSService.speak.mock.calls.length);
            console.log('📝 TTS call details:', mockTTSService.speak.mock.calls);

            expect(ttsWasCalled).toBe(true);
            expect(mockTTSService.speak).toHaveBeenCalledWith(
                testMessage,
                expect.objectContaining({
                    stream: true,
                    handleStreamInternally: true
                })
            );

            console.log('🎉 FIX VERIFIED: TTS is now being called!');
        });
    });
}); 