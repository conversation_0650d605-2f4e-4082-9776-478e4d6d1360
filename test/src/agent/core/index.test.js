/**
 * LangGraphAgentService Core Tests
 * Tests the generic LangGraph agent core with real API integration using apiProxy.ts
 */

// Import test suites
import './initialization.test.js';
import './tools.test.js';
import './multimodal.test.js';

// Import the class to test
import { LangGraphAgentService } from '@/agent/core.js';
import { fetchLLMApi } from '@/utils/apiProxy.ts';
import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';

// Test configuration with real endpoints
const TEST_CONFIG = {
    model: 'Qwen/Qwen2.5-Omni-7B',
    temperature: 0.7,
    maxTokens: 150,
    timeout: 15000, // 15 second timeout for real API calls
    useRealAPI: process.env.TEST_REAL_API === 'true'
};

describe('LangGraphAgentService - Core Integration Tests', () => {
    let agentService;

    beforeEach(async () => {
        agentService = new LangGraphAgentService({
            model: TEST_CONFIG.model,
            temperature: TEST_CONFIG.temperature,
            maxTokens: TEST_CONFIG.maxTokens,
            autoRegisterTools: true,
            useLangGraphStreaming: true,
            // Mock services for testing
            services: {
                ttsService: {
                    speak: vi.fn().mockResolvedValue({ success: true }),
                    constructor: { name: 'MockTTSService' }
                },
                audioPlayer: {
                    playChunk: vi.fn().mockResolvedValue(true)
                },
                animationController: {
                    triggerAnimation: vi.fn().mockResolvedValue({ success: true })
                }
            }
        });

        // Initialize the service
        if (TEST_CONFIG.useRealAPI) {
            try {
                await agentService.initialize();
            } catch (error) {
                console.warn('Could not initialize agent service with real API:', error.message);
            }
        }
    });

    afterEach(async () => {
        if (agentService && typeof agentService.dispose === 'function') {
            agentService.dispose();
        }
    });

    describe('Service Initialization', () => {
        it('should initialize successfully with all components', async () => {
            expect(agentService).toBeDefined();
            expect(agentService.model).toBeDefined();
            expect(agentService.streamProcessor).toBeDefined();
            expect(agentService.tools).toBeDefined();
            expect(agentService.memoryManager).toBeDefined();
        });

        it('should have LangGraph native streaming enabled', () => {
            expect(agentService.streamProcessor).toBeDefined();
            expect(agentService.streamProcessor.isNative).toBe(true);
            expect(typeof agentService.processLangGraphStream).toBe('function');
        });

        it('should auto-register tools with ToolNode', () => {
            expect(Array.isArray(agentService.tools)).toBe(true);
            expect(agentService.toolNode).toBeDefined(); // New: Check ToolNode exists
            expect(agentService.graph).toBeDefined(); // Check LangGraph workflow exists

            // Should have TTS and animation tools registered
            const toolNames = agentService.tools.map(t => t.name);
            expect(toolNames.length).toBeGreaterThan(0);
            expect(toolNames).toContain('speak_response'); // TTS tool
        });

        it('should have service-aware ToolNode', () => {
            expect(agentService.toolNode).toBeDefined();
            expect(typeof agentService.toolNode.invoke).toBe('function');
            // Verify services are accessible
            expect(agentService.services.ttsService).toBeDefined();
            expect(agentService.services.audioPlayer).toBeDefined();
        });
    });

    describe('LangGraph Workflow Tests', () => {
        it('should have properly constructed workflow graph', async () => {
            if (!agentService._initialized) {
                await agentService.initialize();
            }

            expect(agentService.graph).toBeDefined();
            expect(typeof agentService.graph.invoke).toBe('function');
        });

        it('should route to ToolNode when tool calls are present', async () => {
            // Mock a response with tool calls
            const mockState = {
                messages: [{
                    role: 'assistant',
                    content: 'I will speak this text',
                    tool_calls: [{
                        name: 'speak_response',
                        args: { text: 'Hello world' },
                        id: 'call_123'
                    }]
                }]
            };

            const route = agentService._routeAfterGeneration(mockState);
            expect(route).toBe('executeTools');
        });

        it('should route to updateAgent when no tool calls', async () => {
            const mockState = {
                messages: [{
                    role: 'assistant',
                    content: 'Just a regular response',
                    tool_calls: []
                }]
            };

            const route = agentService._routeAfterGeneration(mockState);
            expect(route).toBe('updateAgent');
        });
    });

    describe('Real API Integration Tests', () => {
        it('should connect to LLM API successfully', async () => {
            if (!TEST_CONFIG.useRealAPI) {
                console.warn('Skipping real API test - set TEST_REAL_API=true to enable');
                return;
            }

            try {
                const response = await fetchLLMApi('v1/models');
                expect(response.ok).toBe(true);

                const data = await response.json();
                expect(data).toBeDefined();
                expect(data.data || data.models).toBeDefined();
            } catch (error) {
                console.warn('LLM API not available for testing:', error.message);
            }
        }, TEST_CONFIG.timeout);

        it('should handle streaming responses by default', async () => {
            if (!TEST_CONFIG.useRealAPI || !agentService._initialized) {
                console.warn('Skipping streaming test - API not available or service not initialized');
                return;
            }

            try {
                const input = 'Say hello briefly';
                const options = {
                    sessionId: 'test-streaming',
                    // stream is true by default
                };

                const streamGenerator = await agentService.generateResponse(input, options);
                expect(streamGenerator).toBeDefined();
                expect(typeof streamGenerator[Symbol.asyncIterator]).toBe('function');

                // Collect first few chunks
                const chunks = [];
                for await (const chunk of streamGenerator) {
                    chunks.push(chunk);
                    if (chunks.length >= 3) break; // Limit to avoid long test
                }

                expect(chunks.length).toBeGreaterThan(0);
                expect(chunks[0]).toHaveProperty('type');

            } catch (error) {
                console.warn('Streaming test failed:', error.message);
            }
        }, TEST_CONFIG.timeout);

        it('should handle non-streaming when explicitly requested', async () => {
            if (!TEST_CONFIG.useRealAPI || !agentService._initialized) {
                console.warn('Skipping non-streaming test - API not available or service not initialized');
                return;
            }

            try {
                const input = 'Say hello briefly';
                const options = {
                    sessionId: 'test-non-streaming',
                    stream: false // Explicitly disable streaming
                };

                const result = await agentService.generateResponse(input, options);
                expect(typeof result).toBe('string');
                expect(result.length).toBeGreaterThan(0);

            } catch (error) {
                console.warn('Non-streaming test failed:', error.message);
            }
        }, TEST_CONFIG.timeout);
    });

    describe('Generic Input Processing', () => {
        it('should handle text input correctly', async () => {
            const input = 'Hello, how are you?';

            // Test direct text input processing
            expect(typeof input).toBe('string');
            expect(input.length).toBeGreaterThan(0);
        });

        it('should handle multimodal input objects', async () => {
            const input = {
                text: 'Describe this content',
                video: ['frame1', 'frame2'],
                metadata: { source: 'test' }
            };

            // Test that the agent can accept multimodal input
            expect(input.text).toBe('Describe this content');
            expect(input.video).toEqual(['frame1', 'frame2']);
            expect(input.metadata).toEqual({ source: 'test' });
        });
    });

    describe('Memory Integration', () => {
        it('should have memory manager initialized', () => {
            expect(agentService.memoryManager).toBeDefined();
        });

        it('should handle conversation history', async () => {
            const sessionId = 'test-memory-session';

            // Mock memory context for testing
            const mockMemoryContext = {
                conversation_history: [
                    { role: 'user', content: 'Previous message' },
                    { role: 'assistant', content: 'Previous response' }
                ],
                memory_stats: { total_messages: 2 },
                memory_type: 'langchain'
            };

            // Test memory context exists
            expect(mockMemoryContext.conversation_history).toBeDefined();
            expect(mockMemoryContext.conversation_history.length).toBe(2);
        });
    });

    describe('Tool Integration', () => {
        it('should have tools registered', () => {
            expect(agentService.tools).toBeDefined();
            expect(Array.isArray(agentService.tools)).toBe(true);

            if (agentService.tools.length > 0) {
                const firstTool = agentService.tools[0];
                expect(firstTool).toHaveProperty('name');
                expect(firstTool).toHaveProperty('description');
                expect(typeof firstTool.invoke).toBe('function');
            }
        });

        it('should handle tool execution in streaming mode', async () => {
            if (!TEST_CONFIG.useRealAPI || !agentService._initialized) {
                console.warn('Skipping tool execution test - API not available');
                return;
            }

            try {
                const input = 'Show me a happy animation';
                const options = {
                    sessionId: 'test-tools',
                    configurable: { useTools: true }
                };

                const streamGenerator = await agentService.generateResponse(input, options);
                const events = [];

                for await (const chunk of streamGenerator) {
                    events.push(chunk);

                    // Look for tool-related events
                    if (chunk.type === 'tool_start' || chunk.type === 'tool_end') {
                        expect(chunk.data).toBeDefined();
                        expect(chunk.data.name).toBeDefined();
                    }

                    if (events.length >= 10) break; // Limit to avoid long test
                }

                // Should have received some events
                expect(events.length).toBeGreaterThan(0);

            } catch (error) {
                console.warn('Tool execution test failed:', error.message);
            }
        }, TEST_CONFIG.timeout);
    });

    describe('Generic State Management', () => {
        it('should update agent mode correctly', async () => {
            const sessionId = 'test-state';

            // Test generic mode changes
            await agentService.setMode('processing', sessionId);
            await agentService.setMode('idle', sessionId);

            // Should not throw errors
            expect(true).toBe(true);
        });

        it('should handle interruption scenarios', async () => {
            const sessionId = 'test-interruption';

            await agentService.handleInterruption(sessionId);

            // Should not throw errors
            expect(true).toBe(true);
        });

        it('should get current state', async () => {
            const sessionId = 'test-get-state';

            const state = await agentService.getState(sessionId);

            expect(state).toBeDefined();
            expect(state).toHaveProperty('mode');
            expect(state).toHaveProperty('sessionId');
            expect(state.sessionId).toBe(sessionId);
        });
    });

    describe('Error Handling', () => {
        it('should handle invalid input gracefully', async () => {
            try {
                const result = await agentService.generateResponse(null);
                // Should handle null input without crashing
                expect(result).toBeDefined();
            } catch (error) {
                // Error is acceptable for invalid input
                expect(error).toBeDefined();
            }
        });

        it('should handle network errors gracefully', async () => {
            // Create agent with invalid endpoint
            const invalidAgent = new LangGraphAgentService({
                vllmEndpoint: 'http://invalid-endpoint:9999',
                model: TEST_CONFIG.model
            });

            try {
                await invalidAgent.initialize();
                // If this succeeds, connection test might be mocked
            } catch (error) {
                // Network error is expected
                expect(error).toBeDefined();
            }

            invalidAgent.dispose();
        });
    });

    describe('Performance and Concurrency', () => {
        it('should handle concurrent requests', async () => {
            if (!TEST_CONFIG.useRealAPI || !agentService._initialized) {
                console.warn('Skipping concurrency test - API not available');
                return;
            }

            const requests = Array.from({ length: 3 }, (_, i) =>
                agentService.generateResponse(`Request ${i}`, {
                    sessionId: `concurrent-${i}`,
                    stream: false // Use non-streaming for simpler testing
                })
            );

            try {
                const results = await Promise.all(requests);
                expect(results).toHaveLength(3);
                results.forEach(result => {
                    expect(typeof result).toBe('string');
                    expect(result.length).toBeGreaterThan(0);
                });
            } catch (error) {
                console.warn('Concurrency test failed:', error.message);
            }
        }, TEST_CONFIG.timeout * 2);
    });

    describe('Configuration Management', () => {
        it('should use correct model configuration', () => {
            expect(agentService.model).toBeDefined();
            expect(agentService.temperature).toBe(TEST_CONFIG.temperature);
            expect(agentService.maxTokens).toBe(TEST_CONFIG.maxTokens);
        });

        it('should update configuration dynamically', () => {
            const newConfig = {
                temperature: 0.5,
                maxTokens: 200
            };

            agentService.updateConfig(newConfig);

            // Test that config was updated
            expect(agentService.options.temperature).toBe(0.5);
            expect(agentService.options.maxTokens).toBe(200);
        });
    });
});
