/**
 * Voice Reply Generation Tests
 * Tests for audio output generation from LLM API
 * 
 * This test file focuses on verifying that the /api/llm endpoint
 * correctly handles requests for audio responses and returns valid audio data.
 */

import { describe, it, expect, beforeAll, afterAll, beforeEach } from 'vitest';
import fetch from 'node-fetch';

// Test configuration
const TEST_CONFIG = {
    apiBaseUrl: process.env.API_BASE_URL || 'http://localhost:2994',
    timeout: 30000, // 30 seconds for audio generation
    useRealAPI: process.env.TEST_REAL_API === 'true'
};

// Sample audio data for testing (Base64 encoded WAV header)
const SAMPLE_AUDIO_BASE64 = 'UklGRiT8AABXQVZFZm10IBAAAAABAAEAwF0AAIC7AAACABAAZGF0YQD8AADe/9//4f/j/+D/3v/d/+P/4v/a/8n/w//G/9v/6f/x';

describe('Voice Reply Generation Tests', () => {
    beforeAll(async () => {
        if (!TEST_CONFIG.useRealAPI) {
            console.warn('⚠️ Voice reply tests require TEST_REAL_API=true to run against live API');
            return;
        }

        // Wait for server to be ready
        await new Promise(resolve => setTimeout(resolve, 1000));
    });

    describe('Input Validation Tests', () => {
        it('should reject empty messages array with helpful error', async () => {
            const response = await fetch(`${TEST_CONFIG.apiBaseUrl}/api/llm`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    provider: 'aliyun',
                    model: 'qwen-omni-turbo',
                    modalities: ['text', 'audio'],
                    audioConfig: { voice: 'Chelsie', format: 'wav' },
                    messages: [] // Empty array should be rejected
                })
            });

            expect(response.status).toBe(400);
            const errorData = await response.json();

            expect(errorData.error).toBe('Empty messages array');
            expect(errorData.details).toContain('Aliyun API requires at least one user message');
        });

        it('should reject missing messages parameter', async () => {
            const response = await fetch(`${TEST_CONFIG.apiBaseUrl}/api/llm`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    provider: 'aliyun',
                    model: 'qwen-omni-turbo',
                    modalities: ['text', 'audio'],
                    audioConfig: { voice: 'Chelsie', format: 'wav' }
                    // messages field intentionally omitted
                })
            });

            expect(response.status).toBe(400);
            const errorData = await response.json();

            expect(errorData.error).toBe('Missing required parameter: messages');
            expect(errorData.details).toContain('messages array with at least one message');
        });

        it('should reject invalid messages (not array)', async () => {
            const response = await fetch(`${TEST_CONFIG.apiBaseUrl}/api/llm`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    provider: 'aliyun',
                    model: 'qwen-omni-turbo',
                    modalities: ['text', 'audio'],
                    audioConfig: { voice: 'Chelsie', format: 'wav' },
                    messages: "invalid string instead of array"
                })
            });

            expect(response.status).toBe(400);
            const errorData = await response.json();

            expect(errorData.error).toBe('Invalid messages parameter: must be an array');
        });

        it('should reject messages with all empty content', async () => {
            const response = await fetch(`${TEST_CONFIG.apiBaseUrl}/api/llm`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    provider: 'aliyun',
                    model: 'qwen-omni-turbo',
                    modalities: ['text', 'audio'],
                    audioConfig: { voice: 'Chelsie', format: 'wav' },
                    messages: [
                        { role: 'user', content: '' },
                        { role: 'user', content: '   ' }, // whitespace only
                        { role: 'user' } // no content at all
                    ]
                })
            });

            expect(response.status).toBe(400);
            const errorData = await response.json();

            expect(errorData.error).toBe('No valid messages found');
            expect(errorData.details).toContain('All messages must have non-empty content');
        });

        it('should accept valid messages and transform properly', async () => {
            const response = await fetch(`${TEST_CONFIG.apiBaseUrl}/api/llm`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    provider: 'aliyun',
                    model: 'qwen-omni-turbo',
                    modalities: ['text', 'audio'],
                    audioConfig: { voice: 'Chelsie', format: 'wav' },
                    messages: [
                        { role: 'user', content: 'Hello, please respond with audio' }
                    ]
                })
            });

            expect(response.ok).toBe(true);
            const responseData = await response.json();

            expect(responseData.content).toBeDefined();
            expect(responseData.metadata.originalMessageCount).toBe(1);
            expect(responseData.metadata.messageCount).toBe(1);
        }, TEST_CONFIG.timeout);
    });

    describe('Text-to-Audio Generation', () => {
        it('should generate audio response for text input', async () => {
            const response = await fetch(`${TEST_CONFIG.apiBaseUrl}/api/llm`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    provider: 'aliyun',
                    model: 'qwen-omni-turbo',
                    modalities: ['text', 'audio'],
                    audioConfig: { voice: 'Chelsie', format: 'wav' },
                    messages: [{ role: 'user', content: 'Say hello' }]
                })
            });

            expect(response.ok).toBe(true);
            const responseData = await response.json();

            // Verify response structure matches actual API format
            expect(responseData).toHaveProperty('content');
            expect(responseData).toHaveProperty('audio');
            expect(responseData).toHaveProperty('metadata');

            // Verify text content exists
            expect(typeof responseData.content).toBe('string');
            expect(responseData.content.length).toBeGreaterThan(0);

            // Verify audio content exists and is base64
            expect(responseData.audio).toBeTruthy();
            expect(typeof responseData.audio).toBe('string');
            expect(responseData.audio.length).toBeGreaterThan(1000); // Should be substantial audio data

            // Verify metadata
            expect(responseData.metadata.provider).toBe('aliyun');
            expect(responseData.metadata.modalities).toContain('audio');

            const hasAudio = !!(responseData.audio && responseData.audio.length > 1000);

            expect(hasAudio).toBe(true);
        }, TEST_CONFIG.timeout);

        it('should handle audio input gracefully (HTTP mode limitation)', async () => {
            // Note: Aliyun HTTP API doesn't support audio input, only audio output
            // This test verifies graceful handling of input_audio
            const response = await fetch(`${TEST_CONFIG.apiBaseUrl}/api/llm`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    provider: 'aliyun',
                    model: 'qwen-omni-turbo',
                    modalities: ['text', 'audio'],
                    audioConfig: { voice: 'Chelsie', format: 'wav' },
                    messages: [{
                        role: 'user',
                        content: 'Please respond to this message',
                        input_audio: SAMPLE_AUDIO_BASE64
                    }]
                })
            });

            expect(response.ok).toBe(true);
            const responseData = await response.json();

            // Should still generate text and audio response
            expect(responseData).toHaveProperty('content');
            expect(responseData).toHaveProperty('audio');

            // AI should naturally mention HTTP mode limitation or audio processing issue
            const content = responseData.content.toLowerCase();
            const mentionsAudioIssue = content.includes('audio') &&
                (content.includes('http') || content.includes('process') || content.includes('issue'));

            expect(mentionsAudioIssue).toBe(true);

            // Should still generate audio output
            expect(responseData.audio).toBeTruthy();
            expect(responseData.audio.length).toBeGreaterThan(1000);
        }, TEST_CONFIG.timeout);
    });

    describe('Audio Input Handling (HTTP Mode)', () => {
        it('should properly format audio input with input_audio field for Aliyun HTTP mode', async () => {
            if (!TEST_CONFIG.useRealAPI) {
                console.warn('Skipping real API test - set TEST_REAL_API=true to enable');
                return;
            }

            const response = await fetch(`${TEST_CONFIG.apiBaseUrl}/api/llm`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    provider: 'aliyun',
                    model: 'qwen-omni-turbo',
                    modalities: ['text', 'audio'],
                    audioConfig: { voice: 'Ethan', format: 'wav' },
                    messages: [{
                        role: 'user',
                        content: 'Please respond to this audio input',
                        input_audio: {
                            data: SAMPLE_AUDIO_BASE64,
                            format: 'wav'
                        }
                    }]
                })
            });

            expect(response.ok).toBe(true);
            const responseData = await response.json();

            // Should generate both text and audio response
            expect(responseData.content).toBeTruthy();
            expect(responseData.audio).toBeTruthy();
            expect(responseData.audio.length).toBeGreaterThan(1000);

            console.log('✅ Audio input with input_audio field processed successfully');
        }, TEST_CONFIG.timeout);
    });

    describe('Voice Configuration Tests', () => {
        it('should respect different voice selections', async () => {
            const voices = ['Ethan', 'Chelsie'];

            for (const voice of voices) {
                const response = await fetch(`${TEST_CONFIG.apiBaseUrl}/api/llm`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        provider: 'aliyun',
                        model: 'qwen-omni-turbo',
                        modalities: ['text', 'audio'],
                        audioConfig: { voice, format: 'wav' },
                        messages: [{ role: 'user', content: `Say hello in ${voice} voice` }]
                    })
                });

                expect(response.ok).toBe(true);
                const responseData = await response.json();

                // Verify audio generation for each voice
                expect(responseData.audio).toBeTruthy();
                expect(responseData.audio.length).toBeGreaterThan(1000);
                expect(responseData.metadata.audioConfig.voice).toBe(voice);
            }
        }, TEST_CONFIG.timeout * 2); // Double timeout for multiple requests
    });

    describe('Error Handling', () => {
        it('should handle invalid audio format gracefully', async () => {
            if (!TEST_CONFIG.useRealAPI) {
                console.warn('Skipping real API test - set TEST_REAL_API=true to enable');
                return;
            }

            const requestBody = {
                provider: 'aliyun',
                model: 'qwen-omni-turbo',
                messages: [
                    {
                        role: 'user',
                        content: 'Test message'
                    }
                ],
                modalities: ['text', 'audio'],
                audioConfig: {
                    voice: 'InvalidVoice',
                    format: 'wav'
                }
            };

            const response = await fetch(`${TEST_CONFIG.apiBaseUrl}/api/llm`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestBody),
                timeout: TEST_CONFIG.timeout
            });

            // Should either work with fallback voice or return a clear error
            if (!response.ok) {
                const errorData = await response.json();
                expect(errorData.error).toBeDefined();
            } else {
                // If it succeeds, it should have audio (using correct response format)
                const responseData = await response.json();
                const hasAudio = responseData.audio && responseData.audio.length > 1000;
                expect(hasAudio).toBe(true);
            }
        }, TEST_CONFIG.timeout);
    });

    describe('Input Audio Logging Prevention', () => {
        it('should not log input_audio in server logs and handle gracefully', async () => {
            // This test verifies that input_audio is redacted from logs
            // even when the API doesn't support audio input
            const response = await fetch(`${TEST_CONFIG.apiBaseUrl}/api/llm`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    provider: 'aliyun',
                    model: 'qwen-omni-turbo',
                    modalities: ['text', 'audio'],
                    audioConfig: { voice: 'Chelsie', format: 'wav' },
                    messages: [{
                        role: 'user',
                        content: 'Test message with audio',
                        input_audio: SAMPLE_AUDIO_BASE64
                    }]
                })
            });

            expect(response.ok).toBe(true);
            const responseData = await response.json();

            // AI should naturally mention audio processing limitation
            const content = responseData.content.toLowerCase();
            const mentionsAudioIssue = content.includes('audio') &&
                (content.includes('http') || content.includes('process') || content.includes('can\'t') || content.includes('sorry'));

            expect(mentionsAudioIssue).toBe(true);

            // Log a note about what should be verified manually
            console.log('🔍 MANUAL VERIFICATION NEEDED:');
            console.log('   Check server logs to ensure input_audio is logged as "[REDACTED]"');
            console.log('   Original base64 audio data should NOT appear in logs');
            console.log('   Should see warning about audio input not supported in HTTP mode');

            // The test passes if the API call succeeds - the logging prevention
            // is verified by the sanitization code in the route
        }, TEST_CONFIG.timeout);
    });
}); 