/**
 * Talking Animation and Performance Looping Tests
 * 
 * Tests for:
 * 1. Automatic talking animation triggering during TTS
 * 2. Performance animation looping until interrupted
 * 3. LLMStreamProcessor talking animation callbacks
 */

import { describe, test, expect, beforeAll, vi } from 'vitest';

describe('Talking Animation and Performance Looping', () => {
    let mockLogger;

    beforeAll(async () => {
        // Mock logger to avoid import issues
        mockLogger = {
            info: vi.fn(),
            warn: vi.fn(),
            error: vi.fn(),
            debug: vi.fn()
        };

        global.console = {
            ...console,
            log: vi.fn(),
            warn: vi.fn(),
            error: vi.fn()
        };
    });

    describe('LLMStreamProcessor Talking Animation', () => {
        test('should have talking animation methods', () => {
            // Mock the LLMStreamProcessor methods
            const mockProcessor = {
                currentTalkingAnimation: null,
                _callbacks: {
                    onAnimationTrigger: vi.fn()
                },
                _triggerRandomTalkingAnimation: vi.fn(),
                _stopTalkingAnimation: vi.fn(),
                _handlePlaybackStart: vi.fn(),
                _handlePlaybackEnd: vi.fn()
            };

            expect(typeof mockProcessor._triggerRandomTalkingAnimation).toBe('function');
            expect(typeof mockProcessor._stopTalkingAnimation).toBe('function');
            expect(typeof mockProcessor._handlePlaybackStart).toBe('function');
            expect(typeof mockProcessor._handlePlaybackEnd).toBe('function');
        });

        test('should trigger talking animation callback', () => {
            const mockOnAnimationTrigger = vi.fn();
            const mockProcessor = {
                currentTalkingAnimation: null,
                _callbacks: {
                    onAnimationTrigger: mockOnAnimationTrigger
                }
            };

            // Simulate talking animation trigger
            const talkingAnimationMapping = {
                category: 'communication',
                file: 'Talking.fbx',
                priority: 70,
                loopable: true,
                context: 'Auto-triggered talking animation during TTS playback',
                isTalkingAnimation: true
            };

            mockProcessor._callbacks.onAnimationTrigger(talkingAnimationMapping);

            expect(mockOnAnimationTrigger).toHaveBeenCalledWith(talkingAnimationMapping);
        });

        test('should track current talking animation', () => {
            const mockProcessor = {
                currentTalkingAnimation: null
            };

            // Set a talking animation
            const talkingAnimation = {
                category: 'communication',
                file: 'Talking.fbx',
                priority: 70
            };

            mockProcessor.currentTalkingAnimation = talkingAnimation;
            expect(mockProcessor.currentTalkingAnimation).toEqual(talkingAnimation);

            // Clear talking animation
            mockProcessor.currentTalkingAnimation = null;
            expect(mockProcessor.currentTalkingAnimation).toBeNull();
        });
    });

    describe('SkeletalAnimator Performance Looping', () => {
        test('should handle performance animation looping', () => {
            // Mock Three.js constants
            const THREE = {
                LoopRepeat: 2201,
                LoopOnce: 2200
            };

            const mockAction = {
                setLoop: vi.fn(),
                clampWhenFinished: false,
                play: vi.fn(),
                fadeIn: vi.fn()
            };

            // Test performance animation setup
            const category = 'performance';
            mockAction.setLoop(THREE.LoopRepeat);
            mockAction.clampWhenFinished = false;

            expect(mockAction.setLoop).toHaveBeenCalledWith(THREE.LoopRepeat);
            expect(mockAction.clampWhenFinished).toBe(false);
        });

        test('should handle non-performance animation normally', () => {
            const THREE = {
                LoopOnce: 2200
            };

            const mockAction = {
                setLoop: vi.fn(),
                clampWhenFinished: true,
                play: vi.fn(),
                fadeIn: vi.fn()
            };

            // Test non-performance animation setup
            const category = 'movement';
            mockAction.setLoop(THREE.LoopOnce);
            mockAction.clampWhenFinished = true;

            expect(mockAction.setLoop).toHaveBeenCalledWith(THREE.LoopOnce);
            expect(mockAction.clampWhenFinished).toBe(true);
        });
    });

    describe('TalkingAvatar Integration', () => {
        test('should differentiate talking vs regular animations', () => {
            const mockAnimator = {
                playAnimation: vi.fn().mockResolvedValue(true),
                triggerAnimation: vi.fn().mockResolvedValue(true)
            };

            // Test talking animation handling
            const talkingMapping = {
                category: 'communication',
                file: 'Talking.fbx',
                priority: 70,
                isTalkingAnimation: true
            };

            // Simulate talking animation trigger
            if (talkingMapping.isTalkingAnimation) {
                mockAnimator.playAnimation('communication', talkingMapping.file, 0);
            }

            expect(mockAnimator.playAnimation).toHaveBeenCalledWith(
                'communication',
                'Talking.fbx',
                0
            );

            // Test regular animation handling
            const regularMapping = {
                category: 'performance',
                file: 'Wave Hip Hop Dance.fbx',
                priority: 90,
                isTalkingAnimation: false
            };

            if (!regularMapping.isTalkingAnimation) {
                mockAnimator.triggerAnimation(regularMapping, {});
            }

            expect(mockAnimator.triggerAnimation).toHaveBeenCalledWith(
                regularMapping,
                {}
            );
        });
    });

    describe('Animation Categories Prompt Update', () => {
        test('should exclude communication category from prompts', () => {
            // Mock animation categories result
            const mockCategoriesResult = {
                baseAnimations: 'auto-triggered during speech',
                categoryDescriptions: '- performance: dance animations\n- movement: action animations\nNOTE: Talking animations are automatically triggered during TTS speech.'
            };

            // Verify communication category is not included
            expect(mockCategoriesResult.categoryDescriptions).not.toContain('communication:');

            // Verify note about auto-triggering is present
            expect(mockCategoriesResult.categoryDescriptions).toContain('automatically triggered during TTS speech');

            // Verify base animations indicates auto-triggering
            expect(mockCategoriesResult.baseAnimations).toBe('auto-triggered during speech');
        });
    });

    describe('Error Handling', () => {
        test('should handle missing animation registry gracefully', () => {
            const mockProcessor = {
                _callbacks: {
                    onAnimationTrigger: vi.fn()
                },
                currentTalkingAnimation: null
            };

            // Should not throw when no animation is found
            expect(() => {
                // Simulate no animation found scenario
                if (!mockProcessor.currentTalkingAnimation) {
                    mockLogger.warn('No talking animations found');
                }
            }).not.toThrow();
        });

        test('should handle missing callback gracefully', () => {
            const mockProcessor = {
                _callbacks: {},
                currentTalkingAnimation: null
            };

            // Should not throw when no callback is available
            expect(() => {
                if (!mockProcessor._callbacks.onAnimationTrigger) {
                    mockLogger.warn('No onAnimationTrigger callback available');
                }
            }).not.toThrow();
        });
    });
}); 