/**
 * Animation Looping Fix Validation Test
 * 
 * Tests to verify the fixes for:
 * 1. Performance animation looping
 * 2. TTS animation callback handling
 */

import { describe, test, expect, beforeAll, vi } from 'vitest';

describe('Animation Looping Fix Validation', () => {
    beforeAll(async () => {
        // Mock console and logger
        global.console = {
            ...console,
            log: vi.fn(),
            warn: vi.fn(),
            error: vi.fn(),
            debug: vi.fn()
        };
    });

    describe('Performance Animation Looping', () => {
        test('should set LoopRepeat for performance animations', () => {
            // Mock THREE.js action
            const mockAction = {
                setLoop: vi.fn(),
                clampWhenFinished: false,
                play: vi.fn(),
                reset: vi.fn()
            };

            // Mock ANIMATION_REGISTRY
            const mockRegistry = {
                getByFilename: vi.fn((file) => {
                    if (file === 'Wave Hip Hop Dance.fbx') {
                        return { loopable: true };
                    }
                    return null;
                })
            };

            // Simulate the fixed logic
            const category = 'performance';
            const file = 'Wave Hip Hop Dance.fbx';
            const animationData = mockRegistry.getByFilename(file);
            const shouldLoop = (category === 'performance') || (animationData && animationData.loopable);

            expect(shouldLoop).toBe(true);
            expect(mockRegistry.getByFilename).toHaveBeenCalledWith(file);
        });

        test('should properly track currentAnimationShouldLoop', () => {
            // Mock data for test
            const category = 'performance';
            const file = 'Rumba Dancing.fbx';
            const animationData = { loopable: true };

            // Simulate the fixed looping tracking logic
            const currentAnimationShouldLoop = (category === 'performance') || (animationData && animationData.loopable);

            expect(currentAnimationShouldLoop).toBe(true);
        });

        test('should restart looping animations on finish', () => {
            // Mock current action
            const mockAction = {
                reset: vi.fn(),
                play: vi.fn()
            };

            // Mock animator state
            const mockAnimator = {
                currentAnimationShouldLoop: true,
                currentAction: mockAction,
                currentAnimationFile: 'Wave Hip Hop Dance.fbx'
            };

            // Simulate the fixed mixer event listener logic
            if (mockAnimator.currentAnimationShouldLoop && mockAnimator.currentAction) {
                mockAnimator.currentAction.reset();
                mockAnimator.currentAction.play();
            }

            expect(mockAction.reset).toHaveBeenCalled();
            expect(mockAction.play).toHaveBeenCalled();
        });
    });

    describe('TTS Animation Callback Fixes', () => {
        test('should handle callback validation properly', () => {
            // Mock valid callback scenario
            const mockValidCallback = vi.fn();
            const mockCallbacks = {
                onAnimationTrigger: mockValidCallback
            };

            // Simulate the fixed callback validation
            const animationCallback = mockCallbacks?.onAnimationTrigger;
            const isValidCallback = animationCallback && typeof animationCallback === 'function';

            expect(isValidCallback).toBe(true);
        });

        test('should handle missing callback gracefully', () => {
            // Mock missing callback scenario
            const mockCallbacks = {};

            // Simulate the fixed callback validation
            const animationCallback = mockCallbacks?.onAnimationTrigger;
            const isValidCallback = animationCallback && typeof animationCallback === 'function';

            expect(isValidCallback).toBe(false);
        });

        test('should handle error during callback execution', () => {
            // Mock error-throwing callback
            const errorThrowingCallback = vi.fn().mockImplementation(() => {
                throw new Error('Callback error');
            });

            const mockCallbacks = {
                onAnimationTrigger: errorThrowingCallback
            };

            // Simulate the fixed error handling
            const animationCallback = mockCallbacks?.onAnimationTrigger;

            if (animationCallback && typeof animationCallback === 'function') {
                try {
                    animationCallback({
                        category: 'communication',
                        file: 'Talking.fbx',
                        isTalkingAnimation: true
                    });
                } catch (error) {
                    // Error should be caught and handled gracefully
                    expect(error.message).toBe('Callback error');
                }
            }

            expect(errorThrowingCallback).toHaveBeenCalled();
        });
    });

    describe('Animation Configuration Consistency', () => {
        test('should maintain consistent looping logic', () => {
            // Test data
            const testCases = [
                { category: 'performance', file: 'Dance.fbx', loopable: true, expectedLoop: true },
                { category: 'performance', file: 'Boxing.fbx', loopable: false, expectedLoop: true }, // Performance category overrides
                { category: 'emotional', file: 'Happy.fbx', loopable: true, expectedLoop: true },
                { category: 'emotional', file: 'Sad.fbx', loopable: false, expectedLoop: false },
                { category: 'communication', file: 'Talking.fbx', loopable: true, expectedLoop: true }
            ];

            testCases.forEach(({ category, file, loopable, expectedLoop }) => {
                // Mock animation data
                const animationData = loopable ? { loopable: true } : null;

                // Apply the fixed logic
                const shouldLoop = (category === 'performance') || (animationData && animationData.loopable);

                expect(shouldLoop).toBe(expectedLoop);
            });
        });
    });
}); 