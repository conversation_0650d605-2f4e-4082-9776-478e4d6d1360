/**
 * Looping Verification Test
 * 
 * This test verifies that the `loopable` setup in AnimationConfig.js
 * actually works correctly with SkeletalAnimator.js
 */

import { describe, test, expect, beforeAll, beforeEach, afterEach, vi } from 'vitest';

describe('Animation Looping Verification', () => {
    let mockAnimationRegistry;
    let mockMixer;
    let mockAction;
    let mockLogger;
    let SkeletalAnimator;

    beforeAll(async () => {
        // Mock THREE.js
        global.THREE = {
            LoopOnce: 2200,
            LoopRepeat: 2201,
            AnimationMixer: vi.fn(),
            Clock: vi.fn().mockReturnValue({
                getDelta: vi.fn().mockReturnValue(0.016),
                getElapsedTime: vi.fn().mockReturnValue(1.0)
            })
        };

        // Mock logger
        mockLogger = {
            info: vi.fn(),
            debug: vi.fn(),
            warn: vi.fn(),
            error: vi.fn()
        };

        // Mock performance for timing
        global.performance = {
            now: vi.fn().mockReturnValue(1000)
        };

        // Mock console
        global.console = {
            ...console,
            log: vi.fn(),
            warn: vi.fn(),
            error: vi.fn()
        };
    });

    beforeEach(() => {
        // Reset all mocks
        vi.clearAllMocks();

        // Create mock action
        mockAction = {
            setLoop: vi.fn(),
            clampWhenFinished: true,
            play: vi.fn(),
            fadeIn: vi.fn(),
            fadeOut: vi.fn(),
            stop: vi.fn(),
            reset: vi.fn(),
            enabled: true,
            getEffectiveWeight: vi.fn().mockReturnValue(1.0),
            time: 0,
            isRunning: vi.fn().mockReturnValue(true)
        };

        // Create mock mixer
        mockMixer = {
            clipAction: vi.fn().mockReturnValue(mockAction),
            update: vi.fn(),
            addEventListener: vi.fn(),
            _actions: []
        };

        // Create mock animation registry
        mockAnimationRegistry = {
            getByFilename: vi.fn((filename) => {
                // Dance animations should be loopable
                if (filename.includes('Dance') || filename.includes('dancing')) {
                    return { loopable: true };
                }
                // Action animations should not be loopable
                if (filename.includes('Kick') || filename.includes('Jump')) {
                    return { loopable: false };
                }
                // Default case
                return null;
            }),
            getTransitionSettings: vi.fn().mockReturnValue({
                fadeIn: 500,
                fadeOut: 500,
                returnDelay: 1000
            })
        };

        // Mock global ANIMATION_REGISTRY
        global.ANIMATION_REGISTRY = mockAnimationRegistry;
    });

    afterEach(() => {
        vi.resetAllMocks();
    });

    describe('Looping Logic', () => {
        test('should set LoopRepeat for loopable animations', () => {
            // Test data - dance animation marked as loopable
            const file = 'Wave Hip Hop Dance.fbx';
            const animationData = mockAnimationRegistry.getByFilename(file);

            // Verify animation is marked as loopable
            expect(animationData).toEqual({ loopable: true });

            // Test the actual logic from SkeletalAnimator
            const shouldLoop = animationData && animationData.loopable;

            if (shouldLoop) {
                mockAction.setLoop(THREE.LoopRepeat);
                mockAction.clampWhenFinished = false;
            } else {
                mockAction.setLoop(THREE.LoopOnce);
                mockAction.clampWhenFinished = true;
            }

            expect(shouldLoop).toBe(true);
            expect(mockAction.setLoop).toHaveBeenCalledWith(THREE.LoopRepeat);
            expect(mockAction.clampWhenFinished).toBe(false);
        });

        test('should set LoopOnce for non-loopable animations', () => {
            // Test data - kick animation marked as not loopable
            const file = 'Hurricane Kick.fbx';
            const animationData = mockAnimationRegistry.getByFilename(file);

            // Verify animation is marked as not loopable
            expect(animationData).toEqual({ loopable: false });

            // Test the actual logic from SkeletalAnimator
            const shouldLoop = animationData && animationData.loopable;

            if (shouldLoop) {
                mockAction.setLoop(THREE.LoopRepeat);
                mockAction.clampWhenFinished = false;
            } else {
                mockAction.setLoop(THREE.LoopOnce);
                mockAction.clampWhenFinished = true;
            }

            expect(shouldLoop).toBe(false);
            expect(mockAction.setLoop).toHaveBeenCalledWith(THREE.LoopOnce);
            expect(mockAction.clampWhenFinished).toBe(true);
        });

        test('should handle unknown animations gracefully', () => {
            // Test data - unknown animation
            const file = 'Unknown Animation.fbx';
            const animationData = mockAnimationRegistry.getByFilename(file);

            // Verify unknown animation returns null
            expect(animationData).toBe(null);

            // Test the actual logic from SkeletalAnimator
            const shouldLoop = animationData && animationData.loopable;

            if (shouldLoop) {
                mockAction.setLoop(THREE.LoopRepeat);
                mockAction.clampWhenFinished = false;
            } else {
                mockAction.setLoop(THREE.LoopOnce);
                mockAction.clampWhenFinished = true;
            }

            expect(shouldLoop).toBe(false);
            expect(mockAction.setLoop).toHaveBeenCalledWith(THREE.LoopOnce);
            expect(mockAction.clampWhenFinished).toBe(true);
        });
    });

    describe('Loop State Tracking', () => {
        test('should correctly track currentAnimationShouldLoop', () => {
            // Simulate the tracking logic from SkeletalAnimator.js line 1327
            const file = 'Rumba Dancing.fbx';
            const animationData = mockAnimationRegistry.getByFilename(file);
            const currentAnimationShouldLoop = animationData && animationData.loopable;

            expect(currentAnimationShouldLoop).toBe(true);
        });

        test('should track non-looping animations correctly', () => {
            const file = 'Joyful Jump.fbx';
            const animationData = mockAnimationRegistry.getByFilename(file);
            const currentAnimationShouldLoop = animationData && animationData.loopable;

            expect(currentAnimationShouldLoop).toBe(false);
        });
    });

    describe('Event-Driven Loop Restart', () => {
        test('should restart looping animations on finish event', () => {
            // Mock animator state
            const mockAnimator = {
                currentAnimationShouldLoop: true,
                currentAction: mockAction,
                currentAnimationFile: 'Samba Dancing.fbx'
            };

            // Simulate the 'finished' event handler logic from line 1566
            if (mockAnimator.currentAnimationShouldLoop && mockAnimator.currentAction) {
                mockAnimator.currentAction.reset();
                mockAnimator.currentAction.play();
            }

            expect(mockAction.reset).toHaveBeenCalled();
            expect(mockAction.play).toHaveBeenCalled();
        });

        test('should not restart non-looping animations', () => {
            // Mock animator state for non-looping animation
            const mockAnimator = {
                currentAnimationShouldLoop: false,
                currentAction: mockAction,
                currentAnimationFile: 'Falling.fbx'
            };

            // Simulate the 'finished' event handler logic
            if (mockAnimator.currentAnimationShouldLoop && mockAnimator.currentAction) {
                mockAnimator.currentAction.reset();
                mockAnimator.currentAction.play();
            }

            expect(mockAction.reset).not.toHaveBeenCalled();
            expect(mockAction.play).not.toHaveBeenCalled();
        });
    });

    describe('Integration Test', () => {
        test('should handle complete looping workflow', () => {
            // Test the complete workflow for a looping animation
            const category = 'performance';
            const file = 'Northern Soul Spin Combo.fbx';
            const duration = 0; // No specific duration = use default behavior

            // Step 1: Get animation data
            const animationData = mockAnimationRegistry.getByFilename(file);
            expect(animationData).toEqual({ loopable: true });

            // Step 2: Check looping logic
            const shouldLoop = duration === 0 && animationData && animationData.loopable;
            expect(shouldLoop).toBe(true);

            // Step 3: Configure action
            if (shouldLoop) {
                mockAction.setLoop(THREE.LoopRepeat);
                mockAction.clampWhenFinished = false;
            }

            // Step 4: Track loop state
            const currentAnimationShouldLoop = animationData && animationData.loopable;
            expect(currentAnimationShouldLoop).toBe(true);

            // Step 5: Verify configuration
            expect(mockAction.setLoop).toHaveBeenCalledWith(THREE.LoopRepeat);
            expect(mockAction.clampWhenFinished).toBe(false);

            // Step 6: Simulate finished event and restart
            if (currentAnimationShouldLoop && mockAction) {
                mockAction.reset();
                mockAction.play();
            }

            expect(mockAction.reset).toHaveBeenCalled();
            expect(mockAction.play).toHaveBeenCalled();
        });

        test('should handle complete non-looping workflow', () => {
            // Test the complete workflow for a non-looping animation
            const category = 'action';
            const file = 'Martelo 2.fbx';
            const duration = 0;

            // Step 1: Get animation data
            const animationData = mockAnimationRegistry.getByFilename(file);
            expect(animationData).toEqual({ loopable: false });

            // Step 2: Check looping logic
            const shouldLoop = duration === 0 && animationData && animationData.loopable;
            expect(shouldLoop).toBe(false);

            // Step 3: Configure action
            if (shouldLoop) {
                mockAction.setLoop(THREE.LoopRepeat);
                mockAction.clampWhenFinished = false;
            } else {
                mockAction.setLoop(THREE.LoopOnce);
                mockAction.clampWhenFinished = true;
            }

            // Step 4: Track loop state
            const currentAnimationShouldLoop = animationData && animationData.loopable;
            expect(currentAnimationShouldLoop).toBe(false);

            // Step 5: Verify configuration
            expect(mockAction.setLoop).toHaveBeenCalledWith(THREE.LoopOnce);
            expect(mockAction.clampWhenFinished).toBe(true);

            // Step 6: On finished event, should NOT restart
            if (currentAnimationShouldLoop && mockAction) {
                mockAction.reset();
                mockAction.play();
            }

            expect(mockAction.reset).not.toHaveBeenCalled();
            expect(mockAction.play).not.toHaveBeenCalled();
        });
    });
});
