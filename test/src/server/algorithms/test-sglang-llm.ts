/**
 * SGLang LLM Service Direct Test Script
 * 
 * Run with: DOTENV_CONFIG_PATH=.env npx tsx --env-file=.env test/server/test-sglang-llm.ts
 */

import { sglangLLMService } from '../../src/server/algorithms/sglang-llm';
import { unifiedConfig as config } from '../../src/config/index';

console.log('🧪 Testing SGLang LLM Service...');
console.log('Current provider:', config.llm.provider);
console.log('SGLang endpoint:', config.endpoints.sglang);
console.log('SGLang model:', config.llm?.sglang?.model || 'Xinyuan-LLM-14B-0428');

async function testSGLangService() {
    try {
        console.log('\n=== Testing SGLang LLM Service ===');

        // Test basic text generation
        const testRequest = {
            prompt: "Hello! Please respond with a brief greeting.",
            model: config.llm?.sglang?.model || 'Xinyuan-LLM-14B-0428',
            temperature: 0.7,
            stream: false,
            language: 'English'
        };

        console.log('Sending test request:', {
            promptLength: testRequest.prompt.length,
            model: testRequest.model,
            stream: testRequest.stream
        });

        const response = await sglangLLMService.generateText(testRequest);

        console.log('\n=== SGLang Response ===');
        console.log('Success:', response.success);

        if (response.success && response.data) {
            console.log('Response text:', response.data.text);
            console.log('Model used:', response.data.model);
            console.log('Token usage:', response.data.usage);
        } else {
            console.error('Error:', response.error);
        }

        // Test streaming if the first test was successful
        if (response.success) {
            console.log('\n=== Testing Streaming ===');

            const streamRequest = {
                ...testRequest,
                prompt: "Please count from 1 to 5, each number on a new line.",
                stream: true
            };

            let fullStreamResponse = '';
            const streamResponse = await sglangLLMService.generateText(
                streamRequest,
                (chunk) => {
                    process.stdout.write(chunk);
                    fullStreamResponse += chunk;
                },
                (fullResponse) => {
                    console.log('\n\nStreaming completed. Full response length:', fullResponse.length);
                }
            );

            console.log('\nStreaming success:', streamResponse.success);
            if (!streamResponse.success) {
                console.error('Streaming error:', streamResponse.error);
            }
        }

        // Test Chinese language support
        if (response.success) {
            console.log('\n=== Testing Chinese Language Support ===');

            const chineseRequest = {
                ...testRequest,
                prompt: "你好，请用中文回答。",
                language: 'Chinese'
            };

            const chineseResponse = await sglangLLMService.generateText(chineseRequest);

            console.log('Chinese response success:', chineseResponse.success);
            if (chineseResponse.success && chineseResponse.data) {
                console.log('Chinese response text:', chineseResponse.data.text);
            } else if (!chineseResponse.success) {
                console.error('Chinese response error:', chineseResponse.error);
            }
        }

    } catch (error) {
        console.error('Test failed with error:', error);
        process.exit(1);
    }
}

// Run the test
testSGLangService().then(() => {
    console.log('\n✅ SGLang LLM Test Completed Successfully');
    process.exit(0);
}).catch(error => {
    console.error('❌ Test script failed:', error);
    process.exit(1);
});
