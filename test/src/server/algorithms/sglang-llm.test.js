/**
 * SGLang LLM Service Integration Tests
 * Tests SGLang LLM service functionality including text generation and streaming
 * 
 * Run with: DOTENV_CONFIG_PATH=.env npx tsx --env-file=.env test/server/test-sglang-llm.ts
 * Or via test framework: npm run test:real-api -- test/server/sglang-llm.test.js
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { unifiedConfig as config } from '@/config/index.ts';

// Test configuration
const TEST_CONFIG = {
    timeout: 30000, // 30 seconds timeout for LLM requests
    defaultModel: 'Xinyuan-LLM-14B-0428', // Updated to match actual SGLang model
    testPrompts: {
        simple: "Hello! Please respond with a brief greeting.",
        counting: "Please count from 1 to 5, each number on a new line.",
        conversation: "What is 2 + 2?",
        chinese: "你好，请用中文回答。"
    }
};

// Mock implementations for isolated testing
const mockSGLangService = {
    generateText: vi.fn(),
    processMediaStream: vi.fn(),
    createSGLangPayload: vi.fn()
};

describe('SGLang LLM Service', () => {
    let sglangLLMService;

    beforeEach(async () => {
        // Only import the actual service if we're doing integration tests
        if (process.env.TEST_MODE !== 'unit') {
            const { sglangLLMService: actualService } = await import('../../src/server/algorithms/sglang-llm.js');
            sglangLLMService = actualService;
        } else {
            sglangLLMService = mockSGLangService;
        }
    });

    afterEach(() => {
        vi.clearAllMocks();
    });

    describe('Configuration', () => {
        it('should have correct SGLang configuration', () => {
            expect(config.endpoints.sglang).toBeDefined();
            expect(config.llm.provider).toBe('sglang');
            expect(config.llm.sglang).toBeDefined();
            expect(config.llm.sglang.model).toBe(TEST_CONFIG.defaultModel);
        });

        it('should have valid endpoint URL', () => {
            const endpoint = config.endpoints.sglang;
            expect(endpoint).toMatch(/^https?:\/\/.+/);
        });
    });

    describe('Text Generation', () => {
        it('should generate text successfully', async () => {
            if (process.env.TEST_MODE === 'unit') {
                // Unit test with mocks
                mockSGLangService.generateText.mockResolvedValue({
                    success: true,
                    data: {
                        text: 'Hello there! How can I assist you today?',
                        model: TEST_CONFIG.defaultModel,
                        usage: {
                            promptTokens: 200,
                            completionTokens: 11,
                            totalTokens: 211
                        }
                    }
                });
            }

            const testRequest = {
                prompt: TEST_CONFIG.testPrompts.simple,
                model: config.llm?.sglang?.model || TEST_CONFIG.defaultModel,
                temperature: 0.7,
                stream: false,
                language: 'English'
            };

            const response = await sglangLLMService.generateText(testRequest);

            expect(response).toBeDefined();
            expect(response.success).toBe(true);
            expect(response.data).toBeDefined();
            expect(response.data.text).toBeDefined();
            expect(typeof response.data.text).toBe('string');
            expect(response.data.text.length).toBeGreaterThan(0);
            expect(response.data.model).toBe(testRequest.model);

            if (response.data.usage) {
                expect(response.data.usage.promptTokens).toBeGreaterThan(0);
                expect(response.data.usage.completionTokens).toBeGreaterThan(0);
                expect(response.data.usage.totalTokens).toBeGreaterThan(0);
            }
        }, TEST_CONFIG.timeout);

        it('should handle mathematical questions correctly', async () => {
            if (process.env.TEST_MODE === 'unit') {
                mockSGLangService.generateText.mockResolvedValue({
                    success: true,
                    data: {
                        text: '2 + 2 equals 4.',
                        model: TEST_CONFIG.defaultModel,
                        usage: { promptTokens: 150, completionTokens: 8, totalTokens: 158 }
                    }
                });
            }

            const testRequest = {
                prompt: TEST_CONFIG.testPrompts.conversation,
                model: config.llm?.sglang?.model || TEST_CONFIG.defaultModel,
                temperature: 0.1, // Lower temperature for more consistent answers
                stream: false,
                language: 'English'
            };

            const response = await sglangLLMService.generateText(testRequest);

            expect(response.success).toBe(true);
            expect(response.data.text).toBeDefined();
            expect(response.data.text.toLowerCase()).toMatch(/4|four/);
        }, TEST_CONFIG.timeout);
    });

    describe('Streaming', () => {
        it('should generate streaming text successfully', async () => {
            if (process.env.TEST_MODE === 'unit') {
                // Mock streaming behavior
                mockSGLangService.generateText.mockImplementation(async (request, onProgress, onCompletion) => {
                    const chunks = ['1\n', '2\n', '3\n', '4\n', '5\n'];
                    let fullResponse = '';

                    for (const chunk of chunks) {
                        fullResponse += chunk;
                        if (onProgress) onProgress(chunk);
                        await new Promise(resolve => setTimeout(resolve, 100));
                    }

                    if (onCompletion) onCompletion(fullResponse);

                    return {
                        success: true,
                        data: {
                            text: fullResponse,
                            model: TEST_CONFIG.defaultModel,
                            usage: { promptTokens: 200, completionTokens: 10, totalTokens: 210 }
                        }
                    };
                });
            }

            const testRequest = {
                prompt: TEST_CONFIG.testPrompts.counting,
                model: config.llm?.sglang?.model || TEST_CONFIG.defaultModel,
                temperature: 0.7,
                stream: true,
                language: 'English'
            };

            let streamedChunks = [];
            let fullStreamResponse = '';
            let completionCalled = false;

            const response = await sglangLLMService.generateText(
                testRequest,
                (chunk) => {
                    streamedChunks.push(chunk);
                    fullStreamResponse += chunk;
                },
                (fullResponse) => {
                    completionCalled = true;
                    expect(fullResponse).toBe(fullStreamResponse);
                }
            );

            expect(response.success).toBe(true);
            expect(streamedChunks.length).toBeGreaterThan(0);
            expect(fullStreamResponse.length).toBeGreaterThan(0);
            expect(completionCalled).toBe(true);

            // Check that we received the counting numbers
            expect(fullStreamResponse).toMatch(/1/);
            expect(fullStreamResponse).toMatch(/2/);
            expect(fullStreamResponse).toMatch(/3/);
            expect(fullStreamResponse).toMatch(/4/);
            expect(fullStreamResponse).toMatch(/5/);
        }, TEST_CONFIG.timeout);
    });

    describe('Error Handling', () => {
        it('should handle empty prompts gracefully', async () => {
            if (process.env.TEST_MODE === 'unit') {
                mockSGLangService.generateText.mockResolvedValue({
                    success: false,
                    error: 'Prompt is required',
                    data: null
                });
            }

            const testRequest = {
                prompt: '',
                model: config.llm?.sglang?.model || TEST_CONFIG.defaultModel,
                temperature: 0.7,
                stream: false,
                language: 'English'
            };

            const response = await sglangLLMService.generateText(testRequest);

            expect(response.success).toBe(false);
            expect(response.error).toBeDefined();
        });

        it('should handle invalid model gracefully', async () => {
            if (process.env.TEST_MODE === 'unit') {
                mockSGLangService.generateText.mockResolvedValue({
                    success: false,
                    error: 'Model not found',
                    data: null
                });
            }

            const testRequest = {
                prompt: TEST_CONFIG.testPrompts.simple,
                model: 'invalid-model-name',
                temperature: 0.7,
                stream: false,
                language: 'English'
            };

            const response = await sglangLLMService.generateText(testRequest);

            // Either it should fail gracefully or succeed with the default model
            expect(response).toBeDefined();
            if (!response.success) {
                expect(response.error).toBeDefined();
            }
        });
    });

    describe('Multilingual Support', () => {
        it('should handle Chinese input correctly', async () => {
            if (process.env.TEST_MODE === 'unit') {
                mockSGLangService.generateText.mockResolvedValue({
                    success: true,
                    data: {
                        text: '你好！我很乐意帮助你。',
                        model: TEST_CONFIG.defaultModel,
                        usage: { promptTokens: 180, completionTokens: 12, totalTokens: 192 }
                    }
                });
            }

            const testRequest = {
                prompt: TEST_CONFIG.testPrompts.chinese,
                model: config.llm?.sglang?.model || TEST_CONFIG.defaultModel,
                temperature: 0.7,
                stream: false,
                language: 'Chinese'
            };

            const response = await sglangLLMService.generateText(testRequest);

            expect(response.success).toBe(true);
            expect(response.data.text).toBeDefined();
            expect(response.data.text.length).toBeGreaterThan(0);
        }, TEST_CONFIG.timeout);
    });
});

// Integration test helper
export async function runSGLangIntegrationTest() {
    console.log('🧪 Running SGLang LLM Integration Test...');
    console.log('Provider:', config.llm.provider);
    console.log('Endpoint:', config.endpoints.sglang);
    console.log('Model:', config.llm?.sglang?.model || TEST_CONFIG.defaultModel);

    try {
        const { sglangLLMService } = await import('../../src/server/algorithms/sglang-llm.js');

        // Quick integration test
        const testRequest = {
            prompt: "Hello! Please respond with just 'Integration test successful.'",
            model: config.llm?.sglang?.model || TEST_CONFIG.defaultModel,
            temperature: 0.1,
            stream: false,
            language: 'English'
        };

        const response = await sglangLLMService.generateText(testRequest);

        if (response.success) {
            console.log('✅ Integration test passed');
            console.log('Response:', response.data.text);
            return true;
        } else {
            console.log('❌ Integration test failed');
            console.log('Error:', response.error);
            return false;
        }
    } catch (error) {
        console.log('❌ Integration test failed with exception');
        console.log('Error:', error.message);
        return false;
    }
} 