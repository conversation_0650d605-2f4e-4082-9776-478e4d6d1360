/**
 * Vitest Configuration for LangGraph Agent Tests
 * Provides optimized test running and comprehensive coverage reporting
 */

import { defineConfig } from 'vitest/config';
import { resolve } from 'path';

export default defineConfig({
    test: {
        // Environment setup
        environment: 'node',

        // Global test configuration
        globals: true,
        clearMocks: true,

        // Test file patterns
        include: [
            'test/**/*.test.js',
            'test/**/*.spec.js'
        ],

        // Test patterns to exclude
        exclude: [
            'node_modules/**',
            'dist/**',
            'build/**',
            '**/*.d.ts'
        ],

        // Test timeout configuration for real API calls
        testTimeout: 15000, // 15 seconds for real API calls
        hookTimeout: 10000,

        // Coverage configuration
        coverage: {
            provider: 'v8',
            reporter: ['text', 'json', 'html'],
            reportsDirectory: './test/coverage',
            include: [
                'src/agent/**/*.js',
                'src/utils/apiProxy.ts'
            ],
            exclude: [
                'node_modules/**',
                'test/**',
                '**/*.test.js',
                '**/*.spec.js',
                '**/mock/**'
            ],
            thresholds: {
                global: {
                    branches: 80,
                    functions: 80,
                    lines: 80,
                    statements: 80
                }
            }
        },

        // Mock configuration
        mockReset: true,
        restoreMocks: true,

        // Setup files
        setupFiles: ['./test/setup.js'],

        // Test reporter configuration
        reporters: ['verbose', 'json'],

        // Parallel execution
        pool: 'threads',
        poolOptions: {
            threads: {
                minThreads: 1,
                maxThreads: 4
            }
        },

        // Test sequencing
        sequence: {
            hooks: 'parallel',
            setupFiles: 'list'
        },

        // Retry configuration for real API tests
        retry: {
            'test/agent/integration/real-api.test.js': 2,
            'test/agent/stream/langchain-streaming.test.js': 2
        },

        // Environment variables for testing
        env: {
            NODE_ENV: 'test',
            VITE_VLLM_ENDPOINT: process.env.VITE_VLLM_ENDPOINT || 'http://10.120.16.6:20095',
            VITE_ASR_ENDPOINT: process.env.VITE_ASR_ENDPOINT || 'http://10.120.16.6:20100',
            LOG_LEVEL: 'warn' // Reduce log noise during tests
        }
    },

    // Resolve configuration for imports
    resolve: {
        alias: {
            '@': resolve(__dirname, '../src'),
            '@test': resolve(__dirname, '.'),
            '@agent': resolve(__dirname, '../src/agent'),
            '@utils': resolve(__dirname, '../src/utils')
        }
    },

    // Define global variables for tests
    define: {
        __TEST__: true,
        __VERSION__: JSON.stringify(process.env.npm_package_version || '1.0.0')
    }
});