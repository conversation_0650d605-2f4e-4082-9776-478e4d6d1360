/**
 * Test LangChain.js streaming integration with TalkingAvatar
 * Verifies that the agent service properly streams responses using LangChain.js patterns
 */

import { LangGraphAgentService } from '../../src/agent/core.js';
import { createLogger } from '../../src/utils/logger.js';

const logger = createLogger('StreamingTest');

/**
 * Test streaming functionality
 */
async function testStreaming() {
    logger.info('Starting LangChain.js streaming test...');

    try {
        // Create agent service instance
        const agentService = new LangGraphAgentService({
            temperature: 0.7,
            maxTokens: 100, // Keep short for testing
            services: {
                // Mock services for testing
                ttsService: null,
                audioPlayer: null,
                animationController: {
                    triggerAnimation: async (data) => {
                        logger.info('Mock animation triggered:', data);
                        return true;
                    }
                }
            }
        });

        // Initialize the service
        await agentService.initialize();
        logger.info('Agent service initialized');

        // Test streaming response
        const testInput = "Hello, tell me a short joke";
        const streamingOptions = {
            sessionId: 'test-session',
            language: 'english',
            gender: 'male',
            mood: 'neutral',
            stream: true, // Enable streaming
            useTools: false // Disable tools for simple test
        };

        logger.info('Testing streaming response...');
        const streamResponse = await agentService.generateResponse(testInput, streamingOptions);

        // Check if it's a streaming response
        if (streamResponse && typeof streamResponse[Symbol.asyncIterator] === 'function') {
            logger.info('✓ Streaming response detected');

            let chunkCount = 0;
            let fullResponse = '';

            // Consume the stream
            for await (const chunk of streamResponse) {
                chunkCount++;
                logger.info(`Chunk ${chunkCount}:`, chunk);

                // Extract content based on chunk format
                if (chunk.responseText) {
                    fullResponse = chunk.responseText;
                } else if (chunk.content) {
                    fullResponse += chunk.content;
                } else if (chunk.data?.responseText) {
                    fullResponse = chunk.data.responseText;
                } else if (chunk.data?.content) {
                    fullResponse += chunk.data.content;
                }
            }

            logger.info(`✓ Streaming completed with ${chunkCount} chunks`);
            logger.info(`✓ Final response: "${fullResponse}"`);

            if (fullResponse.length > 0) {
                logger.info('✓ Streaming test PASSED');
                return true;
            } else {
                logger.error('✗ Streaming test FAILED: No content received');
                return false;
            }

        } else {
            logger.error('✗ Expected streaming response but got:', typeof streamResponse);
            return false;
        }

    } catch (error) {
        logger.error('✗ Streaming test FAILED with error:', error);
        return false;
    }
}

/**
 * Test non-streaming functionality
 */
async function testNonStreaming() {
    logger.info('Starting non-streaming test...');

    try {
        // Create agent service instance
        const agentService = new LangGraphAgentService({
            temperature: 0.7,
            maxTokens: 50,
            services: {
                ttsService: null,
                audioPlayer: null,
                animationController: null
            }
        });

        await agentService.initialize();

        // Test non-streaming response
        const testInput = "Say hello";
        const nonStreamingOptions = {
            sessionId: 'test-session-2',
            language: 'english',
            stream: false // Disable streaming
        };

        const response = await agentService.generateResponse(testInput, nonStreamingOptions);

        if (response && response.responseText) {
            logger.info('✓ Non-streaming response received:', response.responseText);
            logger.info('✓ Non-streaming test PASSED');
            return true;
        } else {
            logger.error('✗ Non-streaming test FAILED: No response text');
            return false;
        }

    } catch (error) {
        logger.error('✗ Non-streaming test FAILED with error:', error);
        return false;
    }
}

/**
 * Test TalkingAvatar integration patterns
 */
async function testTalkingAvatarIntegration() {
    logger.info('Starting TalkingAvatar integration test...');

    try {
        // Simulate TalkingAvatar's _consumeStreamingResponse method
        const mockStreamGenerator = async function* () {
            // Simulate LangGraph streaming chunks
            yield {
                type: 'llm_chunk',
                data: {
                    content: 'Hello',
                    responseText: 'Hello',
                    isPartial: true
                }
            };

            yield {
                type: 'llm_chunk',
                data: {
                    content: ' there!',
                    responseText: 'Hello there!',
                    isPartial: true
                }
            };

            yield {
                type: 'llm_complete',
                data: {
                    finalContent: 'Hello there!',
                    responseText: 'Hello there!',
                    isComplete: true
                }
            };
        };

        // Test the consumption logic
        let fullResponse = '';
        let lastCompleteResponse = '';

        for await (const chunk of mockStreamGenerator()) {
            logger.info('Processing chunk:', chunk);

            if (chunk && typeof chunk === 'object') {
                if (chunk.type === 'llm_chunk' && chunk.data) {
                    if (chunk.data.content) {
                        fullResponse += chunk.data.content;
                    }
                    if (chunk.data.responseText) {
                        lastCompleteResponse = chunk.data.responseText;
                    }
                } else if (chunk.type === 'llm_complete' && chunk.data) {
                    if (chunk.data.finalContent) {
                        fullResponse = chunk.data.finalContent;
                    }
                    if (chunk.data.responseText) {
                        lastCompleteResponse = chunk.data.responseText;
                    }
                }
            }
        }

        const finalResponse = lastCompleteResponse || fullResponse;
        
        if (finalResponse === 'Hello there!') {
            logger.info('✓ TalkingAvatar integration test PASSED');
            return true;
        } else {
            logger.error('✗ TalkingAvatar integration test FAILED. Expected "Hello there!", got:', finalResponse);
            return false;
        }

    } catch (error) {
        logger.error('✗ TalkingAvatar integration test FAILED with error:', error);
        return false;
    }
}

/**
 * Run all tests
 */
async function runTests() {
    logger.info('=== LangChain.js Integration Tests ===');

    const results = {
        streaming: await testStreaming(),
        nonStreaming: await testNonStreaming(),
        talkingAvatarIntegration: await testTalkingAvatarIntegration()
    };

    logger.info('=== Test Results ===');
    logger.info('Streaming:', results.streaming ? 'PASSED' : 'FAILED');
    logger.info('Non-streaming:', results.nonStreaming ? 'PASSED' : 'FAILED');
    logger.info('TalkingAvatar Integration:', results.talkingAvatarIntegration ? 'PASSED' : 'FAILED');

    const allPassed = Object.values(results).every(result => result === true);
    logger.info('Overall:', allPassed ? 'ALL TESTS PASSED' : 'SOME TESTS FAILED');

    return allPassed;
}

// Export for use in other tests
export { testStreaming, testNonStreaming, testTalkingAvatarIntegration, runTests };

// Run tests if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
    runTests().then(success => {
        process.exit(success ? 0 : 1);
    }).catch(error => {
        logger.error('Test execution failed:', error);
        process.exit(1);
    });
}
