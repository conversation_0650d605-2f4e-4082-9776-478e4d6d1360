/**
 * Test suite for PhotoCapture component
 * Verifies the refactored PhotoCapture functionality using CameraManager
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';

// Mock CameraManager for testing
class MockCameraManager {
    constructor(container, options = {}) {
        this.container = container;
        this.options = options;
        this.isInitialized = false;
        this.isActive = false;
        this.currentMode = 'corner';
    }

    async initialize() {
        this.isInitialized = true;
        return true;
    }

    async startCamera() {
        if (!this.isInitialized) {
            throw new Error('CameraManager not initialized');
        }
        this.isActive = true;
        return true;
    }

    async showCamera(mode = 'corner') {
        if (!this.isActive) {
            throw new Error('Camera not started');
        }
        this.currentMode = mode;
        return true;
    }

    async hideCamera() {
        this.currentMode = null;
        return true;
    }

    stopCamera() {
        this.isActive = false;
    }

    async capturePhoto(options = {}) {
        if (!this.isActive) {
            throw new Error('Camera not active');
        }

        // Simulate photo capture
        return 'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD...';
    }

    dispose() {
        this.isInitialized = false;
        this.isActive = false;
        this.currentMode = null;
    }
}

// Mock the CameraManager import
vi.mock('../../../src/media/core/CameraManager.js', () => ({
    CameraManager: MockCameraManager
}));

// Mock viewer and cache utilities
const mockViewer = {
    container: {
        appendChild: vi.fn(),
        removeChild: vi.fn()
    },
    objects: {
        get: vi.fn(),
        add: vi.fn()
    }
};

vi.mock('../../viewerConfig.js', () => ({
    ASSETS: {
        meshes: '/assets/meshes/',
        textures: '/assets/textures/'
    }
}));

vi.mock('../../../src/utils/cache.js', () => ({
    generateCacheKey: vi.fn().mockResolvedValue('test-cache-key'),
    storeSeed: vi.fn().mockResolvedValue({ success: true })
}));

describe('PhotoCapture Component', () => {
    let photoCapture;

    beforeEach(async () => {
        // Import PhotoCapture dynamically to ensure mocks are applied
        const { PhotoCapture } = await import('@/../app/viewer/photoCapture.js');
        photoCapture = new PhotoCapture(mockViewer, {
            gender: 'boy',
            photoQuality: 0.9,
            cameraMode: 'embedded'
        });
    });

    afterEach(() => {
        if (photoCapture) {
            photoCapture.dispose();
        }
        vi.clearAllMocks();
    });

    describe('Constructor', () => {
        it('should initialize with default options', () => {
            expect(photoCapture.options.gender).toBe('boy');
            expect(photoCapture.options.photoQuality).toBe(0.9);
            expect(photoCapture.options.cameraMode).toBe('embedded');
            expect(photoCapture.selectedGender).toBe('boy');
        });

        it('should create CameraManager with correct configuration', () => {
            expect(photoCapture.cameraManager).toBeDefined();
            expect(photoCapture.cameraManager.options.defaultMode).toBe('corner');
        });

        it('should handle custom options', async () => {
            const { PhotoCapture } = await import('@/../app/viewer/photoCapture.js');
            const customPhotoCapture = new PhotoCapture(mockViewer, {
                gender: 'girl',
                photoQuality: 0.8,
                cameraMode: 'popup',
                photoWidth: 800,
                photoHeight: 600
            });

            expect(customPhotoCapture.options.gender).toBe('girl');
            expect(customPhotoCapture.options.photoQuality).toBe(0.8);
            expect(customPhotoCapture.options.cameraMode).toBe('popup');
            expect(customPhotoCapture.cameraManager.options.defaultMode).toBe('popup');
        });
    });

    describe('Initialization', () => {
        it('should initialize successfully', async () => {
            const result = await photoCapture.initialize();
            expect(result).toBe(true);
            expect(photoCapture.cameraManager.isInitialized).toBe(true);
        });

        it('should handle initialization failure', async () => {
            // Mock initialization failure
            photoCapture.cameraManager.initialize = vi.fn().mockResolvedValue(false);

            const result = await photoCapture.initialize();
            expect(result).toBe(false);
        });

        it('should handle initialization error', async () => {
            // Mock initialization error
            photoCapture.cameraManager.initialize = vi.fn().mockRejectedValue(new Error('Init failed'));

            const result = await photoCapture.initialize();
            expect(result).toBe(false);
        });
    });

    describe('Camera Operations', () => {
        beforeEach(async () => {
            await photoCapture.initialize();
        });

        it('should open camera successfully', async () => {
            await photoCapture.openCamera();

            expect(photoCapture.cameraManager.isActive).toBe(true);
            expect(photoCapture.isCameraActive).toBe(true);
        });

        it('should handle camera open failure', async () => {
            photoCapture.cameraManager.startCamera = vi.fn().mockRejectedValue(new Error('Camera failed'));

            // Mock alert to prevent actual alert dialog
            global.alert = vi.fn();

            await photoCapture.openCamera();

            expect(global.alert).toHaveBeenCalledWith('Failed to open camera. Please check camera permissions.');
        });

        it('should close camera successfully', async () => {
            await photoCapture.openCamera();
            await photoCapture.closeCamera();

            expect(photoCapture.isCameraActive).toBe(false);
        });
    });

    describe('Photo Capture', () => {
        beforeEach(async () => {
            await photoCapture.initialize();
            await photoCapture.openCamera();

            // Mock DOM elements
            global.document = {
                querySelector: vi.fn().mockReturnValue({
                    appendChild: vi.fn()
                }),
                createElement: vi.fn().mockReturnValue({
                    style: {},
                    appendChild: vi.fn(),
                    addEventListener: vi.fn()
                })
            };
        });

        it('should capture photo successfully', async () => {
            // Mock handlePhotoCaptured to avoid complex processing
            photoCapture.handlePhotoCaptured = vi.fn().mockResolvedValue(true);

            await photoCapture.capturePhoto();

            expect(photoCapture.handlePhotoCaptured).toHaveBeenCalledWith(
                expect.stringContaining('data:image/jpeg;base64,')
            );
        });

        it('should handle capture failure', async () => {
            photoCapture.cameraManager.capturePhoto = vi.fn().mockResolvedValue(null);

            await photoCapture.capturePhoto();

            // Should log error (we can't easily test console.error, but the method should not throw)
            expect(photoCapture.isProcessing).toBe(false);
        });

        it('should prevent multiple concurrent captures', async () => {
            photoCapture.isProcessing = true;

            await photoCapture.capturePhoto();

            // Should exit early without calling cameraManager.capturePhoto
            expect(photoCapture.cameraManager.capturePhoto).not.toHaveBeenCalled();
        });
    });

    describe('Status Updates', () => {
        it('should update status with normal message', () => {
            const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => { });

            photoCapture.updateStatus('Test message');

            expect(consoleSpy).toHaveBeenCalledWith('[PhotoCapture] Status:', 'Test message');

            consoleSpy.mockRestore();
        });

        it('should update status with error message', () => {
            const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => { });

            photoCapture.updateStatus('Error message', true);

            expect(consoleSpy).toHaveBeenCalledWith('[PhotoCapture] Status Error:', 'Error message');

            consoleSpy.mockRestore();
        });
    });

    describe('Cleanup', () => {
        it('should dispose properly', async () => {
            await photoCapture.initialize();
            await photoCapture.openCamera();

            await photoCapture.dispose();

            expect(photoCapture.cameraManager).toBeNull();
            expect(photoCapture.viewer).toBeNull();
        });

        it('should handle cleanup with active camera', async () => {
            await photoCapture.initialize();
            await photoCapture.openCamera();

            photoCapture.isCameraActive = true;

            await photoCapture.dispose();

            expect(photoCapture.isCameraActive).toBe(false);
            expect(photoCapture.cameraManager).toBeNull();
        });
    });

    describe('Error Handling', () => {
        it('should handle missing CameraManager gracefully', async () => {
            photoCapture.cameraManager = null;

            await photoCapture.openCamera();

            // Should log error and return gracefully
            expect(photoCapture.isCameraActive).toBe(false);
        });

        it('should handle photo capture without active camera', async () => {
            photoCapture.cameraManager = null;

            await photoCapture.capturePhoto();

            // Should log error and return gracefully
            expect(photoCapture.isProcessing).toBe(false);
        });
    });
});
