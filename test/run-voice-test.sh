#!/bin/bash

# Voice Reply Test Runner
# Runs the voice reply test with real API enabled

echo "🎤 Running Voice Reply Tests..."
echo "==============================="

# Set environment variables for real API testing
export TEST_REAL_API=true
export API_BASE_URL=http://localhost:2994

# Run the specific voice test
cd "$(dirname "$0")"
node src/agent/run-issue-tests.js voice

echo ""
echo "🎯 Voice reply test completed!"
echo "Check the output above for test results." 