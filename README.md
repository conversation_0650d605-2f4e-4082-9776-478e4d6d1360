# Holographic Hand Tracking Demo

A web-based holographic punching game that uses webcam input and MediaPipe for gesture recognition, rendered in real-time on a Looking Glass holographic display.

## Features

- 🖐️ Real-time hand tracking using MediaPipe
- 👊 Gesture detection (punch, rotate)
- 🎮 Interactive 3D scene with Three.js
- 🔮 Holographic display support via Looking Glass WebXR
- 🎵 3D spatial audio feedback
- ⚡ Local processing (no server-side computation needed)
- 📹 Support for both webcam and video file input
- 🔄 Any-to-3D conversion capabilities:
  - 📝 Text-to-3D generation
  - 🖼️ Image-to-3D conversion
  - 🎎 Doll-to-3D transformation
- 🗣️ Speech recognition and talking avatar:
  - 🎤 Real-time speech recognition using sherpa-onnx
  - 🧠 WebAssembly-powered ASR for local processing
  - 🔊 Text-to-speech synthesis
  - 👄 Lip-sync animation for 3D avatars

## Prerequisites

- Looking Glass Portrait or similar holographic display
- Web browser with WebXR support (Chrome 90+ recommended)
- Webcam
- Node.js (v14 or higher) and npm installed
- Looking Glass Bridge software installed
- CMake (required for building WebAssembly files for speech recognition)
  - macOS: `brew install cmake`
  - Ubuntu/Debian: `sudo apt install cmake`
  - Windows: Download from [cmake.org](https://cmake.org/download/)

## Quick Start

1. Clone the repository:

   ```bash
   <NAME_EMAIL>:xxlbigbrother/Hologram-Software.git
   cd Hologram-Software
   ```

2. Install npm:

   ```bash
   # For macOS/Linux using Homebrew
   brew install node

   # For Windows using Chocolatey
   choco install nodejs

   # For Ubuntu/Debian
   sudo apt update
   sudo apt install nodejs npm
   ```

3. Install dependencies:

   ```bash
   npm install
   ```

4. Start the development server:

   ```bash
   # For talking avatar features, start the proxy server first:
   npm run server
   
   # Then in a new terminal, start the application:
   # Start the baseline environment
   npm run launch baseline dev
   # Start the viewer environment
   npm run launch viewer dev
   npm run launch:mobile viewer dev # Start with mobile control support
   # Start the punchme environment
   npm run launch punchme dev
   ```

5. Open your browser and navigate to `https://localhost:5173`
   (Note: HTTPS is required for webcam access)

## Usage

### Fixing the Qwen-Omni 1011 Error

If you encounter a WebSocket connection error with code 1011 when using the talking avatar features, this indicates that the WebSocket proxy server is not running or not properly configured. The AliyunBailianChatModel requires a proxy server for browser connections to avoid CORS issues.

**Root Cause:** The Aliyun Qwen-Omni Realtime API requires server-side authentication headers that browsers cannot provide directly due to CORS restrictions.

**Complete Solution:**

1. **Ensure API key is in `.env` file:**

   ```bash
   # Check if API key is set
   grep VITE_DASHSCOPE_API_KEY .env
   ```

2. **Start the proxy server first:**

   ```bash
   npm run server
   ```

3. **Verify proxy is working:**

   ```bash
   curl http://localhost:2994/proxy-status
   # Should return: {"status":"ok","websocket_proxy":"/ws"}
   ```

4. **Then start the viewer application:**

   ```bash
   npm run launch viewer dev
   ```

**What this fixes:**

- ❌ **Before:** Browser tries to connect directly to `wss://dashscope.aliyuncs.com` → CORS error → 1011 error
- ✅ **After:** Browser connects via local proxy `ws://localhost:2994/ws` → Proxy forwards with proper authentication → Success

**Verification Steps:**

- Check proxy status: `curl http://localhost:2994/proxy-status`
- Should return: `{"status":"ok","websocket_proxy":"/ws"}`
- Run browser test: Open `test/debug/browser-qwen-omni-fix-test.html`
- Check server logs for successful WebSocket connections (no 1011 errors)

**If still having issues:**

1. Check that the API key has proper permissions for realtime models
2. Verify your region supports the Qwen-Omni model
3. Try restarting both proxy server and viewer application
4. Check browser console for additional error details

### Viewer Usage

- 🎥 **Webcam**: Default input method using your device camera
- 📁 **Video Upload**: For devices without cameras or for pre-recorded content
  - Supported formats: MP4, WebM
  - Maximum file size: 100MB
  - Recommended resolution: 720p or higher

### Supported Gestures

- 👊 **Punch**: Make a fist and punch toward the camera
- ✋ **Rotate**: Open palm and move left/right to rotate the scene
- 👆 **Point**: Point with index finger to select objects

### Controls

- Double-click: Toggle fullscreen
- ESC: Exit fullscreen
- S: Toggle settings panel
- D: Toggle debug overlay
- M: Toggle audio
- R: Reset scene

### Basic Viewer Usage

1. Start the viewer environment:

   ```bash
   npm run launch viewer dev
   ```

2. Open your browser and navigate to `https://localhost:5173`
3. Use the viewer interface:

   - Click the "Convert to 3D" button in the bottom-right corner
   - Select your conversion type:
     - Text: Enter a description
     - Image: Upload an image file
     - Doll: Upload a GLB file
   - Click "Generate" to create your 3D model
   - Interact with the model using mouse controls:
     - Left-click drag: Rotate
     - Right-click drag: Pan
     - Scroll: Zoom

### Speech Recognition and Talking Avatar

1. Before using speech recognition features, you need to build WebAssembly (WASM) files from the ONNX model:

   ```bash
   # Build WASM files for the Dolphin ASR model and copy them to the public folder
   npm run build:wasm:dolphin
   ```

2. The build process will:
   - Download the ONNX model if needed
   - Convert the ONNX model to WebAssembly format
   - Automatically copy the WASM files to the correct location in the public folder

3. After building the WASM files, start the viewer:

   ```bash
   npm run dev
   ```

4. The talking avatar features will be available with speech recognition capabilities

#### WASM Build Process

The WASM build process consists of three main steps:

1. **Building the WASM files**: This is done by the `build-wasm.js` script, which:
   - Downloads the ONNX model if needed
   - Sets up the Emscripten SDK
   - Compiles the ONNX model to WebAssembly format
   - Outputs the files to `temp/sherpa-onnx-build/dolphin/sherpa-onnx/build-wasm-simd-asr/bin`

2. **Copying the WASM files**: This is done by the `copy-wasm-files.js` script, which:
   - Copies the built WASM files to `public/models/tts/dolphin-small-ctc-multi-lang/`
   - Ensures the files are in the correct location for the application to find them

3. **Copying the model files**: This is done by the `copy-model-files.js` script, which:
   - Copies the model.onnx and tokens.txt files to `public/models/tts/dolphin-small-ctc-multi-lang/`
   - Ensures all required files are in the same directory

All three steps are now combined in a single command: `npm run build:wasm:dolphin`

#### Troubleshooting WASM Build

If you encounter issues with the WASM build:

- Ensure you have CMake installed (`brew install cmake` on macOS)
- Check the build log at `temp/sherpa-onnx-build/dolphin/sherpa-onnx-build.log`
- If the WASM files are built but not working, you can manually copy them:

  ```bash
  npm run copy-wasm-files
  npm run copy-model-files
  ```

- If you're still having issues, check the browser console for errors related to loading the WASM module
- The sherpaOnnxLoader.js file includes special handling to resolve path issues with the WASM module's data file
- For detailed instructions, see [Sherpa-ONNX WASM Build Documentation](doc/sherpa-onnx-wasm-build.md)

### Any-to-3D Conversion

The viewer includes powerful 3D conversion capabilities:

1. **Text to 3D**

   ```javascript
   // Convert text description to 3D model
   const result = await api.anyTo3D({
     source: "text",
     input: "A beautiful watch with golden details",
     seed: 42,
   });
   ```

2. **Image to 3D**

   ```javascript
   // Convert image to 3D model
   const result = await api.anyTo3D({
     source: "image",
     input: "path/to/image.jpg",
     seed: 42,
   });
   ```

3. **Doll to 3D**

   ```javascript
   // Convert doll model to enhanced 3D model
   const result = await api.anyTo3D({
     source: "doll",
     input: "path/to/doll.glb",
     seed: 42,
   });
   ```

### Generated Assets

Converted assets are organized in the following structure:

```
public/assets/
├── images/        # Generated images
├── videos/        # Generated videos
├── meshes/        # 3D models (GLB)
├── textures/      # Model textures
└── backgrounds/   # Environment maps
```

## Development

1. Using the Debug Mode Flag in .env:

```
VITE_DEBUG_MODE=true
```

### PWA Development

For building and deploying apps as Progressive Web Applications, see [PWA Documentation](doc/PWA.md).

### Looking Glass Configuration Properties

The Looking Glass WebXR configuration supports the following properties:

- **Quilt Settings**

  - `quiltResolution.width`: Width resolution of the holographic quilt
  - `quiltResolution.height`: Height resolution of the holographic quilt
  - `columns`: Number of columns in the quilt
  - `rows`: Number of rows in the quilt
- **Camera Position**

  - `targetX`: Camera position on X-axis
  - `targetY`: Camera position on Y-axis
  - `targetZ`: Camera position on Z-axis
  - `trackballX`: Camera rotation on X-axis
  - `trackballZ`: Camera rotation on Z-axis
- **Camera Properties**

  - `targetDiam`: Camera size (affects scene scale without changing focus)
  - `fovy`: Vertical field of view in radians
  - `depthiness`: View frustum modifier for perceived depth
  - `inlineView`: Display mode for main canvas ("matrix", "center", or "quilt")

Example configuration in `.env`:

```env
VITE_LOOKING_GLASS_SETTINGS={
  "quiltResolution": {"width": 4096, "height": 4096},
  "columns": 8,
  "rows": 6,
  "targetX": 0,
  "targetY": 0,
  "targetZ": 0,
  "trackballX": 0,
  "trackballZ": 0,
  "targetDiam": 3,
  "fovy": 0.6981317007977318,
  "depthiness": 1,
  "inlineView": "center"
}
```

2. Browser Debug Shortcuts (from your README):

```
### Controls
- D: Toggle debug overlay
- S: Toggle settings panel
```

3. Browser Developer Tools:
   Press F12 or right-click and select "Inspect"
   Check the Console tab for logs
   Check the Network tab for WebXR and MediaPipe connections

### Available Scripts

- `npm run dev` - Start development server
- `npm run launch baseline dev` - Launch baseline development environment
- `npm run launch viewer dev` - Launch viewer development environment
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run build:wasm` - Build WASM files for the default ASR model, copy WASM files, and copy model files to the public folder
- `npm run build:wasm:dolphin` - Build WASM files for the Dolphin ASR model, copy WASM files, and copy model files to the public folder
- `npm run build:wasm:senseVoice` - Build WASM files for the SenseVoice ASR model, copy WASM files, and copy model files to the public folder
- `npm run copy-wasm-files` - Copy built WASM files to the public folder (useful if you've already built the files)
- `npm run copy-model-files` - Copy model files (model.onnx, tokens.txt) to the public folder
- `npm test` - Run tests
- `npm run test:coverage` - Run tests with coverage
- `npm run lint` - Lint code
- `npm run format` - Format code with Prettier

### Project Structure

```
hologram-software/
├── src/
│   ├── apps/              # Application modules
│   │   ├── base.js       # Base application class
│   │   ├── config.js     # Shared app configuration
│   │   ├── utils.js      # Shared utilities
│   │   ├── baseline/     # Baseline application
│   │   │   ├── app.js    # Baseline app implementation
│   │   │   ├── index.js  # Entry point
│   │   │   └── index.html# Baseline HTML template
│   │   └── viewer/       # Viewer application
│   │       ├── viewer.js # Viewer implementation
│   │       ├── index.js  # Entry point
│   │       ├── viewer.html# Viewer HTML template
│   │       ├── viewer.css# Viewer styles
│   │       └── viewerConfig.js # Viewer-specific config
│   ├── ui/              # UI Components
│   │   ├── components/  # Reusable UI components
│   │   │   ├── Button.js     # Custom button component
│   │   │   ├── Slider.js     # Custom slider component
│   │   │   ├── Toggle.js     # Toggle switch component
│   │   │   └── Modal.js      # Modal dialog component
│   │   ├── panels/      # UI panels
│   │   │   ├── Settings.js   # Settings panel
│   │   │   ├── Debug.js      # Debug overlay panel
│   │   │   └── Controls.js   # Main controls panel
│   │   ├── styles/      # UI styling
│   │   │   ├── components.css # Component styles
│   │   │   └── panels.css    # Panel styles
│   │   └── utils/       # UI utilities
│   │       ├── animations.js  # UI animations
│   │       └── themes.js      # Theme management
│   ├── effects/          # Visual effects modules
│   │   └── modelEffects.js # 3D model animations/effects
│   ├── utils/           # Utility modules
│   │   └── assetLoader.js # Asset loading utilities
│   ├── config/           # Configuration modules
│   │   ├── env.ts        # Environment variable handling
│   │   └── endpoints.ts  # API endpoint configuration
│   ├── main.js              # Application entry point
│   ├── config.js            # Configuration constants
│   ├── camera.js            # Webcam handling
│   ├── handTracking.js      # MediaPipe integration
│   ├── sceneManager.js      # Three.js scene management
│   ├── gestureLogic.js      # Gesture detection
│   ├── effects/
│   │   └── particles.js     # Visual effects
│   ├── audio/
│   │   └── soundManager.js  # Audio handling
│   ├── tests/
│   │   ├── setup.js        # Test configuration
│   │   └── camera.test.js  # Camera module tests
│   ├── styles/
│   │   └── viewer.css      # Viewer component styles
│   ├── modules/
│   │   └── looking-glass/  # Looking Glass related modules
│   │       ├── index.ts    # Module exports
│   │       ├── config.ts   # Looking Glass configuration
│   │       └── polyfill.ts # WebXR polyfill setup
│   └── server/             # Server communication module
│       ├── api.ts          # Core API communication handlers
│       ├── config.ts       # Server configuration
│       ├── types.ts        # Type definitions
│       ├── storage.ts      # User data storage management
│       └── algorithms.ts   # Remote algorithm processing
├── doc #  module documentations
├── public/
│   ├── models/         # MediaPipe model files
│   └── assets/
│       ├── models/     # 3D model files (.glb)
│       ├── textures/   # Texture files
│       └── sounds/     # Audio files
├── index.html          # Entry point
├── package.json        # Project dependencies
├── vite.config.js      # Vite configuration
├── .env               # Environment variables
├── .eslintrc.js       # ESLint configuration
├── .gitignore         # Git ignore rules
├── tsconfig.json      # TypeScript configuration
└── README.md          # Project documentation
```

### Application Modules

#### Base Application (`src/apps/base.js`)

- Core functionality shared across all applications
- Scene management and rendering setup
- Asset loading infrastructure
- Event handling system

#### Baseline Application (`src/apps/baseline/`)

- Minimal implementation for development and testing
- Basic 3D scene setup
- Development environment for new features
- Launch with `npm run launch baseline dev`

#### Viewer Application (`src/apps/viewer/`)

- Main holographic viewing application
- Advanced 3D model interactions
- Hand tracking integration
- Looking Glass display optimization
- Custom effects and animations

For detailed information about the server communication module, see [Server Communication Documentation](doc/server-communication.md).

### Key Components

#### Camera Module (`camera.js`)

- Handles webcam initialization and management
- Provides video stream for hand tracking
- Manages camera lifecycle and cleanup

#### Hand Tracking (`handTracking.js`)

- Integrates with MediaPipe Hands
- Processes video frames in real-time
- Provides hand landmark data

#### Gesture Detection (`gestureLogic.js`)

- Analyzes hand landmarks
- Detects punch and rotation gestures
- Calculates gesture velocity and confidence

#### Scene Manager (`sceneManager.js`)

- Manages Three.js scene
- Handles Looking Glass WebXR integration
- Controls 3D object interactions

#### Sound Manager (`soundManager.js`)

- Handles 3D spatial audio
- Manages sound effects
- Controls audio settings

#### UI Components (`src/ui/`)

##### Reusable Components

- Custom buttons, sliders, and toggles for consistent interaction
- Modal system for dialogs and notifications
- Theme-aware components with dark/light mode support

##### UI Panels

- Settings panel for application configuration
- Debug overlay for development and troubleshooting
- Controls panel for main application interactions

##### Styling and Themes

- Modular CSS architecture
- Dynamic theme switching
- Responsive design for different display sizes
- Animation utilities for smooth transitions

### Testing

The project uses Vitest for testing. Tests are located in `src/tests/`.

Run tests:

```bash
npm test
```

Generate coverage report:

```bash
npm run test:coverage
```

### Docker Deployment

1. Build the Docker image:

   ```bash
   docker build -t holographic-demo .
   ```

2. Run the container:

   ```bash
   docker run -p 8080:80 holographic-demo
   ```

3. Access the application at `http://localhost:8080`

## Troubleshooting

### Common Issues

1. **Camera Access**

   - Ensure HTTPS is enabled (required for webcam)
   - Check browser permissions
   - Verify no other apps are using the camera
2. **Hand Tracking**

   - Ensure good lighting conditions
   - Keep hands within camera frame
   - Check MediaPipe model is loaded correctly
3. **Looking Glass Display**

   - Verify Looking Glass Bridge is running
   - Check WebXR support in browser
   - Ensure correct display configuration
4. **Input Selection**

   - Switch between webcam and video input in the settings panel
   - If video upload fails, check file format and size
   - Ensure video contains clear hand movements

### Server Communication

The project includes a server communication module for interacting with remote services:

#### Remote Algorithm Integration

- Secure SSH-based connection to remote computation server
- Asynchronous processing of algorithm requests
- Result handling and integration with the main application

#### User Data Storage

- Secure data transmission to remote storage
- User session management
- Data retrieval and caching mechanisms

Server Configuration:

```env
VITE_SERVER_HOST=*************
VITE_SERVER_PORT=20208
VITE_SERVER_USER=root
VITE_SSH_KEY_PATH=/path/to/ssh/key  # Optional: If using SSH key authentication
```

Example usage:

```typescript
// Request remote algorithm processing
const result = await algorithmService.process({
  inputData: handTrackingData,
  algorithm: 'gestureRecognition'
});

// Store user session data
await storageService.saveUserData({
  userId: 'user123',
  sessionData: {
    timestamps: [...],
    gestures: [...]
  }
});
```

### Performance Tips

- Enable hardware acceleration in Chrome
- Adjust MediaPipe model complexity in settings
- Use compressed textures
- Implement frame rate limiting
- Consider using Web Workers for gesture calculations

### Update Packages

clean up the old environment and install the correct stable versions of packages:

```bash
# check for updates
npx npm-check-updates -u
# Remove old dependencies and cache
rm -rf node_modules package-lock.json && npm install
```

## Contributing

1. Fork the repository
2. Create your feature branch
3. Install dependencies and run tests
4. Commit your changes
5. Push to your branch
6. Open a Pull Request

### Development Guidelines

- Follow ESLint configuration
- Write tests for new features
- Update documentation
- Format code using Prettier

## Recent Updates

### Audio Buffer Overflow Fix (January 2025)

- 🐛 **Fixed**: Critical buffer overflow error in real-time audio processing
- 🔧 **Resolved**: `RangeError: Offset is outside the bounds of the DataView` in Aliyun voice integration
- ✅ **Improved**: Audio utility parameter validation and error handling
- 🧪 **Added**: Comprehensive test suite with 109 media tests covering all edge cases
- 📚 **Enhanced**: Documentation for audio processing workflows

For detailed technical information, see [`doc/audio-buffer-overflow-fix.md`](doc/audio-buffer-overflow-fix.md).

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Acknowledgments

- [MediaPipe](https://mediapipe.dev/)
- [Three.js](https://threejs.org/)
- [Looking Glass Factory](https://lookingglassfactory.com/)

---

Made with ❤️ for the Looking Glass community
