import { Landmark, VirtualLandmark, Bone, VirtualBone } from './elements.js'
import { createKalmanFilter } from './KalmanFilter.js';
export { PoseElementManager, BoneManager, VirtualBoneManager };
import { Vector3, Quaternion } from '@babylonjs/core';
import SmoothingConfig from '../utils/SmoothingConfig.js';

class PoseElementManager {
    constructor() {
        this.container = [];
        this.initKalmanFilter = (initialBoneData) => {
            for (let element of this.container) {
                // Only pass bone data to bone elements, not landmarks
                if (initialBoneData && element.name && element.isBone) {
                    const initialData = initialBoneData[element.name];
                    if (initialData) {
                        element.initKalmanFilter(initialData);
                    } else {
                        element.initKalmanFilter();
                    }
                } else {
                    // For landmarks or elements without matching bone data, initialize with defaults
                    element.initKalmanFilter();
                }
            }
        };

        this.setUseKalmanFilter = (useKalmanFilter) => {
            for (let element of this.container) {
                element.setUseKalmanFilter(useKalmanFilter);
            }
        };

        this.add = (poseElement) => {
            this.container.push(poseElement);
        };
        this.configure = (poseElementConfigures) => {
            this.container = [];
            poseElementConfigures.forEach(config => {
                if (!config) return;
                let type = config.type;
                if (type === 'Landmark') {
                    this.add(new Landmark(this, config.poseElementConfig));
                } else if (type === 'VirtualLandmark') {
                    this.add(new VirtualLandmark(this, config.poseElementConfig, config.virtualLandmarkConfig));
                } else if (type === 'VirtualBone') {
                    this.add(new VirtualBone(this, config.poseElementConfig, config.virtualBoneConfig, config.otherName));
                } else if (type === 'Bone') {
                    this.add(new Bone(this, config.poseElementConfig, config.otherName));
                }
            });
        };
        this.update = (data = null) => {
            this.container.forEach(poseElement => { poseElement.update(data); });
        };
        this.setFromArray = (dataArray, format) => {
            if (!dataArray) return;
            for (let poseEl of this.container) {
                let index = poseEl.index;
                let data = dataArray[index];
                if (data) {
                    if (format === 'xyzv') {
                        poseEl.setFromXYZV(data);
                    } else if (format === 'array') {
                        poseEl.setFromArray(data);
                    } else if (format === 'mat') {
                        poseEl.setFromMat(data);
                    }
                } else {
                    poseEl.update();
                }
            }
        };


        this.get = (index) => {
            for (let i = 0; i < this.container.length; i++) {
                let poseElement = this.container[i];
                if (poseElement.index === index) {
                    return poseElement;
                }
            }
            return null;
        };
        this.getByName = (name) => {
            // console.log(name, this.container);
            for (let poseElement of this.container) {
                if (poseElement.name === name) {
                    return poseElement;
                }
            }
            return null;
        };
    }

    // // Enhanced method to support different filter types
    // setKalmanFilterParams(type, processCovariance, measurementCovariance) {
    //     // Apply new parameters to all elements
    //     for (let element of this.container) {
    //         if (element.kalmanFilter) {
    //             const filterType = element.isLandmark ? 'position' : 'rotation';

    //             // Only update if the type matches or no type specified
    //             if (!type || type === filterType) {
    //                 // Get current position or rotation values
    //                 const pos = element.isLandmark ? element.worldPosition :
    //                     (element.isBone ? element.worldQuaternion.toEulerAngles() : null);

    //                 if (pos) {
    //                     // Create new filter with updated parameters
    //                     element.kalmanFilter = createKalmanFilter(
    //                         filterType,
    //                         [pos.x, pos.y, pos.z],
    //                         {
    //                             processCovariance: processCovariance,
    //                             measurementCovariance: measurementCovariance
    //                         }
    //                     );
    //                 }
    //             }
    //         }
    //     }
    // }
}


class BoneManager extends PoseElementManager {
    constructor() {
        super();
        this.avatar = null;
        this.skeleton = null;
        this.avatarType = null;
        this.avatarBones = null;
        let slerpRatio = 1;

        // Local copy of smoothing params from global config
        this._smoothingParams = {
            baseSlerpRatio: SmoothingConfig.baseSlerpRatio,
            positionStiffness: SmoothingConfig.positionStiffness,
            rotationStiffness: SmoothingConfig.rotationStiffness,
            visibilityFactor: SmoothingConfig.visibilityFactor,
            confidenceThreshold: SmoothingConfig.confidenceThreshold
        };

        this.setSlerpRatio = (ratio) => {
            slerpRatio = ratio;
            this._smoothingParams.baseSlerpRatio = ratio;
        }

        // Add method to get the current slerpRatio
        this.getSlerpRatio = () => {
            return slerpRatio;
        }

        // Method to receive updates from global smoothing config
        this.updateSmoothingConfig = (config) => {
            if (!config) return;

            // Update local params from global config
            if (typeof config.baseSlerpRatio === 'number')
                this._smoothingParams.baseSlerpRatio = config.baseSlerpRatio;

            if (typeof config.positionStiffness === 'number')
                this._smoothingParams.positionStiffness = config.positionStiffness;

            if (typeof config.rotationStiffness === 'number')
                this._smoothingParams.rotationStiffness = config.rotationStiffness;

            if (typeof config.visibilityFactor !== 'undefined')
                this._smoothingParams.visibilityFactor = config.visibilityFactor;

            if (typeof config.confidenceThreshold === 'number')
                this._smoothingParams.confidenceThreshold = config.confidenceThreshold;

            // Update the legacy slerpRatio for backward compatibility
            slerpRatio = this._smoothingParams.baseSlerpRatio;
        }

        // Legacy method - now just calls updateSmoothingConfig
        this.setSmoothingConfig = this.updateSmoothingConfig;

        this.bindAvatar = (avatar, type, coordinateSystem = null) => {
            this.avatar = avatar;
            this.skeleton = avatar.metadata.skeleton;
            this.avatarBones = [];
            this.avatarType = type;
            this.coordinateSystem = coordinateSystem;
            const initialBoneData = this.extractInitialBoneData();

            this.initKalmanFilter(initialBoneData);
        };

        this.extractInitialBoneData = () => {
            if (!this.avatar || !this.skeleton) return {};

            const initialData = {};

            this.container.forEach(bone => {
                try {
                    const boneName = bone.otherName && bone.otherName[this.avatarType];
                    if (!boneName) return;

                    const avatarBone = this.skeleton.getBoneByName(boneName);
                    if (!avatarBone) return;

                    const rotation = avatarBone.getRotation(BABYLON.Space.WORLD, this.avatar);
                    if (rotation) {
                        initialData[bone.name] = [rotation.x, rotation.y, rotation.z];
                    }
                } catch (error) {
                    console.warn(`Error extracting initial data for bone ${bone.name}:`, error);
                }
            });

            return initialData;
        };

        // this.bindAvatarAndInitFilters = (avatar, type, coordinateSystem = null) => {
        //     this.bindAvatar(avatar, type, coordinateSystem);

        //     const initialBoneData = this.extractInitialBoneData();

        //     this.initKalmanFilter(initialBoneData);
        // };

        this.getAvatarRoot = () => {
            if (!this.skeleton || !this.skeleton.bones || this.skeleton.bones.length === 0) {
                return null;
            }

            const rootBoneNames = ["root", "Root", "mixamorig:Root", "hips", "Hips", "mixamorig:Hips"];
            for (const name of rootBoneNames) {
                const rootBone = this.skeleton.bones.find(b => b.name === name);
                if (rootBone) return rootBone;
            }

            return this.skeleton.bones[0];
        }

        this.add = (bone) => {
            this.container.push(bone);
            if (this.avatar) {
                let boneName = bone.otherName[this.avatarType];
                let index = bone.index;
                let avatarBone = this.skeleton.bones.find(b => b.name === boneName);
                if (avatarBone) {
                    this.avatarBones[index] = avatarBone;
                }
            }
        };

        this.updateVisibility = () => {
            for (let bone of this.container) {
                bone.updateVisibility();
            }
        };

        const leftQuatAdjust = Quaternion.FromEulerAngles(0, 0, Math.PI / 2);
        const rightQuatAdjust = Quaternion.FromEulerAngles(0, 0, -Math.PI / 2);
        const midQuatAdjust = Quaternion.FromEulerAngles(0, Math.PI / 2, Math.PI / 2);

        let avatarWorldQuaternion = new Quaternion();
        let coordinateSystemInverseQuaternion = new Quaternion();

        this.updateAvatar = () => {
            if (!this.avatar || !this.avatarBones) return;
            if (this.coordinateSystem) {
                this.coordinateSystem.getWorldQuaternion(coordinateSystemInverseQuaternion);
                coordinateSystemInverseQuaternion.invert();
            }

            this.container.forEach((bone, i) => {
                let avatarBone = this.avatarBones[i];
                if (avatarBone) {
                    // console.log(avatarBone, avatarBone.getRotationQuaternionToRef());
                    avatarBone.getRotationQuaternionToRef(BABYLON.Space.WORLD, this.avatar, avatarWorldQuaternion);

                    if (this.coordinateSystem) {
                        avatarWorldQuaternion.premultiply(coordinateSystemInverseQuaternion);
                    }

                    let avatarLocalQuaternion;
                    if (this.avatarType === 'bipedRobot') {
                        if (bone.side === 'right') {
                            avatarLocalQuaternion = Quaternion.Inverse(avatarWorldQuaternion).multiply(bone.worldQuaternion).multiply(rightQuatAdjust);
                        } else if (bone.side === 'left') {
                            avatarLocalQuaternion = Quaternion.Inverse(avatarWorldQuaternion).multiply(bone.worldQuaternion).multiply(leftQuatAdjust);
                        } else if (avatarBone.name !== 'hips_joint') {
                            avatarLocalQuaternion = Quaternion.Inverse(avatarWorldQuaternion).multiply(bone.worldQuaternion).multiply(midQuatAdjust);
                        } else {
                            avatarLocalQuaternion = Quaternion.Inverse(avatarWorldQuaternion).multiply(bone.worldQuaternion);
                        }
                    } else {
                        avatarLocalQuaternion = Quaternion.Inverse(avatarWorldQuaternion).multiply(bone.worldQuaternion);
                    };

                    let visibilityWeight = this._smoothingParams.visibilityFactor
                        ? Math.max(0.2, bone.visibility || 0.5) // Minimum 20% influence
                        : 1.0;

                    // Calculate confidence level (high/low) based on visibility threshold
                    const isHighConfidence = visibilityWeight >= this._smoothingParams.confidenceThreshold;

                    const stiffnessFactor = isHighConfidence
                        ? this._smoothingParams.rotationStiffness
                        : this._smoothingParams.rotationStiffness * 0.5;

                    // Calculate final adaptive slerp ratio
                    // Calculate final adaptive slerp ratio
                    const adaptiveSlerpRatio = this._smoothingParams.baseSlerpRatio * visibilityWeight * stiffnessFactor;

                    const slerpQuat = Quaternion.Slerp(
                        avatarBone.rotationQuaternion,
                        avatarLocalQuaternion,
                        adaptiveSlerpRatio
                    );
                    let transformNode = avatarBone.getTransformNode();
                        // console.log(avatarBone.getTransformNode());
                    // It's important to understand that when a bone is linked to a transform node, modifying the translation/rotation/scaling of the bone will have no effect; 
                    // instead, you need to update the linked transform node!
                    // avatarBone.rotationQuaternion.copyFrom(slerpQuat);
                    transformNode.rotationQuaternion = avatarLocalQuaternion;
                    console.log(transformNode.rotationQuaternion);
                }
            });
        }

        this.getWorldQuaternionArray = () => {
            let worldQuaternionArray = [];
            for (let bone of this.container) {
                if (!bone) {
                    worldQuaternionArray.push([0, 0, 0, 1]);
                    continue;
                }

                // Check if the quaternion exists and has valid properties
                try {
                    // Check if the quaternion exists and has valid properties
                    const quat = bone.worldQuaternion;
                    if (!quat ||
                        typeof quat.x !== 'number' ||
                        typeof quat.y !== 'number' ||
                        typeof quat.z !== 'number' ||
                        typeof quat.w !== 'number' ||
                        isNaN(quat.x) || isNaN(quat.y) || isNaN(quat.z) || isNaN(quat.w)) {

                        // Use identity quaternion if quaternion is invalid
                        worldQuaternionArray.push([0, 0, 0, 1]);
                    } else {
                        worldQuaternionArray.push([
                            quat.x, quat.y, quat.z, quat.w
                        ]);
                    }
                } catch (error) {
                    console.warn(`Could not get quaternion array for bone: ${bone?.name || 'unknown'}`, error);
                    worldQuaternionArray.push([0, 0, 0, 1]);
                }
            }
            return worldQuaternionArray;
        };
    }

    initKalmanFilter = (initialBoneData = null) => {
        // Initialize filters for each element without config parameters
        for (let element of this.container) {
            if (initialBoneData && element.name) {
                const initialData = initialBoneData[element.name];
                if (initialData) {
                    // Only pass initial data, no configuration
                    element.initKalmanFilter(initialData);
                } else {
                    // Initialize with null data
                    element.initKalmanFilter(null);
                }
            } else {
                // Initialize with null data
                element.initKalmanFilter(null);
            }
        }
    };

    // Add method to update Kalman filter parameters
    setKalmanFilterParams = (type, processCovariance, measurementCovariance) => {
        const config = {
            processCovariance,
            measurementCovariance
        };

        // Update parameters for all elements
        for (let element of this.container) {
            if (element.setKalmanFilterParams) {
                const elementType = element.isBone ? 'rotation' : 'position';
                // Only update if type matches or type is 'all'
                if (type === 'all' || type === elementType) {
                    element.setKalmanFilterParams(config);
                }
            }
        }
    };
}

class VirtualBoneManager extends BoneManager {
    constructor(landmarkManager) {
        super();
        this.landmarkManager = landmarkManager;
    }
    validateQuaternions() {
        this.container.forEach(bone => {
            if (!bone._worldQuaternion ||
                isNaN(bone._worldQuaternion.x) ||
                isNaN(bone._worldQuaternion.y) ||
                isNaN(bone._worldQuaternion.z) ||
                isNaN(bone._worldQuaternion.w)) {
                // Reset to identity quaternion if invalid
                bone._worldQuaternion = new Quaternion();
            }
        });
    }
}