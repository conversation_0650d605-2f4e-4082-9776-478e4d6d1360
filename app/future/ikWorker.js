import { IKSolver } from './inverseKinematics.js';
import { compressData, decompressData } from '../utils/dataCompression.js';

// Constants for data structure
const LANDMARK_SIZE = 33 * 3; // 33 landmarks * (x,y,z)
const BATCH_SIZE = 4; // Process 4 frames at once

// Add logging utilities
const logger = {
    info: (...args) => console.log('[IKWorker]', ...args),
    warn: (...args) => console.warn('[IKWorker]', ...args),
    error: (...args) => console.error('[IKWorker]', ...args),
    debug: (...args) => console.debug('[IKWorker]', ...args)
};

// Initialize SIMD if available
const SIMD_AVAILABLE = typeof WebAssembly.validate === 'function' &&
    WebAssembly.validate(new Uint8Array([0, 97, 115, 109, 1, 0, 0, 0, 1, 4, 1, 96, 0, 0, 3, 2, 1, 0, 10, 9, 1, 7, 0, 65, 0, 253, 15, 26, 11]));

// Create IK solver instance as module-level variable
let ikSolver;

function initialize() {
    return new Promise((resolve, reject) => {
        try {
            if (SIMD_AVAILABLE) {
                logger.info('SIMD operations available');
            }

            // Initialize IK solver
            ikSolver = new IKSolver({
                maxIterations: 5,
                tolerance: 0.02,
                useSIMD: SIMD_AVAILABLE
            });

            logger.info('IK Solver initialized');

            // Notify main thread that worker is ready
            self.postMessage({ type: 'ready', status: 'initialized' });
            logger.info('Worker ready');

            resolve(true);
        } catch (error) {
            logger.error('Initialization failed:', error);
            self.postMessage({
                type: 'error',
                error: error.message,
                stack: error.stack
            });
            reject(error);
        }
    });
}

// Enhanced message handler with initialization check
self.onmessage = function (e) {
    if (!ikSolver) {
        logger.warn('IK Solver not initialized, reinitializing...');
        if (!initialize()) {
            return;
        }
    }

    const { type, data } = e.data;
    logger.debug('Received message:', {
        type,
        dataSize: data?.binaryData?.byteLength,
        timestamp: performance.now()
    });

    switch (type) {
        case 'batchSolve':
            handleBatchSolve(data);
            break;
        case 'solve':
            handleSingleSolve(data);
            break;
        default:
            logger.warn('Unknown message type:', type);
    }
};

async function handleBatchSolve(data) {
    const startTime = performance.now();
    logger.debug('Starting batch solve');

    try {
        const decompressed = decompressData(data.binaryData);
        logger.debug('Decompressed data:', {
            frameCount: decompressed.length / LANDMARK_SIZE,
            totalPoints: decompressed.length
        });

        const landmarks = new Float32Array(decompressed);
        const results = [];

        for (let i = 0; i < landmarks.length; i += LANDMARK_SIZE * BATCH_SIZE) {
            const batchStartTime = performance.now();
            const batchResults = await processBatchWithSIMD(
                landmarks.subarray(i, Math.min(i + LANDMARK_SIZE * BATCH_SIZE, landmarks.length))
            );
            results.push(...batchResults);

            logger.debug('Batch processed:', {
                batchIndex: i / (LANDMARK_SIZE * BATCH_SIZE),
                duration: (performance.now() - batchStartTime).toFixed(2) + 'ms',
                resultCount: batchResults.length
            });
        }

        const compressed = compressData(results);
        logger.debug('Sending results:', {
            resultCount: results.length,
            compressedSize: compressed.byteLength,
            duration: (performance.now() - startTime).toFixed(2) + 'ms'
        });

        self.postMessage({
            type: 'batchResult',
            results: compressed
        }, [compressed.buffer]);

    } catch (error) {
        logger.error('Batch processing error:', error);
        self.postMessage({ type: 'error', error: error.message });
    }
}

async function processBatchWithSIMD(landmarksBatch) {
    const results = [];
    const batchSize = Math.floor(landmarksBatch.length / LANDMARK_SIZE);

    if (SIMD_AVAILABLE) {
        // Use SIMD for parallel processing
        const simdArray = new Float32x4Array(landmarksBatch.buffer);
        for (let i = 0; i < batchSize; i++) {
            const frameOffset = i * LANDMARK_SIZE / 4;
            results.push(processFrameSIMD(simdArray.subarray(frameOffset, frameOffset + LANDMARK_SIZE / 4)));
        }
    } else {
        // Fallback to regular processing
        for (let i = 0; i < batchSize; i++) {
            const frameOffset = i * LANDMARK_SIZE;
            results.push(processFrame(landmarksBatch.subarray(frameOffset, frameOffset + LANDMARK_SIZE)));
        }
    }

    return Promise.all(results);
}

function processFrameSIMD(simdLandmarks) {
    const chains = {};

    // Process arm chains
    if (isValidChain(simdLandmarks, 'leftArm')) {
        chains.leftArm = processChainSIMD('leftArm', simdLandmarks);
    }
    if (isValidChain(simdLandmarks, 'rightArm')) {
        chains.rightArm = processChainSIMD('rightArm', simdLandmarks);
    }

    return chains;
}

function processChainSIMD(chainType, simdData) {
    const indices = getChainIndices(chainType);
    const joints = [];

    // Extract joint positions using SIMD
    for (let i = 0; i < indices.length; i++) {
        const idx = indices[i] * 3 / 4; // Adjust for Float32x4
        const pos = simdData[idx];
        joints.push({
            x: pos[0],
            y: pos[1],
            z: pos[2]
        });
    }

    return ikSolver.solveFABRIK(joints, joints[joints.length - 1], calculateBoneLengths(joints));
}

function handleSingleSolve(data) {
    try {
        if (!data?.landmarks) {
            throw new Error('No landmark data provided');
        }

        const results = {};

        // Process arm chains only if required landmarks exist
        if (isValidChain(data.landmarks, 'leftArm')) {
            results.leftArm = processChain('leftArm', data.landmarks);
        }

        if (isValidChain(data.landmarks, 'rightArm')) {
            results.rightArm = processChain('rightArm', data.landmarks);
        }

        // Only send message if we have valid results
        if (Object.keys(results).length > 0) {
            self.postMessage({
                type: 'ikResult',
                results: results
            });
        }
    } catch (error) {
        logger.error('Error solving IK:', error);
        self.postMessage({
            type: 'error',
            error: error.message
        });
    }
}

function isValidChain(landmarks, chainType) {
    const indices = getChainIndices(chainType);
    if (!indices) return false;

    // Only check landmarks that exist
    const validIndices = indices.filter(i => landmarks[i] !== null);
    if (validIndices.length < 2) return false; // Need at least 2 points for IK

    return validIndices.every(i =>
        landmarks[i] &&
        typeof landmarks[i].x === 'number' &&
        typeof landmarks[i].y === 'number' &&
        typeof landmarks[i].z === 'number' &&
        landmarks[i].visibility > 0.5
    );
}

function processChain(chainType, landmarks) {
    const indices = getChainIndices(chainType);
    if (!indices || !isValidChain(landmarks, chainType)) {
        logger.warn(`Invalid chain data for ${chainType}`);
        return null;
    }

    try {
        // Convert landmarks to simple objects with x, y, z properties
        const joints = indices.map(i => ({
            x: landmarks[i].x,
            y: landmarks[i].y,
            z: landmarks[i].z || 0
        }));

        const boneLengths = [];
        for (let i = 0; i < joints.length - 1; i++) {
            boneLengths.push(distance(joints[i], joints[i + 1]));
        }

        // Solve IK and ensure results are plain objects
        const results = ikSolver.solveFABRIK(
            joints,
            joints[joints.length - 1],
            boneLengths
        );

        // Convert results to plain objects if needed
        return results.map(pos => ({
            x: pos.x,
            y: pos.y,
            z: pos.z
        }));
    } catch (error) {
        logger.error(`Chain processing error for ${chainType}:`, error);
        return null;
    }
}

function getChainIndices(chainType) {
    return BoneMapper.CHAIN_DEFINITIONS[chainType]?.landmarks || null;
}

function distance(p1, p2) {
    return Math.sqrt(
        Math.pow(p2.x - p1.x, 2) +
        Math.pow(p2.y - p1.y, 2) +
        Math.pow(p2.z - p1.z, 2)
    );
}

// Add helper for regular frame processing
function processFrame(landmarks) {
    return {
        leftArm: processChain('leftArm', landmarks),
        rightArm: processChain('rightArm', landmarks)
    };
}

// Add performance monitoring
let perfStats = {
    batchesProcessed: 0,
    totalProcessingTime: 0,
    simdUsed: SIMD_AVAILABLE
};

// Add logging to performance monitoring
function updatePerfStats(startTime) {
    const endTime = performance.now();
    perfStats.batchesProcessed++;
    perfStats.totalProcessingTime += endTime - startTime;

    if (perfStats.batchesProcessed % 10 === 0) { // Increased frequency for debugging
        const avgTime = perfStats.totalProcessingTime / perfStats.batchesProcessed;
        logger.info('Performance stats:', {
            avgProcessingTime: avgTime.toFixed(2) + 'ms',
            batchesProcessed: perfStats.batchesProcessed,
            simdEnabled: perfStats.simdUsed,
            currentBatchTime: (endTime - startTime).toFixed(2) + 'ms'
        });
    }
}

// Initialize worker immediately and handle any errors properly
initialize().catch(error => {
    logger.error('Worker initialization failed:', error);
    self.postMessage({
        type: 'error',
        error: 'Worker initialization failed',
        details: error.message
    });
});
