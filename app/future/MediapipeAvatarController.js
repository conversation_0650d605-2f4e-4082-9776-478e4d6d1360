import { BoneManager } from '../characters/elementManagers.js';
import { BoneIKController, Vector3, Quaternion } from '@babylonjs/core';
// Limbs (Arms and Legs): Use BoneIKController for two-bone chains (e.g., upper arm to lower arm, targeting wrist). This handles four IK chains efficiently.
// Hands: Direct rotation for finger bones using quaternions from leftHandQuatArr and rightHandQuatArr. IK for fingers is avoided due to performance concerns with multiple small chains.
// Spine and Head: Direct quaternion application from poseQuatArr. For the head, BoneLookController could be used with a face landmark target, but direct rotation is simpler and sufficient given existing quaternion data.
// Limitation Mitigation: The BoneIKController two-bone limit (forum concern) is addressed by applying IK only to limbs and using forward kinematics for the spine, which typically has multiple bones.
// Performance: IK may be slower than direct rotation but offers more natural limb posing. Test with your avatar to balance quality and speed.
import {
    MEDIAPIPE_VIRTUAL_BONES_CONFIG,
    MEDIAPIPE_LEFT_HAND_BONES_CONFIG,
    MEDIAPIPE_RIGHT_HAND_BONES_CONFIG,
    IK_TARGETS,
    BONE_CHAINS, // Import the common bone chain definitions
    MOVEMENT_THRESHOLD
} from '../configs/MediapipeConfig.js';
import { MeshBuilder } from '@babylonjs/core';
import SmoothingConfig from '../utils/SmoothingConfig.js';

class MediapipeAvatarController {
    constructor(character, scene, use_inverse_kinematics = false) {
        this.character = character;
        this.skeleton = character.metadata.skeleton; // Babylon.js skeleton
        this.scene = scene;
        this.latestData = null;
        this.use_inverse_kinematics = use_inverse_kinematics;
        this._skinnedMesh = null; // Reference to the skinned mesh, will be set by manager

        // Reference to global smoothing configuration
        this.smoothingConfig = SmoothingConfig;

        // Register as a listener for smoothing config changes
        this.smoothingConfig.addListener(this);

        // Flag to track if we have new pose data to process
        // this._hasNewPoseData = false;
        // Store IK controllers and targets
        if (this.use_inverse_kinematics) {
            this.ikControllers = {};
            // IK setup will happen after bone cache is ready
        }
        // scene.registerBeforeRender(() => {
        //     this.update(character);
        // });
        // Setup hand bone cache first
        // this.setupHandBoneCache();

        let leftHandBoneManager = this.leftHandBoneManager = new BoneManager();

        let rightHandBoneManager = this.rightHandBoneManager = new BoneManager();

        let poseBoneManager = this.poseBoneManager = new BoneManager();

        this.useHand = true;

        // Make bone cache more accessible (public property but with underscore to indicate semi-private)
        this._boneCache = {};
        // Root bone reference made public for the manager to access
        this._rootBone = null;

        // Reference to bone cache provider (will be set by manager)
        this._boneCacheProvider = null;

        // Matrix cache reference (will be set by manager)
        this.matrixCache = null;
        this.scene.registerBeforeRender(() => {
            this.update();
        });

        // TODO: to be applied in the manager
        this.updateIKTargets = (ikTargets) => {
            if (!this.ikControllers || !this.use_inverse_kinematics) return;

            // Update each target position if data exists
            Object.entries(IK_TARGETS).forEach(([chainName, config]) => {
                const controller = this.ikControllers[chainName];
                if (!controller) return;

                const targetData = ikTargets[`${config.targetLandmark}Pos`];
                if (!targetData) return;

                // Extract position and visibility
                const [x, y, z, visibility] = Array.isArray(targetData) && targetData.length >= 4
                    ? targetData
                    : [...targetData, 1.0]; // Default full visibility if not provided

                // Position the IK target with visibility-based interpolation
                const target = controller.targetMesh;

                // If we don't have a last position, initialize it
                if (!target._lastPosition) {
                    target._lastPosition = target.position.clone();
                }

                // Create target position
                const targetPosition = new Vector3(x, y, z);

                // Apply any height adjustment defined in config
                if (config.adjustHeight) {
                    targetPosition.y += config.adjustHeight;
                }

                // Calculate visibility-based interpolation factor
                const visibilityFactor = Math.max(0.1, Math.min(1.0, visibility || 0.5));

                // Interpolate based on visibility
                // - Higher visibility = follow target more closely
                // - Lower visibility = move more slowly/conservatively
                const lerpFactor = 0.2 + (0.6 * visibilityFactor);

                // Apply interpolated position
                target.position = Vector3.Lerp(
                    target._lastPosition,
                    targetPosition,
                    lerpFactor
                );

                // Store the current position for next frame
                target._lastPosition.copyFrom(target.position);

                // Similar approach for pole target
                if (controller.poleTargetMesh && config.poleOffset) {
                    const pole = controller.poleTargetMesh;

                    // Initialize last position if needed
                    if (!pole._lastPosition) {
                        pole._lastPosition = pole.position.clone();
                    }

                    // Create pole position based on target position and configuration
                    const polePosition = targetPosition.clone();

                    // Apply pole offset based on the chain configuration
                    // This positions the pole to control the elbow/knee bend direction
                    if (config.poleOffset.x) polePosition.x += config.poleOffset.x;
                    if (config.poleOffset.y) polePosition.y += config.poleOffset.y;
                    if (config.poleOffset.z) polePosition.z += config.poleOffset.z;

                    // Apply bone specific pole positioning logic if needed
                    if (chainName.includes('Left')) {
                        // Left limbs might need different pole positioning
                        polePosition.x -= 0.2; // Example: move left pole targets more to the left
                    } else if (chainName.includes('Right')) {
                        // Right limbs might need different pole positioning
                        polePosition.x += 0.2; // Example: move right pole targets more to the right
                    }

                    // Apply less aggressive interpolation for pole targets
                    // They need to be more stable to prevent flipping
                    const poleLerpFactor = Math.min(0.3, lerpFactor * 0.6);

                    // Apply interpolated position
                    pole.position = Vector3.Lerp(
                        pole._lastPosition,
                        polePosition,
                        poleLerpFactor
                    );

                    // Store the current position for next frame
                    pole._lastPosition.copyFrom(pole.position);
                }
            });
        };

        this.setupIKControllers = () => {
            // Use the IK_TARGETS from config
            Object.entries(IK_TARGETS).forEach(([chainName, config]) => {
                const { bones, poleOffset } = config;

                // Get the lower bone (second in chain) for two-bone IK
                let lowerBone = this.getBoneByName(bones[1]);

                if (lowerBone) {
                    // Create target and pole meshes
                    const target = MeshBuilder.CreateSphere(`${chainName}_target`, { diameter: 0.05 }, this.scene);
                    target.isVisible = false; // Hide targets in production

                    const poleTarget = MeshBuilder.CreateSphere(`${chainName}_pole`, { diameter: 0.03 }, this.scene);
                    poleTarget.isVisible = false;

                    // Create IK controller
                    this.ikControllers[chainName] = new BoneIKController(
                        this.character,
                        lowerBone,
                        {
                            targetMesh: target,
                            poleTargetMesh: poleTarget,
                            poleAngle: 0,
                            maxAngle: config.maxAngle || Math.PI * 0.9,
                            maxIterations: 20
                        }
                    );

                    console.log(`Set up IK controller for ${chainName}`);
                }
            });
        };

        this.bindAvatar = (avatar, type, coordinateSystem) => {
            poseBoneManager.bindAvatar(avatar, type, coordinateSystem);
            leftHandBoneManager.bindAvatar(avatar, type, coordinateSystem);
            rightHandBoneManager.bindAvatar(avatar, type, coordinateSystem);
            leftHandBoneManager.configure(MEDIAPIPE_LEFT_HAND_BONES_CONFIG);
            rightHandBoneManager.configure(MEDIAPIPE_RIGHT_HAND_BONES_CONFIG);
            poseBoneManager.configure(MEDIAPIPE_VIRTUAL_BONES_CONFIG);
        };

        this.setUseHand = (useHand) => {
            this.useHand = useHand;
        };

        this.setSlerpRatio = (ratio) => {
            // Check if we're in an update cycle
            if (this._updating) {
                // Just update bone managers directly, don't propagate to global config
                this.poseBoneManager.setSlerpRatio(ratio);
                this.leftHandBoneManager.setSlerpRatio(ratio);
                this.rightHandBoneManager.setSlerpRatio(ratio);
                return;
            }

            // Update global config
            this.smoothingConfig.update({ baseSlerpRatio: ratio });
        }

        // New method to set global smoothing configuration
        this.setSmoothingConfig = (config) => {
            // Avoid updates during notification cycle
            if (this._updating) return;

            this.smoothingConfig.update(config);
        }

        this.updateHand = (handData, dtype, isLeft) => {
            if (!this.useHand) return;

            try {
                // Skip update if no data
                if (!handData || handData.length === 0) return;

                let handBoneManager = isLeft ? this.leftHandBoneManager : this.rightHandBoneManager;

                // Validate handData before trying to use it
                if (Array.isArray(handData)) {
                    // Check if we have valid quaternion data
                    const hasValidData = handData.some(item =>
                        Array.isArray(item) && item.length >= 4 &&
                        item.every(val => typeof val === 'number' && !isNaN(val))
                    );

                    if (!hasValidData) {
                        console.warn(`[MediapipeAvatarController] Invalid hand data for ${isLeft ? 'left' : 'right'} hand`);
                        return;
                    }

                    // Extract overall hand visibility 
                    const wristVisibility = handData[0]?.[3] || 0.5;

                    // Set hand-specific slerpRatio based on visibility and global config
                    const visibilityFactor = Math.max(0.3, Math.min(1.0, wristVisibility));
                    const handSlerpRatio = this.smoothingConfig.baseSlerpRatio *
                        (this.smoothingConfig.visibilityFactor ? visibilityFactor : 1.0);
                    handBoneManager.setSlerpRatio(handSlerpRatio);
                }

                handBoneManager.setFromArray(handData, dtype);
                handBoneManager.updateAvatar();
            } catch (error) {
                console.error(`[MediapipeAvatarController] Error updating ${isLeft ? 'left' : 'right'} hand:`, error);
            }
        };

        this.updateRootPos = (position, confidence) => {
            if (!position || confidence < 0.1) {
                // If confidence is too low, keep the avatar still
                return;
            }
            try {
                // console.log(position, confidence);
                // Use the root bone from the cache provider
                const rootBone = this.getRootBone();
                if (!rootBone) return;

                // Add validation for position array
                if (!position || !Array.isArray(position) || position.length < 3) {
                    console.warn("Invalid position data:", position);
                    return; // Skip this update
                }

                // Initialize last position if not set
                if (!this._lastRootPos) {
                    // Make sure rootBone exists
                    if (!rootBone) {
                        console.warn("Root bone not found");
                        return;
                    }
                    const currentPos = rootBone.getPosition(BABYLON.Space.WORLD, this.character);
                    this._lastRootPos = currentPos.clone();
                }

                // Convert position array to Vector3
                const validatedRootPos = new BABYLON.Vector3(position[0], position[1], position[2]);

                // Calculate distance for adaptive smoothing
                const distance = BABYLON.Vector3.Distance(this._lastRootPos, validatedRootPos);
                // Consider capping extreme values
                const cappedDistance = Math.min(distance, 1.0);
                this.character.position.copyFrom(position);
                // console.log(cappedDistance)
                // // Only update if distance exceeds threshold
                // if (distance > MOVEMENT_THRESHOLD) {
                //     const isPartialVisibility = this._isPartialVisibility || false;
                //     const posStiffness = this.smoothingConfig.positionStiffness;
                //     const baseLerpRate = isPartialVisibility ? 0.12 * posStiffness : 0.18 * posStiffness;

                //     // Use confidence instead of hipVisibility for visibility factor
                //     const visibilityFactor = this.smoothingConfig.visibilityFactor
                //         ? Math.max(0.3, Math.min(1.0, confidence * 1.5))
                //         : 1.0;

                //     const distanceFactor = 1.0 / (1.0 + distance * 2);
                //     const lerpFactor = Math.min(0.7, baseLerpRate + distanceFactor) * visibilityFactor;
                //     // console.log(this._lastRootPos, validatedRootPos, lerpFactor);
                //     // Smoothly interpolate position
                //     const newPos = Vector3.Lerp(this._lastRootPos, validatedRootPos, lerpFactor);

                //     // Apply position to the character, accounting for world matrix
                //     if (this.character) {
                //         // const invWorldMatrix = this.matrixCache.getInverseWorldMatrix(this.character) || this.character.getWorldMatrix().clone().invert();
                //         // const localPos = Vector3.TransformCoordinates(newPos, invWorldMatrix);
                //         // console.log(newPos);
                //         this.character.position.copyFrom(newPos);
                //         // console.log(newPos);
                //     } else {
                //         this.character.position.copyFrom(newPos);
                //     }

                //     // Update last position
                //     this._lastRootPos.copyFrom(newPos);
                // }
            } catch (error) {
                console.error("[MediapipeAvatarController] Error updating root position:", error);
            }

        };

        // Add this helper method to get hip visibility
        this.calculateHipVisibility = () => {
            try {
                // If we have pose data with landmarks
                if (this.latestPoseData && this.latestPoseData.rawPose) {
                    // Get hip landmark indices (adjust based on your landmark mapping)
                    const leftHipIndex = 23;  // MediaPipe left hip index
                    const rightHipIndex = 24; // MediaPipe right hip index

                    const leftHipVis = this.latestPoseData.rawPose[leftHipIndex]?.visibility || 0;
                    const rightHipVis = this.latestPoseData.rawPose[rightHipIndex]?.visibility || 0;

                    // Return average visibility of both hips
                    return (leftHipVis + rightHipVis) / 2;
                }
                return this._isPartialVisibility ? 0.4 : 0.8; // Fallback based on partial visibility flag
            } catch (error) {
                console.warn("[MediapipeAvatarController] Error calculating hip visibility:", error);
                return 0.5; // Default mid-value
            }
        };

        this.update = () => {
            if (!this.latestPoseData || Object.keys(this.latestPoseData).length === 0) return;
            try {
                let data = this.latestPoseData;
                const poseQuatArr = data['poseQuatArr'];
                const rootPos = data['rootPos'];
                const leftHandQuatArr = data['leftHandQuatArr'];
                const rightHandQuatArr = data['rightHandQuatArr'];
                if (poseQuatArr) {
                    this.poseBoneManager.updateAvatar();
                }
                // Update root position with proper skinned mesh reference
                // if (this.latestPoseData.rootPos && this.latestPoseData.rootConfidence !== undefined) {
                //     this.updateRootPos(this.latestPoseData.rootPos, this.latestPoseData.rootConfidence);
                // }
            } catch (error) {
                console.error("[MediapipeAvatarController] Error in update:", error);
            }
        };


        // Helper method to validate quaternion array data
        this._validatePoseArray = (poseArray) => {
            if (!Array.isArray(poseArray)) return false;

            // For quaternion arrays, check if we have valid data
            return poseArray.some(item =>
                Array.isArray(item) && item.length === 4 &&
                item.every(val => typeof val === 'number' && !isNaN(val))
            );
        };

        this.updateData = (data) => {
            this.latestPoseData = data;

            // Process data without applying final bone positions
            // For example, calculate rotations, update bone managers, etc.
            if (data.pose) {
                this.poseBoneManager.setFromArray(data.poseQuatArr, 'array');
            }

            if (this.useHand) {
                if (data.leftHand) {
                    this.leftHandBoneManager.setFromArray(data.leftHandQuatArr, 'array');
                }
                if (data.rightHand) {
                    this.rightHandBoneManager.setFromArray(data.rightHandQuatArr, 'array');
                }
            }

            // Track pose visibility information
            this._isPartialVisibility = data._isPartialVisibility || false;
        };



        this.setUseKalmanFilter = (useKalmanFilter) => {
            this.poseBoneManager.setUseKalmanFilter(useKalmanFilter);
            this.leftHandBoneManager.setUseKalmanFilter(useKalmanFilter);
            this.rightHandBoneManager.setUseKalmanFilter(useKalmanFilter);
        };

    }

    // New method to set the bone cache provider
    _setBoneCacheProvider(provider) {
        this._boneCacheProvider = provider;
    }

    // Replace existing getBoneByName method with one that delegates to the provider
    getBoneByName(name) {
        // If we have a provider, delegate to it
        if (this._boneCacheProvider) {
            return this._boneCacheProvider.getBoneByName(name);
        }

        // Fallback to direct skeleton search
        return this.skeleton.getBoneByName(name) ||
            this.skeleton.getBoneByName(`mixamorig:${name}`);
    }

    // New method to get a bone chain from the provider
    getBoneChain(chainName) {
        if (this._boneCacheProvider) {
            return this._boneCacheProvider.getBoneChain(chainName);
        }
        return {};
    }

    // Method to get root bone from cache provider
    getRootBone() {
        if (this._boneCacheProvider) {
            return this._boneCacheProvider.getRootBone();
        }
        return null;
    }

    // Listener method for smoothing config updates
    updateSmoothingConfig(config) {
        // Flag to prevent recursion
        this._updating = true;

        try {
            // Only update if we need to
            const currentSlerpRatio = this.poseBoneManager?.getSlerpRatio();
            const newSlerpRatio = config.baseSlerpRatio;

            if (typeof newSlerpRatio === 'number' &&
                (currentSlerpRatio === undefined || Math.abs(currentSlerpRatio - newSlerpRatio) > 0.001)) {

                // Create quaternion-specific settings to propagate
                const extendedConfig = {
                    ...config,
                    quaternionValidation: true,          // Enable quaternion validation
                    useSafeInterpolation: true,          // Use the enhanced slerp method
                    adaptiveSmoothing: config.adaptiveSmoothing || false,
                    // Add rotation stiffness for quaternion-specific damping
                    rotationStiffness: config.rotationStiffness || 1.0
                };

                // Apply to all bone managers only if the value has changed
                this.poseBoneManager.updateSmoothingConfig(extendedConfig);
                this.leftHandBoneManager.updateSmoothingConfig(extendedConfig);
                this.rightHandBoneManager.updateSmoothingConfig(extendedConfig);
            }
        } finally {
            this._updating = false;
        }
    }

    // Clean up when destroying the controller
    dispose() {
        // Remove reference to bone cache provider
        this._boneCacheProvider = null;

        this.smoothingConfig.removeListener(this);
        // ... any other disposal logic
    }

    validateQuaternion(q, defaultValue = null) {
        // 1. Check if quaternion is defined and all components are valid numbers
        if (!q ||
            typeof q.x !== 'number' || isNaN(q.x) ||
            typeof q.y !== 'number' || isNaN(q.y) ||
            typeof q.z !== 'number' || isNaN(q.z) ||
            typeof q.w !== 'number' || isNaN(q.w)) {

            console.warn("Invalid quaternion detected, using fallback");
            return defaultValue || new Quaternion(0, 0, 0, 1); // Identity quaternion
        }

        // 2. Check if quaternion has near-zero magnitude (degenerate case)
        const magnitudeSq = q.x * q.x + q.y * q.y + q.z * q.z + q.w * q.w;
        if (magnitudeSq < 0.0001) { // Slightly stricter threshold
            console.warn("Near-zero quaternion detected, using fallback");
            return defaultValue || new Quaternion(0, 0, 0, 1);
        }

        // 3. Check for NaN after calculations (can happen with some operations)
        if (isNaN(magnitudeSq)) {
            console.warn("NaN detected in quaternion magnitude calculation");
            return defaultValue || new Quaternion(0, 0, 0, 1);
        }

        // 4. Detect if quaternion is too far from being normalized
        // A valid quaternion should have magnitude close to 1.0
        const magnitudeDiff = Math.abs(1.0 - Math.sqrt(magnitudeSq));
        if (magnitudeDiff > 0.1) { // If more than 10% off from normalized
            console.warn(`Quaternion significantly non-normalized: diff=${magnitudeDiff.toFixed(4)}`);
        }

        // 5. Create normalized copy to prevent drift and modification of original
        const normalized = q.clone();
        normalized.normalize();
        return normalized;
    }

    // Add this method to the MediapipeAvatarController class

    safeSlerpQuaternions(q1, q2, t, fallbackLinear = false) {
        // Validate input quaternions
        const validQ1 = this.validateQuaternion(q1);
        const validQ2 = this.validateQuaternion(q2);

        try {
            // Calculate dot product to determine angle between quaternions
            const dot = validQ1.x * validQ2.x + validQ1.y * validQ2.y +
                validQ1.z * validQ2.z + validQ1.w * validQ2.w;

            // If quaternions are nearly identical or opposite, use linear interpolation
            if (Math.abs(dot) > 0.9999 || fallbackLinear) {
                // Use linear interpolation (LERP) and normalize
                const result = new Quaternion(
                    validQ1.x + t * (validQ2.x - validQ1.x),
                    validQ1.y + t * (validQ2.y - validQ1.y),
                    validQ1.z + t * (validQ2.z - validQ1.z),
                    validQ1.w + t * (validQ2.w - validQ1.w)
                );
                return result.normalize();
            }

            // Standard SLERP implementation with proper shortest-path handling
            // Ensure we take the shortest path
            let adjustedDot = dot;
            let adjustedQ2 = validQ2.clone();

            if (dot < 0) {
                adjustedDot = -dot;
                // Negate Q2 to take shortest path
                adjustedQ2.scaleInPlace(-1);
            }

            // Clamp to valid acos range
            adjustedDot = Math.min(Math.max(adjustedDot, -1), 1);

            const theta = Math.acos(adjustedDot);
            const sinTheta = Math.sin(theta);

            // Handle zero-division case
            if (sinTheta < 0.001) {
                return this.validateQuaternion(validQ1);
            }

            const w1 = Math.sin((1 - t) * theta) / sinTheta;
            const w2 = Math.sin(t * theta) / sinTheta;

            const result = new Quaternion(
                validQ1.x * w1 + adjustedQ2.x * w2,
                validQ1.y * w1 + adjustedQ2.y * w2,
                validQ1.z * w1 + adjustedQ2.z * w2,
                validQ1.w * w1 + adjustedQ2.w * w2
            );

            return this.validateQuaternion(result);
        } catch (error) {
            console.error("Error in quaternion interpolation:", error);
            return this.validateQuaternion(validQ1);
        }
    }

    // Add this method to the MediapipeAvatarController class

    transformQuaternionBetweenCoordinateSystems(sourceQuat, fromSystem, toSystem) {
        // Validate the source quaternion
        const validQuat = this.validateQuaternion(sourceQuat);

        // Skip transformation if coordinate systems are the same
        if (fromSystem === toSystem) {
            return validQuat.clone();
        }

        // Define common coordinate system transforms
        const transforms = {
            // Common rotations between coordinate systems
            'mediapieToBabylon': new Quaternion(0, 1, 0, 0), // 180° around Y
            'babylonToMediapipe': new Quaternion(0, 1, 0, 0), // Same rotation in reverse
            'threejsToBabylon': new Quaternion.FromEulerAngles(0, Math.PI, 0),
            'babylonToThreejs': new Quaternion.FromEulerAngles(0, Math.PI, 0)
            // Add more transforms as needed
        };

        // Get the appropriate transform or use identity
        const transformKey = `${fromSystem}To${toSystem}`;
        const transformQuat = transforms[transformKey] || new Quaternion(0, 0, 0, 1);

        // Apply the transform: result = transform * sourceQuat
        const result = transformQuat.multiply(validQuat);

        return this.validateQuaternion(result);
    }

    // Add to update pipeline before applying rotations
    updateBoneRotations(bones, rotationData) {
        for (const [name, rawQuat] of Object.entries(rotationData)) {
            const bone = this.getBoneByName(name);
            if (!bone) continue;

            // Validate quaternion before applying
            const validQuat = this.validateQuaternion(rawQuat);
            bone.rotationQuaternion = validQuat;
        }
    }

    /**
     * Set Kalman filter parameters for all managed components
     * @param {string} type - Filter type ('position', 'rotation', or 'all')
     * @param {number} processCovariance - Process noise covariance value
     * @param {number} measurementCovariance - Measurement noise covariance value
     */
    setKalmanFilterParams(type, processCovariance, measurementCovariance) {
        try {
            // Store configuration for future reference
            this._kalmanConfig = this._kalmanConfig || {};

            // Apply to appropriate bone managers based on type
            if (type === 'position' || type === 'all') {
                this._kalmanConfig.position = {
                    processCovariance,
                    measurementCovariance
                };
            }

            if (type === 'rotation' || type === 'all') {
                this._kalmanConfig.rotation = {
                    processCovariance,
                    measurementCovariance
                };

                // Apply to all bone managers
                this.poseBoneManager.setKalmanFilterParams(type, processCovariance, measurementCovariance);
                this.leftHandBoneManager.setKalmanFilterParams(type, processCovariance, measurementCovariance);
                this.rightHandBoneManager.setKalmanFilterParams(type, processCovariance, measurementCovariance);
            }

            console.log(`[MediapipeAvatarController] Updated Kalman filter parameters for ${type}: pc=${processCovariance}, mc=${measurementCovariance}`);
        } catch (error) {
            console.error("[MediapipeAvatarController] Error applying Kalman settings:", error);
        }
    }

    /**
     * Initialize Kalman filters with initial bone data and root position
     * @param {Object} initialBoneData - Initial bone data for filter state
     * @param {Array} [initialRootPos] - Initial root position as [x,y,z] array
     */
    initKalmanFilter(initialBoneData, initialRootPos) {
        // Track initialization state
        this._kalmanInitialized = true;

        // Initialize bone managers with initial data only (no configuration)
        this.poseBoneManager.initKalmanFilter(initialBoneData);
        this.leftHandBoneManager.initKalmanFilter(initialBoneData);
        this.rightHandBoneManager.initKalmanFilter(initialBoneData);

        console.log("[MediapipeAvatarController] Initialized Kalman filters with initial data");

        // Return the data used for initialization for potential use by callers
        return {
            initialBoneData,
            initialRootPos
        };
    }

    // Add helper method to get initial root position
    getInitialRootPosition() {
        if (!this.character) return [0, 0, 0];

        try {
            // Try to get root bone position if available
            const rootBone = this.getRootBone();
            if (rootBone) {
                const pos = rootBone.getPosition();
                return [pos.x, pos.y, pos.z];
            }

            // Fallback to character position
            return [
                this.character.position.x,
                this.character.position.y,
                this.character.position.z
            ];
        } catch (error) {
            console.warn("[MediapipeAvatarController] Error getting initial root position:", error);
            return [0, 0, 0];
        }
    }

    // Add helper method to extract initial bone data
    getInitialBoneData() {
        if (!this.character || !this.skeleton) return {};

        try {
            const initialData = {};

            // Extract rotation data from existing bones
            for (const bone of this.skeleton.bones) {
                if (!bone || !bone.name) continue;

                // Clean up bone name to match our naming convention
                const cleanName = bone.name.replace('mixamorig:', '');

                // Get bone's current rotation
                if (bone.rotationQuaternion) {
                    initialData[cleanName] = [
                        bone.rotationQuaternion.x,
                        bone.rotationQuaternion.y,
                        bone.rotationQuaternion.z
                    ];
                }
                if (bone.rotationQuaternion) {
                    initialData[cleanName] = [
                        bone.rotationQuaternion.x,
                        bone.rotationQuaternion.y,
                        bone.rotationQuaternion.z,
                        bone.rotationQuaternion.w
                    ];
                }

                // Add bone position data
                if (bone.position) {
                    initialData[`${cleanName}_pos`] = [
                        bone.position.x,
                        bone.position.y,
                        bone.position.z
                    ];
                }
            }

            return initialData;
        } catch (error) {
            console.warn("[MediapipeAvatarController] Error extracting initial bone data:", error);
            return {};
        }
    }
}

export default MediapipeAvatarController;