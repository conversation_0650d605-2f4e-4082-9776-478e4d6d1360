import <PERSON><PERSON><PERSON>ilter, { <PERSON><PERSON><PERSON><PERSON><PERSON>3<PERSON>, <PERSON><PERSON><PERSON>ilterRotation, create<PERSON><PERSON>man<PERSON>ilt<PERSON> } from "./KalmanFilter.js";
import { PoseElementManager } from "./elementManagers.js";
export { PoseElement, Landmark, VirtualLandmark, Bone, VirtualBone };
import { Vector3, Quaternion, Matrix } from '@babylonjs/core';


Vector3.rightVector = new Vector3(1, 0, 0);
Vector3.upVector = new Vector3(0, 1, 0);
Vector3.FrontVector = new Vector3(0, 0, 1); // Note: Babylon.js Z is backward, adjust if needed

// Add angleTo method to Vector3 prototype for angle calculations
Vector3.prototype.angleTo = function (vector) {
    // Normalize both vectors to get the cosine directly
    const normalizedThis = this.normalizeToNew();
    const normalizedVector = vector.normalizeToNew();

    // Get the dot product
    const dot = Vector3.Dot(normalizedThis, normalizedVector);

    // Clamp to valid range for acos
    const clampedDot = Math.max(Math.min(dot, 1.0), -1.0);

    // Return the angle in radians
    return Math.acos(clampedDot);
};

Vector3.prototype.eliminateComponent = function (vector, target) {
    const coef = Vector3.Dot(vector, target) / Vector3.Dot(target, target);
    this.copyFrom(vector).subtractInPlace(target.scale(coef));
};
/*
Projects argument vectors start, end onto the plane which has this as normal vector
and returns radian angle from start vector to end vector
*/
Vector3.prototype.getProjectedAngle = (function () {
    let cross = new Vector3();
    let projectedStart = new Vector3();
    let projectedEnd = new Vector3();
    return function (start, end) {
        if (!start || !end) return null;
        projectedStart.eliminateComponent(start, this);
        projectedEnd.eliminateComponent(end, this);
        cross = Vector3.Cross(this, projectedStart);

        let projectedAngle = projectedStart.angleTo(projectedEnd);
        if (cross.angleTo(projectedEnd) < Math.PI / 2) {
            projectedAngle = -projectedAngle;
        }
        return projectedAngle;
    };
})();


class PoseElement {
    constructor(manager, { index, name, parentIndex, childrenIndex, counterpartIndex, side }) {
        this.manager = manager;
        this.name = name;
        this.index = index;
        this.parentIndex = parentIndex;
        this.childrenIndex = childrenIndex;
        this.counterpartIndex = counterpartIndex;
        this.side = side;
        this.kalmanFilter = null;
        this.useKalmanFilter = true;

        this._parent = null;
        this._children = null;
        this._counterpart = null;

        this.update = (data) => { }
        this.getChild = (num) => {
            let children = this.children;
            if (children) {
                let child = children.container[num];
                if (child) {
                    return child;
                }
            }
            return null;
        }
        this.getChildByIndex = (index) => {
            let children = this.children;
            if (children) {
                return children.get(index);
            }
            return null;
        }
        this.getChildByName = (name) => {
            let children = this.children;
            if (children) {
                return children.getByName(name);
            }
            return null;
        }

        this.initKalmanFilter = (initialData = null, kalmanConfig = null) => {
            this.kalmanFilter = null;

            // Determine filter type based on element type
            const filterType = this.isBone ? 'rotation' : 'position';

            // Get configuration settings
            const config = kalmanConfig || {
                processCovariance: filterType === 'rotation' ? 0.005 : 0.01,
                measurementCovariance: filterType === 'rotation' ? 0.05 : 0.1
            };

            // For landmarks, don't try to use bone data - just initialize with defaults
            if (this.isLandmark) {
                // For landmarks, initialize with zero or current position if available
                let initialPos = [0, 0, 0];
                if (this._worldPosition) {
                    initialPos = [this._worldPosition.x, this._worldPosition.y, this._worldPosition.z];
                }

                this.kalmanFilter = createKalmanFilter(
                    'position',
                    initialPos,
                    config
                );

                return;
            }

            // For bones, we can use initialBoneData if provided
            if (initialData && Array.isArray(initialData) && initialData.length >= 3) {
                this.kalmanFilter = createKalmanFilter(
                    filterType,
                    initialData,
                    {
                        processCovariance: config.processCovariance,
                        measurementCovariance: config.measurementCovariance
                    }
                );

                // Mark as initialized since we have actual bone data
                if (this.kalmanFilter && 'initialized' in this.kalmanFilter) {
                    this.kalmanFilter.initialized = true;
                }
            } else {
                // Initialize with defaults appropriate for this element type
                const defaultValues = this.isBone ?
                    [0, 0, 0, 1] : // Default quaternion components for bones
                    [0, 0, 0];    // Default position for landmarks

                this.kalmanFilter = createKalmanFilter(
                    filterType,
                    defaultValues.slice(0, 3), // Only use first 3 components
                    config
                );
            }

            // Store initial data and configuration for later use
            this._initialValues = initialData;
            this._kalmanConfig = config;
            this._initialValuesApplied = !!initialData;
        };

        // Add method to update Kalman filter parameters
        this.setKalmanFilterParams = (config) => {
            if (!config) return;

            this._kalmanConfig = {
                ...this._kalmanConfig,
                ...config
            };

            // If filter already exists, update parameters
            if (this.kalmanFilter && this.kalmanFilter.updateParameters) {
                this.kalmanFilter.updateParameters(
                    config.processCovariance,
                    config.measurementCovariance
                );
            }
        };

        this.setUseKalmanFilter = (useKalmanFilter) => {
            this.useKalmanFilter = useKalmanFilter;
        };
        this._updateKalmanFilter = (_x, _y, _z, type = 'position') => {
            if (this.useKalmanFilter === false) {
                return [_x, _y, _z];
            }

            if (this.kalmanFilter === null) {
                // Get configuration (either stored or default)
                const config = this._kalmanConfig || {
                    processCovariance: type === 'rotation' ? 0.005 : 0.01,
                    measurementCovariance: type === 'rotation' ? 0.05 : 0.1
                };

                // Create the appropriate filter based on data type and config
                this.kalmanFilter = createKalmanFilter(
                    type,
                    [_x, _y, _z],
                    config
                );
            } else if (this._initialBoneValues && !this._initialValuesApplied) {
                // Apply initial bone values to existing filter
                if (this.kalmanFilter.reinitialize) {
                    this.kalmanFilter.reinitialize(
                        this._initialBoneValues[0],
                        this._initialBoneValues[1],
                        this._initialBoneValues[2]
                    );
                    this._initialValuesApplied = true;
                }
            }

            // Apply filter and return results
            return this.kalmanFilter.update(_x, _y, _z);
        }
    }

    get parent() {
        if (this.parentIndex === null) this._parent = null;
        else if (this._parent === null) this._parent = this.manager.get(this.parentIndex);
        return this._parent;
    }
    get children() {
        if (this.childrenIndex === null) this._children = null;
        else if (this._children === null) {
            this._children = new PoseElementManager();
            this.childrenIndex.forEach(i => {
                this._children.add(this.manager.get(i));
            });
        }
        return this._children;
    }
    get counterpart() {
        if (this.counterpartIndex === null) this._counterpart = this;
        else if (this._counterpart === null) this._counterpart = this.manager.get(this.counterpartIndex);
        return this._counterpart;
    }
    get isVirtual() { return false; }
    get isBone() { return false; }
    get isLandmark() { return false; }
}
// The process of mapping human pose landmarks to avatar animation parameters requires translation 
// between different skeletal structures and coordinate systems. This translation process, known as retargeting.
class Landmark extends PoseElement {
    constructor(manager, poseElementConfig) {
        super(manager, poseElementConfig);

        this._worldPosition = new Vector3();
        this.visibility = 0;
        this._vectorToCounterpart = new Vector3();
        this._vectorFromParent = new Vector3();

        let landmarkPosition1 = new Vector3();
        let landmarkPosition2 = new Vector3();
        this.lerpLandmarks = (landmark1, landmark2, alpha) => {
            landmarkPosition1.copyFrom(landmark1.worldPosition);
            landmarkPosition2.copyFrom(landmark2.worldPosition);
            landmarkPosition1.scaleInPlace(1 - alpha);
            landmarkPosition2.scaleInPlace(alpha);
            this._worldPosition.copyFrom(landmarkPosition1).addInPlace(landmarkPosition2);
            this.visibility = landmark1.visibility * (1 - alpha) + landmark2.visibility * alpha;
        };

        this.update = (landmarkData) => { };

        this.setFromXYZV = (xyzv) => {
            // Use position-specific filtering for landmarks
            let [x, y, z] = this._updateKalmanFilter(xyzv.x, xyzv.y, xyzv.z, 'position');
            this._worldPosition.set(x, y, z);
            this.visibility = xyzv.visibility;
        };

        this.setFromArray = (array) => {
            // Use position-specific filtering for landmarks
            let [x, y, z] = this._updateKalmanFilter(array[0], array[1], array[2], 'position');
            this._worldPosition.set(x, y, z);
            this.visibility = array[3];
        };

        this.getVectorToChild = (num) => {
            let child = this.getChild(num);
            if (child !== null) {
                return child.vectorFromParent;
            } else {
                return null;
            }
        };
    }

    get worldPosition() { return this._worldPosition; }

    get vectorFromParent() {
        let parent = this.parent;
        if (parent !== null) {
            this._vectorFromParent = this.worldPosition.subtract(parent.worldPosition);
            return this._vectorFromParent;
        } else {
            return null;
        }
    }

    get vectorToCounterpart() {
        let counterpart = this.counterpart;
        if (counterpart !== null) {
            this._vectorToCounterpart = this.worldPosition.subtract(counterpart.worldPosition);
            return this._vectorToCounterpart;
        } else {
            return null;
        }
    }

    get isLandmark() { return true; }

    initKalmanFilter(initialData = null, kalmanConfig = null) {
        this.kalmanFilter = null;

        // For landmarks, initialize with existing position if available
        // or provided initial data, or zeros as last resort
        let initialPos = [0, 0, 0];

        // Use existing position if we have it
        if (this._worldPosition && this._worldPosition.length() > 0.0001) {
            initialPos = [this._worldPosition.x, this._worldPosition.y, this._worldPosition.z];
        }
        // Otherwise use provided initial data if available
        else if (initialData && Array.isArray(initialData) && initialData.length >= 3) {
            initialPos = initialData.slice(0, 3);
        }
        // Otherwise will use zeros (0, 0, 0)

        // Get configuration settings
        const config = kalmanConfig || {
            processCovariance: 0.01,
            measurementCovariance: 0.1
        };

        this.kalmanFilter = createKalmanFilter(
            'position',
            initialPos,
            config
        );

        // Store initial data and configuration for later use
        this._initialValues = initialData || initialPos;
        this._kalmanConfig = config;
    }
}

// Virtual landmarks are not provied by mediapipe but calculated using real landmarks
class VirtualLandmark extends Landmark {
    constructor(manager, poseElementConfig, { startLandmarkIndex, endLandmarkIndex, alpha }) {
        super(manager, poseElementConfig);

        this.startLandmarkIndex = startLandmarkIndex;
        this.endLandmarkIndex = endLandmarkIndex;
        this.alpha = alpha;

        this._startLandmark = null;
        this._endLandmark = null;

        this.update = (data) => {
            if (this.startLandmark && this.endLandmark) {
                this.lerpLandmarks(this.startLandmark, this.endLandmark, this.alpha);
            }
        };
    }
    get startLandmark() {
        if (this.startLandmarkIndex === null) this._startLandmark = null;
        else if (this._startLandmark === null) {
            this._startLandmark = this.manager.get(this.startLandmarkIndex);
        }
        return this._startLandmark;
    };
    get endLandmark() {
        if (this.endLandmarkIndex === null) this._endLandmark = null;
        else if (this._endLandmark === null) {
            this._endLandmark = this.manager.get(this.endLandmarkIndex);
        }
        return this._endLandmark;
    }
    get isVirtual() { return true; }
}

class Bone extends PoseElement {
    constructor(manager, poseElementConfig, otherName) {
        super(manager, poseElementConfig);
        this.otherName = otherName;
        //this.visibility = 1;
        this._worldQuaternion = new Quaternion();

        this.setFromMat = (mat) => {
            this._worldQuaternion.setFromRotationMatrix(mat);
        };
        this.setFromArray = (array) => {
            this._worldQuaternion.fromArray(array);
        };
        this.update = (boneData) => { };
        this.updateVisibility = () => { };
    }
    get isBone() { return true; }
    get worldQuaternion() { return this._worldQuaternion; }
}


class VirtualBone extends Bone {

    constructor(manager, poseElementConfig, { startLandmarkIndex, endLandmarkIndex }, otherName) {
        super(manager, poseElementConfig, otherName);

        this.startLandmarkIndex = startLandmarkIndex;
        this.endLandmarkIndex = endLandmarkIndex;
        this._startLandmark = null;
        this._endLandmark = null;
        this._xDirection = new Vector3(1, 0, 0);
        this._yDirection = new Vector3(0, 1, 0);
        this._zDirection = new Vector3(0, 0, 1);
        this._rotationQuaternion = new Quaternion();
        // this.visibility = 1;
        this.visibility = 0;

        // Enhanced smoothing parameters
        this._smoothingParams = {
            rotationStiffness: 0.8,      // Default stiffness for rotations
            lastValidQuaternion: null,   // Store last valid quaternion for interpolation
            confidenceWeight: 1.0        // Weight based on confidence/visibility
        };

        this.update = () => {
            let parent = this.parent;
            if (parent) {
                this.updateYDirection();
                this.updateZDirection(parent.zDirection);
            } else {
                this.updateYDirection();
                this.updateZDirection(this._zDirection);
            }
        };

        this.updateVisibility = () => {
            let count = 0;
            let visibility = 0;
            if (this.startLandmark) {
                visibility += this.startLandmark.visibility;
                count += 1;
            }
            if (this.endLandmark) {
                visibility += this.endLandmark.visibility;
                count += 1;
            }
            this.visibility = (count > 0) ? visibility / count : 0;
        };

        this.updateYDirection = (yDirection) => {
            if (!yDirection) {
                let startLandmark = this.startLandmark;
                let endLandmark = this.endLandmark;
                if (startLandmark && endLandmark) {
                    this._yDirection = startLandmark.worldPosition.subtract(endLandmark.worldPosition);
                } else {
                    return false;
                }
            } else {
                this._yDirection.copyFrom(yDirection);
            }

            updateWorldQuaternion();
            return true;
        };

        this.updateZDirection = (zDirection) => {
            if (!zDirection) {
                return false;
            } else {
                this._zDirection.copyFrom(zDirection);
            }

            updateWorldQuaternion();
            return true;
        };

        let updateWorldQuaternion = () => {
            // Add input validation diagnostics
            // console.log(`[VirtualBone:${this.name}] Updating quaternion - yDir length: ${this._yDirection?.length()}, zDir length: ${this._zDirection?.length()}`);

            // First ensure orthonormal basis (like THREE.js does)
            if (this._yDirection.length() < 0.0001) {
                // Invalid direction vector, skip update
                console.warn(`[VirtualBone:${this.name}] Invalid yDirection vector with near-zero length`);
                return;
            }

            // Normalize the Y direction vector
            this._yDirection.normalize();

            // Check if zDirection is valid
            if (!this._zDirection || this._zDirection.length() < 0.0001) {
                // Create a default zDirection if none exists
                console.warn(`[VirtualBone:${this.name}] Invalid zDirection, creating default`);
                this._zDirection = new Vector3(0, 0, 1);
            }

            // Ensure z-direction is perpendicular to y-direction
            this._zDirection.eliminateComponent(this._zDirection, this._yDirection);
            if (this._zDirection.length() < 0.0001) {
                // If zDirection became zero after elimination, create a perpendicular vector
                console.warn(`[VirtualBone:${this.name}] zDirection became zero after perpendicular adjustment, creating new perpendicular`);
                if (Math.abs(this._yDirection.y) < 0.9) {
                    this._zDirection.set(0, 1, 0); // Use up vector
                } else {
                    this._zDirection.set(1, 0, 0); // Use right vector
                }
                this._zDirection.eliminateComponent(this._zDirection, this._yDirection);
            }

            // Normalize z-direction
            this._zDirection.normalize();

            // Compute x-direction as cross product of y and z
            this._xDirection = Vector3.Cross(this._yDirection, this._zDirection);

            // OPTIMIZATION: Create quaternion directly from orthonormal basis
            // Create a rotation matrix from the orthonormal basis vectors
            const rotMatrix = new Matrix();
            Matrix.FromXYZAxesToRef(
                this._xDirection,
                this._yDirection,
                this._zDirection,
                rotMatrix
            );

            // Create quaternion directly from the rotation matrix
            const newQuaternion = Quaternion.FromRotationMatrix(rotMatrix);

            // Log the calculated quaternion
            // console.log(`[VirtualBone:${this.name}] New quaternion: [${newQuaternion.x.toFixed(3)}, ${newQuaternion.y.toFixed(3)}, ${newQuaternion.z.toFixed(3)}, ${newQuaternion.w.toFixed(3)}]`);

            // Apply Kalman filtering to the quaternion components
            // Note: While we ideally would filter the quaternion directly,
            // we'll use our existing filter which expects Euler-like values
            let filteredQuaternion;

            if (this.useKalmanFilter && this.kalmanFilter) {
                // Extract quaternion components for filtering
                const [x, y, z, w] = [newQuaternion.x, newQuaternion.y, newQuaternion.z, newQuaternion.w];

                // Filter the x, y, z components (treating them as a vector)
                // This is a simplification - ideally we'd have a quaternion-specific filter
                let [fx, fy, fz] = this._updateKalmanFilter(x, y, z, 'rotation');

                // Log filtered components
                // console.log(`[VirtualBone:${this.name}] Filtered components: [${fx.toFixed(3)}, ${fy.toFixed(3)}, ${fz.toFixed(3)}]`);

                // Reconstruct quaternion, maintaining unit length
                // We need to reconstruct w to ensure a unit quaternion
                const fw = Math.sqrt(1 - (fx * fx + fy * fy + fz * fz));
                filteredQuaternion = new Quaternion(fx, fy, fz, isNaN(fw) ? w : fw);

                // Ensure we have a valid quaternion
                if (isNaN(filteredQuaternion.x) || filteredQuaternion.lengthSquared() < 0.1) {
                    console.warn(`[VirtualBone:${this.name}] Invalid filtered quaternion, using unfiltered`);
                    filteredQuaternion = newQuaternion.clone();
                }
            } else {
                // Use the unfiltered quaternion if filtering is disabled
                filteredQuaternion = newQuaternion.clone();
            }

            // Store this as our last valid quaternion
            if (!this._smoothingParams.lastValidQuaternion) {
                this._smoothingParams.lastValidQuaternion = new Quaternion();
            }
            this._smoothingParams.lastValidQuaternion.copyFrom(filteredQuaternion);

            // Apply stiffness-based interpolation between current and new quaternion
            if (this._worldQuaternion.lengthSquared() > 0.1) {
                // Get stiffness either from the bone manager or use default
                const stiffness = this.manager?.smoothingConfig?.rotationStiffness ||
                    this._smoothingParams.rotationStiffness;

                // Use the visibility as a confidence weight, or default to 1.0
                const confidenceWeight = Math.max(0.2, this.visibility || 0.8);

                // Calculate adaptive blending factor
                const blendFactor = stiffness * confidenceWeight;

                // Log smoothing parameters
                // console.log(`[VirtualBone:${this.name}] Smoothing with stiffness=${stiffness.toFixed(2)}, confidence=${confidenceWeight.toFixed(2)}, blend=${blendFactor.toFixed(2)}`);

                // Apply spherical interpolation
                this._worldQuaternion = Quaternion.Slerp(
                    this._worldQuaternion,
                    filteredQuaternion,
                    blendFactor
                );

                // Log final quaternion after smoothing
                // console.log(`[VirtualBone:${this.name}] Final quat: [${this._worldQuaternion.x.toFixed(3)}, ${this._worldQuaternion.y.toFixed(3)}, ${this._worldQuaternion.z.toFixed(3)}, ${this._worldQuaternion.w.toFixed(3)}]`);
            } else {
                // First initialization or invalid current quaternion
                // console.log(`[VirtualBone:${this.name}] Initial quaternion setup (no smoothing)`);
                this._worldQuaternion.copyFrom(filteredQuaternion);
            }
        }

        // Add method to update smoothing parameters
        this.setSmoothingParams = (params) => {
            if (!params) return;

            if (typeof params.rotationStiffness === 'number') {
                this._smoothingParams.rotationStiffness = params.rotationStiffness;
            }

            if (typeof params.confidenceWeight === 'number') {
                this._smoothingParams.confidenceWeight = params.confidenceWeight;
            }
        };
    }
    get xDirection() { return this._xDirection; }
    get yDirection() { return this._yDirection; }
    get zDirection() { return this._zDirection; }
    get startLandmark() {
        let landmarkManager = this.manager && this.manager.landmarkManager;

        if (this.startLandmarkIndex === null) this._startLandmark = null;
        else if (this._startLandmark === null && landmarkManager) {
            this._startLandmark = landmarkManager.get(this.startLandmarkIndex);
        }
        return this._startLandmark;
    };
    get endLandmark() {
        let landmarkManager = this.manager && this.manager.landmarkManager;
        if (this.endLandmarkIndex === null) this._endLandmark = null;
        else if (this._endLandmark === null && landmarkManager) {
            this._endLandmark = landmarkManager.get(this.endLandmarkIndex);
        }
        return this._endLandmark;
    }
    get isVirtual() { return true; }
    get length() {
        return this.endLandmark.vectorFromParent.length();
    }
}