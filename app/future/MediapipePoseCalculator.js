import {
    MEDIAPIPE_LANDMARKS_CONFIG,
    MEDIAPIPE_VIRTUAL_BONES_CONFIG,
    MEDIAPIPE_LEFT_HAND_LANDMARKS_CONFIG,
    MEDIAPIPE_RIGHT_HAND_LANDMARKS_CONFIG,
    MEDIAPIPE_LEFT_HAND_BONES_CONFIG,
    ME<PERSON><PERSON><PERSON>E_RIGHT_HAND_BONES_CONFIG,
    POSEUPDATE_THRESHOLD,
    VISIBILITY_THRESHOLD,
    IKBONE_NAMES,
    BONE_CHAINS,
    HAND_CHAINS,
    THRESHOLD_CRITICAL,
    THRESHOLD_SECONDARY,
    THRESHOLD_STANDARD

} from '../configs/MediapipeConfig.js';
import { PoseElementManager, VirtualBoneManager } from '../characters/elementManagers.js';
import { Vector3, Quaternion } from '@babylonjs/core';
import SmoothingConfig from '../utils/SmoothingConfig.js';
import { create<PERSON><PERSON>manFilter } from './KalmanFilter.js';

class MediapipePoseCalculator {
    constructor(use_inverse_kinematics = false, manager = null) {
        this.use_inverse_kinematics = use_inverse_kinematics;
        this.manager = manager; // Store reference to cache provider
        // Reference to global smoothing config
        this.smoothingConfig = SmoothingConfig;

        // Register as a listener for smoothing config changes
        this.smoothingConfig.addListener(this);

        // Local copy of smoothing parameters
        this._smoothingParams = { ...this.smoothingConfig.getConfig() };

        let leftHandLandmarkManager = this.leftHandLandmarkManager = new PoseElementManager();
        leftHandLandmarkManager.configure(MEDIAPIPE_LEFT_HAND_LANDMARKS_CONFIG);
        let leftHandBoneManager = this.leftHandBoneManager = new VirtualBoneManager(leftHandLandmarkManager);
        leftHandBoneManager.configure(MEDIAPIPE_LEFT_HAND_BONES_CONFIG);

        let rightHandLandmarkManager = this.rightHandLandmarkManager = new PoseElementManager();
        rightHandLandmarkManager.configure(MEDIAPIPE_RIGHT_HAND_LANDMARKS_CONFIG);
        let rightHandBoneManager = this.rightHandBoneManager = new VirtualBoneManager(rightHandLandmarkManager);
        rightHandBoneManager.configure(MEDIAPIPE_RIGHT_HAND_BONES_CONFIG);

        let poseLandmarkManager = this.poseLandmarkManager = new PoseElementManager();
        poseLandmarkManager.configure(MEDIAPIPE_LANDMARKS_CONFIG);
        let poseBoneManager = this.poseBoneManager = new VirtualBoneManager(poseLandmarkManager);
        poseBoneManager.configure(MEDIAPIPE_VIRTUAL_BONES_CONFIG);
        // Create bone config lookup maps for efficient access
        this._boneConfigByRPM = {};

        // Populate lookup maps
        MEDIAPIPE_VIRTUAL_BONES_CONFIG.forEach(config => {
            // Map by RPM name if available
            if (config.otherName && config.otherName.RPM) {
                this._boneConfigByRPM[config.otherName.RPM] = config;
            }
        });
        this.useHand = true;

        this._kalmanInitialized = false;

        this.initKalmanFilter = (initialBoneData = null, initialRootPos = null) => {
            // Initialize only with state data, not parameters

            // Extract landmark positions from initialBoneData
            const landmarkInitialData = {};

            // Capture position data for landmarks
            if (initialBoneData) {
                // Process landmark positions from bone data
                Object.keys(initialBoneData).forEach(key => {
                    // Check for explicit position data (_pos suffix)
                    if (key.endsWith('_pos')) {
                        landmarkInitialData[key.replace('_pos', '')] = initialBoneData[key];
                    }
                    // Also look for position data in the raw bone data
                    // This helps when position data wasn't explicitly stored with _pos suffix
                    else if (Array.isArray(initialBoneData[key]) &&
                        initialBoneData[key].length >= 3) {
                        // Create position data from the first 3 values
                        landmarkInitialData[key] = initialBoneData[key].slice(0, 3);
                    }
                });


                console.log("[MediapipePoseCalculator] Extracted landmark data:",
                    Object.keys(landmarkInitialData).length,
                    "landmarks from bone data");
            }

            // Flag to track if we should delay initialization until first detection
            const shouldDelayInitialization =
                Object.keys(landmarkInitialData).length === 0 && !initialRootPos;
            if (shouldDelayInitialization) {
                console.log("[MediapipePoseCalculator] Delaying Kalman initialization until first detection");
                // Set a flag to initialize on first update
                this._pendingInitialization = true;
            } else {
                // Initialize landmark managers with whatever data we have
                this.poseLandmarkManager.initKalmanFilter(landmarkInitialData);
                this.leftHandLandmarkManager.initKalmanFilter(landmarkInitialData);
                this.rightHandLandmarkManager.initKalmanFilter(landmarkInitialData);

                // Initialize bone managers (these usually have better initialization data)
                this.poseBoneManager.initKalmanFilter(initialBoneData);
                this.leftHandBoneManager.initKalmanFilter(initialBoneData);
                this.rightHandBoneManager.initKalmanFilter(initialBoneData);

                // Initialize root position Kalman filter with Babylon.js coordinates
                if (initialRootPos) {
                    this.rootPosKalmanFilter = createKalmanFilter('position', initialRootPos);
                } else {
                    // Fallback to a default position if initialRootPos is not provided
                    const defaultPos = this.manager?.character?.position || [0, 0, 0];
                    this.rootPosKalmanFilter = createKalmanFilter('position', [
                        defaultPos.x || 0,
                        defaultPos.y || 0,
                        defaultPos.z || 0
                    ]);
                }

                // Mark initialization as complete
                this._kalmanInitialized = true;
                this._pendingInitialization = false;
            }
        };


        this._updateKalmanParameters = () => {
            // Update parameters without reinitializing filters
            // Implementation depends on your Kalman filter implementation
        }

        this.setUseKalmanFilter = (useKalmanFilter) => {
            this.poseLandmarkManager.setUseKalmanFilter(useKalmanFilter);
            this.leftHandBoneManager.setUseKalmanFilter(useKalmanFilter);
            this.rightHandLandmarkManager.setUseKalmanFilter(useKalmanFilter);
            this.poseBoneManager.setUseKalmanFilter(useKalmanFilter);
            this.leftHandBoneManager.setUseKalmanFilter(useKalmanFilter);
            this.rightHandBoneManager.setUseKalmanFilter(useKalmanFilter);
        }

        this.setUseHand = (useHand) => {
            this.useHand = useHand;
        };

        let previousPoseData = null;

        // Global smoothing configuration
        this.smoothingConfig = {
            baseSlerpRatio: 0.8,        // Base interpolation ratio
            positionStiffness: 0.8,      // Position stiffness (1.0 = follow exactly, 0.0 = don't move)
            rotationStiffness: 0.8,      // Rotation stiffness
            visibilityFactor: true,      // Whether to use visibility for weighting
            confidenceThreshold: 0.5     // Minimum visibility for high confidence
        };
        this.rootPosKalmanFilter = null;
        // Method to configure smoothing parameters
        this.setSmoothingConfig = (config) => {
            if (!config) return;

            // Update local configuration
            if (typeof config.baseSlerpRatio === 'number')
                this.smoothingConfig.baseSlerpRatio = config.baseSlerpRatio;

            if (typeof config.positionStiffness === 'number')
                this.smoothingConfig.positionStiffness = config.positionStiffness;

            if (typeof config.rotationStiffness === 'number')
                this.smoothingConfig.rotationStiffness = config.rotationStiffness;

            if (typeof config.visibilityFactor !== 'undefined')
                this.smoothingConfig.visibilityFactor = config.visibilityFactor;

            if (typeof config.confidenceThreshold === 'number')
                this.smoothingConfig.confidenceThreshold = config.confidenceThreshold;

            // Propagate to bone managers
            this.poseBoneManager.setSmoothingConfig(this.smoothingConfig);
            this.leftHandBoneManager.setSmoothingConfig(this.smoothingConfig);
            this.rightHandBoneManager.setSmoothingConfig(this.smoothingConfig);
        };
        // Get bone by name with cache support
        this.getBoneByName = (name) => {
            // Try to get from cache provider first if available
            if (this.manager) {
                const cachedBone = this.manager.getBoneByName(name);
                if (cachedBone) return cachedBone;
            }
            console.warn(`[MediapipePoseCalculator] Bone ${name} not found in cache, falling back to original lookup`);
            // Fall back to original lookup method
            return this.poseBoneManager.getByName(name);
        };

        // Get an entire bone chain directly from cache
        this.getBoneChain = (chainName) => {
            if (this.manager) {
                return this.manager.getBoneChain(chainName);
            }
            console.warn(`[MediapipePoseCalculator] Bone chain ${chainName} not found in cache`);
            return null;
        };
        this.update = (data) => {
            const poseData = data['poseWorldLandmarks'];
            // MediaPipe uses a right-handed system with X-right, Y-up, Z-forward.
            // Babylon.js uses a left-handed system with Y-up, Z-forward, X-left.
            // poseData.forEach(d => { d.x = -d.x; }); // Coordinate flip
            const results = {};

            // Handle delayed initialization if needed
            if (this._pendingInitialization && poseData && poseData.length > 0) {
                console.log("[MediapipePoseCalculator] Initializing Kalman filters from first detection");

                // Extract position data from first detection
                const initialLandmarkData = {};
                poseData.forEach((landmark, index) => {
                    if (landmark && typeof landmark.x === 'number' &&
                        typeof landmark.y === 'number' &&
                        typeof landmark.z === 'number') {

                        initialLandmarkData[`landmark_${index}`] = [
                            landmark.x, landmark.y, landmark.z
                        ];
                    }
                });

                // Initialize with extracted data
                this.poseLandmarkManager.initKalmanFilter(initialLandmarkData);
                this.leftHandLandmarkManager.initKalmanFilter(initialLandmarkData);
                this.rightHandLandmarkManager.initKalmanFilter(initialLandmarkData);

                // Mark initialization as complete
                this._kalmanInitialized = true;
                this._pendingInitialization = false;

                console.log("[MediapipePoseCalculator] Kalman filters initialized from first detection");
            }

            if (poseData && previousPoseData) {
                const movement = this.calculateMovement(poseData, previousPoseData);
                if (movement < POSEUPDATE_THRESHOLD) {
                    // console.log("[MediapipePoseCalculator] Movement below threshold, skipping update:", movement);
                    return results; // Skip update, return existing results
                }
            }
            previousPoseData = poseData ? [...poseData] : null;
            if (poseData) {
                // Apply global smoothing configuration before updating pose
                const shouldApplyStrongSmoothing = this.shouldApplyStrongFilter(this.calculateMovement(poseData, previousPoseData));

                // Apply stronger smoothing for small movements
                if (shouldApplyStrongSmoothing) {
                    // Temporarily reduce stiffness for smoother motion on subtle movements
                    const originalPositionStiffness = this.smoothingConfig.positionStiffness;
                    const originalRotationStiffness = this.smoothingConfig.rotationStiffness;

                    this.smoothingConfig.positionStiffness *= 0.7;  // 70% of normal stiffness
                    this.smoothingConfig.rotationStiffness *= 0.7;  // 70% of normal stiffness

                    // Apply updated smoothing config
                    this.setSmoothingConfig(this.smoothingConfig);

                    // Get pose data with enhanced smoothing
                    results['poseQuatArr'] = this.updatePose(poseData, 'xyzv');

                    // Restore original stiffness
                    this.smoothingConfig.positionStiffness = originalPositionStiffness;
                    this.smoothingConfig.rotationStiffness = originalRotationStiffness;
                    this.setSmoothingConfig(this.smoothingConfig);
                } else {
                    // Normal update with current smoothing settings
                    results['poseQuatArr'] = this.updatePose(poseData, 'xyzv');
                }

                // Check generated quaternions for diagnosis
                if (results['poseQuatArr']) {
                    // const validQuats = results['poseQuatArr'].filter(q =>
                    //     q && (Math.abs(q[0]) > 0.001 || Math.abs(q[1]) > 0.001 ||
                    //         Math.abs(q[2]) > 0.001 || Math.abs(q[3] - 1) > 0.001));

                    // console.log("[MediapipePoseCalculator] Generated quaternions:", {
                    //     total: results['poseQuatArr'].length,
                    //     nonIdentity: validQuats.length,
                    //     firstFewQuats: results['poseQuatArr'].slice(0, 3)
                    // });
                }

                // const rootData = this.getWorldRootPos();
                // results['rootPos'] = rootData.position;
                // results['rootConfidence'] = rootData.confidence;
                // Add key landmark positions for IK
                // Adds leftWristPos, rightWristPos, leftAnklePos, and rightAnklePos to the results for IK targets.
                if (this.use_inverse_kinematics) {
                    // Get key landmark positions for IK targets
                    results['leftWristPos'] = this.getLandmarkPosition('left_wrist');
                    results['rightWristPos'] = this.getLandmarkPosition('right_wrist');
                    results['leftAnklePos'] = this.getLandmarkPosition('left_ankle');
                    results['rightAnklePos'] = this.getLandmarkPosition('right_ankle');
                }
            }
            if (this.useHand) {
                const leftHandData = data['leftHandLandmarks'];
                const rightHandData = data['rightHandLandmarks'];
                if (leftHandData) {
                    // leftHandData.forEach(d => { d.x = -d.x; });
                    results['leftHandQuatArr'] = this.updateHand(leftHandData, 'xyzv', true);
                }
                if (rightHandData) {
                    // rightHandData.forEach(d => { d.x = -d.x; });
                    results['rightHandQuatArr'] = this.updateHand(rightHandData, 'xyzv', false);
                }
            }

            return results;
        };

        this.calculateMovement = (current, previous) => {
            let totalMovement = 0;
            // Calculate the filtered movement to have smoother thresholding
            current.forEach((landmark, i) => {
                if (!previous[i]) return;

                const dx = landmark.x - previous[i].x;
                const dy = landmark.y - previous[i].y;
                const dz = landmark.z - previous[i].z;

                // Weight each dimension differently based on typical movement patterns
                // Y (up/down) and Z (forward/back) movement is more significant than X (side-to-side)
                const weightedDist = dx * dx * 0.8 + dy * dy * 1.2 + dz * dz * 1.0;
                totalMovement += weightedDist;
            });

            return totalMovement;
        };
        this.getLandmarkPosition = (name) => {
            const landmark = this.poseLandmarkManager.getByName(name);
            if (!landmark) return null;

            // Get the raw MediaPipe position
            const mediapipePosition = [landmark.worldPosition.x, landmark.worldPosition.y, landmark.worldPosition.z];
            // Transform to Babylon world coordinates
            let position = mediapipePosition;

            return {
                position: position,
                visibility: landmark.visibility || 0,
            };
        };

        this.computeBoneVisibility = (boneName) => {
            // Direct lookup instead of find()
            let boneConfig = this._boneConfigByRPM[boneName];

            if (!boneConfig) return 0;

            const { startLandmarkIndex, endLandmarkIndex } = boneConfig.virtualBoneConfig;
            let totalVisibility = 0;
            let landmarkCount = 0;

            // Get visibility from start landmark if it exists
            if (startLandmarkIndex !== null) {
                const startLandmark = this.poseLandmarkManager.getByIndex(startLandmarkIndex);
                if (startLandmark) {
                    totalVisibility += startLandmark.visibility || 0;
                    landmarkCount++;
                }
            }

            // Get visibility from end landmark if it exists
            if (endLandmarkIndex !== null) {
                const endLandmark = this.poseLandmarkManager.getByIndex(endLandmarkIndex);
                if (endLandmark) {
                    totalVisibility += endLandmark.visibility || 0;
                    landmarkCount++;
                }
            }

            // If no landmarks were found, return 0
            if (landmarkCount === 0) return 0;

            // Return average visibility
            return totalVisibility / landmarkCount;
        }
        this.updatePose = (poseData, dtype) => {
            if (!poseData) return [];

            try {
                // Log input data sample for diagnostics
                // console.log("[MediapipePoseCalculator] UpdatePose input sample:",
                //     Array.isArray(poseData[0])
                //         ? poseData[0].slice(0, 3).map(d => ({ x: d.x, y: d.y, z: d.z, vis: d.visibility }))
                //         : poseData.slice(0, 3).map(d => ({ x: d.x, y: d.y, z: d.z, vis: d.visibility })));

                // Update pose landmarks from the data
                this.poseLandmarkManager.setFromArray(Array.isArray(poseData[0]) ? poseData[0] : poseData, dtype);

                // // Track which bone chains have sufficient visibility
                // const visibleChains = new Set();

                // // Process bone chains with more flexible visibility requirements
                // Object.entries(BONE_CHAINS).forEach(([chainName, boneNames]) => {
                //     // First try to get entire chain from cache
                //     const bones = [];

                //     // Use cached bone chain if available
                //     const cachedChain = this.getBoneChain(chainName);
                //     // console.log("[MediapipePoseCalculator] Cached chain:", chainName, cachedChain);
                //     if (cachedChain && Object.keys(cachedChain).length > 0) {
                //         // Convert cached chain object to array of bones
                //         Object.values(cachedChain).forEach(bone => {
                //             if (bone) bones.push(bone);
                //         });
                //     } else {
                //         // Fall back to individual bone lookup
                //         for (const name of boneNames) {
                //             const bone = this.getBoneByName(name);
                //             if (bone !== null) {
                //                 bones.push(bone);
                //             }
                //         }
                //     }

                //     if (bones.length === 0) return; // Skip empty chains

                //     // Choose threshold based on chain importance
                //     let thresholdToUse = THRESHOLD_STANDARD;
                //     if (chainName.includes('Spine') || chainName.includes('Neck')) {
                //         thresholdToUse = THRESHOLD_CRITICAL;
                //     } else if (chainName.includes('Hand') || chainName.includes('Foot')) {
                //         thresholdToUse = THRESHOLD_SECONDARY;
                //     }

                //     // Calculate chain's best and average visibility
                //     let bestVisibility = 0;
                //     let totalVisibility = 0;

                //     // Optimize visibility calculation using cache provider
                //     for (const bone of bones) {
                //         // console.log(bone.name.replace('mixamorig:', ''));
                //         const landmark_name = bone.name.replace('mixamorig:', '');
                //         // console.log(boneLandmark);
                //         // Get visibility from cache provider if available, otherwise fallback to direct access
                //         const visibility = this._boneCacheProvider ? 
                //             this._boneCacheProvider.getBoneVisibility(landmark_name) : 
                //             (bone.visibility || 0);

                //         // Update the cache if we're using a provider
                //         if (this._boneCacheProvider) {
                //             // console.log("[MediapipePoseCalculator] Updating visibility in cache for bone:", landmark_name);
                //             this._boneCacheProvider.updateBoneVisibility(landmark_name, bone.visibility);
                //         }

                //         totalVisibility += visibility;
                //         if (visibility > bestVisibility) {
                //             bestVisibility = visibility;
                //         }
                //     }

                //     const avgVisibility = totalVisibility / bones.length;

                //     // Log chain visibility for debugging
                //     console.log(`[MediapipePoseCalculator] Chain ${chainName}: bestVis=${bestVisibility.toFixed(2)}, avgVis=${avgVisibility.toFixed(2)}, threshold=${thresholdToUse}`);

                //     // Store visibility data for dynamic adaptation
                //     for (const bone of bones) {
                //         bone._chainAvgVisibility = avgVisibility;
                //         bone._chainBestVisibility = bestVisibility;
                //     }

                //     // Determine if chain should be updated
                //     if (bestVisibility >= thresholdToUse) {
                //         visibleChains.add(chainName);

                //         // Check if any bones in this chain are IK bones
                //         let hasIKBones = false;
                //         for (const bone of bones) {
                //             if (IKBONE_NAMES.includes(bone.name)) {
                //                 hasIKBones = true;
                //                 break;
                //             }
                //         }

                //         const shouldUpdate = !this.use_inverse_kinematics || !hasIKBones;

                //         if (shouldUpdate) {
                //             // Update bones with visibility-aware calculations
                //             for (const bone of bones) {
                //                 try {
                //                     // IMPORTANT: Only calculate the bone orientations here
                //                     // but don't apply them to the actual skeleton
                //                     bone.update();

                //                     // Ensure the worldQuaternion is valid after update
                //                     if (!bone.worldQuaternion || isNaN(bone.worldQuaternion.x)) {
                //                         console.warn(`Invalid quaternion for bone: ${bone.name}`);
                //                         // Reset to identity quaternion if invalid
                //                         bone._worldQuaternion = new Quaternion();
                //                     }
                //                 } catch (err) {
                //                     console.error(`Error updating bone ${bone.name}:`, err);
                //                     // Reset to identity quaternion on error
                //                     bone._worldQuaternion = new Quaternion();
                //                 }
                //             }
                //         }
                //     } else if (this._lastActiveChains && this._lastActiveChains.has(chainName)) {
                //         // Chain was active before but now invisible - maintain last position
                //         console.log(`[MediapipePoseCalculator] Chain ${chainName}: Using previous pose (insufficient visibility)`);
                //         for (const bone of bones) {
                //             // Ensure quaternion exists
                //             if (!bone._worldQuaternion) {
                //                 bone._worldQuaternion = new Quaternion();
                //             }
                //         }
                //     }
                // });

                // // Store active chains for next frame
                // this._lastActiveChains = visibleChains;
                // console.log("[MediapipePoseCalculator] Active chains:", Array.from(visibleChains));

                // Always update visibility for proper blending
                this.poseBoneManager.update();
                this.poseBoneManager.updateVisibility();

                // Ensure all bones have valid quaternions before getting the array
                this.poseBoneManager.validateQuaternions();

                return this.poseBoneManager.getWorldQuaternionArray();
            } catch (error) {
                console.error("[MediapipePoseCalculator] Error in updatePose:", error);
                return [];
            }
        };


        this.getWorldRootPos = () => {

            const threshold = VISIBILITY_THRESHOLD; // Minimum visibility threshold for reliability
            const leftHip = this.getLandmarkPosition('left_hip');   // Index 23
            const rightHip = this.getLandmarkPosition('right_hip'); // Index 24
            const leftShoulder = this.getLandmarkPosition('left_shoulder'); // Index 11
            const rightShoulder = this.getLandmarkPosition('right_shoulder'); // Index 12

            let position = null;
            let confidence = 0;
            let caseUsed = 0;

            // Case 1: Both hips are visible and reliable
            if (leftHip && rightHip && leftHip.visibility > threshold && rightHip.visibility > threshold) {
                position = [
                    (leftHip.position[0] + rightHip.position[0]) / 2,
                    (leftHip.position[1] + rightHip.position[1]) / 2,
                    (leftHip.position[2] + rightHip.position[2]) / 2
                ];
                confidence = (leftHip.visibility + rightHip.visibility) / 2; // High confidence
                caseUsed = 1;
            }
            // Case 2: Only left hip is visible
            else if (leftHip && leftHip.visibility > threshold) {
                const assumedRightHip = [-leftHip.position[0], leftHip.position[1], leftHip.position[2]]; // Symmetry assumption
                position = [
                    (leftHip.position[0] + assumedRightHip[0]) / 2,
                    (leftHip.position[1] + assumedRightHip[1]) / 2,
                    (leftHip.position[2] + assumedRightHip[2]) / 2
                ];
                confidence = leftHip.visibility * 0.8; // Slightly reduced confidence
                caseUsed = 2;
            }
            // Case 3: Only right hip is visible
            else if (rightHip && rightHip.visibility > threshold) {
                const assumedLeftHip = [-rightHip.position[0], rightHip.position[1], rightHip.position[2]];
                position = [
                    (assumedLeftHip[0] + rightHip.position[0]) / 2,
                    (assumedLeftHip[1] + rightHip.position[1]) / 2,
                    (assumedLeftHip[2] + rightHip.position[2]) / 2
                ];
                confidence = rightHip.visibility * 0.8;
                caseUsed = 3;
            }
            // Case 4: Both shoulders are visible (fallback)
            else if (leftShoulder && rightShoulder && leftShoulder.visibility > threshold && rightShoulder.visibility > threshold) {
                const shoulderCenter = [
                    (leftShoulder.position[0] + rightShoulder.position[0]) / 2,
                    (leftShoulder.position[1] + rightShoulder.position[1]) / 2,
                    (leftShoulder.position[2] + rightShoulder.position[2]) / 2
                ];
                // Estimate hips ~0.4 units below shoulders (adjustable based on model scale)
                position = [shoulderCenter[0], shoulderCenter[1] - 0.4, shoulderCenter[2]];
                confidence = (leftShoulder.visibility + rightShoulder.visibility) / 2 * 0.7; // Lower confidence
                caseUsed = 4;
            }
            // Case 5: Only left shoulder is visible
            else if (leftShoulder && leftShoulder.visibility > threshold) {
                position = [leftShoulder.position[0], leftShoulder.position[1] - 0.4, leftShoulder.position[2]];
                confidence = leftShoulder.visibility * 0.5; // Even lower confidence
                caseUsed = 5;
            }
            // Case 6: Only right shoulder is visible
            else if (rightShoulder && rightShoulder.visibility > threshold) {
                position = [rightShoulder.position[0], rightShoulder.position[1] - 0.4, rightShoulder.position[2]];
                confidence = rightShoulder.visibility * 0.5;
                caseUsed = 6;
            }
            // Case 7: No reliable landmarks
            else {
                position = null;
                confidence = 0;
                caseUsed = 7;
            }


            // Store last known position for potential future use
            if (position) {
                this._lastRootPos = position;
            }
            // console.log(`[RootPos] Using case ${caseUsed}, ${position} with confidence ${confidence.toFixed(2)}`);

            // Apply Kalman filter
            if (position && this.rootPosKalmanFilter) {
                const filtered = this.rootPosKalmanFilter.update(position[0], position[1], position[2]);
                position = filtered;
            }


            // Transform to Babylon.js world coordinates using manager's transformation
            if (position && this.manager) {
                const worldPos = this.manager.transformCoordinates(position);
                if (worldPos) {
                    position = [worldPos.x, worldPos.y, worldPos.z];
                    // console.log(`Transformed to world coordinates: ${position}`);
                }
            }
            // console.log(position, confidence);
            return { position, confidence };
        };

        this.updateHand = (handData, dtype, isLeft) => {
            if (!handData) return null;

            try {
                let handLandmarkManager = isLeft ? this.leftHandLandmarkManager : this.rightHandLandmarkManager;
                let handBoneManager = isLeft ? this.leftHandBoneManager : this.rightHandBoneManager;

                handLandmarkManager.setFromArray(handData, dtype);

                // Process each hand chain
                Object.entries(HAND_CHAINS).forEach(([chainName, boneNames]) => {
                    const bones = boneNames.map(name => handBoneManager.getByName(name))
                        .filter(bone => bone !== null);

                    if (bones.length === 0) return;

                    const chainVisibility = bones.reduce((sum, bone) => sum + bone.visibility, 0) / bones.length;

                    if (chainVisibility < VISIBILITY_THRESHOLD) return;

                    bones.forEach(bone => {
                        try {

                            // Validate quaternions after update
                            if (!bone.worldQuaternion || isNaN(bone.worldQuaternion.x)) {
                                console.warn(`Invalid quaternion for hand bone: ${bone.name}`);
                                bone._worldQuaternion = new Quaternion();
                            }
                        } catch (err) {
                            console.error(`Error updating hand bone ${bone.name}:`, err);
                            bone._worldQuaternion = new Quaternion();
                        }
                    });
                });

                handBoneManager.updateVisibility();

                // Validate all quaternions before getting array
                handBoneManager.validateQuaternions();

                return handBoneManager.getWorldQuaternionArray();
            } catch (error) {
                console.error(`[MediapipePoseCalculator] Error in updateHand (${isLeft ? 'left' : 'right'}):`, error);
                return null;
            }
        };


        let setPoseBoneCalculator = () => {

            const Hips = poseBoneManager.getByName('Hips');
            const LeftUpLeg = poseBoneManager.getByName('LeftUpLeg');
            const LeftLeg = poseBoneManager.getByName('LeftLeg');
            const LeftFoot = poseBoneManager.getByName('LeftFoot');
            const RightUpLeg = poseBoneManager.getByName('RightUpLeg');
            const RightLeg = poseBoneManager.getByName('RightLeg');
            const RightFoot = poseBoneManager.getByName('RightFoot');
            const Spine = poseBoneManager.getByName('Spine');
            const LeftArm = poseBoneManager.getByName('LeftArm');
            const LeftForeArm = poseBoneManager.getByName('LeftForeArm');
            const Neck = poseBoneManager.getByName('Neck');
            const Head = poseBoneManager.getByName('Head');
            const RightArm = poseBoneManager.getByName('RightArm');
            const RightForeArm = poseBoneManager.getByName('RightForeArm');




            let zDirection = new Vector3();


            Hips.update = function () {
                let leftUpLeg = this.getChild(1);

                let hipVector = leftUpLeg && leftUpLeg.startLandmark && leftUpLeg.startLandmark.vectorToCounterpart;
                let spine = this.getChild(2);
                let spineYDirection = spine && spine.yDirection;
                if (hipVector && spineYDirection) {
                    zDirection = Vector3.Cross(spineYDirection, hipVector);
                    this.updateYDirection(spineYDirection);
                    this.updateZDirection(zDirection);
                }
            };

            Spine.update = function () {
                let leftArm = this.getChild(0);
                let shoulderVector = leftArm && leftArm.startLandmark && leftArm.startLandmark.vectorToCounterpart;

                if (shoulderVector && this.yDirection) {
                    zDirection = Vector3.Cross(this.yDirection, shoulderVector);
                    this.updateYDirection();
                    this.updateZDirection(zDirection);
                }
            };



            LeftArm.update = RightArm.update = LeftForeArm.update = RightForeArm.update = function () {
                let spine = Spine;
                let spineXDirection = spine && spine.xDirection;
                let spineZDirection = spine && spine.zDirection;
                let armYDirection = this.yDirection;
                if (spineXDirection && spineZDirection && armYDirection) {
                    let angle = spineXDirection.angleTo(armYDirection);
                    if (angle > Math.PI / 4 && angle < 3 * Math.PI / 4) {
                        zDirection = Vector3.Cross(spineXDirection, armYDirection);
                    } else {
                        zDirection = Vector3.Cross(spineZDirection, armYDirection);
                        zDirection = Vector3.Cross(zDirection, armYDirection);
                    }
                    this.updateYDirection();
                    this.updateZDirection(zDirection);
                }

            };


            LeftUpLeg.update = RightUpLeg.update = LeftLeg.update = RightLeg.update = LeftFoot.update = RightFoot.update = function () {
                let hips = Hips;
                let hipsXDirection = hips && hips.xDirection;
                let hipsZDirection = hips && hips.zDirection;
                if (hipsXDirection && hipsZDirection && this.yDirection) {
                    let angle = hipsXDirection.angleTo(this.yDirection);
                    if (angle > Math.PI / 4 && angle < 3 * Math.PI / 4) {
                        zDirection = Vector3.Cross(hipsXDirection, this.yDirection);
                        zDirection.negate();
                    } else {
                        zDirection = Vector3.Cross(hipsZDirection, this.yDirection);
                        zDirection = Vector3.Cross(zDirection, this.yDirection);
                        zDirection.negate();
                    }
                    this.updateYDirection();
                    this.updateZDirection(zDirection);
                }
            };

            //LeftFoot.update = RightFoot.update = function () {


            let yDirection = new Vector3();
            Head.update = function () {
                let lm_head = this.startLandmark;
                let lm_nose = lm_head && lm_head.getChild(0);
                let lm_right_ear = lm_head.getChild(7);
                let earVector = lm_right_ear && lm_right_ear.vectorToCounterpart;

                let neck = this.parent;
                let neckYDirection = neck && neck.yDirection;

                if (lm_nose && earVector && neckYDirection) {
                    zDirection = lm_head.worldPosition.subtract(lm_nose.worldPosition);
                    yDirection = Vector3.Cross(earVector, zDirection);

                    this.updateYDirection(neckYDirection);
                    this.updateZDirection(zDirection);
                }
            };


        };
        let setHandBoneCalculator = (isLeft) => {
            let handBoneManager = isLeft ? leftHandBoneManager : rightHandBoneManager;
            const Wrist = handBoneManager.getByName('wrist');
            const ThumbMetacarpal = handBoneManager.getByName('thumb-metacarpal');
            const ThumbProximal = handBoneManager.getByName('thumb-phalanx-proximal');
            const ThumbDistal = handBoneManager.getByName('thumb-phalanx-distal');

            const IndexMetacarpal = handBoneManager.getByName('index-finger-metacarpal');
            const IndexProximal = handBoneManager.getByName('index-finger-phalanx-proximal');
            const IndexIntermediate = handBoneManager.getByName('index-finger-phalanx-intermediate');
            const IndexDistal = handBoneManager.getByName('index-finger-phalanx-distal');

            const MiddleMetacarpal = handBoneManager.getByName('middle-finger-metacarpal');
            const MiddleProximal = handBoneManager.getByName('middle-finger-phalanx-proximal');
            const MiddleIntermediate = handBoneManager.getByName('middle-finger-phalanx-intermediate');
            const MiddleDistal = handBoneManager.getByName('middle-finger-phalanx-distal');

            const RingMetacarpal = handBoneManager.getByName('ring-finger-metacarpal');
            const RingProximal = handBoneManager.getByName('ring-finger-phalanx-proximal');
            const RingIntermediate = handBoneManager.getByName('ring-finger-phalanx-intermediate');
            const RingDistal = handBoneManager.getByName('ring-finger-phalanx-distal');

            const PinkyMetacarpal = handBoneManager.getByName('pinky-finger-metacarpal');
            const PinkyProximal = handBoneManager.getByName('pinky-finger-phalanx-proximal');
            const PinkyIntermediate = handBoneManager.getByName('pinky-finger-phalanx-intermediate');
            const PinkyDistal = handBoneManager.getByName('pinky-finger-phalanx-distal');

            let zDirection = new Vector3();
            let yDirection = new Vector3();

            Wrist.update = function () {
                let lm_wrist = this.endLandmark;
                let vectorToPinky = lm_wrist && lm_wrist.getVectorToChild(1);
                let vectorToIndex = lm_wrist && lm_wrist.getVectorToChild(4);

                if (vectorToPinky && vectorToIndex) {
                    zDirection = Vector3.Cross(vectorToIndex, vectorToPinky);
                    yDirection.copyFrom(vectorToPinky).add(vectorToIndex).negate();

                    if (this.side === 'right') {
                        zDirection.negate();
                    }
                    this.updateYDirection(yDirection);
                    this.updateZDirection(zDirection);
                }
            };

            ThumbMetacarpal.update = IndexMetacarpal.update = MiddleMetacarpal.update = RingMetacarpal.update = PinkyMetacarpal.update = function () {
                let wrist = Wrist;
                this.updateYDirection();
                this.updateZDirection(wrist.zDirection);
            };

            ThumbProximal.update = ThumbDistal.update = function () {
                let thisYDirection = this.yDirection;
                let thumbXDirection = ThumbMetacarpal.xDirection;
                let thumbZDirection = ThumbMetacarpal.zDirection;
                if (thumbXDirection && thumbZDirection && thisYDirection) {
                    let angle = thisYDirection.angleTo(thumbXDirection);
                    if (angle > Math.PI / 4 && angle < 3 * Math.PI / 4) {
                        zDirection = Vector3.Cross(thumbXDirection, thisYDirection);
                        zDirection.negate();
                    } else {
                        zDirection = Vector3.Cross(thumbZDirection, thisYDirection);
                        zDirection = Vector3.Cross(zDirection, thisYDirection);
                    }
                    zDirection.negate();
                    this.updateYDirection();
                    this.updateZDirection(zDirection);
                }
            };
            IndexProximal.update = IndexIntermediate.update = IndexDistal.update = function () {
                let thisYDirection = this.yDirection;
                let thumbXDirection = IndexMetacarpal.xDirection;
                let thumbZDirection = IndexMetacarpal.zDirection;
                if (thumbXDirection && thumbZDirection && thisYDirection) {
                    let angle = thisYDirection.angleTo(thumbXDirection);
                    if (angle > Math.PI / 4 && angle < 3 * Math.PI / 4) {
                        zDirection = Vector3.Cross(thumbXDirection, thisYDirection);
                        zDirection.negate();
                    } else {
                        zDirection = Vector3.Cross(thumbZDirection, thisYDirection);
                        zDirection = Vector3.Cross(zDirection, thisYDirection);
                    }
                    zDirection.negate();
                    this.updateYDirection();
                    this.updateZDirection(zDirection);
                }
            };
            MiddleProximal.update = MiddleIntermediate.update = MiddleDistal.update = function () {
                let thisYDirection = this.yDirection;
                let thumbXDirection = MiddleMetacarpal.xDirection;
                let thumbZDirection = MiddleMetacarpal.zDirection;
                if (thumbXDirection && thumbZDirection && thisYDirection) {
                    let angle = thisYDirection.angleTo(thumbXDirection);
                    if (angle > Math.PI / 4 && angle < 3 * Math.PI / 4) {
                        zDirection = Vector3.Cross(thumbXDirection, thisYDirection);
                        zDirection.negate();
                    } else {
                        zDirection = Vector3.Cross(thumbZDirection, thisYDirection);
                        zDirection = Vector3.Cross(zDirection, thisYDirection);
                    }
                    zDirection.negate();
                    this.updateYDirection();
                    this.updateZDirection(zDirection);
                }
            };
            RingProximal.update = RingIntermediate.update = RingDistal.update = function () {
                let thisYDirection = this.yDirection;
                let thumbXDirection = RingMetacarpal.xDirection;
                let thumbZDirection = RingMetacarpal.zDirection;
                if (thumbXDirection && thumbZDirection && thisYDirection) {
                    let angle = thisYDirection.angleTo(thumbXDirection);
                    if (angle > Math.PI / 4 && angle < 3 * Math.PI / 4) {
                        zDirection = Vector3.Cross(thumbXDirection, thisYDirection);
                        zDirection.negate();
                    } else {
                        zDirection = Vector3.Cross(thumbZDirection, thisYDirection);
                        zDirection = Vector3.Cross(zDirection, thisYDirection);
                    }
                    zDirection.negate();
                    this.updateYDirection();
                    this.updateZDirection(zDirection);
                }
            };
            PinkyProximal.update = PinkyIntermediate.update = PinkyDistal.update = function () {
                let thisYDirection = this.yDirection;
                let thumbXDirection = PinkyMetacarpal.xDirection;
                let thumbZDirection = PinkyMetacarpal.zDirection;
                if (thumbXDirection && thumbZDirection && thisYDirection) {
                    let angle = thisYDirection.angleTo(thumbXDirection);
                    if (angle > Math.PI / 4 && angle < 3 * Math.PI / 4) {
                        zDirection = Vector3.Cross(thumbXDirection, thisYDirection);
                        zDirection.negate();
                    } else {
                        zDirection = Vector3.Cross(thumbZDirection, thisYDirection);
                        zDirection = Vector3.Cross(zDirection, thisYDirection);
                    }
                    zDirection.negate();
                    this.updateYDirection();
                    this.updateZDirection(zDirection);
                }
            };



            /*
            ThumbMetacarpal.update = function () { };
            ThumbProximal.update = function () { };
            ThumbDistal.update = function () { };
            IndexMetacarpal.update = function () { };
            IndexProximal.update = function () { };
            IndexIntermediate.update = function () { };
            IndexDistal.update = function () { };
            MiddleMetacarpal.update = function () { };
            MiddleProximal.update = function () { };
            MiddleIntermediate.update = function () { };
            MiddleDistal.update = function () { };
            RingMetacarpal.update = function () { };
            RingProximal.update = function () { };
            RingIntermediate.update = function () { };
            RingDistal.update = function () { };
            PinkyMetacarpal.update = function () { };
            PinkyProximal.update = function () { };
            PinkyIntermediate.update = function () { };
            PinkyDistal.update = function () { };
            */
        }



        setPoseBoneCalculator();
        setHandBoneCalculator(true);
        setHandBoneCalculator(false);



    }

    shouldApplyStrongFilter(movement) {
        // For very small movements, use stronger filtering
        return movement < POSEUPDATE_THRESHOLD * 0.5;
    }

    computeIKTargets() {
        // More intelligent target position calculation 
        // This can be enhanced based on your specific needs
        // Example implementation stub
    }

    // Method to receive updates from global smoothing config
    updateSmoothingConfig = (config) => {
        // Flag to prevent recursion
        this._updating = true;

        try {
            // Update local params
            if (typeof config.baseSlerpRatio === 'number')
                this._smoothingParams.baseSlerpRatio = config.baseSlerpRatio;

            if (typeof config.positionStiffness === 'number')
                this._smoothingParams.positionStiffness = config.positionStiffness;

            if (typeof config.rotationStiffness === 'number')
                this._smoothingParams.rotationStiffness = config.rotationStiffness;

            if (typeof config.visibilityFactor !== 'undefined')
                this._smoothingParams.visibilityFactor = config.visibilityFactor;

            if (typeof config.confidenceThreshold === 'number')
                this._smoothingParams.confidenceThreshold = config.confidenceThreshold;

            // Propagate to bone managers
            if (this.poseBoneManager) {
                this.poseBoneManager.updateSmoothingConfig(this._smoothingParams);
            }
            if (this.leftHandBoneManager) {
                this.leftHandBoneManager.updateSmoothingConfig(this._smoothingParams);
            }
            if (this.rightHandBoneManager) {
                this.rightHandBoneManager.updateSmoothingConfig(this._smoothingParams);
            }
        } finally {
            this._updating = false;
        }
    }

    // Legacy method - now just delegates to global config
    setSmoothingConfig = (config) => {
        // Prevent recursion
        if (this._updating) return;

        this.smoothingConfig.update(config);
    }

    // Clean up when destroying the calculator
    dispose() {
        this.smoothingConfig.removeListener(this);
        // ... any other disposal logic
    }

    // Add this method to the class
    setKalmanFilterParams(type, processCovariance, measurementCovariance) {
        if (!this._kalmanInitialized) {
            console.error("[MediapipePoseCalculator] Kalman filter not initialized yet!");
            return;
        }

        try {
            // Use this.kalmanConfig instead of this.smoothingConfig.kalmanConfig
            if (type === 'position' && this.rootPosKalmanFilter) {
                this.rootPosKalmanFilter.updateParameters(processCovariance, measurementCovariance);
            }

            // Apply to element managers based on type
            if (type === 'position' || type === 'all') {
                this._applyElementKalmanSettings(this.poseLandmarkManager, 'position', processCovariance, measurementCovariance);
                this._applyElementKalmanSettings(this.leftHandLandmarkManager, 'position', processCovariance, measurementCovariance);
                this._applyElementKalmanSettings(this.rightHandLandmarkManager, 'position', processCovariance, measurementCovariance);
            }

            if (type === 'rotation' || type === 'all') {
                this._applyElementKalmanSettings(this.poseBoneManager, 'rotation', processCovariance, measurementCovariance);
                this._applyElementKalmanSettings(this.leftHandBoneManager, 'rotation', processCovariance, measurementCovariance);
                this._applyElementKalmanSettings(this.rightHandBoneManager, 'rotation', processCovariance, measurementCovariance);
            }

            console.log(`[MediapipePoseCalculator] Updated Kalman filter parameters for ${type}: pc=${processCovariance}, mc=${measurementCovariance}`);
        } catch (error) {
            console.error("[MediapipePoseCalculator] Error applying Kalman settings:", error);
        }
    }

    // Add helper method to apply settings to element managers
    _applyElementKalmanSettings(manager, type, processCovariance, measurementCovariance) {
        if (!manager || !manager.container) return;

        // Apply new parameters to all elements
        for (let element of manager.container) {
            if (element.kalmanFilter) {
                const filterType = element.isLandmark ? 'position' : 'rotation';

                // Only update if the type matches
                if (type === filterType) {
                    // Get current position or rotation values
                    const currentValues = element.isLandmark ?
                        [element.worldPosition.x, element.worldPosition.y, element.worldPosition.z] :
                        [element._worldQuaternion?.x || 0, element._worldQuaternion?.y || 0, element._worldQuaternion?.z || 0];

                    // Create new filter with updated parameters
                    element.kalmanFilter = createKalmanFilter(
                        filterType,
                        currentValues,
                        {
                            processCovariance: processCovariance,
                            measurementCovariance: measurementCovariance
                        }
                    );
                }
            }
        }
    }
}

export default MediapipePoseCalculator;