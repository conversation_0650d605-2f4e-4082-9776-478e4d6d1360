import MediapipeAvatarController from "./MediapipeAvatarController.js";
import MediapipePoseCalculator from './MediapipePoseCalculator.js';
import SmoothingConfig from '../utils/SmoothingConfig.js';
import MatrixCache from '../utils/MatrixCache.js';
import { BONE_CHAINS, HAND_CHAINS, MOVEMENT_THRESHOLD } from '../configs/MediapipeConfig.js';
import { Vector3 } from "@babylonjs/core";

class MediapipeAvatarManager {
    constructor(scene, charactermanager) {
        this.charactermanager = charactermanager;
        this.use_inverse_kinematics = false; // Default value
        this.scene = scene; // Pass scene from CharacterManager
        this.poseCalculator = new MediapipePoseCalculator(this.use_inverse_kinematics, this);
        this.avatarController = null;

        // Reference to the global smoothing config
        this.smoothingConfig = SmoothingConfig;

        // Register as a listener for smoothing config changes
        this.smoothingConfig.addListener(this);

        // Separate configurations for position and rotation filtering
        this.kalmanConfig = {
            position: {
                processCovariance: 0.01,
                measurementCovariance: 0.1
            },
            rotation: {
                processCovariance: 0.005,
                measurementCovariance: 0.05
            }
        };

        // Apply the settings to the poseCalculator immediately
        this._applyKalmanSettings();

        // Set medium smoothing as the default configuration
        this.smoothingConfig.setMediumSmoothing();

        // Use MatrixCache instead of the custom cache implementation
        this.matrixCache = new MatrixCache({
            timeout: 100,
            movementThreshold: MOVEMENT_THRESHOLD,
            rotationThreshold: 0.01
        });

        // Consolidated bone cache architecture
        this._boneCache = {
            // Organize bones by anatomical chains
            byChain: {},
            // Flat lookup table for efficient access
            byName: {},
            // Root bone reference
            rootBone: null,
            // Visibility cache for bones
            visibilityByName: {}
        };

        // Initialize chain structure in cache
        Object.keys(BONE_CHAINS).forEach(chainName => {
            this._boneCache.byChain[chainName] = {};
        });

        // Add hand chains
        this._boneCache.byChain.leftHand = {};
        this._boneCache.byChain.rightHand = {};

        // Additional cache for non-bone frequently accessed objects
        this._cache = {
            skinnedMesh: null,
            lastCharacterPosition: null
        };
    }
    /**
     * Get the scene manager for coordinate transformations
     * @returns {SceneManager|null} The scene manager instance or null
     */
    getSceneManager() {
        if (!this.scene) return null;
        
        // Access the sceneManager attached to the scene
        return this.scene.sceneManager || null;
    }
    /**
     * Transform MediaPipe coordinates to Babylon.js world space
     * @param {Object|Array} coordinates - MediaPipe coordinates as {x,y,z} or [x,y,z]
     * @returns {Object} - Babylon coordinates
     */

    // Listener method for smoothing config updates
    updateSmoothingConfig(config) {
        // Flag to prevent recursion
        this._updating = true;

        try {
            // Propagate to avatar controller if exists
            if (this.avatarController) {
                this.avatarController.updateSmoothingConfig(config);
            }

            // Propagate to pose calculator
            if (this.poseCalculator) {
                this.poseCalculator.updateSmoothingConfig(config);
            }

            // Update slerpRatio for backward compatibility
            // But avoid updating the global config again, which would cause recursion
            if (typeof config.baseSlerpRatio === 'number' && !this._inSlerpRatioUpdate) {
                this._inSlerpRatioUpdate = true;
                // Just update local values, don't propagate changes back up
                if (this.avatarController) {
                    this.avatarController.poseBoneManager.setSlerpRatio(config.baseSlerpRatio);
                    this.avatarController.leftHandBoneManager.setSlerpRatio(config.baseSlerpRatio);
                    this.avatarController.rightHandBoneManager.setSlerpRatio(config.baseSlerpRatio);
                }
                this._inSlerpRatioUpdate = false;
            }
        } finally {
            this._updating = false;
        }
    }

    // Method to update smoothing configuration 
    setSmoothingConfig(config) {
        // Prevent recursion
        if (this._updating) return;

        // Now delegate to the global config
        this.smoothingConfig.update(config);
    }

    // Get cached inverse world matrix using MatrixCache
    _getInverseWorldMatrix(mesh) {
        if (!mesh) return null;
        return this.matrixCache.getInverseWorldMatrix(mesh);
    }

    // Get a bone from cache or find and cache it
    _getCachedBone(name, skeletonSearchFallback = false) {
        // First check our consolidated cache
        if (this._boneCache.byName[name]) {
            return this._boneCache.byName[name];
        }

        // Try with mixamorig prefix
        const mixamoName = `mixamorig:${name}`;
        if (this._boneCache.byName[mixamoName]) {
            return this._boneCache.byName[mixamoName];
        }

        // Try controller's cache if available
        if (this.avatarController && this.avatarController._boneCache?.byName) {
            const bone = this.avatarController._boneCache.byName[name] ||
                this.avatarController._boneCache.byName[mixamoName];

            if (bone) {
                // Cache it in our cache too
                this._cacheBone(name, bone);
                return bone;
            }
        }

        // Last resort - search skeleton
        if (skeletonSearchFallback && this.avatarController?.skeleton) {
            try {
                const skeleton = this.avatarController.skeleton;
                let bone = null;

                // Try to find the bone in the scene using common naming patterns
                if (this.scene) {
                    // Try without prefix first
                    bone = this.scene.getBoneByName(name);

                    // If not found, try with common prefixes from config
                    if (!bone) {
                        // Try with mixamorig: prefix (common in imported models)
                        bone = this.scene.getBoneByName(mixamoName);

                        // If still not found and we have avatar type info, try with appropriate prefix
                        if (!bone && this.avatarController.avatarType) {
                            // Get the proper name format for this bone based on avatar type
                            const boneConfig = this._findBoneConfigByName(name);
                            if (boneConfig?.otherName?.[this.avatarController.avatarType]) {
                                bone = this.scene.getBoneByName(boneConfig.otherName[this.avatarController.avatarType]);
                            }
                        }
                    }
                }

                // If scene lookup failed, fallback to direct skeleton access
                if (!bone && skeleton) {
                    // Use bones array if available
                    if (Array.isArray(skeleton.bones)) {
                        bone = skeleton.bones.find(b =>
                            b.name === name ||
                            b.name === mixamoName ||
                            (b.id && (b.id === name || b.id === mixamoName))
                        );
                    }
                }

                if (bone) {
                    // Cache for future use
                    this._cacheBone(name, bone);
                    return bone;
                }
            } catch (error) {
                console.warn(`[MediapipeAvatarManager] Error finding bone '${name}':`, error);
            }
        }

        return null;
    }

    // Get bone visibility from cache
    getBoneVisibility(name) {
        // Check if we have this bone's visibility in cache
        if (this._boneCache.visibilityByName[name] !== undefined) {
            return this._boneCache.visibilityByName[name];
        }

        // Try with mixamorig prefix
        const mixamoName = `mixamorig:${name}`;
        if (this._boneCache.visibilityByName[mixamoName] !== undefined) {
            return this._boneCache.visibilityByName[mixamoName];
        }

        // Default visibility if not found
        return 0;
    }

    // Update bone visibility in cache
    updateBoneVisibility(name, visibility) {
        if (name) {
            this._boneCache.visibilityByName[name] = visibility;

            // Also store with simplified name for easier lookup
            const simpleName = name.replace('mixamorig:', '');
            if (simpleName !== name) {
                this._boneCache.visibilityByName[simpleName] = visibility;
            }
        }
    }

    // Helper method to find bone configuration by name
    _findBoneConfigByName(name) {
        // Import configurations from MediapipeConfig.js (should be already imported)
        const { MEDIAPIPE_VIRTUAL_BONES_CONFIG } = window.MediapipeConfig || {};

        if (!MEDIAPIPE_VIRTUAL_BONES_CONFIG) return null;

        // Find matching bone config
        return MEDIAPIPE_VIRTUAL_BONES_CONFIG.find(
            config => config.poseElementConfig.name === name
        );
    }

    // Simplified update method that just passes results to pose calculator and then to controller
    update(results) {
        // Process pose detection results
        const calculatedResult = this.poseCalculator.update(results);

        // Send calculated result to avatar controller if it exists
        if (this.avatarController) {
            this.avatarController.updateData(calculatedResult);
        }
        else {
            console.warn("[MediapipeAvatarManager] No avatar controller bound. Results not applied.");
        }
    }

    bindAvatar(avatar) {
        this.avatarController = new MediapipeAvatarController(avatar, this.scene, this.use_inverse_kinematics);
        this.avatarController.bindAvatar(avatar, 'RPM');
        // Register for bounding box updates if SceneManager exists
        const sceneManager = this.getSceneManager();
        if (sceneManager && avatar) {
            sceneManager.addBoundingBoxObserver(avatar.name, (boundingData) => {
            // Update character metrics when the character's bounding box changes
            sceneManager.updateVisualizationScale(avatar);
            // console.log('[MediapipeAvatarManager] Character metrics updated from bounding box change');
            });
        }
        // Register the controller as a client that will use our bone cache
        if (this.avatarController) {
            // Provide the controller with the manager's matrix cache
            this.avatarController.matrixCache = this.matrixCache;

            // Set bone cache provider
            this.avatarController._setBoneCacheProvider(this);

            // Share the existing smoothing config
            this.avatarController.updateSmoothingConfig(this.smoothingConfig);

            // Now that the avatar controller exists, we can apply Kalman settings to it
            const { position, rotation } = this.kalmanConfig;
            this.avatarController.setUseKalmanFilter &&
                this.avatarController.setUseKalmanFilter(true);

            // Cache bones after binding
            this._cacheBones();

            // Extract initial bone data and position for proper Kalman initialization
            const initialBoneData = this.avatarController.getInitialBoneData();
            const initialRootPos = this.avatarController.getInitialRootPosition();
            // Initialize Kalman filters with the avatar's initial data
            this.initKalmanFilter(initialBoneData, initialRootPos);

            // If using IK, set up IK controllers AFTER bones are cached
            if (this.use_inverse_kinematics && this.avatarController.setupIKControllers) {
                this.avatarController.setupIKControllers();
            }
        }
    }

    // Helper method to find and cache the skinned mesh
    _cacheSkinnedMesh(avatar) {
        // Clear previous cache
        this._cache.skinnedMesh = null;
        this._cache.lastCharacterPosition = null;

        // Clear bone cache
        Object.keys(this._boneCache.byChain).forEach(chain => {
            this._boneCache.byChain[chain] = {};
        });
        this._boneCache.byName = {};
        this._boneCache.rootBone = null;

        // Clear matrix cache
        this.matrixCache.clearAllCaches();

        if (!avatar || !this.avatarController) return;

        try {
            // Find the skinned mesh that uses the skeleton
            const skinnedMesh = avatar.getChildMeshes().find(m =>
                m.skeleton === this.avatarController.skeleton
            );

            if (skinnedMesh) {
                this._cache.skinnedMesh = skinnedMesh;

                // Cache initial position
                this._cache.lastCharacterPosition = skinnedMesh.position.clone();

                console.log("[MediapipeAvatarManager] Cached skinned mesh:", skinnedMesh.name);

                // Pre-populate bone cache with frequently used bones
                this._cacheBones();
            } else {
                console.warn("[MediapipeAvatarManager] Could not find skinned mesh for caching");
            }
        } catch (error) {
            console.error("[MediapipeAvatarManager] Error caching skinned mesh:", error);
        }
    }

    _cacheBones() {
        // Clear previous cache
        Object.keys(this._boneCache.byChain).forEach(chain => {
            this._boneCache.byChain[chain] = {};
        });
        this._boneCache.byName = {};
        this._boneCache.rootBone = null;

        // Clear matrix cache
        this.matrixCache.clearAllCaches();
        this._cache.skinnedMesh = null;
        this._cache.lastCharacterPosition = null;

        if (!this.avatarController || !this.avatarController.character) {
            console.warn("[MediapipeAvatarManager] Cannot cache bones: No avatar controller or character");
            return;
        }

        try {
            const skeleton = this.avatarController.skeleton;
            if (!skeleton) {
                console.warn("[MediapipeAvatarManager] Cannot cache bones: No skeleton found");
                return;
            }

            // Find the skinned mesh that uses the skeleton
            const skinnedMesh = this.avatarController.character.getChildMeshes().find(m =>
                m.skeleton === skeleton
            );

            if (skinnedMesh) {
                this._cache.skinnedMesh = skinnedMesh;
                this._cache.lastCharacterPosition = skinnedMesh.position.clone();
                console.log("[MediapipeAvatarManager] Cached skinned mesh:", skinnedMesh.name);

                // Share the skinned mesh with the controller
                this.avatarController._skinnedMesh = skinnedMesh;
            }

            // Cache root bone first
            const hips = this.scene.getBoneByName('Hips') || this.scene.getBoneByName('mixamorig:Hips');
            if (hips) {
                this._boneCache.rootBone = hips;
                this._cacheBone('Hips', hips);
            }

            // Cache all bones from the skeleton
            if (Array.isArray(skeleton.bones)) {
                skeleton.bones.forEach(bone => {
                    if (!bone || !bone.name) return;

                    // Clean the name by removing common prefixes
                    const cleanName = bone.name.replace('mixamorig:', '');
                    this._cacheBone(cleanName, bone);
                });
            }

            console.log(`[MediapipeAvatarManager] Cached ${Object.keys(this._boneCache.byName).length} bones in ${Object.keys(this._boneCache.byChain).length} chains`);

        } catch (error) {
            console.error("[MediapipeAvatarManager] Error caching bones:", error);
        }
    }

    // Enhanced bone retrieval methods
    /**
     * Get a bone by name from the cache or find it in the skeleton
     * @param {string} name - The name of the bone to find
     * @param {boolean} searchSkeleton - Whether to search the skeleton if not found in cache
     * @returns {BABYLON.Bone|null} - The found bone or null
     */
    getBoneByName(name, searchSkeleton = true) {
        // Use the _getCachedBone method which now uses the consolidated cache
        return this._getCachedBone(name, searchSkeleton);
    }

    /**
     * Get bones belonging to a specific chain
     * @param {string} chainName - The name of the chain
     * @returns {Object} - Object mapping bone names to bones
     */
    getBoneChain(chainName) {
        return this._boneCache.byChain[chainName] || {};
    }

    /**
     * Get the root bone of the skeleton
     * @returns {BABYLON.Bone|null} - The root bone or null
     */
    getRootBone() {
        return this._boneCache.rootBone;
    }

    /**
     * Cache a bone reference
     * @param {string} name - The name of the bone
     * @param {BABYLON.Bone} bone - The bone object
     */
    _cacheBone(name, bone) {
        if (!bone) return;

        const simpleName = name.replace('mixamorig:', '');

        // Store in consolidated cache
        this._boneCache.byName[name] = bone;
        this._boneCache.byName[simpleName] = bone;

        // Check if it's a root bone
        if (simpleName === 'Hips') {
            this._boneCache.rootBone = bone;
        }

        // Determine which chain this bone belongs to
        for (const [chainName, boneNames] of Object.entries(BONE_CHAINS)) {
            if (boneNames.some(boneName => simpleName.includes(boneName))) {
                this._boneCache.byChain[chainName][simpleName] = bone;
                this._boneCache.byChain[chainName][name] = bone;
                break;
            }
        }

        // Categorize hand bones by finger
        if (simpleName.includes('Hand')) {
            const isLeft = simpleName.includes('Left');
            const chainKey = isLeft ? 'leftHand' : 'rightHand';
            this._boneCache.byChain[chainKey][simpleName] = bone;
            this._boneCache.byChain[chainKey][name] = bone;
        }
        else if (simpleName.includes('Thumb')) {
            this._boneCache.byChain.thumb[simpleName] = bone;
            this._boneCache.byChain.thumb[name] = bone;
        }
        else if (simpleName.includes('Index')) {
            this._boneCache.byChain.indexFinger[simpleName] = bone;
            this._boneCache.byChain.indexFinger[name] = bone;
        }
        else if (simpleName.includes('Middle')) {
            this._boneCache.byChain.middleFinger[simpleName] = bone;
            this._boneCache.byChain.middleFinger[name] = bone;
        }
        else if (simpleName.includes('Ring')) {
            this._boneCache.byChain.ringFinger[simpleName] = bone;
            this._boneCache.byChain.ringFinger[name] = bone;
        }
        else if (simpleName.includes('Pinky')) {
            this._boneCache.byChain.pinkyFinger[simpleName] = bone;
            this._boneCache.byChain.pinkyFinger[name] = bone;
        }
    }

    setUseHand(useHand) {
        this.poseCalculator.setUseHand(useHand);
        if (this.avatarController) {
            this.avatarController.setUseHand(useHand);
        }
    }

    // Modified setSlerpRatio to avoid recursion
    setSlerpRatio(ratio) {
        // Check if we're already in an update cycle
        if (this._updating || this._inSlerpRatioUpdate) return;

        this._inSlerpRatioUpdate = true;
        try {
            // Update global config's base slerp ratio
            this.setSmoothingConfig({ baseSlerpRatio: ratio });
        } finally {
            this._inSlerpRatioUpdate = false;
        }
    }

    setUseInverseKinematics(useIK) {
        this.use_inverse_kinematics = useIK;
        if (this.avatarController) {
            this.avatarController.use_inverse_kinematics = useIK;
        }
    }

    /**
     * Initialize Kalman filters with initial data and apply settings
     * @param {Object} initialBoneData - Initial bone data 
     * @param {Array} initialRootPos - Initial root position
     */
    initKalmanFilter(initialBoneData = null, initialRootPos = null) {
        // STEP 1: Initialize filters with initial data (no parameters)
        if (this.poseCalculator) {
            this.poseCalculator.initKalmanFilter(initialBoneData, initialRootPos);
        }

        if (this.avatarController) {
            this.avatarController.initKalmanFilter(initialBoneData, initialRootPos);
        }

        // STEP 2: Apply parameters separately after initialization
        this._applyKalmanParameters();
    }

    /**
     * Apply Kalman parameters separately from initialization
     * @private
     */
    _applyKalmanParameters() {
        const { position, rotation } = this.kalmanConfig;

        // Apply position parameters if available
        if (position) {
            if (this.poseCalculator?.setKalmanFilterParams) {
                this.poseCalculator.setKalmanFilterParams('position',
                    position.processCovariance,
                    position.measurementCovariance);
            }

            if (this.avatarController?.setKalmanFilterParams) {
                this.avatarController.setKalmanFilterParams('position',
                    position.processCovariance,
                    position.measurementCovariance);
            }
        }

        // Apply rotation parameters if available
        if (rotation) {
            if (this.poseCalculator?.setKalmanFilterParams) {
                this.poseCalculator.setKalmanFilterParams('rotation',
                    rotation.processCovariance,
                    rotation.measurementCovariance);
            }

            if (this.avatarController?.setKalmanFilterParams) {
                this.avatarController.setKalmanFilterParams('rotation',
                    rotation.processCovariance,
                    rotation.measurementCovariance);
            }
        }
    }

    /**
     * Apply Kalman filter settings to all components
     * @private
     */
    _applyKalmanSettings() {
        if (!this.poseCalculator) return;

        try {
            // Set medium configuration as default
            this.smoothingConfig.setMediumSmoothing();

            // STEP 1: Initialize with initial data if not already done
            if (this.avatarController && !this.avatarController._kalmanInitialized) {
                // Extract initial data from avatar controller
                const initialBoneData = this.avatarController.getInitialBoneData?.() || null;
                const initialRootPos = this.avatarController.getInitialRootPosition?.() || null;

                // Initialize filters with just the data
                this.initKalmanFilter(initialBoneData, initialRootPos);
            }
            console.log("[MediapipeAvatarManager] Applied default medium smoothing and Kalman settings");
        } catch (error) {
            console.error("[MediapipeAvatarManager] Error applying Kalman settings:", error);
        }
    }

    setUseKalmanFilter(useKalmanFilter) {
        if (this.poseCalculator) {
            this.poseCalculator.setUseKalmanFilter(useKalmanFilter);
        }

        if (this.avatarController) {
            this.avatarController.setUseKalmanFilter(useKalmanFilter);
        }
    }

    // Enhanced method to configure Kalman filter parameters with type support
    setKalmanFilterParams(type, processCovariance, measurementCovariance) {
        // Create update object with proper structure
        const update = {
            kalmanConfig: {}
        };

        update.kalmanConfig[type] = {
            processCovariance,
            measurementCovariance
        };

        // Update through global config
        this.smoothingConfig.update(update);
    }

    // These methods now delegate to the global smoothing config
    setHighSmoothing() {
        this.smoothingConfig.setHighSmoothing();
    }

    setMediumSmoothing() {
        this.smoothingConfig.setMediumSmoothing();
    }

    setLowSmoothing() {
        this.smoothingConfig.setLowSmoothing();
    }
    /**
     * Transform MediaPipe coordinates to Babylon.js world space
     * @param {Object|Array} coordinates - MediaPipe coordinates as {x,y,z} or [x,y,z]
     * @param {Object} [options] - Optional transformation options
     * @param {boolean} [options.flipX=true] - Whether to flip X axis (MediaPipe to Babylon)
     * @param {boolean} [options.flipZ=false] - Whether to flip Z axis
     * @param {number} [options.scaleFactor] - Override default scaling
     * @param {Vector3} [options.offset] - Additional position offset to apply
     * @returns {Vector3} - Transformed coordinates in Babylon.js world space
     */
    transformCoordinates(coordinates, options = {}) {
        // Default options
        const {
            flipX = true,
            flipZ = false,
            scaleFactor = null,
            offset = null
        } = options;
        
        // Extract x, y, z values from input (handle both array and object formats)
        let x, y, z;
        if (Array.isArray(coordinates)) {
            [x, y, z] = coordinates;
        } else {
            x = coordinates.x;
            y = coordinates.y;
            z = coordinates.z;
        }
        
        // Validate input
        if (x === undefined || y === undefined || z === undefined ||
            isNaN(x) || isNaN(y) || isNaN(z)) {
            console.warn("[MediapipeAvatarManager] Invalid coordinates for transformation");
            return new Vector3(0, 0, 0);
        }
        
        // Apply coordinate system conversion (MediaPipe → Babylon)
        if (flipX) x = -x; // Flip X axis for right-hand to left-hand conversion
        if (flipZ) z = -z; // Optionally flip Z based on requirements
        
        // Get scene manager for metrics and bounds
        const sceneManager = this.getSceneManager();
        
        // Calculate appropriate scaling
        let scale = 1;
        if (scaleFactor !== null) {
            scale = scaleFactor;
        } else if (sceneManager) {
            // Get character metrics for intelligent scaling
            const metrics = sceneManager.getCharacterMetrics();
            
            if (metrics && metrics.dimensions && metrics.dimensions.height > 0) {
                // Use character height as reference for scaling
                // Typical MediaPipe pose height is around 2.0 units
                const mediapipeHeight = 2.0;
                scale = metrics.dimensions.height / mediapipeHeight;
            } else {
                // Fallback to visualization bounds
                const bounds = sceneManager.getVisualizationBounds();
                if (bounds) {
                    const heightRange = bounds.y.max - bounds.y.min;
                    scale = heightRange / 2.0; // Assume MediaPipe height of 2.0
                }
            }
        }
        
        // Apply scaling
        x *= scale;
        y *= scale;
        z *= scale;
        
        // Create resulting vector
        const result = new Vector3(x, y, z);
        
        // Apply additional offset if provided
        if (offset) {
            result.addInPlace(offset);
        }
        
        // Apply scene-specific offset if available
        if (sceneManager && sceneManager.characterMetrics && sceneManager.characterMetrics.center) {
            // Use character center as reference point
            const yOffset = sceneManager.worldBounds?.y.min || 0;
            result.y += yOffset;
        }
        
        return result;
    }

    /**
     * Transform a world position from Babylon space to MediaPipe space
     * @param {Vector3} position - Position in Babylon world space
     * @param {Object} [options] - Optional transformation options
     * @returns {Object} - Position in MediaPipe coordinate system {x,y,z}
     */
    transformWorldToMediapipe(position, options = {}) {
        if (!position) return { x: 0, y: 0, z: 0 };
        
        // Default options with inverse flip from the forward transform
        const {
            flipX = true,
            flipZ = false,
            scaleFactor = null
        } = options;
        
        // Get scene manager for metrics and bounds
        const sceneManager = this.getSceneManager();
        
        // Calculate appropriate scaling
        let scale = 1;
        if (scaleFactor !== null) {
            scale = 1 / scaleFactor; // Inverse of forward scaling
        } else if (sceneManager) {
            // Use a more robust approach: average scale from multiple reliable landmark pairs
            const robustScale = this._calculateRobustScaleFactor();
            if (robustScale > 0) {
                scale = robustScale;
            } else {
                // Fallback to traditional methods if robust calculation fails
                const metrics = sceneManager.getCharacterMetrics();
                if (metrics && metrics.dimensions && metrics.dimensions.height > 0) {
                    const mediapipeHeight = 2.0;
                    scale = mediapipeHeight / metrics.dimensions.height; // Inverse scaling
                } else {
                    const bounds = sceneManager.getVisualizationBounds();
                    if (bounds) {
                        const heightRange = bounds.y.max - bounds.y.min;
                        scale = 2.0 / heightRange; // Inverse of forward scaling
                    }
                }
            }
        }
        
        // Remove scene offset if available
        let x = position.x;
        let y = position.y;
        let z = position.z;
        
        if (sceneManager && sceneManager.characterMetrics && sceneManager.characterMetrics.center) {
            const yOffset = sceneManager.worldBounds?.y.min || 0;
            y -= yOffset;
        }
        
        // Apply inverse scaling
        x *= scale;
        y *= scale;
        z *= scale;
        
        // Apply coordinate system conversion (Babylon → MediaPipe)
        if (flipX) x = -x;
        if (flipZ) z = -z;
        
        return { x, y, z };
    }
    /**
     * Calculate a robust scale factor by averaging measurements from multiple landmark pairs
     * @returns {number} Calculated scale factor or 0 if calculation fails
     * @private
     */
    _calculateRobustScaleFactor() {
        // We need access to both the MediaPipe pose calculator and character manager
        if (!this.mediapipeAvatarManager?.poseCalculator) {
            return 0;
        }
        
        // Define reliable landmark pairs to measure
        const landmarkPairs = [
            // Shoulders span (highly visible in most poses)
            { start: 'left_shoulder', end: 'right_shoulder', minVisibility: 0.6 },
            
            // Hip span (usually visible)
            { start: 'left_hip', end: 'right_hip', minVisibility: 0.5 },
            
            // Spine length (vertical reference)
            { start: 'hips', end: 'neck', minVisibility: 0.6 },
            
            // Left arm length
            { start: 'left_shoulder', end: 'left_elbow', minVisibility: 0.5 },
            
            // Right arm length
            { start: 'right_shoulder', end: 'right_elbow', minVisibility: 0.5 },
            
            // Left forearm length
            { start: 'left_elbow', end: 'left_wrist', minVisibility: 0.5 },
            
            // Right forearm length
            { start: 'right_elbow', end: 'right_wrist', minVisibility: 0.5 }
        ];
        
        const calculator = this.mediapipeAvatarManager.poseCalculator;
        const landmarkManager = calculator?.poseLandmarkManager;
        const boneManager = calculator?.poseBoneManager;
        
        if (!landmarkManager || !boneManager) {
            return 0;
        }
        
        // Collect valid scale measurements
        const scaleFactors = [];
        
        // For each landmark pair
        landmarkPairs.forEach(pair => {
            // Get landmarks
            const startLandmark = landmarkManager.getByName(pair.start);
            const endLandmark = landmarkManager.getByName(pair.end);
            
            // Skip if landmarks aren't available
            if (!startLandmark || !endLandmark) {
                return;
            }
            
            // Skip if visibility is too low
            if (startLandmark.visibility < pair.minVisibility || 
                endLandmark.visibility < pair.minVisibility) {
                return;
            }
            
            // Calculate MediaPipe distance
            const mpDistance = startLandmark.worldPosition.subtract(
                endLandmark.worldPosition
            ).length();
            
            // Find corresponding bones in avatar
            let startBone, endBone;
            
            // Try to find corresponding bones by name
            for (let i = 0; i < boneManager.container.length; i++) {
                const bone = boneManager.container[i];
                
                // Look for bones with matching start/end landmarks
                if (bone.startLandmark === startLandmark && bone.endLandmark === endLandmark) {
                    // Direct match found
                    startBone = bone;
                    break;
                }
                
                // Also check bone landmarks by name
                if (bone.startLandmark && bone.startLandmark.name === pair.start &&
                    bone.endLandmark && bone.endLandmark.name === pair.end) {
                    startBone = bone;
                    break;
                }
            }
            
            // Skip if no corresponding bones found
            if (!startBone) {
                return;
            }
            
            // Get bone from avatar
            const avatarBone = this.getBoneByName(startBone.name) || 
                this.getBoneByName(startBone.otherName?.RPM);
            
            if (!avatarBone || !avatarBone.position) {
                return;
            }
            
            // Get position of parent and child in avatar
            const parentPos = avatarBone.position.clone();
            const childPos = avatarBone.getChildren()[0]?.position;
            
            if (!childPos) {
                return;
            }
            
            // Calculate avatar bone length in world space
            const avatarDistance = parentPos.subtract(childPos).length();
            
            // Skip if either distance is too small
            if (mpDistance < 0.01 || avatarDistance < 0.01) {
                return;
            }
            
            // Calculate scale factor
            const pairScale = mpDistance / avatarDistance;
            
            // Add to collection if reasonable
            if (isFinite(pairScale) && pairScale > 0.01 && pairScale < 100) {
                scaleFactors.push(pairScale);
            }
        });
        
        // Return average or 0 if no valid measurements
        if (scaleFactors.length === 0) {
            return 0;
        }
        
        // Calculate average, removing outliers
        scaleFactors.sort((a, b) => a - b);
        
        // Remove extreme outliers (lowest 10% and highest 10%)
        const startIndex = Math.floor(scaleFactors.length * 0.1);
        const endIndex = Math.ceil(scaleFactors.length * 0.9);
        
        // Calculate average of remaining values
        let sum = 0;
        let count = 0;
        
        for (let i = startIndex; i < endIndex; i++) {
            sum += scaleFactors[i];
            count++;
        }
        
        return count > 0 ? sum / count : 0;
    }
    /**
     * Transform a world point to visualization space
     * @param {Vector3} worldPoint - Point in world space
     * @param {Object} characterMetrics - Character metrics for scaling
     * @param {Object|number} scaleFactor - Scale factor or object with xyz components
     * @returns {Vector3} Point in visualization space
     */
    transformWorldToVisual(worldPoint, characterMetrics, scaleFactor = 1) {
        if (!worldPoint) return new Vector3(0, 0, 0);
        
        const sceneManager = this.getSceneManager();
        if (!sceneManager) return worldPoint.clone();
        
        // Get visualization bounds
        const bounds = sceneManager.getVisualizationBounds();
        if (!bounds) return worldPoint.clone();
        
        // Calculate scale factors for each dimension
        let scaleX = 1, scaleY = 1, scaleZ = 1;
        
        if (typeof scaleFactor === 'number') {
            scaleX = scaleY = scaleZ = scaleFactor;
        } else if (scaleFactor) {
            scaleX = scaleFactor.x || 1;
            scaleY = scaleFactor.y || 1;
            scaleZ = scaleFactor.z || 1;
        }
        
        // Transform point to visualization space
        const result = new Vector3(
            worldPoint.x * scaleX,
            worldPoint.y * scaleY,
            worldPoint.z * scaleZ
        );
        
        return result;
    }
    // Clean up when destroying the manager
    dispose() {
        // Clear enhanced bone cache
        Object.keys(this._boneCache.byChain).forEach(chain => {
            this._boneCache.byChain[chain] = {};
        });
        this._boneCache.byName = {};
        this._boneCache.rootBone = null;

        // Clear other cached references
        this._cache = {
            skinnedMesh: null,
            lastCharacterPosition: null
        };

        // Clear matrix cache
        this.matrixCache.clearAllCaches();

        this.smoothingConfig.removeListener(this);
        // ...any other disposal logic
    }
}

export default MediapipeAvatarManager;