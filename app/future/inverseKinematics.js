import { Vector3, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON> } from '@babylonjs/core';
import { ensureMathExtensions } from '../utils/mathExtensions.js';
import BoneMapper from '../characters/boneMapper.js';

export class IKSolver {
    constructor(options = {}) {
        ensureMathExtensions();

        this.maxIterations = options.maxIterations || 15;
        this.tolerance = options.tolerance || 0.005;
        this.collisionRadius = options.collisionRadius || 0.1;
        this.stretchLimit = options.stretchLimit || 1.1; // Allow 10% stretch
        this.debug = options.debug || false;
        this.scene = null;
        this.physicsSystem = null;

        // Add inertia smoothing parameters
        this.inertiaAlpha = options.inertiaAlpha || 0.5;
        this.previousPositions = new Map();

        // Add joint constraints
        this.constraints = {
            elbow: { minAngle: 45, maxAngle: 135 },
            shoulder: { minRotation: -45, maxRotation: 90 },
            knee: { minAngle: 0, maxAngle: 160 },
            hip: { minRotation: -90, maxRotation: 45 }
        };

        // Add momentum parameters
        this.momentum = new Vector3(0, 0, 0);
        this.momentumDecay = 0.9;

        // Add batch processing options
        this.batchSize = options.batchSize || 4;
        this.useSIMD = options.useSIMD !== false && typeof WebAssembly.validate === 'function';

        // Remove chainDefinitions and use BoneMapper instead
        this.chainDefinitions = BoneMapper.CHAIN_DEFINITIONS;
    }

    initialize(scene) {
        this.scene = scene;
        this.physicsSystem = scene.getPhysicsEngine();
        console.log('[IKSolver] Initialized with scene and physics:', {
            hasScene: !!this.scene,
            hasPhysics: !!this.physicsSystem
        });
    }

    solveFABRIK(joints, target, boneLengths, constraints = []) {
        if (!this.validateInputs(joints, target, boneLengths)) {
            return joints;
        }

        // Store previous positions for inertia smoothing
        const prevPositions = joints.map(j => j.clone());
        this.previousPositions.set(joints[0].toString(), prevPositions);

        // Calculate momentum influence
        this.updateMomentum(joints[joints.length - 1], target);
        const targetWithMomentum = target.add(this.momentum);

        if (this.debug) {
            this.logSolveStart(joints, target, boneLengths);
        }

        const baseToTarget = Vector3.Distance(joints[0], target);
        const totalLength = boneLengths.reduce((sum, len) => sum + len, 0);
        const stretchFactor = baseToTarget / totalLength;
        const isReachable = stretchFactor <= this.stretchLimit;

        // Optimize iterations based on reach and stretch
        const iterationsNeeded = this.calculateIterations(isReachable, stretchFactor);

        // Handle unreachable targets
        if (!isReachable) {
            return this.solveUnreachableTarget(joints, target, boneLengths);
        }

        let iteration = 0;
        let diff = Number.MAX_VALUE;
        let bestSolution = null;
        let bestError = Number.MAX_VALUE;

        while (iteration < iterationsNeeded && diff > this.tolerance) {
            const basePos = joints[0].clone();

            // Forward reaching phase with collision avoidance
            this.forwardReachingPhase(joints, targetWithMomentum, boneLengths);

            // Backward reaching phase with base position maintenance
            this.backwardReachingPhase(joints, basePos, boneLengths);

            // Apply constraints and physics
            if (constraints.length > 0) {
                this.applyConstraintsWithPhysics(joints, constraints);
            }

            // Apply joint constraints
            this.applyJointConstraints(joints, constraints);

            // Calculate error and store best solution
            diff = Vector3.Distance(joints[joints.length - 1], target);
            if (diff < bestError) {
                bestError = diff;
                bestSolution = joints.map(j => j.clone());
            }

            this.logIterationProgress(iteration, diff, joints);
            iteration++;
        }

        // Apply inertia smoothing
        const smoothedSolution = this.applyInertiaSmoothing(
            this.previousPositions.get(joints[0].toString()) || joints,
            bestSolution || joints
        );

        // Update previous positions
        this.previousPositions.set(joints[0].toString(), smoothedSolution.map(p => p.clone()));

        // Decay momentum
        this.momentum.scaleInPlace(this.momentumDecay);

        // Return best solution found
        return smoothedSolution;
    }

    validateInputs(joints, target, boneLengths) {
        if (!joints || !target || !boneLengths) {
            console.error('[IKSolver] Invalid inputs:', {
                hasJoints: !!joints,
                hasTarget: !!target,
                hasBoneLengths: !!boneLengths
            });
            return false;
        }

        if (joints.some(j => !j || !(j instanceof Vector3))) {
            console.error('[IKSolver] Invalid joint positions');
            return false;
        }

        if (boneLengths.length !== joints.length - 1) {
            console.error('[IKSolver] Mismatch between joints and bone lengths');
            return false;
        }

        return true;
    }

    calculateIterations(isReachable, stretchFactor) {
        return Math.ceil(
            isReachable ?
                Math.min(this.maxIterations, Math.max(3, stretchFactor * 5)) :
                this.maxIterations / 2
        );
    }

    forwardReachingPhase(joints, target, boneLengths) {
        joints[joints.length - 1].copyFrom(target);

        for (let i = joints.length - 2; i >= 0; i--) {
            const currentToNext = joints[i + 1].subtract(joints[i]);
            const currentDist = currentToNext.length();
            const lambda = boneLengths[i] / currentDist;

            // Calculate new position
            const newPos = Vector3.Lerp(joints[i + 1], joints[i], lambda);

            // Check for collisions if physics system is available
            if (this.physicsSystem) {
                const ray = new Ray(joints[i + 1], newPos.subtract(joints[i + 1]), currentDist);
                const hit = this.scene.pickWithRay(ray);

                if (hit.hit) {
                    // Adjust position to avoid collision
                    joints[i] = hit.pickedPoint.add(ray.direction.scale(-this.collisionRadius));
                } else {
                    joints[i].copyFrom(newPos);
                }
            } else {
                joints[i].copyFrom(newPos);
            }
        }
    }

    backwardReachingPhase(joints, basePos, boneLengths) {
        joints[0].copyFrom(basePos);

        for (let i = 1; i < joints.length; i++) {
            const prevToCurrent = joints[i].subtract(joints[i - 1]);
            const currentDist = prevToCurrent.length();
            const lambda = boneLengths[i - 1] / currentDist;

            // Calculate new position with collision check
            const newPos = Vector3.Lerp(joints[i - 1], joints[i], lambda);

            if (this.physicsSystem) {
                const ray = new Ray(joints[i - 1], newPos.subtract(joints[i - 1]), boneLengths[i - 1]);
                const hit = this.scene.pickWithRay(ray);

                if (hit.hit) {
                    // Adjust position to avoid collision
                    joints[i] = hit.pickedPoint.add(ray.direction.scale(this.collisionRadius));
                } else {
                    joints[i].copyFrom(newPos);
                }
            } else {
                joints[i].copyFrom(newPos);
            }
        }
    }

    applyConstraintsWithPhysics(joints, constraints) {
        for (let i = 0; i < constraints.length; i++) {
            const constraint = constraints[i];
            if (!constraint) continue;

            const currentDir = joints[i + 1].subtract(joints[i]).normalize();
            const angle = Vector3.GetAngleBetweenVectors(
                constraint.axis,
                currentDir,
                Vector3.Cross(constraint.axis, currentDir)
            );

            // Apply physics-based damping to constraint enforcement
            const damping = this.physicsSystem ? 0.8 : 1.0;
            const clampedAngle = Math.max(
                constraint.minAngle,
                Math.min(constraint.maxAngle, angle)
            ) * damping;

            if (Math.abs(angle - clampedAngle) > 0.01) {
                this.rotateJoint(joints, i, constraint.axis, clampedAngle - angle);
            }
        }
    }

    rotateJoint(joints, index, axis, angle) {
        const rotation = Quaternion.RotationAxis(
            Vector3.Cross(axis, joints[index + 1].subtract(joints[index])).normalize(),
            angle
        );

        const rotationMatrix = Matrix.Identity();
        rotation.toRotationMatrix(rotationMatrix);

        // Rotate all subsequent joints
        for (let i = index + 1; i < joints.length; i++) {
            const point = joints[i].subtract(joints[index]);
            const rotated = Vector3.TransformCoordinates(point, rotationMatrix);
            joints[i].copyFrom(joints[index].add(rotated));
        }
    }

    solveChain(chainName, landmarks, bones) {
        const chainDef = this.chainDefinitions[chainName];
        if (!chainDef) {
            console.warn('[IKSolver] Unknown chain:', chainName);
            return null;
        }

        // Convert Vector3 landmarks to MediaPipe format
        const mediapipeLandmarks = landmarks.map((vec3, idx) => ({
            x: vec3._x,
            y: vec3._y,
            z: vec3._z,
            visibility: 1.0,
            index: idx
        }));

        // Process landmarks using MediaPipe indices
        const points = this.extractChainPoints(mediapipeLandmarks, chainDef.landmarks);
        if (!points || points.length < 2) {
            console.warn('[IKSolver] Invalid points for chain:', chainName, {
                pointCount: points?.length,
                required: chainDef.landmarks.length,
                landmarks: mediapipeLandmarks,
                indices: chainDef.landmarks
            });
            return null;
        }

        const boneLengths = this.calculateBoneLengths(points);
        const constraints = this.createChainConstraints(chainDef, bones);

        return this.solveFABRIK(points, points[points.length - 1], boneLengths, constraints);
    }

    extractChainPoints(landmarks, indices) {
        if (!landmarks || !indices || !Array.isArray(landmarks)) {
            console.warn('[IKSolver] Invalid input:', {
                hasLandmarks: !!landmarks,
                hasIndices: !!indices,
                landmarksType: typeof landmarks
            });
            return null;
        }

        // Convert landmarks array to sequential points
        const points = [];
        for (let i = 0; i < indices.length && i < landmarks.length; i++) {
            const landmark = landmarks[i];
            if (!landmark || typeof landmark.x !== 'number' ||
                typeof landmark.y !== 'number' ||
                typeof landmark.z !== 'number') {
                console.warn('[IKSolver] Invalid landmark data at position:', i, landmark);
                return null;
            }

            points.push(new Vector3(landmark.x, landmark.y, landmark.z));
        }

        if (points.length === 0) {
            console.warn('[IKSolver] No valid points extracted from landmarks');
            return null;
        }

        return points;
    }

    calculateBoneLengths(points) {
        const lengths = [];
        for (let i = 0; i < points.length - 1; i++) {
            lengths.push(Vector3.Distance(points[i], points[i + 1]));
        }
        return lengths;
    }

    createChainConstraints(chainDef, bones) {
        if (!chainDef?.constraints || !bones?.length) {
            console.log('[IKSolver] No constraints or bones to process:', {
                hasConstraints: !!chainDef?.constraints,
                bonesLength: bones?.length
            });
            return [];
        }

        return chainDef.constraints.map((constraint, i) => {
            if (!constraint || !bones[i]) return null;
            const bone = bones[i];

            // Get bone's local direction
            const boneDirection = bone.getDirection(constraint.axis);
            if (!boneDirection) {
                console.warn('[IKSolver] Could not get bone direction:', {
                    boneName: bone.name,
                    index: i
                });
                return null;
            }

            return {
                minAngle: constraint.minAngle,
                maxAngle: constraint.maxAngle,
                axis: boneDirection.normalize()
            };
        }).filter(Boolean);
    }

    logSolveStart(joints, target, boneLengths) {
        console.log('[IKSolver] Starting solve:', {
            jointCount: joints.length,
            target: target.toString(),
            boneLengths,
            totalLength: boneLengths.reduce((a, b) => a + b, 0)
        });
    }

    logIterationProgress(iteration, error, joints) {
        if (this.debug && iteration % 5 === 0) {
            console.log('[IKSolver] Iteration progress:', {
                iteration,
                error,
                positions: joints.map(j => j.toString())
            });
        }
    }

    updateMomentum(currentPos, targetPos) {
        const direction = targetPos.subtract(currentPos);
        const speed = direction.length();
        this.momentum.addInPlace(direction.normalize().scale(speed * 0.1));
    }

    applyInertiaSmoothing(prevPositions, currentPositions) {
        return currentPositions.map((pos, i) => {
            const prev = prevPositions[i];
            return new Vector3(
                this.inertiaAlpha * pos.x + (1 - this.inertiaAlpha) * prev.x,
                this.inertiaAlpha * pos.y + (1 - this.inertiaAlpha) * prev.y,
                this.inertiaAlpha * pos.z + (1 - this.inertiaAlpha) * prev.z
            );
        });
    }

    applyJointConstraints(joints, constraints) {
        for (let i = 1; i < joints.length - 1; i++) {
            const prev = joints[i - 1];
            const current = joints[i];
            const next = joints[i + 1];

            // Calculate joint angle
            const v1 = current.subtract(prev);
            const v2 = next.subtract(current);
            const angle = Vector3.GetAngleBetweenVectors(v1, v2, Vector3.Cross(v1, v2));

            // Get joint type and constraints
            const jointType = this.getJointType(i, joints.length);
            const jointConstraints = this.constraints[jointType];

            if (jointConstraints) {
                // Apply angle limits
                const clampedAngle = Math.max(
                    jointConstraints.minAngle * Math.PI / 180,
                    Math.min(jointConstraints.maxAngle * Math.PI / 180, angle)
                );

                if (Math.abs(angle - clampedAngle) > 0.01) {
                    // Rotate the chain to satisfy the constraint
                    this.rotateChainToAngle(joints, i, clampedAngle, v1, v2);
                }
            }
        }
    }

    getJointType(index, totalJoints) {
        // Determine joint type based on position in chain
        if (index === 1) return 'shoulder';
        if (index === 2) return 'elbow';
        if (index === totalJoints - 3) return 'hip';
        if (index === totalJoints - 2) return 'knee';
        return null;
    }

    rotateChainToAngle(joints, jointIndex, targetAngle, v1, v2) {
        const rotationAxis = Vector3.Cross(v1, v2).normalize();
        const currentAngle = Vector3.GetAngleBetweenVectors(v1, v2, rotationAxis);
        const rotationAngle = targetAngle - currentAngle;

        const rotation = Quaternion.RotationAxis(rotationAxis, rotationAngle);
        const rotationMatrix = Matrix.Identity();
        rotation.toRotationMatrix(rotationMatrix);

        // Rotate all joints after the constrained joint
        for (let i = jointIndex + 1; i < joints.length; i++) {
            const point = joints[i].subtract(joints[jointIndex]);
            Vector3.TransformCoordinatesToRef(point, rotationMatrix, point);
            joints[i].copyFrom(joints[jointIndex].add(point));
        }
    }

    // Add new method for batch solving
    solveBatch(jointsBatch, targetsBatch, boneLengthsBatch, constraintsBatch = []) {
        if (!Array.isArray(jointsBatch) || jointsBatch.length === 0) {
            return [];
        }

        const results = [];
        const batchSize = Math.min(this.batchSize, jointsBatch.length);

        for (let i = 0; i < jointsBatch.length; i += batchSize) {
            const batchResults = this.processBatch(
                jointsBatch.slice(i, i + batchSize),
                targetsBatch.slice(i, i + batchSize),
                boneLengthsBatch.slice(i, i + batchSize),
                constraintsBatch.slice(i, i + batchSize)
            );
            results.push(...batchResults);
        }

        return results;
    }

    processBatch(joints, targets, boneLengths, constraints) {
        if (this.useSIMD) {
            return this.processBatchSIMD(joints, targets, boneLengths, constraints);
        }
        return joints.map((j, i) =>
            this.solveFABRIK(j, targets[i], boneLengths[i], constraints[i])
        );
    }
}

export default IKSolver;