// Scene and camera settings
export const CONFIG = {
    CAMERA: {
        // WIDTH: 1280,
        // HEIGHT: 720,
        // FPS: 60,
        VIDEO_FILE: import.meta.env.VITE_VIDEO_FILE_PATH || null
    },
    GLASS_CONFIG: {
        tileHeight: 512, //defines the height of the individual quilt view, the width is then set based on the aspect ratio of the connected device.
        numViews: 45, // the number of views to be rendered
        targetX: 0, // the position of the camera on the x-axis
        targetY: 0, // the position of the camera on the y-axis
        targetZ: 0, // the position of the camera on the z-axis
        targetDiam: 3, // he size of the camera, this makes your scene bigger or smaller without changing the focus.
        fovy: (80 * Math.PI) / 180, // the vertical FOV of your camera (defined in radians)
        depthiness: 0.9 // the depthiness of the scene
    },
    // MediaPipe settings
    HAND_TRACKING: {
        maxNumHands: 2,
        modelComplexity: 1,
        minDetectionConfidence: 0.5,
        minTrackingConfidence: 0.5
    },

    // Gesture detection
    GESTURE: {
        PUNCH_VELOCITY_THRESHOLD: 0.3,
        ROTATION_SENSITIVITY: 0.5,
        MIN_CONFIDENCE: 0.7
    },

    // Audio settings
    AUDIO: {
        PUNCH_VOLUME_SCALE: 0.8,
        SPATIAL_AUDIO_ENABLED: true
    },

    // Debug settings
    DEBUG: {
        SHOW_HAND_MESH: false,
        SHOW_LANDMARKS: false,
        LOG_GESTURES: false
    },

    // Draco compression settings
    DRACO: {
        // Path to Draco decoder/encoder
        DRACO_URL: 'https://www.gstatic.com/draco/versioned/decoders/1.5.7/',
        
        // Compression settings
        // this will make the previous glb mesh file not readable without the draco decoder
        COMPRESSION: {
            AUTO_CONVERT: true // Automatically convert non-Draco meshes to Draco format
        }
    },

    RELOAD_OPTIONS: {
        reloadMeshes: false,  // Whether to replace existing meshes with newly added ones
        reloadEnvironment: true  // Whether to reload the environment when changed
    }
};

// Load environment variables if available
if (import.meta.env.VITE_DEBUG_MODE) {
    CONFIG.DEBUG.SHOW_HAND_MESH = true;
    CONFIG.DEBUG.SHOW_LANDMARKS = true;
}
