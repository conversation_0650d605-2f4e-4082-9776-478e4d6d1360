import { MeshSelector } from '../../src/ui/components/MeshSelector.js';
import { CameraViewer } from '../../src/ui/components/cameraViewer.js';
import { VRButton } from "three/examples/jsm/webxr/VRButton.js";
import {
    transcribeAudioWithSTT,
    createOptimizedRecordingOptions,
    handleTranscriptionWorkflow
} from '../../src/utils/audioTranscription.js';

/**
 * UI Settings and Controls for the Viewer
 *
 * Manages UI components, layouts, and control settings
 * for the viewer application.
 */
export class UISettings {
    constructor(viewer) {
        this.viewer = viewer;
        this.container = viewer?.container;

        // Store references to UI components
        this.components = {
            progressContainer: null,
            meshSelector: null,
            cameraViewer: null,
            actionsMenu: null
        };

        // UI state
        this.state = {
            isGenerating: false,
            debugMode: false
        };

        // Initialize notification system
        this.setupNotifications();

        // Add UI components for gesture controls
        this.gestureUI = {
            scalingIndicatorCreated: false,
            zoomSliderCreated: false
        };
    }

    /**
     * Initialize UI components and controls
     */
    initialize() {
        if (!this.container) {
            console.error('[UISettings] Container not found');
            return false;
        }

        // Create progress container
        this.setupProgressContainer();

        // // Add conversion controls
        // this.setupConversionUI();

        // // Add Ready Player Me button
        // this.setupReadyPlayerMeButton();

        // Add actions menu (replaces individual buttons)
        this.setupActionsMenu();

        return true;
    }

    /**
     * Initialize UI components from config
     */
    async initializeComponents(config) {
        console.log('[UISettings] Initializing components from config');

        // Initialize MeshSelector component
        if (config?.props?.MeshSelector) {
            await this.initializeMeshSelector(config.props.MeshSelector);
        }

        // Initialize CameraViewer component with embedded preference
        if (config?.props?.CameraViewer) {
            // Set preferPopup to false to use embedded corner mode by default
            const cameraViewerConfig = {
                ...config.props.CameraViewer,
                preferPopup: false // Use embedded corner mode instead of popup
            };
            await this.initializeCameraViewer(cameraViewerConfig);
        }

        return true;
    }
    /**
     * Setup actions menu that combines multiple action buttons
     */
    setupActionsMenu() {
        if (!this.container) return;

        // Create menu container
        const menuContainer = document.createElement('div');
        menuContainer.className = 'actions-menu-container';

        // Create main menu button
        const menuButton = document.createElement('button');
        menuButton.className = 'actions-menu-button';
        menuButton.innerHTML = '<span>⚙️</span>';
        menuButton.title = 'Actions Menu';

        // Add inline styles to ensure button is clickable
        menuButton.style.cssText = `
            width: 42px;
            height: 42px;
            border-radius: 50%;
            background-color: rgba(0, 120, 255, 0.7);
            border: none;
            color: white;
            font-size: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: background-color 0.3s ease;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
            z-index: 1001;
            position: relative;
        `;

        // Create dropdown menu
        const dropdownMenu = document.createElement('div');
        dropdownMenu.className = 'actions-dropdown';

        // Add inline styles to ensure dropdown is properly visible and clickable
        dropdownMenu.style.cssText = `
            position: absolute;
            top: 52px;
            left: 0;
            background-color: rgba(30, 30, 30, 0.85);
            border-radius: 8px;
            width: 180px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
            opacity: 0;
            transform: translateY(-10px);
            pointer-events: none;
            transition: all 0.3s ease;
            z-index: 1000;
        `;

        // Create menu items
        const convertMenuItem = document.createElement('div');
        convertMenuItem.className = 'menu-item';
        convertMenuItem.innerHTML = '<span>🔄</span> Any to 3D';
        convertMenuItem.style.cssText = `
            padding: 12px 16px;
            color: white;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 8px;
            cursor: pointer;
            transition: background-color 0.2s ease;
        `;

        const avatarMenuItem = document.createElement('div');
        avatarMenuItem.className = 'menu-item';
        avatarMenuItem.innerHTML = '<span>👤</span> ReadyPlayerMe';
        avatarMenuItem.style.cssText = `
            padding: 12px 16px;
            color: white;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 8px;
            cursor: pointer;
            transition: background-color 0.2s ease;
        `;

        // Create gesture control menu item
        const gestureMenuItem = document.createElement('div');
        gestureMenuItem.className = 'menu-item';
        gestureMenuItem.innerHTML = '<span>👋</span> Gesture Controls';
        gestureMenuItem.title = 'Toggle Gesture Controls';
        gestureMenuItem.style.cssText = `
            padding: 12px 16px;
            color: white;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 8px;
            cursor: pointer;
            transition: background-color 0.2s ease;
        `;

        // Create QR code menu item for mobile control
        const qrCodeMenuItem = document.createElement('div');
        qrCodeMenuItem.className = 'menu-item';
        qrCodeMenuItem.innerHTML = '<span>📱</span> Mobile Control';
        qrCodeMenuItem.title = 'Connect Mobile Device';
        qrCodeMenuItem.style.cssText = `
            padding: 12px 16px;
            color: white;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 8px;
            cursor: pointer;
            transition: background-color 0.2s ease;
        `;

        // We'll check if the WebSocket server is available when the viewer is initialized
        // For now, we'll create the menu item but we'll update its state later

        // Add VR button from base or viewer if available
        let vrMenuItemAdded = false;

        // Try to find VR button from different sources
        let vrButton = null;

        if (this.viewer && this.viewer.vrButton) {
            console.log('[UISettings] Found VR button in viewer');
            vrButton = this.viewer.vrButton;
        } else if (document.getElementById('VRButton')) {
            console.log('[UISettings] Found VR button in DOM');
            vrButton = document.getElementById('VRButton');
        }

        if (vrButton) {
            this.addVRMenuOption(vrButton, dropdownMenu);
            vrMenuItemAdded = true;
        }

        if (!vrMenuItemAdded) {
            console.warn('[UISettings] No VR button found to add to menu');
        }

        // Add menu items to dropdown
        dropdownMenu.appendChild(convertMenuItem);
        dropdownMenu.appendChild(avatarMenuItem);
        dropdownMenu.appendChild(gestureMenuItem);
        dropdownMenu.appendChild(qrCodeMenuItem);

        // Add elements to container
        menuContainer.appendChild(menuButton);
        menuContainer.appendChild(dropdownMenu);

        // Add to document
        this.container.appendChild(menuContainer);

        // Add styles for menu
        this.addActionsMenuStyles();

        // Setup button handlers and prep modals

        // Create conversion modal but don't add the button
        this.setupConversionUI(false);

        // Setup click events
        menuButton.addEventListener('click', () => {
            // Toggle visible class
            dropdownMenu.classList.toggle('visible');

            // Apply inline styles when visible to ensure it's clickable
            if (dropdownMenu.classList.contains('visible')) {
                dropdownMenu.style.opacity = '1';
                dropdownMenu.style.transform = 'translateY(0)';
                dropdownMenu.style.pointerEvents = 'all';
            } else {
                dropdownMenu.style.opacity = '0';
                dropdownMenu.style.transform = 'translateY(-10px)';
                dropdownMenu.style.pointerEvents = 'none';
            }

            // Send UI state to mobile devices
            if (this.viewer && typeof this.viewer._sendUIState === 'function') {
                this.viewer._sendUIState();
            }
        });

        // Close menu when clicking outside
        document.addEventListener('click', (e) => {
            if (!menuContainer.contains(e.target)) {
                dropdownMenu.classList.remove('visible');
                dropdownMenu.style.opacity = '0';
                dropdownMenu.style.transform = 'translateY(-10px)';
                dropdownMenu.style.pointerEvents = 'none';
            }
        });

        // Add hover effects to all menu items
        const addHoverEffect = (menuItem) => {
            menuItem.addEventListener('mouseenter', () => {
                menuItem.style.backgroundColor = 'rgba(0, 120, 255, 0.5)';
            });
            menuItem.addEventListener('mouseleave', () => {
                menuItem.style.backgroundColor = 'transparent';
            });
        };

        // Apply hover effects
        addHoverEffect(convertMenuItem);
        addHoverEffect(avatarMenuItem);
        addHoverEffect(gestureMenuItem);
        addHoverEffect(qrCodeMenuItem);

        // Convert to 3D functionality
        convertMenuItem.addEventListener('click', () => {
            dropdownMenu.classList.remove('visible');
            dropdownMenu.style.opacity = '0';
            dropdownMenu.style.transform = 'translateY(-10px)';
            dropdownMenu.style.pointerEvents = 'none';

            // Find and show the conversion modal
            const modal = document.querySelector('.conversion-modal');
            if (modal) {
                modal.classList.remove('hidden');

                // Reset form fields (copied from the original handler)
                const textInput = modal.querySelector('.text-input');
                const fileInput = modal.querySelector('.file-input');
                const preview = modal.querySelector('.preview');
                const sourceSelect = modal.querySelector('.source-select');
                const cameraButton = modal.querySelector('.camera-button');
                const genderWrapper = modal.querySelector('.gender-wrapper');

                if (textInput) textInput.value = '';
                if (fileInput) fileInput.value = '';
                if (preview) preview.innerHTML = '';

                // Always hide camera button and gender selector initially
                if (cameraButton) cameraButton.classList.add('hidden');
                if (genderWrapper) genderWrapper.classList.add('hidden');

                if (sourceSelect) {
                    sourceSelect.value = 'text';
                    if (textInput) textInput.classList.remove('hidden');
                    if (fileInput) fileInput.classList.add('hidden');

                    // Double-check that camera and gender are hidden for Text to 3D
                    if (cameraButton && sourceSelect.value === 'text') {
                        cameraButton.classList.add('hidden');
                    }
                    if (genderWrapper && sourceSelect.value === 'text') {
                        genderWrapper.classList.add('hidden');
                    }

                    // Trigger the change event to ensure proper UI state
                    const event = new Event('change');
                    sourceSelect.dispatchEvent(event);
                }
            }
        });

        // Ready Player Me functionality
        avatarMenuItem.addEventListener('click', () => {
            dropdownMenu.classList.remove('visible');
            dropdownMenu.style.opacity = '0';
            dropdownMenu.style.transform = 'translateY(-10px)';
            dropdownMenu.style.pointerEvents = 'none';
            this.viewer.openReadyPlayerMe();
        });

        // Gesture control functionality
        gestureMenuItem.addEventListener('click', () => {
            dropdownMenu.classList.remove('visible');
            dropdownMenu.style.opacity = '0';
            dropdownMenu.style.transform = 'translateY(-10px)';
            dropdownMenu.style.pointerEvents = 'none';

            // Toggle gesture controls
            const isActive = this.viewer.toggleGestureControls();

            // Update menu item appearance based on state
            this.updateGestureMenuItem(gestureMenuItem, isActive);
        });

        // QR code functionality
        qrCodeMenuItem.addEventListener('click', () => {
            dropdownMenu.classList.remove('visible');
            dropdownMenu.style.opacity = '0';
            dropdownMenu.style.transform = 'translateY(-10px)';
            dropdownMenu.style.pointerEvents = 'none';

            // First check if mobile mode is enabled
            if (!window.isMobileEnabled) {
                this.showNotification('Mobile control is not available. Use "npm run launch:mobile viewer dev" to enable mobile support.', true, 5000);
                return;
            }

            // QR code display functionality removed - using realtime audio mode
            this.showNotification('Mobile QR connection disabled. Using realtime audio mode for interaction.', false, 3000);
        });

        // Store references
        this.components.actionsMenu = {
            container: menuContainer,
            button: menuButton,
            dropdown: dropdownMenu,
            gestureMenuItem: gestureMenuItem,
            qrCodeMenuItem: qrCodeMenuItem
        };

        // Update the gesture control menu item based on state
        this.updateGestureMenuItem = (menuItem, isActive) => {
            if (!menuItem) return;

            if (isActive) {
                menuItem.classList.add('active');
                menuItem.innerHTML = '<span>👋</span> Gesture Controls (On)';
                menuItem.style.color = '#4CAF50'; // Green color for active state
            } else {
                menuItem.classList.remove('active');
                menuItem.innerHTML = '<span>👋</span> Gesture Controls';
                menuItem.style.color = 'white'; // Default color
            }
        };

        // Update the mobile control menu item when the QR code display is initialized
        // This will be called from the viewer when the QR code display is ready
        this.updateMobileControlMenuItem = (isAvailable) => {
            if (!qrCodeMenuItem) return;

            if (!isAvailable) {
                // Disable the menu item
                qrCodeMenuItem.classList.add('disabled');
                qrCodeMenuItem.style.opacity = '0.5';
                qrCodeMenuItem.style.cursor = 'not-allowed';

                // Check if we're not in mobile mode or if the WebSocket server is not available
                if (!window.isMobileEnabled) {
                    qrCodeMenuItem.title = 'Mobile control not available. Use "npm run launch:mobile viewer dev" to enable.';
                    qrCodeMenuItem.innerHTML = '<span>📱</span> Mobile Control (Use launch:mobile)';
                } else {
                    qrCodeMenuItem.title = 'WebSocket server not available. Check if the server is running on port 3000.';
                    qrCodeMenuItem.innerHTML = '<span>📱</span> Mobile Control (Server Error)';
                }
            } else {
                // Enable the menu item
                qrCodeMenuItem.classList.remove('disabled');
                qrCodeMenuItem.style.opacity = '1';
                qrCodeMenuItem.style.cursor = 'pointer';
                qrCodeMenuItem.title = 'Connect Mobile Device';
                qrCodeMenuItem.innerHTML = '<span>📱</span> Mobile Control';
            }
        };
    }

    /**
     * Adds a VR menu option that triggers the VR button
     * @param {HTMLElement} vrButton - The VR button element from base
     * @param {HTMLElement} dropdownMenu - The dropdown menu to add the option to
     */
    addVRMenuOption(vrButton, dropdownMenu) {
        if (!vrButton) return;

        // Create VR menu item
        const vrMenuItem = document.createElement('div');
        vrMenuItem.className = 'menu-item';

        // Create icon span
        const iconSpan = document.createElement('span');
        iconSpan.textContent = '🥽';
        iconSpan.style.marginRight = '8px';

        // Create text span - ensure styling matches other menu items
        const textSpan = document.createElement('span');
        textSpan.style.fontSize = '14px'; // Match font size with other menu items
        textSpan.style.fontFamily = 'inherit'; // Use same font as parent
        textSpan.style.fontWeight = 'normal'; // Use normal font weight

        // Check the current text content and modify based on session state
        const buttonText = vrButton.textContent || '';
        if (buttonText.includes('EXIT')) {
            textSpan.textContent = 'Exit Glass';
        } else {
            textSpan.textContent = 'Enter Glass';
        }

        // Remove custom class that might be affecting styling
        // textSpan.className = 'vr-button-text';

        // Create a MutationObserver to watch for text changes on the original button
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'childList' || mutation.type === 'characterData') {
                    const newText = vrButton.textContent || '';
                    if (newText.includes('EXIT')) {
                        textSpan.textContent = 'Exit Glass';
                    } else {
                        textSpan.textContent = 'Enter Glass';
                    }
                }
            });
        });

        // Start observing the button
        observer.observe(vrButton, {
            childList: true,
            characterData: true,
            subtree: true
        });

        vrMenuItem.appendChild(iconSpan);
        vrMenuItem.appendChild(textSpan);

        // Add click handler that triggers the original VR button
        vrMenuItem.addEventListener('click', () => {
            console.log('[UISettings] VR menu item clicked, triggering original button');
            vrButton.click();

            // Update text immediately after clicking
            setTimeout(() => {
                const currentText = vrButton.textContent || '';
                if (currentText.includes('EXIT')) {
                    textSpan.textContent = 'Exit Glass';
                } else {
                    textSpan.textContent = 'Enter Glass';
                }
            }, 100);

            dropdownMenu.classList.remove('visible');
        });

        // Add to dropdown menu
        dropdownMenu.appendChild(vrMenuItem);

        console.log('[UISettings] VR menu item added to actions menu');
    }

    /**
     * Add styles for actions menu
     */
    addActionsMenuStyles() {
        if (!document.getElementById('actions-menu-styles')) {
            const style = document.createElement('style');
            style.id = 'actions-menu-styles';
            style.textContent = `
                .actions-menu-container {
                    position: fixed;
                    top: 20px;
                    left: 20px;
                    z-index: 1000;
                }

                .actions-menu-button {
                    width: 42px;
                    height: 42px;
                    border-radius: 50%;
                    background-color: rgba(0, 120, 255, 0.7);
                    border: none;
                    color: white;
                    font-size: 20px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    cursor: pointer;
                    transition: background-color 0.3s ease;
                    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
                }

                .actions-menu-button:hover {
                    background-color: rgba(0, 120, 255, 0.9);
                }

                .actions-dropdown {
                    position: absolute;
                    top: 52px; /* Changed from bottom: 52px to top: 52px */
                    left: 0;   /* Changed from right: 0 to left: 0 */
                    background-color: rgba(30, 30, 30, 0.85);
                    border-radius: 8px;
                    width: 180px;
                    overflow: hidden;
                    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
                    opacity: 0;
                    transform: translateY(-10px); /* Changed to move up instead of down */
                    pointer-events: none;
                    transition: all 0.3s ease;
                }

                .actions-dropdown.visible {
                    opacity: 1;
                    transform: translateY(0);
                    pointer-events: all;
                }

                .menu-item {
                    padding: 12px 16px;
                    color: white;
                    font-size: 14px;
                    display: flex;
                    align-items: center;
                    gap: 8px;
                    cursor: pointer;
                    transition: background-color 0.2s ease;
                }

                .menu-item:hover {
                    background-color: rgba(0, 120, 255, 0.5);
                }

                .menu-item span {
                    font-size: 16px;
                }

                /* Microphone button styles */
                .microphone-button {
                    display: flex;
                    align-items: center;
                    gap: 8px;
                    padding: 10px 16px;
                    margin: 10px 0;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    border: none;
                    border-radius: 8px;
                    font-size: 14px;
                    font-weight: 500;
                    cursor: pointer;
                    transition: all 0.3s ease;
                    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
                }

                .microphone-button:hover:not(.recording) {
                    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
                    transform: translateY(-2px);
                    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
                }

                .microphone-button.recording {
                    background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);
                    animation: pulse 1.5s ease-in-out infinite alternate;
                }

                .microphone-button.recording:hover {
                    background: linear-gradient(135deg, #ff2f5a 0%, #ff3f1f 100%);
                }

                .microphone-button .mic-icon {
                    width: 20px;
                    height: 20px;
                    flex-shrink: 0;
                }

                .microphone-button.hidden {
                    display: none;
                }

                @keyframes pulse {
                    0% {
                        box-shadow: 0 2px 8px rgba(255, 65, 108, 0.3);
                    }
                    100% {
                        box-shadow: 0 4px 16px rgba(255, 65, 108, 0.6);
                    }
                }
            `;
            document.head.appendChild(style);
        }
    }

    setupReadyPlayerMeButton() {
        // Create avatar button
        const avatarButton = document.createElement('button');
        avatarButton.className = 'rpm-avatar-button';
        avatarButton.innerHTML = '<span>👤</span> Create Avatar';
        avatarButton.title = 'Create Custom Avatar';

        // Add styles
        avatarButton.style.cssText = `
            position: fixed;
            bottom: 70px;
            right: 20px;
            padding: 8px 12px;
            border-radius: 20px;
            background-color: rgba(0, 120, 255, 0.7);
            border: none;
            color: white;
            font-size: 14px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 6px;
            cursor: pointer;
            z-index: 1000;
            transition: background-color 0.3s ease;
        `;

        // Add hover effect
        avatarButton.addEventListener('mouseenter', () => {
            avatarButton.style.backgroundColor = 'rgba(0, 120, 255, 0.9)';
        });

        avatarButton.addEventListener('mouseleave', () => {
            avatarButton.style.backgroundColor = 'rgba(0, 120, 255, 0.7)';
        });

        // Add click handler to open Ready Player Me
        avatarButton.addEventListener('click', () => {
            this.viewer.openReadyPlayerMe();
        });

        // Add to container
        this.container.appendChild(avatarButton);

        // Store reference
        this.components.readyPlayerMeButton = avatarButton;
    }
    /**
     * Initialize MeshSelector component
     */
    async initializeMeshSelector(config) {
        try {
            console.log('[UISettings] Initializing MeshSelector with config:', config);

            this.components.meshSelector = new MeshSelector(
                this.viewer,
                config.meshPath,
                config.meshFormat,
                config.enableDelete
            );

            console.log('[UISettings] MeshSelector initialized successfully');
            return this.components.meshSelector;
        } catch (error) {
            console.error('[UISettings] Failed to initialize MeshSelector:', error);
            return null;
        }
    }

    /**
     * Initialize CameraViewer component
     */
    async initializeCameraViewer(config) {
        try {
            console.log('[UISettings] Initializing CameraViewer');

            // Clean up existing instance if present
            if (this.components.cameraViewer) {
                this.components.cameraViewer.dispose();
                this.components.cameraViewer = null;
            }

            // Get or create video element - ensure it exists FIRST
            let videoElement = this.viewer?.inputManager?.videoElement;
            if (!videoElement) {
                console.log('[UISettings] Creating new video element for CameraViewer');
                videoElement = document.createElement('video');
                videoElement.id = 'input-video';
                videoElement.autoplay = true;
                videoElement.playsInline = true;
                videoElement.muted = true;
                videoElement.style.display = 'none';
                document.body.appendChild(videoElement);

                // Store in viewer's inputManager
                if (this.viewer) {
                    if (!this.viewer.inputManager) {
                        this.viewer.inputManager = {};
                    }
                    this.viewer.inputManager.videoElement = videoElement;
                }
            }

            // Create new camera viewer instance with simplified settings
            this.components.cameraViewer = new CameraViewer(this.viewer, {
                width: 640,
                height: 480,
                preferPopup: false, // Use corner mode by default
                modalConfig: {
                    maxWidth: '90vw',
                    maxHeight: '90vh',
                    minWidth: '320px',
                    minHeight: '180px'
                }
            });

            // Initialize with video element
            const canvas = await this.components.cameraViewer.initialize(videoElement);
            console.log('[UISettings] CameraViewer initialized with canvas:', canvas ? 'Success' : 'Failed');

            // Set up camera callbacks
            this.setupCameraCallbacks();

            return this.components.cameraViewer;
        } catch (error) {
            console.error('[UISettings] Failed to initialize CameraViewer:', error);
            return null;
        }
    }

    /**
     * Setup callbacks for camera viewer events
     */
    setupCameraCallbacks() {
        if (!this.components.cameraViewer) return;

        this.components.cameraViewer.setViewerCallbacks(
            // On camera open
            (canvas) => {
                console.log('[UISettings] Camera opened');

                // Notify any listeners about camera state
                if (this.viewer.gestureController) {
                    // Give a moment for the camera to fully initialize
                    setTimeout(() => {
                        this.viewer.gestureController.validateComponents();
                    }, 500);
                }
            },
            // On camera close
            () => {
                console.log('[UISettings] Camera closed');
            }
        );
    }


    /**
     * Get reference to the camera viewer component
     */
    getCameraViewer() {
        return this.components.cameraViewer;
    }

    /**
     * Setup progress container
     */
    setupProgressContainer() {
        const template = `
            <div class="progress-fill"></div>
            <div class="progress-text">Generating: 0%</div>
            <div class="status-text">Starting...</div>
            <button class="cancel-button">Cancel</button>
        `;

        // Create progress container
        const progressContainer = document.createElement('div');
        progressContainer.className = 'progress-container';
        progressContainer.innerHTML = template;

        // Add cancel handler
        const cancelButton = progressContainer.querySelector('.cancel-button');
        cancelButton.addEventListener('click', () => {
            if (this.state.isGenerating) {
                this.viewer.cancelGeneration();
            }
        });

        document.body.appendChild(progressContainer);

        // Store reference
        this.components.progressContainer = {
            container: progressContainer,
            bar: progressContainer.querySelector('.progress-fill'),
            text: progressContainer.querySelector('.progress-text'),
            status: progressContainer.querySelector('.status-text'),
            cancelButton: cancelButton
        };
    }

    /**
     * Setup conversion UI for the viewer
     * @param {boolean} addButton - Whether to add the standalone button (defaults to true)
     */
    setupConversionUI(addButton = true) {
        if (!this.container) return;

        // Create conversion modal
        const modal = document.createElement('div');
        modal.className = 'conversion-modal hidden';

        // Create modal content
        const content = document.createElement('div');
        content.className = 'conversion-content';

        // Create source type wrapper for horizontal layout
        const sourceWrapper = document.createElement('div');
        sourceWrapper.className = 'source-wrapper';

        // Source type selector
        const sourceSelect = document.createElement('select');
        sourceSelect.className = 'source-select';
        sourceSelect.innerHTML = `
            <option value="text">Text to 3D</option>
            <option value="textto3d">Text to 3D (Dedicated)</option>
            <option value="image">Image to 3D</option>
            <option value="doll">Doll to 3D</option>
            <option value="tripo-doll">Tripo Doll</option>
        `;

        // Create microphone button for ASR (initially hidden)
        const microphoneButton = document.createElement('button');
        microphoneButton.className = 'microphone-button hidden';
        microphoneButton.type = 'button';
        microphoneButton.innerHTML = `
            <svg class="mic-icon" viewBox="0 0 24 24" width="20" height="20" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M12 2a3 3 0 0 0-3 3v6a3 3 0 0 0 6 0V5a3 3 0 0 0-3-3z"/>
                <path d="M19 10v2a7 7 0 0 1-14 0v-2"/>
                <line x1="12" y1="19" x2="12" y2="23"/>
                <line x1="8" y1="23" x2="16" y2="23"/>
            </svg>
            <span class="mic-text">Voice Input</span>
        `;
        microphoneButton.title = 'Record audio for text generation';

        // Add recording state management
        microphoneButton.isRecording = false;
        microphoneButton.mediaRecorder = null;
        microphoneButton.audioChunks = [];

        // Create regenerate checkbox wrapper with icon
        const checkboxWrapper = document.createElement('div');
        checkboxWrapper.className = 'regenerate-wrapper';

        const regenerateCheckbox = document.createElement('input');
        regenerateCheckbox.type = 'checkbox';
        regenerateCheckbox.id = 'regenerate-checkbox';
        regenerateCheckbox.className = 'regenerate-checkbox';

        // Set reference to checkbox for viewer access
        this.viewer.regenerateCheckbox = regenerateCheckbox;

        // Add change listener to regenerate checkbox
        regenerateCheckbox.addEventListener('change', (e) => {
            if (e.target.checked) {
                console.log('[UI] Regenerate enabled - will use random seed on next generation');
            } else {
                console.log('[UI] Regenerate disabled - will use default seed on next generation');
            }
        });

        const checkboxLabel = document.createElement('label');
        checkboxLabel.htmlFor = 'regenerate-checkbox';
        checkboxLabel.className = 'regenerate-label';
        // Using a refresh/sync icon
        checkboxLabel.innerHTML = `
            <svg class="regenerate-icon" viewBox="0 0 24 24" width="18" height="18">
                <path d="M12 4V1L8 5l4 4V6c3.31 0 6 2.69 6 6 0 1.01-.25 1.97-.7 2.8l1.46 1.46C19.54 15.03 20 13.57 20 12c0-4.42-3.58-8-8-8zm0 14c-3.31 0-6-2.69-6-6 0-1.01.25-1.97.7-2.8L5.24 7.74C4.46 8.97 4 10.43 4 12c0 4.42 3.58 8 8 8v3l4-4-4-4v3z"/>
            </svg>
            <span class="tooltip">Regenerate with new seed</span>
        `;

        // Add elements to their containers
        checkboxWrapper.appendChild(regenerateCheckbox);
        checkboxWrapper.appendChild(checkboxLabel);

        sourceWrapper.appendChild(sourceSelect);
        sourceWrapper.appendChild(checkboxWrapper);

        // Input area
        const inputArea = document.createElement('div');
        inputArea.className = 'input-area';

        const textInput = document.createElement('textarea');
        textInput.placeholder = 'Enter description for 3D model...';
        textInput.className = 'text-input';

        const fileInput = document.createElement('input');
        fileInput.type = 'file';
        fileInput.accept = 'image/*,.glb';
        fileInput.className = 'file-input hidden';

        const preview = document.createElement('div');
        preview.className = 'preview';

        // Buttons
        const generateButton = document.createElement('button');
        generateButton.textContent = 'Generate';
        generateButton.className = 'generate-button';

        const cancelButton = document.createElement('button');
        cancelButton.textContent = 'Cancel';
        cancelButton.className = 'cancel-button';

        // Add elements to modal
        inputArea.appendChild(textInput);
        inputArea.appendChild(fileInput);
        inputArea.appendChild(preview);
        inputArea.appendChild(microphoneButton);

        content.appendChild(sourceWrapper);
        content.appendChild(inputArea);
        content.appendChild(generateButton);
        content.appendChild(cancelButton);

        modal.appendChild(content);
        this.container.appendChild(modal);

        // Only add the standalone button if specified
        if (addButton) {
            // Add conversion button to main UI
            const convertButton = document.createElement('button');
            convertButton.textContent = 'Convert to 3D';
            convertButton.className = 'convert-button';

            // Event handlers
            this.setupConversionEventHandlers(
                modal, sourceSelect, textInput, fileInput,
                preview, generateButton, cancelButton, convertButton, inputArea, microphoneButton
            );

            this.container.appendChild(convertButton);
        } else {
            // Setup event handlers without the standalone button
            this.setupConversionEventHandlers(
                modal, sourceSelect, textInput, fileInput,
                preview, generateButton, cancelButton, null, inputArea, microphoneButton
            );
        }
    }

    /**
     * Setup event handlers for conversion UI
     */
    setupConversionEventHandlers(
        modal, sourceSelect, textInput, fileInput,
        preview, generateButton, cancelButton, convertButton, inputArea, microphoneButton
    ) {
        // Create camera button for tripo-doll with improved styling
        const cameraButton = document.createElement('button');
        cameraButton.className = 'camera-button hidden'; // Start hidden by default
        cameraButton.innerHTML = '<svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M23 19a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h4l2-3h6l2 3h4a2 2 0 0 1 2 2z"></path><circle cx="12" cy="13" r="4"></circle></svg> Use Camera';
        cameraButton.title = 'Take a photo with your camera';
        // Styles are now in CSS

        // Create gender selector for tripo-doll with improved styling
        const genderWrapper = document.createElement('div');
        genderWrapper.className = 'gender-wrapper hidden'; // Start hidden by default
        // Styles are now in CSS

        const genderLabel = document.createElement('label');
        genderLabel.textContent = 'Gender:';
        genderLabel.htmlFor = 'gender-select';
        // Styles are now in CSS

        const genderSelect = document.createElement('select');
        genderSelect.className = 'gender-select';
        genderSelect.id = 'gender-select';
        genderSelect.innerHTML = `
            <option value="boy">Boy</option>
            <option value="girl">Girl</option>
        `;
        // Styles are now in CSS

        genderWrapper.appendChild(genderLabel);
        genderWrapper.appendChild(genderSelect);

        // Don't add camera button and gender selector to input area initially
        // They will be added only when Tripo Doll is selected

        // Source select change handler
        sourceSelect.addEventListener('change', () => {
            const source = sourceSelect.value;
            console.log('[UISettings] Source changed to:', source);

            // First, remove all UI elements from the DOM to ensure they don't appear
            if (cameraButton.parentNode === inputArea) {
                inputArea.removeChild(cameraButton);
            }

            if (genderWrapper.parentNode === inputArea) {
                inputArea.removeChild(genderWrapper);
            }

            // Hide all input elements first
            textInput.classList.add('hidden');
            fileInput.classList.add('hidden');

            // Show appropriate elements based on source
            if (source === 'text' || source === 'textto3d') {
                textInput.classList.remove('hidden');

                // Show microphone button only for textto3d (dedicated service)
                if (source === 'textto3d') {
                    microphoneButton.classList.remove('hidden');
                } else {
                    microphoneButton.classList.add('hidden');
                }
            } else {
                fileInput.classList.remove('hidden');
                microphoneButton.classList.add('hidden');
            }

            // ONLY add camera button and gender selector for Tripo Doll
            if (source === 'tripo-doll') {
                // Add camera button back to the DOM
                inputArea.appendChild(cameraButton);
                cameraButton.classList.remove('hidden');
                cameraButton.innerHTML = '<svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M23 19a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h4l2-3h6l2 3h4a2 2 0 0 1 2 2z"></path><circle cx="12" cy="13" r="4"></circle></svg> Take Tripo Doll Photo';

                // Add gender selector back to the DOM
                inputArea.appendChild(genderWrapper);
                genderWrapper.classList.remove('hidden');

                console.log('[UISettings] Camera button and gender selector added for Tripo Doll');
            } else {
                console.log('[UISettings] Camera button and gender selector not added for source:', source);
            }

            // Set appropriate file accept types based on source
            switch (source) {
                case 'image':
                    fileInput.accept = 'image/*';
                    break;
                case 'doll':
                    fileInput.accept = 'image/*';  // Doll to 3D also accepts images
                    break;
                case 'tripo-doll':
                    fileInput.accept = 'image/*';  // Tripo Doll accepts images
                    break;
                default:
                    fileInput.accept = '';
            }

            // Clear preview and file input
            preview.innerHTML = '';
            fileInput.value = '';

            // Find the modal content element
            const modalContent = modal.querySelector('.conversion-content');
            if (modalContent) {
                // Add a title to the modal based on the selected source
                const modalTitle = modalContent.querySelector('.modal-title') || document.createElement('h2');
                modalTitle.className = 'modal-title';
                modalTitle.style.cssText = `
                    margin: 0 0 15px 0;
                    font-size: 20px;
                    font-weight: 600;
                    color: #333;
                    text-align: center;
                `;

                // Set title based on source
                switch (source) {
                    case 'text':
                        modalTitle.textContent = 'Text to 3D';
                        break;
                    case 'textto3d':
                        modalTitle.textContent = 'Text to 3D (Dedicated)';
                        break;
                    case 'image':
                        modalTitle.textContent = 'Image to 3D';
                        break;
                    case 'doll':
                        modalTitle.textContent = 'Doll to 3D';
                        break;
                    case 'tripo-doll':
                        modalTitle.textContent = 'Tripo Doll';
                        break;
                    default:
                        modalTitle.textContent = 'Any to 3D';
                }

                // Add title to content if it doesn't exist
                if (!modalContent.querySelector('.modal-title')) {
                    modalContent.insertBefore(modalTitle, modalContent.firstChild);
                }
            }
        });

        // File input change handler
        fileInput.addEventListener('change', (e) => {
            const file = e.target.files[0];
            if (file) {
                if (file.type.startsWith('image/')) {
                    const reader = new FileReader();
                    reader.onload = (e) => {
                        preview.innerHTML = `<img src="${e.target.result}" alt="Preview">`;
                    };
                    reader.readAsDataURL(file);
                } else {
                    preview.innerHTML = `<div class="file-name">${file.name}</div>`;
                }
            }
        });

        // Camera button click handler
        cameraButton.addEventListener('click', async () => {
            // Check if camera is available
            if (!this.components.cameraViewer) {
                console.error('[UISettings] Camera viewer not available');
                this.showNotification('Camera not available', true);
                return;
            }

            // Get the current source type
            const source = sourceSelect.value;

            // Only allow camera for Tripo Doll
            if (source !== 'tripo-doll') {
                console.error('[UISettings] Camera not available for this source type');
                this.showNotification('Camera is only available for Tripo Doll', true);
                return;
            }

            const isTripoDoll = source === 'tripo-doll';

            try {
                // Create a promise to get the captured photo
                const capturePromise = new Promise((resolve, reject) => {
                    // Start camera
                    this.components.cameraViewer.startCamera()
                        .then(stream => {
                            console.log('[UISettings] Camera started successfully, stream active:', stream && stream.active);

                            // Verify stream has video tracks
                            if (!stream || !stream.getVideoTracks || stream.getVideoTracks().length === 0) {
                                reject(new Error('No video tracks available in stream'));
                                return;
                            }

                            console.log('[UISettings] Stream has', stream.getVideoTracks().length, 'video tracks');

                            // Create centered embedded camera modal instead of popup
                            const cameraModal = this.createCenteredCameraModal(stream, isTripoDoll, genderSelect, resolve, reject);

                            if (!cameraModal) {
                                reject(new Error('Failed to create camera modal'));
                                return;
                            }


                        })
                        .catch(error => {
                            console.error('[UISettings] Failed to start camera:', error);
                            reject(error);
                        });
                });

                // Wait for capture
                const captureResult = await capturePromise;

                // Update preview with captured photo
                preview.innerHTML = `<img src="${captureResult.dataUrl}" alt="Captured Photo">`;

                // Update gender select if it's Tripo Doll
                if (isTripoDoll && captureResult.gender) {
                    genderSelect.value = captureResult.gender;
                }

                // Store the data URL for later use
                preview.dataset.capturedPhoto = captureResult.dataUrl;
                preview.dataset.source = source; // Store the source type

                console.log(`[UISettings] ${isTripoDoll ? 'Tripo Doll' : 'Doll'} photo captured successfully`);
            } catch (error) {
                console.error('[UISettings] Error in camera capture process:', error);
                this.showNotification('Failed to capture photo: ' + (error.message || 'Unknown error'), true);
            }
        });

        // Microphone button click handler
        microphoneButton.addEventListener('click', async () => {
            try {
                if (!microphoneButton.isRecording) {
                    // Start recording
                    console.log('[UI] Starting audio recording for ASR');

                    // Get optimized recording options
                    const recordingOptions = createOptimizedRecordingOptions();

                    // Request microphone access with optimized settings
                    const stream = await navigator.mediaDevices.getUserMedia(recordingOptions.getUserMediaOptions);

                    // Create MediaRecorder with optimized options
                    microphoneButton.mediaRecorder = new MediaRecorder(stream, recordingOptions.mediaRecorderOptions);

                    // Clear previous chunks
                    microphoneButton.audioChunks = [];

                    // Set up data collection
                    microphoneButton.mediaRecorder.ondataavailable = (event) => {
                        if (event.data.size > 0) {
                            microphoneButton.audioChunks.push(event.data);
                        }
                    };

                    // Set up recording completion with unified transcription workflow
                    microphoneButton.mediaRecorder.onstop = async () => {
                        console.log('[UI] Audio recording stopped, processing...');

                        // Stop all tracks to release microphone
                        stream.getTracks().forEach(track => track.stop());

                        // Create audio blob
                        const audioBlob = new Blob(microphoneButton.audioChunks, {
                            type: recordingOptions.mediaRecorderOptions.mimeType
                        });

                        // Use unified transcription workflow
                        await handleTranscriptionWorkflow({
                            audioBlob,
                            talkingAvatar: this.viewer.talkingAvatar,
                            uiComponent: this,
                            onSuccess: async (result) => {
                                // Fill the text input with transcribed text
                                textInput.value = result.text;

                                // Show success message
                                this.showNotification('Audio transcribed successfully! Generating 3D model...');

                                // Auto-click the generate button after a short delay
                                setTimeout(() => {
                                    generateButton.click();
                                }, 500);
                            },
                            onError: (error) => {
                                // Focus on text input for manual entry
                                if (textInput) {
                                    textInput.focus();
                                    textInput.placeholder = 'Please type your 3D model description here...';
                                }
                            }
                        });
                    };

                    // Start recording
                    microphoneButton.mediaRecorder.start();
                    microphoneButton.isRecording = true;

                    // Update UI
                    microphoneButton.classList.add('recording');
                    microphoneButton.innerHTML = `
                        <svg class="mic-icon" viewBox="0 0 24 24" width="20" height="20" fill="currentColor" stroke="currentColor" stroke-width="2">
                            <circle cx="12" cy="12" r="3"/>
                            <path d="M12 2a3 3 0 0 0-3 3v6a3 3 0 0 0 6 0V5a3 3 0 0 0-3-3z"/>
                            <path d="M19 10v2a7 7 0 0 1-14 0v-2"/>
                            <line x1="12" y1="19" x2="12" y2="23"/>
                            <line x1="8" y1="23" x2="16" y2="23"/>
                        </svg>
                        <span class="mic-text">Recording...</span>
                    `;

                    this.showNotification('Recording audio... Click again to stop');

                } else {
                    // Stop recording
                    console.log('[UI] Stopping audio recording');

                    if (microphoneButton.mediaRecorder && microphoneButton.mediaRecorder.state === 'recording') {
                        microphoneButton.mediaRecorder.stop();
                    }

                    microphoneButton.isRecording = false;

                    // Update UI
                    microphoneButton.classList.remove('recording');
                    microphoneButton.innerHTML = `
                        <svg class="mic-icon" viewBox="0 0 24 24" width="20" height="20" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M12 2a3 3 0 0 0-3 3v6a3 3 0 0 0 6 0V5a3 3 0 0 0-3-3z"/>
                            <path d="M19 10v2a7 7 0 0 1-14 0v-2"/>
                            <line x1="12" y1="19" x2="12" y2="23"/>
                            <line x1="8" y1="23" x2="16" y2="23"/>
                        </svg>
                        <span class="mic-text">Voice Input</span>
                    `;

                    this.showNotification('Processing audio...');
                }
            } catch (error) {
                console.error('[UI] Error with microphone recording:', error);
                this.showNotification('Error accessing microphone: ' + error.message, true);

                // Reset recording state
                microphoneButton.isRecording = false;
                microphoneButton.classList.remove('recording');
                microphoneButton.innerHTML = `
                    <svg class="mic-icon" viewBox="0 0 24 24" width="20" height="20" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M12 2a3 3 0 0 0-3 3v6a3 3 0 0 0 6 0V5a3 3 0 0 0-3-3z"/>
                        <path d="M19 10v2a7 7 0 0 1-14 0v-2"/>
                        <line x1="12" y1="19" x2="12" y2="23"/>
                        <line x1="8" y1="23" x2="16" y2="23"/>
                    </svg>
                    <span class="mic-text">Voice Input</span>
                `;
            }
        });

        // Generate button click handler
        generateButton.addEventListener('click', async () => {
            const source = sourceSelect.value;
            let input;
            let options = {};

            // Find the modal content element
            const modalContent = modal.querySelector('.conversion-content');
            if (modalContent) {
                // Create a button container for the buttons if it doesn't exist
                if (!modalContent.querySelector('.button-container')) {
                    const buttonContainer = document.createElement('div');
                    buttonContainer.className = 'button-container';

                    // Move the buttons to the container
                    buttonContainer.appendChild(generateButton);
                    buttonContainer.appendChild(cancelButton);

                    // Add the container to the content
                    modalContent.appendChild(buttonContainer);
                }
            }

            if (source === 'text' || source === 'textto3d') {
                input = textInput.value.trim();
                if (!input) {
                    this.showNotification('Please enter a description', true);
                    return;
                }
            } else if (source === 'tripo-doll' || source === 'doll') {
                // For tripo-doll and doll, check if we have a captured photo or a file
                if (preview.dataset.capturedPhoto) {
                    // Use captured photo
                    const dataUrl = preview.dataset.capturedPhoto;

                    // Convert data URL to blob
                    const response = await fetch(dataUrl);
                    const blob = await response.blob();

                    // Upload the blob with appropriate filename
                    const filename = source === 'tripo-doll' ? 'tripo_doll_photo.jpg' : 'doll_photo.jpg';
                    input = await this.uploadFile(blob);

                    // Add gender option for tripo-doll
                    if (source === 'tripo-doll') {
                        options.gender = genderSelect.value;
                    }

                    // Add source type to options
                    options.sourceType = preview.dataset.source || source;
                } else {
                    // Check for file input
                    const file = fileInput.files[0];
                    if (!file) {
                        // Only mention camera for Tripo Doll
                        if (source === 'tripo-doll') {
                            this.showNotification('Please select a file or capture a Tripo Doll photo', true);
                        } else {
                            this.showNotification('Please select a file', true);
                        }
                        return;
                    }
                    input = await this.uploadFile(file);

                    // Add gender option for tripo-doll
                    if (source === 'tripo-doll') {
                        options.gender = genderSelect.value;
                    }
                }
            } else {
                // For other sources, use file input
                const file = fileInput.files[0];
                if (!file) {
                    this.showNotification('Please select a file', true);
                    return;
                }
                input = await this.uploadFile(file);
            }

            // Start generation and hide modal
            modal.classList.add('hidden');
            await this.viewer.generate3DModel(source, input, options);
        });

        // Cancel button click handler
        cancelButton.addEventListener('click', () => {
            console.log('[UI] Cancel button clicked in modal');
            if (this.state.isGenerating) {
                this.viewer.cancelGeneration();
            }

            // Reset microphone button state when canceling
            if (microphoneButton && microphoneButton.isRecording) {
                if (microphoneButton.mediaRecorder && microphoneButton.mediaRecorder.state === 'recording') {
                    microphoneButton.mediaRecorder.stop();
                }
                microphoneButton.isRecording = false;
                microphoneButton.classList.remove('recording');
                microphoneButton.innerHTML = `
                    <svg class="mic-icon" viewBox="0 0 24 24" width="20" height="20" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M12 2a3 3 0 0 0-3 3v6a3 3 0 0 0 6 0V5a3 3 0 0 0-3-3z"/>
                        <path d="M19 10v2a7 7 0 0 1-14 0v-2"/>
                        <line x1="12" y1="19" x2="12" y2="23"/>
                        <line x1="8" y1="23" x2="16" y2="23"/>
                    </svg>
                    <span class="mic-text">Voice Input</span>
                `;
            }

            modal.classList.add('hidden');
        });

        // Convert button click handler
        if (convertButton) {
            convertButton.addEventListener('click', () => {
                modal.classList.remove('hidden');
                textInput.value = '';
                fileInput.value = '';
                preview.innerHTML = '';
                sourceSelect.value = 'text';

                // Show text input and hide file input
                textInput.classList.remove('hidden');
                fileInput.classList.add('hidden');

                // Reset microphone button state
                microphoneButton.classList.add('hidden');
                if (microphoneButton.isRecording) {
                    if (microphoneButton.mediaRecorder && microphoneButton.mediaRecorder.state === 'recording') {
                        microphoneButton.mediaRecorder.stop();
                    }
                    microphoneButton.isRecording = false;
                    microphoneButton.classList.remove('recording');
                    microphoneButton.innerHTML = `
                        <svg class="mic-icon" viewBox="0 0 24 24" width="20" height="20" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M12 2a3 3 0 0 0-3 3v6a3 3 0 0 0 6 0V5a3 3 0 0 0-3-3z"/>
                            <path d="M19 10v2a7 7 0 0 1-14 0v-2"/>
                            <line x1="12" y1="19" x2="12" y2="23"/>
                            <line x1="8" y1="23" x2="16" y2="23"/>
                        </svg>
                        <span class="mic-text">Voice Input</span>
                    `;
                }

                // Remove camera button and gender selector from DOM if they exist
                if (cameraButton.parentNode === inputArea) {
                    inputArea.removeChild(cameraButton);
                }

                if (genderWrapper.parentNode === inputArea) {
                    inputArea.removeChild(genderWrapper);
                }

                // Trigger the change event to ensure proper UI state
                const event = new Event('change');
                sourceSelect.dispatchEvent(event);
            });
        }
    }

    /**
     * Create a centered camera modal for tripo-doll photo capture
     */
    createCenteredCameraModal(stream, isTripoDoll, genderSelect, resolve, reject) {
        // Create modal overlay
        const modalOverlay = document.createElement('div');
        modalOverlay.className = 'tripo-camera-modal-overlay';
        modalOverlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            backdrop-filter: blur(8px);
            opacity: 0;
            transition: opacity 0.3s ease;
        `;

        // Create modal content
        const modalContent = document.createElement('div');
        modalContent.className = 'tripo-camera-modal-content';
        modalContent.style.cssText = `
            position: relative;
            width: 90vw;
            max-width: 800px;
            height: 70vh;
            max-height: 600px;
            background: linear-gradient(135deg, #1a1a1a, #2d2d2d);
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
            border: 2px solid rgba(255, 255, 255, 0.1);
            transform: scale(0.9);
            transition: transform 0.3s ease;
        `;

        // Create video element
        const videoElement = document.createElement('video');
        videoElement.autoplay = true;
        videoElement.playsInline = true;
        videoElement.muted = true;
        videoElement.srcObject = stream;
        videoElement.style.cssText = `
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 18px;
        `;

        // Ensure video plays properly
        videoElement.addEventListener('loadedmetadata', () => {
            console.log('[UISettings] Video metadata loaded, dimensions:', videoElement.videoWidth, 'x', videoElement.videoHeight);
            videoElement.play().catch(err => {
                console.error('[UISettings] Error starting video playback:', err);
            });
        });

        // Handle video errors
        videoElement.addEventListener('error', (e) => {
            console.error('[UISettings] Video error:', e);
        });

        // Add additional debugging
        videoElement.addEventListener('canplay', () => {
            console.log('[UISettings] Video can start playing');
        });

        videoElement.addEventListener('playing', () => {
            console.log('[UISettings] Video is now playing');
        });

        // Force video to start playing if stream is already ready
        if (stream && stream.active && stream.getVideoTracks().length > 0) {
            console.log('[UISettings] Stream is ready, attempting to play video immediately');
            setTimeout(() => {
                videoElement.play().catch(err => {
                    console.error('[UISettings] Error in delayed video play:', err);
                });
            }, 100);
        }

        // Create title
        const title = document.createElement('div');
        title.textContent = isTripoDoll ? 'Tripo Doll Photo Capture' : 'Photo Capture';
        title.style.cssText = `
            position: absolute;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            color: white;
            font-size: 24px;
            font-weight: 700;
            text-shadow: 0 2px 8px rgba(0,0,0,0.8);
            z-index: 1001;
            background: linear-gradient(135deg, rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.4));
            padding: 12px 24px;
            border-radius: 25px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        `;

        // No gender selector in the photo capture modal - it's handled in the any-to-3d panel

        // Create capture button
        const captureButton = document.createElement('button');
        captureButton.className = 'tripo-camera-capture-button';
        captureButton.style.cssText = `
            position: absolute;
            bottom: 30px;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            border: 5px solid white;
            cursor: pointer;
            box-shadow: 0 8px 25px rgba(0,0,0,0.4);
            z-index: 1001;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0;
        `;

        // Add camera icon to capture button
        captureButton.innerHTML = `
            <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M23 19a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h4l2-3h6l2 3h4a2 2 0 0 1 2 2z"></path>
                <circle cx="12" cy="13" r="4"></circle>
            </svg>
        `;

        // Add hover effects
        captureButton.addEventListener('mouseenter', () => {
            captureButton.style.transform = 'translateX(-50%) scale(1.1)';
            captureButton.style.boxShadow = '0 12px 35px rgba(0,0,0,0.5)';
            captureButton.style.background = 'linear-gradient(135deg, #c0392b, #a93226)';
        });

        captureButton.addEventListener('mouseleave', () => {
            captureButton.style.transform = 'translateX(-50%) scale(1)';
            captureButton.style.boxShadow = '0 8px 25px rgba(0,0,0,0.4)';
            captureButton.style.background = 'linear-gradient(135deg, #e74c3c, #c0392b)';
        });

        // Create close button
        const closeButton = document.createElement('button');
        closeButton.innerHTML = '×';
        closeButton.style.cssText = `
            position: absolute;
            top: 20px;
            right: 20px;
            width: 40px;
            height: 40px;
            border: none;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            font-size: 24px;
            font-weight: bold;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1001;
            transition: all 0.2s ease;
            backdrop-filter: blur(10px);
        `;

        closeButton.addEventListener('mouseenter', () => {
            closeButton.style.background = 'rgba(255, 255, 255, 0.3)';
            closeButton.style.transform = 'scale(1.1)';
        });

        closeButton.addEventListener('mouseleave', () => {
            closeButton.style.background = 'rgba(255, 255, 255, 0.2)';
            closeButton.style.transform = 'scale(1)';
        });

        // Add capture functionality
        captureButton.addEventListener('click', () => {
            try {
                // Create canvas for capture
                const canvas = document.createElement('canvas');
                canvas.width = videoElement.videoWidth;
                canvas.height = videoElement.videoHeight;

                // Draw video frame to canvas
                const ctx = canvas.getContext('2d');
                ctx.drawImage(videoElement, 0, 0, canvas.width, canvas.height);

                // Get data URL
                const dataUrl = canvas.toDataURL('image/jpeg', 0.9);

                // Close modal
                this.closeCameraModal(modalOverlay);

                // Resolve with data URL and gender from the any-to-3d panel
                resolve({
                    dataUrl,
                    gender: isTripoDoll && genderSelect ? genderSelect.value : null
                });
            } catch (error) {
                console.error('[UISettings] Error capturing photo:', error);
                reject(error);
            }
        });

        // Add close functionality
        const closeModal = () => {
            this.closeCameraModal(modalOverlay);
            reject(new Error('Camera modal closed'));
        };

        closeButton.addEventListener('click', closeModal);
        modalOverlay.addEventListener('click', (e) => {
            if (e.target === modalOverlay) {
                closeModal();
            }
        });

        // Escape key to close
        const handleEscape = (e) => {
            if (e.key === 'Escape') {
                document.removeEventListener('keydown', handleEscape);
                closeModal();
            }
        };
        document.addEventListener('keydown', handleEscape);

        // Assemble modal
        modalContent.appendChild(videoElement);
        modalContent.appendChild(title);
        modalContent.appendChild(captureButton);
        modalContent.appendChild(closeButton);
        modalOverlay.appendChild(modalContent);

        // Add to DOM
        document.body.appendChild(modalOverlay);

        // Animate in
        requestAnimationFrame(() => {
            modalOverlay.style.opacity = '1';
            modalContent.style.transform = 'scale(1)';
        });

        return modalOverlay;
    }

    /**
     * Close the camera modal
     */
    closeCameraModal(modalOverlay) {
        // DON'T stop the camera stream here - let the cameraViewer manage it
        // The stream should remain active for potential reuse
        console.log('[UISettings] Closing camera modal, keeping stream active for reuse');

        // Animate out and remove
        modalOverlay.style.opacity = '0';
        modalOverlay.querySelector('.tripo-camera-modal-content').style.transform = 'scale(0.9)';

        setTimeout(() => {
            if (modalOverlay.parentNode) {
                modalOverlay.parentNode.removeChild(modalOverlay);
            }
        }, 300);
    }



    /**
     * Helper method to upload a file
     * @param {File|Blob} file - The file or blob to upload
     * @returns {Promise<Object>} The uploaded file data
     */
    async uploadFile(file) {
        // Convert file to proper format for image/doll processing
        return new Promise((resolve) => {
            if (file instanceof Blob) {
                // If it's already a blob, create a data URL
                const reader = new FileReader();
                reader.onload = (e) => {
                    // Format the image data as required by the API
                    const imageData = {
                        url: e.target.result,
                        name: file.name || 'captured_photo.jpg',
                        type: file.type || 'image/jpeg',
                        size: file.size,
                    };
                    resolve(imageData);
                };
                reader.readAsDataURL(file);
            } else {
                // Regular file object
                const reader = new FileReader();
                reader.onload = (e) => {
                    // Format the image data as required by the API
                    const imageData = {
                        url: e.target.result,
                        name: file.name,
                        type: file.type,
                        size: file.size,
                    };
                    resolve(imageData);
                };
                reader.readAsDataURL(file);
            }
        });
    }

    /**
     * Update the isGenerating state
     */
    setGeneratingState(isGenerating) {
        this.state.isGenerating = isGenerating;
    }

    /**
     * Show progress indicator
     */
    showProgress() {
        if (!this.components.progressContainer) return;

        this.components.progressContainer.container.classList.add('visible');
        this.updateProgress(10);
        this.updateStatus('Starting generation...');
    }

    /**
     * Hide progress indicator
     */
    hideProgress() {
        if (!this.components.progressContainer) return;

        this.components.progressContainer.container.classList.remove('visible');
    }

    /**
     * Update progress bar
     */
    updateProgress(progress) {
        const { bar, text } = this.components.progressContainer || {};
        if (!bar || !text) return;

        // Ensure progress is a number between 0-100
        const safeProgress = Math.min(Math.max(0, progress), 100);

        // Update the progress bar width
        bar.style.width = `${safeProgress}%`;

        // Update the progress text
        text.textContent = `Generating: ${Math.round(safeProgress)}%`;

        // Log for debugging
        console.log(`[UI] Progress updated: ${safeProgress}%`);
    }

    /**
     * Update status text
     */
    updateStatus(message, isError = false) {
        const { status } = this.components.progressContainer || {};
        if (!status) return;

        status.textContent = message;
        status.style.color = isError ? '#ff4444' : '';

        // Log for debugging
        console.log(`[UI] Status updated: ${message}`);
    }

    /**
     * Show a loading panel with a message
     */
    showLoadingPanel(message = 'Loading...') {
        // Use the progress container for loading panel
        if (this.components.progressContainer) {
            this.components.progressContainer.container.classList.add('visible');
            this.updateStatus(message);
            this.updateProgress(10); // Start with a small progress indication
            console.log(`[UISettings] Showing loading panel: ${message}`);
        } else {
            console.warn('[UISettings] Progress container not available for loading panel');
        }
    }

    /**
     * Update loading progress
     */
    updateLoadingProgress(message, progress = null) {
        if (!this.components.progressContainer) return;

        // Update status message
        this.updateStatus(message);

        // Update progress if provided
        if (progress !== null && !isNaN(progress)) {
            this.updateProgress(progress);
        }

        console.log(`[UISettings] Loading progress: ${message} (${progress !== null ? progress + '%' : 'indeterminate'})`);
    }

    /**
     * Hide the loading panel
     */
    hideLoadingPanel() {
        if (this.components.progressContainer) {
            this.components.progressContainer.container.classList.remove('visible');
            console.log('[UISettings] Hiding loading panel');
        }
    }

    /**
     * Set up notification system
     */
    setupNotifications() {
        if (document.getElementById('ui-notifications')) return;

        // Add styles for notifications
        const style = document.createElement('style');
        style.id = 'ui-notification-styles';
        style.textContent = `
            .ui-notification {
                position: fixed;
                top: 20px;
                left: 50%;
                transform: translateX(-50%);
                background-color: rgba(0, 0, 0, 0.8);
                color: white;
                padding: 10px 20px;
                border-radius: 5px;
                z-index: 1000;
                font-size: 14px;
                box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
                opacity: 1;
                transition: opacity 0.3s ease;
                pointer-events: none;
            }

            .ui-notification.error {
                background-color: rgba(200, 0, 0, 0.8);
            }

            .ui-notification.fade-out {
                opacity: 0;
            }
        `;
        document.head.appendChild(style);

        // Create container for notifications
        const container = document.createElement('div');
        container.id = 'ui-notifications';
        document.body.appendChild(container);
    }

    /**
     * Show a notification message
     */
    showNotification(message, isError = false) {
        const container = document.getElementById('ui-notifications');
        if (!container) return;

        const notification = document.createElement('div');
        notification.className = `ui-notification ${isError ? 'error' : ''}`;
        notification.textContent = message;
        container.appendChild(notification);

        // Remove after delay with fade
        setTimeout(() => {
            notification.classList.add('fade-out');
            setTimeout(() => notification.remove(), 500);
        }, 2000);
    }

    /**
     * Clean up resources
     */
    dispose() {
        // Remove gesture control container
        if (this.components.gestureControl?.container) {
            this.components.gestureControl.container.remove();
        }

        // Remove progress container
        if (this.components.progressContainer?.container) {
            this.components.progressContainer.container.remove();
        }

        // Remove notification container
        const notificationContainer = document.getElementById('ui-notifications');
        if (notificationContainer) {
            notificationContainer.remove();
        }

        // Clean up gesture UI elements
        this.cleanupGestureUI();
    }
    /**
     * Show rotation gesture indicator in the UI
     */
    showGestureRotationIndicator() {
        // Create indicator if it doesn't exist
        if (!this.rotationIndicator) {
            this.rotationIndicator = document.createElement('div');
            this.rotationIndicator.className = 'gesture-rotation-indicator';
            this.rotationIndicator.innerHTML = `
                <div class="rotation-icon">
                    <svg viewBox="0 0 24 24" width="24" height="24">
                        <path d="M12,4V1L8,5L12,9V6C15.31,6 18,8.69 18,12C18,15.31 15.31,18 12,18C8.69,18 6,15.31 6,12H4C4,16.42 7.58,20 12,20C16.42,20 20,16.42 20,12C20,7.58 16.42,4 12,4Z" />
                    </svg>
                </div>
                <div class="rotation-status">Rotating</div>
            `;
            document.body.appendChild(this.rotationIndicator);

            // Add styles if not already added
            if (!document.getElementById('gesture-rotation-styles')) {
                const styles = document.createElement('style');
                styles.id = 'gesture-rotation-styles';
                styles.innerHTML = `
                    .gesture-rotation-indicator {
                        position: fixed;
                        top: 100px;
                        right: 20px;
                        background-color: rgba(0, 0, 0, 0.7);
                        color: white;
                        padding: 8px 12px;
                        border-radius: 5px;
                        z-index: 1000;
                        display: flex;
                        align-items: center;
                        gap: 10px;
                        opacity: 0;
                        transition: opacity 0.3s ease;
                    }
                    .gesture-rotation-indicator.visible {
                        opacity: 1;
                    }
                    .rotation-icon {
                        display: flex;
                        align-items: center;
                        justify-content: center;
                    }
                    .rotation-icon svg {
                        fill: white;
                    }
                    .rotation-status {
                        font-size: 12px;
                    }
                `;
                document.head.appendChild(styles);
            }
        }

        // Make indicator visible
        this.rotationIndicator.classList.add('visible');

        // Clear hide timeout if exists
        if (this.hideRotationIndicatorTimeout) {
            clearTimeout(this.hideRotationIndicatorTimeout);
        }
    }

    /**
     * Hide rotation gesture indicator
     */
    hideGestureRotationIndicator() {
        if (!this.rotationIndicator) return;

        // Set timeout to hide with animation
        this.hideRotationIndicatorTimeout = setTimeout(() => {
            this.rotationIndicator.classList.remove('visible');
        }, 1000);
    }

    /**
     * Update rotation indicator - REMOVED locked parameter
     * This function is kept for compatibility but simplified
     */
    updateGestureRotationIndicator() {
        // Function kept for API compatibility but functionality removed
        // as we no longer show lock status for rotation
    }
    /**
     * Create and show scaling status indicator for gesture controls
     * @param {boolean} locked - Whether the scaling is locked
     */
    updateGestureScalingIndicator(locked) {
        // Create indicator if it doesn't exist
        if (!document.getElementById('scaling-status-indicator')) {
            const indicator = document.createElement('div');
            indicator.id = 'scaling-status-indicator';
            indicator.className = 'scaling-status-indicator';

            // Add loading spinner
            const spinner = document.createElement('div');
            spinner.className = 'scaling-spinner';
            indicator.appendChild(spinner);

            // Add status text
            const statusText = document.createElement('div');
            statusText.className = 'scaling-status-text';
            indicator.appendChild(statusText);

            // Add styles
            this.addGestureScalingIndicatorStyles();

            // Append to document
            document.body.appendChild(indicator);

            this.gestureUI.scalingIndicatorCreated = true;
        }

        // Get indicator and update text/class
        const indicator = document.getElementById('scaling-status-indicator');
        const statusText = indicator.querySelector('.scaling-status-text');

        // Update text and class based on lock state
        statusText.textContent = locked ? 'Locked' : 'Unlocked';
        indicator.className = `scaling-status-indicator ${locked ? 'locked' : 'unlocked'}`;

        // Show the indicator
        indicator.style.display = 'flex';
    }

    /**
     * Hide scaling status indicator
     */
    hideGestureScalingIndicator() {
        const indicator = document.getElementById('scaling-status-indicator');
        if (indicator) {
            indicator.style.display = 'none';
        }
    }
    /**
     * Display gesture tutorial overlay
     */
    showGestureTutorial() {
        if (document.getElementById('gesture-tutorial')) return;

        const tutorial = document.createElement('div');
        tutorial.id = 'gesture-tutorial';
        tutorial.className = 'gesture-tutorial';

        // Add tutorial content with illustrations
        tutorial.innerHTML = `
            <div class="tutorial-header">
                <h3>Gesture Controls</h3>
                <button class="close-tutorial">×</button>
            </div>
            <div class="tutorial-content">
                <div class="gesture-item">
                    <div class="gesture-icon rotation-icon">
                        <svg viewBox="0 0 100 100" width="80" height="80">
                            <path d="M30,50 L50,30 L50,70 Z" fill="#4CAF50"/>
                            <circle cx="65" cy="50" r="15" stroke="#4CAF50" stroke-width="2" fill="none"/>
                        </svg>
                    </div>
                    <div class="gesture-description">
                        <h4>Rotation</h4>
                        <p>Extend index and middle fingers, move hand to rotate view</p>
                    </div>
                </div>
                <div class="gesture-item">
                    <div class="gesture-icon pinch-icon">
                        <svg viewBox="0 0 100 100" width="80" height="80">
                            <path d="M30,50 L70,50" stroke="#2196F3" stroke-width="2"/>
                            <circle cx="30" cy="50" r="10" fill="#2196F3"/>
                            <circle cx="70" cy="50" r="10" fill="#2196F3"/>
                            <path d="M45,35 L55,65" stroke="#2196F3" stroke-width="2" stroke-dasharray="4"/>
                        </svg>
                    </div>
                    <div class="gesture-description">
                        <h4>Zoom</h4>
                        <p>Pinch thumb and index finger to zoom in/out</p>
                    </div>
                </div>
            </div>
        `;

        // Add styles
        const style = document.createElement('style');
        style.id = 'gesture-tutorial-styles';
        style.textContent = `
            .gesture-tutorial {
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background-color: rgba(0, 0, 0, 0.8);
                color: white;
                padding: 20px;
                border-radius: 10px;
                z-index: 1000;
                max-width: 400px;
                box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
                transition: opacity 0.5s ease;
            }
            .tutorial-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                border-bottom: 1px solid rgba(255, 255, 255, 0.3);
                padding-bottom: 10px;
                margin-bottom: 15px;
            }
            .close-tutorial {
                background: none;
                border: none;
                color: white;
                font-size: 20px;
                cursor: pointer;
            }
            .tutorial-content {
                display: flex;
                flex-direction: column;
                gap: 20px;
            }
            .gesture-item {
                display: flex;
                align-items: center;
                gap: 15px;
            }
            .gesture-icon {
                background-color: rgba(255, 255, 255, 0.1);
                border-radius: 50%;
                padding: 10px;
            }
            .gesture-description h4 {
                margin: 0 0 5px 0;
            }
            .gesture-description p {
                margin: 0;
                font-size: 14px;
                opacity: 0.8;
            }
        `;

        document.head.appendChild(style);
        document.body.appendChild(tutorial);

        // Add event listener for close button
        tutorial.querySelector('.close-tutorial').addEventListener('click', () => {
            this.hideGestureTutorial();
        });

        // Auto-hide after 10 seconds
        setTimeout(() => this.hideGestureTutorial(), 10000);
    }
    /**
     * Hide gesture tutorial overlay
     */
    hideGestureTutorial() {
        const tutorial = document.getElementById('gesture-tutorial');
        if (tutorial) {
            tutorial.style.opacity = '0';
            setTimeout(() => tutorial.remove(), 500);
        }
    }
    /**
     * Add styles for scaling status indicator
     */
    addGestureScalingIndicatorStyles() {
        if (!document.getElementById('scaling-indicator-styles')) {
            const style = document.createElement('style');
            style.id = 'scaling-indicator-styles';
            style.textContent = `
                .scaling-status-indicator {
                    position: fixed;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    background-color: rgba(0, 0, 0, 0.7);
                    color: white;
                    border-radius: 5px;
                    padding: 15px;
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;
                    z-index: 1000;
                    pointer-events: none;
                }

                .scaling-status-indicator.locked {
                    background-color: rgba(0, 120, 255, 0.7);
                }

                .scaling-status-indicator.unlocked {
                    background-color: rgba(100, 100, 100, 0.7);
                }

                .scaling-spinner {
                    width: 30px;
                    height: 30px;
                    border: 3px solid rgba(255, 255, 255, 0.3);
                    border-radius: 50%;
                    border-top-color: #fff;
                    animation: spin 1s ease-in-out infinite;
                    margin-bottom: 10px;
                }

                .scaling-status-text {
                    font-size: 16px;
                    font-weight: bold;
                }

                @keyframes spin {
                    to { transform: rotate(360deg); }
                }
            `;
            document.head.appendChild(style);
        }
    }

    /**
     * Show zoom slider for gesture controls
     */
    showGestureZoomSlider() {
        // Create slider if it doesn't exist
        if (!document.getElementById('zoom-slider-container')) {
            const container = document.createElement('div');
            container.id = 'zoom-slider-container';
            container.className = 'zoom-slider-container';

            const track = document.createElement('div');
            track.className = 'zoom-slider-track';

            const fill = document.createElement('div');
            fill.className = 'zoom-slider-fill';

            const thumb = document.createElement('div');
            thumb.className = 'zoom-slider-thumb';

            const label = document.createElement('div');
            label.className = 'zoom-label';

            track.appendChild(fill);
            track.appendChild(thumb);
            container.appendChild(track);
            container.appendChild(label);

            // Add styles for zoom slider
            this.addGestureZoomSliderStyles();

            document.body.appendChild(container);
            this.gestureUI.zoomSliderCreated = true;
        }

        // Show the slider
        const slider = document.getElementById('zoom-slider-container');
        slider.classList.add('visible');
    }

    /**
     * Hide zoom slider
     */
    hideGestureZoomSlider() {
        const slider = document.getElementById('zoom-slider-container');
        if (slider) {
            slider.classList.remove('visible');
        }
    }

    /**
     * Update zoom slider position based on current zoom level
     * @param {Object} camera - Camera object with zoom property
     * @param {number} minZoom - Minimum zoom level
     * @param {number} maxZoom - Maximum zoom level
     */
    updateGestureZoomSlider(camera, minZoom, maxZoom) {
        const slider = document.getElementById('zoom-slider-container');
        if (!slider) return;

        let currentZoom = 1;

        // Get current zoom level
        if (camera && camera.zoom !== undefined) {
            currentZoom = camera.zoom;
        }

        // Calculate position percentage based on zoom constraints
        let percentage = ((currentZoom - minZoom) / (maxZoom - minZoom)) * 100;

        // Clamp between 0-100
        percentage = Math.min(Math.max(percentage, 0), 100);

        // Update UI elements
        const fill = slider.querySelector('.zoom-slider-fill');
        const thumb = slider.querySelector('.zoom-slider-thumb');
        const label = slider.querySelector('.zoom-label');

        if (fill) fill.style.width = `${percentage}%`;
        if (thumb) thumb.style.left = `${percentage}%`;
        if (label) label.textContent = `Zoom: ${currentZoom.toFixed(1)}x`;
    }

    /**
     * Add styles for zoom slider
     */
    addGestureZoomSliderStyles() {
        if (!document.getElementById('zoom-slider-styles')) {
            const style = document.createElement('style');
            style.id = 'zoom-slider-styles';
            style.textContent = `
                .zoom-slider-container {
                    position: fixed;
                    bottom: 30px;
                    left: 50%;
                    transform: translateX(-50%);
                    background-color: rgba(0, 0, 0, 0.5);
                    border-radius: 20px;
                    padding: 5px 20px;
                    width: 300px;
                    height: 40px;
                    display: flex;
                    align-items: center;
                    pointer-events: none;
                    opacity: 0;
                    transition: opacity 0.3s ease;
                    z-index: 1000;
                }

                .zoom-slider-container.visible {
                    opacity: 1;
                }

                .zoom-slider-track {
                    width: 100%;
                    height: 6px;
                    background-color: rgba(255, 255, 255, 0.3);
                    border-radius: 3px;
                    position: relative;
                }

                .zoom-slider-fill {
                    height: 100%;
                    background-color: rgba(0, 120, 255, 0.8);
                    border-radius: 3px;
                    position: absolute;
                    left: 0;
                    top: 0;
                }

                .zoom-slider-thumb {
                    width: 16px;
                    height: 16px;
                    background-color: white;
                    border-radius: 50%;
                    position: absolute;
                    top: 50%;
                    transform: translate(-50%, -50%);
                    box-shadow: 0 0 5px rgba(0, 0, 0, 0.3);
                }

                .zoom-label {
                    position: absolute;
                    top: -20px;
                    left: 50%;
                    transform: translateX(-50%);
                    color: white;
                    font-size: 12px;
                    white-space: nowrap;
                }
            `;
            document.head.appendChild(style);
        }
    }

    /**
     * Clean up gesture UI elements
     */
    cleanupGestureUI() {
        // Remove scaling indicator
        const indicator = document.getElementById('scaling-status-indicator');
        if (indicator) {
            indicator.remove();
        }

        // Remove zoom slider
        const slider = document.getElementById('zoom-slider-container');
        if (slider) {
            slider.remove();
        }

        // Remove styles
        const indicatorStyles = document.getElementById('scaling-indicator-styles');
        if (indicatorStyles) {
            indicatorStyles.remove();
        }

        const sliderStyles = document.getElementById('zoom-slider-styles');
        if (sliderStyles) {
            sliderStyles.remove();
        }

        this.gestureUI = {
            scalingIndicatorCreated: false,
            zoomSliderCreated: false
        };
    }

    /**
     * Update the gesture control UI based on state
     * @param {boolean} isActive - Whether gesture controls are active
     */
    updateGestureControlUI(isActive) {
        // Update the menu item in the actions menu
        if (this.components.actionsMenu && this.components.actionsMenu.gestureMenuItem) {
            this.updateGestureMenuItem(this.components.actionsMenu.gestureMenuItem, isActive);
        }
    }
}
