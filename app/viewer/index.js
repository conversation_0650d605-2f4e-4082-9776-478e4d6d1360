import { Viewer } from './viewer.js';
import { setLog<PERSON><PERSON>l, Log<PERSON>evel, createLogger } from '../../src/utils/logger.js';

// ---------------------------------------------------------------------------
// Debug-entry logger
// ---------------------------------------------------------------------------
// Creating a scoped logger that fires as soon as this entry module is parsed so
// we can confirm the *exact* entry-point that Vite serves to the browser.
// Useful when multiple build outputs or HTML variants exist.
const entryLogger = createLogger('ViewerEntry');
entryLogger.debug(`[debugpp] index.js module loaded → import.meta.url = ${import.meta.url}`);

// Enable debug logging for development
setLogLevel(LogLevel.INFO);

// Wait for DOM to be ready
document.addEventListener('DOMContentLoaded', () => {
    entryLogger.debug('[debugpp] DOMContentLoaded fired');

    const container = document.getElementById('viewer-container');
    if (!container) {
        console.error('Viewer container not found');
        return;
    }

    entryLogger.debug('[debugpp] Initializing hand viewer with container:', container);
    const viewer = new Viewer(container);

    // Expose viewer globally for testing
    window.viewer = viewer;

    viewer.initViewer().catch(error => {
        console.error('Initialization failed:', error);
    });
});
