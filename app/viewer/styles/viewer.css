/* Add at the top of your CSS file */
:root {
  --top-button-width: 120px;
  --button-spacing: 8px;
}

/* @import url('https://rsms.me/inter/inter.css'); */

* {
  box-sizing: border-box;
}

html,
body,
#root {
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
}

body {
  background: #ffffff;
  font-family: 'Inter', sans-serif;
  overflow: hidden;
}

/* Main container */
#viewer-container {
  position: relative;
  width: 100vw;
  height: 100vh;
  background: #ffffff;
  overflow: hidden;
}

/* Canvas settings */
canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  touch-action: none;
}

/* Debug and Error Displays */
#debug-overlay {
  position: fixed;
  top: 10px;
  left: 10px;
  z-index: 1000;
  color: #fff;
  font-family: monospace;
  pointer-events: none;
  display: none;
  /* Hidden by default */
}

#error-display {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0, 0, 0, 0.8);
  color: #ff4444;
  padding: 20px;
  border-radius: 5px;
  font-family: sans-serif;
  display: none;
  /* Hidden until needed */
}

.error-message {
  position: absolute;
  top: 20px;
  left: 50%;
  background: rgba(255, 0, 0, 0.8);
  color: white;
  padding: 10px 20px;
  border-radius: 4px;
  font-family: sans-serif;
  font-size: 14px;
  z-index: 1000;
}

/* Settings Panel */
#settings-panel {
  position: fixed;
  top: 10px;
  right: 10px;
  background: rgba(255, 255, 255, 0.7);
  padding: 15px;
  border-radius: 5px;
  color: #fff;
  font-family: sans-serif;
  display: none;
  /* Hidden by default */
}

/* Input Controls */
.input-controls {
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.7);
  padding: 10px;
  border-radius: 5px;
  display: none;
  /* Hidden until implemented */
}

.input-controls select {
  padding: 5px;
  margin-right: 10px;
  background: #333;
  color: #fff;
  border: 1px solid #555;
  border-radius: 3px;
}

.input-controls input[type="file"] {
  display: none;
}

/* Annotations */
.annotation {
  cursor: pointer;
  outline: none;
  border: none;
  font-size: 8px;
  font-weight: 300;
  background: black;
  color: #f0f0f0;
  padding: 2px 10px;
  border-radius: 20px;
  letter-spacing: 1px;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 5px;
}

/* Looking Glass Button */
#LookingGlassButton {
  position: absolute;
  bottom: 24px;
  left: 50%;
  transform: translateX(-50%);
  padding: 12px 24px;
  border: 1px solid white;
  border-radius: 4px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  font: 13px sans-serif;
  text-align: center;
  opacity: 0.85;
  outline: none;
  z-index: 999;
  cursor: pointer;
  transition: opacity 0.2s ease;
}

#LookingGlassButton:hover {
  background-color: rgba(0, 0, 0, 0.85);
  opacity: 1;
}

#LookingGlassButton:active {
  opacity: 0.75;
}

.conversion-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.75);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(5px);
  transition: opacity 0.3s ease;
}

.conversion-modal.hidden {
  display: none;
}

.conversion-content {
  background: linear-gradient(135deg, #ffffff, #f8f9fa);
  padding: 30px;
  border-radius: 16px;
  width: 90%;
  max-width: 520px;
  display: flex;
  flex-direction: column;
  gap: 22px;
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.25), 0 5px 15px rgba(0, 0, 0, 0.1);
  transform: translateY(0);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.modal-title {
  margin: 0 0 5px 0 !important;
  font-size: 24px !important;
  font-weight: 700 !important;
  color: #2c3e50 !important;
  text-align: center !important;
  position: relative !important;
  padding-bottom: 12px !important;
}

.modal-title::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background: linear-gradient(90deg, #3498db, #2ecc71);
  border-radius: 3px;
}

.input-area {
  display: flex;
  flex-direction: column;
  gap: 18px;
  background: rgba(255, 255, 255, 0.5);
  padding: 20px;
  border-radius: 12px;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.text-input {
  width: 100%;
  min-height: 120px;
  padding: 15px 18px;
  border: 1px solid rgba(0, 0, 0, 0.12);
  border-radius: 10px;
  resize: vertical;
  font-size: 16px;
  font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  transition: all 0.25s ease;
  background-color: white;
  color: #2c3e50;
}

.text-input:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 3px 10px rgba(52, 152, 219, 0.15);
}

.text-input::placeholder {
  color: #95a5a6;
}

.file-input {
  width: 100%;
  padding: 15px 18px;
  border: 1px solid rgba(0, 0, 0, 0.12);
  border-radius: 10px;
  background-color: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  transition: all 0.25s ease;
  color: #2c3e50;
}

.file-input:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 3px 10px rgba(52, 152, 219, 0.15);
}

.preview {
  max-height: 220px;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
  border: 1px dashed rgba(52, 152, 219, 0.3);
  border-radius: 10px;
  padding: 18px;
  background-color: rgba(52, 152, 219, 0.03);
  transition: all 0.25s ease;
}

.preview:hover {
  border-color: rgba(52, 152, 219, 0.5);
  background-color: rgba(52, 152, 219, 0.05);
}

.preview img {
  max-width: 100%;
  max-height: 200px;
  object-fit: contain;
  border-radius: 6px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.file-name {
  padding: 14px;
  background: #f8f9fa;
  border-radius: 8px;
  word-break: break-all;
  font-size: 14px;
  color: #2c3e50;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.button-container {
  display: flex;
  gap: 15px;
  margin-top: 5px;
}

.generate-button,
.cancel-button,
.convert-button {
  padding: 14px 28px;
  border: none;
  border-radius: 10px;
  cursor: pointer;
  font-weight: 600;
  font-size: 16px;
  transition: all 0.25s ease;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  letter-spacing: 0.3px;
}

.generate-button {
  background: linear-gradient(135deg, #2ecc71, #27ae60);
  color: white;
  flex: 2;
}

.generate-button:hover {
  background: linear-gradient(135deg, #27ae60, #219653);
  transform: translateY(-2px);
  box-shadow: 0 6px 15px rgba(46, 204, 113, 0.25);
}

.cancel-button {
  background: linear-gradient(135deg, #e74c3c, #c0392b);
  color: white;
  flex: 1;
}

.cancel-button:hover {
  background: linear-gradient(135deg, #c0392b, #a93226);
  transform: translateY(-2px);
  box-shadow: 0 6px 15px rgba(231, 76, 60, 0.25);
}

.convert-button {
  position: fixed;
  top: 20px;
  right: calc(240px + 40px);
  background: #2196F3;
  color: white;
  z-index: 100;
  width: 200px;
  border-radius: 6px;
  transition: all 0.2s ease;
  padding: 8px 16px;
  white-space: nowrap;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.convert-button:hover {
  background: #1976D2;
}

.hidden {
  display: none !important;
}

select {
  width: 100%;
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 4px;
  background: white;
}

/* Progress Container Styles */
.progress-container {
  display: none;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  padding: 12px 20px;
  z-index: 1000;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
}

.progress-container.visible {
  display: block;
}

.progress-bar-wrapper {
  display: flex;
  align-items: center;
  gap: 15px;
  max-width: 800px;
  margin: 0 auto;
}

.progress-bar {
  flex-grow: 1;
  height: 4px;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 2px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  width: 0%;
  background: #2196F3;
  transition: width 0.3s ease;
}

.progress-text {
  color: #333;
  font-size: 14px;
  font-weight: 500;
  white-space: nowrap;
  min-width: 120px;
}

.status-text {
  color: #000000;
  font-size: 14px;
  white-space: nowrap;
  font-style: normal;
}

.progress-container .cancel-button {
  padding: 6px 16px;
  background: #ff4444;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  margin: 0;
  transition: all 0.2s ease;
}

.progress-container .cancel-button:hover {
  background: #ff6666;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* VR Button */
.VRButton {
  position: fixed;
  z-index: 1001;
  /* Above progress container */
  background: rgba(0, 0, 0, 0.8);
  border-radius: 8px;
  border: none;
  font-weight: 500;
  color: white;
  padding: 12px 24px;
  cursor: pointer;
  font-size: 16px;
  bottom: calc(4rem + 20px);
  /* Position above progress container */
  left: 50%;
  transform: translateX(-50%);
  transition: all 0.2s ease;
  backdrop-filter: blur(5px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.VRButton:hover {
  background: rgba(0, 0, 0, 0.9);
  transform: translateX(-50%) translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

/* Mesh Selector Styles - Viewer-specific overrides */
.mesh-selector {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1000;
  pointer-events: auto;
  display: flex;
  align-items: center;
  gap: 8px;
  background: rgba(255, 255, 255, 0.9);
  padding: 8px;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  height: 40px;
}

.mesh-select {
  width: 200px;
  height: 100%;
  background: white;
  padding: 0 8px;
  border: 1px solid #ccc;
  border-radius: 6px;
  font-size: 14px;
}

.mesh-select option {
  max-width: 220px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  padding-right: 24px;
}

.button-container {
  height: 100%;
  display: flex;
  align-items: center;
  gap: 8px;
}

.delete-button {
  height: 100%;
  aspect-ratio: 1;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background: #f5f5f5;
  border: 1px solid #ccc;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.delete-button:hover {
  background: #e0e0e0;
  border-color: #888;
}

.delete-button:active {
  background: #d0d0d0;
}

.loading-indicator {
  color: #666;
  margin-left: 8px;
}

.source-wrapper {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 10px;
  position: relative;
}

.source-wrapper::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 0;
  width: 100%;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(52, 152, 219, 0.3), transparent);
}

.source-select {
  flex: 1;
  padding: 14px 18px;
  border-radius: 10px;
  border: 1px solid rgba(52, 152, 219, 0.2);
  background-color: white;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.25s ease;
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.05);
  color: #2c3e50;
  font-weight: 500;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%233498db' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 15px center;
  background-size: 16px;
  padding-right: 45px;
}

.source-select:hover:not(:disabled) {
  border-color: rgba(52, 152, 219, 0.5);
  box-shadow: 0 4px 12px rgba(52, 152, 219, 0.1);
  transform: translateY(-1px);
}

.source-select:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.2);
}

/* Option styles */
.source-select option {
  padding: 10px;
  font-size: 15px;
}

/* Tripo Doll and Doll specific styles */
.camera-button {
  background: linear-gradient(135deg, #3498db, #2980b9) !important;
  color: white !important;
  padding: 14px 20px !important;
  border-radius: 10px !important;
  border: none !important;
  font-weight: 600 !important;
  font-size: 16px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  gap: 10px !important;
  cursor: pointer !important;
  transition: all 0.25s ease !important;
  box-shadow: 0 4px 10px rgba(52, 152, 219, 0.2) !important;
  margin-top: 8px !important;
  width: 100% !important;
  letter-spacing: 0.3px !important;
}

.camera-button:hover {
  background: linear-gradient(135deg, #2980b9, #2573a7) !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 6px 15px rgba(52, 152, 219, 0.3) !important;
}

.camera-button svg {
  width: 22px !important;
  height: 22px !important;
}

.gender-wrapper {
  display: flex !important;
  align-items: center !important;
  gap: 15px !important;
  margin-top: 8px !important;
  padding: 15px 18px !important;
  background: linear-gradient(135deg, rgba(52, 152, 219, 0.05), rgba(46, 204, 113, 0.05)) !important;
  border-radius: 10px !important;
  border: 1px solid rgba(52, 152, 219, 0.15) !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03) !important;
}

.gender-wrapper label {
  font-weight: 600 !important;
  color: #2c3e50 !important;
  font-size: 16px !important;
  min-width: 70px !important;
}

.gender-select {
  flex: 1 !important;
  padding: 10px 15px !important;
  border-radius: 8px !important;
  border: 1px solid rgba(52, 152, 219, 0.2) !important;
  background-color: white !important;
  font-size: 15px !important;
  cursor: pointer !important;
  transition: all 0.25s ease !important;
  -webkit-appearance: none !important;
  -moz-appearance: none !important;
  appearance: none !important;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%233498db' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e") !important;
  background-repeat: no-repeat !important;
  background-position: right 10px center !important;
  background-size: 14px !important;
  padding-right: 35px !important;
  color: #2c3e50 !important;
  font-weight: 500 !important;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.03) !important;
}

.gender-select:hover {
  border-color: rgba(52, 152, 219, 0.4) !important;
  box-shadow: 0 3px 8px rgba(52, 152, 219, 0.1) !important;
}

.gender-select:focus {
  outline: none !important;
  border-color: #3498db !important;
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.15) !important;
}

.regenerate-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  margin-left: 8px;
}

.regenerate-checkbox {
  display: none;
}

.regenerate-label {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  background: #f5f5f5;
}

.regenerate-label:hover {
  background: #e0e0e0;
}

.regenerate-checkbox:checked+.regenerate-label {
  background: #e3f2fd;
}

.regenerate-checkbox:checked+.regenerate-label .regenerate-icon {
  fill: #1976d2;
  transform: rotate(180deg);
}

.regenerate-icon {
  fill: #666;
  transition: all 0.3s ease;
}

.regenerate-label:hover .regenerate-icon {
  fill: #333;
}

/* Tooltip */
.tooltip {
  position: absolute;
  top: 100%;
  right: 50%;
  transform: translateX(50%);
  padding: 6px 10px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  font-size: 12px;
  border-radius: 4px;
  white-space: nowrap;
  opacity: 0;
  visibility: hidden;
  transition: all 0.2s ease;
  margin-top: 8px;
}

.tooltip::before {
  content: '';
  position: absolute;
  top: -4px;
  right: 50%;
  transform: translateX(50%) rotate(45deg);
  width: 8px;
  height: 8px;
  background: rgba(0, 0, 0, 0.8);
}

.regenerate-label:hover .tooltip {
  opacity: 1;
  visibility: visible;
}

/* Ensure UI elements are above canvas */
.mesh-selector,
.conversion-modal,
.convert-button,
.progress-container,
#debug-overlay,
#error-display {
  position: fixed;
  z-index: 9999;
  pointer-events: auto;
}

/* Camera Viewer Styles */
.camera-viewer-container {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background: transparent;
  border-radius: 12px;
  overflow: hidden;
}

.camera-preview {
  position: fixed;
  bottom: 24px;
  right: 24px;
  width: 240px;
  height: 180px;
  background: rgba(0, 0, 0, 0.85);
  border-radius: 12px;
  cursor: pointer;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.25);
  transition: all 0.3s ease;
  border: 2px solid rgba(255, 255, 255, 0.1);
  z-index: 1000;
}

.camera-preview:hover {
  transform: scale(1.05);
  border-color: rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.35);
}

.camera-preview video {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}

.camera-viewer-container video {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}

/* 最外层 modal：全屏透明，不阻挡点击，但让内部 .modal-content 可交互 */
.modal.camera-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: transparent;
  /* 去掉任何遮罩 */
  pointer-events: none;
  /* 不阻挡背景点击 */
  z-index: 9999;
  display: none;
  /* JS 控制显示 */
  opacity: 0;
  /* 用于淡入淡出 */
  transition: opacity 0.3s ease;
}

/* 打开/关闭动画 */
.modal.camera-modal.show {
  opacity: 1;
}

.modal.camera-modal.closing {
  opacity: 0;
}

/* 弹窗主体 .modal-content */
.modal.camera-modal .modal-content {
  position: absolute;
  /* 初始位置可自行调节 */
  top: 40px;
  left: 40px;

  /* 给个可见的半透明背景，否则看起来像按钮悬空 */
  background: rgba(33, 33, 33, 0.95);
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);

  /* 允许本区域拦截鼠标事件 */
  pointer-events: auto;

  /* 设置初始大小并允许缩放 */
  width: 480px;
  height: 270px;
  /* 16:9 */
  resize: both;
  overflow: auto;

  /* 限制最大最小值，防止拖到太大或太小 */
  min-width: 320px;
  min-height: 180px;
  max-width: 90vw;
  max-height: 90vh;

  /* 用于全局拖拽 */
  cursor: grab;
}

/* 鼠标按下时显示“抓取”状态 */
.modal.camera-modal .modal-content:active {
  cursor: grabbing;
}

/* 弹窗主体内的内容区域（视频/Canvas等）可设为透明或自定义 */
.modal.camera-modal .modal-body {
  background: transparent;
  /* 或者保留黑色等 */
  border: none;
  padding: 0;
  margin: 0;
  width: 100%;
  height: 100%;
}

/* 关闭按钮固定在弹窗右上角 */
.modal.camera-modal .modal-close {
  position: absolute;
  top: 8px;
  right: 8px;
  background: none;
  border: none;
  color: white;
  font-size: 24px;
  cursor: pointer;
  padding: 0;
  line-height: 1;
  z-index: 2;
  opacity: 0.7;
}

.modal.camera-modal .modal-close:hover {
  opacity: 1;
}


.camera-viewer-container {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #000;
  border-radius: 8px;
  overflow: hidden;
}

.camera-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background: black;
}

.camera-wrapper video {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.camera-wrapper canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.modal .modal-content {
  position: fixed;
  background: rgba(33, 33, 33, 0.95);
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(8px);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.modal .modal-header {
  min-height: 24px;
  background: rgba(0, 0, 0, 0.3);
  cursor: grab;
  user-select: none;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 0 8px;
  flex-shrink: 0;
}

.modal .modal-body {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.HoloViewButton {
  position: absolute;
  bottom: 24px;
  padding: 12px 24px;
  border: 1px solid #fff;
  border-radius: 4px;
  background: #2196F3;
  color: #fff;
  font: 13px sans-serif;
  text-align: center;
  opacity: 0.8;
  outline: none;
  z-index: 999;
  cursor: pointer;
  width: 220px;
  left: calc(50% - 110px);
  transition: all 0.2s ease;
}

.HoloViewButton:hover {
  opacity: 1;
  background: rgba(149, 208, 220, 0.9);
}

.HoloViewButton.presenting {
  background: rgba(33, 150, 243, 0.8);
}