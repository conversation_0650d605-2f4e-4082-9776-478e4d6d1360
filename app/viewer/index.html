<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="xr-spatial-tracking" content="true">
    <!-- Future MediaPipe Integration -->
    <link rel="stylesheet" href="styles/viewer.css">
    <!-- ICON -->
    <link rel="icon" href="/assets/icons/apple-icon-180.png">
    <!-- manifest -->
    <link rel="manifest" href="./manifest.json">
    <title>Glass Viewer</title>
</head>

<body>
    <!-- <script>
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.register('./service-worker.js');
        }
    </script> -->
    <!-- Main viewer container -->
    <div id="viewer-container">
        <!-- UI components will be dynamically added here -->
    </div>

    <!-- Debug and monitoring -->
    <div id="debug-overlay" class="hidden">
        <div id="fps-counter"></div>
        <div id="scene-stats"></div>
    </div>
    <div id="error-display" class="hidden"></div>

    <!-- Settings and controls -->
    <div id="settings-panel" class="hidden">
        <h3>Viewer Settings</h3>
        <!-- Settings will be dynamically populated -->
    </div>

    <!-- Future input controls -->
    <!-- <div class="input-controls">
        <select id="inputSource" disabled>
            <option value="none">Input Source (Coming Soon)</option>
            <option value="webcam">Webcam</option>
            <option value="video">Video File</option>
        </select>
        <input type="file" id="videoUpload" accept="video/mp4,video/webm">
    </div> -->

    <!-- VAD (Voice Activity Detection) libraries -->
    <script src="https://cdn.jsdelivr.net/npm/onnxruntime-web@1.14.0/dist/ort.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@ricky0123/vad-web@0.0.24/dist/bundle.min.js"></script>

    <!-- Move initialization to a separate module file -->
    <!-- Initialize the viewer -->
    <script type="module" src="./index.js"></script>
    <!-- <script type="module">
        import viewer from './viewer.js';
        import { setupKeyboardShortcuts, showError } from '../utils.js';  // Create this utility file

        // Initialize immediately
        viewer.init()
            .then(setupKeyboardShortcuts)
            .catch(error => {
                console.error('Failed to initialize viewer:', error);
                showError('Failed to initialize viewer. Please check console for details.');
            });

        // Cleanup
        window.addEventListener('beforeunload', () => viewer.dispose());
    </script> -->
</body>

</html>