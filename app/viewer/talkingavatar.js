// @ts-nocheck - Disable TypeScript checking for this file

import * as THREE from 'three';
import { TalkingHeadUI } from '@/modules/talkinghead/src/ui.js';
import { ReadyPlayerMe } from '@/modules/talkinghead/src/readyplayme.js';
// TTS and STT services removed - <PERSON>yun realtime model handles these natively
import { AnimatorFactory } from '@/animation/animatorFactory.js';
import { fileExists, fetchFile, getStoredSeed } from '@/utils/cache.js';
import { analyzeMesh, debugBones } from '../../src/animation/AnimationUtils.js';
import { detectAvatarMesh } from '../../src/viewer/detection/AvatarMeshDetector.ts';
import { ASSETS } from './viewerConfig.js';
import { getEnvVar as getEnv } from "@/config/env";
import { MODEL_CONFIGS } from '@/config/models.js';
import { pinyin } from 'pinyin';
import {
    checkAudioFileExists,
    checkClonedVoiceFileExists,
    findClonedVoiceInFavoriteFolder,
    updateSeedWithVoiceConfig
} from '@/utils/voiceUtils.js';
// Import streaming components
// LLMStreamProcessor removed - agent mode handles all processing natively
// Import agent system components
// Server config removed - using client config from viewerConfig.js
// Import Aliyun centralized configuration
import { ALIYUN_AUDIO_CONFIG } from '@/agent/models/aliyun/config/AliyunConfig.js';
// Import audio utilities
import { detectAudioFormat, convertFloat32ToWav } from '@/media/utils/audioUtils.js';
// Import multimodal processing utilities
import {
    normalizeInput,
    processMultimodal,
    validateMultimodalInput,
    MULTIMODAL_LIMITS
} from '@/media/utils/multimodalUtils.js';
// Voice cloning functionality now handled by @/agent/tools/tts.js
// detectLanguageFromText import removed - language detection now handled by LangGraph agent
import { setLogLevel, LogLevel, createLogger } from '@/utils/logger.js';
import { ANIMATION_REGISTRY } from '@/animation/AnimationConfig.js';
import { LangGraphAgentService } from '@/agent/core.js';
import { endpoints } from '@/config/endpoints.ts';
import { getDownloadServerUrl } from '@/utils/portManager.js';
import { AvatarStateManager } from '@/agent/stateManager.js';
import { MediaCaptureManager } from '@/media/capture/MediaCaptureManager.ts';
import { DEFAULT_AUDIO_CONFIG } from '@/media/modality/audio';

/**
 * TalkingAvatar.js
 * Functionality for handling talking avatar features with Ready Player Me integration
 */
export class TalkingAvatar {
    constructor(viewer, options = {}) {
        this.viewer = viewer;
        this.initialized = false;

        // Set logger to debug level for detailed logging
        setLogLevel(LogLevel.DEBUG);
        this.logger = createLogger('TalkingAvatar');
        this.logger.info('Logger set to DEBUG level');
        this.avatarMesh = null; // Reference to the SkinnedMesh with morph targets
        this.morphTargetDictionary = null; // Map blendshape names to indices
        this.morphTargetInfluences = null;

        this.originalMeshId = null; // Store the ID of the original mesh used for TalkingHead
        this.inTalkingHeadMode = false; // Track if we're in TalkingHead mode
        this.talkingHead = null; // TalkingHead instance
        this.avatarUrl = null; // Store the original avatar URL for reuse
        this.ui = null; // TalkingHead UI instance
        this.name = null; // Store the avatar name


        // In-memory cache of URLs - will be backed by files
        this._avatarUrlCache = new Map();

        // State management - maintained for backward compatibility
        this.isListening = false;
        this.isVideoStreaming = false; // Track video streaming state 
        this.isSpeaking = false;
        this.currentlyPlayingText = null; // Track currently playing TTS text

        // Video frame capture for multimodal input
        this.currentVideoFrames = null; // Store current video frames for multimodal input
        this.videoFrameCaptureInterval = null; // Interval for capturing frames
        this.currentAnimationData = null; // Stores timed blendshape weights, e.g., [{ time: 0.1, weights: {mouthOpen: 0.8, ...} }, ...]
        this.animationStartTime = 0;
        this.audioContext = null; // For Web Audio API
        // Speech APIs removed - Aliyun omni model handles STT/TTS natively

        // Processing options - configure based on multimodal capability
        const isMultimodal = import.meta.env.VITE_IS_LLM_MULTIMODALITY === 'true';
        this.logger.info('Multimodal LLM capability:', isMultimodal);

        // Always use ASR to transcribe audio first for clean text input
        // Enable video input based on multimodal capability
        this.enableVideoInput = options.enableVideoInput !== undefined ? options.enableVideoInput : isMultimodal;

        this.logger.info('Configuration:', {
            enableVideoInput: this.enableVideoInput,
            isMultimodal: isMultimodal,
            audioProcessing: 'ASR-first (always transcribe audio to text)'
        });

        // Audio processing settings
        this.audioAnalyzer = null;
        this.audioDataArray = null;
        this.audioSource = null;
        this.audioProcessor = null;

        // Voice Activity Detection handled by Aliyun omni server VAD
        this.isSpeechDetected = false;
        this.vadTimeout = null;
        this.lastTTSStartTime = null; // Track when TTS last started to prevent immediate VAD interruption

        // Conversation state
        this.waitingForResponse = false;

        // Callback function for conversation events
        this.onConversationUpdate = null;

        // Speech handler for server integration
        this.speechListeningHandler = null;

        // Voice configuration now handled by @/agent system
        this.voiceConfig = {
            // Legacy compatibility only
            currentLanguage: 'auto',
            currentGender: 'male',
            useVoice: { lang: 'en-US', id: 'default' }
        };

        // Realtime mode only - proxy mode removed

        // Fixed streaming support
        this.useStreaming = true; // Enable by default when listening is on
        // LLMStreamProcessor removed - agent mode handles all processing natively
        // StreamingAudioPlayer removed - agent mode handles audio playback natively

        // LangGraph Agent system integration - Unified state management
        this.agentService = null; // Will hold the LangGraphAgentService instance
        this.agentInitialized = false; // Track agent service initialization status

        // State manager for centralized state control via LangGraph
        this.stateManager = new AvatarStateManager(null, {
            onStateChange: (newState, oldState, meta) => {
                this.logger.debug(`[AvatarStateManager] State changed: ${oldState} -> ${newState}`, meta);
            },
            onListeningStart: () => this.logger.debug('[AvatarStateManager] Listening started'),
            onListeningStop: () => this.logger.debug('[AvatarStateManager] Listening stopped'),
            onSpeakingStart: () => this.logger.debug('[AvatarStateManager] Speaking started'),
            onSpeakingStop: () => this.logger.debug('[AvatarStateManager] Speaking stopped'),
            onProcessingStart: () => this.logger.debug('[AvatarStateManager] Processing started'),
            onProcessingStop: () => this.logger.debug('[AvatarStateManager] Processing stopped'),
            onError: (err) => this.logger.error('[AvatarStateManager] Error:', err)
        });

        // Agent system configuration - determined by viewerConfig.js
        // Agent system is always used now

        this.logger.info('LangGraph Agent system configuration:', {
            agentSystemAlwaysEnabled: true,
            llmService: ASSETS.AI_SERVICES?.llm,
            configuredViaViewerConfig: true
        });

        // Add voice configuration tracking
        this.currentVoiceConfig = null;
        this.isUsingClonedVoice = false;
        this.currentRole = null;
        // this.memory = avatarMemory;
        this.agentId = null; // Will be set after avatar is loaded
        // Define state constants
        this.STATES = {
            IDLE: 'idle',           // Not listening
            LISTENING: 'listening',  // Listening but no speech detected yet
            SPEAKING: 'speaking',    // Speech detected, collecting audio
            PROCESSING: 'processing', // Processing collected speech
            SPEECH_DETECTED: 'speech_detected' // Speech detected, waiting for response
        };

        // Barge-in functionality state
        this._lastBargeInTime = null; // Track last barge-in to prevent rapid triggering

        // Animation state tracking
        this.currentTalkingAnimation = null; // Track current talking animation
    }



    async initialize() {
        if (this.initialized) return true;
        try {
            this.logger.info('Initializing...');

            // Initialize Web Audio API if needed - with error handling
            try {
                if (window.AudioContext) {
                    this.audioContext = new window.AudioContext();
                } else if (window.webkitAudioContext) {
                    // @ts-ignore: webkitAudioContext is for Safari compatibility
                    this.audioContext = new window.webkitAudioContext();
                } else {
                    this.logger.warn('AudioContext not supported in this browser, some features may not work');
                }
            } catch (audioError) {
                this.logger.warn('Error initializing AudioContext:', audioError);
                // Continue without AudioContext - some features may not work
            }

            // StreamingAudioPlayer initialization removed - agent mode handles audio natively

            // TTS is now handled as a tool in the LangGraph workflow
            // No separate TTS service instance needed - TTS tool gets auto-registered by agent system
            this.logger.info('TTS functionality available through LangGraph agent tools');

            // Initialize agent service asynchronously (Phase 1: alongside existing services)
            this._initializeAgentService().catch(error => {
                this.logger.warn('Agent service initialization error (non-blocking):', error);
            });

            // Streaming initialization removed - handled by realtime mode

            // Standalone mode is default - connectionManager removed

            // Initialize memory if not already
            // if (!this.memory.initialized) {
            //     await this.memory.initialize();
            // }

            // Mark as initialized even if some components failed
            // This allows the application to continue with limited functionality
            this.initialized = true;
            this.logger.info('Basic initialization complete, services initializing in background');
            return true;
        } catch (error) {
            this.logger.error('Initialization failed:', error);
            // Mark as initialized anyway to prevent blocking the application
            this.initialized = true;
            return true;
        }
    }

    /**
     * Initialize Voice Activity Detection with streaming support
     * @returns {Promise<boolean>} Success status
     * @private
     */
    // VAD initialization removed - Aliyun realtime model handles Voice Activity Detection natively

    // StreamingAudioPlayer initialization removed - agent mode handles audio playback natively

    /**
     * Initialize TTS service based on configuration
     */
    // _initializeTTSService method removed - TTS is now handled by @/agent system

    /**
     * Initialize streaming functionality - simplified for agent mode
     * @returns {Promise<boolean>} Success status
     * @private
     */
    // initializeStreaming method removed - agent mode handles all streaming natively

    // LLMStreamProcessor-based streaming has been removed - agent mode handles all processing natively






    /**
     * Set the voice based on language, gender, and optionally a specific role name
     * @param {string} language - 'english' or 'chinese'
     * @param {string} gender - 'male' or 'female'
     * @param {string} [service] - Optional service override (e.g., 'googleTTS', 'sparkTTS')
     * @param {string} [roleName] - Optional specific role name for SparkTTS (e.g., 'Donald Trump', '周杰伦')
     * @param {boolean} [override=false] - Force override even if a cloned voice is active
     * @returns {Object} The selected voice configuration
     */


    /**
     * Speak text using the current voice configuration
     * @param {string} text - Text to speak
     * @param {Object} [options] - Additional options
     * @returns {Promise<void>}
     */
    // speak method removed - agent mode handles TTS directly through workflow

    // getLipsyncLanguage method removed - handled by @/agent system

    /**
     * Transform the current RPM avatar into a TalkingHead
     * @param {THREE.Object3D} [inputMesh=null] - Optional mesh to transform
     * @returns {Promise<boolean>} Success status
     */
    async transformToTalkingHead(inputMesh = null) {
        // Check if already in TalkingHead mode
        if (this.inTalkingHeadMode) {
            console.log('[TalkingAvatar] Already in TalkingHead mode');
            return true;
        }

        // First check which mesh to use - either the provided mesh or try to find one
        const meshToUse = inputMesh || this.avatarMesh;

        if (!meshToUse) {
            this.viewer.showError("No avatar mesh loaded. Please create an avatar first.");
            return false;
        }
        try {
            // Show loading indicator
            this.viewer.uiSettings?.showProgress();
            this.viewer.uiSettings?.updateStatus('Transforming to TalkingHead...');

            // Store the original mesh ID/reference for later comparison
            this.originalMeshId = meshToUse.uuid;
            this.inTalkingHeadMode = true;

            // IMPORTANT: Make sure we're using the original orbital controller
            // We don't need to store it, just make sure it's enabled and working
            if (!this.viewer.controls) {
                console.warn('[TalkingAvatar] Orbital controls not available, cannot transform to TalkingHead with zooming or rotation');
            }

            // Configure TalkingHead options with adaptive camera settings
            const options = {
                // modelRoot: "Armature",
                lipsyncModules: ["en", "fi", "zh"], // Add Chinese lip-sync
                avatarMood: "neutral",
                cameraView: 'full',
                cameraRotateX: 0,
                useExistRenderer: true,
                // TTS configuration now handled by @/agent/tools/tts.js
            };
            // Create the appropriate animator based on mesh capabilities
            console.log('[TalkingAvatar] Creating animator for mesh');

            // Configure animator options
            const animatorOptions = {
                debug: true,
                headMovementIntensity: 0.7,
                handGestureIntensity: 0.8,
                idleAnimationIntensity: 0.4,
                speakingAnimationIntensity: 0.7
            };

            // Create the animator using the factory
            // This will ensure we get the appropriate animator based on mesh capabilities
            const { AnimatorFactory } = await import('../../src/animation/AnimatorFactory.js');

            // Create animator instance using factory
            const animator = AnimatorFactory.createAnimator(meshToUse, animatorOptions);

            // Initialize the animator (this also starts the animation loop)
            animator.initialize();

            // Store the animator reference in the mesh's userData for animation coordination
            if (meshToUse && meshToUse.userData) {
                console.log('[TalkingAvatar] Storing baseAnimator reference in mesh userData');
                meshToUse.userData.baseAnimator = animator;

                // Also store it in the armature if available
                if (animator.armature && animator.armature.userData) {
                    console.log('[TalkingAvatar] Storing baseAnimator reference in armature userData');
                    animator.armature.userData.baseAnimator = animator;
                    animator.armature.userData.isDoll = true; // Mark as doll mesh for position preservation
                }
            }

            // Make sure the animation loop is running
            if (!animator.isRunning) {
                console.log('[TalkingAvatar] Animation loop not started by initialize, starting manually');
                animator.startAnimationLoop();
            }

            // Create a compatible interface
            this.talkingHead = AnimatorFactory.getAnimatorInterface(animator);

            // Store the animator instance for direct access if needed
            this.animator = animator;

            // Log the type of animator created
            console.log(`[TalkingAvatar] Created ${animator.constructor.name} for mesh`);

            // No need to initialize a separate animation system - our animator already handles this
            // We directly use the created animator with its unified interface

            // TTS service connection is now handled by @/agent/tools/tts.js
            console.log('[TalkingAvatar] TTS service managed by agent workflow');

            // Add for debugging in console
            window.th = this.talkingHead;

            // Show the avatar - CRITICAL: Wait for this promise to resolve
            // Generate a stable filename based on unique identifiers
            const filename = meshToUse.userData.fileName || '';
            const baseFilename = filename.replace(/\.[^/.]+$/, ''); // Remove file extension
            const meshFilePath = `${ASSETS.SAVE_OPTIONS.meshPath}/${baseFilename}.glb`;

            // Set the avatar name property for use in voice cloning
            this.avatar = this.avatar || {};
            // No sanitization - preserve the original name including Chinese characters
            this.avatar.name = baseFilename;
            console.log(`[TalkingAvatar] Set avatar name for voice cloning: ${this.avatar.name}`);
            // Set agentId for memory (avatar name + hash)
            try {
                const { generateCacheKey } = await import('@/utils/cache.js');
                this.agentId = await generateCacheKey('voice', this.avatar.name, null);
                console.log('[TalkingAvatar] Set agentId for memory:', this.agentId);
            } catch (e) {
                console.warn('[TalkingAvatar] Failed to set agentId for memory:', e);
            }

            let meshUrl;

            // First try to check if the mesh file exists directly
            meshUrl = meshFilePath;
            console.log('[TalkingAvatar] Using existing mesh file:', meshUrl, meshToUse.userData);
            // If mesh file doesn't exist, try to load from seed file

            try {
                const gender = meshToUse.userData.gender;
                // Voice configuration is now handled by @/agent system
                // Get voice configuration for compatibility
                const currentVoice = this.voiceConfig?.useVoice || { lang: 'en-US', id: 'default' };

                // Avatar configuration now handled by @/agent system
                const showAvatarConfig = {
                    body: gender === 'female' ? 'F' : 'M',
                    avatarMood: "neutral" // Initial mood
                };

                // We don't need to call showAvatar since we're using our own animators
                // The mesh is already loaded and configured
                console.log('[TalkingAvatar] Using animator with existing mesh');
            } catch (error) {
                console.error('[TalkingAvatar] Error showing avatar:', error);
                throw error; // Re-throw to handle in the parent try-catch
            }

            // Debug the bone structure using our utility method
            this.logSkeletonStructure(this.talkingHead.armature);

            // Initialize UI and connect controls
            this.ui = new TalkingHeadUI(this);
            this.talkingHeadControls = this.ui.createControls(this.talkingHead);

            // Note: We no longer hide the original avatar since we're directly animating it
            // with the new animator system

            this.viewer.uiSettings?.updateStatus('TalkingHead transformation complete!');
            this.viewer.uiSettings?.hideProgress();

            // Store reference to original mesh for cleanup later
            this.originalMesh = meshToUse;

            // Update the mesh selector in the viewer to reflect the transformation
            if (this.viewer.meshSelector) {
                this.viewer.meshSelector.updateAfterTransformation();
            }

            return true;
        } catch (error) {
            console.error('[TalkingAvatar] Failed to transform to TalkingHead:', error);
            this.viewer.showError(`Failed to transform: ${error.message}`);
            this.viewer.uiSettings?.hideProgress();
            this.inTalkingHeadMode = false;
            return false;
        }
    }

    /**
     * Get authentication token for services (placeholder)
     * @returns {Promise<string>} JWT token
     */
    async _getAuthToken() {
        // Implementation depends on your authentication system
        // For demo purposes, return null to use default settings
        return null;
    }

    // Note: _hideOriginalAvatar method removed since we're directly animating the original mesh

    /**
     * Dispose TalkingHead or Animator resources
     */
    disposeTalkingHead() {
        // Stop any active speech
        if (this.isSpeaking) {
            this.stopSpeaking();
        }

        // Stop the animation loop if we have direct access to the animator
        if (this.animator) {
            try {
                this.animator.stopAnimationLoop();
                console.log('[TalkingAvatar] Animation loop stopped');

                // Call dispose method if available
                if (typeof this.animator.dispose === 'function') {
                    this.animator.dispose();
                }
            } catch (error) {
                console.error('[TalkingAvatar] Error stopping animation loop:', error);
            }
            this.animator = null;
        }

        // Clean up talkingHead reference
        this.talkingHead = null;

        // Remove UI elements using the UI class
        if (this.ui) {
            this.ui.dispose();
            this.ui = null;
        }
        this.talkingHeadControls = null;

        // Remove container if it exists
        const container = document.getElementById('talking-head-container');
        if (container) {
            container.parentNode.removeChild(container);
        }

        // Make sure the original orbital controller is enabled
        if (this.viewer.controls) {
            this.viewer.controls.enabled = true;
            console.log('[TalkingAvatar] Re-enabled original orbital controller');
        }

        // Clear original mesh reference
        this.originalMesh = null;
    }

    /**
     * Opens the Ready Player Me avatar creation interface
     */
    openReadyPlayerMe() {
        console.log('[TalkingAvatar] Opening Ready Player Me');
        this.rpm.openAvatarCreator();
    }

    // downloadAndDisplayAvatar method removed - functionality moved to agent workflow

    /**
     * Log the skeleton structure of a 3D model to help with debugging
     * @param {Object3D} object - The 3D object to analyze
     */
    logSkeletonStructure(object) {
        if (!object) return;
        console.log('[TalkingAvatar] Logging skeleton structure');
        // Use the debugBones utility function from AnimationUtils
        const bones = debugBones(object);
        if (bones.length > 0) {
            console.log(`[TalkingAvatar] Found ${bones.length} bones in skeleton`);
        }
    }


    _updateListeningUI(isListening) {
        // Update UI elements to reflect listening status
        // Check if the method exists before calling it
        if (typeof this.viewer.uiSettings?.updateListeningStatus === 'function') {
            this.viewer.uiSettings.updateListeningStatus(isListening);
        } else {
            // Fallback: Update status message if the specific method doesn't exist
            if (this.viewer.uiSettings?.updateStatus) {
                this.viewer.uiSettings.updateStatus(
                    isListening ? 'Listening...' : 'Ready',
                    false
                );
            }
        }

        // Update our own UI elements if they exist
        if (this.ui && this.talkingHeadControls) {
            this.ui.updateStatus(isListening ? 'Listening...' : 'Ready');
        }
    }


    async stopAllListening() {
        console.log('[TalkingAvatar] Stopping all listening activities');

        // Proxy mode removed - using realtime mode directly

        // Update VAD state if available
        if (this.STATES) {
            this.vadState = this.STATES.IDLE;
        }

        // Stop VAD
        if (this.isVadInitialized && this.vadInstance) {
            try {
                console.log('[TalkingAvatar] Stopping VAD instance');
                this.vadInstance.stop();
            } catch (error) {
                console.error('[TalkingAvatar] Error stopping VAD:', error);
            }
        }

        // Stop LocalMediaManager
        if (this.localMediaManager) {
            console.log('[TalkingAvatar] Stopping LocalMediaManager');
            this.localMediaManager.stopListening();
        }

        // Stop STT service
        if (this.sttServiceInstance && this.speechListeningHandler) {
            console.log('[TalkingAvatar] Stopping STT service');
            this.speechListeningHandler();
        }

        // Stop direct media streaming
        if (this.mediaManager) {
            console.log('[TalkingAvatar] Stopping direct media streaming');
            this.mediaManager.stopStreaming();
            this.mediaManager.dispose();
            this.mediaManager = null;
        }

        // Reset all state flags
        this.isListening = false;
        this.isSpeechDetected = false;

        // Clear any pending timeouts
        if (this.vadTimeout) {
            clearTimeout(this.vadTimeout);
            this.vadTimeout = null;
        }

        // Update UI
        this._updateListeningUI(false);

        // Update button UI if present
        if (this.talkingHeadControls) {
            this.talkingHeadControls.listenButton.innerHTML = '👂 Listen';
            this.talkingHeadControls.listenButton.style.backgroundColor = 'rgba(0, 120, 255, 0.7)';
            this.talkingHeadControls.statusSpan.textContent = 'Ready';
        }

        console.log('[TalkingAvatar] All listening activities stopped');
    }



    startListening() {
        // If already listening, toggle off instead
        if (this.isListening) {
            console.log('[TalkingAvatar] Already listening, stopping');
            this.stopListening().catch(error =>
                console.error('[TalkingAvatar] Error stopping listening:', error)
            );
            return;
        }

        this.isListening = true;
        this._updateListeningUI(true);

        // Reset speech detection state
        this.isSpeechDetected = false;
        if (this.vadTimeout) {
            clearTimeout(this.vadTimeout);
            this.vadTimeout = null;
        }

        // Set initial VAD state
        if (this.STATES) {
            this.vadState = this.STATES.LISTENING;
        }

        // Set listening animation state if we have an animator
        if (this.animator) {
            this.animator.setState('listening');
            console.log('[TalkingAvatar] Set animator to listening state');
        }

        // Initialize realtime mode for voice streaming
        if (this.agentService?.model?.model?.includes('realtime')) {
            console.log('[TalkingAvatar] 🎙️  Using Aliyun Qwen-Omni realtime mode with built-in VAD');
            this._initializeRealtimeMode().then(success => {
                if (success) {
                    // Start MediaCaptureManager with centralized audio configuration
                    this._startRealtimeListening({
                        audio: {
                            echoCancellation: true,
                            noiseSuppression: true,
                            autoGainControl: true,
                            sampleRate: DEFAULT_AUDIO_CONFIG.sampleRate
                        },
                        vadMode: 'server'
                    }).catch(error => {
                        this.logger.error('Error starting realtime listening:', error);
                        this._updateStatus('Error starting listening');
                    });
                } else {
                    this.logger.error('Failed to initialize realtime mode');
                    this._updateStatus('Failed to initialize realtime mode');
                }
            }).catch(error => {
                this.logger.error('Error initializing realtime mode:', error);
                this._updateStatus('Error initializing realtime mode');
            });
        } else {
            // Use HTTP mode with client VAD
            console.log('[TalkingAvatar] 🎤 Using HTTP mode with client VAD');
            this._initializeHttpVAD().catch(error => {
                this.logger.error('Error initializing HTTP VAD:', error);
                this._updateStatus('Error initializing VAD');
            });
        }
    }

    /**
     * Handle audio data from media capture
     * @param {ArrayBuffer} audioData - PCM audio data
     * @private
     */
    _onAudioData(audioData) {
        if (!this.isListening || !this.agentService?.model) {
            return;
        }

        // Rate limit audio chunks to prevent 1011 errors
        // Aliyun has a limit of ~8 chunks per second
        const now = Date.now();
        if (!this._lastAudioSendTime) {
            this._lastAudioSendTime = now;
            this._audioChunkCounter = 0;
            this._audioRateWindow = now;
        }

        // Track audio rate in a 1-second window
        this._audioChunkCounter++;
        if (now - this._audioRateWindow > 1000) {
            const rate = this._audioChunkCounter / ((now - this._audioRateWindow) / 1000);
            if (rate > 6) {
                this.logger.warn(`⚠️ Audio chunk rate too high: ${rate.toFixed(2)}/sec (max safe: 5/sec)`);
            }
            this._audioChunkCounter = 0;
            this._audioRateWindow = now;
        }

        // Enforce minimum interval between audio chunks (200ms = 5 chunks/sec max)
        const minInterval = 200;
        const timeSinceLastSend = now - this._lastAudioSendTime;

        if (timeSinceLastSend < minInterval) {
            return; // Skip this chunk to maintain safe rate
        }

        this._lastAudioSendTime = now;
        this._sendRealtimeAudio(audioData);
    }

    /**
     * Send audio data to realtime API with error handling
     * @param {ArrayBuffer} audioData - PCM audio data
     * @private
     */
    async _sendRealtimeAudio(audioData) {
        try {
            if (!this.agentService?.model) {
                if (!this._hasLoggedNoModel) {
                    this.logger.warn('Cannot send realtime audio: No model available');
                    this._hasLoggedNoModel = true;
                }
                return false;
            }

            if (!this.agentService.model.isRealtimeModeActive()) {
                if (!this._hasLoggedNotActive) {
                    this.logger.warn('Cannot send realtime audio: Realtime mode not active');
                    this._hasLoggedNotActive = true;
                }
                return false;
            }

            // Apply rate limiting based on centralized configuration
            const now = Date.now();
            if (this._lastAudioSendTime && (now - this._lastAudioSendTime) < DEFAULT_AUDIO_CONFIG.chunkDurationMs) {
                return false; // Skip this chunk to maintain rate limit
            }

            // Send audio data to model
            const success = await this.agentService.model.sendRealtimeAudio(audioData);
            if (success) {
                this._lastAudioSendTime = now;
            }
            return success;
        } catch (error) {
            this.logger.error('Error sending realtime audio:', error);
            return false;
        }
    }

    // _fallbackToNaiveVAD method removed - agent workflow handles all speech recognition directly

    async stopListening() {
        if (!this.isListening) return;

        console.log('[TalkingAvatar] Stopping speech recognition');

        // Clear any VAD timeout
        if (this.vadTimeout) {
            clearTimeout(this.vadTimeout);
            this.vadTimeout = null;
        }

        // Update VAD state if available
        if (this.STATES) {
            this.vadState = this.STATES.IDLE;
        }

        // Stop realtime mode
        if (this.agentService?.model) {
            try {
                console.log('[TalkingAvatar] 🔇 Stopping Qwen-Omni realtime listening');

                // Close realtime WebSocket connection if active
                if (this.agentService.model.closeRealtimeMode) {
                    this.agentService.model.closeRealtimeMode();
                }
            } catch (error) {
                console.error('[TalkingAvatar] Error closing realtime mode:', error);
            }
        }

        // Stop media capture if active
        if (this.mediaCaptureManager) {
            try {
                await this.mediaCaptureManager.stopCapture();
                this.mediaCaptureManager = null;
            } catch (error) {
                console.error('[TalkingAvatar] Error stopping media capture:', error);
            }
        }

        // Reset state
        this.isListening = false;
        this.isSpeechDetected = false;
        this.isRealtimeReady = false;
        this._lastAudioSendTime = null;
        this._audioChunkCounter = 0;
        this._audioRateWindow = null;

        // Update UI
        this._updateListeningUI(false);
        this._updateStatus('Idle');

        // Update animation state
        if (this.animator) {
            this.animator.setState('idle');
            console.log('[TalkingAvatar] Set animator to idle state');
        }
    }

    /**
     * Handle user input from any modality (text, audio, video, or combined)
     * Unified entry point that processes all input types through the Aliyun Qwen-Omni realtime pipeline
     * 
     * OPTIMIZED FOR ALIYUN QWEN-OMNI REALTIME API:
     * - Default mode: audio + video → LLM → audio response with tool calls
     * - Server VAD automatically detects speech start/stop
     * - Video frames sent at 2fps according to Aliyun guidelines
     * - Audio sent first, then video frames (Aliyun requirement)
     * - Tool calls integrated with LangGraph for enhanced functionality
     * 
     * @param {string|Float32Array|Object} input - Text input, audio data, or multimodal input object
     * @returns {Promise<string>} The AI response text
     */
    async handleUserInput(input) {
        if (!input) return;

        // 🎯 UNIFIED INPUT PROCESSING PIPELINE optimized for Aliyun Qwen-Omni
        const normalizedInput = normalizeInput(input, {
            currentVideoFrames: this.currentVideoFrames,
            enableVideoInput: this.enableVideoInput,
            isVideoStreaming: this.isVideoStreaming
        });

        this.logger.info('🎯 Aliyun Qwen-Omni Multimodal Input Processing:', {
            hasText: !!normalizedInput.text,
            hasAudio: !!normalizedInput.audio,
            hasVideo: !!normalizedInput.video,
            textLength: normalizedInput.text?.length || 0,
            videoFrameCount: normalizedInput.video?.length || 0,
            isListening: this.isListening,
            isVideoStreaming: this.isVideoStreaming,
            realtimeMode: 'Aliyun Qwen-Omni with Server VAD'
        });

        // 🛑 BARGE-IN: Stop any ongoing processing (Aliyun supports interrupt_response)
        if (this.isSpeaking || this.waitingForResponse) {
            this.logger.info('🛑 Barge-in triggered - leveraging Aliyun interrupt_response capability');
            await this._handleBargeIn(normalizedInput);
        }

        try {
            this.waitingForResponse = true;
            this._updateStatus('Processing multimodal input...');

            // Process through Aliyun Qwen-Omni realtime pipeline
            const aiResponse = await this._processMultimodal(normalizedInput);

            this.waitingForResponse = false;
            this.logger.info('✅ Aliyun Qwen-Omni response received:', aiResponse.substring(0, 100) + '...');

            // Notify UI
            if (this.onConversationUpdate) {
                this.onConversationUpdate({ type: 'aiMessage', text: aiResponse });
            }

            return aiResponse;

        } catch (error) {
            this.logger.error('❌ Error in Aliyun Qwen-Omni processing:', error);
            this.waitingForResponse = false;

            if (this.onConversationUpdate) {
                this.onConversationUpdate({ type: 'error', text: error.message || 'Multimodal processing error' });
            }

            return "I'm sorry, I encountered an error processing your multimodal request.";
        } finally {
            this._updateStatus('Ready');
        }
    }

    /**
     * Process multimodal input through real-time API
     * Optimized for the default use case: audio + video → LLM → audio + tool calls
     * @param {Object} normalizedInput - Normalized multimodal input
     * @returns {Promise<string>} AI response text
     * @private
     */
    async _processMultimodal(normalizedInput) {
        // Primary path: Use direct LangGraphAgentService with Aliyun Qwen-Omni realtime mode
        if (this.agentService?.model?.isRealtimeModeActive()) {
            this.logger.debug('🚀 Processing through Aliyun Qwen-Omni realtime mode');

            // Build context for LangGraph tool integration
            const context = await this._buildUnifiedContext(normalizedInput);

            // Send multimodal data according to Aliyun requirements:
            // 1. Audio first, 2. Video frames at 2fps, 3. Let server VAD handle timing
            if (normalizedInput.audio || normalizedInput.video) {
                let success = true;

                // Send audio data if available
                if (normalizedInput.audio) {
                    success = await this._sendRealtimeAudio(normalizedInput.audio);
                    if (!success) {
                        throw new Error('Failed to send audio data to Aliyun Qwen-Omni');
                    }
                }

                // Send video frames if available (after audio)
                if (normalizedInput.video && Array.isArray(normalizedInput.video)) {
                    for (const frame of normalizedInput.video) {
                        // TODO: Implement video frame sending when needed
                        // Currently focusing on audio-only realtime mode
                    }
                }

                // Commit audio buffer to trigger processing
                if (normalizedInput.audio) {
                    await this._commitRealtimeAudio();
                }

                // Return placeholder - actual response will come through WebSocket callbacks
                return "Processing input in realtime mode...";
            }

            // Text-only fallback
            if (normalizedInput.text) {
                // Generate response using LangGraph agent service
                const result = await this.agentService.generateResponse(
                    normalizedInput.text,
                    {
                        sessionId: context.sessionId || this._getSessionId(),
                        language: context.language || 'english',
                        stream: true,
                        configurable: {
                            language: context.language || 'english',
                            useTools: true
                        }
                    }
                );

                return typeof result === 'string' ? result : 'Response processed';
            }
        }

        // Fallback: Use HTTP API for text and images
        if (normalizedInput.text || normalizedInput.video) {
            this.logger.debug('🌐 Falling back to HTTP API for text/image processing');

            // Extract content from input
            const content = await this._extractContent(normalizedInput);

            // Build unified context
            const context = await this._buildUnifiedContext(normalizedInput);

            // Generate response using AI
            return await this._generateAIResponse(content, context);
        }

        throw new Error('No valid input for multimodal processing');
    }

    /**
     * Extract and transcribe content from all input types
     * @private
     */

    async _extractContent(normalizedInput) {
        const content = {
            text: normalizedInput.text,
            videoFrames: normalizedInput.video,
            metadata: normalizedInput.metadata
        };

        // Handle audio - pass directly to Aliyun omni model for native processing
        if (normalizedInput.audio) {
            this.logger.info('🎙️ Audio input detected - Aliyun omni model will handle STT natively');
            content.audioData = normalizedInput.audio;
            this.logger.debug('✅ Audio prepared for native omni model processing, length:', content.audioData?.length);
        }

        // Create minimal metadata for text input
        if (content.text && !content.metadata) {
            content.metadata = {
                source: 'text_input'
            };
        }

        // Notify UI with user input
        if (this.onConversationUpdate && content.text) {
            this.onConversationUpdate({ type: 'userMessage', text: content.text });
        }

        return content;
    }

    /**
     * Build unified context for all processing modes
     * @private
     */
    async _buildUnifiedContext(content) {
        this.logger.debug('🔧 Building unified context for processing');

        // Context building now handled by @/agent system
        const context = {
            meshFileName: this.avatarMesh?.userData?.fileName,
            sessionId: this._getSessionId(),
            useAnimation: true,
            userInput: content.text,
            skipTTS: false
        };

        // Legacy LLM system is no longer used - agent mode is always enabled

        // Language consistency now handled by @/agent system
        if (content.metadata?.detectedLanguage) {
            context.asrMetadata = content.metadata;
        }

        return context;
    }

    /**
     * Generate AI response with unified animation handling - integrates agent system
     * @private
     */
    async _generateAIResponse(content, context) {
        this.logger.info('🚀 Generating AI response');
        this.logger.debug('📝 Input content:', {
            text: content.text,
            hasVideoFrames: !!content.videoFrames?.length,
            videoFrameCount: content.videoFrames?.length || 0
        });
        this.logger.debug('🔧 Context:', {
            language: context.language,
            useAnimation: context.useAnimation,
            agentModeEnabled: true, // Agent mode is always enabled now
            hasTTSService: !!context.ttsService,
            agentSystemAlwaysEnabled: true,
            hasAgentAdapter: !!this.agentAdapter
        });

        try {
            let result;

            // Route through LangGraph Agent via Adapter (agent mode is always used now)
            if (this.agentAdapter) {
                this.logger.info('🤖 Using LangGraph Agent System via Adapter');

                // Handle audio input (realtime mode is always used)
                if (content.audioData) {
                    this.logger.info('🎙️ Processing audio in realtime mode');

                    // Initialize realtime mode
                    await this.agentAdapter.initializeRealtimeMode();

                    // Send audio directly to realtime model
                    const audioSent = this.agentAdapter.sendRealtimeAudio(content.audioData);
                    if (audioSent) {
                        this.logger.debug('✅ Audio sent to realtime model');
                        // For realtime mode, the response will come through WebSocket callbacks
                        // Return early as processing is handled asynchronously
                        return 'Processing audio in realtime mode...';
                    } else {
                        this.logger.warn('Failed to send audio to realtime model, falling back to text mode');
                        // Fallback to text-based processing if audio fails
                        content.text = content.text || '[Audio input - transcription not available]';
                    }
                }

                // Use adapter for agent response generation
                result = await this.agentAdapter.generateResponse(content, context);

                this.logger.info('🤖 Agent response processed via adapter. TTS handled automatically via agent tool invocation.');

            } else {
                this.logger.debug('🏛️ Using Legacy LLM System');

                // Build input for legacy LLM (text or multimodal)
                const llmInput = content.videoFrames?.length > 0 ? {
                    text: content.text,
                    videoFrames: content.videoFrames,
                    additionalContent: {
                        videoFrames: content.videoFrames,
                        text: content.text
                    }
                } : content.text;

                // Generate response using legacy pipeline with timeout
                const timeoutPromise = new Promise((_, reject) => {
                    setTimeout(() => reject(new Error('generateResponse timeout after 30 seconds')), 30000);
                });

                result = await Promise.race([
                    generateResponse(llmInput, context),
                    timeoutPromise
                ]);
            }

            console.log('[TalkingAvatar] 📥 Received result:', {
                hasResponse: !!result.response,
                hasAnimation: !!result.animation,
                responseLength: result.response?.length || 0,
                animationFile: result.animation?.file || 'none',
                systemUsed: 'LangGraph Agent via Adapter' // Agent mode is always used now
            });

            // Legacy animation handling is no longer needed since agent mode is always used

            return result.response;

        } catch (error) {
            this.logger.error('❌ Error in _generateAIResponse:', error);
            throw error;
        }
    }

    /**
     * Handle barge-in interruption via adapter
     */
    async _handleBargeIn(normalizedInput) {
        // Always use agent adapter for barge-in handling
        if (this.agentAdapter) {
            await this.agentAdapter.handleBargeIn(normalizedInput);
        } else {
            this.logger.warn('Agent adapter not available - barge-in handling may be incomplete');
            // Basic fallback: just stop speaking
            this.stopSpeaking();
            this.waitingForResponse = false;
        }
    }

    /**
     * Update UI status consistently
     * @private
     */
    _updateStatus(status) {
        if (this.talkingHeadControls) {
            this.talkingHeadControls.statusSpan.textContent = status;
        }
    }

    /**
     * Normalize any input to consistent format optimized for Aliyun Qwen-Omni multimodal processing
     * Output format: { text, audio, video, metadata }
     * - audio: PCM16 audio data (24kHz mono for real-time APIs)
     * - video: Array of Base64-encoded JPEG frames (480P/720P/1080P, max 500KB each)
     * - Automatically captures video frames when streaming is active
     * 
     * NOTE: This functionality has been moved to src/media/utils/multimodalUtils.js
     * @param {any} input - Raw input data
     * @returns {Object} Normalized multimodal input
     * @private
     */
    _normalizeInput(input) {
        // Delegate to the new multimodal utilities
        return normalizeInput(input, {
            currentVideoFrames: this.currentVideoFrames,
            enableVideoInput: this.enableVideoInput,
            isVideoStreaming: this.isVideoStreaming
        });
    }

    /**
     * Get a human-readable label for the input type optimized for real-time modes
     * @private
     */
    _getInputTypeLabel(normalizedInput) {
        if (normalizedInput.metadata?.inputType === 'multimodal') return 'multimodal';
        if (normalizedInput.metadata?.inputType === 'audio_video') return 'audio+video (default)';
        if (normalizedInput.audio) return 'voice';
        if (normalizedInput.video) return 'video';
        return normalizedInput.metadata?.inputType || 'text';
    }

    // Animation triggering removed - LangGraph animation tools handle animation selection and execution via tool calling
    // Animation duration methods removed - now using centralized functions from AnimationConfig.js
    // 1. Add a single method to manage speaking state
    async _setSpeakingState(isSpeaking) {
        if (this.isSpeaking === isSpeaking) return; // No change needed

        this.isSpeaking = isSpeaking;

        // Update UI
        if (this.talkingHeadControls) {
            this.talkingHeadControls.statusSpan.textContent = isSpeaking ? 'Speaking...' : 'Ready';
        }

        // Update animation state if we have an animator
        if (this.animator) {
            if (isSpeaking) {
                // Set speaking state with speak_with_hands animation
                this.animator.setState('speaking');
                console.log('[TalkingAvatar] Set animator to speaking state');
            } else if (this.isListening) {
                // If we're listening, set listening state
                this.animator.setState('listening');
                console.log('[TalkingAvatar] Set animator to listening state');
            } else {
                // Otherwise set idle state
                this.animator.setState('idle');
                console.log('[TalkingAvatar] Set animator to idle state');
            }
        }

        // VAD handled by Aliyun omni server - no local VAD needed

        this.stateManager.setState(isSpeaking ? 'speaking' : (this.isListening ? 'listening' : 'idle'));
    }

    // 2. Modify stopSpeaking to use the new method
    stopSpeaking() {
        if (!this.isSpeaking) return;

        console.log('[TalkingAvatar] Stopping all speech playback');

        try {
            // Stop speech based on current speech method
            if (this.talkingHead && typeof this.talkingHead.stopSpeaking === 'function') {
                this.talkingHead.stopSpeaking();
            }

            // Stop any active TTS service
            if (this.ttsServiceInstance) {
                this.ttsServiceInstance.stop();
            }

            // Stream processor no longer used in agent mode

            // Stop TTS service wrapper if active
            if (this.ttsServiceWrapper) {
                this.ttsServiceWrapper.stop();
            }

            // Audio queue clearing is now handled by agent adapter
        } catch (error) {
            console.debug('[TalkingAvatar] Error during stop operation (likely due to race condition):', error.message);
        }

        // Reset state
        this._setSpeakingState(false);
        this.currentAnimationData = null;
        this.resetBlendshapes();
    }

    resetBlendshapes() {
        if (!this.avatarMesh || !this.morphTargetInfluences) return;
        for (let i = 0; i < this.morphTargetInfluences.length; i++) {
            this.morphTargetInfluences[i] = 0;
        }
    }
    // Random talking animation removed - LangGraph animation tools handle contextual animation selection via tool calling

    // Animation stopping moved to LangGraph animation tools - use stop_animation tool

    /**
     * Exit TalkingHead mode and return to normal mesh display
     */
    exitTalkingHeadMode() {
        if (!this.inTalkingHeadMode) {
            return;
        }

        // Stop any active listening or streaming
        this.stopListening().catch(error =>
            console.error('[TalkingAvatar] Error stopping listening in exitTalkingHeadMode:', error)
        );

        // Explicitly dispose of the direct audio stream manager if it exists
        if (this.directAudioStreamManager) {
            console.log('[TalkingAvatar] Disposing direct audio stream manager');
            this.directAudioStreamManager.stopStreaming();
            this.directAudioStreamManager.dispose();
            this.directAudioStreamManager = null;
        }

        // Stream processor no longer used in agent mode

        // Stop the TalkingHead before disposal
        if (this.talkingHead) {
            // Explicitly stop the TalkingHead animation and processing
            // Check if stop method exists (it might not in the animator interface)
            if (typeof this.talkingHead.stop === 'function') {
                this.talkingHead.stop();
                console.log('[TalkingAvatar] TalkingHead stopped explicitly');
            } else if (this.animator && typeof this.animator.stopAnimationLoop === 'function') {
                // If stop is not available, try to stop the animation loop directly
                this.animator.stopAnimationLoop();
                console.log('[TalkingAvatar] Animation loop stopped explicitly');
            }
        }

        // Clean up animation controller
        if (this.animationController) {
            this.animationController.stop();
            this.animationController = null;
            console.log('[TalkingAvatar] Animation controller stopped and cleaned up');
        }

        // Clean up TalkingHead resources
        this.disposeTalkingHead();

        // Reset tracking variables
        this.originalMeshId = null;
        this.inTalkingHeadMode = false;

        console.log('[TalkingAvatar] Exited TalkingHead mode');

        // Update UI if needed
        this.viewer.uiSettings?.updateStatus('Returned to standard viewer mode');

        // Update MeshSelector UI if available
        if (this.viewer.meshSelector) {
            // Re-enable the Make Avatar button
            const button = this.viewer.meshSelector.makeAvatarButton;
            if (button) {
                button.disabled = false;
                button.classList.add('available');
                button.title = 'Transform selected mesh to talking avatar';
            }
        }
    }

    /**
     * Checks if a mesh can be transformed to TalkingHead
     * @param {THREE.Object3D} mesh - The mesh to check
     * @returns {boolean} - True if the mesh can be transformed
     */
    canTransformToTalkingHead(mesh) {
        // Exit talking head mode if we're already in it
        if (this.inTalkingHeadMode) {
            this.exitTalkingHeadMode();
            return false;
        }

        // Default check for transformable meshes - can be extended
        return mesh && mesh.isObject3D;
    }


    /**
     * Handle language detection when speech is recognized
     * @param {string} text - Recognized speech text
     * @param {string} detectedLanguage - Language code from speech recognition
     */
    // handleLanguageDetection removed - language detection is now handled by the LangGraph agent and Aliyun omni model



    /**
     * Start video streaming to LLM
     * This method integrates with the UI's video streaming functionality
     */
    async startVideoStreaming() {
        try {
            console.log('[TalkingAvatar] Starting video streaming integration...');

            // Check if video input is enabled
            if (!this.enableVideoInput) {
                throw new Error('Video input is not enabled. Set enableVideoInput option to true.');
            }


            // Set up video frame capture if camera manager is available
            this._setupVideoFrameCapture();

            // Set video streaming state
            this.isVideoStreaming = true;
            console.log('[TalkingAvatar] Video streaming state set to true');

            // Update UI status
            if (this.talkingHeadControls) {
                this.talkingHeadControls.statusSpan.textContent = 'Video streaming enabled';
            }

            console.log('[TalkingAvatar] Video streaming integration ready');
            return true;

        } catch (error) {
            console.error('[TalkingAvatar] Error starting video streaming:', error);
            this.isVideoStreaming = false;
            throw error;
        }
    }

    /**
     * Set up video frame capture for multimodal input
     * @private
     */
    _setupVideoFrameCapture() {
        try {
            // Clear any existing frame capture interval
            if (this.videoFrameCaptureInterval) {
                clearInterval(this.videoFrameCaptureInterval);
            }

            // Get camera manager from UI controls or viewer
            let cameraManager = null;
            if (this.talkingHeadControls && this.talkingHeadControls.cameraManager) {
                cameraManager = this.talkingHeadControls.cameraManager;
                console.log('[TalkingAvatar] Using camera manager from UI controls:', !!cameraManager);
            } else if (this.viewer && this.viewer.cameraManager) {
                cameraManager = this.viewer.cameraManager;
                console.log('[TalkingAvatar] Using camera manager from viewer:', !!cameraManager);
            }

            // 🔍 DEBUG: Log camera manager details
            console.log('[TalkingAvatar] Camera manager detection:', {
                hasUIControls: !!this.talkingHeadControls,
                hasUIControlsCameraManager: !!(this.talkingHeadControls && this.talkingHeadControls.cameraManager),
                hasViewer: !!this.viewer,
                hasViewerCameraManager: !!(this.viewer && this.viewer.cameraManager),
                foundCameraManager: !!cameraManager,
                cameraManagerType: cameraManager ? cameraManager.constructor.name : 'none'
            });

            if (!cameraManager) {
                console.warn('[TalkingAvatar] No camera manager available for video frame capture');
                return;
            }

            // Check if camera is active
            if (!cameraManager.isCameraActive()) {
                console.warn('[TalkingAvatar] Camera is not active, frame capture will not work');
                return;
            }

            console.log('[TalkingAvatar] Setting up video frame capture...');

            // Capture frames periodically for multimodal input (every 2 seconds)
            this.videoFrameCaptureInterval = setInterval(async () => {
                try {
                    console.log('[TalkingAvatar] 🎬 Frame capture interval triggered:', {
                        isVideoStreaming: this.isVideoStreaming,
                        cameraActive: cameraManager.isCameraActive(),
                        cameraManagerExists: !!cameraManager
                    });

                    if (this.isVideoStreaming && cameraManager.isCameraActive()) {
                        console.log('[TalkingAvatar] 📸 Attempting to capture frames...');
                        const frames = await cameraManager.captureCurrentFrames(3); // Capture 3 frames
                        if (frames && frames.length > 0) {
                            this.currentVideoFrames = frames;
                            console.log('[TalkingAvatar] ✅ Captured', frames.length, 'video frames for multimodal input, frame sizes:', frames.map(f => f.length));
                        } else {
                            console.warn('[TalkingAvatar] ⚠️ Frame capture returned empty or invalid frames:', frames);
                        }
                    } else {
                        console.log('[TalkingAvatar] ⏸️ Skipping frame capture - conditions not met');
                    }
                } catch (error) {
                    console.warn('[TalkingAvatar] ❌ Error capturing video frames:', error);
                }
            }, 2000); // Capture frames every 2 seconds

            console.log('[TalkingAvatar] Video frame capture setup complete');

        } catch (error) {
            console.error('[TalkingAvatar] Error setting up video frame capture:', error);
        }
    }

    /**
     * Stop video streaming
     */
    async stopVideoStreaming() {
        try {
            console.log('[TalkingAvatar] Stopping video streaming integration...');

            // Clear video streaming state
            this.isVideoStreaming = false;

            // Stop video frame capture
            if (this.videoFrameCaptureInterval) {
                clearInterval(this.videoFrameCaptureInterval);
                this.videoFrameCaptureInterval = null;
                console.log('[TalkingAvatar] Stopped video frame capture');
            }

            // Clear current video frames
            this.currentVideoFrames = null;

            // Update UI status
            if (this.talkingHeadControls) {
                this.talkingHeadControls.statusSpan.textContent = 'Video streaming disabled';
            }

            console.log('[TalkingAvatar] Video streaming integration stopped');
            return true;

        } catch (error) {
            console.error('[TalkingAvatar] Error stopping video streaming:', error);
            throw error;
        }
    }



    dispose() {
        console.log('[TalkingAvatar] Disposing resources');

        // Clean up video streaming if active
        if (this.isVideoStreaming) {
            this.stopVideoStreaming().catch(error => {
                console.error('[TalkingAvatar] Error stopping video streaming during dispose:', error);
            });
        }

        // Clean up video frame capture
        if (this.videoFrameCaptureInterval) {
            clearInterval(this.videoFrameCaptureInterval);
            this.videoFrameCaptureInterval = null;
        }
        this.currentVideoFrames = null;

        // Stop listening first
        this.stopListening().catch(error =>
            console.error('[TalkingAvatar] Error stopping listening in dispose:', error)
        );

        if (this.inTalkingHeadMode) {
            this.exitTalkingHeadMode();
        }

        // Clean up animation controller
        if (this.animationController) {
            this.animationController.stop();
            this.animationController = null;
            console.log('[TalkingAvatar] Animation controller stopped and cleaned up');
        }

        // TTS services are now handled by @/agent/tools/tts.js

        // Dispose TTS service wrapper if active
        if (this.ttsServiceWrapper) {
            this.ttsServiceWrapper.stop();
            this.ttsServiceWrapper = null;
        }

        // Stream processor no longer used in agent mode

        // STT services removed - handled by Aliyun omni model natively

        // Dispose direct audio stream manager if active
        if (this.directAudioStreamManager) {
            this.directAudioStreamManager.dispose();
            this.directAudioStreamManager = null;
        }

        // Remove event listeners
        window.removeEventListener('modeSwitch', this._handleModeSwitch);

        // Speech synthesis handled by Aliyun omni model

        if (this.audioContext && this.audioContext.state !== 'closed') {
            this.audioContext.close(); // Close audio context
        }

        this.avatarMesh = null;
        this.morphTargetDictionary = null;
        this.morphTargetInfluences = null;

        // Dispose UI
        if (this.ui) {
            this.ui.dispose();
            this.ui = null;
        }

        // Clean up RPM reference
        this.rpm = null;

        this.initialized = false;
        this.isVideoStreaming = false;
    }

    /**
     * Dispose of streaming resources - simplified for agent mode
     * @returns {Promise<void>}
     * @private
     */
    async _disposeStreaming() {
        try {
            // StreamingAudioPlayer has been removed - agent mode handles audio natively
        } catch (error) {
            console.error('[TalkingAvatar] Error disposing streaming resources:', error);
        }
    }

    /**
     * Transcribe audio data using STT service
     * @param {Float32Array} audioData - Raw audio data from VAD
     * @returns {Promise<string>} Transcribed text
     * @private
     */
    // _transcribeAudioWithSTT removed - Aliyun omni model handles STT directly with audio input




    /**
     * Set the cloned voice role - proxy to agent system for UI compatibility
     * @param {Object} voiceData - The voice data from UI
     * @param {boolean} [isPermanent=false] - Whether this is a permanent setting (via favorite) or temporary
     * @returns {Promise<boolean>} Success status
     */
    async setClonedVoice(voiceData, isPermanent = false) {
        try {
            this.logger.info('🎤 Setting cloned voice via agent system:', {
                roleName: voiceData.roleName,
                isPermanent,
                hasAgentAdapter: !!this.agentAdapter
            });

            // Use agent adapter if available
            if (this.agentAdapter && this.agentAdapter.voiceCloningService) {
                const result = await this.agentAdapter.voiceCloningService.setVoice({
                    roleName: voiceData.roleName,
                    referenceAudio: voiceData.reference_audio_file || voiceData.reference_audio_file_path,
                    referenceText: voiceData.reference_text,
                    isPermanent
                });

                if (result.success) {
                    this.currentClonedVoice = voiceData.roleName;
                    this.isUsingClonedVoice = true;
                    this.logger.info('✅ Cloned voice set successfully via agent system');
                    return true;
                }
            }

            // Fallback: store voice configuration for later use
            this.currentVoiceConfig = voiceData;
            this.currentClonedVoice = voiceData.roleName;
            this.isUsingClonedVoice = true;

            this.logger.info('✅ Voice configuration stored for agent system use');
            return true;

        } catch (error) {
            this.logger.error('❌ Error setting cloned voice:', error);
            return false;
        }
    }

    /**
     * Initialize Agent Service directly using LangGraphAgentService
     * This directly uses the core.js framework instead of going through TalkingAvatarAdapter
     * @returns {Promise<boolean>} Success status
     * @private
     */
    async _initializeAgentService() {
        try {
            this.logger.info('Initializing LangGraph agent service directly...');

            // Import LangGraphAgentService directly from core.js
            const { LangGraphAgentService } = await import('@/agent/core.js');

            // Create LangGraphAgentService with TalkingAvatar-specific configuration
            this.agentService = new LangGraphAgentService({
                // Core LLM configuration
                temperature: 0.7,
                maxTokens: 2048,

                // Model provider configuration
                modelProvider: 'aliyun',
                model: 'qwen-omni-turbo-realtime',
                aliyunApiKey: getEnv('VITE_DASHSCOPE_API_KEY', ''),

                // Use centralized audio configuration
                audioConfig: {
                    sampleRate: DEFAULT_AUDIO_CONFIG.sampleRate,
                    numChannels: DEFAULT_AUDIO_CONFIG.numChannels,
                    bitDepth: DEFAULT_AUDIO_CONFIG.bitDepth,
                    voice: 'Chelsie',
                    format: 'wav'
                },

                // Enable realtime mode for WebSocket streaming
                enableRealtimeMode: true,
                apiMode: 'websocket',

                // LangGraph-specific configuration
                autoRegisterTools: true,
                streamingMode: 'messages',

                // Avatar-specific state management callbacks
                onStateUpdate: this._handleAgentStateUpdate.bind(this),
                onModeChange: this._handleAgentModeChange.bind(this),

                // Streaming callbacks for avatar integration
                onStreamingStart: this._handleStreamingStart.bind(this),
                onProcessingStart: this._handleProcessingStart.bind(this),
                onProcessingEnd: this._handleProcessingEnd.bind(this),

                // Enhanced conversation callbacks
                onConversationUpdate: this._handleConversationUpdate.bind(this)
            });

            // Initialize the core agent service
            await this.agentService.initialize();
            this.logger.info('LangGraph agent service initialized successfully');

            return true;
        } catch (error) {
            this.logger.error('Failed to initialize LangGraph agent service:', error);
            return false;
        }
    }

    /**
     * Initialize voice cloning integration
     */
    async _initializeVoiceCloning() {
        try {
            this.logger.info('Initializing voice cloning integration...');

            // Ensure transcription is enabled for voice cloning workflows
            if (this.agentService?.model) {
                this.agentService.model.enableTranscription = true;
                this.logger.info('Transcription enabled for voice cloning functionality');
            }

            // Import simplified agent TTS service with integrated voice cloning
            const { AgentTTSService } = await import('@/agent/tools/tts.js');
            this.voiceCloningService = new AgentTTSService({
                model: this.agentService?.model,
                enableTranscription: true
            });

            this.logger.info('Voice cloning integration initialized successfully');
            return true;
        } catch (error) {
            this.logger.warn('Failed to initialize voice cloning:', error);
            return false;
        }
    }

    /**
     * Handle agent state updates
     */
    _handleAgentStateUpdate(agentState, sessionId) {
        try {
            this.logger.debug('Handling agent state update:', agentState, sessionId);

            if (agentState.mode) {
                // Update UI and animation states based on mode
                switch (agentState.mode) {
                    case 'idle':
                        this._updateStatus('ready');
                        this._setSpeakingState(false);
                        break;
                    case 'listening':
                        this._updateStatus('listening');
                        this._setSpeakingState(false);
                        break;
                    case 'processing':
                        if (agentState.streaming) {
                            this._updateStatus('processing_stream');
                        } else {
                            this._updateStatus('processing');
                        }
                        break;
                    case 'speaking':
                        this._setSpeakingState(true);
                        this._updateStatus('speaking');
                        break;
                    case 'error':
                        this._updateStatus('error');
                        this._setSpeakingState(false);
                        if (agentState.error) {
                            this.logger.error('Agent error:', agentState.error);
                        }
                        break;
                }
            }

            // Handle streaming states
            if (agentState.streaming !== undefined) {
                this.logger.debug('Streaming state changed:', agentState.streaming);
                if (agentState.streaming) {
                    this._updateStatus('processing_stream');
                }
            }
        } catch (error) {
            this.logger.error('Error handling agent state update:', error);
        }
    }

    /**
     * Handle agent mode changes
     */
    _handleAgentModeChange(mode, sessionId) {
        try {
            this.logger.debug('Handling agent mode change:', mode, sessionId);

            // Map agent modes to TalkingAvatar states
            switch (mode) {
                case 'idle':
                    this._updateStatus('ready');
                    break;
                case 'listening':
                    this._updateStatus('listening');
                    break;
                case 'processing':
                    this._updateStatus('processing');
                    break;
                case 'speaking':
                    this._setSpeakingState(true);
                    this._updateStatus('speaking');
                    break;
                case 'error':
                    this._updateStatus('error');
                    break;
            }
        } catch (error) {
            this.logger.error('Error handling agent mode change:', error);
        }
    }

    /**
     * Handle streaming callbacks
     */
    _handleStreamingStart() {
        this.logger.debug('LangGraph streaming started');
        this._setSpeakingState(false);
    }

    _handleProcessingStart(type) {
        this.logger.debug('LangGraph processing started:', type);
        this._updateStatus('processing');
    }

    _handleProcessingEnd(type) {
        this.logger.debug('LangGraph processing ended:', type);
        this._updateStatus('idle');
    }

    /**
     * Handle conversation updates
     */
    _handleConversationUpdate(update) {
        this.logger.debug('LangGraph conversation update:', update);

        // Handle real-time text updates
        if (update.type === 'assistantMessage') {
            // Update UI with streaming text
            if (this.onConversationUpdate) {
                this.onConversationUpdate(update);
            }

            // If processing is active, show indicator
            if (update.processingActive) {
                this._updateStatus('processing');
            }
        }
    }

    // Agent TTS processing is now handled by TalkingAvatarAdapter

    // Agent state updates are now handled by TalkingAvatarAdapter

    // Agent mode handling methods are now in TalkingAvatarAdapter

    // Agent response generation is now handled by TalkingAvatarAdapter

    // Agent stream processing methods are now in TalkingAvatarAdapter

    // Additional agent methods are now in TalkingAvatarAdapter

    /**
     * Get session ID for conversation context
     * @returns {string} Session ID based on avatar mesh filename or default
     */
    _getSessionId() {
        // Use avatar mesh filename as session ID if available
        if (this.avatarMesh && this.avatarMesh.userData && this.avatarMesh.userData.fileName) {
            return this.avatarMesh.userData.fileName;
        }

        // Fallback to agentId if available
        if (this.agentId) {
            return this.agentId;
        }

        // Final fallback to default session
        return 'default-session';
    }



    /**
     * Handle state changes from the state manager
     * @param {string} newState - New avatar state
     * @param {string} oldState - Previous avatar state  
     * @param {Object} metadata - State change metadata
     * @private
     */
    // _handleStateChange removed - state management is now handled by the TalkingAvatarAdapter

    /**
     * Initialize realtime mode for voice streaming with built-in VAD
     * @returns {Promise<boolean>} Success status
     * @private
     */
    async _initializeRealtimeMode() {
        try {
            this.logger.info('🎙️ Preparing Aliyun Qwen-Omni realtime mode...');

            if (!this.agentService?.model) {
                throw new Error('Agent service model not available');
            }

            // Set up realtime callbacks
            const realtimeCallbacks = {
                onVoiceActivityDetected: () => {
                    this.logger.debug('🎤 Server VAD: Speech started (input_audio_buffer.speech_started)');
                    
                    // Implement interrupt-based VAD following vad_mode.py design
                    this.handleInterrupt();

                    // Trigger barge-in if currently speaking
                    if (this.isSpeaking || this.waitingForResponse) {
                        this.logger.debug('🛑 Server VAD triggering barge-in');
                        this._handleBargeIn({ type: 'voice_activity' });
                    }

                    // Update animation to show active listening
                    if (this.animator) {
                        this.animator.setState('speaking');
                    }
                },
                onVoiceActivityStopped: () => {
                    this.logger.debug('🎤 Server VAD: Speech ended (input_audio_buffer.speech_stopped)');

                    // Reset speech detection state
                    this.isSpeechDetected = false;

                    // Set VAD state if available
                    if (this.STATES) {
                        this.vadState = this.STATES.LISTENING;
                    }

                    // Update animation to show normal listening
                    if (this.animator) {
                        this.animator.setState('listening');
                    }

                    // No need to manually commit audio or create response
                    // The server will do this automatically with server VAD
                },
                onTranscriptReceived: (transcript) => {
                    this.logger.debug('📝 Transcript received:', transcript);

                    // Update UI with real-time transcription
                    if (this.onTranscriptReceived) {
                        this.onTranscriptReceived(transcript);
                    }
                },
                onAudioReceived: (audioData) => {
                    this.logger.debug('🔊 Received audio data from realtime API');

                    // Update speaking state when audio starts
                    if (!this.isSpeaking) {
                        this._setSpeakingState(true);
                        this._updateStatus('Speaking (realtime audio)...');
                    }

                    // Process audio data
                    if (audioData) {
                        // Use the processLLMResponseAudio function to handle the audio
                        import('@/agent/tools/tts.js').then(({ processLLMResponseAudio }) => {
                            processLLMResponseAudio({ audio: audioData }).catch(error => {
                                this.logger.error('Error processing realtime audio:', error);
                            });
                        }).catch(error => {
                            this.logger.error('Error importing audio processor:', error);
                        });
                    }
                },
                onError: (error) => {
                    this.logger.error('❌ Realtime API error:', error);
                    this._updateStatus(`Realtime error: ${error.message || 'Unknown error'}`);
                }
            };

            // Initialize realtime mode with centralized audio configuration
            const success = await this.agentService.model.initializeRealtimeMode({
                ...realtimeCallbacks,
                audioConfig: {
                    sampleRate: DEFAULT_AUDIO_CONFIG.sampleRate,
                    numChannels: DEFAULT_AUDIO_CONFIG.numChannels,
                    bitDepth: DEFAULT_AUDIO_CONFIG.bitDepth,
                    minIntervalMs: DEFAULT_AUDIO_CONFIG.minIntervalMs
                }
            });

            if (success) {
                this.logger.info('✅ Realtime mode initialized successfully');
                return true;
            } else {
                this.logger.error('❌ Failed to initialize realtime mode');
                return false;
            }

        } catch (error) {
            this.logger.error('Error initializing realtime mode:', error);
            return false;
        }
    }

    /**
     * Start realtime listening with MediaCaptureManager
     * @param {Object} options - Configuration options
     * @returns {Promise<boolean>} - Success status
     * @private
     */
    async _startRealtimeListening(options = {}) {
        try {
            this.logger.info('🎙️ Starting realtime voice streaming...');

            // Initialize media capture for audio with centralized configuration
            this.mediaCaptureManager = new MediaCaptureManager({
                audio: {
                    echoCancellation: true,
                    noiseSuppression: true,
                    autoGainControl: true,
                    sampleRate: DEFAULT_AUDIO_CONFIG.sampleRate
                },
                video: false,
                vadMode: 'server',
                onAudioData: async (audioData) => {
                    // Send audio data to realtime model
                    await this._sendRealtimeAudio(audioData);
                },
                onError: (error) => {
                    this.logger.error('❌ Media capture error:', error);
                    this._updateStatus(`Media error: ${error.message}`);
                    this.stopListening();
                }
            });

            // Start media capture
            const started = await this.mediaCaptureManager.startCapture('audio');
            if (!started) {
                throw new Error('Failed to start media capture');
            }

            this.logger.info('✅ Realtime voice streaming started successfully');
            this._updateStatus('Listening...');
            return true;
        } catch (error) {
            this.logger.error('❌ Failed to start realtime listening:', error);
            this._updateStatus(`Failed to start listening: ${error.message}`);
            return false;
        }
    }

    /**
     * Initialize HTTP mode with client-side VAD
     * @returns {Promise<boolean>} Success status
     * @private
     */
    async _initializeHttpVAD() {
        try {
            this.logger.info('🎤 Initializing HTTP mode with client-side VAD...');

            if (!this.mediaCaptureManager) {
                const { MediaCaptureManager } = await import('@/media/capture/MediaCaptureManager');

                let audioBuffer = [];
                this.mediaCaptureManager = new MediaCaptureManager({
                    audio: {
                        echoCancellation: true,
                        noiseSuppression: true,
                        autoGainControl: true,
                    },
                    vadMode: 'client',
                    onSpeechStart: async () => {
                        this.logger.info('[HTTP VAD] Speech started');
                        if (this.stateManager) this.stateManager.setState('listening');
                        audioBuffer = [];
                    },
                    onSpeechEnd: async (audioData, userPrompt) => {
                        this.logger.info('[HTTP VAD] Speech ended');

                        try {
                            // Only send if audioBuffer is present and has data
                            if (audioData) {
                                // Process audio through LLM API
                                const { llmAPI } = await import('@/media/api/llmAPI.ts');

                                // Prepare audio for API
                                const { convertFloat32ToWav, upsampleTo24kHz } = await import('@/media/modality/audio.ts');
                                const upsampledBuffer = upsampleTo24kHz(audioData, 16000); // VAD typically outputs 16kHz
                                const wavBlob = convertFloat32ToWav(upsampledBuffer, {
                                    sampleRate: ALIYUN_AUDIO_CONFIG.sampleRate,
                                    numChannels: ALIYUN_AUDIO_CONFIG.numChannels,
                                    bitDepth: ALIYUN_AUDIO_CONFIG.bitDepth
                                });

                                // Convert to base64
                                const arrayBuffer = await wavBlob.arrayBuffer();
                                const base64Audio = window.btoa(String.fromCharCode(...new Uint8Array(arrayBuffer)));

                                // Determine voice based on avatar gender
                                let gender = 'male';
                                if (this.avatarMesh?.userData?.gender) {
                                    gender = this.avatarMesh.userData.gender;
                                }
                                let voice = gender === 'female' ? 'Serena' : 'Ethan';

                                // Build message for API
                                const message = {
                                    role: 'user',
                                    content: userPrompt || '',
                                    input_audio: {
                                        data: base64Audio,
                                        format: 'wav'
                                    }
                                };

                                // Invoke LLM API
                                const result = await llmAPI.invoke([message], {
                                    provider: 'aliyun',
                                    model: 'qwen-omni-turbo',
                                    modalities: ['text', 'audio'],
                                    audioConfig: { voice, format: 'wav' },
                                    stream: true
                                });

                                // Process audio response
                                if (result) {
                                    const { processLLMResponseAudio } = await import('@/agent/tools/tts.js');
                                    await processLLMResponseAudio(result);
                                }

                                this.logger.info('[HTTP VAD] Audio processed successfully');
                                if (this.stateManager) this.stateManager.setState('speaking');
                            }
                        } catch (error) {
                            this.logger.error('[HTTP VAD] Error processing audio:', error);
                            if (this.stateManager) this.stateManager.setState('error');
                        }
                    },
                    onCaptureStart: () => {
                        this.logger.debug('🎤 Audio capture started');
                        if (this.stateManager) this.stateManager.setState('listening');
                        this.isListening = true;
                        this._updateListeningUI(true);
                        this._updateStatus('Listening (HTTP VAD)...');
                    },
                    onCaptureError: (error) => {
                        this.logger.error('❌ Audio capture error:', error);
                        if (this.stateManager) this.stateManager.setState('error');
                        this._updateStatus('Audio capture failed');
                    }
                });
            }

            // Initialize and start capture
            const initialized = await this.mediaCaptureManager.initialize('audio');
            if (!initialized) {
                this.logger.error('[HTTP VAD] Failed to initialize audio capture');
                return false;
            }

            const started = await this.mediaCaptureManager.startCapture('audio');
            if (!started) {
                this.logger.error('[HTTP VAD] Failed to start audio capture');
                return false;
            }

            this.logger.info('[HTTP VAD] Audio capture and VAD activated');
            return true;
        } catch (error) {
            this.logger.error('Error initializing HTTP VAD:', error);
            return false;
        }
    }

    /**
     * Send audio data to the realtime model
     * @param {ArrayBuffer|Float32Array} audioData - Audio data to send
     * @returns {Promise<boolean>} - Success status
     * @private
     */
    async _sendRealtimeAudio(audioData) {
        try {
            if (!this.agentService?.model) {
                if (!this._hasLoggedNoModel) {
                    this.logger.warn('Cannot send realtime audio: No model available');
                    this._hasLoggedNoModel = true;
                }
                return false;
            }

            if (!this.agentService.model.isRealtimeModeActive()) {
                if (!this._hasLoggedNotActive) {
                    this.logger.warn('Cannot send realtime audio: Realtime mode not active');
                    this._hasLoggedNotActive = true;
                }
                return false;
            }

            // Apply rate limiting based on centralized configuration
            const now = Date.now();
            if (this._lastAudioSendTime && (now - this._lastAudioSendTime) < DEFAULT_AUDIO_CONFIG.chunkDurationMs) {
                return false; // Skip this chunk to maintain rate limit
            }

            // Send audio data to model
            const success = await this.agentService.model.sendRealtimeAudio(audioData);
            if (success) {
                this._lastAudioSendTime = now;
            }
            return success;
        } catch (error) {
            this.logger.error('Error sending realtime audio:', error);
            return false;
        }
    }

    /**
     * Commit realtime audio buffer to trigger processing
     * @returns {Promise<boolean>} Success status
     * @private
     */
    async _commitRealtimeAudio() {
        try {
            if (this.agentService?.model?.commitRealtimeAudio) {
                const success = await this.agentService.model.commitRealtimeAudio();
                this.logger.info(`Audio buffer committed, result: ${success}`);
                return success;
            } else if (this.agentService?.model?.isRealtimeModeActive()) {
                // Send the commit event directly via WebSocket
                const socket = this.agentService.model.realtimeSocket;
                if (socket && socket.readyState === WebSocket.OPEN) {
                    const commitEvent = {
                        event_id: `event_${Date.now()}`,
                        type: 'input_audio_buffer.commit'
                    };
                    socket.send(JSON.stringify(commitEvent));
                    this.logger.info('✅ Successfully sent input_audio_buffer.commit event');

                    // After committing audio, create a response to get model output
                    setTimeout(() => {
                        const responseEvent = {
                            event_id: `event_${Date.now()}`,
                            type: 'response.create'
                        };
                        socket.send(JSON.stringify(responseEvent));
                        this.logger.info('✅ Successfully sent response.create event');
                    }, 300);

                    return true;
                } else {
                    throw new Error('WebSocket not connected or not available');
                }
            } else {
                this.logger.warn('commitRealtimeAudio not supported in current mode');
                return false;
            }
        } catch (err) {
            this.logger.error('Audio commit failed:', err);
            return false;
        }
    }

    /**
     * Handle interrupt event - immediately stop audio playback following vad_mode.py design
     */
    handleInterrupt() {
        this.logger.debug('VAD interrupt detected - stopping audio playback');
        
        // Set interrupt flag (following vad_mode.py pattern)
        this.interruptFlag = true;
        
        // Clear audio queue immediately (following clear_audio_queue() pattern)
        this.clearAudioQueue();
        
        // Stop current playback
        this.isPlaying = false;
        
        this.logger.debug('Audio playback interrupted');
    }

    /**
     * Clear audio queue following vad_mode.py clear_audio_queue() pattern
     */
    clearAudioQueue() {
        this.logger.debug('Clearing audio queue');
        
        // Clear the queue
        this.audioQueue = [];
        
        this.logger.debug('Audio queue cleared');
    }

    /**
     * Check interrupt flag before audio processing (following vad_mode.py pattern)
     */
    checkInterruptFlag() {
        if (this.interruptFlag) {
            this.logger.debug('Audio processing interrupted by VAD');
            this.interruptFlag = false; // Clear interrupt flag
            return true;
        }
        return false;
    }

    /**
     * Handle barge-in interruption
     * @param {Object} input - Input that triggered the barge-in
     * @private
     */
    async _handleBargeIn(input) {
        const now = Date.now();
        if (!this._lastBargeInTime || (now - this._lastBargeInTime) > 500) {
            this._lastBargeInTime = now;

            this.logger.debug('🛑 Executing barge-in sequence');

            // Stop all ongoing processing
            this.stopSpeaking();
            this.waitingForResponse = false;

            // Clear audio buffer if in realtime mode
            if (this.agentService?.model?.clearRealtimeAudioBuffer) {
                await this.agentService.model.clearRealtimeAudioBuffer();
                this.logger.debug('🧹 Cleared realtime audio buffer for barge-in');
            }

            // Stop TalkingHead
            if (this.talkingHead && typeof this.talkingHead.stopSpeaking === 'function') {
                this.talkingHead.stopSpeaking();
            }

            const inputType = input.type || 'user input';
            this._updateStatus(`Interrupted by ${inputType}...`);
        }
    }
}
