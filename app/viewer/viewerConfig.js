// Import shared model configurations
import { getEnvVar } from '@/config/env';
import { MODEL_CONFIGS } from '@/config/models.js';
import { DEFAULT_ENVIRONMENT_CONFIG } from '@/scene/environment';

// Environment configuration - override the default configuration
export const ENVIRONMENT_CONFIG = {
    ...DEFAULT_ENVIRONMENT_CONFIG,
    type: 'thinBox',
    thinBox: {
        ...DEFAULT_ENVIRONMENT_CONFIG.thinBox,
        size: 30,
        color: 0xffffff,
        setupIBL: true,
        position: { x: 0, y: 0, z: 0 },
        metalness: 0.2,
        roughness: 0.8,
        receiveShadows: true,
        scaleFactor: 1.0,
        minSize: 10,
        maxSize: 100,
        ibl: {
            sigmaRadians: 0.05,
            maxSamples: 30
        },
    },
    backgroundColor: 0x333333
};

// Scene configuration
export const SCENE_CONFIG = {
    initialCameraPosition: { x: 0, y: 0, z: 5 },
    rendererOptions: {
        antialias: true,
        alpha: true,
        preserveDrawingBuffer: true
    },
    lights: {
        ambient: {
            color: 0xffffff,
            intensity: 0.8
        },
        directional: {
            color: 0xffffff,
            intensity: 1.0,
            position: { x: 5, y: 5, z: 5 }
        }
    },
    environment: ENVIRONMENT_CONFIG,
    debug: false // This explicitly overrides VITE_DEBUG_MODE for module-specific debugging
};

// Controls configuration
export const CONTROLS_CONFIG = {
    enableDamping: true,
    dampingFactor: 0.05,
    enableZoom: true,
    enablePan: true,
    enableRotate: true,
    autoRotate: false,
    target: { x: 0, y: 0, z: 0 }
};

// Animation settings
export const ANIMATION_CONFIG = {
    MODEL: {
        EFFECTS: {
            FLOATING: {
                type: 'FLOATING',
                params: {
                    rotation: {
                        enabled: { x: false, y: true, z: false },
                        speed: { y: 0.5 },
                        amplitude: { y: Math.PI / 8 }
                    },
                    position: {
                        enabled: { x: false, y: true, z: false },
                        speed: { y: 0.5 },
                        amplitude: { y: 0.1 }
                    }
                }
            }
        },
        INITIAL: {
            position: { x: 0, y: 0, z: 0 },
            scale: { x: 1, y: 1, z: 1 }
        }
    }
};

// Asset paths
export const ASSETS = {

    // Model configurations (imported from shared config)
    MODEL_CONFIG: MODEL_CONFIGS,
    // Ready Player Me configuration
    RPM_CONFIG: {
        morphTargets: "ARKit,Oculus+Visemes,mouthOpen,mouthSmile,eyesClosed,eyesLookUp,eyesLookDown",
        textureSizeLimit: 1024,
        textureFormat: "png",
        defaultPose: "T",  // Default pose for avatar ('T' or 'A')
        poseOptions: {
            T: {
                description: "T-Pose - arms extended horizontally",
                useFor: "Animation rigging, default pose"
            },
            A: {
                description: "A-Pose - arms angled downward",
                useFor: "More natural standing pose"
            }
        }
    },
    SAVE_OPTIONS: {
        basePath: '/assets',
        meshPath: '/assets/meshes',
        imagePath: '/assets/images',
        videoPath: '/assets/videos',
        seedPath: '/assets/seeds',
        audioPath: '/assets/audios',
        formats: {
            mesh: ['.glb', '.fbx'],
            image: '.png',
            video: '.mp4'
        },
    },
    RELOAD_OPTIONS: {
        reloadEnvironment: false,
        reloadMeshes: true
    },
    // Voice configuration
    VOICE_CONFIG: {
        language: 'auto',  // Default language auto/english/chinese - use auto to enable ASR detection
        gender: 'male',
        // SherpaOnnx configuration (imported from shared config)
        sherpaOnnx: MODEL_CONFIGS.sherpaOnnx
    }
};

// UI Configuration
export const UI_CONFIG = {
    // Component configuration
    components: ['MeshSelector', 'CameraViewer'],  // Added CameraViewer to enabled components
    props: {
        MeshSelector: {
            meshPath: ASSETS.SAVE_OPTIONS.meshPath,
            meshFormat: ASSETS.SAVE_OPTIONS.formats.mesh,
            enableDelete: true  // Enable delete functionality
        },
        CameraViewer: {
            buttonPosition: {
                top: '20px',
                right: 'calc(440px + 60px)'  // Position next to convert button
            },
            buttonStyle: {
                background: '#2196F3',
                color: 'white',
                minWidth: '120px',
                height: '40px'
            },
            modalConfig: {
                maxWidth: '80vw',
                aspectRatio: '16/9'
            }
        }
    },
    // Progress UI configuration
    progress: {
        containerId: 'progress-container',
        template: `
            <div class="progress-bar-wrapper">
                <div class="progress-bar">
                    <div class="progress-fill"></div>
                </div>
                <div class="progress-text">Generating: 0%</div>
                <div class="status-text">Starting generation...</div>
                <button class="cancel-button">Cancel</button>
            </div>
        `
    }
};