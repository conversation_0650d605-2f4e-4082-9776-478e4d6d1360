export function setupKeyboardShortcuts() {
    document.addEventListener('keydown', (event) => {
        switch(event.key.toLowerCase()) {
            case 'd':
                toggleDebug();
                break;
            case 'f':
                toggleFullscreen();
                break;
            case 'escape':
                if (document.fullscreenElement) {
                    document.exitFullscreen();
                }
                break;
        }
    });
}

export function toggleDebug() {
    const debug = document.getElementById('debug-overlay');
    debug.style.display = debug.style.display === 'none' ? 'block' : 'none';
}

export function toggleFullscreen() {
    if (!document.fullscreenElement) {
        document.documentElement.requestFullscreen();
    } else {
        document.exitFullscreen();
    }
}

export function showError(message) {
    console.error(message);
    const error = document.createElement('div');
    error.style.cssText = 'position:fixed;top:10px;left:10px;color:red;';
    error.textContent = message;
    document.body.appendChild(error);
    setTimeout(() => error.remove(), 5000);
} 