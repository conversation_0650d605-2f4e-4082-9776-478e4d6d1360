<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>Looking Glass Mobile Controller</title>
    <style>
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        html,
        body {
            width: 100%;
            height: 100%;
            overflow: hidden;
            background-color: #000;
            color: #fff;
            font-family: Arial, sans-serif;
        }

        #app {
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }

        .loading {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            height: 100%;
            width: 100%;
            text-align: center;
            padding: 20px;
        }

        .loading h1 {
            margin-bottom: 20px;
        }

        .loading p {
            margin-bottom: 10px;
        }

        .spinner {
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top: 4px solid #fff;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin-bottom: 20px;
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }

        .error {
            color: #ff6b6b;
            margin-top: 20px;
            text-align: center;
            padding: 10px;
        }

        .button {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            text-align: center;
            text-decoration: none;
            display: inline-block;
            font-size: 16px;
            margin: 10px 2px;
            cursor: pointer;
            border-radius: 4px;
        }

        .button:disabled {
            background-color: #cccccc;
            color: #666666;
            cursor: not-allowed;
        }
    </style>
</head>

<body>
    <div id="app">
        <div class="loading">
            <div class="spinner"></div>
            <h1>Looking Glass Controller</h1>
            <p>Connecting to Looking Glass Display...</p>
            <p id="status">Initializing...</p>
            <div id="error" class="error" style="display: none;"></div>
            <button id="retry" class="button" style="display: none;">Retry Connection</button>
        </div>
    </div>

    <!-- Include Three.js library directly -->
    <script src="https://cdn.jsdelivr.net/npm/three@0.160.0/build/three.min.js"></script>

    <!-- Include a simple fallback for OrbitControls and GLTFLoader -->
    <script>
        // Simple OrbitControls implementation
        window.addEventListener('DOMContentLoaded', () => {
            if (!window.THREE) {
                console.error('Three.js not loaded');
                return;
            }

            // Create a simple OrbitControls if it doesn't exist
            if (!window.THREE.OrbitControls) {
                THREE.OrbitControls = function (camera, domElement) {
                    this.object = camera;
                    this.domElement = domElement;
                    this.enabled = true;
                    this.target = new THREE.Vector3();
                    this.enableDamping = false;
                    this.dampingFactor = 0.05;

                    this.update = function () { };
                    this.dispose = function () { };
                    this.rotateLeft = function () { };
                    this.rotateUp = function () { };
                    this.dollyIn = function () { };
                    this.dollyOut = function () { };

                    console.log('Using fallback OrbitControls');
                };
            }

            // Create a simple GLTFLoader if it doesn't exist
            if (!window.THREE.GLTFLoader) {
                THREE.GLTFLoader = function () {
                    this.load = function (url, onLoad, onProgress, onError) {
                        // Create a simple cube as a fallback
                        const geometry = new THREE.BoxGeometry(1, 1, 1);
                        const material = new THREE.MeshBasicMaterial({ color: 0x00ff00 });
                        const cube = new THREE.Mesh(geometry, material);

                        // Create a scene with the cube
                        const scene = new THREE.Scene();
                        scene.add(cube);

                        // Call the onLoad callback with the scene
                        if (onLoad) {
                            onLoad({ scene: scene });
                        }

                        console.log('Using fallback GLTFLoader');
                    };
                };
            }

            // Signal that Three.js is loaded
            window.threeJsLoaded = true;
            document.dispatchEvent(new Event('threeJsLoaded'));
        });
    </script>

    <script type="module">
        import { MobileController } from '../../src/ui/components/MobileController.js';
        import mobileDebugger from '../../src/utils/mobileDebugger.js';

        // Get the session ID from the URL
        const urlParams = new URLSearchParams(window.location.search);
        let sessionId = urlParams.get('session');

        // If no session ID is provided, generate a default one for testing
        if (!sessionId) {
            console.warn('[Mobile] No session ID provided in URL, using default session ID for testing');
            sessionId = 'default-session';
        }

        console.log('[Mobile] Using session ID:', sessionId);

        const statusElement = document.getElementById('status');
        const errorElement = document.getElementById('error');
        const retryButton = document.getElementById('retry');

        // Initialize the controller
        async function initController() {
            try {
                // Initialize the mobile debugger
                mobileDebugger.initialize();
                mobileDebugger.log('Mobile debugger initialized');
                mobileDebugger.log('Session ID:', sessionId);

                // We now handle the case of no session ID above by using a default one
                statusElement.textContent = `Connecting to Looking Glass Display (Session: ${sessionId})...`;

                // statusElement.textContent = 'Connecting to Looking Glass Display...';

                // Wait for Three.js and its extensions to be fully loaded
                await waitForThreeJs();

                // Create and initialize the controller
                const controller = new MobileController({
                    container: document.getElementById('app'),
                    useOrientation: true
                });

                const success = await controller.initialize(sessionId);

                if (success) {
                    statusElement.textContent = 'Connected successfully!';
                    // The loading screen will be replaced by the controller UI
                } else {
                    throw new Error('Failed to connect to Looking Glass Display.');
                }
            } catch (error) {
                console.error('Error initializing controller:', error);
                mobileDebugger.error('Error initializing controller:', error.message);
                mobileDebugger.error('Error stack:', error.stack);

                // Show error in UI
                statusElement.textContent = 'Connection failed.';
                errorElement.textContent = error.message || 'Unknown error occurred.';
                errorElement.style.display = 'block';
                retryButton.style.display = 'block';

                // Show the debugger automatically on error
                mobileDebugger.show();
            }
        }

        // Function to wait for Three.js and its extensions to be fully loaded
        function waitForThreeJs() {
            return new Promise((resolve, reject) => {
                // If Three.js is already loaded, resolve immediately
                if (window.threeJsLoaded) {
                    console.log('[Mobile] Three.js already loaded');
                    resolve();
                    return;
                }

                // Listen for the threeJsLoaded event
                document.addEventListener('threeJsLoaded', () => {
                    console.log('[Mobile] Three.js loaded event received');
                    resolve();
                }, { once: true });

                // Listen for the threeJsLoadError event
                document.addEventListener('threeJsLoadError', () => {
                    console.error('[Mobile] Three.js failed to load');
                    // Resolve anyway and let the error handling take care of it
                    resolve();
                }, { once: true });

                // Set a timeout in case the events never fire
                setTimeout(() => {
                    console.warn('[Mobile] Timed out waiting for Three.js to load');
                    resolve();
                }, 10000); // 10 seconds timeout
            });
        }

        // Add retry button event listener
        retryButton.addEventListener('click', () => {
            errorElement.style.display = 'none';
            retryButton.style.display = 'none';
            initController();
        });

        // Initialize the controller when the page loads
        window.addEventListener('DOMContentLoaded', initController);
    </script>
</body>

</html>