import * as THREE from 'three';
import { CONFIG } from './config.js';
import { LookingGlassWebXRPolyfill, LookingGlassConfig } from "@/modules/lookingglass/webxr";
import { AssetLoader } from "@/utils/assetLoader.js";
import { getEnvVar as getEnv, getEnvVarAsBoolean, getAssetPath } from "@/config/env";
import { VRButton } from "three/examples/jsm/webxr/VRButton.js";
import { SceneManager } from "@/scene";
import { DEFAULT_ENVIRONMENT_CONFIG } from "@/scene/environment";

// Export THREE for use in other modules
export { THREE };

class Base {
    constructor(params = {}) {
        // Create environment configuration object
        this.envConfig = {
            config: {
                app: {
                    env: getEnv('VITE_APP_ENV', 'development'),
                    title: getEnv('VITE_APP_TITLE', ''),
                    debug: getEnvVarAsBoolean('VITE_DEBUG_MODE', false),
                    enableStats: getEnvVarAsBoolean('VITE_ENABLE_STATS', false)
                },
                lookingGlass: {
                    mock: getEnvVarAsBoolean('VITE_LOOKING_GLASS_MOCK', false),
                },
                assets: {
                    basePath: getEnv('VITE_ASSETS_PATH', '/assets'),
                    mediapipeModelPath: getEnv('VITE_MEDIAPIPE_MODEL_PATH', ''),
                    videoFallbackPath: getEnv('VITE_VIDEO_FILE_PATH', '')
                }
            },
            getAssetPath
        };

        // Initialize Looking Glass config first
        this.glassConfig = LookingGlassConfig;

        // Merge environment settings with passed parameters
        this.params = {
            initialCameraPosition: params.initialCameraPosition || { x: 0, y: 0, z: 3 },
            lookingGlassConfig: {
                ...CONFIG.GLASS_CONFIG,
                ...params.lookingGlassConfig
            },
            rendererOptions: {
                antialias: true,
                ...params.rendererOptions
            },
            lights: {
                ambient: {
                    color: 0xffffff,
                    intensity: 2,
                    ...params.lights?.ambient
                },
                directional: {
                    color: 0x8888aa,
                    intensity: 30,
                    position: { x: 3, y: 3, z: 3 },
                    phi: 1,
                    theta: 2,
                    ...params.lights?.directional
                },
                lightspot: {
                    color: 0x3388ff,
                    intensity: 0,
                    dispersion: 1,
                    phi: 0.1,
                    theta: 4,
                    ...params.lights?.lightspot
                }
            },
            environment: {
                ...DEFAULT_ENVIRONMENT_CONFIG, // Use centralized environment config
                ...params.environment // Allow overrides from params
            },
            debug: params.debug !== undefined ? params.debug : this.envConfig.config.app.debug || false
        };

        // Initialize asset loader
        this.assetLoader = new AssetLoader();

        // Initialize model cache
        this.modelCache = new Map();

        // Initialize scene manager (will be set up in init_base)
        this.sceneManager = null;

        // Create container reference
        this.container = params.container || document.body;
    }

    // Initiating async initialization without awaiting
    async init_base() {
        await this.setupLookingGlass(); // this should be configured before

        // Create SceneManager configuration
        const sceneManagerConfig = {
            environment: this.params.environment,
            lighting: {
                ambient: {
                    color: this.params.lights?.ambient?.color || 0xffffff,
                    intensity: this.params.lights?.ambient?.intensity || 0.3
                },
                directional: {
                    color: this.params.lights?.directional?.color || 0xffffff,
                    intensity: this.params.lights?.directional?.intensity || 1.0,
                    position: this.params.lights?.directional?.position || { x: 5, y: 5, z: 5 },
                    castShadow: true,
                    shadowMapSize: 1024
                },
                fill: {
                    color: 0xffffff,
                    intensity: 0.5,
                    position: { x: -5, y: 3, z: -5 }
                }
            },
            camera: {
                type: 'perspective',
                fov: 25,
                near: 0.1,
                far: 1000,
                position: this.params.initialCameraPosition || { x: 0, y: 0, z: 3 },
                lookAt: { x: 0, y: 0, z: 0 },
                enableControls: true
            },
            renderer: {
                antialias: this.params.rendererOptions?.antialias !== undefined ? this.params.rendererOptions.antialias : true,
                alpha: this.params.rendererOptions?.alpha !== undefined ? this.params.rendererOptions.alpha : true,
                preserveDrawingBuffer: this.params.rendererOptions?.preserveDrawingBuffer !== undefined ? this.params.rendererOptions.preserveDrawingBuffer : true,
                toneMapping: THREE.ACESFilmicToneMapping,
                outputColorSpace: THREE.SRGBColorSpace,
                shadowMap: {
                    enabled: true,
                    type: THREE.PCFSoftShadowMap,
                    autoUpdate: true
                },
                xr: {
                    enabled: true,
                    addVRButton: false
                }
            },
            // Allow debug flag to be overridden by params (which includes viewerConfig settings)
            debug: this.params.debug !== undefined ? this.params.debug : this.envConfig.config.app.debug || false,
        };

        // Initialize SceneManager
        this.sceneManager = new SceneManager(this.container, sceneManagerConfig);

        // Store references to scene components for backward compatibility
        this.scene = this.sceneManager.scene;
        this.camera = this.sceneManager.camera.camera;
        this.renderer = this.sceneManager.renderer.renderer;
        this.sceneEnvironment = this.sceneManager.environment;
        this.sceneLighting = this.sceneManager.lighting;
        this.sceneCamera = this.sceneManager.camera;
        this.sceneRenderer = this.sceneManager.renderer;

        // Setup VR Button
        this.vrButton = this.setupVRButton(this.renderer);

        // Start render loop
        this.sceneManager.start();

        // Setup event listeners
        this.setupEventListeners();

        if (this.envConfig.config.app.debug) {
            console.group('✅ Base Initialization Complete');
            console.log('🎬 Scene Manager Status:', {
                sceneRendered: !!this.scene,
                cameraPosition: this.camera?.position.toArray(),
                rendererSize: this.renderer ? [this.renderer.domElement.width, this.renderer.domElement.height] : 'unknown',
                vrEnabled: this.renderer?.xr?.enabled || false,
                environmentLoaded: !!this.sceneEnvironment
            });
            console.groupEnd();
        }
    }

    initLookingGlass() {
        console.log('Initializing Looking Glass...');

        // Create merged configuration with params taking priority
        const mergedConfig = {
            ...CONFIG.GLASS_CONFIG,  // Base configuration
            ...this.params.lookingGlassConfig  // Override with params (takes priority)
        };

        // Initialize Looking Glass WebXR polyfill with merged config
        this.lookingGlassPolyfill = new LookingGlassWebXRPolyfill(mergedConfig);

        // Log Looking Glass configuration for debugging
        console.log('Looking Glass Configuration:', {
            merged: LookingGlassConfig
        });
    }

    /**
     * Create VR Button for the given renderer
     * @param {THREE.WebGLRenderer} renderer - The WebGL renderer to use with the VR button
     * @returns {HTMLElement|null} - The created VR button or null if creation failed
     */
    setupVRButton(renderer) {
        if (!renderer) {
            console.warn('[Base] Cannot setup VR Button - renderer is not available');
            return null;
        }

        try {
            // Create VR button but don't add to DOM (will be added by UI)
            const vrButton = VRButton.createButton(renderer);

            // Store reference and mark for UI
            vrButton.id = 'VRButton';
            vrButton.className = 'VRButton';
            vrButton._isVRButton = true;

            console.log('[Base] VR button created successfully');
            return vrButton;
        } catch (error) {
            console.warn('[Base] Failed to create VR button:', error);
            return null;
        }
    }

    startRenderLoop() {
        // Use SceneManager to start render loop if available
        if (this.sceneManager) {
            this.sceneManager.start();
        } else if (this.sceneRenderer) {
            // Fallback to old method for backward compatibility
            this.sceneRenderer.startAnimationLoop(() => this.render());
        }
    }

    render() {
        // Use SceneManager to render if available
        if (this.sceneManager) {
            this.sceneManager.update();

            // Add debug logging for camera position and box environment
            if (this.sceneManager.environment && this.sceneManager.environment.boxEnvironmentMesh && this.sceneManager.camera) {
                const boxMesh = this.sceneManager.environment.boxEnvironmentMesh;
                const camera = this.sceneManager.camera.camera;

                // Log every 100 frames to avoid console spam
                if (Math.random() < 0.01) {
                    console.log("[Base] Debug - Camera and Environment:", {
                        cameraPosition: camera.position.toArray(),
                        boxPosition: boxMesh.position.toArray(),
                        boxVisible: boxMesh.visible,
                        boxDimensions: boxMesh instanceof THREE.Mesh && boxMesh.geometry instanceof THREE.BoxGeometry ? {
                            width: boxMesh.geometry.parameters.width,
                            height: boxMesh.geometry.parameters.height,
                            depth: boxMesh.geometry.parameters.depth
                        } : 'unknown',
                        isCameraInsideBox: boxMesh instanceof THREE.Mesh && boxMesh.geometry instanceof THREE.BoxGeometry ?
                            Math.abs(camera.position.x - boxMesh.position.x) < boxMesh.geometry.parameters.width / 2 &&
                            Math.abs(camera.position.y - boxMesh.position.y) < boxMesh.geometry.parameters.height / 2 &&
                            Math.abs(camera.position.z - boxMesh.position.z) < boxMesh.geometry.parameters.depth / 2
                            : 'unknown'
                    });
                }
            }
        } else {
            // Fallback to old method for backward compatibility

            // Update camera controls if available
            if (this.sceneCamera && this.sceneCamera.controls) {
                this.sceneCamera.update();
            }

            // Ensure the thin box environment is properly rendered
            if (this.sceneEnvironment && this.sceneEnvironment.boxEnvironmentMesh) {
                // Make sure the box environment is visible and receiving shadows
                this.sceneEnvironment.boxEnvironmentMesh.visible = true;
                this.sceneEnvironment.boxEnvironmentMesh.receiveShadow = true;

                // Check if the material side is set correctly
                const material = this.sceneEnvironment.boxEnvironmentMesh.material;
                if (material && material.side !== THREE.BackSide) {
                    console.warn("[Base] Box environment material side is not BackSide:", material.side);
                    // Try to fix it
                    material.side = THREE.BackSide;
                    material.needsUpdate = true;
                }

                // Add debug logging for camera position and box environment
                if (Math.random() < 0.01) {
                    console.log("[Base] Debug - Camera and Environment:", {
                        cameraPosition: this.camera.position.toArray(),
                        boxPosition: this.sceneEnvironment.boxEnvironmentMesh.position.toArray(),
                        boxVisible: this.sceneEnvironment.boxEnvironmentMesh.visible,
                        boxDimensions: this.sceneEnvironment.boxEnvironmentMesh.geometry instanceof THREE.BoxGeometry ? {
                            width: this.sceneEnvironment.boxEnvironmentMesh.geometry.parameters.width,
                            height: this.sceneEnvironment.boxEnvironmentMesh.geometry.parameters.height,
                            depth: this.sceneEnvironment.boxEnvironmentMesh.geometry.parameters.depth
                        } : 'unknown',
                        isCameraInsideBox: this.sceneEnvironment.boxEnvironmentMesh.geometry instanceof THREE.BoxGeometry ?
                            Math.abs(this.camera.position.x - this.sceneEnvironment.boxEnvironmentMesh.position.x) < this.sceneEnvironment.boxEnvironmentMesh.geometry.parameters.width / 2 &&
                            Math.abs(this.camera.position.y - this.sceneEnvironment.boxEnvironmentMesh.position.y) < this.sceneEnvironment.boxEnvironmentMesh.geometry.parameters.height / 2 &&
                            Math.abs(this.camera.position.z - this.sceneEnvironment.boxEnvironmentMesh.position.z) < this.sceneEnvironment.boxEnvironmentMesh.geometry.parameters.depth / 2
                            : 'unknown'
                    });
                }
            }

            // Render scene
            this.renderer.render(this.scene, this.camera);
        }
    }


    async setupLookingGlass() {
        // Initialize Looking Glass if available and not in mock mode
        if (!this.isMockMode) {
            try {
                this.initLookingGlass();
            } catch (error) {
                console.warn('Looking Glass initialization failed, falling back to regular WebGL:', error);
            }
        } else {
            console.log('Running in mock mode');
        }
    }

    setupEventListeners() {
        // Handle window resize
        this.handleResize = this.handleResize.bind(this);

        window.addEventListener('resize', () => this.handleResize());
        window.addEventListener('glass-config-updated', this.handleConfigUpdate.bind(this));
        window.addEventListener('beforeunload', () => this.dispose());

        this.handleResize(); // init to resize the canvas first
    }

    handleResize() {
        // Use SceneManager to handle resize if available
        if (this.sceneManager) {
            // SceneManager already handles resize internally via its setupResizeHandler method
            // Just trigger a render update
            this.render();
            return;
        }

        // Fallback to old method for backward compatibility
        if (!this.renderer) return;

        // Update renderer size
        if (this.sceneRenderer) {
            this.sceneRenderer.updateSize();
        } else {
            const width = window.innerWidth;
            const height = window.innerHeight;
            this.renderer.setSize(width, height);
        }

        // Update camera aspect ratio
        if (this.sceneCamera) {
            this.sceneCamera.updateAspect(window.innerWidth, window.innerHeight);
        } else if (this.camera) {
            this.camera.aspect = window.innerWidth / window.innerHeight;
            this.camera.updateProjectionMatrix();
        }

        // Trigger a render update
        this.render();
    }

    // Asset loading methods
    async loadAssets(assets, options) {
        const loadPromises = [];
        console.log('Received assets:', assets);  // Debug log

        // Merge options with defaults from CONFIG
        const loadOptions = {
            reloadMeshes: options?.reloadMeshes ?? CONFIG.RELOAD_OPTIONS.reloadMeshes,
            reloadEnvironment: options?.reloadEnvironment ?? CONFIG.RELOAD_OPTIONS.reloadEnvironment
        };

        // Load meshes
        if (assets.meshes) {
            // If reloadMeshes is true, clear existing meshes from scene
            if (loadOptions.reloadMeshes) {
                this.deleteMesh();
            }

            for (const [key, path] of Object.entries(assets.meshes)) {
                console.log('Loading mesh:', path);
                loadPromises.push(
                    this.assetLoader.loadMesh(path)
                        .then(async ({ mesh, animation }) => {
                            await this.setupObject(key, mesh);
                            if (animation) {
                                console.log('Animation loaded:', animation.name || 'unnamed');
                            } else {
                                console.log('No animation data found for mesh:', key);
                            }
                        })
                );
            }
        }

        this.render();

        await Promise.all(loadPromises);
    }

    /**
     * Sets up a loaded object with necessary configurations and features
     * @param {string} id - The identifier for the object
     * @param {THREE.Object3D} object - The object to setup
     */
    async setupObject(id, object) {
        // Store object reference
        this.objects = this.objects || new Map();
        this.objects.set(id, object);
        this.scene.add(object);

        // Log found meshes for debugging
        let meshCount = 0;
        object.traverse((child) => {
            if (child.type === 'Mesh' || child.type === 'SkinnedMesh') {
                meshCount++;
                // Apply standard mesh settings if needed
                child.castShadow = true;
                child.receiveShadow = true;
            }
        });

        console.group(`🎯 Object Setup: ${id}`);
        console.log('📊 Mesh Analysis:', {
            totalMeshes: meshCount,
            objectType: object.type,
            hasAnimations: !!object.animations?.length,
            animationCount: object.animations?.length || 0,
            boundingBox: object.geometry?.boundingBox || 'not calculated',
            scale: object.scale.toArray(),
            position: object.position.toArray()
        });

        if (object.animations?.length > 0) {
            console.groupCollapsed('🎬 Available Animations');
            object.animations.forEach((anim, index) => {
                console.log(`${index + 1}. ${anim.name} (${anim.duration.toFixed(2)}s, ${anim.tracks.length} tracks)`);
            });
            console.groupEnd();
        }
        console.groupEnd();

        // Apply standard mesh settings
        object.traverse((child) => {
            if (child.isMesh) {
                child.castShadow = true;
                child.receiveShadow = true;
            }
        });

        // Update environment for the newly loaded mesh
        if (this.sceneManager && this.sceneManager.environment) {
            // Use the new updateEnvironmentForMesh method to properly size and position
            // the environment box relative to the loaded mesh
            this.sceneManager.environment.updateEnvironmentForMesh(object);

            console.log(`[Base] Updated environment for object ${id}`);
        }
    }

    deleteMesh() {
        // Remove all meshes from the scene, including nested ones
        const meshesToRemove = [];
        this.scene.traverse((object) => {
            if (object.isMesh) {
                meshesToRemove.push(object);
                // Dispose of geometry and materials
                if (object.geometry) {
                    object.geometry.dispose();
                }
                if (object.material) {
                    if (Array.isArray(object.material)) {
                        object.material.forEach(material => material.dispose());
                    } else {
                        object.material.dispose();
                    }
                }
            }
        });
        // Remove meshes from their parent
        meshesToRemove.forEach(mesh => {
            mesh.parent?.remove(mesh);
        });
    }

    handleConfigUpdate(event) {
        const newConfig = event.detail;

        // Update camera position if target values changed
        if (newConfig.targetY !== undefined || newConfig.targetZ !== undefined) {
            this.camera.position.y = newConfig.targetY ?? this.camera.position.y;
            this.camera.position.z = newConfig.targetZ ?? this.camera.position.z;
        }

        // Update field of view if changed
        if (newConfig.fovy !== undefined) {
            this.camera.fov = THREE.MathUtils.radToDeg(newConfig.fovy);
            this.camera.updateProjectionMatrix();
        }

        // Trigger a render update
        this.render();
    }

    // Resource cleanup
    dispose() {
        // Remove event listeners
        window.removeEventListener('glass-config-updated', this.handleConfigUpdate.bind(this));
        window.removeEventListener('resize', this.handleResize.bind(this));
        window.removeEventListener('beforeunload', () => this.dispose());

        // Dispose SceneManager if available
        if (this.sceneManager) {
            this.sceneManager.dispose();
            this.sceneManager = null;
            console.log('[Base] Scene manager disposed');
        } else {
            // Fallback to old method for backward compatibility

            // Clean up scene environment
            if (this.sceneEnvironment) {
                this.sceneEnvironment.cleanupEnvironment();
                this.sceneEnvironment = null;
                console.log('[Base] Scene environment cleaned up');
            }

            // Dispose scene components
            if (this.sceneRenderer) {
                this.sceneRenderer.dispose();
            } else if (this.renderer) {
                this.renderer.dispose();
            }
        }

        // Dispose meshes
        this.meshes?.forEach(mesh => {
            if (mesh.geometry) mesh.geometry.dispose();
            if (mesh.material) {
                if (Array.isArray(mesh.material)) {
                    mesh.material.forEach(material => material.dispose());
                } else {
                    mesh.material.dispose();
                }
            }
        });
        this.meshes?.clear();
        this.textures?.forEach(texture => texture.dispose());
        this.textures?.clear();

        // Dispose objects collection if it exists
        if (this.objects) {
            this.objects.forEach(object => {
                object.traverse(child => {
                    if (child.isMesh) {
                        if (child.geometry) child.geometry.dispose();
                        if (child.material) {
                            if (Array.isArray(child.material)) {
                                child.material.forEach(material => material.dispose());
                            } else {
                                child.material.dispose();
                            }
                        }
                    }
                });
            });
            this.objects.clear();
        }

        // Dispose Draco loader if initialized
        if (this.dracoLoader) {
            this.dracoLoader.dispose();
        }

        // Dispose GLTF loader if initialized
        if (this.gltfLoader) {
            this.gltfLoader.dispose?.();
        }

        // Dispose RGBE loader if initialized
        if (this.rgbeLoader) {
            this.rgbeLoader.dispose?.();
        }

        // Clear references
        this.scene = null;
        this.camera = null;
        this.renderer = null;
        this.sceneEnvironment = null;
        this.sceneLighting = null;
        this.sceneCamera = null;
        this.sceneRenderer = null;
    }

    async loadModel(modelPath) {
        try {
            // Check cache first
            if (this.modelCache.has(modelPath)) {
                return this.modelCache.get(modelPath);
            }

            // Load the model
            const model = await this.loadModelFromPath(modelPath);
            this.modelCache.set(modelPath, model);
            return model;
        } catch (error) {
            console.error('Error loading model:', error);
            throw error;
        }
    }

    // Helper method to load model from path
    async loadModelFromPath(_path) {
        // Implementation depends on your 3D engine (Three.js, Babylon.js, etc.)
        throw new Error('loadModelFromPath must be implemented by subclass');
    }
}

export { Base };
