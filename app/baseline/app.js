import * as THREE from 'three';
import { VRButton } from "three/examples/jsm/webxr/VRButton.js";
import { RGBELoader } from "three/examples/jsm/loaders/RGBELoader";
import { GLTFLoader } from "three/examples/jsm/loaders/GLTFLoader";
import { DRACOLoader } from "three/examples/jsm/loaders/DRACOLoader";
import {
    LookingGlassWebXRPolyfill,
    LookingGlassConfig
} from "../../modules/lookingglass/webxr";
import { CONFIG } from '../config';

export class BaselineApp {
    constructor() {
        // Setup Looking Glass Config
        this.config = LookingGlassConfig;
        this.config.targetY = 0;
        this.config.targetZ = 0;
        this.config.targetDiam = 3;
        this.config.fovy = (14 * Math.PI) / 180;
        new LookingGlassWebXRPolyfill();

        // Setup scene
        this.scene = new THREE.Scene();

        // Setup lights
        this.setupLights();

        // Setup renderer
        this.renderer = new THREE.WebGLRenderer({ antialias: true });
        document.body.append(this.renderer.domElement);
        this.renderer.xr.enabled = true;
        this.renderer.toneMapping = THREE.ACESFilmicToneMapping;
        this.renderer.toneMappingExposure = 1;
        this.renderer.outputEncoding = THREE.sRGBEncoding;

        // Setup camera
        this.camera = new THREE.PerspectiveCamera();
        this.camera.position.z = 3;

        // Setup loaders
        this.setupLoaders();

        // Add VR button
        document.body.append(VRButton.createButton(this.renderer));

        // Handle window resize
        this.handleResize = this.handleResize.bind(this);
        window.addEventListener("resize", this.handleResize);
        this.handleResize();
    }

    setupLoaders() {
        // Initialize GLTF loader
        this.gltfLoader = new GLTFLoader();

        // Setup Draco decoder for GLTF compression if enabled
        if (CONFIG.DRACO?.COMPRESSION?.AUTO_CONVERT) {
            this.dracoLoader = new DRACOLoader();
            this.dracoLoader.setDecoderPath(CONFIG.DRACO.DRACO_URL);
            this.gltfLoader.setDRACOLoader(this.dracoLoader);
        }

        // Initialize RGBE loader
        this.rgbeLoader = new RGBELoader();
    }

    setupLights() {
        this.scene.add(new THREE.AmbientLight(0xaaaaaa));
        const directionalLight = new THREE.DirectionalLight(0xffffff);
        directionalLight.position.set(3, 3, 3);
        this.scene.add(directionalLight);
    }

    async loadAssets(hdrPath, modelPath) {
        try {
            // Load HDR environment
            const rgbeLoader = new RGBELoader();
            const texture = await rgbeLoader.loadAsync(hdrPath);
            texture.mapping = THREE.EquirectangularReflectionMapping;
            this.scene.background = texture;
            this.scene.environment = texture;

            // Clear previous model if it exists
            if (this.currentModel) {
                this.scene.remove(this.currentModel);
            }

            // If modelPath is a URL (starts with http), download and save it
            if (modelPath.startsWith('http')) {
                // Download the GLB file
                const response = await fetch(modelPath);
                const blob = await response.blob();

                // Generate filename from URL or timestamp
                const timestamp = new Date().getTime();
                const filename = `model_${timestamp}.glb`;

                // Save to local storage
                const formData = new FormData();
                formData.append('file', blob, filename);

                try {
                    // Save the file to public/assets/meshes
                    const saveResponse = await fetch('/api/save-mesh', {
                        method: 'POST',
                        body: formData
                    });
                    const result = await saveResponse.json();
                    // Update modelPath to use local path
                    modelPath = `/assets/meshes/${filename}`;
                } catch (error) {
                    console.warn('Failed to save model locally, using URL directly:', error);
                }
            }

            // Load model
            const loader = new GLTFLoader();
            const gltf = await loader.loadAsync(modelPath);
            this.currentModel = gltf.scene;
            this.scene.add(this.currentModel);

            this.render();

            return modelPath; // Return the local path or original URL
        } catch (error) {
            console.error('Error loading assets:', error);
            throw error;
        }
    }

    handleResize() {
        this.renderer.setSize(window.innerWidth, window.innerHeight);
        this.camera.aspect = window.innerWidth / window.innerHeight;
        this.camera.updateProjectionMatrix();
    }

    render() {
        this.renderer.render(this.scene, this.camera);
    }

    start() {
        this.renderer.setAnimationLoop(() => {
            this.render();
        });
    }

    dispose() {
        // Stop animation loop
        this.renderer.setAnimationLoop(null);

        // Remove event listeners
        window.removeEventListener("resize", this.handleResize);

        // Dispose Three.js resources
        this.renderer.dispose();
        this.scene.traverse((object) => {
            if (object.geometry) {
                object.geometry.dispose();
            }
            if (object.material) {
                if (Array.isArray(object.material)) {
                    object.material.forEach(material => material.dispose());
                } else {
                    object.material.dispose();
                }
            }
        });

        // Dispose loaders
        if (this.dracoLoader) {
            this.dracoLoader.dispose();
        }
    }
} 