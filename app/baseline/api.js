import { Client, handle_file } from '@gradio/client';

// # 初始化Gradio客户端
const client = await Client.connect("http://10.120.16.180:20210/")  
const app_info = await client.view_api();
console.log(app_info);

// 生成图像的函数
async function generateImage(promptText, seed = 10) {
    try {
        // 调用 text_to_image API
        const result = await client.predict("/text_to_image", [promptText, seed]);
        console.log("Generated Image: ", result);
        return result;
    } catch (error) {
        console.error("Error generating image: ", error);
    }
}

// 更新网格文本的函数
async function updateMeshText(promptText, seed = 10) {
    try {
        // 调用 update_mesh_text API
        console.log(promptText);
        const result = await client.predict("/update_mesh_text", [promptText, seed]);
        console.log("Generated Video: ", result);
        return result;
    } catch (error) {
        console.error("Error updating mesh text: ", error);
    }
}

// 下载文本网格的函数
async function downloadTextMesh() {
    try {
        // 调用 download_text_mesh API
        const result = await client.predict("/download_text_mesh");
        console.log("Downloaded File: ", result);
        return result;
    } catch (error) {
        console.error("Error downloading text mesh: ", error);
    }
}

// 主函数
async function text_to_3d(promptText) {
    // 生成图像
    console.log("Starting generating Mesh from text:", { promptText });

    const imageResult = await generateImage(promptText);
    console.log("Generating multiview:", { imageResult });

    // 更新网格文本
    const videoResult = await updateMeshText(promptText);

    // 下载文本网格
    const downloadResult = await downloadTextMesh();

    console.log("All results:", { imageResult, videoResult, downloadResult });

    // 返回下载结果
    return downloadResult;
}


// 导出text_to_3d函数
export { text_to_3d };