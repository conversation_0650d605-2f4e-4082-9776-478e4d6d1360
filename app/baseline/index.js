import { BaselineApp } from './app';

// Create and initialize the app
const app = new BaselineApp();
    // '/assets/meshes/DamagedHelmet.glb'
// Load assets and start rendering
app.loadAssets(
    '/assets/backgrounds/royal_esplanade_1k.hdr',
    '/assets/meshes/DamagedHelmet.glb'
)
    .then(() => {
        app.start();
        console.log('Baseline app started successfully');
    })
    .catch(error => {
        console.error('Failed to initialize baseline app:', error);
    });

// Cleanup on page unload
window.addEventListener('beforeunload', () => {
    app.dispose();
});

