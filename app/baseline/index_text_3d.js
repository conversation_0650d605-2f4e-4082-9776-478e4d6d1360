import { BaselineApp } from './app';
import { text_to_3d } from './api';


// 创建并初始化应用
const app = new BaselineApp();

// 加载初始的GLB文件
app.loadAssets(
    '/assets/backgrounds/royal_esplanade_1k.hdr',
    '/assets/meshes/DamagedHelmet.glb'
)
    .then(() => {
        app.start();
        console.log('Baseline app started successfully with default model');
    })
    .catch(error => {
        console.error('Failed to initialize baseline app:', error);
    });


// 监听键盘事件
window.addEventListener('keydown', async (event) => {
    if (event.key === 't' || event.key === 'T') {
        // 弹出文本输入框
        const promptText = prompt('Enter description for new 3D model:');
        
        console.log(`Key pressed: ${event.key}`);
        console.log("Text :", { promptText});

        if (promptText) {
            try {
                // 调用Gradio API生成新的3D模型
            
                const response = await text_to_3d(promptText);
                console.log(response);
                const glbUrl = response.data[0].url;  // 假设API返回GLB文件的URL
                
                // 加载新生成的GLB文件
                await app.loadAssets(
                    '/assets/backgrounds/royal_esplanade_1k.hdr',
                    glbUrl
                );
                console.log('New 3D model loaded successfully');
            } catch (error) {
                console.error('Failed to fetch new 3D model:', error);
            }
        }
    }
});

// 页面卸载时清理
window.addEventListener('beforeunload', () => {
    app.dispose();
});
