# Input Module Documentation

## Overview
The input module handles all camera input, pose detection, and gesture recognition functionality. It provides a unified interface for other modules to access processed pose and gesture data.

## Architecture

```
/input
├── InputManager.js      # Camera and frame management
├── pose/
│   ├── PoseManager.js   # High-level pose management
│   ├── PoseDetector.js  # MediaPipe pose detection
│   └── PoseValidator.js # Pose data validation
└── hand/
    └── HandPoseDetector.js # Hand pose detection
```

## Core Components

### InputManager
- Manages camera initialization and frame capture
- Coordinates with PoseManager for detection scheduling
- Provides frame data to detection pipeline
- Handles input device selection and configuration

### PoseManager
- Centralizes pose detection and processing
- Manages model initialization and lifecycle
- Provides smooth, validated pose data
- <PERSON><PERSON> pose data subscription/notification

### Event Flow
1. InputManager captures frames
2. Frames are passed to PoseManager
3. PoseManager coordinates detection and validation
4. Subscribers receive processed pose data via callbacks

## Usage Example

```javascript
const inputManager = new InputManager();
const poseManager = new PoseManager();

// Setup pose detection
await poseManager.initialize();

// Subscribe to pose updates
poseManager.subscribe((poseData) => {
  // Handle pose data
});

// Start capture
await inputManager.startCamera();
```

## Configuration

### Camera Settings
- Resolution: 640x480 (default)
- FPS: 30 (configurable)
- Device: User-selectable

### Pose Detection
- Model: MediaPipe Pose (Heavy)
- Confidence Threshold: 0.5
- Tracking Confidence: 0.5

### Hand Detection
- Model: MediaPipe Hands
- Max Hands: 2
- Minimum Detection Confidence: 0.5
