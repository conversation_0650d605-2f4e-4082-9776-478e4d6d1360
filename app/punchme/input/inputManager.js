import { Camera as MpCamera } from '@mediapipe/camera_utils';
// import { PerformanceSettings } from '../core/performanceSettings.js';
class InputManager {
    constructor() {
        this.videoElement = null;
        this.context = null;
        this.lastTimestamp = 0;
        this.areLandmarksVisible = false;
        this.frameCallback = null;
        this.isProcessing = false;
        this.minFrameTime = 30; // Minimum time between frames (ms)
        this.mpCamera = null;
        this.hasCamera = false;
        this.onPoseResultsCallback = null;
        this.cameraDevices = [];
        this.frameRequestId = null;
        this.isCleaningUp = false;
        this.resolutionConfig = {
            width: 640,  // Default width
            height: 480, // Default height
            // Add presets for different performance levels
            presets: {
                low: { width: 320, height: 240 },
                medium: { width: 640, height: 480 },
                high: { width: 1280, height: 720 }
            }
        };
        // Replace performance settings with shared config
        // this.performanceSettings = PerformanceSettings;

        // // Initialize IK worker if enabled
        // if (this.performanceSettings.useWorker) {
        //     this.initializeIKWorker();
        // }

        // State management
        this.state = {
            isDetecting: false,
            lastFrameTime: 0,
            frameCount: 0,
            isProcessing: false
        };

        this.poseResultsCallbacks = new Set(); // Store multiple callbacks
    }

    // initializeIKWorker() {
    //     this.ikWorker = new Worker(
    //         new URL('../characters/ikWorker.js', import.meta.url),
    //         { type: 'module' }
    //     );

    //     this.ikWorker.onmessage = ({ data }) => {
    //         if (data.type === 'ikResult' && this.frameCallback) {
    //             // Merge IK results with pose detection results
    //             const combinedResults = {
    //                 ...this.lastPoseResults,
    //                 ikData: data.results
    //             };
    //             this.frameCallback(combinedResults);
    //         }
    //     };
    // }

    setupVideoElement() {
        this.videoElement = document.createElement('video');
        this.videoElement.style.display = 'none';
        this.videoElement.autoplay = true;

        console.log('[InputManager] Setting up video element :', this.videoElement);
    }

    setupOverlayCanvas() {
        this.canvasOverlay = document.createElement("canvas");
        this.canvasOverlay.style.position = "absolute";
        this.canvasOverlay.style.top = "0";
        this.canvasOverlay.style.left = "0";
        this.canvasOverlay.style.pointerEvents = "none";
        this.canvasOverlay.style.display = "none";
        document.body.appendChild(this.canvasOverlay);
    }

    async start() {
        try {
            // Ensure clean state before starting
            // await this.stopDetection();
            // Setup camera last
            await this.setupCamera();

            // Remove pose detector initialization - now handled by PunchViewer

            // Start detection loop
            this.state.isDetecting = true;

            console.log('[InputManager] Started successfully with detection enabled');
            return true;
        } catch (error) {
            console.error('[InputManager] Failed to start:', error);
            throw error;
        }
    }



    async setupCamera() {
        try {
            // Setup video element first
            this.setupVideoElement();

            // Initialize MediaPipe camera with basic video config
            const videoConfig = {
                onFrame: async () => {
                    // Only notify callbacks that a new frame is available
                    const timestamp = performance.now();
                    this.poseResultsCallbacks.forEach(callback => {
                        try {
                            callback({
                                videoElement: this.videoElement,
                                timestamp
                            });
                        } catch (error) {
                            console.error('[InputManager] Error in frame callback:', error);
                        }
                    });
                },
                width: this.resolutionConfig.width,
                height: this.resolutionConfig.height
            };

            this.mpCamera = new MpCamera(this.videoElement, videoConfig);

            // Start camera with constraints
            const constraints = {
                video: {
                    width: { ideal: this.resolutionConfig.width },
                    height: { ideal: this.resolutionConfig.height },
                    frameRate: { ideal: 30, max: 60 }
                }
            };

            await this.mpCamera.start(constraints);
            this.hasCamera = true;
            console.log('[InputManager] Camera started with constraints:', constraints);
        } catch (error) {
            console.error('[InputManager] Camera setup failed:', error);
            this.hasCamera = false;
            throw error;
        }
    }

    // async startDetection() {
    //     console.log('[InputManager] Starting detection');

    //     // Initialize timing with current time
    //     const now = performance.now();
    //     this.state = {
    //         ...this.state,
    //         isDetecting: true,
    //         lastFrameTime: now,
    //         frameCount: 0,
    //         isProcessing: false,
    //         lastValidFrameTime: now  // Add tracking for last valid frame
    //     };

    //     this.areLandmarksVisible = true;

    //     if (!this.poseDetector.isInitialized()) {
    //         console.log('[InputManager] PoseDetector not initialized, initializing...');
    //         return this.poseDetector.initialize()
    //             .then(() => {
    //                 this.startDetectionLoop();
    //             })
    //             .catch(error => {
    //                 console.error('[InputManager] Failed to initialize PoseDetector:', error);
    //                 this.state.isDetecting = false;
    //                 this.areLandmarksVisible = false;
    //             });
    //     }

    //     return Promise.resolve(this.startDetectionLoop());
    // }

    // async stopDetection() {
    //     console.log('[InputManager] Stopping detection');

    //     // Prevent concurrent cleanup
    //     if (this.isCleaningUp) return;
    //     this.isCleaningUp = true;

    //     try {
    //         // Stop detection loop
    //         this.state.isDetecting = false;
    //         this.areLandmarksVisible = false;

    //         // Cancel animation frame
    //         if (this.frameRequestId) {
    //             cancelAnimationFrame(this.frameRequestId);
    //             this.frameRequestId = null;
    //         }

    //         // Stop MediaPipe camera
    //         if (this.mpCamera) {
    //             await this.mpCamera.stop();
    //         }

    //         // Stop video tracks
    //         if (this.videoElement?.srcObject) {
    //             const tracks = this.videoElement.srcObject.getTracks();
    //             tracks.forEach(track => track.stop());
    //             this.videoElement.srcObject = null;
    //         }

    //         // Reset pose detector
    //         if (this.poseDetector) {
    //             await this.poseDetector.reset();
    //         }

    //         // Clear worker if exists
    //         if (this.ikWorker) {
    //             this.ikWorker.terminate();
    //             this.ikWorker = null;
    //         }

    //         // Reset internal state
    //         this.state.lastFrameTime = 0;
    //         this.state.frameCount = 0;
    //         this.state.isProcessing = false;

    //         console.log('[InputManager] Detection stopped and resources cleaned up');
    //     } catch (error) {
    //         console.error('[InputManager] Error during cleanup:', error);
    //     } finally {
    //         this.isCleaningUp = false;
    //     }
    // }

    async toggleCamera() {
        const newState = !this.state.isDetecting;
        console.log('[InputManager] Toggling camera:', newState);

        if (newState) {
            // Reset state before starting
            this.state = {
                isDetecting: false,
                lastFrameTime: performance.now(),  // Reset timing
                frameCount: 0,
                isProcessing: false
            };

            // Ensure clean state before starting
            // await this.stopDetection();
            // Reset and reinitialize components
            await this.reinitialize();
            // Start detection with fresh state
            // await this.startDetection();
        } else {
            // await this.stopDetection();
        }

        return newState;
    }

    async reinitialize() {
        try {
            // Stop everything first
            // await this.stopDetection();

            // Reset state completely
            this.state = {
                isDetecting: false,
                lastFrameTime: performance.now(),
                frameCount: 0,
                isProcessing: false
            };

            // Get fresh canvas
            const canvas = await this.uiManager.cameraViewer?.get_canvas_element();

            // Reinitialize pose detector with fresh canvas
            await this.poseDetector.initialize(canvas);

            // Reinitialize video element
            // this.setupVideoElement();

            // Reinitialize worker if needed
            // if (this.performanceSettings.useWorker) {
            //     this.initializeIKWorker();
            // }

            console.log('[InputManager] Components reinitialized successfully');
        } catch (error) {
            console.error('[InputManager] Reinitialization failed:', error);
            throw error;
        }
    }

    onPoseResults(callback) {
        console.log('[InputManager] Registering pose results callback');
        this.poseResultsCallbacks.add(callback);
        return () => {
            console.log('[InputManager] Unregistering pose results callback');
            this.poseResultsCallbacks.delete(callback);
        };
    }

    // async startDetectionLoop() {
    //     console.log('[InputManager] Starting detection loop');

    //     const processFrame = async () => {
    //         if (!this.state.isDetecting) return;

    //         try {
    //             // Check if already processing or video not ready
    //             if (!this.videoElement?.readyState === 4 || this.state.isProcessing) {
    //                 this.frameRequestId = requestAnimationFrame(processFrame);
    //                 return;
    //             }

    //             this.state.isProcessing = true;

    //             // Ensure detector is initialized
    //             if (!this.poseDetector.isInitialized()) {
    //                 try {
    //                     await this.poseDetector.initialize();
    //                 } catch (error) {
    //                     console.error('[InputManager] Failed to initialize PoseDetector:', error);
    //                     this.state.isProcessing = false;
    //                     this.frameRequestId = requestAnimationFrame(processFrame);
    //                     return;
    //                 }
    //             }

    //             // Detect pose
    //             const results = await this.poseDetector.detectPose(
    //                 this.videoElement,
    //                 performance.now()
    //             ).catch(error => {
    //                 console.error('[InputManager] Pose detection error:', error);
    //                 return null;
    //             });

    //             // Process results if valid
    //             if (results?.landmarks?.[0]) {
    //                 this.lastPoseResults = results;
    //                 this.poseResultsCallbacks.forEach(callback => {
    //                     try {
    //                         callback(results);
    //                     } catch (error) {
    //                         console.error('[InputManager] Error in pose results callback:', error);
    //                     }
    //                 });
    //             }

    //             this.state.frameCount++;
    //             this.state.isProcessing = false;
    //             this.frameRequestId = requestAnimationFrame(processFrame);

    //         } catch (error) {
    //             console.error('[InputManager] Error in processFrame:', error);
    //             this.state.isProcessing = false;
    //             this.frameRequestId = requestAnimationFrame(processFrame);
    //         }
    //     };

    //     this.frameRequestId = requestAnimationFrame(processFrame);
    // }

    // Separate visualization state from detection state
    setVisualizationEnabled(enabled) {
        this.areLandmarksVisible = enabled;
        console.log('[InputManager] Visualization state:', enabled);
    }

    captureVideoFrame() {
        if (!this.videoElement || this.videoElement.readyState !== 4) return null;

        // Reuse canvas and context
        if (!this.offscreenCanvas) {
            this.offscreenCanvas = new OffscreenCanvas(
                this.videoElement.videoWidth,
                this.videoElement.videoHeight
            );
            this.offscreenContext = this.offscreenCanvas.getContext('2d');
        }

        // Draw frame to offscreen canvas
        this.offscreenContext.drawImage(this.videoElement, 0, 0);
        return this.offscreenContext.getImageData(
            0, 0,
            this.offscreenCanvas.width,
            this.offscreenCanvas.height
        );
    }

    // handleWorkerResults(results) {
    //     if (!results) return;

    // const processingTime = performance.now() - results.timestamp;
    // this.updateProcessingTimes(processingTime);

    // // If processing is too slow, consider skipping frames
    // if (processingTime > this.performanceSettings.frameSkipThreshold) {
    //     console.warn('[InputManager] Frame processing too slow:', processingTime.toFixed(2) + 'ms');
    //     return;
    // }

    // Forward results to callback
    //     if (this.frameCallback) {
    //         this.frameCallback(results);
    //     }
    // }

    // updateProcessingTimes(time) {
    //     this.performanceSettings.processingTimeWindow.push(time);
    //     if (this.performanceSettings.processingTimeWindow.length > this.performanceSettings.windowSize) {
    //         this.performanceSettings.processingTimeWindow.shift();
    //     }
    // }

    // updatePerformanceMetrics(now) {
    //     const avgProcessingTime = this.performanceSettings.processingTimeWindow.reduce((a, b) => a + b, 0)
    //         / this.performanceSettings.processingTimeWindow.length;

    //     console.log('[InputManager] Performance metrics:', {
    //         fps: (1000 / (now - this.lastMetricsUpdate)).toFixed(1),
    //         avgProcessingTime: avgProcessingTime.toFixed(1) + 'ms',
    //         frameCount: this.frameCount
    //     });

    //     this.lastMetricsUpdate = now;
    // }

    dispose() {
        this.stopDetection();
    }
}

export default InputManager;
