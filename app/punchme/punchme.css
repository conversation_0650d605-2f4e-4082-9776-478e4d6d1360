/* Base styles */
body {
    margin: 0;
    padding: 0;
    overflow: hidden;
    background: #000;
}

#renderCanvas {
    width: 100vw;
    height: 100vh;
    display: block;
    touch-action: none;
}

#ui-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
}

/* Punch-specific styles */
.punch-controls {
    position: fixed;
    bottom: 24px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    padding: 16px;
    border-radius: 12px;
    display: flex;
    gap: 20px;
    align-items: center;
    z-index: 30;
    backdrop-filter: blur(5px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Button styles */
.punch-button {
    padding: 10px 20px;
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 16px;
    font-weight: 500;
    transition: all 0.2s ease;
    background: #ff4444;
}

.punch-button:hover {
    background: #ff6666;
    transform: translateY(-1px);
}

.punch-button.active {
    background: #44ff44;
}

.punch-strength {
    display: flex;
    flex-direction: column;
    gap: 8px;
    color: white;
}

.punch-strength label {
    font-size: 14px;
    opacity: 0.8;
}

.punch-strength input[type="range"] {
    width: 150px;
    height: 4px;
    -webkit-appearance: none;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 2px;
    outline: none;
}

.punch-strength input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    width: 16px;
    height: 16px;
    background: #ff4444;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.2s ease;
}

.punch-strength input[type="range"]::-webkit-slider-thumb:hover {
    transform: scale(1.2);
    background: #ff6666;
}

/* Impact effect styles */
.impact-flash {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 0, 0, 0.2);
    pointer-events: none;
    opacity: 0;
    transition: opacity 0.1s ease;
}

.impact-flash.active {
    opacity: 1;
}

#viewer-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 10;
}

/* 确保UI元素在画布上层 */
.camera-button,
.punch-controls,
.health-bar {
    pointer-events: auto;
    z-index: 100;
}

#renderCanvas {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    touch-action: none;
    z-index: 1;
}

/* 确保所有UI元素可以接收点击事件 */
.punch-controls,
.camera-button,
.health-bar,
.modal {
    pointer-events: auto;
}

/* UI 层级关系 */
.camera-button {
    z-index: 20;
}

.punch-controls {
    z-index: 30;
}

.modal {
    z-index: 1000;
}

/* 更新健康条布局和层级 */
#health-bars-container {
    position: fixed;
    top: 12px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 100px;
    /* 增大间距 */
    z-index: 0;
    /* 降低层级 */
    pointer-events: none;
}

/* 更新角色血条外观 */
.character-health-bar {
    display: flex;
    align-items: center;
    gap: 8px;
    background: #2b2b2b;
    border-radius: 10px;
    padding: 8px 16px;
    min-width: 220px;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.3);
    border: 2px solid;
}

/* 玩家和敌人专属样式 */
.player-health {
    border-color: #00c853;
}

.player-health .character-name {
    color: #00c853;
}

.enemy-health {
    border-color: #d50000;
}

.enemy-health .character-name {
    color: #ff5252;
}

/* 更新血条容器样式 */
.health-bar-container {
    position: relative;
    width: 160px;
    height: 12px;
    background: #444444;
    border: 1px solid;
    border-radius: 6px;
    overflow: hidden;
}

/* 玩家和敌人血条容器样式 */
.player-health .health-bar-container {
    border-color: #00c853;
    background: #333333;
}

.enemy-health .health-bar-container {
    border-color: #d50000;
    background: #333333;
}

/* 更新玩家血条填充样式 */
.player-health .health-fill {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    width: 100%;
    background: linear-gradient(90deg, #00c853, #64dd17);
    box-shadow: 0 0 8px rgba(0, 200, 83, 0.5);
    transition: width 0.4s ease;
}

/* 更新敌人血条填充样式 */
.enemy-health .health-fill {
    position: absolute;
    top: 0;
    bottom: 0;
    right: 0;
    width: 100%;
    background: linear-gradient(90deg, #d50000, #ff1744);
    box-shadow: 0 0 8px rgba(213, 0, 0, 0.5);
    transition: width 0.4s ease;
}

/* 更新血量文本样式 */
.health-text {
    position: absolute;
    right: 4px;
    top: 50%;
    transform: translateY(-50%);
    color: #ffffff;
    font-size: 10px;
    font-weight: bold;
    text-shadow: 0 0 2px rgba(0, 0, 0, 0.8);
    pointer-events: none;
    z-index: 2;
}

/* 血条状态颜色 */
.health-fill.high {
    opacity: 1;
}

.health-fill.medium {
    opacity: 0.8;
}

.health-fill.low {
    opacity: 0.6;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        opacity: 0.6;
    }

    50% {
        opacity: 0.8;
    }

    100% {
        opacity: 0.6;
    }
}

/* 受伤闪烁动画 */
@keyframes damage-flash {

    0%,
    100% {
        transform: scale(1);
    }

    50% {
        transform: scale(1.02);
    }
}

.damage-flash {
    animation: damage-flash 0.2s ease;
}

/* 响应式设计 */
@media (max-width: 768px) {
    #health-bars-container {
        width: auto;
        gap: 10px;
        padding: 5px;
    }

    .character-health-bar {
        min-width: 120px;
        padding: 4px 8px;
    }

    .character-avatar {
        width: 20px;
        height: 20px;
    }

    .character-name {
        font-size: 10px;
    }

    .health-bar-container {
        width: 60px;
        height: 8px;
    }

    .health-text {
        font-size: 9px;
    }
}

.camera-button {
    position: fixed;
    top: 20px;
    right: 20px;
    /* Changed: position directly to the right edge with padding */
    background: rgba(33, 150, 243, 0.9);
    /* A more modern background color */
    color: white;
    z-index: 100;
    width: auto;
    min-width: 120px;
    border-radius: 6px;
    transition: all 0.2s ease;
    padding: 8px 16px;
    border: none;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    font-weight: 500;
    cursor: pointer;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    /* Add a subtle shadow */
}

.camera-button:hover:not(.disabled) {
    background: rgba(25, 118, 210, 1);
    /* Darken on hover */
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    /* Enhance shadow on hover */
}

.camera-button.disabled {
    background: #9e9e9e;
    cursor: not-allowed;
    opacity: 0.7;
}

.camera-button svg {
    width: 20px;
    height: 20px;
}


/* Inspector Button - Match Camera Button Style */
.inspector-button {
    position: fixed;
    top: 20px;
    right: 160px;
    /* Changed: position to the left of camera button (accounting for min-width + padding) */
    background: rgba(33, 150, 243, 0.9);
    color: white;
    z-index: 100;
    width: auto;
    min-width: 120px;
    border-radius: 6px;
    transition: all 0.2s ease;
    padding: 8px 16px;
    border: none;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    font-weight: 500;
    cursor: pointer;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.inspector-button:hover:not(.disabled) {
    background: rgba(25, 118, 210, 1);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.inspector-button.active {
    background: #4CAF50;
}

.inspector-button svg {
    width: 20px;
    height: 20px;
}

.inspector-button.disabled {
    background: #9e9e9e;
    cursor: not-allowed;
    opacity: 0.7;
}