import { Mesh<PERSON>uilder, StandardMaterial, Color3 } from "@babylonjs/core";

export class CameraDebugVisualizer {
    constructor(scene) {
        this.scene = scene;
        this.enabled = false;
        this.markers = {};

        // Color mapping for string color names
        this.colorMap = {
            "RED": new Color3(1, 0, 0),
            "GREEN": new Color3(0, 1, 0),
            "BLUE": new Color3(0, 0, 1),
            "YELLOW": new Color3(1, 1, 0),
            "CYAN": new Color3(0, 1, 1),
            "MAGENTA": new Color3(1, 0, 1),
            "WHITE": new Color3(1, 1, 1),
            "BLACK": new Color3(0, 0, 0),
            "ORANGE": new Color3(1, 0.5, 0),
            "PURPLE": new Color3(0.5, 0, 0.5),
            "PINK": new Color3(1, 0.75, 0.8),
            "BROWN": new Color3(0.6, 0.3, 0),
            "GRAY": new Color3(0.5, 0.5, 0.5)
        };

        // Create materials with different colors for different landmark types
        this.materials = {
            target: this._createMaterial("targetMat", new Color3(1, 0, 0)),    // Red for target
            shoulder: this._createMaterial("shoulderMat", new Color3(0, 1, 0)), // Green for shoulder
            midpoint: this._createMaterial("midpointMat", new Color3(0, 0, 1)), // Blue for midpoint
            default: this._createMaterial("defaultMat", new Color3(1, 1, 0))    // Yellow for default
        };
    }

    _createMaterial(name, color) {
        const material = new StandardMaterial(name, this.scene);
        material.diffuseColor = color;
        material.specularColor = new Color3(0.2, 0.2, 0.2);
        material.emissiveColor = color.scale(0.5); // Make it slightly emissive for better visibility
        return material;
    }

    enable() {
        this.enabled = true;
        // Make all existing markers visible
        Object.values(this.markers).forEach(marker => {
            marker.isVisible = true;
        });
    }

    disable() {
        this.enabled = false;
        // Hide all markers
        Object.values(this.markers).forEach(marker => {
            marker.isVisible = false;
        });
    }

    showMarker(position, name, type = 'default', color = null) {
        if (!this.enabled) return;

        // Remove existing marker with the same name if it exists
        if (this.markers[name]) {
            this.markers[name].dispose();
        }

        // Create a small box at the position
        const mesh = MeshBuilder.CreateBox(name, { size: 0.05 }, this.scene);
        mesh.position = position.clone();

        // Apply material based on provided color or type
        if (color) {
            // Handle color as string input
            let colorObj;

            if (typeof color === 'string') {
                // Convert string to uppercase for case-insensitive matching
                const colorKey = color.toUpperCase();
                colorObj = this.colorMap[colorKey];

                // Use default color if string not found in color map
                if (!colorObj) {
                    console.warn(`Color "${color}" not found, using default color.`);
                    colorObj = this.colorMap.YELLOW;
                }
            } else if (color instanceof Color3) {
                // Handle if Color3 object is directly passed (for backward compatibility)
                colorObj = color;
            } else {
                // Default color for invalid input
                colorObj = this.colorMap.YELLOW;
            }

            // Create a custom material with the determined color
            const customMat = this._createMaterial(`${name}_customMat`, colorObj);
            mesh.material = customMat;
        } else {
            // Use predefined material based on type if no color is specified
            mesh.material = this.materials[type] || this.materials.default;
        }

        // Store the marker for later reference
        this.markers[name] = mesh;

        return mesh;
    }

    removeMarker(name) {
        if (this.markers[name]) {
            this.markers[name].dispose();
            delete this.markers[name];
        }
    }

    dispose() {
        // Clean up all markers
        Object.values(this.markers).forEach(marker => {
            marker.dispose();
        });
        this.markers = {};

        // Dispose materials
        Object.values(this.materials).forEach(material => {
            material.dispose();
        });
    }
}
