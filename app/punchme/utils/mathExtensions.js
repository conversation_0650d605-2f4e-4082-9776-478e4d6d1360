import { Vector3, Quaternion } from '@babylonjs/core';

// Extend Quaternion with rotation alignment helper
export function initQuaternionExtensions() {
    if (typeof Quaternion.RotationAlign !== 'function') {
        Quaternion.RotationAlign = function(fromVec, toVec) {
            const cross = Vector3.Cross(fromVec, toVec);
            const dot = Vector3.Dot(fromVec, toVec);
            
            if (dot < -0.999999) {
                // Vectors nearly opposite: choose arbitrary orthogonal axis
                let ortho = Vector3.Cross(fromVec, new Vector3(1, 0, 0));
                if (ortho.length() < 0.001) {
                    ortho = Vector3.Cross(fromVec, new Vector3(0, 1, 0));
                }
                ortho.normalize();
                return Quaternion.RotationAxis(ortho, Math.PI);
            }

            const s = Math.sqrt((1 + dot) * 2);
            const invs = 1 / s;
            return new Quaternion(
                cross.x * invs,
                cross.y * invs,
                cross.z * invs,
                s * 0.5
            );
        };
    }
}

export function ensureMathExtensions() {
    initQuaternionExtensions();
    // 可以添加其他数学扩展的初始化
}
