/**
 * Compresses landmark data using Float32Array for efficient transfer
 * @param {Array} data - Array of landmark objects
 * @returns {Float32Array} - Compressed binary data
 */
export function compressData(data) {
    // For landmarks array, each point has x,y,z,visibility
    const bufferSize = data.length * 33 * 3; // 33 landmarks * 3 components (x,y,z)
    const buffer = new Float32Array(bufferSize);
    
    data.forEach((frame, frameIndex) => {
        const offset = frameIndex * 33 * 3;
        frame.forEach((landmark, i) => {
            const baseIndex = offset + (i * 3);
            buffer[baseIndex] = landmark.x;
            buffer[baseIndex + 1] = landmark.y;
            buffer[baseIndex + 2] = landmark.z || 0;
        });
    });
    
    return buffer;
}

/**
 * Decompresses binary data back into landmark objects
 * @param {Float32Array} buffer - Compressed binary data
 * @returns {Array} - Array of landmark objects
 */
export function decompressData(buffer) {
    const frames = [];
    const frameSize = 33 * 3; // 33 landmarks * 3 components
    
    for (let frameIndex = 0; frameIndex < buffer.length / frameSize; frameIndex++) {
        const landmarks = [];
        const offset = frameIndex * frameSize;
        
        for (let i = 0; i < 33; i++) {
            const baseIndex = offset + (i * 3);
            landmarks.push({
                x: buffer[baseIndex],
                y: buffer[baseIndex + 1],
                z: buffer[baseIndex + 2],
                visibility: 1.0 // Default visibility
            });
        }
        
        frames.push(landmarks);
    }
    
    return frames;
}
