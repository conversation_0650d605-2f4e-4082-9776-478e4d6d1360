/**
 * Utility class for caching and efficiently computing matrix transformations
 * Used to improve performance when repeatedly transforming coordinates
 * between world and local spaces.
 */
class MatrixCache {
    /**
     * Create a new matrix cache with the specified options
     * @param {Object} options Configuration options
     * @param {number} options.timeout Time in ms after which cached matrices are invalidated
     * @param {number} options.movementThreshold Distance threshold that invalidates cached matrices
     * @param {number} options.rotationThreshold Rotation threshold that invalidates cached matrices
     */
    constructor(options = {}) {
        this.options = {
            timeout: options.timeout || 100,
            movementThreshold: options.movementThreshold || 0.005,
            rotationThreshold: options.rotationThreshold || 0.01
        };

        this.caches = {
            world: new Map(),
            inverseWorld: new Map(),
            lastPositions: new Map(),
            lastRotations: new Map()
        };
    }

    /**
     * Get the world matrix for a mesh, using cached value if valid
     * @param {BABYLON.Mesh} mesh The mesh to get the world matrix for
     * @returns {BABYLON.Matrix} The world matrix
     */
    getWorldMatrix(mesh) {
        if (!mesh) return null;

        const id = mesh.uniqueId;
        const cached = this.caches.world.get(id);
        
        // Check if we need to recompute
        if (this._shouldRecompute(mesh, id)) {
            // Get fresh world matrix from mesh
            const worldMatrix = mesh.getWorldMatrix().clone();
            
            // Cache it with timestamp
            this.caches.world.set(id, {
                matrix: worldMatrix,
                timestamp: Date.now()
            });
            
            // Cache position and rotation for change detection
            this._updatePositionRotationCache(mesh, id);
            
            return worldMatrix;
        }
        
        return cached ? cached.matrix : mesh.getWorldMatrix().clone();
    }

    /**
     * Get the inverse world matrix for a mesh, using cached value if valid
     * @param {BABYLON.Mesh} mesh The mesh to get the inverse world matrix for
     * @returns {BABYLON.Matrix} The inverse world matrix
     */
    getInverseWorldMatrix(mesh) {
        if (!mesh) return null;

        const id = mesh.uniqueId;
        const cached = this.caches.inverseWorld.get(id);
        
        // Check if we need to recompute
        if (this._shouldRecompute(mesh, id)) {
            // Get fresh world matrix and invert it
            const worldMatrix = this.getWorldMatrix(mesh);
            const inverseWorldMatrix = BABYLON.Matrix.Invert(worldMatrix);
            
            // Cache it with timestamp
            this.caches.inverseWorld.set(id, {
                matrix: inverseWorldMatrix,
                timestamp: Date.now()
            });
            
            return inverseWorldMatrix;
        }
        
        return cached ? cached.matrix : BABYLON.Matrix.Invert(mesh.getWorldMatrix());
    }

    /**
     * Transform a point from world space to local space
     * @param {BABYLON.Vector3} worldPoint Point in world space
     * @param {BABYLON.Mesh} mesh Mesh whose local space to transform into
     * @returns {BABYLON.Vector3} Point in mesh's local space
     */
    worldToLocal(worldPoint, mesh) {
        if (!worldPoint || !mesh) return null;
        
        const inverseWorldMatrix = this.getInverseWorldMatrix(mesh);
        if (!inverseWorldMatrix) return null;
        
        return BABYLON.Vector3.TransformCoordinates(worldPoint, inverseWorldMatrix);
    }

    /**
     * Transform a point from local space to world space
     * @param {BABYLON.Vector3} localPoint Point in mesh's local space
     * @param {BABYLON.Mesh} mesh Mesh whose local space the point is in
     * @returns {BABYLON.Vector3} Point in world space
     */
    localToWorld(localPoint, mesh) {
        if (!localPoint || !mesh) return null;
        
        const worldMatrix = this.getWorldMatrix(mesh);
        if (!worldMatrix) return null;
        
        return BABYLON.Vector3.TransformCoordinates(localPoint, worldMatrix);
    }

    /**
     * Clear all cached matrices
     */
    clearAllCaches() {
        this.caches.world.clear();
        this.caches.inverseWorld.clear();
        this.caches.lastPositions.clear();
        this.caches.lastRotations.clear();
    }

    /**
     * Clear cached matrices for a specific mesh
     * @param {BABYLON.Mesh} mesh The mesh to clear caches for
     */
    clearCacheForMesh(mesh) {
        if (!mesh) return;
        
        const id = mesh.uniqueId;
        this.caches.world.delete(id);
        this.caches.inverseWorld.delete(id);
        this.caches.lastPositions.delete(id);
        this.caches.lastRotations.delete(id);
    }

    /**
     * Check if matrix needs to be recomputed due to movement, rotation or timeout
     * @private
     */
    _shouldRecompute(mesh, id) {
        // Always recompute if no cache exists
        if (!this.caches.world.has(id)) return true;
        
        const cached = this.caches.world.get(id);
        const lastPosition = this.caches.lastPositions.get(id);
        const lastRotation = this.caches.lastRotations.get(id);
        
        // Check timeout
        if (Date.now() - cached.timestamp > this.options.timeout) return true;
        
        // Check significant position change
        if (lastPosition && mesh.position) {
            const distSquared = BABYLON.Vector3.DistanceSquared(lastPosition, mesh.position);
            if (distSquared > this.options.movementThreshold * this.options.movementThreshold) {
                return true;
            }
        }
        
        // Check significant rotation change
        if (lastRotation && mesh.rotationQuaternion) {
            const dotProduct = BABYLON.Quaternion.Dot(lastRotation, mesh.rotationQuaternion);
            // Dot product close to 1 means similar rotation, close to 0 means very different
            if (Math.abs(1 - dotProduct) > this.options.rotationThreshold) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * Update the cached position and rotation for change detection
     * @private
     */
    _updatePositionRotationCache(mesh, id) {
        if (mesh.position) {
            this.caches.lastPositions.set(id, mesh.position.clone());
        }
        
        if (mesh.rotationQuaternion) {
            this.caches.lastRotations.set(id, mesh.rotationQuaternion.clone());
        }
    }
}

export default MatrixCache;