/**
 * Global motion smoothing configuration for avatars
 * Handles all settings for blending between poses based on confidence,
 * visibility and other factors
 */
class SmoothingConfig {
    constructor() {
        // Initialize the listeners array FIRST, before any other operations
        this._listeners = [];

        // Default settings
        this.baseSlerpRatio = 0.8;        // Base interpolation ratio (0.8 = blend 80% toward target)
        this.positionStiffness = 0.8;      // Position stiffness (1.0 = follow exactly, 0.0 = don't move)
        this.rotationStiffness = 0.8;      // Rotation stiffness 
        this.visibilityFactor = true;      // Whether to use visibility as a factor in blending
        this.confidenceThreshold = 0.5;    // Minimum visibility to be considered high confidence

        // Set medium smoothing AFTER listeners array is initialized
        // This will trigger a notification to all listeners
        this.setMediumSmoothing();
    }

    /**
     * Update configuration settings
     * @param {Object} config - Configuration options to update
     */
    update(config) {
        if (!config) return;

        let changed = false;

        if (typeof config.baseSlerpRatio === 'number') {
            this.baseSlerpRatio = config.baseSlerpRatio;
            changed = true;
        }

        if (typeof config.positionStiffness === 'number') {
            this.positionStiffness = config.positionStiffness;
            changed = true;
        }

        if (typeof config.rotationStiffness === 'number') {
            this.rotationStiffness = config.rotationStiffness;
            changed = true;
        }

        if (typeof config.visibilityFactor !== 'undefined') {
            this.visibilityFactor = config.visibilityFactor;
            changed = true;
        }

        if (typeof config.confidenceThreshold === 'number') {
            this.confidenceThreshold = config.confidenceThreshold;
            changed = true;
        }

        // Notify listeners if anything changed
        if (changed && this._listeners) {
            this.notifyListeners();
        }
    }

    /**
     * Register a listener to be notified when settings change
     * @param {Object} listener - Object with an updateSmoothingConfig method
     */
    addListener(listener) {
        if (!this._listeners) {
            this._listeners = [];
        }

        if (listener && typeof listener.updateSmoothingConfig === 'function' &&
            !this._listeners.includes(listener)) {
            this._listeners.push(listener);
        }
    }

    /**
     * Remove a listener
     * @param {Object} listener - Previously registered listener
     */
    removeListener(listener) {
        if (!this._listeners) return;

        const index = this._listeners.indexOf(listener);
        if (index !== -1) {
            this._listeners.splice(index, 1);
        }
    }

    /**
     * Notify all registered listeners of configuration changes
     */
    notifyListeners() {
        // Check if listeners array exists and is iterable
        if (!this._listeners || !Array.isArray(this._listeners)) {
            console.warn("SmoothingConfig: _listeners is not properly initialized");
            return;
        }

        for (const listener of this._listeners) {
            try {
                if (listener && typeof listener.updateSmoothingConfig === 'function') {
                    listener.updateSmoothingConfig(this);
                }
            } catch (err) {
                console.error("Error notifying smoothing config listener:", err);
            }
        }
    }

    /**
     * Create a copy of the current configuration
     * @returns {Object} Configuration object
     */
    getConfig() {
        return {
            baseSlerpRatio: this.baseSlerpRatio,
            positionStiffness: this.positionStiffness,
            rotationStiffness: this.rotationStiffness,
            visibilityFactor: this.visibilityFactor,
            confidenceThreshold: this.confidenceThreshold
        };
    }

    // Preset configurations
    setHighSmoothing() {
        this.update({
            baseSlerpRatio: 0.5,         // Slow blending (50% per frame)
            positionStiffness: 0.6,      // Moderately stiff positions
            rotationStiffness: 0.5,      // Smoother rotations
            visibilityFactor: true       // Use visibility weighting
        });
    }

    setMediumSmoothing() {
        this.update({
            baseSlerpRatio: 0.7,         // Medium blending (70% per frame)
            positionStiffness: 0.8,      // Standard stiffness
            rotationStiffness: 0.7,      // Standard rotation stiffness
            visibilityFactor: true       // Use visibility weighting
        });
    }

    setLowSmoothing() {
        this.update({
            baseSlerpRatio: 0.9,         // Fast blending (90% per frame)
            positionStiffness: 0.95,     // Very responsive
            rotationStiffness: 0.9,      // Responsive rotations
            visibilityFactor: true       // Still use visibility
        });
    }
}

// Export a singleton instance for global use
export default new SmoothingConfig();
