// File: GameCameraManager.js

import { ArcRotateCamera, Vector3, CubicEase, Animation, UniversalCamera, TransformNode, MeshBuilder, EasingFunction, AnimationGroup } from "@babylonjs/core";
import { CameraDebugVisualizer } from "../utils/cameraDebugVisualizer.js";

export class GameCameraManager {
  constructor(scene, canvas) {
    this.scene = scene;
    this.canvas = canvas;
    this.firstPersonCamera = null;
    this.thirdPersonCamera = null;
    // Used to store an onBeforeRenderObservable if needed
    this.cameraUpdateObserver = null;

    // Flag to disable observer updates during transitions
    this.isTransitioning = false;

    // Add debug visualizer
    this.debugVisualizer = new CameraDebugVisualizer(scene);
    // Enable debug visualization in development mode
    if (process.env.NODE_ENV === 'development') {
      this.debugVisualizer.enable();
    }

    // Flag to track if user has manually adjusted camera
    this.userAdjustedCamera = false;
  }

  /**
  * Revised setupStartCamera:
  * Creates the start camera and animates its properties from default values to a “framing” view
  * computed from the player and enemy positions.
  */
  setupStartCamera() {
    // Create the start camera with initial parameters.
    const arcCam = new ArcRotateCamera(
      "startCam",
      0,
      0.7,
      6,
      new Vector3(0, 1, 0),
      this.scene
    );
    arcCam.wheelDeltaPercentage = 0.01;
    arcCam.attachControl(this.canvas, true);
    this.scene.activeCamera = arcCam;

    // Compute desired parameters based on player and enemy.
    const player = this.scene.getMeshByName("player");
    const enemy = this.scene.getMeshByName("enemy");
    if (player && enemy) {
      player.computeWorldMatrix(true);
      enemy.computeWorldMatrix(true);
      const pBox = player.getBoundingInfo().boundingBox;
      const eBox = enemy.getBoundingInfo().boundingBox;
      const min = Vector3.Minimize(pBox.minimumWorld, eBox.minimumWorld);
      const max = Vector3.Maximize(pBox.maximumWorld, eBox.maximumWorld);
      const center = min.add(max).scale(0.5);
      const size = max.subtract(min);
      const largestDim = Math.max(size.x, size.y, size.z);
      const desiredRadius = Math.max(5, largestDim * 1.0);
      const desiredBeta = 0.8;
      const verticalOffset = largestDim * 0.2;
      const desiredTarget = new Vector3(center.x, center.y + verticalOffset, center.z);
      const finalAlpha = Math.PI / 2; // Example: view from the right

      const fps = 60;
      const durationInFrames = 60;
      const ease = new CubicEase();
      ease.setEasingMode(EasingFunction.EASINGMODE_EASEINOUT);

      // Phase 1: Animate alpha, beta, and target.
      const alphaAnim = new Animation(
        "alphaAnim",
        "alpha",
        fps,
        Animation.ANIMATIONTYPE_FLOAT,
        Animation.ANIMATIONLOOPMODE_CONSTANT
      );
      alphaAnim.setKeys([
        { frame: 0, value: arcCam.alpha },
        { frame: durationInFrames, value: finalAlpha }
      ]);
      alphaAnim.setEasingFunction(ease);

      const betaAnim = new Animation(
        "betaAnim",
        "beta",
        fps,
        Animation.ANIMATIONTYPE_FLOAT,
        Animation.ANIMATIONLOOPMODE_CONSTANT
      );
      betaAnim.setKeys([
        { frame: 0, value: arcCam.beta },
        { frame: durationInFrames, value: desiredBeta }
      ]);
      betaAnim.setEasingFunction(ease);

      const targetAnim = new Animation(
        "targetAnim",
        "target",
        fps,
        Animation.ANIMATIONTYPE_VECTOR3,
        Animation.ANIMATIONLOOPMODE_CONSTANT
      );
      targetAnim.setKeys([
        { frame: 0, value: arcCam.target.clone() },
        { frame: durationInFrames, value: desiredTarget }
      ]);
      targetAnim.setEasingFunction(ease);

      arcCam.animations = [alphaAnim, betaAnim, targetAnim];
      this.scene.beginAnimation(arcCam, 0, durationInFrames, false, 1, () => {
        // Phase 2: Animate the radius.
        const radiusAnim = new Animation(
          "radiusAnim",
          "radius",
          fps,
          Animation.ANIMATIONTYPE_FLOAT,
          Animation.ANIMATIONLOOPMODE_CONSTANT
        );
        radiusAnim.setKeys([
          { frame: 0, value: arcCam.radius },
          { frame: durationInFrames, value: desiredRadius }
        ]);
        radiusAnim.setEasingFunction(ease);
        arcCam.animations = [radiusAnim];
        this.scene.beginAnimation(arcCam, 0, durationInFrames, false);
      });
    }
    return arcCam;
  }
  /**
   * setupFollowCamera now accepts an optional baseRadius.
   * It computes the final parameters using computeCameraParamsForCharacters,
   * then creates the follow camera starting with a slight offset for animation.
   */
  setupFollowCamera() {
    // Retrieve the player mesh
    const player = this.scene.getMeshByName("player");
    const skeleton = player.metadata.skeleton;
    if (!player) {
      console.warn("[GameCameraManager] No player mesh found for camera setup.");
      return null;
    }
    // // 1. Find the head bone (by name, case-insensitive)
    // let headBone = skeleton.bones.find(b => b.name.toLowerCase().includes("head"));
    // // 2. Compute absolute transforms for accurate bone positions
    // skeleton.computeAbsoluteTransforms();  // update bone matrices 
    // // 3. Create a transform node to attach to the head bone
    // this.dummyBox = new TransformNode("cameraMount", this.scene);
    // this.dummyBox.attachToBone(headBone, player);  // attach node to head bone [oai_citation_attribution:5‡grideasy.github.io](http://grideasy.github.io/tutorials/How_to_use_Bones_and_Skeletons#:~:text=Starting%20with%20babylon,bone%20with%20the%20following%20code)
    
    // (Mixamo rigs typically have a "Head" bone [oai_citation_attribution:3‡forum.babylonjs.com]
    const cameraOffset = new Vector3(0, player.metadata.height-0.1, 0.1); // Lower height, slightly back
    // Create a transform node attached to the player's head bone if available
    this.dummyBox = MeshBuilder.CreateBox("test", { size: 0.2 }, this.scene);
    this.dummyBox.isVisible = false;
    this.dummyBox.parent = player;
    this.dummyBox.position.addInPlace(cameraOffset);
    // console.log(player.getChildMeshes());
    // First-Person Camera setup
    const firstPersonCamera = new UniversalCamera("firstPersonCam", Vector3.Zero(), this.scene);
    firstPersonCamera.position = this.dummyBox.getAbsolutePosition();
    // firstPersonCamera.position.set(0, 0, 0);             // position at bone (can add offset if needed)
    // Widen the field of view for better peripheral vision
    // firstPersonCamera.fov = 1.2; // Approximately 70 degrees (default is 0.8)
    firstPersonCamera.minZ = 0.2; // Prevent camera clipping at close range
    // Automatically target the enemy's upper head if available
    const eyeOffset = new Vector3(0, 1.0, 0);
    const enemy = this.scene.getMeshByName('enemy');
    firstPersonCamera.setTarget(enemy.getAbsolutePosition().add(eyeOffset));

    // Third-Person Camera setup using ArcRotateCamera
    // Position the camera behind the player with an offset
    const thirdPersonCamera = new ArcRotateCamera(
      "thirdPersonCam",
      Math.PI / -2,
      0.8,
      3,
      Vector3.Zero(),
      this.scene
    );
    // thirdPersonCamera.attachControl(this.canvas, true);
    thirdPersonCamera.lowerRadiusLimit = 2;
    thirdPersonCamera.upperRadiusLimit = 15;
    thirdPersonCamera.wheelDeltaPercentage = 0.01;
    thirdPersonCamera.target = this.dummyBox;

    // Add input observers to detect user camera adjustments
    // thirdPersonCamera.onViewMatrixChangedObservable.add(() => {
    //   if (thirdPersonCamera.inertialAlphaOffset !== 0 ||
    //     thirdPersonCamera.inertialBetaOffset !== 0 ||
    //     thirdPersonCamera.inertialRadiusOffset !== 0) {
    //     this.userAdjustedCamera = true;
    //   }
    // });

    // Set default active camera to first-person view
    this.scene.activeCamera = firstPersonCamera;

    // Store references for toggling
    this.firstPersonCamera = firstPersonCamera;
    this.thirdPersonCamera = thirdPersonCamera;
    this.firstPersonCamera.attachControl(this.canvas, true);
    // this.thirdPersonCamera.attachControl(this.canvas, true);

    // Add keydown event listener to toggle between cameras when 'V' key is pressed
    document.addEventListener('keydown', (e) => {
      if (e.key === 'v' || e.key === 'V') {
        if (this.scene.activeCamera === this.firstPersonCamera) {
          // Switch to third-person view
          this.scene.activeCamera = this.thirdPersonCamera;
          // this.thirdPersonCamera.target = this.dummyBox;

          // Reset user adjustment flag when switching to third person
          this.userAdjustedCamera = false;

          // Update camera direction and radius to face enemy
          const directionParams = this.computeCameraDirectionParams();
          this.thirdPersonCamera.alpha = directionParams.alpha;
          this.thirdPersonCamera.beta = directionParams.beta;
          this.thirdPersonCamera.radius = directionParams.radius;
          this.thirdPersonCamera.attachControl(this.canvas, true);
          console.log('Switched to third-person view');


        } else {
          // Switch back to first-person view
          this.scene.activeCamera = this.firstPersonCamera;
          this.firstPersonCamera.attachControl(this.canvas, true);
          console.log('Switched to first-person view');
        }
      }
    });

    // Activate camera update observer
    this.cameraUpdateObserver = this.scene.onBeforeRenderObservable.add(() => {
      this.updateActiveCamera();
    });

    // Return the active camera
    return this.scene.activeCamera;
  }
  // 1) Add a method to update the active camera every frame
  updateActiveCamera() {
    const player = this.scene.getMeshByName("player");
    const enemy = this.scene.getMeshByName("enemy");
    if (!player || !enemy) return;

    if (this.scene.activeCamera === this.firstPersonCamera) {
      // Update first-person camera position
      this.firstPersonCamera.position = this.dummyBox.getAbsolutePosition();
      // Keep first-person camera targeted at enemy
      // const enemyHeadOffset = new Vector3(0, 1.0, 0);
      // this.firstPersonCamera.setTarget(enemy.getAbsolutePosition().add(enemyHeadOffset));

    } else if (this.scene.activeCamera === this.thirdPersonCamera && !this.userAdjustedCamera) {
      // Only update third-person camera if user hasn't manually adjusted it
      const directionParams = this.computeCameraDirectionParams();

      // Apply smooth interpolation to avoid sudden changes
      this.thirdPersonCamera.alpha = this.lerpAngle(
        this.thirdPersonCamera.alpha,
        directionParams.alpha,
        0.05
      );

      // Keep beta mostly unchanged or slightly adjust if needed
      this.thirdPersonCamera.beta = this.lerp(
        this.thirdPersonCamera.beta,
        directionParams.beta,
        0.02
      );

      // Update radius to maintain both characters in view
      this.thirdPersonCamera.radius = this.lerp(
        this.thirdPersonCamera.radius,
        directionParams.radius,
        0.03
      );
    }
  }


  // New method to compute only camera direction parameters
  computeCameraDirectionParams() {
    const player = this.scene.getMeshByName("player");
    const enemy = this.scene.getMeshByName("enemy");

    if (!player || !enemy) {
      return { alpha: Math.PI / -2, beta: 0.8, radius: 3 };
    }

    const playerPos = player.getAbsolutePosition();
    const enemyPos = enemy.getAbsolutePosition();

    // Compute the direction from player to enemy
    const direction = enemyPos.subtract(playerPos);

    // Calculate distance between characters for radius calculation
    const distanceBetweenCharacters = direction.length();

    // Calculate alpha angle (around Y-axis) to face the enemy
    // We add PI to face FROM the player's back TOWARDS the enemy
    const alpha = Math.atan2(direction.z, direction.x) + Math.PI + Math.PI / 6;

    // Beta angle (elevation) - keep a good default or compute based on height difference
    const heightDifference = enemyPos.y - playerPos.y;
    // Adjust beta slightly based on height difference
    const beta = 1.5 + (heightDifference);

    // Compute ideal radius based on distance between characters
    // This ensures both characters remain in view regardless of their separation
    // Formula: base radius + distance factor + height adjustment
    const baseRadius = 1; // Minimum radius
    const distanceFactor = distanceBetweenCharacters * 0.6; // Scale distance for proper framing
    const heightFactor = Math.abs(heightDifference) * 0.5; // Account for vertical separation
    const idealRadius = baseRadius + distanceFactor + heightFactor;

    // Clamp radius between reasonable values
    const minRadius = 2;
    const maxRadius = 12;
    const radius = Math.max(minRadius, Math.min(maxRadius, idealRadius));

    return { alpha, beta, radius };
  }

  // Utility method for linear interpolation
  lerp(start, end, factor) {
    return start + (end - start) * factor;
  }

  // Utility method for angle interpolation (handles angle wrapping)
  lerpAngle(start, end, factor) {
    let diff = (end - start) % (Math.PI * 2);

    if (diff > Math.PI) {
      diff -= Math.PI * 2;
    } else if (diff < -Math.PI) {
      diff += Math.PI * 2;
    }

    return start + diff * factor;
  }

  // Clean up resources when no longer needed
  dispose() {
    if (this.cameraUpdateObserver) {
      this.scene.onBeforeRenderObservable.remove(this.cameraUpdateObserver);
    }

    this.debugVisualizer.dispose();
  }
}