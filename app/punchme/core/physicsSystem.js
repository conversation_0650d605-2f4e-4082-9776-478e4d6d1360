import { Vector3, HavokPlugin, PhysicsAggregate, PhysicsShapeType, PhysicsCharacterController, PhysicsImpostor } from '@babylonjs/core';
import HavokPhysics from "@babylonjs/havok";

export class PhysicsSystem {
    constructor(scene) {
        this.scene = scene;
        this.isInitialized = false;
        this.initPromise = this.initEngine();
        // Add tracking of character colliders
        this.characterColliders = new Map();
    }

    async initEngine() {
        try {
            const gravity = new Vector3(0, -9.81, 0);
            // Load the Havok module (this returns a promise)
            const hkModule = await HavokPhysics();
            // Create HavokPlugin with the loaded module
            var physicsPlugin = new HavokPlugin(true, hkModule);
            this.scene.enablePhysics(gravity, physicsPlugin);
            this.scene.collisionsEnabled = true;

            // Setup ground physics if available
            const ground = this.scene.ground;
            if (ground) {
                ground.physicsAggregate = new PhysicsAggregate(
                    ground,
                    PhysicsShapeType.BOX,
                    {   
                        mass: 0,
                        friction: 0.5,
                    },
                    this.scene
                );
                ground.checkCollisions = true;
                ground.receiveShadows = true;
            }

            this.isInitialized = true;
            console.log("[PhysicsSystem] Physics engine initialized with HavokPlugin.");
            return true;
        } catch (err) {
            console.error("[PhysicsSystem] Failed to initialize Havok physics:", err);
            throw err;
        }
    }

    // Add a method to wait for physics initialization
    async waitForInit() {
        if (this.isInitialized) return true;
        return this.initPromise;
    }

    updatePhysics() {
        // Babylon.js handles the physics stepping automatically.
    }

    createCharacterController(mesh, options = {}) {
        let h = 1.8;
        let r = 0.6;
        const controller = new PhysicsCharacterController(
          mesh.position,
          {capsuleHeight: mesh.metadata.height, capsuleRadius: r},
          this.scene,
        );
        
        // this.characterControllers.set(mesh.uniqueId, controller);
        return controller;
      }
    // syncColliderWithCharacter(character) {
    //     if (!character || !character.metadata || !character.metadata.physicsRoot) {
    //         console.warn("[PhysicsSystem] Cannot sync collider: Invalid character or missing physicsRoot");
    //         return;
    //     }
    
    //     const physicsRoot = character.metadata.physicsRoot;
    //     const aggregate = this.characterColliders.get(physicsRoot.id);
        
    //     if (!aggregate) {
    //         console.warn(`[PhysicsSystem] No physics aggregate found for character: ${character.name}`);
    //         return;
    //     }
    
    //     // Get the character's current position, considering any animations
    //     const characterPos = character.position.clone();
        
    //     // For characters with skeletons, adjust collider position based on root bone
    //     if (character.skeleton) {
    //         const skeleton = character.skeleton;
    //         // Find the root bone or a key bone like pelvis/hips
    //         const rootBone = skeleton.bones.find(b => 
    //             b.name.toLowerCase().includes('root') || 
    //             b.name.toLowerCase().includes('pelvis') || 
    //             b.name.toLowerCase().includes('hips'));
                
    //         if (rootBone) {
    //             // Get absolute position of this bone in world space
    //             const bonePos = rootBone.getAbsoluteTransform().getTranslation();
    //             // Add bone offset to character position
    //             characterPos.y = bonePos.y;
    //         }
    //     }
        
    //     // Update physics body position
    //     aggregate.body.disablePreStep = true;
    //     aggregate.transformNode.position.copyFrom(characterPos);
    //     aggregate.body.setLinearVelocity(Vector3.Zero());
    //     aggregate.body.disablePreStep = false;
        
    //     return aggregate;
    // }
    dispose() {
        this.scene.disablePhysicsEngine();
        console.log("[PhysicsSystem] Physics engine disposed.");
    }
}