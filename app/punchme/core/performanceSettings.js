export const PerformanceSettings = {
    // FPS and timing
    targetFPS: 30,
    minFrameTime: 1000 / 30, // 33ms between frames
    drawInterval: 33,        // ~30fps for 2D visualization

    // Processing thresholds
    frameSkipThreshold: 50,  // Skip frame if processing takes longer

    // Performance monitoring
    processingTimeWindow: [], // Track recent processing times
    windowSize: 10,          // Number of frames to average
    logInterval: 1000,       // Log interval in ms

    // Feature flags
    useWorker: true,        // Use Web Workers for heavy computation
    debug: process.env.NODE_ENV !== 'production',

    // State tracking
    lastUpdateTime: 0,
    frameCount: 0,

    // 2D Visualization settings
    batchSize: 5,     // Process landmarks in batches
    maxPoints: 100,   // Maximum points to draw per frame

    // Throttling
    throttleInterval: 16,  // ~60fps max update rate
    lastThrottleTime: 0,

    // Performance monitoring
    performanceMetrics: {
        frameCount: 0,
        drawTime: 0,
        lastFPSUpdate: 0,
        fps: 0
    },

    // Methods
    shouldUpdate() {
        const now = performance.now();
        const shouldUpdate = now - this.lastThrottleTime > this.throttleInterval;
        if (shouldUpdate) {
            this.lastThrottleTime = now;
        }
        return shouldUpdate;
    },

    updateMetrics(processingTime) {
        this.processingTimeWindow.push(processingTime);
        if (this.processingTimeWindow.length > this.windowSize) {
            this.processingTimeWindow.shift();
        }

        this.frameCount++;

        // Log performance metrics periodically
        if (this.debug && this.frameCount % 30 === 0) {
            const avgTime = this.processingTimeWindow.reduce((a, b) => a + b, 0) / this.processingTimeWindow.length;
            console.log('[Performance] Metrics:', {
                fps: (1000 / avgTime).toFixed(1),
                avgProcessingTime: avgTime.toFixed(1) + 'ms',
                frameCount: this.frameCount
            });
        }
    }
};
