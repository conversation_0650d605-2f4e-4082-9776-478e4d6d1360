<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="xr-spatial-tracking" content="true">
    <link rel="stylesheet" href="../viewer/styles/viewer.css">
    <link rel="stylesheet" href="./punchme.css">
    <link rel="icon" href="/assets/icons/apple-icon-180.png">
    <link rel="manifest" href="../viewer/manifest.json">
    <title>Punch Me Viewer</title>
    <style>
        .start-game-button:hover {
            background-color: #f94653 !important;
            box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3) !important;
        }

        .start-game-button:active {
            transform: translate(-50%, -50%) scale(0.95) !important;
        }
    </style>
</head>

<body style="margin:0; padding:0; overflow:hidden;">
    <!-- 渲染画布必须直接在body下 -->
    <canvas id="renderCanvas"></canvas>

    <!-- 所有UI控件容器 -->
    <div id="ui-overlay" style="position:absolute; top:0; left:0; width:100%; height:100%; pointer-events:none;">
        <div id="debug-overlay" class="hidden">
            <div id="fps-counter"></div>
            <div id="scene-stats"></div>
        </div>

        <div id="error-display" class="hidden"></div>

        <div id="settings-panel" class="hidden">
            <h3>Punch Me Settings</h3>
            <!-- Settings will be dynamically populated -->
        </div>

        <!-- <div class="punch-controls">
            <button id="punchMode" class="punch-button">🥊 Punch Mode</button>
            <div class="punch-strength">
                <label for="strength">Punch Strength</label>
                <input type="range" id="strength" min="0.1" max="2.0" step="0.1" value="1.0">
            </div>
        </div> -->
    </div>

    <script type="module" src="./index.js"></script>
</body>

</html>