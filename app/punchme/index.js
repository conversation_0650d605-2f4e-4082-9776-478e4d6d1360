import { PunchViewer } from './punchViewer.js';
import { ASSETS } from '../viewer/viewerConfig.js';

let viewer;
let isGameRunning = false;

function togglePunchMe() {
    if (isGameRunning) {
        // Clean up existing game
        if (viewer) {
            viewer.dispose();
            viewer = null;
        }
        isGameRunning = false;
        document.getElementById('renderCanvas')?.remove();
        return;
    }

    // Create canvas
    const canvas = document.createElement('canvas');
    canvas.id = 'renderCanvas';
    canvas.style.width = '100%';
    canvas.style.height = '100%';
    canvas.style.position = 'fixed';
    canvas.style.top = '0';
    canvas.style.left = '0';
    canvas.style.zIndex = '1000';
    document.body.appendChild(canvas);

    // Initialize game
    initializeViewer().catch(console.error);
    isGameRunning = true;
}

async function initializeViewer() {
    console.log('Initializing Punch Viewer');
    try {
        // 确保渲染画布存在且大小正确
        const canvas = document.getElementById('renderCanvas');
        if (!canvas) {
            throw new Error('Canvas element not found');
        }

        // 设置画布尺寸
        function resizeCanvas() {
            canvas.width = window.innerWidth;
            canvas.height = window.innerHeight;
        }
        resizeCanvas();
        window.addEventListener('resize', resizeCanvas);

        // 初始化查看器
        const viewer = new PunchViewer("renderCanvas");

        window.addEventListener('beforeunload', () => {
            viewer.dispose();
        });

    } catch (error) {
        console.error('Failed to initialize viewer:', error);
        document.getElementById('error-display').textContent = error.message;
        document.getElementById('error-display').classList.remove('hidden');
    }
}

// Initialize the viewer when the DOM is ready
document.addEventListener('DOMContentLoaded', initializeViewer);

// Expose the toggle function globally
window.togglePunchMe = togglePunchMe;

// Export for module usage
export { viewer as punchViewer, togglePunchMe };