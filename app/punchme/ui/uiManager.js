// uiManager.js
import { CameraViewer } from '../../../src/ui/components/cameraViewer.js';
import { Inspector } from '@babylonjs/inspector';

class UIManager {
    constructor(app) {
        this.app = app;
        this.container = app.container;
        this.inputManager = app.inputManager;  // Store reference to InputManager

        // Add font fallback mechanism to handle Typekit loading failures
        this.setupFontFallbacks();

        this.setupCameraViewer();
        this.healthBars = new Map();
        this.setupHealthBars();
        this.activeLandmarkCanvas = null;  // 添加这行来跟踪当前活动的 canvas

        // State tracking
        this.state = {
            isCameraOn: false,
            wasLandmarkVisible: false,
            isProcessing: false,
            gameStarted: false
        };

        this.debugState = {
            inspectorEnabled: false,
            skeletonViewersEnabled: false
        };

        // Initialize debug visualizer with explicit canvas
        this.debugVisualizer = app.debugVisualizer;
        if (this.debugVisualizer) {
            this.debugVisualizer.enable();
        }
        console.log('[UIManager] Debug visualizer initialized:', {
            exists: !!this.debugVisualizer,
            enabled: this.debugVisualizer?.enabled
        });

        window.addEventListener('resize', () => this.updateScale());
        this.updateScale();
        this.setupUI();
        this.setupStartGameButton(); // Add this line to setup the Start Game button
        this.initializeLoadingPanel();
        this.initializeNotificationPanel();
        this.skeletonViewers = new Map(); // Add skeleton viewers map
    }

    // Add method to handle font loading issues
    setupFontFallbacks() {
        // Add a style element with fallback fonts in case Typekit fonts fail to load
        const style = document.createElement('style');
        style.textContent = `
            /* Fallback font definitions */
            body, button, div {
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif !important;
            }
        `;

        // Handle Typekit font loading error
        const fontLink = document.querySelector('link[href*="typekit"]');
        if (fontLink) {
            fontLink.addEventListener('error', () => {
                console.warn('[UIManager] Typekit font loading failed, using fallbacks');
                document.head.appendChild(style);
            });
        } else {
            // If there's no Typekit link yet or it was already attempted, just add the fallbacks
            document.head.appendChild(style);
        }
    }

    updateScale() {
        const pixelRatio = window.devicePixelRatio || 1;
        this.baseScale = 1 / pixelRatio;
    }

    setupHealthBars() {
        const healthBarContainer = document.createElement('div');
        healthBarContainer.id = 'health-bars-container';
        this.container.appendChild(healthBarContainer);

        const characterInfo = {
            player: {
                name: 'PLAYER',
                avatar: '/assets/images/player-avatar-male.png'
            },
            enemy: {
                name: 'ENEMY',
                avatar: '/assets/images/enemy-avatar.png'
            }
        };

        ['player', 'enemy'].forEach(role => {
            const healthBar = document.createElement('div');
            healthBar.className = `character-health-bar ${role}-health`;

            // 添加头像和名称容器
            const characterHeader = document.createElement('div');
            characterHeader.className = 'character-header';

            // 添加头像
            const avatar = document.createElement('div');
            avatar.className = 'character-avatar';
            avatar.style.backgroundImage = `url(${characterInfo[role].avatar})`;

            // 添加名称
            const name = document.createElement('div');
            name.className = 'character-name';
            name.textContent = characterInfo[role].name;

            // 血条容器
            const barContainer = document.createElement('div');
            barContainer.className = 'health-bar-container';

            // 血条填充
            const healthFill = document.createElement('div');
            healthFill.className = 'health-fill';

            // 血量数值
            const healthText = document.createElement('div');
            healthText.className = 'health-text';
            healthText.textContent = '100/100';

            // 组装 DOM
            characterHeader.appendChild(avatar);
            characterHeader.appendChild(name);
            barContainer.appendChild(healthFill);
            barContainer.appendChild(healthText);
            healthBar.appendChild(characterHeader);
            healthBar.appendChild(barContainer);

            healthBarContainer.appendChild(healthBar);

            this.healthBars.set(role, {
                container: healthBar,
                fill: healthFill,
                text: healthText,
            });
        });
    }

    async setupCameraViewer() {
        console.log('[UIManager] Setting up camera viewer');

        if (this.cameraViewer) {
            this.cameraViewer.dispose();
            this.cameraViewer = null;
        }

        // Verify that we have access to inputManager and its video element
        if (!this.app?.inputManager?.videoElement) {
            console.error('[UIManager] Cannot setup camera viewer: No video element available');
            return;
        }

        this.cameraViewer = new CameraViewer(this.app, {
            width: 640,
            height: 480,
            preferPopup: false, // Use embedded corner mode instead of popup
            modalConfig: {
                maxWidth: '90vw',
                maxHeight: '90vh',
                minWidth: '320px',
                minHeight: '180px'
            }
        });

        const canvas = await this.cameraViewer.initialize(this.app.inputManager.videoElement);

        // Ensure debugVisualizer gets properly sized canvas
        if (this.debugVisualizer && canvas) {
            await this.debugVisualizer.initialize(canvas);
            console.log('[UIManager] Debug visualizer initialized with canvas:', {
                width: canvas.width,
                height: canvas.height
            });
        }

        this.setupCameraCallbacks();

        // Update camera button state based on device availability
        if (this.buttons?.cameraButton) {
            // Check if camera is available
            const hasCamera = !!navigator.mediaDevices;
            this.buttons.cameraButton.classList.toggle('disabled', !hasCamera);
            this.buttons.cameraButton.title = hasCamera ? 'Open camera view' : 'No camera available';

            // Update click handler to use centralized camera system
            this.buttons.cameraButton.addEventListener('click', async () => {
                console.log('[UIManager] Camera button clicked (from setupCameraViewer)');
                await this.handleCameraButtonClick();
            });
        }
    }

    /**
     * Handle camera button click using centralized camera system with fallback
     */
    async handleCameraButtonClick() {
        try {
            // Try to use centralized camera system first
            const { CameraManager } = await import('../../../src/ui/components/CameraManager.js');

            // Create or get existing camera manager instance
            if (!this.CameraManager) {
                this.CameraManager = new CameraManager();
                console.log('[UIManager] Created new centralized camera manager');
            }

            // Start camera and show in popup mode
            await this.CameraManager.startCamera();
            await this.CameraManager.showCamera('popup');

            console.log('[UIManager] Camera opened using centralized system');

        } catch (error) {
            console.error('[UIManager] Centralized camera failed, falling back to legacy:', error);

            // Fallback to legacy camera system
            if (this.cameraViewer) {
                this.cameraViewer.openPopupWindow();
            } else {
                console.error('[UIManager] No camera system available');
            }
        }
    }

    setupCameraCallbacks() {
        if (!this.cameraViewer) return;

        this.cameraViewer.setViewerCallbacks(
            async (canvas) => {
                console.log('[UIManager] Camera opened');
                if (canvas) {
                    // Simply enable visualization when camera opens
                    this.debugVisualizer?.enable();
                    this.startLandmarkVisualization();
                }
                if (this.onCameraToggle) {
                    await this.onCameraToggle(true);
                }
            },
            () => {
                console.log('[UIManager] Camera closing, but leaving pose detection active');
                // Keep MediaPipe detection running in the background
                this.stopLandmarkVisualization();
                if (this.debugVisualizer) {
                    this.debugVisualizer.disable();
                }
                if (this.onCameraToggle) {
                    this.onCameraToggle(false);
                }
            }
        );
    }

    setupUI() {
        // Create camera button with passive touch listener
        const cameraButton = document.createElement('button');
        cameraButton.className = 'camera-button';
        cameraButton.innerHTML = `
            <svg viewBox="0 0 24 24" width="18" height="18">
                <path fill="currentColor" d="M12 15.2a3.2 3.2 0 100-6.4 3.2 3.2 0 000 6.4z"/>
                <path fill="currentColor" d="M20 4h-3.17L15 2H9L7.17 4H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm-8 13c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5z"/>
            </svg>
            <span>Camera</span>
        `;

        // Create inspector button
        const inspectorButton = document.createElement('button');
        inspectorButton.className = 'inspector-button';
        inspectorButton.innerHTML = `
            <svg viewBox="0 0 24 24" width="18" height="18">
                <path fill="currentColor" d="M12 8c-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4-1.79-4-4-4zm-7 7H3v4c0 1.1.9 2 2 2h4v-2H5v-4zM5 5h4V3H5c-1.1 0-2 .9-2 2v4h2V5zm14-2h-4v2h4v4h2V5c0-1.1-.9-2-2-2zm0 16h-4v2h4c1.1 0 2-.9 2-2v-4h-2v4z"/>
            </svg>
            <span>Inspector</span>
        `;

        // Add buttons to container
        this.container.appendChild(cameraButton);
        this.container.appendChild(inspectorButton);

        // Setup button handlers with passive touch events
        cameraButton.addEventListener('touchstart', async () => {
            console.log('[UIManager] Camera button touched');
            await this.handleCameraButtonClick();
        }, { passive: true });

        cameraButton.addEventListener('click', async () => {
            console.log('[UIManager] Camera button clicked');
            await this.handleCameraButtonClick();
        });

        // Updated inspector button click handler with additional logging
        inspectorButton.addEventListener('click', async (event) => {
            console.log('[UIManager] Inspector button clicked - event captured');
            event.preventDefault();

            if (!this.app.scene) {
                console.warn('[UIManager] No scene available for inspector');
                return;
            }

            try {
                await this.toggleInspector(this.app.scene);
                console.log('[UIManager] Inspector toggled successfully');
            } catch (error) {
                console.error('[UIManager] Error toggling inspector:', error);
            }
        });

        // Ensure button is visible and clickable
        inspectorButton.style.pointerEvents = 'auto';
        console.log('[UIManager] Inspector button initialized:', inspectorButton);

        // Store button references
        this.buttons = { cameraButton, inspectorButton };
    }

    // Add this new method to create and handle the start game button
    setupStartGameButton() {
        // Create start game button
        const startGameButton = document.createElement('button');
        startGameButton.id = 'start-game-button';
        startGameButton.className = 'start-game-button';
        startGameButton.innerHTML = `
            <span>Start Game</span>
        `;
        startGameButton.style.cssText = `
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: #e63946;
            color: white;
            padding: 15px 30px;
            font-size: 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
            pointer-events: auto;
        `;

        // Add hover effects
        startGameButton.addEventListener('mouseover', () => {
            startGameButton.style.background = '#f94653';
            startGameButton.style.transform = 'translate(-50%, -50%) scale(1.05)';
        });

        startGameButton.addEventListener('mouseout', () => {
            startGameButton.style.background = '#e63946';
            startGameButton.style.transform = 'translate(-50%, -50%)';
        });

        // Add click handler
        startGameButton.addEventListener('click', () => this.handleStartGame());

        // Add to container
        this.container.appendChild(startGameButton);

        // Store reference to button
        this.buttons = {
            ...this.buttons,
            startGameButton
        };
    }

    // Add this method to handle the start game button click
    handleStartGame() {
        if (this.state.gameStarted) return;

        console.log('[UIManager] Starting game');
        this.state.gameStarted = true;

        // Hide the start button
        if (this.buttons.startGameButton) {
            this.buttons.startGameButton.style.display = 'none';
        }

        // Show notification
        this.showNotification('Game Starting!', 3000);

        // If we have a reference to the game state manager, start the game
        if (this.app.gameStateManager) {
            this.app.gameStateManager.startGame();
        }
    }

    updateHealth(role, health, maxHealth = 100) {
        const healthBar = this.healthBars.get(role);
        if (!healthBar) return;

        const percent = Math.max(0, Math.min(health, maxHealth)) / maxHealth * 100;
        // 设置填充宽度
        healthBar.fill.style.width = `${percent}%`;
        // 更新血量文本
        healthBar.text.textContent = `${Math.round(health)}/${maxHealth}`;

        // 更新血条状态类
        healthBar.fill.classList.remove('high', 'medium', 'low');
        if (percent > 60) {
            healthBar.fill.classList.add('high');
        } else if (percent > 30) {
            healthBar.fill.classList.add('medium');
        } else {
            healthBar.fill.classList.add('low');
        }

        // 添加受伤闪烁效果
        healthBar.container.classList.remove('damage-flash');
        void healthBar.container.offsetWidth;
        healthBar.container.classList.add('damage-flash');
    }

    setOnCameraToggle(callback) {
        console.log('[UIManager] Registering camera toggle callback');
        this.onCameraToggle = callback;
    }



    startLandmarkVisualization() {
        if (!this.state.wasLandmarkVisible) {
            this.state.wasLandmarkVisible = true;
            console.log('[UIManager] Landmark visualization started');

            // Ensure debug visualizer is enabled
            if (this.debugVisualizer) {
                this.debugVisualizer.enable();
            }
        }
    }

    stopLandmarkVisualization() {
        this.state.wasLandmarkVisible = false;
        console.log('[UIManager] Landmark visualization stopped');
    }

    showError(message) {
        console.error('[UIManager] Error:', message);
        // Add basic error display
        const errorDiv = document.createElement('div');
        errorDiv.style.cssText = `
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(255, 0, 0, 0.8);
            color: white;
            padding: 10px 20px;
            border-radius: 5px;
            z-index: 1000;
        `;
        errorDiv.textContent = message;
        document.body.appendChild(errorDiv);
        setTimeout(() => errorDiv.remove(), 5000);
    }

    dispose() {
        if (this.cameraViewer) {
            this.cameraViewer.dispose();
            this.cameraViewer = null;
        }
        // ...existing disposal code...
    }

    closeViewer() {
        // Ensure proper cleanup order
        if (this.debugVisualizer) {
            this.debugVisualizer.disable();
        }

        // Other cleanup
        // ...existing code...
    }

    async loadInspector(scene) {
        if (!this.debugState.inspectorEnabled) {
            console.log('[UIManager] Loading inspector...');
            try {
                // Ensure debug layer is created
                if (!scene.debugLayer) {
                    console.error('[UIManager] No debug layer available');
                    return;
                }

                // Show inspector first
                Inspector.Show(scene, {
                    embedMode: true,
                    overlay: true,
                    showInspector: true,
                    handleResize: true,
                    globalRoot: document.body
                });

                // Update UI state
                this.debugState.inspectorEnabled = true;
                this.buttons.inspectorButton.classList.add('active');

                // Add observer for inspector closing
                const observer = new MutationObserver((mutations) => {
                    // Check if inspector panel is still in the DOM
                    const inspectorExists = document.querySelector('.inspector-canvas-wrapper') ||
                        document.querySelector('.inspector-overlay');

                    if (!inspectorExists) {
                        console.log('[UIManager] Inspector panel was closed externally');
                        // Inspector was closed, update state
                        this.debugState.inspectorEnabled = false;
                        this.buttons.inspectorButton.classList.remove('active');
                        observer.disconnect();
                    }
                });

                // Observe the body for any changes to detect inspector removal
                observer.observe(document.body, {
                    childList: true,
                    subtree: true
                });

                console.log('[UIManager] Inspector loaded successfully');
            } catch (error) {
                console.error('[UIManager] Failed to load inspector:', error);
                this.debugState.inspectorEnabled = false;
                this.buttons.inspectorButton.classList.remove('active');
                throw error;
            }
        }
    }

    unloadInspector(scene) {
        if (this.debugState.inspectorEnabled && scene.debugLayer) {
            try {
                // First disable skeleton viewers
                // this.toggleSkeletonViewers(false);

                Inspector.Hide();

                this.debugState.inspectorEnabled = false;
                this.buttons.inspectorButton.classList.remove('active');
                console.log('[UIManager] Inspector unloaded');
            } catch (error) {
                console.error('[UIManager] Error unloading inspector:', error);
                throw error;
            }
        }
    }

    async toggleInspector(scene) {
        if (!scene) {
            console.warn('[UIManager] Cannot toggle Inspector: No scene available');
            return;
        }

        console.log('[UIManager] Toggling inspector. Current state:', this.debugState.inspectorEnabled);

        try {
            // Ensure scene is ready
            // if (!scene.isReady()) {
            //     await scene.whenReadyAsync();
            // }

            if (this.debugState.inspectorEnabled) {
                await this.unloadInspector(scene);
            } else {
                await this.loadInspector(scene);
            }

            // Force shader recompilation
            // scene.forceWireframe = true;
            // scene.forceWireframe = false;

            console.log('[UIManager] Inspector toggled successfully. New state:', this.debugState.inspectorEnabled);
        } catch (error) {
            console.error('[UIManager] Error toggling inspector:', error);
            // Reset state in case of error
            this.debugState.inspectorEnabled = false;
            this.buttons.inspectorButton.classList.remove('active');
            throw error;
        }
    }

    setDebugMode(enabled) {
        console.log('[UIManager] Setting debug mode:', enabled);

        // Update debug state
        this.debugState = {
            ...this.debugState,
            debugEnabled: enabled
        };

        // Update Inspector button state
        if (this.buttons?.inspectorButton) {
            this.buttons.inspectorButton.classList.toggle('active', enabled);
        }

        // Update debug visualizer if available
        if (this.debugVisualizer) {
            if (enabled) {
                this.debugVisualizer.enable();
            } else {
                this.debugVisualizer.disable();
            }
        }

        // Update camera viewer if available
        if (this.cameraViewer) {
            this.cameraViewer.setDebugOverlay(enabled);
        }

        console.log('[UIManager] Debug mode set:', {
            enabled,
            visualizer: this.debugVisualizer?.enabled,
            inspector: this.debugState.inspectorEnabled
        });
    }
    initializeLoadingPanel() {
        // Create loading panel
        this.loadingPanel = document.createElement('div');
        this.loadingPanel.className = 'loading-panel';
        this.loadingPanel.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 20px 30px;
            border-radius: 8px;
            font-size: 16px;
            z-index: 1000;
            display: none;
            text-align: center;
            min-width: 300px;
        `;

        // Add loading spinner
        const spinner = document.createElement('div');
        spinner.className = 'loading-spinner';
        spinner.style.cssText = `
            border: 3px solid #f3f3f3;
            border-top: 3px solid #3498db;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            animation: spin 1s linear infinite;
            margin: 10px auto;
        `;

        // Add loading text
        const text = document.createElement('div');
        text.textContent = 'Loading landmark detection models...';
        text.style.marginTop = '10px';

        // Add progress text
        this.loadingProgress = document.createElement('div');
        this.loadingProgress.style.fontSize = '14px';
        this.loadingProgress.style.marginTop = '5px';
        this.loadingProgress.style.color = '#aaa';

        // Assemble panel
        this.loadingPanel.appendChild(spinner);
        this.loadingPanel.appendChild(text);
        this.loadingPanel.appendChild(this.loadingProgress);
        document.body.appendChild(this.loadingPanel);

        // Add keyframe animation
        const style = document.createElement('style');
        style.textContent = `
            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }
        `;
        document.head.appendChild(style);
    }

    showLoadingPanel(message = '') {
        this.loadingPanel.style.display = 'block';
        if (message) {
            this.loadingProgress.textContent = message;
        }
    }

    hideLoadingPanel() {
        this.loadingPanel.style.display = 'none';
        this.loadingProgress.textContent = '';
    }

    updateLoadingProgress(message) {
        if (this.loadingProgress) {
            this.loadingProgress.textContent = message;
        }
    }

    initializeNotificationPanel() {
        // Create notification panel with centered position
        this.notificationPanel = document.createElement('div');
        this.notificationPanel.className = 'notification-panel';
        this.notificationPanel.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 10px 20px;
            border-radius: 5px;
            z-index: 1000;
            opacity: 0;
            transition: opacity 0.3s ease;
            pointer-events: none;
        `;
        document.body.appendChild(this.notificationPanel);
    }

    showNotification(message, duration = 2000) {
        this.notificationPanel.textContent = message;
        this.notificationPanel.style.opacity = '1';

        setTimeout(() => {
            this.notificationPanel.style.opacity = '0';
        }, duration);
    }
}

export default UIManager;