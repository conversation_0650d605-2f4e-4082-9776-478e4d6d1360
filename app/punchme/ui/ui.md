
# UIManager

The `UIManager` is responsible for rendering and updating the user interface elements such as health bars, scores, and game prompts. It handles DOM operations and ensures that the UI is updated independently of the game logic.

## Responsibilities
- Display and update health bars for player and enemy.
- Handle camera viewer setup and callbacks.
- Draw landmarks on the canvas for pose visualization.

## Methods
- `updateScale()`: Adjusts the scale based on the device pixel ratio.
- `setupHealthBars()`: Initializes and sets up the health bars for player and enemy.
- `drawLandmarks(landmarks)`: Draws pose landmarks on the canvas.
- `setupCameraViewer()`: Sets up the camera viewer and handles its callbacks.
- `updateHealth(role, health, maxHealth)`: Updates the health bar for the specified role.
- `setOnCameraToggle(callback)`: Registers a callback for camera toggle events.
- `startLandmarkVisualization()`: Enables landmark visualization.
- `stopLandmarkVisualization()`: Disables landmark visualization.

# DebugVisualizer

The `DebugVisualizer` is used during development to draw pose keypoints and skeleton connections. It helps in verifying detection data and IK results.

## Responsibilities
- Visualize pose keypoints and connections in the 3D scene.
- Update the visualization based on the detected landmarks.

## Methods
- `initializeMaterials()`: Initializes materials for debug points and lines.
- `updateVisualization(landmarks, parentMesh)`: Updates the debug visualization with the given landmarks.
- `updateDebugPoint(index, position, parent)`: Updates or creates a debug point at the specified position.
- `updateDebugLine(key, points, parent)`: Updates or creates a debug line between the specified points.
- `hideDebugPoint(index)`: Hides the debug point with the given index.
- `hideDebugLine(key)`: Hides the debug line with the given key.
- `dispose()`: Disposes of all debug points, lines, and materials.
