// import { Canvas2DVisualizer } from './visualizers/canvas2DVisualizer.js';
// import { Scene3DVisualizer } from './visualizers/scene3DVisualizer.js';
import { Canvas2DVisualizer } from "../../../src/ui/components/visualizers/canvas2DVisualizer.js";
class DebugVisualizer {
    constructor(scene, debug = false) {
        if (!scene) {
            throw new Error('[DebugVisualizer] Scene is required');
        }

        this.scene = scene;
        this.enabled = false;
        this.lineMap = new Map();
        this.materials = {};

        // Create visualizers with enabled state
        this.canvas2DVisualizer = new Canvas2DVisualizer(false);
        // this.scene3DVisualizer = new Scene3DVisualizer(scene, false);

        // Add initialization state tracking
        this.pendingVisualizations = [];

        // Add visualization state tracking
        this.visualizationState = {
            pending: [],
            processing: false,
            lastProcessTime: 0
        };

        // Add visualization batch management
        this.visualizationQueue = {
            items: [],
            processing: false,
            lastProcessTime: 0,
            batchSize: 0,
            maxBatchSize: 10,
            processInterval: 1000 / 30 // 30fps cap
        };

        // Track initialization status
        this.readyState = {
            initialized: false,
            canvas2D: false,
            scene3D: false
        };

        console.log('[DebugVisualizer] Created with:', {
            debug,
            hasScene: !!scene,
            has2D: !!this.canvas2DVisualizer,
            has3D: !!this.scene3DVisualizer,
        });
    }

    async initialize(canvas) {
        try {
            console.log('[DebugVisualizer] Initializing visualizers with canvas:', !!canvas);
            if (!canvas) {
                throw new Error('Canvas is required for initialization');
            }

            // Set up Canvas2D first
            this.canvas2DVisualizer.setCanvas(canvas);
            this.readyState.canvas2D = true;
            console.log('[DebugVisualizer] Canvas2D initialized');

            // Wait for character to be loaded if Scene3D exists
            // if (this.scene3DVisualizer) {
            //     await this.scene3DVisualizer.waitForCharacterLoading();
            //     this.readyState.scene3D = true;
            //     console.log('[DebugVisualizer] Scene3D initialized');
            // }

            // Mark as initialized only if both systems are ready
            this.initialized = true;
            this.readyState.initialized = true;

            console.log('[DebugVisualizer] Initialization complete:', this.readyState);
            return true;
        } catch (error) {
            console.error('[DebugVisualizer] Initialization failed:', error);
            this.readyState.initialized = false;
            throw error;
        }
    }

    isReady() {
        const ready = this.initialized &&
            this.enabled &&
            this.canvas2DVisualizer?.readyState?.initialized;

        // console.log('[DebugVisualizer] Checking readiness:', {
        //     initialized: this.initialized,
        //     enabled: this.enabled,
        //     canvas2D: this.canvas2DVisualizer?.readyState,
        //     ready
        // });

        return ready;
    }

    visualizeIK(chainName, points) {
        if (!this.enabled || !points) return;

        // Delegate all visualization to Scene3DVisualizer
        if (this.scene3DVisualizer) {
            if (typeof chainName === 'object') {
                this.scene3DVisualizer.visualizeIK(chainName);
            } else {
                this.scene3DVisualizer.visualizeIK({ [chainName]: points });
            }
        }
    }

    visualizePoseResults(results) {
        if (!results?.landmarks?.[0]) {
            return;
        }
        if (!this.enabled) {
            this.enable();
        }

        if (!this.canvas2DVisualizer?.isReady()) {
            console.debug('[DebugVisualizer] Canvas not ready:', {
                enabled: this.enabled,
                ready: this.canvas2DVisualizer?.isReady(),
                hasCanvas: !!this.canvas2DVisualizer?.canvas
            });
            return;
        }

        try {
            this.canvas2DVisualizer.drawResults(results);
        } catch (error) {
            console.error('[DebugVisualizer] Visualization error:', error);
        }
    }

    enable() {
        // Only enable if we have proper initialization
        if (!this.initialized) {
            console.log('[DebugVisualizer] Cannot enable: Not initialized');
            return;
        }

        if (this.enabled) {
            console.log('[DebugVisualizer] Already enabled');
            return;
        }

        console.log('[DebugVisualizer] Enabling visualizers');
        this.enabled = true;

        // Enable Canvas2D visualizer if it's ready
        if (this.canvas2DVisualizer && this.canvas2DVisualizer.canvas) {
            this.canvas2DVisualizer.enable();
            this.readyState.canvas2D = this.canvas2DVisualizer.isReady();
        }

        // Enable Scene3D visualizer
        if (this.scene3DVisualizer) {
            this.scene3DVisualizer.enable();
            this.readyState.scene3D = true;
        }

        console.log('[DebugVisualizer] Enable complete, state:', {
            enabled: this.enabled,
            canvas2DReady: this.readyState.canvas2D,
            scene3DReady: this.readyState.scene3D
        });
    }

    disable() {
        if (!this.enabled) {
            console.log('[DebugVisualizer] Already disabled');
            return;
        }

        console.log('[DebugVisualizer] Disabling visualizers');
        this.enabled = false;

        // Disable both visualizers
        if (this.canvas2DVisualizer) {
            this.canvas2DVisualizer.disable();
            this.readyState.canvas2D = false;
        }

        if (this.scene3DVisualizer) {
            this.scene3DVisualizer.disable();
            this.readyState.scene3D = false;
        }

        console.log('[DebugVisualizer] Disabled');
    }

    clearVisualizations() {
        this.lineMap.forEach(mesh => mesh.dispose());
        this.lineMap.clear();

        [
            this.canvas2DVisualizer,
            this.scene3DVisualizer,
        ].forEach(visualizer => {
            if (visualizer?.dispose) {
                visualizer.dispose();
            }
        });
    }

    getActiveCanvas() {
        return this.activeCanvas || null;
    }

    dispose() {
        this.clearVisualizations();
        Object.values(this.materials).forEach(material => material.dispose());
    }
}

export default DebugVisualizer;
