class CombatSystem {
    constructor(scene, effectsManager, punchViewer) {
        this.scene = scene;
        this.effectsManager = effectsManager;
        this.lastHitTime = 0;
        this.punchViewer = punchViewer;
    }

    handleCollision(collider, collidedAgainst) {
        if (collider.object.metadata?.isHand) {
            const hitPosition = collider.object.position;
            const { type, side } = collider.object.metadata;

            if (type === 'punch') {
                this.handlePunchHit(hitPosition, side, collidedAgainst);
            } else if (type === 'block') {
                this.handleBlockHit(hitPosition, side);
            }
        }
    }

    handlePunchHit(hitPosition, side, collidedWith) {
        const now = Date.now();
        if (now - this.lastHitTime > 1000) {
            // Trigger punch effects
            this.effectsManager.createPunchImpact(hitPosition);
            this.lastHitTime = now;
            this.punchViewer.takeDamage(15);

            // Apply impulse to the collided object
            const hitDirection = collidedWith.position.subtract(hitPosition).normalize();
            collidedWith.applyImpulse(
                hitDirection.scale(20),
                hitPosition
            );
        }
    }

    handleBlockHit(hitPosition, side) {
        const now = Date.now();
        if (now - this.lastHitTime > 500) {
            this.punchViewer.takeDamage(5);
            this.lastHitTime = now;
            // Different effects for blocked hits
            // if (this.punchEffects) {
            //     this.punchEffects.createBlockImpact(hitPosition);
            // }
        }
    }
}

export default CombatSystem;
