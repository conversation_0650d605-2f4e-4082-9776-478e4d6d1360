import {
    Vector3, TransformNode, MeshBuilder, StandardMaterial,
    PhysicsImpostor, Color3, ParticleSystem, Texture,
    Animation, Scalar, Color4, DynamicTexture // 添加 DynamicTexture 导入
} from '@babylonjs/core';

export class PunchEffectsManager {
    constructor(scene) {
        this.scene = scene;
        this.particleSystems = new Map();
        this.colliders = new Map();
        this.hitboxes = new Map();

        // Create a default particle system first, then try to load texture
        this.createDefaultParticleSystem();
    }

    initParticleSystems() {
        try {
            // Instead of loading external texture, create a default one
            const defaultTexture = this.createDefaultTexture();
            const system = this.createParticleSystem(defaultTexture);
            this.particleSystems.set('default', system);
            console.log('[PunchEffects] Particle system initialized with default texture');
        } catch (error) {
            console.error('[PunchEffects] Error initializing particle systems:', error);
            this.createDefaultParticleSystem();
        }
    }

    createDefaultTexture() {
        const texture = new DynamicTexture("particleTexture", 32, this.scene, true);
        const ctx = texture.getContext();

        // Draw a simple circular particle
        ctx.beginPath();
        ctx.arc(16, 16, 8, 0, Math.PI * 2);
        ctx.fillStyle = 'white';
        ctx.fill();

        texture.update();
        return texture;
    }

    createDefaultParticleSystem() {
        // 创建一个简单的默认粒子系统
        const system = new ParticleSystem("default", 2000, this.scene);

        // 使用默认渲染器和颜色
        system.createPointEmitter(new Vector3(0.1, 0.1, 0.1), new Vector3(-0.1, -0.1, -0.1));

        // 基本粒子属性
        system.color1 = new Color4(1, 0.5, 0, 1);
        system.color2 = new Color4(1, 0.2, 0, 1);
        system.colorDead = new Color4(0, 0, 0, 0);

        system.minSize = 0.1;
        system.maxSize = 0.5;
        system.minEmitPower = 1;
        system.maxEmitPower = 3;
        system.updateSpeed = 0.02;
        system.minLifeTime = 0.2;
        system.maxLifeTime = 0.4;
        system.emitRate = 50;

        system.gravity = new Vector3(0, -9.81, 0);

        // 停止并存储系统
        system.stop();
        this.particleSystems.set('default', system);

        console.log('[PunchEffects] Default particle system created');
        return system;
    }

    createParticleSystem(texture) {
        // Impact particle system
        const impactSystem = new ParticleSystem("impactParticles", 100, this.scene);
        impactSystem.particleTexture = texture;

        // General properties
        impactSystem.minEmitBox = new Vector3(-0.1, -0.1, -0.1);
        impactSystem.maxEmitBox = new Vector3(0.1, 0.1, 0.1);

        // Particles properties
        impactSystem.color1 = new Color4(1, 0.5, 0, 1.0);
        impactSystem.color2 = new Color4(1, 0.2, 0, 1.0);
        impactSystem.colorDead = new Color4(0, 0, 0, 0.0);

        impactSystem.minSize = 0.1;
        impactSystem.maxSize = 0.5;

        // Emission properties
        impactSystem.emitRate = 100;
        impactSystem.minEmitPower = 1;
        impactSystem.maxEmitPower = 3;
        impactSystem.updateSpeed = 0.01;

        // Lifetime
        impactSystem.minLifeTime = 0.1;
        impactSystem.maxLifeTime = 0.3;

        // Emission cone
        impactSystem.createSphericEmitter(0.5);

        // Gravity
        impactSystem.gravity = new Vector3(0, -9.81, 0);

        // Store the system
        this.particleSystems.set('impact', impactSystem);

        // Ensure it's stopped initially
        impactSystem.stop();

        console.log('[PunchEffects] Particle system created successfully');
        return impactSystem;
    }

    setupParticleSystemProperties(system) {
        // General properties
        system.minEmitBox = new Vector3(-0.1, -0.1, -0.1);
        system.maxEmitBox = new Vector3(0.1, 0.1, 0.1);

        // Particles properties
        system.color1 = new Color4(1, 1, 1, 1.0);
        system.color2 = new Color4(1, 0.8, 0.8, 1.0);
        system.colorDead = new Color4(0, 0, 0, 0.0);

        system.minSize = 0.1;
        system.maxSize = 0.3;

        // Emission properties
        system.emitRate = 50;
        system.minEmitPower = 0.5;
        system.maxEmitPower = 1.5;
        system.updateSpeed = 0.02;

        // Lifetime
        system.minLifeTime = 0.2;
        system.maxLifeTime = 0.4;

        // Simple spherical emission
        system.createSphericEmitter(0.2);

        // Stop initially
        system.stop();
    }

    createPunchImpact(position) {
        const impactSystem = this.particleSystems.get('impact');
        if (!impactSystem) return;

        impactSystem.emitter = position;
        impactSystem.start();

        // Auto-stop after duration
        setTimeout(() => {
            impactSystem.stop();
        }, 200);
    }

    setupCharacterColliders(character, role) {
        // Create capsule collider
        const capsule = MeshBuilder.CreateCapsule(
            `${role}Capsule`,
            {
                height: 2,
                radius: 0.5,
            },
            this.scene
        );

        capsule.isVisible = false;
        capsule.parent = character;

        // Add physics impostor
        capsule.physicsImpostor = new PhysicsImpostor(
            capsule,
            PhysicsImpostor.CapsuleImpostor,
            {
                mass: role === 'enemy' ? 10 : 0,
                friction: 0.5,
                restitution: 0.3
            },
            this.scene
        );

        this.colliders.set(role, capsule);

        // Setup hitboxes
        this.setupHitboxes(character, role);
    }

    setupHitboxes(character, role) {
        const hitbox = MeshBuilder.CreateBox(
            `${role}Hitbox`,
            { width: 0.4, height: 0.4, depth: 0.8 },
            this.scene
        );

        const hitboxMat = new StandardMaterial("hitboxMat", this.scene);
        hitboxMat.diffuseColor = role === 'enemy' ? new Color3(1, 0, 0) : new Color3(0, 1, 0);
        hitboxMat.alpha = 0.3;

        hitbox.material = hitboxMat;
        hitbox.parent = character;
        hitbox.isVisible = false; // Toggle for debug

        // Add physics impostor for collision detection
        hitbox.physicsImpostor = new PhysicsImpostor(
            hitbox,
            PhysicsImpostor.BoxImpostor,
            { mass: 0, isTrigger: true },
            this.scene
        );

        this.hitboxes.set(role, hitbox);

        // Set up collision callbacks
    }

    handleHitboxCollision(attackerRole, collider, collidedWith) {
        const hitPosition = collider.object.getAbsolutePosition();
        this.createPunchImpact(hitPosition);

        // Calculate and apply impact force
        const hitDirection = collidedWith.object.position.subtract(hitPosition).normalize();
        collidedWith.applyImpulse(
            hitDirection.scale(20),
            collidedWith.object.getAbsolutePosition()
        );

        // Trigger hit reaction animation if available
        if (this.onHitCallback) {
            this.onHitCallback(attackerRole, hitPosition);
        }
    }

    // Method to register hit callback
    onHit(callback) {
        this.onHitCallback = callback;
    }
}
