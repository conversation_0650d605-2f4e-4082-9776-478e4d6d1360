import { SceneManager } from './core/index.js';
import '@babylonjs/loaders/glTF';
import { InputManager, PoseDetector, DetectionMode } from './input/index.js';
import CombatSystem from './combact/combatSystem.js';
import { PunchEffectsManager } from './combact/punchEffects.js';
import { UIManager, DebugVisualizer } from './ui/index.js';
import {
  GameStateManager,
  // IKSolver
} from './characters/index.js';
import { VISIBILITY_THRESHOLD } from './configs/MediapipeConfig.js';
import { ensureMathExtensions } from './utils/mathExtensions.js';

export class PunchViewer {
  constructor(canvasId) {
    // Initialize math extensions
    ensureMathExtensions();

    this.canvas = document.getElementById(canvasId);
    if (!this.canvas) {
      throw new Error('Canvas not found');
    }

    // Add WebGL context check
    try {
      const gl = this.canvas.getContext('webgl2') || this.canvas.getContext('webgl');
      if (!gl) {
        throw new Error('WebGL not supported');
      }
      console.log('[PunchViewer] WebGL context initialized successfully');
    } catch (error) {
      console.error('[PunchViewer] WebGL initialization failed:', error);
    }

    // Remove container creation since we're using ui-overlay
    this.container = document.getElementById('ui-overlay');
    if (!this.container) {
      this.container = document.createElement('div');
      this.container.id = 'ui-overlay';
      document.body.appendChild(this.container);
    }

    // Ensure canvas is properly sized
    this.canvas.style.width = '100%';
    this.canvas.style.height = '100%';

    // 确保canvas在正确的位置
    this.canvas.style.position = 'absolute';
    this.canvas.style.top = '0';
    this.canvas.style.left = '0';
    this.canvas.style.zIndex = '0';

    // Define world boundaries
    this.worldBounds = {
      x: { min: -4, max: 4 },
      y: { min: 0.5, max: 5 },
      z: { min: -1, max: 5 }
    };

    // Initialize SceneManager first
    this.sceneManager = new SceneManager(this.canvas, this.worldBounds);
    this.scene = this.sceneManager.getScene();

    // Get physics system reference
    this.physicsSystem = this.scene.physicsSystem;

    // Initialize worker-related state before worker initialization
    this.ikWorkerState = {
      ready: false,
      pendingFrames: [],
      processingBatch: false,
      lastBatchTime: 0
    };

    // Initialize IK worker with proper error handling
    // this.initializeIKWorker().catch(error => {
    //   console.error('[PunchViewer] IK Worker initialization failed:', error);
    // });

    // Move debug handler setup here, after canvas initialization
    // this._setupDebugHandler();  // Note the underscore prefix
    // Make initialization async
    this.initializationComplete = this.initializeBaseSystems().then(() => {
      console.log('[PunchViewer] Base systems initialized, waiting for SceneManager');
      return this.sceneManager.initializationPromise;
    }).then(() => {
      console.log('[PunchViewer] SceneManager initialized, getting CharacterManager');
      this.characterManager = this.sceneManager.getCharacterManager();
      this.scene.characterManager = this.characterManager;
      console.log('[PunchViewer] Setting up event handlers');
      return this.setupEventHandlers();
    }).catch(error => {
      console.error('[PunchViewer] Failed to initialize:', error);
      this.uiManager?.showError('Failed to initialize game. Please refresh the page.');
    });
  }

  async initializeBaseSystems() {
    console.log('[PunchViewer] Starting base systems initialization');
    try {
      // Add initialization state tracking
      this.initializationState = {
        inputManager: false,
        uiManager: false,
        poseDetector: false,
        debugVisualizer: false
      };

      // Initialize input manager first to get video element
      this.inputManager = new InputManager();
      await this.inputManager.start();
      this.initializationState.inputManager = true;
      console.log('[PunchViewer] InputManager initialized successfully');

      if (!this.inputManager.videoElement) {
        throw new Error('Video element not initialized in InputManager');
      }

      // Initialize UI after input manager so we have video element
      this.uiManager = new UIManager(this);
      this.initializationState.uiManager = true;
      console.log('[PunchViewer] UIManager initialized successfully');

      // Initialize pose detector with UI reference and ONLY pose mode
      // This ensures we're only loading pose detection, not hand detection
      this.poseDetector = new PoseDetector({
        detectionMode: DetectionMode.POSE_ONLY, // Explicitly use pose-only mode
        useGestureDetection: false, // Explicitly disable gesture detection
        uiManager: this.uiManager
      });

      // Add error handling for pose detector
      try {
        await this.poseDetector.initialize();
        this.initializationState.poseDetector = true;
        console.log('[PunchViewer] PoseDetector initialized successfully');
      } catch (error) {
        console.error('[PunchViewer] PoseDetector initialization error:', error);
        this.uiManager?.showError('PoseDetector initialization failed. Some features may not work.');
      }

      // Initialize debug visualizer and connect it to the pose detector
      console.log('[PunchViewer] Initializing DebugVisualizer');
      this.debugVisualizer = new DebugVisualizer(this.scene, true);
      if (!this.uiManager?.cameraViewer?.canvas) {
        throw new Error('Camera viewer canvas not available');
      }
      await this.debugVisualizer.initialize(this.uiManager.cameraViewer.canvas);

      // Connect visualizer to pose detector
      this.poseDetector.setVisualizer(this.debugVisualizer);

      this.initializationState.debugVisualizer = true;

      // Initialize remaining components
      this.punchEffectsManager = new PunchEffectsManager(this.scene);
      this.combatSystem = new CombatSystem(this.scene, this.punchEffectsManager, this);

      // Initialize state variables
      this.initializeGameState();

      // Log final initialization state
      console.log('[PunchViewer] Final initialization state:', this.initializationState);
      console.log('[PunchViewer] Base systems initialized');
    } catch (error) {
      console.error('[PunchViewer] Failed to initialize base systems:', error);
      throw error;
    }
  }

  async setupEventHandlers() {
    console.log('[PunchViewer] Setting up event handlers');
    try {
      if (!this.inputManager || !this.poseDetector) {
        throw new Error('Required systems not initialized');
      }

      // Setup input handlers first
      await this.setupInputHandlers();
      console.log('[PunchViewer] Input handlers setup complete');

      // Setup debug handlers
      this._setupDebugHandler();
      console.log('[PunchViewer] Debug handlers setup complete');

      // Setup camera handlers
      await this.setupCameraHandlers();
      console.log('[PunchViewer] Camera handlers setup complete');

      console.log('[PunchViewer] All event handlers setup complete');
    } catch (error) {
      console.error('[PunchViewer] Failed to setup event handlers:', error);
      throw error;
    }
  }

  initializeGameState() {
    this.isPunchMode = false;
    this.lastPunchTime = 0;
    this.punchCooldown = 500;
    this.punchThreshold = 0.2;
    this.debug = false;
    this.gameStateManager = new GameStateManager(
      this.scene,
      this.characterManager,
      this.uiManager
    );
  }

  // Camera-specific handlers
  async setupCameraHandlers() {
    console.log('[PunchViewer] Setting up camera handlers');
    this.uiManager.setOnCameraToggle((isVisible) => {
      console.log('[PunchViewer] Camera toggle callback triggered:', isVisible);
      this.handleCameraToggle(isVisible);
    });
  }

  // Input-specific handlers
  async setupInputHandlers() {
    if (!this.inputManager || !this.poseDetector) {
      throw new Error('InputManager or PoseDetector not initialized');
    }

    try {
      await this.setupPoseCallback();
    } catch (error) {
      console.error('[PunchViewer] Failed to setup input handlers:', error);
      throw error;
    }
  }

  async setupPoseCallback() {
    console.log('[PunchViewer] Setting up pose callback');
    this.inputManager.onPoseResults(async ({ videoElement, timestamp }) => {
      try {
        // Check if game has started before processing pose data
        if (!this.gameStateManager?.isGameStarted()) {
          // Skip pose processing if game hasn't started yet
          return;
        }

        // Track frame timing for performance monitoring
        const frameStartTime = performance.now();

        // Get pose detection results
        let results = null;
        try {
          results = await this.poseDetector.detectPose(videoElement, timestamp);
        } catch (error) {
          console.error("[PunchViewer] Pose detection failed:", error);
          return; // Exit early on detection error
        }

        // Exit early if no results returned
        if (!results) {
          console.warn("[PunchViewer] No pose detection results returned");
          return;
        }

        this.lastPoseTimestamp = timestamp;

        // Process results for character animation
        if (this.characterManager) {
          try {
            const holisticResults = this.poseDetector.convertToHolisticFormat(results);

            // Add timing metadata to help with debugging performance
            holisticResults._metadata = {
              timestamp: timestamp,
              detectionTime: performance.now() - frameStartTime,
              frameId: this._frameCounter = (this._frameCounter || 0) + 1
            };
            // console.log(holisticResults);
            // Forward pose data to character manager
            this.characterManager.onPoseUpdate(holisticResults);
          } catch (error) {
            console.error("[PunchViewer] Error processing character update:", error);
          }
        }

        // Update visualization if debug mode is on
        if (this.debug && this.debugVisualizer?.initialized &&
          this.inputManager.areLandmarksVisible) {
          this.debugVisualizer.visualizePoseResults(results);
        }
      } catch (error) {
        console.error('[PunchViewer] Error processing pose results:', error);
      }
    });
  }

  // Rename to _setupDebugHandler and make it a class method
  _setupDebugHandler() {
    console.log('[PunchViewer] Setting up debug handler');

    this._handleDebugKeypress = async (ev) => {
      if (ev.ctrlKey && ev.shiftKey && (ev.key === 'I' || ev.key === 'i')) {
        console.log('[PunchViewer] Debug mode toggle triggered');

        // Toggle debug state first
        this.debug = !this.debug;

        try {
          // Update mesh bounding boxes
          // if (this.scene?.meshes) {
          //   this.scene.meshes.forEach(mesh => {
          //     if (mesh) {
          //       mesh.showBoundingBox = this.debug;
          //     }
          //   });
          // }

          // Update arm controller debug mode for player character
          const playerCharacter = this.characterManager?.scene?.getMeshByName('player');
          if (playerCharacter?.armController) {
            playerCharacter.armController.setDebugMode(this.debug);
            console.log('[PunchViewer] Set player arm controller debug mode:', this.debug);
          } else {
            console.warn('[PunchViewer] Player character or arm controller not found');
          }

          // Update pose detector debug mode
          this.poseDetector.setDebugMode(this.debug);

          // Update UI after all scene changes
          await this.uiManager?.setDebugMode(this.debug);

          // Toggle inspector last
          if (this.uiManager) {
            await this.uiManager.toggleInspector(this.scene);
          }

          this.uiManager?.showNotification(`Debug Mode: ${this.debug ? 'Enabled' : 'Disabled'}`);
        } catch (error) {
          console.error('[PunchViewer] Error toggling debug mode:', error);
        }
      }
    };

    window.addEventListener("keydown", this._handleDebugKeypress);
    this._boundDebugHandler = this._handleDebugKeypress;
  }

  // Add cleanup method
  cleanup() {
    // Remove debug handler
    if (this._boundDebugHandler) {
      window.removeEventListener("keydown", this._boundDebugHandler);
    }
    // ...existing cleanup code if any...
  }

  handleCameraToggle(isVisible) {
    console.log('[PunchViewer] handleCameraToggle called with:', isVisible);
    try {
      if (isVisible) {
        console.log('[PunchViewer] Enabling camera visualization');
        // Only toggle visualization state
        if (this.debugVisualizer?.initialized) {
          this.debugVisualizer.enable();
          // Set landmarks visible after visualizer is enabled
          this.inputManager.areLandmarksVisible = true;
        }
        console.log('[PunchViewer] Camera visualization enabled successfully');
      } else {
        console.log('[PunchViewer] Disabling camera visualization');
        // Only toggle visualization state
        if (this.debugVisualizer) {
          this.debugVisualizer.disable();
        }
        this.inputManager.areLandmarksVisible = false;
        console.log('[PunchViewer] Camera visualization disabled successfully');
      }
    } catch (error) {
      console.error('[PunchViewer] Error in camera toggle:', error);
      // Only cleanup visualization on error
      this.debugVisualizer?.disable();
      this.inputManager.areLandmarksVisible = false;
    }
  }

  takeDamage(amount, role) {
    const currentHealth = this.gameStateManager.updateHealth(role, amount);
    if (currentHealth <= 0) {
      // Game over handling is now managed by GameStateManager
    }
  }

  async initializeIKWorker() {
    try {
      console.log('[PunchViewer] Initializing IK Worker');
      this.ikWorker = new Worker(
        new URL('./characters/ikWorker.js', import.meta.url),
        { type: 'module' }
      );

      // Return a promise that resolves when the worker is ready
      return new Promise((resolve, reject) => {
        this.ikWorker.onmessage = (e) => {
          const { type, results, error } = e.data;
          console.log('[PunchViewer] Worker message received:', { type, hasResults: !!results });

          switch (type) {
            case 'ready':
              this.ikWorkerState.ready = true;
              this.processPendingFrames();
              resolve();
              break;
            case 'batchResult':
              if (results) {
                const decompressed = decompressData(results);
                this.characterController?.updateFromBatchResults(decompressed);
              }
              break;
            case 'error':
              console.error('[PunchViewer] Worker error:', error);
              reject(error);
              break;
          }
        };

        this.ikWorker.onerror = (error) => {
          console.error('[PunchViewer] Worker error:', error);
          this.ikWorkerState.ready = false;
          reject(error);
        };
      });

    } catch (error) {
      console.error('[PunchViewer] Failed to initialize IK Worker:', error);
      this.ikWorkerState.ready = false;
      throw error;
    }
  }

  processPendingFrames() {
    const { pendingFrames } = this.ikWorkerState;
    if (!pendingFrames?.length) {
      return;
    }

    try {
      console.log('[PunchViewer] Processing pending frames:', pendingFrames.length);
      const binaryData = compressData(pendingFrames);
      this.ikWorker.postMessage({
        type: 'batchSolve',
        binaryData
      }, [binaryData.buffer]);
      this.ikWorkerState.pendingFrames = [];
    } catch (error) {
      console.error('[PunchViewer] Error processing pending frames:', error);
      this.ikWorkerState.pendingFrames = [];
    }
  }

  addFrameToBatch(frame) {
    if (!this.ikWorkerState.pendingFrames) {
      this.ikWorkerState.pendingFrames = [];
    }
    this.ikWorkerState.pendingFrames.push(frame);
  }
}
