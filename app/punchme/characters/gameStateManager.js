// gameStateManager.js
import { Vector3, Matrix } from '@babylonjs/core';

class GameStateManager {
  constructor(scene, characterManager, uiManager) {
    this.scene = scene;
    this.characterManager = characterManager;
    this.uiManager = uiManager;
    this.engine = scene.getEngine();

    this.health = { player: 100, enemy: 100 };
    this.gameState = {
      isGameOver: false,
      winner: null,
      isGameStarted: false
    };
  }

  // Add this new method to start the game
  startGame() {
    if (this.gameState.isGameStarted) return;

    console.log('[GameStateManager] Starting game');
    this.gameState.isGameStarted = true;
    this.resetGame();
  
    // Start the fight camera sequence
    if (this.scene.sceneManager) {
      const oldCam = this.scene.activeCamera; 
      // Pass oldCam.radius so we keep the same initial radius for a smooth transition.
      this.scene.sceneManager.gameCameraManager.setupFollowCamera();
    }

    // Additional game start logic
    // this.startCharacterAnimations();
  }

  // Helper method to play start animations on characters
  // startCharacterAnimations() {
  //   ['player', 'enemy'].forEach(role => {
  //     const character = this.scene.getMeshByName(role);
  //     if (character?.animationController) {
  //       // Play a "ready to fight" animation if available, otherwise idle
  //       const animName = character.animationController.hasAnimation('ready') ?
  //         'ready' : character.animationController.defaultAnimation;
  //       character.animationController.playAnimation(animName);
  //     }
  //   });
  // }

  updateHealth(role, damage) {
    const newHealth = Math.max(0, this.health[role] - damage);
    this.health[role] = newHealth;
    this.uiManager.updateHealth(role, newHealth);
    if (newHealth <= 0) {
      this.handleCharacterDefeat(role);
    }
    return newHealth;
  }

  handleCharacterDefeat(role) {
    this.gameState.isGameOver = true;
    this.gameState.winner = role === 'player' ? 'enemy' : 'player';
    this.uiManager.showGameOver && this.uiManager.showGameOver(this.gameState.winner);
  }

  resetGame() {
    this.health = { player: 100, enemy: 100 };
    this.gameState = {
      isGameOver: false,
      winner: null,
      isGameStarted: this.gameState.isGameStarted // Keep the started state
    };

    ['player', 'enemy'].forEach(role => {
      this.uiManager.updateHealth(role, 100);
    });
  }

  getHealth(role) {
    return this.health[role];
  }

  isGameOver() {
    return this.gameState.isGameOver;
  }

  isGameStarted() {
    return this.gameState.isGameStarted;
  }
}

export default GameStateManager;