import {
    Vector3, MeshBuilder, TransformNode, PhysicsAggregate, PhysicsShapeType, 
} from '@babylonjs/core';
import { AnimationController } from './animationController.js';
import ArmController from './ArmControler.js';

class CharacterManager {
    constructor(scene, worldBounds) {
        this.scene = scene;
        this.worldBounds = worldBounds;

        // this.skeletonMap = {};
        this.positionObservers = new Map(); // Add position observers

        // Add event handlers for character loading
        this.onCharacterLoadedCallbacks = new Map();

        // Replace simple ready states with loading states
        // this.characterStates = {
        //     player: { loading: false, initialized: false, ready: false },
        //     enemy: { loading: false, initialized: false, ready: false }
        // };

        // Character-specific configurations
        this.characterConfigs = {
            enemy: {
                targetHeight: 2.2,  // Maria更高
                minScale: 1.0,      // 最小缩放增加
                maxScale: 1.2,      // 最大缩放增加
                position: new Vector3(-1, worldBounds.y.min, worldBounds.z.min),
                // Rotate enemy to face right (toward the player)
                rotation: new Vector3(0, Math.PI / 2, 0),
                baseScale: new Vector3(1.1, 1.1, 1.1)  // 基础缩放也略微增加
            },
            player: {
                targetHeight: 1.7,  // Remy保持不变
                minScale: 0.7,
                maxScale: 1.1,
                position: new Vector3(1, worldBounds.y.min, worldBounds.z.min),
                // Rotate player to face left (toward the enemy)
                rotation: new Vector3(0, -Math.PI / 2, 0),
                baseScale: new Vector3(1, 1, 1)
            }
        };

        // Log configuration
        console.log("[CharacterManager] Initialized with world bounds:", worldBounds);
        console.log("[CharacterManager] Character configs:", this.characterConfigs);

        this.assetsManager = scene.sceneManager?.getAssetsManager();
        if (!this.assetsManager) {
            console.error('[CharacterManager] No AssetsManager available');
        }
    }
    // getOrCreateHeadTarget(character) {
    //     if (!character || !character.metadata?.skeleton) return null;

    //     const skeleton = character.metadata.skeleton;
    //     const headBone = skeleton.bones.find(b => b.name.toLowerCase().includes("headtop_end"));
    //     console.log(headBone);
    //     if (!headBone) {
    //       console.warn(`[CharacterManager] No head bone found for ${character.name}`);
    //       return null;
    //     }

    //     if (!character._headTarget) {
    //         character._headTarget = new TransformNode(`${character.name}_headTarget`, this.scene);
    //       }
    //     return character._headTarget;
    // }

    async loadCharacter(name, role) {
        const config = this.characterConfigs[role];
        if (!config) {
            throw new Error(`Invalid role: ${role}`);
        }

        try {
            return new Promise((resolve, reject) => {
                const meshTask = this.assetsManager.addMeshTask(
                    `load_${role}_character`,
                    null,               // meshesNames (null loads all meshes)
                    '/assets/characters/',  // rootUrl
                    name                // sceneFilename
                );

                meshTask.onSuccess = async (task) => {
                    try {
                        console.log(`[CharacterManager] Loaded assets for ${role}:`, {
                            meshes: task.loadedMeshes.length,
                            skeletons: task.loadedSkeletons.length,
                            particleSystems: task.loadedParticleSystems.length
                        });

                        const character = task.loadedMeshes[0];
                        // console.log(`[CharacterManager] Character ${role} loaded:`, character);
                        // Set standardized name for scene-wide access
                        character.name = role;
                        character.metadata = { role };
                        character.metadata.skeleton = task.loadedSkeletons[0];
                        character.metadata.animationGroups = task.loadedAnimationGroups;
                        // Initialize character
                        await this.initializeCharacter(character, role, config);

                        // Set player transparency
                        if (role === 'player') {
                            // this.setCharacterTransparency(role, 0.7);
                        }

                        // Notify listeners
                        this.notifyCharacterLoaded(role, character);
                        resolve(character);

                    } catch (error) {
                        console.error(`[CharacterManager] Task success handler failed for ${role}:`, error);
                        reject(error);
                    }
                };

                meshTask.onError = (task, message, error) => {
                    const errorMessage = `Failed to load ${role} character: ${message}`;
                    console.error('[CharacterManager]', errorMessage, error);
                    reject(new Error(errorMessage));
                };

                this.assetsManager.load();
            });

        } catch (error) {
            console.error(`[CharacterManager] Failed to load ${role}:`, error);
            throw error;
        }
    }

    async initializeCharacter(character, role, config) {
        console.log(`[CharacterManager] Initializing ${role} character:`, {
            hasMetadata: !!character.metadata,
            role: character.metadata?.role,
            name: character.name
        });

        // Calculate initial height and apply transformations
        const initialHeight = this.calculateCharacterHeight(character);
        const targetScale = config.targetHeight / initialHeight;
        const clampedScale = Math.min(Math.max(targetScale, config.minScale), config.maxScale);
        const finalScale = config.baseScale.scale(clampedScale);

        // Apply basic transformations to the character mesh (visual part)
        character.scaling = finalScale;
        character.position = config.position.clone();
        character.rotation = config.rotation.clone(); // Apply rotation to character mesh
        // Create a physics root mesh using a capsule collider for proper physics simulation
        // const halfHeight = config.targetHeight / 2;
        // const physicsRoot = MeshBuilder.CreateCapsule(`${role}_physics_root`, {
        //     height: config.targetHeight,
        //     subdivisions: 4
        // }, this.scene);
        // // Make it semi-visible for debugging
        // // physicsRoot.visibility = 0.2;
        // physicsRoot.isVisible = false;

        // // Parent the character under the physicsRoot
        // character.parent = physicsRoot;
        // // Offset the character so its feet align with the bottom of the capsule
        // character.position.y = -halfHeight; // Position relative to parent
        // // Apply physicsRoot position AFTER parenting character
        // physicsRoot.position = config.position.clone();
        // physicsRoot.position.y += halfHeight;
        // // physicsRoot.rotation = config.rotation.clone(); // Apply rotation to physics root too if needed

        // // --- CRITICAL: Force Matrix Updates BEFORE ArmController Init ---
        // console.log(`[CharacterManager] Forcing matrix updates for ${role} before controller init...`);
        // physicsRoot.computeWorldMatrix(true); // Update parent first
        character.computeWorldMatrix(true);   // Update character mesh itself
        if (character.metadata?.skeleton) {
            character.metadata.skeleton.computeAbsoluteMatrices(true); // Update skeleton bones
            console.log(`[CharacterManager] Skeleton matrices computed for ${role}.`);
        } else {
            console.warn(`[CharacterManager] No skeleton found for matrix update on ${role}.`);
        }
        // -----------------------------------------------------------------

        // Update metadata if needed
        character.metadata = {
            ...character.metadata,
            height: initialHeight,
            scale: finalScale,
            // physicsRoot: physicsRoot
        };
        // Initialize animation controller for the character
        character.animationController = new AnimationController(this.scene, character);
        character.animationController.setupAnimations();
        if (role === 'player') {
            // Initialize arm controller for IK-based arm movements
            character.armController = new ArmController(this.scene, character);
            console.log(`[CharacterManager] Controllers initialized for ${role}:`, {
                scale: finalScale.asArray(),
                hasAnimationController: !!character.animationController,
                hasArmController: !!character.armController
            });
        }
        // Optionally, start playing the default (idle) animation if available
        // if (character.animationController.defaultAnimation) {
        // character.animationController.playAnimation(character.animationController.defaultAnimation);
        // }
        console.log(`[CharacterManager] AnimationController initialized for ${role}`);
        // Initialize animation controller for the character
        // character.animationController = new AnimationController(this.scene, character);
        console.log(`[CharacterManager] Character ${role} initialized:`, {
            finalHeight: character.metadata.height,
            scale: finalScale.asArray(),
            // position: physicsRoot.absolutePosition.asArray(),
            hasAnimationController: !!character.animationController
        });

        // Instead of creating collider on character, create it on physicsRoot.
        // this.physicsSystem.createCharacterCollider(physicsRoot, { mass: 1 });
    }
    onPoseUpdate(poseResults) {
        // Pass pose data to all characters or specific ones based on your logic
        const character = this.scene.getMeshByName('player');
        if (character) {
            // Update both controllers with pose data
            // if (character.animationController) {
            //     character.animationController.updateWithPoseData(poseResults);
            // }

            if (character.armController) {
                character.armController.updateWithPoseData(poseResults);
            }
        }
    }


    calculateCharacterHeight(character) {
        if (!character) {
            console.error("[CharacterManager] Cannot calculate height: No mesh provided");
            return 1.8; // Default height if calculation fails
        }

        // Force immediate computation
        character.computeWorldMatrix(true);
        character.refreshBoundingInfo(true); // Force refresh of bounding info including children

        const boundingVectors = character.getHierarchyBoundingVectors(true);
        const height = Math.abs(boundingVectors.max.y - boundingVectors.min.y);

        console.log(`[CharacterManager] Initial height calculation for ${character.name}:`, {
            height,
            boundingVectors: {
                min: boundingVectors.min.asArray(),
                max: boundingVectors.max.asArray()
            }
        });

        return height > 0.1 ? height : 1.8; // Return default height if calculated height is too small
    }

    logMeshHierarchy(mesh, depth) {
        const prefix = '  '.repeat(depth);
        console.log(`${prefix} [Mesh] ${mesh.name}: `, {
            position: mesh.position.toString(),
            absolutePosition: mesh.absolutePosition.toString(),
            isVisible: mesh.isVisible,
            hasVertices: mesh.getVerticesData ? true : false
        });

        // Only traverse children if they exist
        const children = mesh.getChildren?.() || [];
        children.forEach(child => {
            if (child.position) {  // Only process valid mesh objects
                this.logMeshHierarchy(child, depth + 1);
            }
        });
    }

    onPositionUpdate(role, results) {
        // Thread Safety: Processing happens when data arrives, but visual updates occur only during the render cycle。
        if (role === 'player') {
            try {
                // Validate input data
                if (!results) {
                    console.warn("[CharacterManager] Empty pose results received");
                    return;
                }

                // Pass results to the avatar manager
                // this.mediapipeAvatarManager.update(results);

                // Trigger any additional visibility-based logic
                const hasVisibleLandmarks = results.poseWorldLandmarks &&
                    results.poseWorldLandmarks.some(lm => lm && lm.visibility > 0.5);

                if (hasVisibleLandmarks) {
                    // Player is visible, could trigger additional game logic here
                    if (!this._wasPlayerVisible) {
                        // console.log("[CharacterManager] Player became visible");
                        // Additional logic when player first becomes visible
                    }
                    this._wasPlayerVisible = true;
                } else {
                    if (this._wasPlayerVisible) {
                        // console.log("[CharacterManager] Player no longer visible");
                        // Additional logic when player is no longer visible
                    }
                    this._wasPlayerVisible = false;
                }
            } catch (error) {
                console.error("[CharacterManager] Error in position update:", error);
            }
        }
    }

    /**
     * Estimate the distance between two bones by comparing their absolute transforms.
     */
    estimateBoneLength(bone, childBone) {
        const boneMatrix = bone.getFinalMatrix();
        const childMatrix = childBone.getFinalMatrix();
        const bonePos = boneMatrix.getTranslation();
        const childPos = childMatrix.getTranslation();
        return Vector3.Distance(bonePos, childPos);
    }

    setCharacterTransparency(role, alpha) {
        // const character = this.characters.get(role);
        const character = this.scene.getMeshByName(role);
        if (!character) return;

        const setMeshTransparency = (node) => {
            if (node.material) {
                node.material.alpha = alpha;
                node.material.transparencyMode = 2;
                node.material.needDepthPrePass = true;
            }
            node.getChildren().forEach(setMeshTransparency);
        };

        if (character.metadata?.isMultiPart) {
            character.metadata.parts.forEach(setMeshTransparency);
        }
        setMeshTransparency(character);

        console.log(`[CharacterManager] Set ${role} transparency:`, {
            alpha,
            meshCount: this.countAffectedMeshes(character)
        });
    }

    countAffectedMeshes(node) {
        let count = 0;
        if (node.material) count++;
        node.getChildren().forEach(child => {
            count += this.countAffectedMeshes(child);
        });
        return count;
    }

    notifyCharacterLoaded(role, character) {
        const callbacks = this.onCharacterLoadedCallbacks.get(role);
        if (callbacks) {
            callbacks.forEach(callback => {
                try {
                    callback(character);
                } catch (error) {
                    console.error(`[CharacterManager] Error in character loaded callback for ${role}:`, error);
                }
            });
        }
    }
    setDebugMode(enabled) {
        console.log('[CharacterManager] Debug mode toggled:', enabled);

        // Apply debug mode to all characters that have controllers
        const playerCharacter = this.scene.getMeshByName('player');
        if (playerCharacter) {
            if (playerCharacter.armController) {
                playerCharacter.armController.setDebugMode(enabled);
                console.log('[CharacterManager] Set player arm controller debug mode:', enabled);
            }

            if (playerCharacter.animationController) {
                // If animation controller has debug mode, set it here
                console.log('[CharacterManager] Animation controller debug mode:', enabled);
            }
        }

        // Show bounding boxes for all character meshes if desired
        // commented out as per previous code, but could be enabled
        /*
        const characters = ['player', 'enemy'].map(role => this.scene.getMeshByName(role)).filter(Boolean);
        characters.forEach(character => {
            if (character) {
                character.showBoundingBox = enabled;
                const physicsRoot = character.metadata?.physicsRoot;
                if (physicsRoot) {
                    physicsRoot.showBoundingBox = enabled;
                }
            }
        });
        */
    }

    // Add cleanup method for character removal
    cleanup(role) {
        const character = this.scene.getMeshByName(role);
        if (character) {
            // Clean up controllers
            if (character.armController) {
                character.armController.dispose();
            }

            // Remove any remaining observers
            this.scene.sceneManager.removeBoundingBoxObserver(character.name);
        }
    }
}

export default CharacterManager;
