import {
    Vector3, BoneIKController, MeshBuilder,
    StandardMaterial, Color3, TransformNode, Matrix, Bone, Space, Quaternion
} from '@babylonjs/core';
import { Debug } from 'babylonjs';
import BoneMapper from './boneMapper.js';

// --- Configuration ---
// Adjust this scale based on your character size and desired movement range
// How much do normalized landmark movements translate to world units?
const LANDMARK_TO_WORLD_SCALE = 2.5;
// Optional: Scale the influence of landmark Z coordinate (depth)
const LANDMARK_Z_SCALE = 1.5;
// Visibility threshold for landmarks
const LANDMARK_VISIBILITY_THRESHOLD = 0.6;
// IK Pole angle - adjust if elbows bend unnaturally (e.g., Math.PI for backward bend)
const IK_POLE_ANGLE = Math.PI / 2; // 90 degrees forward bend often work
// ---------------------
// --- ArmController Class ---
class ArmController {
    // https://www.babylonjs-playground.com/#VSURBJ#2 -> bone ik setup
    constructor(scene, character) {
        this.scene = scene;
        this.character = character;
        this.skeleton = character.metadata?.skeleton;
        this.ikControllers = {};
        this.targets = {}; // IK Targets (TransformNodes)
        this.debugMode = false;
        this.boneAxesViewers = {}; // Store bone axes viewers
        this.boneMap = {};       // Stores mapped bone names
        this.bones = { leftArm: {}, rightArm: {} }; // Stores actual Bone objects

        if (!this.skeleton) {
            console.error('[ArmController] Initialization failed: No skeleton found for character', character.name);
            return; // Prevent further errors
        }
        if (!this.character.parent) {
            console.warn('[ArmController] Character mesh is not parented. Positioning might be relative to world origin instead of a physics root.');
        }

        //  // Ensure skeleton matrices are up-to-date after potential parent transforms
        this.character.computeWorldMatrix(true);
        this.skeleton.computeAbsoluteMatrices();

        console.log(`[ArmController] Initializing for ${character.name}...`);
        this.logInitialTransforms(); // Log transforms before doing anything else

        this.initBones();          // Find and map bones
        this.initIkControllers();  // Setup IK controllers and targets
        this.updateTargetInitialPositions(); // Set initial target positions *after* IK setup
        console.log(`[ArmController] Initialized successfully for ${character.name}`);
    }
    logInitialTransforms() {
        console.log('[ArmController] Initial Character Transforms:');
        console.log("  Mesh Absolute Position:", this.character.getAbsolutePosition());
        console.log("  Mesh Scaling:", this.character.scaling);
        if (this.character.parent) {
            console.log("  Parent Position:", this.character.parent.getAbsolutePosition());
            console.log("  Parent Scaling:", this.character.parent.scaling);
        } else {
             console.log("  Mesh has no parent.");
        }
        // Log root bone's WORLD position for reference
        if (this.skeleton.bones.length > 0) {
             console.log("  Root Bone World Position:", this.skeleton.bones[0].getPosition(Space.WORLD, this.character));
        } else {
            console.log("  Skeleton has no bones to get root position from.");
        }
         const worldBoundingVectors = this.character.getHierarchyBoundingVectors(true);
         console.log('  Character World BBox:', worldBoundingVectors.min, worldBoundingVectors.max);
    }
    initBones() {
        console.log('[ArmController] Mapping bones...');
        // --- REMOVED Manual Bone Transform Loop ---
        // Scaling and transforms should be inherited hierarchically from CharacterManager setup.
        // Manually manipulating bone matrices here is error-prone and usually unnecessary.
        // Ensure matrices reflect the current state after potential parent scaling/positioning
        this.skeleton.computeAbsoluteMatrices();
        // Use BoneMapper to get standardized bone mapping
        this.boneMap = BoneMapper.createSkeletonMap(this.skeleton);
        if (!this.boneMap.chains?.leftArm || !this.boneMap.chains?.rightArm) {
             console.error('[ArmController] BoneMapper failed to identify arm chains. Check bone names and mapping logic.', this.boneMap);
             return; // Stop initialization if critical bones are missing
        }
        // Extract and store required Bone objects
        this.bones.leftArm = {
            upperArm: this.findBone(this.boneMap.chains.leftArm.UPPER_ARM),
            foreArm: this.findBone(this.boneMap.chains.leftArm.FOREARM),
            hand: this.findBone(this.boneMap.chains.leftArm.HAND) // Hand/Wrist bone is crucial for target
        };
        this.bones.rightArm = {
            upperArm: this.findBone(this.boneMap.chains.rightArm.UPPER_ARM),
            foreArm: this.findBone(this.boneMap.chains.rightArm.FOREARM),
            hand: this.findBone(this.boneMap.chains.rightArm.HAND)
        };
        // Validation
        if (!this.bones.leftArm.upperArm || !this.bones.leftArm.foreArm || !this.bones.leftArm.hand ||
            !this.bones.rightArm.upperArm || !this.bones.rightArm.foreArm || !this.bones.rightArm.hand) {
            console.error('[ArmController] Failed to find all required arm bones (UpperArm, ForeArm, Hand). Check BoneMapper output and skeleton names.', this.bones);
            // You might want to disable IK or throw an error depending on requirements
        } else {
            console.log('[ArmController] Successfully mapped arm bones:', {
                left: {
                    upper: this.bones.leftArm.upperArm.name,
                    fore: this.bones.leftArm.foreArm.name,
                    hand: this.bones.leftArm.hand.name
                 },
                right: {
                    upper: this.bones.rightArm.upperArm.name,
                    fore: this.bones.rightArm.foreArm.name,
                    hand: this.bones.rightArm.hand.name
                 }
            });
        }
    }
    findBone(boneName) {
        if (!boneName || !this.skeleton) return null;
        const bone = this.skeleton.bones.find(b => b.name === boneName);
        if (!bone) {
            // console.warn(`[ArmController] Bone named "${boneName}" not found in skeleton.`);
        }
        return bone;
    }

    initIkControllers() {
        console.log('[ArmController] Initializing IK controllers...');
        // --- Create IK Targets (TransformNodes) ---
        // These nodes will be positioned by MediaPipe data.
        // We'll parent them to the character's PARENT (usually the physics root)
        // so their positions are relative to the character's root transform,
        // but IKController reads their absolute world position.
        const parentNode = this.character; // Parent to physics root or scene if no parent
        console.log(`[ArmController] Parenting IK targets to: ${parentNode.name}`);
        this.targets.leftWrist = new TransformNode("leftWristTarget", this.scene);
        this.targets.rightWrist = new TransformNode("rightWristTarget", this.scene);
        this.targets.leftWrist.parent = parentNode;
        this.targets.rightWrist.parent = parentNode;
        // Optional: Pole targets help control elbow direction.
        // Create one pole target, possibly positioned behind the character.
        this.targets.pole = new TransformNode("armPoleTarget", this.scene);
        this.targets.pole.parent = parentNode;
        // Position it relative to the parent (e.g., behind and slightly above the character's base)
        this.targets.pole.position = new Vector3(0, 0.5, -1.0); // Adjust Z based on character forward direction
        // Create visual representations for debugging
        this.createTargetVisual(this.targets.leftWrist, "leftWrist");
        this.createTargetVisual(this.targets.rightWrist, "rightWrist");
        this.createTargetVisual(this.targets.pole, "pole");
        // var box = MeshBuilder.CreateBox('box', { size: .1 }, this.scene);
        // box.rotationQuaternion = Quaternion.Identity();
        // var bone = this.skeleton.bones[15];
        // this.scene.registerBeforeRender(() => {
        //     box.position = bone.getTransformNode().getAbsolutePosition();
            // box.rotation = bone.getTransformNode().getRotationQuaternion();
            // bone.getPositionToRef(Space.WORLD, this.character.metadata.physicsRoot, box.position);
			// bone.getRotationQuaternionToRef(Space.WORLD, this.character.metadata.physicsRoot, box.rotationQuaternion);
        // }   
        // )
        // --- Create IK Controllers ---
        const leftForearmBone = this.bones.leftArm.foreArm;
        const rightForearmBone = this.bones.rightArm.foreArm;
        // Ensure the bones needed for IK exist
        if (leftForearmBone && this.bones.leftArm.upperArm) {
            this.ikControllers.leftArm = new BoneIKController(this.character, leftForearmBone, {
                target: this.targets.leftWrist, // Use the TransformNode directly
                poleTarget: this.targets.pole,
                poleAngle: IK_POLE_ANGLE,
                // bendAxis: Axis.X, // Might need adjustment depending on bone orientation
            });
            var t = 0;
            this.scene.registerBeforeRender(() => {
                t += .03;
                // Update the pole target position based on the character's position
                var target = this.targets.leftWrist;
                target.position.x = 1;
                target.position.y = 2 + 1 * Math.sin(t);
                target.position.z = -1 + 1 * Math.cos(t);
                
                this.ikControllers.leftArm.update();
            });
             console.log('[ArmController] Left Arm IK Controller created.');
        } else {
             console.error('[ArmController] Cannot create Left Arm IK: Missing forearm or upper arm bone.');
        }
        if (rightForearmBone && this.bones.rightArm.upperArm) {
            this.ikControllers.rightArm = new BoneIKController(this.character, rightForearmBone, {
                target: this.targets.rightWrist, // Use the TransformNode directly
                poleTarget: this.targets.pole,
                poleAngle: IK_POLE_ANGLE,
                 // bendAxis: Axis.X, // Might need adjustment
            });
            console.log('[ArmController] Right Arm IK Controller created.');
        } else {
             console.error('[ArmController] Cannot create Right Arm IK: Missing forearm or upper arm bone.');
        }
        console.log('[ArmController] IK controllers initialization attempt finished.');
    }
    createTargetVisual(targetNode, name) {
        // Simple sphere visual attached to the TransformNode
        const sphere = MeshBuilder.CreateSphere(`${name}_visual`, { diameter: 0.1 }, this.scene);
        const material = new StandardMaterial(`${name}_material`, this.scene);
        if (name.includes("left")) material.diffuseColor = Color3.Blue();
        else if (name.includes("right")) material.diffuseColor = Color3.Red();
        else material.diffuseColor = Color3.Green(); // Pole target
        material.emissiveColor = material.diffuseColor.scale(0.5);
        material.alpha = 0.7;
        sphere.material = material;
        sphere.parent = targetNode; // Attach visual to the target node
        sphere.isPickable = false;
        sphere.setEnabled(this.debugMode); // Initially hidden unless debug mode is on
        return sphere;
    }
    updateTargetInitialPositions() {
        console.log('[ArmController] Setting initial IK target positions...');
        // Add safeguard matrix computation
        if (this.skeleton) { // Check skeleton exists
            this.skeleton.computeAbsoluteMatrices(true);
            console.log('[ArmController] Re-computed skeleton matrices before getting initial hand positions.');
       } else {
            console.error('[ArmController] Cannot update initial target positions: Skeleton missing.');
            return;
       }
        // Get the character's parent's world matrix (usually physics root) to convert world to local
        const parentWorldMatrix = this.character.parent ? this.character.parent.getWorldMatrix() : Matrix.Identity();
        const invParentWorldMatrix = Matrix.Invert(parentWorldMatrix);
        // Calculate initial position based on the HAND bone's world position
        if (this.bones.leftArm.hand && this.targets.leftWrist) {
            const leftHandWorldPos = this.getBoneWorldPosition(this.bones.leftArm.hand);
            // Add logging to see the calculated world position
            console.log(`  Raw Left Hand World Pos: ${leftHandWorldPos.toString()}`);
            Vector3.TransformCoordinatesToRef(leftHandWorldPos, invParentWorldMatrix, this.targets.leftWrist.position);
            console.log(`  Initial Left Wrist Target Local Pos (relative to ${this.targets.leftWrist.parent?.name}):`, this.targets.leftWrist.position);
            // Check if the resulting local position is reasonable
            if(Math.abs(this.targets.leftWrist.position.x) > 50 || Math.abs(this.targets.leftWrist.position.y) > 50 || Math.abs(this.targets.leftWrist.position.z) > 50) {
                console.warn("!! Left Wrist initial local position seems excessively large !!");
            }
        }
        if (this.bones.rightArm.hand && this.targets.rightWrist) {
            const rightHandWorldPos = this.getBoneWorldPosition(this.bones.rightArm.hand);
             console.log(`  Raw Right Hand World Pos: ${rightHandWorldPos.toString()}`);
            Vector3.TransformCoordinatesToRef(rightHandWorldPos, invParentWorldMatrix, this.targets.rightWrist.position);
             console.log(`  Initial Right Wrist Target Local Pos (relative to ${this.targets.rightWrist.parent?.name}):`, this.targets.rightWrist.position);
             if(Math.abs(this.targets.rightWrist.position.x) > 50 || Math.abs(this.targets.rightWrist.position.y) > 50 || Math.abs(this.targets.rightWrist.position.z) > 50) {
                console.warn("!! Right Wrist initial local position seems excessively large !!");
            }
        }
    }
    // --- MediaPipe Data Processing ---
    updateWithPoseData(poseResults) {
    // When dealing with bones from glTF files you must use the linked TransformNode and not directly the bone:
    // https://playground.babylonjs.com/#88CB6A#77
    // That’s because of the way skeleton/bones are handled in glTF files.
    //         scene.registerBeforeRender(() => {            
    //     skeletons[0].bones[0].getTransformNode().rotationQuaternion.multiplyInPlace(quatRotY);
    // });
        // Basic validation
        if (!poseResults || !poseResults.poseLandmarks || poseResults.poseLandmarks.length === 0) {
            // console.warn('[ArmController] No valid pose landmarks received.');
            return;
        }
        // Assuming single person detection
        const landmarks = poseResults.poseLandmarks[0];
        if (!landmarks) {
             // console.warn('[ArmController] Landmarks array is empty.');
            return;
        }
        // Landmark indices from BoneMapper or defined here
        const lmIndices = BoneMapper.LANDMARK_INDICES;
        // Helper to check visibility
        const isVis = (idx) => landmarks[idx] && landmarks[idx].visibility > LANDMARK_VISIBILITY_THRESHOLD;
        // --- Update Left Arm ---
        if (this.targets.leftWrist && this.bones.leftArm.upperArm &&
            isVis(lmIndices.LEFT_WRIST) && isVis(lmIndices.LEFT_SHOULDER) && isVis(lmIndices.LEFT_ELBOW)) {
            this.positionTarget(
                'leftWrist',
                landmarks[lmIndices.LEFT_WRIST],
                landmarks[lmIndices.LEFT_SHOULDER],
                this.bones.leftArm.upperArm // Pass the corresponding shoulder bone
            );
        } else {
             // Optional: Add logic if landmarks are not visible (e.g., reset to default, hold position)
             // console.log("Left arm landmarks not sufficiently visible.");
        }
        // --- Update Right Arm ---
        if (this.targets.rightWrist && this.bones.rightArm.upperArm &&
            isVis(lmIndices.RIGHT_WRIST) && isVis(lmIndices.RIGHT_SHOULDER) && isVis(lmIndices.RIGHT_ELBOW)) {
            this.positionTarget(
                'rightWrist',
                landmarks[lmIndices.RIGHT_WRIST],
                landmarks[lmIndices.RIGHT_SHOULDER],
                this.bones.rightArm.upperArm // Pass the corresponding shoulder bone
            );
        } else {
             // Optional: Add logic if landmarks are not visible
             // console.log("Right arm landmarks not sufficiently visible.");
        }
        // IMPORTANT: Call the IK update AFTER targets have been positioned
        this.update();
    }
    /**
     * Calculates the world position for an IK target based on MediaPipe landmarks
     * and sets the local position of the target TransformNode.
     */
    positionTarget(targetName, wristLandmark, shoulderLandmark, shoulderBone) {
        const targetNode = this.targets[targetName];
        if (!targetNode || !shoulderBone) return;
        // Ensure matrices are current before reading bone world position
        this.skeleton.computeAbsoluteMatrices(true); // Force update THIS FRAME

        // 1. Get the Anchor Point: World position of the character's shoulder bone
        const shoulderWorldPos = this.getBoneWorldPosition(shoulderBone); // Now uses updated matrix
        if (!shoulderWorldPos || shoulderWorldPos.equals(Vector3.Zero())) { // Added check for Zero vector just in case
            console.error(`[ArmController] Could not get valid world position for shoulder bone: ${shoulderBone.name}`);
            return;
        }
        // 2. Calculate Offset Vector (MediaPipe Landmark Space -> World Space Offset)
        //    Vector from shoulder to wrist in landmark space
        const landmarkDeltaX = wristLandmark.x - shoulderLandmark.x;
        const landmarkDeltaY = wristLandmark.y - shoulderLandmark.y;
        //    'z' from MediaPipe is harder to map directly, represents depth relative to hip center?
        //    We scale it and use it, but might need refinement depending on MediaPipe version/output.
        const landmarkDeltaZ = (wristLandmark.z - shoulderLandmark.z) * LANDMARK_Z_SCALE;
        // 3. Map Landmark Vector to World Offset Vector
        //    This requires knowing your character's orientation and MediaPipe's coord system.
        //    Assumption: Character +Z is forward, +Y is up, +X is right.
        //    Assumption: MediaPipe landmarks X increases right, Y increases DOWN, Z increases AWAY? (Verify!)
        //    Mirror X-axis movement for the left arm relative to the right arm's landmarks
        const mirror = targetName.includes('left') ? -1 : 1;
        //    Map axes and apply scale:
        const worldOffsetX = landmarkDeltaX * LANDMARK_TO_WORLD_SCALE * mirror;
        const worldOffsetY = -landmarkDeltaY * LANDMARK_TO_WORLD_SCALE; // Invert Y axis (landmark Y down, world Y up)
        const worldOffsetZ = landmarkDeltaZ * LANDMARK_TO_WORLD_SCALE; // Apply Z scale (adjust sign if needed)
        const worldOffset = new Vector3(worldOffsetX, worldOffsetY, worldOffsetZ);
        // 4. Calculate Final Target World Position
        const targetWorldPos = shoulderWorldPos.add(worldOffset);
        // 5. Convert World Position to Local Position (Relative to Target's Parent)
        const parentWorldMatrix = targetNode.parent ? targetNode.parent.getWorldMatrix() : Matrix.Identity();
        const invParentWorldMatrix = Matrix.Invert(parentWorldMatrix);
        Vector3.TransformCoordinatesToRef(targetWorldPos, invParentWorldMatrix, targetNode.position);
        // Optional Debugging Log
        if (this.debugMode) {
           console.log(`[ArmController] ${targetName}: ShoulderW=${shoulderWorldPos.toString()}`);
           console.log(`  LandmarkDelta=(${landmarkDeltaX.toFixed(2)}, ${landmarkDeltaY.toFixed(2)}, ${landmarkDeltaZ.toFixed(2)})`);
           console.log(`  WorldOffset=${worldOffset.toString()}`);
           console.log(`  TargetWorld=${targetWorldPos.toString()}`);
           console.log(`  TargetLocal=${targetNode.position.toString()}`);
        }
        if(Math.abs(targetNode.position.x) > 50 || Math.abs(targetNode.position.y) > 50 || Math.abs(targetNode.position.z) > 50) {
            console.warn(`!! ${targetName} frame update local position seems excessively large !!`);
        }
    }
    /**
     * Gets the TRUE WORLD position of a bone.
     * https://playground.babylonjs.com/#YGSAZJ#7
     * https://forum.babylonjs.com/t/bone-getabsoluteposition-not-working-correctly/14218/3
     */
    getBoneWorldPosition(bone) {
        if (!bone) return Vector3.Zero(); // Return zero vector if bone is invalid
         // Ensure the bone's matrix is current
        return bone.getPosition(Space.WORLD, this.character); // Use the built-in method
        // const transformNode = bone.getTransformNode(); // Get the TransformNode linked to the bone
        // // console.log(`[ArmController] Bone ${bone.name} TransformNode:`, transformNode);
        // return transformNode.getAbsolutePosition(); // This should give the world position directly
    }
    /**
     * lifted from SkeletonViewer
     */
    _getBonePosition(position, bone, meshMat, x = 0, y = 0, z = 0){
        const parentBone = bone.getParent();
        const tmat = bone.getLocalMatrix().clone();

        if (x !== 0 || y !== 0 || z !== 0) {
            const tmat2 = BABYLON.Matrix.Identity();
            tmat2.setTranslationFromFloats(x, y, z);
            tmat2.multiplyToRef(tmat, tmat);
        }

        if (parentBone) {
            tmat.multiplyToRef(parentBone.getAbsoluteTransform(), tmat);
        }

        tmat.multiplyToRef(meshMat, tmat);

        position.x = tmat.m[12];
        position.y = tmat.m[13];
        position.z = tmat.m[14];
    }

    // --- Debugging Methods ---
    createBoneAxesViewers() {
        if (!this.skeleton || !this.character) return;
        this.skeleton.computeAbsoluteMatrices(true); // Force computation
        this.disposeBoneAxesViewers(); // Clean previous ones

        // var root = container.transformNodes.filter(m => m.name === rootName)[0];

        const createViewer = (bone, name) => {
            // Add extra validation for the bone object itself
            if (bone && bone instanceof Bone) { // Check if it's actually a Bone
                console.log(`  Attempting to create viewer for ${name} (Bone: ${bone.name}, ID: ${bone.getIndex()})`);
                try {
                    this.boneAxesViewers[name] = new Debug.BoneAxesViewer(this.scene, bone, this.character, 0.6); // Added scale parameter
                     console.log(`    Viewer created for ${name}. Associated mesh: ${this.boneAxesViewers[name].mesh?.name}`); // Check if viewer has a mesh
                } catch (error) {
                    console.error(`    Error creating BoneAxesViewer for ${name}:`, error);
                }
            } else {
                 console.warn(`  Skipped viewer for ${name} (Bone object is invalid or not found)`);
            }
        };
        createViewer(this.bones.leftArm.upperArm, 'leftUpperArm');
        createViewer(this.bones.leftArm.foreArm, 'leftForeArm');
        createViewer(this.bones.leftArm.hand, 'leftHand');
        createViewer(this.bones.rightArm.upperArm, 'rightUpperArm');
        createViewer(this.bones.rightArm.foreArm, 'rightForeArm');
        createViewer(this.bones.rightArm.hand, 'rightHand');
        // Optional: Create a viewer for the pole target (if needed)
        // Object.values(this.boneAxesViewers).forEach(viewer => viewer?.update());
    }
    disposeBoneAxesViewers() {
        Object.values(this.boneAxesViewers).forEach(viewer => viewer?.dispose());
        this.boneAxesViewers = {};
    }
    // --- Update and Control ---
    update() {
        // Update IK controllers - this performs the calculation
        if (this.ikControllers.leftArm) {
            this.ikControllers.leftArm.update();
        }
        if (this.ikControllers.rightArm) {
            this.ikControllers.rightArm.update();
        }
        // Update bone axes viewers ONLY if debug mode is on
        // Note: BoneAxesViewer usually updates automatically when attached correctly.
        // Manual update might be needed in specific scenarios or older Babylon versions.
        // Let's rely on automatic update first. Remove manual update if redundant.
        // if (this.debugMode) {
        //     Object.values(this.boneAxesViewers).forEach(viewer => viewer?.update());
        // }
    }
    setDebugMode(enabled) {
        this.debugMode = enabled;
        console.log(`[ArmController] Debug mode set to ${enabled}`);
        // Toggle visibility of IK target visuals
        Object.values(this.targets).forEach(targetNode => {
            const visual = targetNode.getChildMeshes()[0]; // Assumes visual is the first child
            if (visual) {
                visual.setEnabled(enabled);
            }
        });
        // Create or dispose bone axes viewers
        if (enabled) {
            this.createBoneAxesViewers();
        } else {
            this.disposeBoneAxesViewers();
        }
    }
    // --- Cleanup ---
    dispose() {
        console.log(`[ArmController] Disposing for ${this.character.name}`);
        // Dispose IK controllers (Babylon doesn't have explicit dispose, just remove refs)
        this.ikControllers = {};
        // Dispose target nodes and their visuals
        Object.values(this.targets).forEach(targetNode => {
            targetNode.dispose(); // Disposes node and children (visuals)
        });
        this.targets = {};
        // Dispose bone axes viewers
        this.disposeBoneAxesViewers();
        // Nullify references
        this.scene = null;
        this.character = null;
        this.skeleton = null;
        this.boneMap = null;
        this.bones = null;
    }
}

export default ArmController;
