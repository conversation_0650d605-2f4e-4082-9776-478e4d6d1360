import ActionDetector from './actionDetector.js';
import { KalmanFilter3D } from '../../../src/utils/KalmanFilter.js';

class AnimationController {
    constructor(scene, character) {
        this.scene = scene;
        this.character = character;
        this.currentAnimation = null;
        this.animationGroups = {};
        this.defaultAnimation = "idle";
        this.isPlaying = false;

        // Action detection properties
        this.actionDetector = new ActionDetector();
        this.lastActionTime = {};
        this.actionCooldown = 700; // 动作检测间隔时间（毫秒）

        // Add gesture state tracking with <PERSON><PERSON> filtering
        this.gestureFilters = {
            rightPunch: new KalmanFilter3D(0, 0, 0, 0.01, 0.5),
            leftPunch: new KalmanFilter3D(0, 0, 0, 0.01, 0.5),
            block: new KalmanFilter3D(0, 0, 0, 0.01, 0.5)
        };
        this.gestureStates = {
            rightPunch: { active: false, startTime: 0, confidence: 0 },
            leftPunch: { active: false, startTime: 0, confidence: 0 },
            block: { active: false, startTime: 0, confidence: 0 }
        };
        // Thresholds for gesture detection
        this.gestureThresholds = {
            activation: 0.7,  // Confidence threshold to activate gesture
            deactivation: 0.3 // Confidence threshold to deactivate gesture
        };

        this.gestureConfirmTime = 100; // Minimum active time in milliseconds

        this.setupAnimations();
        this.playAnimation(this.defaultAnimation);
    }

    setupAnimations() {
        // Instead of using skeleton.animations, use the animation groups loaded by the AssetManager
        const animationGroups = this.character.metadata?.animationGroups;
        if (!animationGroups || animationGroups.length === 0) {
            console.warn('[AnimationController] No animations available for character');
            return;
        }

        // Iterate over each AnimationGroup, normalize it, and store it in a mapping using a lower-case key
        animationGroups.forEach(ag => {
            // Normalize the animation group for consistent timing
            const name = ag.name.toLowerCase();
            const actionsToNormalize = ['block', 'leftPunch', 'rightPunch'];
            if (actionsToNormalize.includes(name)) {
                ag.normalize(0, 100);
            }
            // Store using the animation group's name in lower-case to avoid case-sensitivity issues
            this.animationGroups[name] = ag;
            // console.log(`[AnimationController] Found animation group: ${ag.name}`);
        });

        console.log(animationGroups);

        // Set default animation to 'idle' if available; otherwise, fallback to the first available group
        if (this.animationGroups['idle']) {
            this.defaultAnimation = 'idle';
        } else {
            const keys = Object.keys(this.animationGroups);
            if (keys.length > 0) {
                this.defaultAnimation = keys[0];
                console.log(`[AnimationController] 'idle' animation not found. Using default: ${this.defaultAnimation}`);
            } else {
                console.warn('[AnimationController] No animations available for character');
            }
        }
    }

    playAnimation(name, loop = true) {
        if (!this.animationGroups[name]) {
            console.warn(`[AnimationController] Animation not found: ${name}`);
            return false;
        }

        // 停止当前动画
        if (this.currentAnimation && this.animationGroups[this.currentAnimation].isPlaying) {
            this.animationGroups[this.currentAnimation].stop();
        }
        // console.log(`[AnimationController] Stopped animation: ${this.currentAnimation}`);
        // 播放新动画
        this.animationGroups[name].play(loop);
        this.currentAnimation = name;
        this.isPlaying = true;
        console.log(`[AnimationController] Playing animation: ${name}`);
        return true;
    }

    stopAnimation() {
        if (this.currentAnimation && this.animationGroups[this.currentAnimation]) {
            this.animationGroups[this.currentAnimation].stop();
            this.isPlaying = false;
        }
    }

    resetToDefault() {
        this.playAnimation(this.defaultAnimation);
    }

    triggerAnimation(animationName, duration = 1000) {
        animationName = animationName.toLowerCase();
        console.log(`[AnimationController] Triggering animation: ${animationName}`);
        if (!this.animationGroups[animationName]) {
            console.warn(`[AnimationController] Animation not found: ${animationName}`);
            return false;
        }

        // 播放指定动画
        this.playAnimation(animationName, false);

        // 设置动画结束后恢复默认动画
        setTimeout(() => {
            if (this.currentAnimation === animationName) {
                this.resetToDefault();
            }
        }, duration);

        return true;
    }

    // setupAnimationTriggers() {
    //     // 在场景渲染前监听姿态数据
    //     this.scene.onBeforeRenderObservable.add(() => {
    //         if (window.poseResults && window.poseResults.worldLandmarks &&
    //             window.poseResults.worldLandmarks.length > 0) {
    //             this.processPoseResults(window.poseResults);
    //         }
    //     });
    // }
    // Add a new public method to receive pose data directly
    updateWithPoseData(poseResults) {
        if (poseResults.poseLandmarks && poseResults.poseLandmarks.length > 0) {
            this.processPoseResults(poseResults);
        }
    }
    processPoseResults(poseResults) {
        const landmarks = poseResults.poseLandmarks[0];
        const now = Date.now();

        // Check if landmarks exist and have visibility data
        if (!landmarks) {
            return;
        }

        // Minimum visibility threshold for considering landmarks
        const minVisibility = 0.7;

        // FIXED: Swap left/right detection to account for camera mirroring
        // User's right hand appears as left in the camera
        const userRightPunchDetected = this.actionDetector.detectPunch(landmarks, 'left', minVisibility);
        this._updateGestureState('rightPunch', userRightPunchDetected, now);

        // User's left hand appears as right in the camera
        const userLeftPunchDetected = this.actionDetector.detectPunch(landmarks, 'right', minVisibility);
        this._updateGestureState('leftPunch', userLeftPunchDetected, now);

        // Block detection doesn't need swapping (it's centered)
        const blockDetected = this.actionDetector.detectblock(landmarks, minVisibility);
        this._updateGestureState('block', blockDetected, now);
    }
    _updateGestureState(gesture, detected, currentTime) {
        const state = this.gestureStates[gesture];
        const filter = this.gestureFilters[gesture];

        // Convert boolean detection to numeric confidence
        const detectionValue = detected ? 1.0 : 0.0;

        // Apply Kalman filtering to smooth the detection value
        filter.update(detectionValue, 0, 0);
        const filteredValue = filter.predict()[0];

        // Store filtered confidence value
        state.confidence = filteredValue;

        // Apply hysteresis thresholding to avoid rapid toggling
        if (!state.active && filteredValue > this.gestureThresholds.activation) {
            // Gesture became active
            state.active = true;
            state.startTime = currentTime;
        } else if (state.active && filteredValue < this.gestureThresholds.deactivation) {
            // Gesture became inactive
            state.active = false;
        }

        // Check if gesture has been active long enough and cooldown has passed
        if (state.active &&
            (currentTime - state.startTime >= this.gestureConfirmTime) &&
            this._checkCooldown(gesture, currentTime)) {

            // Trigger the action
            this._triggerAction(gesture, currentTime);

            // Reset state to prevent multiple triggers
            state.active = false;
        }
    }
    _checkCooldown(action, currentTime) {
        // 检查动作冷却时间
        return !this.lastActionTime[action] ||
            (currentTime - this.lastActionTime[action]) > this.actionCooldown;
    }

    _triggerAction(actionName, currentTime) {
        // 获取映射的动画名称
        this.triggerAnimation(actionName);
        this.lastActionTime[actionName] = currentTime;
    }
}


export { AnimationController };
export default AnimationController;