import { Vector3, Quaternion } from '@babylonjs/core';
import { MEDIAPIPE_LANDMARKS } from '../configs/mediapipeLandmarks.js';

class BoneMapper {
    // Use imported landmarks instead of defining them here
    static LANDMARK_INDICES = MEDIAPIPE_LANDMARKS;

    // Add chain landmark indices mapping
    static CHAIN_LANDMARK_INDICES = {
        leftArm: [11, 13, 15], // shoulder, elbow, wrist
        rightArm: [12, 14, 16],
        spine: [11, 12, 23, 24], // shoulders and hips
        leftLeg: [23, 25, 27, 31], // hip, knee, ankle, foot
        rightLeg: [24, 26, 28, 32],
        leftHand: [15, 17, 19, 21], // wrist to finger tips
        rightHand: [16, 18, 20, 22]
    };

    // Update chain definitions to use the imported landmarks
    static CHAIN_DEFINITIONS = {
        leftArm: {
            joints: ['leftShoulder', 'leftElbow', 'leftWrist', 'leftHand'],
            landmarks: [
                MEDIAPIPE_LANDMARKS.LEFT_SHOULDER,
                MEDIAPIPE_LANDMARKS.LEFT_ELBOW,
                MEDIAPIPE_LANDMARKS.LEFT_WRIST,
                MEDIAPIPE_LANDMARKS.LEFT_PINKY
            ],
            constraints: [{
                minAngle: -90,
                maxAngle: 90,
                axis: new Vector3(-1, 0, 0)
            }, {
                minAngle: 0,
                maxAngle: 145,
                axis: new Vector3(0, -1, 0)
            }],
            mapping: {
                SHOULDER: { name: 'mixamorig:LeftShoulder' },
                UPPER_ARM: { name: 'mixamorig:LeftArm' },
                FOREARM: { name: 'mixamorig:LeftForeArm' },
                HAND: { name: 'mixamorig:LeftHand' }
            }
        },
        rightArm: {
            joints: ['rightShoulder', 'rightElbow', 'rightWrist', 'rightHand'],
            landmarks: [
                MEDIAPIPE_LANDMARKS.RIGHT_SHOULDER,
                MEDIAPIPE_LANDMARKS.RIGHT_ELBOW,
                MEDIAPIPE_LANDMARKS.RIGHT_WRIST,
                MEDIAPIPE_LANDMARKS.RIGHT_PINKY
            ],
            constraints: [{
                minAngle: -90,
                maxAngle: 90,
                axis: new Vector3(1, 0, 0)
            }, {
                minAngle: 0,
                maxAngle: 145,
                axis: new Vector3(0, 1, 0)
            }],
            mapping: {
                SHOULDER: { name: 'mixamorig:RightShoulder' },
                UPPER_ARM: { name: 'mixamorig:RightArm' },
                FOREARM: { name: 'mixamorig:RightForeArm' },
                HAND: { name: 'mixamorig:RightHand' }
            }
        },
        spine: {
            joints: ['hips', 'spine', 'spine1', 'spine2', 'neck', 'head'],
            landmarks: [
                MEDIAPIPE_LANDMARKS.LEFT_HIP,
                MEDIAPIPE_LANDMARKS.RIGHT_HIP,
                MEDIAPIPE_LANDMARKS.LEFT_SHOULDER,
                MEDIAPIPE_LANDMARKS.RIGHT_SHOULDER,
                MEDIAPIPE_LANDMARKS.NOSE
            ],
            interpolated: true,
            constraints: [{
                minAngle: -Math.PI * 0.2,
                maxAngle: Math.PI * 0.2,
                axis: new Vector3(1, 0, 0)
            }],
            mapping: {
                HIPS: { name: 'mixamorig:Hips' },
                SPINE: { name: 'mixamorig:Spine' },
                SPINE1: { name: 'mixamorig:Spine1' },
                SPINE2: { name: 'mixamorig:Spine2' },
                NECK: { name: 'mixamorig:Neck' },
                HEAD: { name: 'mixamorig:Head' }
            }
        },
        leftHand: {
            joints: ['leftWrist', 'leftIndex', 'leftMiddle', 'leftPinky'],
            landmarks: [15, 17, 19, 21], // Left hand chain including wrist
            constraints: [{
                minAngle: -45,
                maxAngle: 45,
                axis: new Vector3(0, 0, 1)
            }],
            mapping: {
                WRIST: { name: 'mixamorig:LeftHand' },
                INDEX: { name: 'mixamorig:LeftHandIndex1' },
                MIDDLE: { name: 'mixamorig:LeftHandMiddle1' },
                PINKY: { name: 'mixamorig:LeftHandPinky1' }
            }
        },
        rightHand: {
            joints: ['rightWrist', 'rightIndex', 'rightMiddle', 'rightPinky'],
            landmarks: [16, 18, 20, 22], // Right hand chain including wrist
            constraints: [{
                minAngle: -45,
                maxAngle: 45,
                axis: new Vector3(0, 0, 1)
            }],
            mapping: {
                WRIST: { name: 'mixamorig:RightHand' },
                INDEX: { name: 'mixamorig:RightHandIndex1' },
                MIDDLE: { name: 'mixamorig:RightHandMiddle1' },
                PINKY: { name: 'mixamorig:RightHandPinky1' }
            }
        },
        leftLeg: {
            joints: ['leftHip', 'leftKnee', 'leftAnkle', 'leftFoot'],
            landmarks: [
                MEDIAPIPE_LANDMARKS.LEFT_HIP,
                MEDIAPIPE_LANDMARKS.LEFT_KNEE,
                MEDIAPIPE_LANDMARKS.LEFT_ANKLE,
                MEDIAPIPE_LANDMARKS.LEFT_FOOT_INDEX
            ],
            constraints: [{
                minAngle: -90,
                maxAngle: 45,
                axis: new Vector3(-1, 0, 0)
            }, {
                minAngle: 0,
                maxAngle: 145,
                axis: new Vector3(0, -1, 0)
            }],
            mapping: {
                HIP: { name: 'mixamorig:LeftUpLeg' },
                KNEE: { name: 'mixamorig:LeftLeg' },
                ANKLE: { name: 'mixamorig:LeftFoot' },
                FOOT: { name: 'mixamorig:LeftToeBase' }
            }
        },
        rightLeg: {
            joints: ['rightHip', 'rightKnee', 'rightAnkle', 'rightFoot'],
            landmarks: [
                MEDIAPIPE_LANDMARKS.RIGHT_HIP,
                MEDIAPIPE_LANDMARKS.RIGHT_KNEE,
                MEDIAPIPE_LANDMARKS.RIGHT_ANKLE,
                MEDIAPIPE_LANDMARKS.RIGHT_FOOT_INDEX
            ],
            constraints: [{
                minAngle: -90,
                maxAngle: 45,
                axis: new Vector3(1, 0, 0)
            }, {
                minAngle: 0,
                maxAngle: 145,
                axis: new Vector3(0, 1, 0)
            }],
            mapping: {
                HIP: { name: 'mixamorig:RightUpLeg' },
                KNEE: { name: 'mixamorig:RightLeg' },
                ANKLE: { name: 'mixamorig:RightFoot' },
                FOOT: { name: 'mixamorig:RightToeBase' }
            }
        }
    };

    static HAND_CONNECTIONS = [
        [0, 1], [1, 2], [2, 3], [3, 4],           // Thumb
        [0, 5], [5, 6], [6, 7], [7, 8],           // Index
        [0, 9], [9, 10], [10, 11], [11, 12],      // Middle
        [0, 13], [13, 14], [14, 15], [15, 16],    // Ring
        [0, 17], [17, 18], [18, 19], [19, 20],    // Pinky
        [0, 17], [0, 13], [0, 9], [0, 5]          // Palm
    ];

    // Add POSE_CONNECTIONS for MediaPipe pose landmarks
    static POSE_CONNECTIONS = [
        // Spine and shoulders
        [11, 12], // Left shoulder to right shoulder
        [11, 23], // Left shoulder to left hip
        [12, 24], // Right shoulder to right hip
        [23, 24], // Left hip to right hip

        // Arms
        [11, 13], [13, 15], // Left arm
        [12, 14], [14, 16], // Right arm

        // Legs
        [23, 25], [25, 27], [27, 31], // Left leg
        [24, 26], [26, 28], [28, 32], // Right leg
    ];

    // Add getter for hand connections
    static getHandConnections() {
        return this.HAND_CONNECTIONS;
    }

    // Alternative names remain unchanged...
    static ALTERNATIVE_NAMES = {
        LEFT_SHOULDER: ['leftShoulder', 'l_shoulder', 'l.shoulder'],
        RIGHT_SHOULDER: ['rightShoulder', 'r_shoulder', 'r.shoulder'],
    };

    static findExactBoneMatch(boneName, candidates) {
        if (!candidates) {
            console.warn('[BoneMapper] No candidates provided for bone:', boneName);
            return null;
        }

        const normalizedName = boneName.replace(/^mixamorig:/i, '').toLowerCase();
        const candidateArray = Array.isArray(candidates) ? candidates :
            Object.values(candidates).map(c => typeof c === 'string' ? c : c.name);

        for (const candidate of candidateArray) {
            if (typeof candidate !== 'string') continue;
            const normalizedCandidate = candidate.replace(/^mixamorig:/i, '').toLowerCase();
            if (normalizedName === normalizedCandidate) {
                return boneName;
            }
        }
        return null;
    }

    static findBestMatchingBone(skeleton, targetKey, preferredNames) {
        if (!preferredNames) {
            console.warn('[BoneMapper] No preferred names provided for:', targetKey);
            return null;
        }

        const candidateArray = Array.isArray(preferredNames) ?
            preferredNames :
            Object.values(preferredNames).map(p => typeof p === 'string' ? p : p.name).flat();

        // Try exact matches first
        for (const bone of skeleton.bones) {
            const exactMatch = this.findExactBoneMatch(bone.name, candidateArray);
            if (exactMatch) {
                console.log('[BoneMapper] Found exact match:', {
                    targetKey,
                    match: exactMatch
                });
                return exactMatch;
            }
        }

        // Try alternatives
        const alternatives = this.ALTERNATIVE_NAMES[targetKey] || [];
        for (const bone of skeleton.bones) {
            const altMatch = this.findExactBoneMatch(bone.name, alternatives);
            if (altMatch) return altMatch;
        }

        console.warn(`[BoneMapper] No match found for ${targetKey}`);
        return null;
    }

    static createSkeletonMap(skeleton) {
        if (!skeleton?.bones?.length) {
            console.error('[BoneMapper] Invalid skeleton provided');
            return null;
        }

        const mapping = {
            bones: {},
            physics: {},
            chains: {},
            metadata: {
                interpolatedChains: new Set(),
                physicsProperties: new Map()
            }
        };

        // Map each bone chain
        Object.entries(this.CHAIN_DEFINITIONS).forEach(([chainName, chainDef]) => {
            const chainMapping = {};
            Object.entries(chainDef.mapping).forEach(([part, boneData]) => {
                const key = `${chainName.toLowerCase()}_${part.toLowerCase()}`;
                const boneName = this.findBestMatchingBone(skeleton, key, [boneData.name]);

                if (boneName) {
                    chainMapping[part] = boneName;
                    mapping.physics[boneName] = {
                        mass: boneData.mass,
                        friction: boneData.friction,
                        restitution: boneData.restitution
                    };
                }
            });

            if (Object.keys(chainMapping).length > 0) {
                mapping.chains[chainName] = chainMapping;
            }
        });

        // Set up interpolated bones and chain metadata
        this.setupInterpolatedBones(mapping, skeleton);

        return mapping;
    }

    static setupInterpolatedBones(mapping, skeleton) {
        // Find and sort spine chain
        const spineChain = skeleton.bones
            .filter(bone => bone.name.toLowerCase().includes('spine'))
            .sort((a, b) => {
                const aNum = parseInt(a.name.replace(/\D/g, '') || '0');
                const bNum = parseInt(b.name.replace(/\D/g, '') || '0');
                return aNum - bNum;
            });

        if (spineChain.length > 0) {
            mapping.chains.SPINE = {
                bones: spineChain.map(bone => bone.name),
                interpolated: true,
                properties: this.CHAIN_DEFINITIONS.spine
            };
        }

        // Add metadata for chain configurations
        mapping.metadata.interpolatedChains = new Set(
            Object.entries(this.CHAIN_DEFINITIONS)
                .filter(([_, config]) => config.interpolated)
                .map(([name]) => name)
        );
    }

    static getChainLandmarks(chainName) {
        return this.CHAIN_LANDMARK_INDICES[chainName] ||
            this.CHAIN_DEFINITIONS[chainName]?.landmarks ||
            [];
    }

    static getChainConstraints(chainName) {
        return this.CHAIN_DEFINITIONS[chainName]?.constraints || [];
    }

    static getCollisionVolume(chainName) {
        return this.CHAIN_DEFINITIONS[chainName]?.collisionVolume;
    }

    static getLandmarkIndicesForChain(chainName) {
        return this.CHAIN_LANDMARK_INDICES[chainName] || null;
    }

    static getInterpolatedPosition(landmarks, indices, weights = null) {
        if (!landmarks || !indices?.length) return null;

        if (!weights) {
            weights = new Array(indices.length).fill(1 / indices.length);
        }

        const position = new Vector3(0, 0, 0);
        let totalWeight = 0;

        indices.forEach((index, i) => {
            if (landmarks[index]?.visibility > 0.5) {
                position.addInPlace(new Vector3(
                    landmarks[index].x * weights[i],
                    landmarks[index].y * weights[i],
                    landmarks[index].z * weights[i]
                ));
                totalWeight += weights[i];
            }
        });

        return totalWeight > 0 ? position.scaleInPlace(1 / totalWeight) : null;
    }

}

export default BoneMapper;
