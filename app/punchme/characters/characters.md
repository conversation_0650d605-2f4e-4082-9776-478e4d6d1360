# Character Module Documentation

## Overview
The character module manages character handling in the hologram combat system, providing a modular architecture with clean separation of concerns through dependency injection. It handles character loading, animation, bone mapping, inverse kinematics, and state management.

## Core Components

### CharacterManager (`characterManager.js`)
- Loads and manages character models and skeletons
- Handles character scaling, positioning, and transformations
- Manages multiple character instances
- Sets up physics colliders
- Provides character transparency control
- <PERSON>les multi-part character loading

### BoneMapper (`boneMapper.js`)
- Maps MediaPipe landmarks to Mixamo skeleton bones
- Provides fallback bone name matching
- Handles alternative bone naming conventions
- Supports spine chain interpolation
- Validates bone mappings

### CharacterController (`characterController.js`)
- Manages character skeletal animation
- Implements IK (Inverse Kinematics) for pose updates
- Handles bone rotations and transformations
- Provides debug visualization
- Processes MediaPipe pose data

### IKSolver (`inverseKinematics.js`)
- Implements FABRIK algorithm for IK chains
- Handles joint constraints and rotations
- Provides bone chain solving
- Supports multiple iteration passes
- Implements position tolerance checks

### GameStateManager (`gameStateManager.js`)
- Manages character health states
- Handles game win/loss conditions
- Updates UI with character status
- Manages game state transitions

## Technical Details

### Bone Mapping
- Supports Mixamo standard bone names
- Handles alternative naming conventions
- Provides fallback matching for non-standard skeletons
- Maps MediaPipe landmarks to character bones

### Inverse Kinematics
- FABRIK algorithm implementation
- Joint chain solving
- Rotation constraints
- Position tolerance
- Multiple iteration support

### Character Loading
- GLB/GLTF model support
- Multi-part character handling
- Automatic scaling
- Physics collider setup
- Transparent character support

### Animation System
- Pose-based animation
- One Euro Filter smoothing
- IK chain updates
- Bone rotation interpolation
- Debug visualization

### Debug Features
- Visual bone chain debugging
- Pose visualization
- Skeleton mapping validation
- Comprehensive logging
- Performance monitoring

## Best Practices
- Always validate skeleton mappings before use
- Implement proper error handling for model loading
- Use dependency injection for better testing
- Monitor performance with debug tools
- Handle character scaling consistently

## Future Improvements
- Add animation state machine
- Implement advanced IK constraints
- Add support for custom rigs
- Improve performance with worker threads
- Add animation blending system

## Usage Examples
### Bone Mapping Example
```javascript
// Create a skeleton map from a loaded character
const mapping = BoneMapper.createSkeletonMap(character.skeleton);
console.log("Skeleton mapping:", mapping);
```
### IK Chain Example
```javascript
// Define an IK chain for an arm and solve using FABRIK
const joints = [shoulderPosition, elbowPosition, wristPosition];
const boneLengths = [
  Vector3.Distance(shoulderPosition, elbowPosition),
  Vector3.Distance(elbowPosition, wristPosition)
];
ikSolver.solveFABRIK(joints, wristPosition, boneLengths);
```
