import { Vector3 } from '@babylonjs/core';
import { MEDIAPIPE_LANDMARKS } from '../configs/mediapipeLandmarks.js';

class ActionDetector {
    constructor() {
        // 定义动作阈值
        this.thresholds = {
            punch: {
                minWristDistance: 0.4,    // 手腕距离肩膀的最小距离
                maxElbowAngle: 130,       // 手臂伸直时的最大角度
                minForwardDisplacement: 0.2 // 前向位移最小值
            },
            block: {
                minElbowAngle: 70,        // 格挡时手肘最小角度
                maxElbowAngle: 110,       // 格挡时手肘最大角度
                minHeightRatio: 1.1       // 手部相对肩膀的最小高度比例
            }
        };
    }

    // 检测出拳动作
    detectPunch(landmarks, side = 'left', minVisibility = 0.5) {
        if (!landmarks || landmarks.length < 33) return false;

        // Get landmark indices
        const shoulderIndex = side === 'left' ?
            MEDIAPIPE_LANDMARKS.LEFT_SHOULDER :
            MEDIAPIPE_LANDMARKS.RIGHT_SHOULDER;
        const elbowIndex = side === 'left' ?
            MEDIAPIPE_LANDMARKS.LEFT_ELBOW :
            MEDIAPIPE_LANDMARKS.RIGHT_ELBOW;
        const wristIndex = side === 'left' ?
            MEDIAPIPE_LANDMARKS.LEFT_WRIST :
            MEDIAPIPE_LANDMARKS.RIGHT_WRIST;

        const shoulder = landmarks[shoulderIndex];
        const elbow = landmarks[elbowIndex];
        const wrist = landmarks[wristIndex];

        // Check visibility first
        if (!this._arePointsVisible([shoulder, elbow, wrist], minVisibility)) {
            return false;
        }

        // Enhanced validation - check if all required points exist and have valid coordinates
        if (!this._areValidPoints([shoulder, elbow, wrist])) {
            console.warn('[ActionDetector] Invalid points for punch detection');
            return false;
        }

        // 1. Calculate distance from wrist to shoulder
        const wristToShoulderDist = this._calculateDistance(wrist, shoulder);

        // 2. Calculate elbow angle with error handling
        let elbowAngle = 0;
        try {
            elbowAngle = this._calculateAngle(shoulder, elbow, wrist);
        } catch (error) {
            console.warn('[ActionDetector] Failed to calculate elbow angle:', error);
            return false;
        }

        // 3. Calculate forward displacement - REVERSED direction to match coordinate system
        // In MediaPipe, smaller Z values are closer to camera, so subtract wrist from shoulder
        const forwardDisplacement = shoulder.z - wrist.z;

        // Log debugging info
        // console.log(`[Punch Detection ${side}] Distance: ${wristToShoulderDist.toFixed(2)} (min: ${this.thresholds.punch.minWristDistance})`);
        // console.log(`[Punch Detection ${side}] Elbow angle: ${elbowAngle.toFixed(2)}° (min: ${this.thresholds.punch.maxElbowAngle}°)`);
        // console.log(`[Punch Detection ${side}] Forward displacement: ${forwardDisplacement.toFixed(2)} (min: ${this.thresholds.punch.minForwardDisplacement})`);

        // Check conditions
        const distanceCheck = wristToShoulderDist >= this.thresholds.punch.minWristDistance;
        const angleCheck = elbowAngle >= this.thresholds.punch.maxElbowAngle;
        const displacementCheck = forwardDisplacement >= this.thresholds.punch.minForwardDisplacement;

        const result = distanceCheck && angleCheck && displacementCheck;
        // console.log(`[Punch Detection ${side}] Result: ${result}`);

        return result;
    }

    // 检测中央格挡动作（双手在胸前交叉）
    detectblock(landmarks) {
        if (!landmarks || landmarks.length < 33) return false;

        // 获取左右手腕和肘部的关键点
        const leftWrist = landmarks[MEDIAPIPE_LANDMARKS.LEFT_WRIST];
        const rightWrist = landmarks[MEDIAPIPE_LANDMARKS.RIGHT_WRIST];
        const leftElbow = landmarks[MEDIAPIPE_LANDMARKS.LEFT_ELBOW];
        const rightElbow = landmarks[MEDIAPIPE_LANDMARKS.RIGHT_ELBOW];
        const neck = landmarks[MEDIAPIPE_LANDMARKS.NOSE]; // 用鼻子作为头部/颈部参考点

        // 检查可见性
        if (!this._arePointsVisible([leftWrist, rightWrist, leftElbow, rightElbow, neck])) {
            // console.warn('[ActionDetector] Both two hands and neck should be visible for block action detection.');
            return false;
        }

        // 1. 检查两手是否都在身体前方
        const handsInFront = leftWrist.z < neck.z && rightWrist.z < neck.z;

        // 2. 检查手腕是否在适当高度（胸部附近）
        const properHeight =
            (leftWrist.y > neck.y * 0.7) && (leftWrist.y < neck.y * 1.2) &&
            (rightWrist.y > neck.y * 0.7) && (rightWrist.y < neck.y * 1.2);

        // 3. 检查手是否相对接近（中央格挡）
        const wristDistance = this._calculateDistance(leftWrist, rightWrist);
        const closeWrists = wristDistance < 0.3; // 手腕距离阈值

        return handsInFront && properHeight && closeWrists;
    }

    // 辅助方法：计算两点之间的距离
    _calculateDistance(point1, point2) {
        // Add validation to prevent errors with invalid points
        if (!this._isValidPoint(point1) || !this._isValidPoint(point2)) {
            return 0;
        }

        return Math.sqrt(
            Math.pow(point2.x - point1.x, 2) +
            Math.pow(point2.y - point1.y, 2) +
            Math.pow(point2.z - point1.z, 2)
        );
    }

    // 辅助方法：计算角度
    _calculateAngle(point1, point2, point3) {
        // Add validation to prevent errors with invalid points
        if (!this._isValidPoint(point1) || !this._isValidPoint(point2) || !this._isValidPoint(point3)) {
            console.warn('[ActionDetector] Invalid points for angle calculation');
            return 0;
        }

        try {
            const vector1 = new Vector3(
                point1.x - point2.x,
                point1.y - point2.y,
                point1.z - point2.z
            );

            const vector2 = new Vector3(
                point3.x - point2.x,
                point3.y - point2.y,
                point3.z - point2.z
            );

            // Check if vectors have zero length - can't calculate angle in that case
            if (vector1.length() === 0 || vector2.length() === 0) {
                console.warn('[ActionDetector] Zero length vector detected, cannot calculate angle');
                return 0;
            }

            // Normalize vectors to avoid precision issues
            const normalizedVector1 = vector1.normalize();
            const normalizedVector2 = vector2.normalize();

            // Calculate angle using dot product: cos(angle) = dot(v1,v2)/(|v1|*|v2|)
            // Since vectors are already normalized, |v1| = |v2| = 1
            const dotProduct = Vector3.Dot(normalizedVector1, normalizedVector2);

            // Clamp the value to avoid errors from floating point precision
            const clampedDot = Math.max(-1, Math.min(1, dotProduct));
            const angle = Math.acos(clampedDot) * (180 / Math.PI);

            return angle;
        } catch (error) {
            console.error('[ActionDetector] Error calculating angle:', error);
            return 0;
        }
    }

    // 辅助方法：检查关键点可见性
    _arePointsVisible(points, minVisibility = 0.5) {
        return points.every(point =>
            point &&
            typeof point.visibility === 'number' &&
            point.visibility >= minVisibility
        );
    }

    // New helper method: Check if a point has valid x, y, z coordinates
    _isValidPoint(point) {
        return point &&
            typeof point.x === 'number' && !isNaN(point.x) &&
            typeof point.y === 'number' && !isNaN(point.y) &&
            typeof point.z === 'number' && !isNaN(point.z);
    }

    // New helper method: Check if all points in an array are valid
    _areValidPoints(points) {
        return points.every(point => this._isValidPoint(point));
    }
}

export default ActionDetector;