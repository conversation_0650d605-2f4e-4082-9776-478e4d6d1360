<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LLM Invoke Debug Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f0f0f0;
        }

        .debug-panel {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .log-output {
            background: #1e1e1e;
            color: #e0e0e0;
            padding: 15px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
        }

        button {
            background: #007acc;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }

        button:hover {
            background: #005999;
        }

        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }

        .status.success {
            background: #d4edda;
            color: #155724;
        }

        .status.error {
            background: #f8d7da;
            color: #721c24;
        }

        .status.info {
            background: #d1ecf1;
            color: #0c5460;
        }
    </style>
</head>

<body>
    <h1>🧪 LLM Invoke Debug Test</h1>
    <p>This page tests the LLM invoke flow in the actual browser environment.</p>

    <div class="debug-panel">
        <h3>Debug Controls</h3>
        <button onclick="runFullDebugTest()">🚀 Run Full Debug Test</button>
        <button onclick="testDirectLLMRoute()">🔄 Test /llm Route Directly</button>
        <button onclick="testModelCreation()">🏗️ Test Model Creation</button>
        <button onclick="clearLogs()">🧹 Clear Logs</button>
    </div>

    <div class="debug-panel">
        <h3>Test Status</h3>
        <div id="status" class="status info">Ready to test...</div>
    </div>

    <div class="debug-panel">
        <h3>Debug Logs</h3>
        <div id="logOutput" class="log-output">Logs will appear here...\n</div>
    </div>

    <script type="module">
        // Debug logging
        let logBuffer = '';
        const originalConsole = {
            log: console.log,
            error: console.error,
            warn: console.warn,
            debug: console.debug
        };

        function addLog(level, ...args) {
            const timestamp = new Date().toISOString().split('T')[1].split('.')[0];
            const message = args.map(arg =>
                typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
            ).join(' ');
            logBuffer += `[${timestamp}] ${level.toUpperCase()}: ${message}\n`;
            document.getElementById('logOutput').textContent = logBuffer;
            document.getElementById('logOutput').scrollTop = document.getElementById('logOutput').scrollHeight;

            // Call original console method
            originalConsole[level](...args);
        }

        // Override console methods
        console.log = (...args) => addLog('log', ...args);
        console.error = (...args) => addLog('error', ...args);
        console.warn = (...args) => addLog('warn', ...args);
        console.debug = (...args) => addLog('debug', ...args);

        function setStatus(message, type = 'info') {
            const statusEl = document.getElementById('status');
            statusEl.textContent = message;
            statusEl.className = `status ${type}`;
        }

        window.clearLogs = function () {
            logBuffer = '';
            document.getElementById('logOutput').textContent = 'Logs cleared...\n';
        };

        window.testModelCreation = async function () {
            setStatus('Testing model creation...', 'info');
            console.log('🧪 Testing AliyunBailianChatModel creation...');

            try {
                const { AliyunBailianChatModel } = await import('/src/agent/models/AliyunBailianChatModel.js');

                const model = new AliyunBailianChatModel({
                    model: 'qwen-omni-turbo',
                    apiMode: 'http',
                    apiKey: import.meta.env.VITE_DASHSCOPE_API_KEY || '',
                    modalities: ['text', 'audio'],
                    audioConfig: { voice: 'Ethan', format: 'wav' }
                });

                console.log('✅ Model created successfully:', {
                    hasModel: !!model,
                    constructor: model.constructor.name,
                    apiMode: model.apiMode,
                    hasInvoke: typeof model.invoke,
                    hasOpenAI: !!model.openai
                });

                setStatus('✅ Model creation successful', 'success');
                return model;
            } catch (error) {
                console.error('❌ Model creation failed:', error);
                setStatus('❌ Model creation failed: ' + error.message, 'error');
                throw error;
            }
        };

        window.testDirectLLMRoute = async function () {
            setStatus('Testing direct /llm route...', 'info');
            console.log('🧪 Testing direct /llm route...');

            try {
                const response = await fetch('/llm', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        provider: 'aliyun',
                        model: 'qwen-omni-turbo',
                        messages: [{ role: 'user', content: 'Hello test from debug page' }],
                        modalities: ['text', 'audio'],
                        audioConfig: { voice: 'Ethan', format: 'wav' }
                    })
                });

                console.log('🔄 Direct /llm response:', {
                    status: response.status,
                    statusText: response.statusText,
                    headers: Object.fromEntries(response.headers.entries())
                });

                if (response.ok) {
                    const data = await response.json();
                    console.log('✅ Direct /llm response data:', data);
                    setStatus('✅ Direct /llm route successful', 'success');
                } else {
                    const errorText = await response.text();
                    console.error('❌ Direct /llm route error:', errorText);
                    setStatus('❌ Direct /llm route failed: ' + response.status, 'error');
                }
            } catch (error) {
                console.error('❌ Direct /llm route exception:', error);
                setStatus('❌ Direct /llm route exception: ' + error.message, 'error');
            }
        };

        window.runFullDebugTest = async function () {
            setStatus('Running full debug test...', 'info');
            console.log('🧪 Starting comprehensive LLM invoke debug test...');

            try {
                // Step 1: Test model creation
                console.log('\n📋 Step 1: Testing model creation...');
                const model = await testModelCreation();

                // Step 2: Test direct /llm route
                console.log('\n📋 Step 2: Testing direct /llm route...');
                await testDirectLLMRoute();

                // Step 3: Test model.invoke with network monitoring
                console.log('\n📋 Step 3: Testing model.invoke with network monitoring...');

                // Monitor all network requests
                const originalFetch = window.fetch;
                let fetchCalls = [];
                window.fetch = function (...args) {
                    fetchCalls.push({
                        url: args[0],
                        options: args[1],
                        timestamp: new Date().toISOString()
                    });
                    console.log('🌐 FETCH INTERCEPTED:', args[0], args[1]?.method || 'GET');
                    return originalFetch.apply(this, args);
                };

                try {
                    const message = {
                        role: 'user',
                        content: undefined
                    };

                    const invokeOptions = {
                        modalities: ['text', 'audio'],
                        audioConfig: { voice: 'Ethan', format: 'wav' },
                        stream: true,
                        stream_options: { include_usage: true }
                    };

                    console.log('🚀 Calling model.invoke()...');
                    const result = await model.invoke([message], invokeOptions);

                    console.log('✅ model.invoke() result:', {
                        hasResult: !!result,
                        resultType: typeof result,
                        resultKeys: result ? Object.keys(result) : null
                    });

                    console.log('🌐 Network calls made during invoke:', fetchCalls);

                    if (fetchCalls.length === 0) {
                        console.error('❌ CRITICAL: No network requests were made!');
                        setStatus('❌ CRITICAL: No network requests made by model.invoke()', 'error');
                    } else {
                        console.log('✅ Network requests detected:', fetchCalls.length);
                        setStatus('✅ Full debug test completed successfully', 'success');
                    }

                } finally {
                    // Restore original fetch
                    window.fetch = originalFetch;
                }

                console.log('\n🎯 Debug test completed. Check logs for details.');

            } catch (error) {
                console.error('❌ Full debug test failed:', error);
                setStatus('❌ Full debug test failed: ' + error.message, 'error');
            }
        };

        // Auto-run basic test on page load
        document.addEventListener('DOMContentLoaded', () => {
            console.log('🧪 Debug page loaded. Environment:', {
                hasViteEnv: !!import.meta.env,
                apiKey: import.meta.env.VITE_DASHSCOPE_API_KEY ? '[REDACTED]' : 'MISSING',
                provider: import.meta.env.VITE_MODEL_PROVIDER,
                model: import.meta.env.VITE_ALIYUN_MODEL
            });
        });
    </script>
</body>

</html>