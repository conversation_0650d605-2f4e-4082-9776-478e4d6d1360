/**
 * Test script to verify that the AliyunBailianChatModel now correctly uses
 * the proxy setting from client configuration
 */

import { LangGraphAgentService } from '../src/agent/core.js';
import { createLogger, LogLevel } from '../src/utils/logger.js';

const logger = createLogger('ProxyFixVerification');
logger.setLogLevel(LogLevel.DEBUG);

async function testProxyConfiguration() {
    logger.info('🔍 Testing AliyunBailianChatModel proxy configuration fix...');

    // Test case 1: Explicit useProxy = true
    logger.info('\n📝 Test 1: Creating agent with useProxy = true');
    try {
        const agentWithProxy = new LangGraphAgentService({
            modelProvider: 'aliyun',
            aliyunApiKey: 'test-key',
            useProxy: true,
            realtimeEndpoint: 'wss://dashscope.aliyuncs.com/api-ws/v1/realtime'
        });

        await agentWithProxy.initialize();
        logger.info('✅ Agent with useProxy=true initialized successfully');

        // Check if the model has the correct proxy setting
        if (agentWithProxy.model && agentWithProxy.model.useProxy === true) {
            logger.info('✅ Model correctly configured with useProxy=true');
        } else {
            logger.error('❌ Model proxy setting not properly configured');
            logger.error('Model useProxy value:', agentWithProxy.model?.useProxy);
        }

    } catch (error) {
        logger.error('❌ Error initializing agent with useProxy=true:', error.message);
    }

    // Test case 2: Explicit useProxy = false
    logger.info('\n📝 Test 2: Creating agent with useProxy = false');
    try {
        const agentWithoutProxy = new LangGraphAgentService({
            modelProvider: 'aliyun',
            aliyunApiKey: 'test-key',
            useProxy: false,
            realtimeEndpoint: 'wss://dashscope.aliyuncs.com/api-ws/v1/realtime'
        });

        await agentWithoutProxy.initialize();
        logger.info('✅ Agent with useProxy=false initialized successfully');

        // Check if the model has the correct proxy setting
        if (agentWithoutProxy.model && agentWithoutProxy.model.useProxy === false) {
            logger.info('✅ Model correctly configured with useProxy=false');
        } else {
            logger.error('❌ Model proxy setting not properly configured');
            logger.error('Model useProxy value:', agentWithoutProxy.model?.useProxy);
        }

    } catch (error) {
        logger.error('❌ Error initializing agent with useProxy=false:', error.message);
    }

    // Test case 3: Default behavior (should use browser detection or config default)
    logger.info('\n📝 Test 3: Creating agent without explicit useProxy setting');
    try {
        const agentDefault = new LangGraphAgentService({
            modelProvider: 'aliyun',
            aliyunApiKey: 'test-key',
            realtimeEndpoint: 'wss://dashscope.aliyuncs.com/api-ws/v1/realtime'
        });

        await agentDefault.initialize();
        logger.info('✅ Agent with default useProxy initialized successfully');

        // Check what the default proxy setting is
        logger.info('Default model useProxy value:', agentDefault.model?.useProxy);

    } catch (error) {
        logger.error('❌ Error initializing agent with default settings:', error.message);
    }

    logger.info('\n🎯 Proxy configuration fix verification completed!');
    logger.info('📋 Summary:');
    logger.info('   - AliyunBailianChatModel now receives useProxy setting from agent options');
    logger.info('   - Browser environments should set aliyunUseProxy=true in client config');
    logger.info('   - This prevents CORS/auth failures when connecting to Aliyun WebSocket API');
}

// Run the test
testProxyConfiguration().catch(error => {
    logger.error('Test failed:', error);
    process.exit(1);
});
