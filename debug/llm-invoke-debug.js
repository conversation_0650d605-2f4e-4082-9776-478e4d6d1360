/**
 * Browser Debug Script: LLM Invoke Flow
 * 
 * Run this in the browser console to debug the exact LLM invoke issue.
 * This simulates the same flow as TalkingAvatarAdapter but with more debugging.
 */

window.debugLLMInvoke = async function () {
    console.log('🧪 [DEBUG SCRIPT] Starting LLM invoke debugging...');

    try {
        // Step 1: Check if we have access to the necessary classes
        console.log('🧪 [DEBUG SCRIPT] Step 1: Checking imports and classes...');

        const { AliyunBailianChatModel } = await import('/src/agent/models/AliyunBailianChatModel.js');
        const { HumanMessage } = await import('https://esm.sh/@langchain/core@0.3.39/messages');

        console.log('🧪 [DEBUG SCRIPT] Imports successful:', {
            AliyunBailianChatModel: !!AliyunBailianChatModel,
            HumanMessage: !!HumanMessage
        });

        // Step 2: Create model instance (same config as in the app)
        console.log('🧪 [DEBUG SCRIPT] Step 2: Creating model instance...');

        const model = new AliyunBailianChatModel({
            model: 'qwen-omni-turbo',
            apiMode: 'http',
            apiKey: import.meta.env.VITE_DASHSCOPE_API_KEY || '',
            modalities: ['text', 'audio'],
            audioConfig: { voice: 'Ethan', format: 'wav' },
            enablePrompts: true,
            language: 'english',
            gender: null,
            mood: 'neutral'
        });

        console.log('🧪 [DEBUG SCRIPT] Model created:', {
            hasModel: !!model,
            constructor: model.constructor.name,
            apiMode: model.apiMode,
            hasInvoke: typeof model.invoke,
            invokeFunction: model.invoke
        });

        // Step 3: Prepare test message (simulating audio input)
        console.log('🧪 [DEBUG SCRIPT] Step 3: Preparing test message...');

        const testAudioBase64 = 'UklGRiTYAABXQVZFZm10IBAAAAABAAEAwF0AAIC7AAACABAAZGF0YQDYAABYFlIX'; // Sample WAV header

        const message = {
            role: 'user',
            content: undefined // This matches what TalkingAvatarAdapter sends
        };

        const invokeOptions = {
            modalities: ['text', 'audio'],
            audioConfig: { voice: 'Ethan', format: 'wav' },
            stream: true,
            stream_options: { include_usage: true }
        };

        console.log('🧪 [DEBUG SCRIPT] Message and options prepared:', {
            message,
            invokeOptions
        });

        // Step 4: Test model.invoke() directly
        console.log('🧪 [DEBUG SCRIPT] Step 4: Testing model.invoke() directly...');

        // Monitor network requests
        const originalFetch = window.fetch;
        let fetchCalled = false;
        let fetchArgs = null;

        window.fetch = function (...args) {
            console.log('🧪 [DEBUG SCRIPT] FETCH INTERCEPTED:', args);
            fetchCalled = true;
            fetchArgs = args;
            return originalFetch.apply(this, args);
        };

        try {
            console.log('🧪 [DEBUG SCRIPT] Calling model.invoke() NOW...');
            const result = await model.invoke([message], invokeOptions);

            console.log('🧪 [DEBUG SCRIPT] model.invoke() completed:', {
                hasResult: !!result,
                resultType: typeof result,
                resultKeys: result ? Object.keys(result) : null,
                fetchWasCalled: fetchCalled,
                fetchArgs: fetchArgs
            });

            if (!fetchCalled) {
                console.error('🧪 [DEBUG SCRIPT] ❌ CRITICAL: No fetch() call was made!');
                console.log('🧪 [DEBUG SCRIPT] This means the model.invoke() is not making HTTP requests.');
            } else {
                console.log('🧪 [DEBUG SCRIPT] ✅ Fetch was called:', fetchArgs);
            }

        } catch (error) {
            console.error('🧪 [DEBUG SCRIPT] model.invoke() error:', error);
        } finally {
            // Restore original fetch
            window.fetch = originalFetch;
        }

        // Step 5: Check if the model is using a proxy/route
        console.log('🧪 [DEBUG SCRIPT] Step 5: Checking model configuration...');
        console.log('🧪 [DEBUG SCRIPT] Model internal state:', {
            hasOpenAI: !!model.openai,
            apiKey: model.apiKey ? '[REDACTED]' : 'MISSING',
            baseURL: model.openai?.baseURL,
            defaultHeaders: model.openai?.defaultHeaders
        });

        // Step 6: Test the /llm route directly
        console.log('🧪 [DEBUG SCRIPT] Step 6: Testing /llm route directly...');

        try {
            const directResponse = await fetch('/llm', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    provider: 'aliyun',
                    model: 'qwen-omni-turbo',
                    messages: [{ role: 'user', content: 'Hello test' }],
                    modalities: ['text', 'audio'],
                    audioConfig: { voice: 'Ethan', format: 'wav' }
                })
            });

            console.log('🧪 [DEBUG SCRIPT] Direct /llm route response:', {
                status: directResponse.status,
                statusText: directResponse.statusText,
                headers: directResponse.headers,
                hasResponse: !!directResponse
            });

            if (directResponse.ok) {
                const data = await directResponse.json();
                console.log('🧪 [DEBUG SCRIPT] Direct /llm route data:', data);
            } else {
                const errorText = await directResponse.text();
                console.error('🧪 [DEBUG SCRIPT] Direct /llm route error:', errorText);
            }

        } catch (error) {
            console.error('🧪 [DEBUG SCRIPT] Direct /llm route failed:', error);
        }

        console.log('🧪 [DEBUG SCRIPT] Debugging complete. Check logs above for issues.');

    } catch (error) {
        console.error('🧪 [DEBUG SCRIPT] Debug script failed:', error);
    }
};

// Auto-run the debug script
console.log('🧪 [DEBUG SCRIPT] Debug script loaded. Call window.debugLLMInvoke() to run.');
console.log('🧪 [DEBUG SCRIPT] Or auto-running now...');
window.debugLLMInvoke(); 