/**
 * Test script to verify VAD stopping functionality
 * Run this in browser console to test VAD lifecycle
 */

console.log('🧪 Testing VAD stopping functionality...');

// Test in browser environment
if (typeof window !== 'undefined') {

    // 1. Test MediaCaptureManager VAD stopping
    async function testMediaCaptureVADStopping() {
        console.log('\n📋 Test 1: MediaCaptureManager VAD stopping');

        try {
            // Import MediaCaptureManager
            const { MediaCaptureManager } = await import('../src/media/capture/MediaCaptureManager.ts');

            // Create instance with client VAD
            const captureManager = new MediaCaptureManager({
                vadMode: 'client',
                onSpeechStart: () => console.log('🎤 Test: Speech started'),
                onSpeechEnd: (audio) => console.log('🔇 Test: Speech ended, audio length:', audio?.length),
            });

            console.log('✅ MediaCaptureManager created');

            // Initialize audio capture
            const initialized = await captureManager.initialize('audio');
            if (!initialized) {
                throw new Error('Failed to initialize audio');
            }
            console.log('✅ Audio initialized');

            // Start capture
            const started = await captureManager.startCapture('audio');
            if (!started) {
                throw new Error('Failed to start capture');
            }
            console.log('✅ Capture started');

            // Wait 2 seconds
            await new Promise(resolve => setTimeout(resolve, 2000));

            // Stop capture
            console.log('🛑 Stopping capture...');
            captureManager.stopCapture();
            console.log('✅ Capture stopped');

            // Call stopVAD
            console.log('🛑 Stopping VAD...');
            await captureManager.stopVAD();
            console.log('✅ VAD stopped');

            // Dispose
            console.log('🗑️ Disposing...');
            captureManager.dispose();
            console.log('✅ Disposed');

            console.log('✅ Test 1 completed successfully');

        } catch (error) {
            console.error('❌ Test 1 failed:', error);
        }
    }

    // 2. Test TalkingAvatarAdapter VAD stopping
    async function testTalkingAvatarAdapterStopping() {
        console.log('\n📋 Test 2: TalkingAvatarAdapter VAD stopping');

        try {
            // Mock TalkingAvatar object
            const mockTalkingAvatar = {
                isListening: false,
                _updateListeningUI: (listening) => console.log('🎛️ Mock: updateListeningUI called with:', listening),
                _updateStatus: (status) => console.log('📊 Mock: updateStatus called with:', status),
            };

            // Import TalkingAvatarAdapter
            const { TalkingAvatarAdapter } = await import('../src/agent/adapters/TalkingAvatarAdapter.js');

            // Create adapter instance
            const adapter = new TalkingAvatarAdapter(mockTalkingAvatar);
            console.log('✅ TalkingAvatarAdapter created');

            // Test stopListening method
            console.log('🛑 Calling stopListening...');
            const stopped = await adapter.stopListening();
            console.log('✅ stopListening completed, result:', stopped);

            // Dispose
            console.log('🗑️ Disposing adapter...');
            adapter.dispose();
            console.log('✅ Adapter disposed');

            console.log('✅ Test 2 completed successfully');

        } catch (error) {
            console.error('❌ Test 2 failed:', error);
        }
    }

    // Run tests
    async function runTests() {
        try {
            console.log('🚀 Starting VAD stopping tests...\n');

            await testMediaCaptureVADStopping();
            await testTalkingAvatarAdapterStopping();

            console.log('\n🎉 All tests completed!');

        } catch (error) {
            console.error('💥 Test suite failed:', error);
        }
    }

    // Auto-run tests
    runTests();

} else {
    console.log('⚠️ This test must be run in a browser environment');
} 