#!/usr/bin/env node

/**
 * Test script to verify session stabilization check is working
 * This tests the logic without requiring actual WebSocket connection
 */

import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const parentDir = path.dirname(__dirname);
const AliyunBailianChatModelPath = path.join(parentDir, 'src/agent/models/AliyunBailianChatModel.js');

console.log('🧪 Testing Session Stabilization Check Logic');
console.log('==============================================');

// Mock WebSocket class for testing
class MockWebSocket {
    constructor(url) {
        this.url = url;
        this.readyState = 1; // OPEN
        this.OPEN = 1;
        this.CLOSED = 3;
        setTimeout(() => {
            if (this.onopen) this.onopen({});
        }, 100);
    }

    send(data) {
        console.log('📤 Mock WebSocket send:', JSON.parse(data).type);
    }

    close() {
        this.readyState = 3;
        if (this.onclose) this.onclose({});
    }
}

// Mock WebSocket globally
global.WebSocket = MockWebSocket;

async function testSessionStabilization() {
    try {
        // Import AliyunBailianChatModel
        const { AliyunBailianChatModel } = await import(AliyunBailianChatModelPath);

        // Create instance with session stabilization enabled
        const model = new AliyunBailianChatModel({
            apiKey: 'test-key',
            model: 'qwen-omni-turbo-realtime',
            modalities: ['text', 'audio'],
            verbose: false
        });

        console.log('✅ Model created successfully');

        // Test 1: Check session stabilization state initially
        console.log('\n🔍 Test 1: Initial session state');
        console.log('Session stabilized:', model._sessionStabilized);

        // Test 2: Try to send audio before session is stabilized
        console.log('\n🔍 Test 2: Attempt to send audio before session stabilization');
        const testAudioData = new ArrayBuffer(1024); // Mock audio data
        const result = await model._sendAudioContentDirectly(testAudioData);
        console.log('Audio send result (should be false):', result);

        // Test 3: Stabilize session and try again
        console.log('\n🔍 Test 3: Stabilize session and try again');
        model._sessionStabilized = true;
        model.realtimeSocket = new MockWebSocket('wss://test.com');

        // Wait for mock socket to be ready
        await new Promise(resolve => setTimeout(resolve, 150));

        const result2 = await model._sendAudioContentDirectly(testAudioData);
        console.log('Audio send result after stabilization (should be true):', result2);

        // Test 4: Check VAD queue processing
        console.log('\n🔍 Test 4: Test VAD queue processing with unstabilized session');
        model._sessionStabilized = false;
        model._audioVADQueue.push({ data: testAudioData, timestamp: Date.now() });

        // This should not send audio and should warn about unstabilized session
        await model._processQueuedAudioForVAD();

        console.log('VAD queue size after processing (should be 0):', model._audioVADQueue.length);

        console.log('\n✅ All tests completed successfully!');
        console.log('\n📋 Summary:');
        console.log('- Session stabilization check prevents audio sending before session is ready');
        console.log('- Audio is only sent after session is marked as stabilized');
        console.log('- VAD queue processing respects session stabilization state');
        console.log('- This should prevent 1011 errors caused by sending audio too early');

    } catch (error) {
        console.error('❌ Test failed:', error.message);
        console.error('Stack:', error.stack);
    }
}

testSessionStabilization();
