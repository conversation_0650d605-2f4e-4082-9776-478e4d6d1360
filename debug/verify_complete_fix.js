#!/usr/bin/env node

/**
 * Final verification test for AliyunBailianChatModel 1011 error fix
 * 
 * This test verifies that:
 * 1. Direct connection works with fixed session configuration
 * 2. Proxy is disabled by default to avoid 1011 errors
 * 3. The actual AliyunBailianChatModel class works correctly
 */

import { AliyunBailianChatModel } from '../src/agent/models/AliyunBailianChatModel.js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

console.log('🧪 Final Verification: AliyunBailianChatModel 1011 Error Fix');
console.log('Testing AliyunBailianChatModel class with direct connection...\n');

if (!process.env.VITE_DASHSCOPE_API_KEY) {
    console.error('❌ VITE_DASHSCOPE_API_KEY environment variable not set');
    process.exit(1);
}

async function testDirectConnection() {
    console.log('🔧 Testing with DIRECT connection (useProxy: false)...');

    const model = new AliyunBailianChatModel({
        apiKey: process.env.VITE_DASHSCOPE_API_KEY,
        useProxy: false,          // Explicitly disable proxy to avoid 1011 errors
        skipSessionUpdate: false, // Test our fixed session configuration
        verbose: false
    });

    try {
        let sessionStabilized = false;
        let errorOccurred = false;

        const callbacks = {
            onError: (error) => {
                console.error('❌ Realtime error:', error);
                errorOccurred = true;

                if (error.code === 1011) {
                    console.error('🚨 1011 ERROR DETECTED - This should not happen with direct connection!');
                }
            },
            onSessionStabilized: () => {
                console.log('✅ Session stabilized successfully with direct connection!');
                sessionStabilized = true;
            },
            onSessionCreated: () => {
                console.log('✅ Session created successfully');
            },
            onSessionUpdated: () => {
                console.log('✅ Session updated successfully with fixed configuration');
            }
        };

        console.log('🔌 Initializing realtime connection with direct WebSocket...');
        await model.initializeRealtimeMode(callbacks);

        // Wait for session to stabilize
        const maxWaitTime = 15000; // 15 seconds
        const startTime = Date.now();

        while (!sessionStabilized && !errorOccurred && (Date.now() - startTime) < maxWaitTime) {
            await new Promise(resolve => setTimeout(resolve, 100));
        }

        if (sessionStabilized && !errorOccurred) {
            console.log('\n🎉 SUCCESS: Direct connection works perfectly!');
            console.log('✅ Session configuration with required fields accepted');
            console.log('✅ No 1011 errors detected');
            console.log('✅ AliyunBailianChatModel is ready for production use');

            // Test some basic functionality
            console.log('\n🧪 Testing basic functionality...');

            if (model.isRealtimeConnected()) {
                console.log('✅ Realtime connection status: Connected');
            }

            if (model.supportsServerVAD()) {
                console.log('✅ Server VAD support: Available');
            }

        } else if (errorOccurred) {
            console.log('\n❌ FAILED: Error occurred during connection');
            return false;
        } else {
            console.log('\n⏰ TIMEOUT: Session did not stabilize within 15 seconds');
            return false;
        }

        // Clean up
        model.stopRealtime();
        console.log('🔌 Connection closed cleanly');

        return true;

    } catch (error) {
        console.error('❌ Test failed with exception:', error);
        return false;
    }
}

async function testProxyConnection() {
    console.log('\n🔧 Testing with PROXY connection (useProxy: true) - expecting potential 1011 error...');

    const model = new AliyunBailianChatModel({
        apiKey: process.env.VITE_DASHSCOPE_API_KEY,
        useProxy: true,           // Enable proxy to demonstrate the issue
        skipSessionUpdate: false, // Test our fixed session configuration
        verbose: false
    });

    try {
        let sessionStabilized = false;
        let errorOccurred = false;
        let error1011Detected = false;

        const callbacks = {
            onError: (error) => {
                errorOccurred = true;

                if (error.code === 1011) {
                    console.log('⚠️ Expected 1011 error detected with proxy connection');
                    error1011Detected = true;
                } else {
                    console.error('❌ Unexpected error:', error);
                }
            },
            onSessionStabilized: () => {
                console.log('✅ Session stabilized with proxy connection!');
                sessionStabilized = true;
            }
        };

        console.log('🔌 Initializing realtime connection with proxy...');
        await model.initializeRealtimeMode(callbacks);

        // Wait for result
        const maxWaitTime = 10000; // 10 seconds
        const startTime = Date.now();

        while (!sessionStabilized && !errorOccurred && (Date.now() - startTime) < maxWaitTime) {
            await new Promise(resolve => setTimeout(resolve, 100));
        }

        if (sessionStabilized) {
            console.log('✅ Proxy connection worked - proxy server may have been fixed');
            model.stopRealtime();
            return true;
        } else if (error1011Detected) {
            console.log('⚠️ Proxy connection failed with 1011 error as expected');
            console.log('💡 This confirms the proxy server needs fixing');
            return 'expected_failure';
        } else {
            console.log('❌ Proxy connection failed with unexpected error');
            return false;
        }

    } catch (error) {
        console.error('❌ Proxy test failed with exception:', error);
        return false;
    }
}

async function runTests() {
    console.log('Starting comprehensive verification tests...\n');

    // Test 1: Direct connection (should work)
    const directResult = await testDirectConnection();

    // Test 2: Proxy connection (may fail with 1011)
    const proxyResult = await testProxyConnection();

    // Final report
    console.log('\n📊 FINAL TEST RESULTS:');
    console.log('=====================');
    console.log(`Direct Connection: ${directResult ? '✅ PASSED' : '❌ FAILED'}`);

    if (proxyResult === true) {
        console.log('Proxy Connection:  ✅ PASSED (proxy server working)');
    } else if (proxyResult === 'expected_failure') {
        console.log('Proxy Connection:  ⚠️ FAILED (expected - proxy server issue)');
    } else {
        console.log('Proxy Connection:  ❌ FAILED (unexpected error)');
    }

    console.log('\n💡 CONCLUSIONS:');

    if (directResult) {
        console.log('✅ 1011 error fix is SUCCESSFUL');
        console.log('✅ Session configuration with required fields works correctly');
        console.log('✅ Direct connection is recommended for production use');

        if (proxyResult !== true) {
            console.log('⚠️ Proxy server needs fixing to handle WebSocket messages properly');
            console.log('💡 Use { useProxy: false } in production to avoid 1011 errors');
        }

        console.log('\n🚀 READY FOR PRODUCTION:');
        console.log('   - Use direct connection (useProxy: false)');
        console.log('   - Session configuration fixed with required fields');
        console.log('   - 1011 error eliminated');

        process.exit(0);
    } else {
        console.log('❌ Direct connection failed - investigate further');
        process.exit(1);
    }
}

runTests().catch(error => {
    console.error('❌ Test execution failed:', error);
    process.exit(1);
});
