#!/usr/bin/env node

/**
 * Verification script for AliyunBailianChatModel with 1011 error fix
 * This tests the actual AliyunBailianChatModel class to ensure the fix works in practice
 */

import { AliyunBailianChatModel } from '../src/agent/models/AliyunBailianChatModel.js';

console.log('🧪 Verifying AliyunBailianChatModel 1011 Error Fix');

if (!process.env.VITE_DASHSCOPE_API_KEY) {
    console.error('❌ VITE_DASHSCOPE_API_KEY environment variable not set');
    process.exit(1);
}

async function testAliyunModel() {
    console.log('🔧 Testing AliyunBailianChatModel with fixed configuration...');

    const model = new AliyunBailianChatModel({
        apiKey: process.env.VITE_DASHSCOPE_API_KEY,
        skipSessionUpdate: false, // Test the fixed session configuration
        verbose: true
    });

    // Note: Model now automatically uses appropriate connection method:
    // - Node.js: Direct connection with header authentication
    // - Browser: Proxy connection with backend authentication (secure)

    try {
        console.log('🔌 Initializing realtime connection...');
        console.log('🔒 Using secure architecture: Node.js → Direct → Aliyun');

        // Set up test callbacks
        const callbacks = {
            onError: (error) => {
                console.error('❌ Realtime error:', error);
            },
            onSessionStabilized: () => {
                console.log('✅ Session stabilized successfully!');
                setTimeout(() => {
                    model.stopRealtime();
                    console.log('🎉 Test completed successfully - 1011 error fix verified!');
                    process.exit(0);
                }, 2000);
            }
        };

        await model.initializeRealtime(callbacks);

        // Wait for session to stabilize
        setTimeout(() => {
            if (!model._sessionStabilized) {
                console.log('⏰ Session taking longer than expected...');
            }
        }, 5000);

        // Timeout after 30 seconds
        setTimeout(() => {
            console.log('⏰ Test timeout - manual verification needed');
            model.stopRealtime();
            process.exit(1);
        }, 30000);

    } catch (error) {
        console.error('❌ Test failed:', error);
        process.exit(1);
    }
}

async function testSkipSessionUpdate() {
    console.log('🔧 Testing AliyunBailianChatModel with skipSessionUpdate=true...');

    const model = new AliyunBailianChatModel({
        apiKey: process.env.VITE_DASHSCOPE_API_KEY,
        skipSessionUpdate: true, // Test server defaults approach
        verbose: true
    });

    try {
        console.log('🔌 Initializing realtime connection with server defaults...');
        console.log('🔒 Using secure architecture: Node.js → Direct → Aliyun');

        const callbacks = {
            onError: (error) => {
                console.error('❌ Realtime error:', error);
            },
            onSessionStabilized: () => {
                console.log('✅ Session stabilized with server defaults!');
                setTimeout(() => {
                    model.stopRealtime();
                    console.log('🎉 Server defaults test completed successfully!');

                    // Now test the fixed configuration
                    setTimeout(testAliyunModel, 1000);
                }, 2000);
            }
        };

        await model.initializeRealtime(callbacks);

    } catch (error) {
        console.error('❌ Server defaults test failed:', error);
        process.exit(1);
    }
}

// Start with server defaults test, then custom config test
testSkipSessionUpdate();
