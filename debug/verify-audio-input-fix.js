/**
 * Verification script for audio input fix
 * Tests that the TalkingAvatarAdapter correctly includes audio in messages for Aliyun HTTP mode
 */

import { llmAPI } from '../src/media/api/llmAPI.ts';

// Sample base64 audio data (minimal WAV file)
const SAMPLE_AUDIO_BASE64 = 'UklGRiTYAABXQVZFZm10IBAAAAABAAEAwF0AAIC7AAACABAAZGF0YQDYAABYFlIX';

async function testAudioInputFix() {
    console.log('🧪 Testing audio input fix for Aliyun HTTP mode...');

    try {
        // Test 1: Message with empty content but with input_audio (this should now work)
        console.log('\n📋 Test 1: Message with input_audio field');
        const messages = [{
            role: 'user',
            content: '', // Empty content, but has audio
            input_audio: {
                data: SAMPLE_AUDIO_BASE64,
                format: 'wav'
            }
        }];

        const options = {
            provider: 'aliyun',
            model: 'qwen-omni-turbo',
            modalities: ['text', 'audio'],
            audioConfig: { voice: 'Ethan', format: 'wav' },
            temperature: 0.7
        };

        console.log('📤 Sending request with audio input...');
        const result = await llmAPI.invoke(messages, options);

        console.log('✅ Success! Audio input was processed correctly');
        console.log('📊 Response:', {
            hasContent: !!result.content,
            hasAudio: !!result.audio,
            contentLength: result.content?.length || 0,
            audioLength: result.audio?.length || 0
        });

        // Test 2: Message with both text and audio
        console.log('\n📋 Test 2: Message with both text and audio');
        const messagesWithText = [{
            role: 'user',
            content: 'Hello, please respond to this audio',
            input_audio: {
                data: SAMPLE_AUDIO_BASE64,
                format: 'wav'
            }
        }];

        console.log('📤 Sending request with text + audio input...');
        const result2 = await llmAPI.invoke(messagesWithText, options);

        console.log('✅ Success! Text + audio input was processed correctly');
        console.log('📊 Response:', {
            hasContent: !!result2.content,
            hasAudio: !!result2.audio,
            contentLength: result2.content?.length || 0,
            audioLength: result2.audio?.length || 0
        });

        console.log('\n🎉 All tests passed! The audio input fix is working correctly.');

    } catch (error) {
        console.error('❌ Test failed:', error.message);

        if (error.message.includes('No valid messages found')) {
            console.error('💥 The original error still exists - the fix may not be complete');
        } else {
            console.error('🔍 Different error occurred - this may be expected if the server is not running');
        }

        throw error;
    }
}

// Run the test
testAudioInputFix().catch(error => {
    console.error('Test execution failed:', error);
    process.exit(1);
}); 