/**
 * Test WebSocket Proxy Connection
 * 
 * This script tests the WebSocket proxy connection to ensure the browser
 * can connect to <PERSON><PERSON>'s realtime API through the local proxy.
 */

import WebSocket from 'ws';
import { ConnectionManager } from '../src/agent/models/aliyun/connection/ConnectionManager.js';

// Simulate browser environment for testing
global.window = {
    WebSocket: WebSocket,
    location: { port: '2994' }
};

async function testProxyConnection() {
    console.log('🧪 Testing WebSocket Proxy Connection...\n');

    // Create connection manager instance
    const connectionManager = new ConnectionManager({
        realtimeEndpoint: 'wss://dashscope.aliyuncs.com/api-ws/v1/realtime',
        model: 'qwen-omni-turbo-realtime',
        apiKey: process.env.VITE_DASHSCOPE_API_KEY || 'test-key'
    });

    // Test URL building
    const urlInfo = connectionManager._buildConnectionUrl();
    console.log('📍 Connection URL Info:', urlInfo);

    // Check if we're using proxy method
    if (urlInfo.method === 'proxy') {
        console.log('✅ Browser environment detected - using proxy method');
        console.log('🔗 Proxy URL:', urlInfo.wsUrl);
    } else {
        console.log('⚠️  Direct connection method detected');
    }

    console.log('\n✨ Test completed. The ConnectionManager is now configured to use the proxy in browser environments.');
}

// Run the test
testProxyConnection().catch(console.error);
