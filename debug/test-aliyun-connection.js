#!/usr/bin/env node

/**
 * Debug script for Aliyun Realtime WebSocket connection issues
 * 
 * This script helps diagnose the 1011 Internal Server Error by:
 * 1. Testing both proxy and direct connections
 * 2. Validating API key and configuration
 * 3. Providing detailed error analysis and recommendations
 * 
 * Usage:
 *   node debug/test-aliyun-connection.js
 * 
 * Make sure to set VITE_DASHSCOPE_API_KEY in your .env file
 */

import { AliyunBailianChatModel } from '../src/agent/models/AliyunBailianChatModel.js';
import { createLogger, LogLevel } from '../src/utils/logger.js';

const logger = createLogger('AliyunDebug');
logger.setLogLevel(LogLevel.DEBUG);

async function testAliyunConnection() {
    logger.info('🔍 Starting Aliyun Realtime WebSocket Connection Debugging...');
    logger.info('════════════════════════════════════════════════════════════');

    // Test 1: Connection Diagnostics
    logger.info('📡 Step 1: Running connection diagnostics...');

    const model = new AliyunBailianChatModel({
        useProxy: true, // Test with proxy first
        enableTranscription: true,
        verbose: true
    });

    try {
        const testResults = await model.testRealtimeConnection();

        if (testResults.success) {
            logger.info(`✅ Connection test passed - Recommended mode: ${testResults.recommendedMode}`);
        } else {
            logger.error('❌ All connection tests failed');
            logger.error('📋 Detailed results:', JSON.stringify(testResults.results, null, 2));
        }
    } catch (error) {
        logger.error('❌ Connection test crashed:', error.message);
    }

    // Test 2: Initialize Realtime Mode
    logger.info('🔗 Step 2: Testing realtime mode initialization...');

    try {
        const success = await model.initializeRealtimeMode({
            onError: (error) => {
                logger.error('💥 Realtime error callback triggered:', error);
            },
            onVoiceActivityDetected: (event) => {
                logger.info('🎤 Voice activity detected:', event);
            },
            onVoiceActivityStopped: (event) => {
                logger.info('🔇 Voice activity stopped:', event);
            },
            onAudioReceived: (audio) => {
                logger.info('🔊 Audio received:', { size: audio.audio?.length });
            },
            onTranscriptReceived: (transcript) => {
                logger.info('📝 Transcript received:', transcript.text);
            }
        });

        if (success) {
            logger.info('✅ Realtime mode initialized successfully');

            // Test 3: Send a simple text message
            logger.info('💬 Step 3: Testing text message sending...');
            const textSent = model.sendRealtimeText('Hello, this is a test message.');

            if (textSent) {
                logger.info('✅ Text message sent successfully');
            } else {
                logger.error('❌ Failed to send text message');
            }

            // Wait a bit for any responses
            await new Promise(resolve => setTimeout(resolve, 3000));

            // Test 4: Check session status
            logger.info('📊 Step 4: Checking session status...');
            const isActive = model.isRealtimeModeActive();
            const session = model.getRealtimeSession();

            logger.info('Session Status:', {
                isActive,
                sessionId: session?.id,
                modalities: session?.modalities,
                voice: session?.voice
            });

        } else {
            logger.error('❌ Failed to initialize realtime mode');
        }

    } catch (error) {
        logger.error('❌ Realtime initialization failed:', error.message);
        logger.debug('Full error:', error);
    } finally {
        // Clean up
        logger.info('🧹 Cleaning up connections...');
        model.closeRealtimeMode();
    }

    logger.info('════════════════════════════════════════════════════════════');
    logger.info('🏁 Debugging session complete');

    // Provide recommendations based on common issues
    logger.info('💡 Common Solutions for 1011 Internal Server Error:');
    logger.info('   1. Check API key: Ensure VITE_DASHSCOPE_API_KEY is valid');
    logger.info('   2. Use proxy mode: Set useProxy: true in browser environments');
    logger.info('   3. Verify session config: Ensure modalities are ["text", "audio"] only');
    logger.info('   4. Check audio format: Use PCM16, 16kHz, mono for audio input');
    logger.info('   5. Rate limiting: Avoid sending audio too frequently (max 15 chunks/sec)');
    logger.info('   6. Network: Check firewall/proxy settings and connectivity');
}

// Run the debugging session
testAliyunConnection().catch(error => {
    logger.error('💥 Debugging session crashed:', error);
    process.exit(1);
});
