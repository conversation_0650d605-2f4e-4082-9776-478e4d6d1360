#!/usr/bin/env python3
"""
Simple test to verify Aliyun API access with your account
"""

import asyncio
import websockets
import json
import os
from omni_realtime_client import OmniRealtimeClient

# Get API key from Node.js environment with dotenv
import subprocess
import os
os.chdir('/Volumes/SN580-2T/Users/<USER>/Hologram-Software')
result = subprocess.run(['node', '-e', 'require("dotenv").config(); console.log(process.env.VITE_DASHSCOPE_API_KEY || "")'], 
                       capture_output=True, text=True)
api_key = result.stdout.strip()
os.chdir('/Volumes/SN580-2T/Users/<USER>/Hologram-Software/debug')

print(f"API Key length: {len(api_key)}")
print(f"API Key preview: {api_key[:10]}...{api_key[-10:] if len(api_key) > 10 else ''}")

if len(api_key) < 10:
    print("❌ No valid API key found!")
    exit(1)

async def test_connection():
    print("🔗 Testing connection to Aliyun Realtime API...")
    
    client = OmniRealtimeClient(
        base_url="wss://dashscope.aliyuncs.com/api-ws/v1/realtime",
        api_key=api_key,
        model="qwen-omni-turbo-realtime",
        voice="Chelsie"
    )
    
    try:
        print("📞 Attempting connection...")
        await client.connect()
        print("✅ Connection successful!")
        
        # Just wait a moment to see if we get session.created
        await asyncio.sleep(2)
        print("✅ Test completed successfully - no 1011 errors!")
        
    except websockets.exceptions.ConnectionClosedError as e:
        if e.code == 1011:
            print("❌ Got 1011 Internal Server Error - this is the problem we're debugging")
        else:
            print(f"❌ Connection closed: {e.code} - {e.reason}")
    except Exception as e:
        print(f"❌ Error: {e}")
    finally:
        try:
            await client.close()
        except:
            pass

if __name__ == "__main__":
    asyncio.run(test_connection())