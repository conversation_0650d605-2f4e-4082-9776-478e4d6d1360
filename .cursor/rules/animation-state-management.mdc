---
description: 
globs: 
alwaysApply: false
---
# Animation State Management

## Overview
This document outlines the state management system for animations in the Hologram Software, integrating with the LLM agent and TalkingAvatar components.

## Core States

### Primary States
- `idle`: Default state when avatar is not speaking or listening
- `speaking`: Active when avatar is generating speech
- `listening`: Active when avatar is processing user input
- `thinking`: Transitional state between actions

### State Transitions
```javascript
states: {
    default: 'idle',
    transitions: {
        idle: ['speaking', 'listening', 'thinking'],
        speaking: ['idle', 'listening', 'thinking'],
        listening: ['idle', 'speaking', 'thinking'],
        thinking: ['idle', 'speaking', 'listening']
    }
}
```

## Animation Components

### Base Animation System
The animation system is built on [BaseAnimator.js](mdc:src/animation/BaseAnimator.js) which provides:
- State management
- Animation queue processing
- Morph target updates
- Bone transformations

### Animation Configuration
[AnimationConfig.js](mdc:src/animation/AnimationConfig.js) defines:
- Default pose values
- Animation templates
- State transitions
- Animation priorities

## Integration with LLM Agent

### State Triggers
The [LLMService](mdc:src/server/llm.ts) triggers state changes through:
- `generateText()`: Triggers speaking state
- `processMediaStream()`: Handles audio input for listening state
- `processVideoFrames()`: Processes visual input

### Emotion Integration
```typescript
interface MediaStreamResponse {
    input: {
        text: string;
        language: string;
        emotion?: string;
    };
    response: {
        text: string;
        emotion?: string;
    };
    sessionId: string;
}
```

## TalkingAvatar Integration

### State Management
[TalkingAvatar.js](mdc:app/viewer/talkingavatar.js) implements:
- State transitions
- Animation coordination
- Voice activity detection
- Speech synthesis

### Key Methods
- `_setSpeakingState()`: Manages speaking state
- `startListening()`: Activates listening state
- `stopListening()`: Returns to idle state
- `handleUserInput()`: Processes user interaction

## Animation Priority System

### Processing Order
1. Viseme (lip sync)
2. Expression (facial)
3. Head Movement
4. Hand Gesture
5. Body Movement
6. Idle Animation

### Animation Templates
```javascript
templates: {
    blink: {
        type: 'expression',
        duration: [100, 300],
        morphTargets: {
            eyeBlink: 1.0,
            eyeSquint: 0.2
        }
    },
    headNod: {
        type: 'headMovement',
        duration: [500, 500],
        rotations: {
            Head: { x: [0, 0.2, 0] },
            Neck: { x: [0, 0.1, 0] }
        }
    }
}
```

## Best Practices

1. State Transitions
   - Always use defined transition paths
   - Handle edge cases in state changes
   - Implement proper cleanup on state exit

2. Animation Coordination
   - Respect animation priorities
   - Use animation queue for smooth transitions
   - Implement proper animation cleanup

3. Performance
   - Use animation pooling for common animations
   - Implement proper animation culling
   - Monitor animation memory usage

4. Error Handling
   - Implement fallback animations
   - Handle animation loading failures
   - Provide visual feedback for errors

## Implementation Guidelines

1. State Changes
```javascript
// Example state change
async function changeState(newState) {
    if (!this.isValidTransition(this.currentState, newState)) {
        console.warn(`Invalid state transition: ${this.currentState} -> ${newState}`);
        return;
    }
    
    // Cleanup current state
    await this.cleanupCurrentState();
    
    // Update state
    this.currentState = newState;
    
    // Initialize new state
    await this.initializeNewState();
}
```

2. Animation Queue Management
```javascript
// Example animation queue processing
function processAnimationQueue() {
    const currentTime = performance.now();
    
    // Process animations in priority order
    for (const priority of this.processingOrder) {
        const animations = this.animQueue.filter(a => a.priority === priority);
        for (const animation of animations) {
            this.updateAnimation(animation, currentTime);
        }
    }
}
```

3. Emotion Integration
```javascript
// Example emotion-based animation selection
function selectAnimationForEmotion(emotion) {
    const emotionTemplates = this.animationTemplates[emotion] || this.animationTemplates.neutral;
    return this.selectRandomTemplate(emotionTemplates);
}
```


