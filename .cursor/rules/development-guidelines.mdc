---
description:
globs:
alwaysApply: false
---
# Development Guidelines

## Code Style and Standards
- Follow TypeScript best practices and maintain type safety
- Use ESLint for code linting (see [.eslintrc.js](mdc:.eslintrc.js))
- Follow the project's existing code structure and patterns

## Testing
- Write tests in the `src/tests/` directory
- Use the test utilities and mocks in `__mocks__/` directory
- Run tests using the scripts defined in [package.json](mdc:package.json)

## Building and Running
1. Install dependencies:
   ```bash
   npm install
   ```
2. Development server:
   ```bash
   npm run dev
   ```
3. Production build:
   ```bash
   npm run build
   ```

## Debugging
- Refer to [PHONE_APP_DEBUGGING.md](mdc:PHONE_APP_DEBUGGING.md) for detailed debugging instructions
- Use the provided test utilities in `test/` directory for debugging

## Deployment
- The project uses Docker for containerization (see [Dockerfile](mdc:Dockerfile))
- Nginx is used for serving the application (see [nginx.conf](mdc:nginx.conf))
- Follow the deployment instructions in the README
