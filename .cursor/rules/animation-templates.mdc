---
description:
globs:
alwaysApply: false
---
# Animation Templates and Poses

The animation system uses predefined templates for poses and gestures. This document outlines the available templates and how to use them.

## Pose Templates

### Standing Poses
- `side` - Side view pose
- `hip` - Hip-leaning pose
- `turn` - Turned pose
- `bend` - Bent pose
- `back` - Back view pose
- `straight` - Straight standing pose
- `wide` - Wide stance pose

### Sitting Poses
- `oneknee` - One knee down pose
- `kneel` - Kneeling pose
- `sitting` - Sitting pose

## Pose Properties

Each pose template contains:
- `standing` - <PERSON><PERSON><PERSON> indicating if it's a standing pose
- `sitting` - <PERSON><PERSON><PERSON> indicating if it's a sitting pose
- `kneeling` - <PERSON><PERSON><PERSON> indicating if it's a kneeling pose
- `lying` - <PERSON><PERSON><PERSON> indicating if it's a lying pose
- `props` - Object containing bone transformations

## Gesture Templates

### Hand Gestures
- `handup` - Hand raised gesture
- `index` - Pointing gesture
- `ok` - OK sign gesture
- More gestures defined in [AnimationConfig.js](mdc:src/animation/AnimationConfig.js)

## Animation Timing

### Default Timing Values
- Frame duration: 1000/30 ms (30 FPS)
- Slowdown rate: 1
- Easing factor: 5
- Looping animation duration: 2000 ms
- Idle animation interval: 3000-7000 ms
- Blink interval: 3000-7000 ms
- Blink duration: 100-300 ms

### Transition Timing
- Pose transition duration: 1000 ms
- Expression transition duration: 500 ms
- Head movement duration: 1000-2000 ms
- Hand gesture duration: 800-1500 ms

## Animation Pipeline

### 1. Pose Initialization
```javascript
animator.setPoseFromTemplate('side', 2000); // 2 second transition
```

### 2. Gesture Animation
```javascript
animator.playGesture('handup', 1000); // 1 second gesture
```

### 3. Head Movement
```javascript
animator.moveHead('up', 0.5, 1000); // Move head up with 0.5 intensity
```

### 4. Look At
```javascript
animator.lookAtCamera(1000); // Look at camera for 1 second
```

## Animation States

The system manages several animation states:
- `idle` - Default state
- `speaking` - During speech
- `listening` - During audio playback
- `gesturing` - During hand gestures
- `transitioning` - During pose changes

## Best Practices

1. Use predefined pose templates for consistency
2. Apply smooth transitions between poses
3. Coordinate gestures with speech
4. Maintain natural head movements
5. Use appropriate timing for animations
6. Clean up animations when disposing

## Integration

The animation templates work with:
- BaseAnimator for core functionality
- Bone structure for transformations
- Audio system for speech-driven animations
- Camera system for look-at animations
