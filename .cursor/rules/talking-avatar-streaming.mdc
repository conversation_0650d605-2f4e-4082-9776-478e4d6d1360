---
description:
globs:
alwaysApply: false
---
# TalkingAvatar Streaming Architecture

## System Overview

```mermaid
graph TD
    A[User] -->|Audio Input| B[Browser MediaCapture]
    B -->|Audio Stream| C[VAD Processing]
    C -->|Voice Activity| D[STT Service]
    D -->|Text| E[LLM Service]
    E -->|Response Text| F[TTS Service]
    F -->|Audio Stream| G[Audio Player]
    G -->|Playback| A

    subgraph "Streaming Components"
        C
        D
        E
        F
        G
    end

    subgraph "Interruption Handling"
        H[Interrupt Detection]
        I[Stream Control]
        J[State Management]
    end

    A -->|Interrupt| H
    H -->|Stop Signal| I
    I -->|Reset| J
    J -->|New State| C
```

## Current Implementation

The TalkingAvatar class ([talkingavatar.js](mdc:app/viewer/talkingavatar.js)) implements a real-time conversational system with the following key components:

### 1. Media Capture and VAD
- Initialized in `_initializeLocalMediaCapture()`
- Voice Activity Detection in `_initializeVAD()`
- Handles continuous audio input from user

### 2. Speech-to-Text (STT)
- Implemented in `_initializeSTTService()`
- Converts audio input to text
- Supports multiple languages and services

### 3. LLM Processing (Simplified Unified Design)
- Managed in `_getServerLLMResponse()` - unified entry point for all processing
- Handles both text and audio inputs with integrated animation support
- Text path: uses `generateResponse()` with built-in animation analysis
- Audio path: uses `processMediaStream()` + response analysis for animations
- Supports `bypassASR` option for direct audio processing vs ASR-first approach

### 4. Text-to-Speech (TTS)
- Initialized in `_initializeTTSService()`
- Converts LLM responses to speech
- Supports multiple voices and languages

### 5. Audio Playback
- Handled by `StreamingAudioPlayer` class
- Manages audio context and playback
- Currently lacks interruption support

## Streaming Benefits

1. **Reduced Latency**
   - Immediate response to user input
   - No waiting for complete response generation
   - Smoother conversation flow

2. **Resource Efficiency**
   - Processing happens in chunks
   - Lower memory usage
   - Better handling of long responses

3. **Natural Interaction**
   - More human-like conversation pace
   - Better turn-taking
   - Reduced "dead air" time

4. **Real-time Adaptation**
   - Can adjust to user interruptions
   - More responsive to context changes
   - Better handling of dynamic conversations

## Proposed Improvements

### 1. Interruption Support
```javascript
class TalkingAvatar {
    // Add new properties
    isInterrupted = false;
    interruptionTimeout = null;

    // Add interruption handling
    async handleInterruption() {
        this.isInterrupted = true;
        clearTimeout(this.interruptionTimeout);

        // Stop current audio playback
        await this.stopSpeaking();

        // Reset VAD and STT
        await this.stopAllListening();
        await this._initializeVAD();

        // Clear any pending responses
        this.currentResponsePromise = null;

        // Reset state
        this.isInterrupted = false;
    }

    // Modify existing methods
    async startListening() {
        if (this.isInterrupted) {
            await this.handleInterruption();
        }
        // ... existing code ...
    }

    async stopSpeaking() {
        if (this.isInterrupted) {
            // Additional cleanup for interrupted state
            this.resetBlendshapes();
        }
        // ... existing code ...
    }
}
```

### 2. Enhanced Stream Control
```javascript
class StreamController {
    constructor() {
        this.activeStreams = new Map();
        this.backpressureQueue = [];
    }

    async addStream(streamId, stream) {
        this.activeStreams.set(streamId, {
            stream,
            status: 'active',
            startTime: Date.now()
        });
    }

    async stopStream(streamId) {
        const streamInfo = this.activeStreams.get(streamId);
        if (streamInfo) {
            await streamInfo.stream.cancel();
            this.activeStreams.delete(streamId);
        }
    }

    handleBackpressure(streamId, data) {
        this.backpressureQueue.push({ streamId, data });
        this.processQueue();
    }
}
```

### 3. Future Enhancements

1. **Adaptive Chunking**
   - Dynamic chunk size based on network conditions
   - Intelligent sentence boundary detection
   - Context-aware buffering

2. **Quality of Service**
   - Network condition monitoring
   - Automatic quality adjustment
   - Fallback mechanisms

3. **Enhanced Interruption**
   - Natural interruption detection
   - Context preservation
   - Smooth state transitions

4. **Multi-modal Streaming**
   - Synchronized audio-visual streams
   - Enhanced lip-sync
   - Gesture coordination

## Implementation Guidelines

1. **Stream Initialization**
   - Initialize all components in correct order
   - Handle initialization failures gracefully
   - Implement proper cleanup

2. **Error Handling**
   - Implement retry mechanisms
   - Handle network interruptions
   - Provide user feedback

3. **Resource Management**
   - Monitor memory usage
   - Implement proper cleanup
   - Handle concurrent streams

4. **Performance Optimization**
   - Use appropriate buffer sizes
   - Implement backpressure handling
   - Optimize chunk processing

## Code Examples

### Stream Initialization
```javascript
async _initializeStreaming() {
    try {
        // Initialize components in parallel
        const [vad, stt, tts] = await Promise.all([
            this._initializeVAD(),
            this._initializeSTTService(),
            this._initializeTTSService()
        ]);

        // Set up stream controllers
        this.streamController = new StreamController();

        // Initialize audio player
        this.audioPlayer = new StreamingAudioPlayer();
        await this.audioPlayer.initialize();

    } catch (error) {
        console.error('Streaming initialization failed:', error);
        throw error;
    }
}
```

### Interruption Handling
```javascript
async handleUserInterruption() {
    // Stop current processing
    await this.handleInterruption();

    // Clear any pending timeouts
    clearTimeout(this.interruptionTimeout);

    // Reset state
    this.isInterrupted = false;

    // Restart listening
    await this.startListening();
}
```

## Best Practices

1. **Stream Management**
   - Always clean up streams properly
   - Handle backpressure appropriately
   - Monitor stream health

2. **Error Recovery**
   - Implement automatic reconnection
   - Preserve conversation context
   - Provide user feedback

3. **Performance**
   - Monitor memory usage
   - Optimize chunk sizes
   - Handle concurrent operations

4. **User Experience**
   - Provide clear feedback
   - Handle interruptions gracefully
   - Maintain conversation flow
