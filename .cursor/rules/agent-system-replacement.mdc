---
description: 
globs: 
alwaysApply: false
---
# Agent System Replacement Strategy

## Overview

The agent system ([src/agent/](mdc:src/agent)) is designed to replace the current dual LLM integration pattern in [talkingavatar.js](mdc:app/viewer/talkingavatar.js), which currently uses both [LLMStreamProcessor.js](mdc:src/media/streaming/LLMStreamProcessor.js) and [llm.ts](mdc:src/server/llm.ts).

## Current Architecture Analysis

### Current LLM Integration Pattern in TalkingAvatar

The current implementation in [talkingavatar.js](mdc:app/viewer/talkingavatar.js) uses a complex dual-service approach:

1. **LLMStreamProcessor** - Handles streaming LLM responses with TTS integration
   - Initialized in `_initializedStreaming()` 
   - Used for real-time text-to-speech streaming
   - Manages backpressure and audio chunking
   - Located at lines 612-704 in talkingavatar.js

2. **LLM Service (llm.ts)** - Handles core LLM logic and animation selection
   - Called via `_getServerLLMResponse()` method (lines 2079-2249)
   - Manages unified response generation with animation support
   - Handles ASR metadata integration
   - Provides conversation history and context awareness

### Key Integration Points

#### Primary Entry Point: `handleUserInput()` (lines 1925-2059)
```javascript
// Current flow:
handleUserInput() → _getServerLLMResponse() → getLLMService() → generateResponse() → streamProcessor
```

#### Streaming Integration: `_initializedStreaming()` (lines 612-704)
```javascript
// Creates LLMStreamProcessor with callbacks for:
// - onTextChunk: UI updates
// - onAudioChunk: Speaking state management  
// - onAnimationTrigger: Animation system integration
```

#### Current Dual Dependencies
```javascript
// In talkingavatar.js:
import { LLMStreamProcessor } from '@/media/streaming/index.ts';
import { getLLMService } from '@/server/llm.ts';
```

## Agent System Architecture

### Core Components

#### 1. LangChainCoreService ([src/agent/core.js](mdc:src/agent/core.js))
- **Primary LLM Interface**: LangChain-based service with vLLM backend
- **Unified Processing**: Handles both text and multimodal inputs
- **Tool Integration**: Built-in animation tool support via tool calling
- **Structured Outputs**: Zod schema for consistent response format
- **Streaming Support**: Native streaming with chunked responses

#### 2. MultimodalInputProcessor ([src/agent/input.js](mdc:src/agent/input.js))
- **Input Detection**: Automatically detects text, image, audio, video modalities
- **LangChain Messages**: Converts inputs to LangChain message format
- **Processing Strategy**: Determines optimal processing approach per modality

#### 3. Prompt Management ([src/agent/prompt.js](mdc:src/agent/prompt.js))
- **Context-Aware Prompts**: Dynamic prompt generation with conversation history
- **Language Support**: Multi-language prompt templates
- **Animation Integration**: Built-in animation selection prompts

#### 4. Tool System ([src/agent/tools/](mdc:src/agent/tools))
- **Animation Tools**: Direct animation triggering via tool calls
- **TTS Integration**: Text-to-speech tool integration
- **Extensible Framework**: Easy addition of new tools

## Replacement Strategy

### Phase 1: Agent Service Integration

Replace the dual service pattern with a single agent service:

```javascript
// Current (to be replaced):
import { LLMStreamProcessor } from '@/media/streaming/index.ts';
import { getLLMService } from '@/server/llm.ts';

// New (agent-based):
import { LangChainCoreService } from '@/agent/core.js';
```

### Phase 2: Method Replacement Mapping

#### Replace `_getServerLLMResponse()` 
**Current Location**: Lines 2079-2249 in talkingavatar.js
**Replacement**: Direct agent service call

```javascript
// Current pattern:
async _getServerLLMResponse(input, contextInfo = {}) {
    const llmService = await getLLMService();
    const result = await llmService.generateResponse(input, options);
    // Handle animation callbacks
    // Handle streaming processor
}

// Agent pattern:
async _getAgentResponse(input, contextInfo = {}) {
    const response = await this.agentService.generateResponse(input, {
        ...contextInfo,
        useTools: true, // Enable animation tools
        stream: this.useStreaming
    });
    // Animation handled via tool calls automatically
}
```

#### Replace `_initializedStreaming()`
**Current Location**: Lines 612-704 in talkingavatar.js  
**Replacement**: Agent service initialization with streaming

```javascript
// Current pattern:
async _initializedStreaming() {
    this.streamProcessor = new LLMStreamProcessor({
        ttsService: this.ttsServiceInstance,
        onTextChunk: (data) => { /* UI updates */ },
        onAudioChunk: (audioData) => { /* Speaking state */ },
        onAnimationTrigger: (animation) => { /* Animation */ }
    });
}

// Agent pattern:
async _initializeAgentService() {
    this.agentService = new LangChainCoreService({
        ttsService: this.ttsServiceInstance,
        audioPlayer: this.streamingAudioPlayer,
        animationController: this // Pass talkingavatar as animation controller
    });
}
```

### Phase 3: Callback Integration

#### Animation Callbacks
Replace manual animation triggering with tool-based approach:

```javascript
// Current: Manual callback in generateResponse options
onAnimationTrigger: async (animation) => {
    await this._triggerAnimation(animation);
}

// Agent: Automatic via animation tool registration
// Animation tools are registered in agent service initialization
// Tools call this._triggerAnimation() directly when invoked
```

#### Streaming Callbacks  
Replace LLMStreamProcessor callbacks with agent streaming:

```javascript
// Current: LLMStreamProcessor callbacks
onTextChunk: (data) => {
    if (this.onConversationUpdate) {
        this.onConversationUpdate({
            type: 'assistantMessage', 
            text: data.fullText,
            isPartial: true
        });
    }
}

// Agent: Native streaming response handling
for await (const chunk of response) {
    if (this.onConversationUpdate) {
        this.onConversationUpdate({
            type: 'assistantMessage',
            text: chunk.content,
            isPartial: !chunk.done
        });
    }
}
```

## Testing Strategy

### Test Coverage Requirements

#### 1. Agent Service Integration Tests
- [ ] **Connection Testing**: Verify vLLM endpoint connectivity
- [ ] **Multimodal Input**: Test text, audio, image processing
- [ ] **Tool Integration**: Verify animation tool calling works
- [ ] **Streaming**: Test streaming response handling
- [ ] **Error Handling**: Test graceful degradation

#### 2. TalkingAvatar Integration Tests  
- [ ] **Service Replacement**: Verify agent service replaces dual services
- [ ] **Animation Triggering**: Test animation tools work with existing animation system
- [ ] **Voice Integration**: Test TTS service integration 
- [ ] **Conversation Flow**: Test conversation history and context
- [ ] **Performance**: Compare response times vs current implementation

#### 3. Regression Tests
- [ ] **Feature Parity**: All current features work with agent system
- [ ] **UI Integration**: Conversation updates and status display
- [ ] **Audio Playback**: TTS and audio streaming continuity
- [ ] **Language Support**: Multi-language responses maintained

### Test Files Reference

#### Existing Agent Tests
- [test/agent/input.test.js](mdc:test/agent/input.test.js) - Multimodal input processing
- [test/agent/prompt.test.js](mdc:test/agent/prompt.test.js) - Prompt generation
- [test/agent/core/](mdc:test/agent/core) - Core service tests
- [test/agent/stream/](mdc:test/agent/stream) - Streaming tests

#### New Integration Tests Needed
```javascript
// test/integration/talkingavatar-agent.test.js
describe('TalkingAvatar Agent Integration', () => {
    test('should replace LLMStreamProcessor with agent service');
    test('should handle animation triggering via tools');
    test('should maintain conversation context');
    test('should support streaming responses');
});
```

## Implementation Checklist

### Pre-Implementation
- [ ] **Review Agent Tests**: Run existing agent test suite
- [ ] **Test vLLM Connection**: Verify agent can connect to vLLM endpoint
- [ ] **Animation Tool Testing**: Test animation tool registration and execution
- [ ] **TTS Service Compatibility**: Verify agent works with current TTS services

### Implementation Steps
- [ ] **Step 1**: Add agent service initialization to TalkingAvatar constructor
- [ ] **Step 2**: Replace `_getServerLLMResponse()` with agent service calls
- [ ] **Step 3**: Replace `_initializedStreaming()` with agent initialization
- [ ] **Step 4**: Update `handleUserInput()` to use agent service
- [ ] **Step 5**: Remove LLMStreamProcessor and llm.ts imports
- [ ] **Step 6**: Update animation callback integration

### Post-Implementation
- [ ] **Integration Testing**: Full TalkingAvatar functionality test
- [ ] **Performance Testing**: Compare response times and resource usage
- [ ] **Feature Verification**: Ensure all current features work
- [ ] **Documentation Update**: Update code comments and documentation

## Migration Benefits

### Simplified Architecture
- **Single Service**: Replace dual LLM services with unified agent service
- **Native Tool Support**: Built-in animation and TTS tool integration
- **Better Error Handling**: Centralized error management
- **Improved Maintainability**: Single point of LLM integration

### Enhanced Capabilities  
- **Multimodal Support**: Native support for image, audio, video inputs
- **Structured Outputs**: Consistent response format with Zod validation
- **Advanced Prompting**: Context-aware prompt generation with conversation history
- **Tool Extensibility**: Easy addition of new tools and capabilities

### Performance Improvements
- **Reduced Complexity**: Eliminate dual service coordination overhead
- **Better Streaming**: Native LangChain streaming support
- **Optimized Context**: Efficient conversation history management
- **Resource Efficiency**: Single service instance vs multiple service coordination

## Risk Mitigation

### Backward Compatibility
- Maintain existing public TalkingAvatar API
- Preserve current animation triggering interface
- Keep conversation update callback compatibility
- Ensure TTS service integration continuity

### Fallback Strategy
- Keep current services available during transition
- Implement feature flags for gradual rollout
- Maintain error recovery to current implementation
- Plan rollback strategy if issues occur

### Testing Safety Net
- Comprehensive test coverage before replacement
- Parallel testing of both systems during development
- Performance benchmarking to ensure no degradation
- User acceptance testing for feature completeness
