---
description:
globs:
alwaysApply: false
---
# Bone Structure and Naming Conventions

The animation system uses a standardized bone hierarchy for doll meshes. This document outlines the required bone structure and naming conventions.

## Core Bone Hierarchy

### Spine Chain
- `Hips` - Root bone for the entire skeleton
- `Spine` - Lower spine
- `Spine1` - Middle spine
- `Spine2` - Upper spine
- `Neck` - Neck bone
- `Head` - Head bone

### Arm Chain (Left)
- `LeftShoulder` - Left shoulder
- `LeftArm` - Upper arm
- `LeftForeArm` - Forearm
- `LeftHand` - Hand
- Finger bones (see Finger Bones section)

### Arm Chain (Right)
- `RightShoulder` - Right shoulder
- `RightArm` - Upper arm
- `RightForeArm` - Forearm
- `RightHand` - Hand
- Finger bones (see Finger Bones section)

### Leg Chain (Left)
- `LeftUpLeg` - Upper leg
- `LeftLeg` - Lower leg
- `LeftFoot` - Foot
- `LeftToeBase` - Toe base

### Leg Chain (Right)
- `RightUpLeg` - Upper leg
- `RightLeg` - Lower leg
- `RightFoot` - Foot
- `RightToeBase` - Toe base

## Finger Bones

### Left Hand Fingers
- Thumb: `LeftHandThumb1`, `LeftHandThumb2`, `LeftHandThumb3`
- Index: `LeftHandIndex1`, `LeftHandIndex2`, `LeftHandIndex3`
- Middle: `LeftHandMiddle1`, `LeftHandMiddle2`, `LeftHandMiddle3`
- Ring: `LeftHandRing1`, `LeftHandRing2`, `LeftHandRing3`
- Pinky: `LeftHandPinky1`, `LeftHandPinky2`, `LeftHandPinky3`

### Right Hand Fingers
- Thumb: `RightHandThumb1`, `RightHandThumb2`, `RightHandThumb3`
- Index: `RightHandIndex1`, `RightHandIndex2`, `RightHandIndex3`
- Middle: `RightHandMiddle1`, `RightHandMiddle2`, `RightHandMiddle3`
- Ring: `RightHandRing1`, `RightHandRing2`, `RightHandRing3`
- Pinky: `RightHandPinky1`, `RightHandPinky2`, `RightHandPinky3`

## Bone Properties

Each bone should have the following properties:
- `position` - THREE.Vector3 for bone position
- `rotation` - THREE.Euler for bone rotation
- `quaternion` - THREE.Quaternion for bone rotation (optional)

## Naming Conventions

1. All bone names should be in PascalCase
2. Left/Right prefix for paired bones
3. Numbered suffixes for chain bones (e.g., Spine1, Spine2)
4. Descriptive names for finger bones (Thumb, Index, Middle, Ring, Pinky)

## Bone Groups

The system uses predefined bone groups for animation:
- Core bones (spine chain)
- Arm bones (shoulders, arms, hands)
- Leg bones (hips, legs, feet)
- Finger bones (all finger joints)

## Validation

The animation system validates the bone structure by:
1. Checking for required bones
2. Verifying bone hierarchy
3. Ensuring proper naming conventions
4. Validating bone properties

## Best Practices

1. Follow the exact bone naming conventions
2. Maintain proper bone hierarchy
3. Ensure all required bones are present
4. Use consistent bone orientations
5. Keep bone chains properly connected
