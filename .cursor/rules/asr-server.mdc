---
description: 
globs: 
alwaysApply: false
---
# ASR WebSocket Server Implementation Guide

This guide explains the structure and key components of the ASR (Automatic Speech Recognition) WebSocket server implementation.

## Core Components

### 1. Dependencies
- `sherpa-onnx`: The core ASR engine addon
- `ws`: WebSocket server implementation
- `http`: Built-in Node.js HTTP server
- `fs`: File system utilities for model path validation

### 2. Configuration
The server requires configuration for the ASR model:
- Model file paths (encoder, decoder, joiner, tokens)
- Processing parameters (threads, provider, sample rate)
- Decoding method settings

### 3. Server Architecture
The server implements a WebSocket-based streaming ASR system with these key features:
- HTTP server for basic health checks
- WebSocket server for real-time audio streaming
- Per-connection ASR stream management
- Audio format conversion (16-bit PCM to Float32)
- Real-time transcription with partial and final results

### 4. Audio Processing
The server expects:
- Single-channel audio
- 16kHz sample rate
- 16-bit PCM format
- Converts incoming audio to Float32 format for Sherpa-ONNX

### 5. WebSocket Protocol
The server implements a specific protocol:
- Binary messages for audio data
- JSON messages for control (e.g., `{"eof": 1}`)
- JSON responses with transcription results:
  - `{"type": "partial", "text": "..."}` for ongoing transcription
  - `{"type": "final", "text": "..."}` for completed transcription

## Implementation Details

### Model Management
- Validates model file existence on startup
- Supports different model types (transducer-based)
- Configurable for CPU/GPU processing

### Stream Handling
- Creates dedicated ASR stream per WebSocket connection
- Manages stream lifecycle (creation, processing, cleanup)
- Handles end-of-stream processing for final results

### Error Handling
- Validates incoming audio format
- Manages WebSocket connection errors
- Handles ASR processing errors
- Implements graceful shutdown

## Usage Guidelines

1. **Setup Requirements**
   - Node.js environment
   - Sherpa-ONNX addon installed
   - WebSocket library (`ws`)
   - Appropriate ASR model files

2. **Configuration**
   - Update model paths in config section
   - Adjust processing parameters as needed
   - Configure server port (default: 6006)

3. **Client Implementation**
   - Connect to WebSocket endpoint
   - Send audio in correct format
   - Handle transcription responses
   - Implement end-of-stream signaling

4. **Best Practices**
   - Monitor server resources
   - Implement proper error handling
   - Consider scaling for multiple clients
   - Secure WebSocket connections in production

## Security Considerations

1. **Production Deployment**
   - Implement proper authentication
   - Use secure WebSocket (WSS)
   - Rate limit connections
   - Monitor resource usage

2. **Error Handling**
   - Validate all inputs
   - Implement proper logging
   - Handle connection timeouts
   - Manage resource cleanup

## Performance Optimization

1. **Resource Management**
   - Monitor memory usage
   - Clean up streams properly
   - Optimize thread usage
   - Consider connection pooling

2. **Scaling Considerations**
   - Load balancing for multiple instances
   - Resource limits per connection
   - Connection pooling
   - Caching strategies
