---
description: 
globs: 
alwaysApply: false
---
# Viewer Environment Setup

The viewer's environment and background are configured through a combination of files and settings:

## Main Configuration Files
- [viewerConfig.js](mdc:app/viewer/viewerConfig.js) - Contains the primary environment configuration
- [viewer.js](mdc:app/viewer/viewer.js) - Implements the environment setup in the Viewer class
- [environmentConfig.ts](mdc:src/scene/environmentConfig.ts) - Centralized environment configuration
- [environment.ts](mdc:src/scene/environment.ts) - Core environment implementation

## Environment Configuration Structure

The environment is configured through `ENVIRONMENT_CONFIG` which is imported from a centralized configuration. The main components are:

1. **Environment Types**
   - `thinBox`: A thin box environment that can receive shadows
   - `blank`: Simple environment with background color
   - `hdr`: HDR environment maps (not fully implemented yet)

2. **ThinBox Environment Features**
   - Dynamic sizing based on scene content
   - Configurable dimensions (width, height, depth)
   - Shadow reception
   - Image-based lighting (IBL) support
   - Material properties (metalness, roughness)
   - Auto-scaling with min/max size limits
   - Position control

3. **Scene Configuration**
   - Initial camera position
   - Renderer options (antialias, alpha, etc.)
   - Lighting setup (ambient and directional lights)
   - Environment settings

4. **Background Options**
   - HDR environment maps (e.g., Royal Esplanade)
   - Background color (managed through `getBackgroundColorCSS()`)
   - Alpha channel support for transparency
   - Default background color: 0x222222

5. **Lighting Setup**
   - Ambient light (color: 0xffffff, intensity: 0.8)
   - Directional light (color: 0xffffff, intensity: 1.0, position: {x: 5, y: 5, z: 5})
   - Image-based lighting (IBL) with configurable parameters

## Implementation Details

The environment is managed by the `SceneEnvironment` class which provides:

1. **Dynamic Sizing**
   - Automatically calculates environment size based on scene content
   - Applies scale factor (default: 1.5x)
   - Enforces minimum (5 units) and maximum (50 units) size limits
   - Maintains thin depth ratio (0.3x) for box environment

2. **Environment Setup Process**
   - Environment config is merged with user parameters
   - Scene configuration is combined with environment settings
   - Renderer is configured with antialias and alpha support
   - IBL is set up if enabled
   - Environment mesh is created and positioned

3. **Color Management**
   - Hex to CSS color conversion utilities
   - Background color management through THREE.Color
   - Material color configuration for environment meshes

## Usage Notes
- The environment can be customized by passing parameters to the Viewer constructor
- Background color can be changed through the centralized configuration
- HDR environment maps can be enabled/disabled through the ASSETS.ENVIRONMENTS configuration
- Environment size automatically adjusts to scene content
- IBL parameters can be tuned for better lighting quality
- Environment type can be switched between thinBox, blank, and hdr modes

# Thin Box Room Environment

## Purpose
Provides a visible “room” (thin box) around the main mesh, with four walls, for context and shadow projection.

## Implementation

- The thin box environment is managed by the `SceneEnvironment` class in [src/scene/environment.ts](mdc:src/scene/environment.ts).
- The method `setupThinBoxEnvironment` creates a `THREE.Mesh` with `BoxGeometry` sized to fit the scene’s contents, using a scale factor and min/max limits.
- The box is rendered with `MeshStandardMaterial`, `side: THREE.BackSide`, and can receive shadows.
- The environment config is passed via `EnvironmentConfig` (see `DEFAULT_ENVIRONMENT_CONFIG`), with options for size, color, metalness, roughness, and shadow receiving.
- The environment is set up automatically by the `SceneManager` ([src/scene/sceneManager.ts](mdc:src/scene/sceneManager.ts)) when the scene is initialized.

## Usage

- To enable the thin box room, set `type: 'thinBox'` in your environment config.
- Adjust `scaleFactor` in `thinBox` config to control how much larger the box is than your mesh.
- The box will automatically resize to fit the mesh if you call `updateEnvironmentSize()` after adding your mesh.
- Shadows will be projected onto the box walls if your lights are set to cast shadows and your mesh is set to cast shadows.

## Example Config

```js
{
  type: 'thinBox',
  thinBox: {
    scaleFactor: 1.2, // Slightly larger than mesh
    color: 0xeeeeee,
    metalness: 0.1,
    roughness: 0.9,
    receiveShadows: true,
    minSize: 5,
    maxSize: 50
  }
}
```

## Best Practices

- Add your mesh to the scene before calling `updateEnvironmentSize()` to ensure the box fits.
- Use a directional light with `castShadow: true` for best shadow results.
- The box is centered at the origin by default; adjust `position` if needed.

## Related Files

- [src/scene/environment.ts](mdc:src/scene/environment.ts)
- [src/scene/sceneManager.ts](mdc:src/scene/sceneManager.ts)
- [src/scene/lighting.ts](mdc:src/scene/lighting.ts)


# Thin Box Background Environment

## Overview
The thin box background environment provides a contained space for 3D content in the Looking Glass display. It creates a sense of depth and context while maintaining performance.

## Implementation

### Base Class Integration
The thin box environment is implemented in the `Base` class through the `setupBoxEnvironment` method:

```javascript
setupBoxEnvironment(size = 20, color = 0xeeeeee, setupIBL = true) {
    // Clean up existing environment
    if (this.boxEnvironmentMesh) {
        this.scene.remove(this.boxEnvironmentMesh);
        if (this.boxEnvironmentMesh.geometry) this.boxEnvironmentMesh.geometry.dispose();
        if (this.boxEnvironmentMesh.material) {
            if (Array.isArray(this.boxEnvironmentMesh.material)) {
                this.boxEnvironmentMesh.material.forEach(m => {
                    if (m.map) m.map.dispose();
                    m.dispose();
                });
            } else {
                if (this.boxEnvironmentMesh.material.map) this.boxEnvironmentMesh.material.map.dispose();
                this.boxEnvironmentMesh.material.dispose();
            }
        }
        this.boxEnvironmentMesh = null;
    }

    // Create box geometry
    const geometry = new THREE.BoxGeometry(size, size, size * 0.3); // Thin depth
    const material = new THREE.MeshStandardMaterial({
        color: color,
        side: THREE.BackSide, // Render inside
        metalness: 0.1,
        roughness: 0.9
    });

    this.boxEnvironmentMesh = new THREE.Mesh(geometry, material);
    this.boxEnvironmentMesh.receiveShadow = true;
    this.boxEnvironmentMesh.position.set(0, 0, -size * 0.15); // Position slightly behind content

    this.scene.add(this.boxEnvironmentMesh);

    // Set dark background
    this.scene.background = new THREE.Color(0x101010);

    // Setup IBL if needed
    if (setupIBL && !this.scene.environment) {
        const pmremGenerator = new THREE.PMREMGenerator(this.renderer);
        pmremGenerator.compileEquirectangularShader();
        if (!this._roomEnvironmentInstance) {
            this._roomEnvironmentInstance = new RoomEnvironment(this.renderer);
        }
        this.scene.environment = pmremGenerator.fromScene(this._roomEnvironmentInstance, 0.5).texture;
        pmremGenerator.dispose();
    }
}
```

### Usage in Viewer
The Viewer class initializes the environment during setup:

```javascript
async initViewer() {
    // ... other initialization code ...
    
    if (this.renderer) {
        this.renderer.shadowMap.enabled = true;
        this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
        
        // Create box environment
        this.setupBoxEnvironment(50, 0x999999, true);
    }
    
    // ... rest of initialization ...
}
```

## Best Practices

1. **Size and Proportions**
   - Use a size that's 2-3x larger than your main content
   - Keep depth (z-dimension) thin (0.2-0.3x of width/height)
   - Position slightly behind content (z = -size * 0.15)

2. **Materials and Lighting**
   - Use MeshStandardMaterial for better lighting interaction
   - Set side to BackSide to render inside the box
   - Enable shadow receiving
   - Use subtle metalness (0.1) and higher roughness (0.9)

3. **Performance**
   - Clean up old environment before creating new one
   - Dispose of geometries and materials properly
   - Use simple geometry (BoxGeometry is efficient)
   - Consider texture compression for any applied textures

4. **Looking Glass Integration**
   - Works with Looking Glass WebXR polyfill
   - Maintains proper depth perception
   - Supports shadow casting and receiving

## Configuration Options

```javascript
{
    size: 20,           // Size of the box
    color: 0xeeeeee,    // Color of the box material
    setupIBL: true,     // Whether to setup image-based lighting
    position: {         // Optional position override
        x: 0,
        y: 0,
        z: -3
    }
}
```

## Related Files
- @base.js - Contains the main implementation
- @viewer.js - Shows usage in the viewer
- @webxr.js - Looking Glass integration

