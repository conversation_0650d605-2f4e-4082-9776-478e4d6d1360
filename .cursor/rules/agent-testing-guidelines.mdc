---
description:
globs:
alwaysApply: false
---
# Agent System Testing Guidelines

## Testing Philosophy

The agent system replacement requires comprehensive testing to ensure feature parity, performance, and reliability. Testing should be done incrementally with clear validation criteria.

## Test Environment Setup

### Prerequisites
- vLLM server running and accessible
- TTS service instances available
- Animation system loaded
- Test avatars and 3D models available

### Configuration
```javascript
// test/config/agent-test-config.js
export const agentTestConfig = {
    vllmEndpoint: 'http://localhost:8000', // Test vLLM instance
    testModels: ['meta-llama/Llama-2-7b-chat-hf'],
    ttsServices: ['sparkTTS', 'serverTTS'],
    animationRegistry: true,
    timeout: 30000 // 30 second timeout for LLM responses
};
```

## Test Categories

### 1. Unit Tests for Agent Components

#### Core Service Tests ([test/agent/core/](mdc:test/agent/core/))
```javascript
describe('LangChainCoreService', () => {
    test('should initialize with vLLM endpoint');
    test('should handle text input generation');
    test('should support streaming responses');
    test('should bind animation tools correctly');
    test('should handle structured output schema');
    test('should manage conversation context');
});
```

#### Input Processor Tests ([test/agent/input.test.js](mdc:test/agent/input.test.js))
```javascript
describe('MultimodalInputProcessor', () => {
    test('should detect text-only input');
    test('should detect multimodal input with images');
    test('should detect audio input from TalkingAvatar');
    test('should convert to LangChain message format');
    test('should determine processing strategies');
});
```

#### Prompt Service Tests ([test/agent/prompt.test.js](mdc:test/agent/prompt.test.js))
```javascript
describe('LangChain Prompt Service', () => {
    test('should generate context-aware prompts');
    test('should include conversation history');
    test('should support multi-language prompts');
    test('should integrate animation selection prompts');
    test('should handle ASR metadata integration');
});
```

### 2. Integration Tests

#### TalkingAvatar Integration
```javascript
// test/integration/talkingavatar-agent.test.js
describe('TalkingAvatar Agent Integration', () => {
    let talkingAvatar;
    
    beforeEach(async () => {
        talkingAvatar = new TalkingAvatar(mockViewer);
        await talkingAvatar.initialize();
    });
    
    describe('Service Replacement', () => {
        test('should initialize agent service instead of dual services', async () => {
            expect(talkingAvatar.agentService).toBeDefined();
            expect(talkingAvatar.streamProcessor).toBeUndefined();
            expect(talkingAvatar.agentService).toBeInstanceOf(LangChainCoreService);
        });
        
        test('should handle text input via agent service', async () => {
            const response = await talkingAvatar.handleUserInput('Hello');
            
            expect(response).toBeDefined();
            expect(typeof response).toBe('string');
            expect(response.length).toBeGreaterThan(0);
        });
        
        test('should handle audio input via agent service', async () => {
            const mockAudioData = new Float32Array(16000); // 1 second of audio
            const response = await talkingAvatar.handleUserInput(mockAudioData, true);
            
            expect(response).toBeDefined();
        });
    });
    
    describe('Animation Integration', () => {
        test('should trigger animations via agent tools', async () => {
            const animationSpy = jest.spyOn(talkingAvatar, '_triggerAnimation');
            
            await talkingAvatar.handleUserInput('Dance for me');
            
            expect(animationSpy).toHaveBeenCalled();
            const animationCall = animationSpy.mock.calls[0][0];
            expect(animationCall).toHaveProperty('file');
            expect(animationCall).toHaveProperty('category');
        });
        
        test('should maintain animation priority system', async () => {
            const animationSpy = jest.spyOn(talkingAvatar, '_triggerAnimation');
            
            await talkingAvatar.handleUserInput('Show me a happy dance');
            
            expect(animationSpy).toHaveBeenCalledWith(
                expect.objectContaining({
                    priority: expect.any(Number),
                    category: expect.stringMatching(/(dance|emotional)/)
                })
            );
        });
    });
    
    describe('Streaming Support', () => {
        test('should support streaming responses', async () => {
            const textChunks = [];
            talkingAvatar.onConversationUpdate = (data) => {
                if (data.type === 'assistantMessage') {
                    textChunks.push(data.text);
                }
            };
            
            await talkingAvatar.handleUserInput('Tell me a story');
            
            expect(textChunks.length).toBeGreaterThan(1);
            expect(textChunks.some(chunk => chunk.includes('story'))).toBe(true);
        });
    });
    
    describe('Conversation Context', () => {
        test('should maintain conversation history', async () => {
            await talkingAvatar.handleUserInput('My name is Alice');
            const response = await talkingAvatar.handleUserInput('What is my name?');
            
            expect(response.toLowerCase()).toContain('alice');
        });
        
        test('should use session ID for context', async () => {
            const sessionId = talkingAvatar.avatarMesh?.userData?.fileName || 'test-session';
            
            // Test that agent service uses the session ID
            expect(talkingAvatar.agentService).toBeDefined();
            // Verify session context is maintained in agent service
        });
    });
});
```

### 3. Performance Tests

#### Response Time Comparison
```javascript
describe('Performance Comparison', () => {
    test('should match or improve response times vs current implementation', async () => {
        const startTime = Date.now();
        const response = await talkingAvatar.handleUserInput('Hello');
        const responseTime = Date.now() - startTime;
        
        // Should respond within 5 seconds for simple queries
        expect(responseTime).toBeLessThan(5000);
        expect(response).toBeDefined();
    });
    
    test('should handle concurrent requests efficiently', async () => {
        const promises = Array(3).fill().map((_, i) => 
            talkingAvatar.handleUserInput(`Query ${i + 1}`)
        );
        
        const responses = await Promise.all(promises);
        
        expect(responses).toHaveLength(3);
        responses.forEach(response => {
            expect(response).toBeDefined();
            expect(typeof response).toBe('string');
        });
    });
});
```

#### Memory Usage Tests
```javascript
describe('Memory Management', () => {
    test('should not leak memory during extended conversations', async () => {
        const initialMemory = process.memoryUsage().heapUsed;
        
        // Simulate extended conversation
        for (let i = 0; i < 10; i++) {
            await talkingAvatar.handleUserInput(`Message ${i}`);
            await new Promise(resolve => setTimeout(resolve, 100));
        }
        
        // Force garbage collection if available
        if (global.gc) global.gc();
        
        const finalMemory = process.memoryUsage().heapUsed;
        const memoryIncrease = finalMemory - initialMemory;
        
        // Memory increase should be reasonable (less than 50MB)
        expect(memoryIncrease).toBeLessThan(50 * 1024 * 1024);
    });
});
```

### 4. Regression Tests

#### Feature Parity Tests
```javascript
describe('Feature Parity', () => {
    test('should support all current TalkingAvatar features', () => {
        // Core features that must be preserved
        expect(talkingAvatar).toHaveProperty('handleUserInput');
        expect(talkingAvatar).toHaveProperty('_triggerAnimation');
        expect(talkingAvatar).toHaveProperty('setEmotion');
        expect(talkingAvatar).toHaveProperty('speak');
        expect(talkingAvatar).toHaveProperty('stopSpeaking');
        expect(talkingAvatar).toHaveProperty('startListening');
        expect(talkingAvatar).toHaveProperty('stopListening');
    });
    
    test('should maintain backward API compatibility', () => {
        // Ensure existing method signatures are preserved
        expect(typeof talkingAvatar.handleUserInput).toBe('function');
        expect(talkingAvatar.handleUserInput.length).toBe(2); // (input, isAudio)
    });
    
    test('should support existing voice configurations', async () => {
        await talkingAvatar.setVoice('english', 'female');
        
        expect(talkingAvatar.voiceConfig.currentLanguage).toBe('english');
        expect(talkingAvatar.voiceConfig.currentGender).toBe('female');
    });
    
    test('should maintain emotion detection and setting', async () => {
        const emotionSpy = jest.spyOn(talkingAvatar, 'setEmotion');
        
        await talkingAvatar.handleUserInput('I am very happy!');
        
        // Should detect emotion and set it
        expect(emotionSpy).toHaveBeenCalled();
    });
});
```

### 5. Error Handling Tests

#### Network Error Tests
```javascript
describe('Error Handling', () => {
    test('should handle vLLM service unavailable', async () => {
        // Mock network error
        const originalEndpoint = talkingAvatar.agentService.vllmEndpoint;
        talkingAvatar.agentService.vllmEndpoint = 'http://invalid-endpoint:9999';
        
        await expect(
            talkingAvatar.handleUserInput('Hello')
        ).rejects.toThrow();
        
        // Restore endpoint
        talkingAvatar.agentService.vllmEndpoint = originalEndpoint;
    });
    
    test('should gracefully handle tool execution failures', async () => {
        // Mock animation tool failure
        const originalTriggerAnimation = talkingAvatar._triggerAnimation;
        talkingAvatar._triggerAnimation = jest.fn().mockRejectedValue(new Error('Animation failed'));
        
        const response = await talkingAvatar.handleUserInput('Dance for me');
        
        // Should still provide text response even if animation fails
        expect(response).toBeDefined();
        expect(typeof response).toBe('string');
        
        // Restore method
        talkingAvatar._triggerAnimation = originalTriggerAnimation;
    });
    
    test('should handle malformed LLM responses', async () => {
        // This tests the robustness of structured output parsing
        const response = await talkingAvatar.handleUserInput('Generate invalid JSON');
        
        // Should fallback gracefully
        expect(response).toBeDefined();
        expect(typeof response).toBe('string');
    });
});
```

## Test Data and Mocks

### Mock Data Setup
```javascript
// test/mocks/agent-mocks.js
export const mockAnimationRegistry = {
    getById: jest.fn(),
    getByCategory: jest.fn(),
    getAllAnimations: jest.fn()
};

export const mockTTSService = {
    speak: jest.fn(),
    stop: jest.fn(),
    initialize: jest.fn().mockResolvedValue(true)
};

export const mockAudioPlayer = {
    playChunk: jest.fn(),
    stopAll: jest.fn(),
    isPlaying: false
};

export const mockViewer = {
    scene: { add: jest.fn(), remove: jest.fn() },
    camera: {},
    renderer: { domElement: document.createElement('canvas') }
};
```

### Test Scenarios
```javascript
// test/data/test-scenarios.js
export const testScenarios = {
    textInputs: [
        'Hello, how are you?',
        'Tell me a joke',
        'Dance for me',
        'What is 2+2?',
        '你好吗？', // Chinese
        'こんにちは', // Japanese
    ],
    
    animationRequests: [
        'Show me a happy dance',
        'Do a spinning kick',
        'Wave hello',
        'Look sad',
        'Jump with joy'
    ],
    
    multimodalInputs: [
        {
            text: 'What do you see in this image?',
            image: 'data:image/jpeg;base64,...'
        },
        {
            text: 'Transcribe this audio',
            audio: new Float32Array(16000)
        }
    ]
};
```

## CI/CD Integration

### GitHub Actions Workflow
```yaml
# .github/workflows/agent-tests.yml
name: Agent System Tests

on:
  pull_request:
    paths:
      - 'src/agent/**'
      - 'app/viewer/talkingavatar.js'
      - 'test/agent/**'

jobs:
  test:
    runs-on: ubuntu-latest
    
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          
      - name: Install dependencies
        run: npm install
        
      - name: Start vLLM test server
        run: |
          docker run -d --name vllm-test \
            -p 8000:8000 \
            vllm/vllm-openai:latest \
            --model microsoft/DialoGPT-small
            
      - name: Wait for vLLM server
        run: |
          timeout 300 bash -c 'until curl -f http://localhost:8000/health; do sleep 5; done'
          
      - name: Run Agent Tests
        run: npm run test:agent
        env:
          VLLM_ENDPOINT: http://localhost:8000
          
      - name: Run Integration Tests
        run: npm run test:integration
        
      - name: Cleanup
        run: docker stop vllm-test
```

## Test Execution Commands

### Run All Agent Tests
```bash
npm run test:agent
```

### Run Integration Tests Only  
```bash
npm run test:integration
```

### Run Performance Tests
```bash
npm run test:performance
```

### Run Specific Test Categories
```bash
# Unit tests only
npm run test -- --testPathPattern=test/agent/

# Integration tests only  
npm run test -- --testPathPattern=test/integration/

# TalkingAvatar specific tests
npm run test -- --testNamePattern="TalkingAvatar"
```

## Test Reporting

### Coverage Requirements
- **Unit Tests**: 90% coverage for agent components
- **Integration Tests**: 80% coverage for TalkingAvatar integration
- **Feature Tests**: 100% coverage for existing TalkingAvatar features

### Performance Benchmarks
- **Response Time**: < 3 seconds for simple queries
- **Memory Usage**: < 100MB increase during extended sessions  
- **Concurrent Requests**: Handle 3+ simultaneous requests
- **Error Recovery**: < 1 second to handle and recover from errors

### Success Criteria

#### Pre-Deployment Checklist
- [ ] All unit tests pass (100%)
- [ ] All integration tests pass (100%) 
- [ ] Performance tests meet benchmarks
- [ ] Feature parity tests pass (100%)
- [ ] Error handling tests pass (100%)
- [ ] Memory leak tests pass
- [ ] Cross-browser compatibility verified
- [ ] Documentation updated
- [ ] Code review completed

#### Post-Deployment Monitoring
- Monitor response times in production
- Track error rates and types
- Monitor memory usage patterns
- Collect user feedback on animation triggering
- Track conversation quality metrics
