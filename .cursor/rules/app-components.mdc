---
description: 
globs: 
alwaysApply: true
---
# Main Application Components

## Core Application Structure
The application is built with a modular architecture, with the following key components:

### UI Components
- Located in `src/ui/`
- Contains reusable UI components and layouts
- Follows a component-based architecture

### Server Components
- Main server logic in [server.js](mdc:server.js)
- WebSocket handling in [test-websocket.js](mdc:test-websocket.js)
- Three.js server in [serve-three.js](mdc:serve-three.js)

### Recognition System
- Located in `src/recognition/`
- Handles recognition-related functionality
- Integrates with external recognition services

### Animation System
- Located in `src/animation/`
- Manages animation states and transitions
- Provides animation utilities and components

### Effects System
- Located in `src/effects/`
- Handles visual effects and processing
- Provides effect utilities and components

### Visualization
- Located in `src/visualization/`
- Data visualization components
- Chart and graph implementations

## Module Organization
- Core modules in `src/modules/`
- Application-specific modules in `src/apps/`
- Shared utilities in `src/utils/`
- Configuration in `src/config/`

## Testing
- Test files in `src/tests/`
- Mock implementations in `__mocks__/`
- Example implementations in `src/examples/`
