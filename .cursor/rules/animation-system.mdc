---
description:
globs:
alwaysApply: false
---
# Animation System Architecture

The animation system provides a generalized framework for animating 3D meshes with doll bones, extending the functionality of the talking head system to support simpler mesh types.

## Core Components

### BaseAnimator
[BaseAnimator.js](mdc:src/animation/BaseAnimator.js) serves as the foundation for all animation functionality:
- Handles core animation loop and timing
- Manages pose transitions and gestures
- Provides audio analysis for animation
- Supports bone-based animations for doll meshes

### Animation Configuration
[AnimationConfig.js](mdc:src/animation/AnimationConfig.js) centralizes all animation settings:
- Defines bone groups and hierarchies
- Contains pose templates and gesture animations
- Configures timing and physics parameters
- Provides utility functions for animation

### Bone Structure
The system uses a standardized bone hierarchy for doll meshes:
- Core bones (spine, neck, head)
- Arm bones (shoulders, arms, hands)
- Leg bones (hips, legs, feet)
- Finger bones for hand gestures

## Key Features

### Pose System
- Predefined pose templates (side, hip, turn, bend, etc.)
- Smooth pose transitions with easing
- Support for standing, sitting, and kneeling poses
- Weight distribution between legs

### Gesture System
- Hand gesture templates (handup, index, ok, etc.)
- Natural hand movement during speech
- Gesture mirroring for left/right hands
- Finger bone animations

### Animation Pipeline
1. Base pose initialization
2. Continuous idle animations
3. Gesture and pose transitions
4. Audio-driven animations
5. Head and eye movements

## Usage

The animation system can be used with any mesh that follows the doll bone structure:
1. Initialize BaseAnimator with your mesh
2. Configure animation options
3. Start the animation loop
4. Control poses and gestures through the API

## Integration with Talking Head

While the animation system generalizes the talking head functionality, it maintains compatibility:
- Shared bone structure
- Common animation templates
- Unified timing system
- Compatible gesture system

## Best Practices

1. Ensure mesh follows doll bone naming convention
2. Initialize with appropriate animation options
3. Use pose templates for consistent animations
4. Implement proper cleanup on disposal
