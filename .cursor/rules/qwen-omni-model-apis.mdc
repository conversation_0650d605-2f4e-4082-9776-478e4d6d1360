 Qwen-Omni Model APIs: HTTP & Realtime

## 1. Lifecycle & VAD/Manual Modes (Realtime)

**VAD Mode (Voice Activity Detection):**
- Enable by setting `session.update.session.turn_detection = "server_vad"`.
- The server automatically detects speech start/end and responds accordingly. Suitable for voice chat scenarios.

**Interaction Flow:**
1. **Session Initialization**
    - Client: `session.update` (configure session, including VAD)
    - Server: `session.created` (session created)
    - Server: `session.updated` (session config updated)
2. **User Audio Input**
    - Client: `input_audio_buffer.append` (append audio to buffer)
    - Server: `input_audio_buffer.speech_started` (speech detected)
    - Server: `input_audio_buffer.speech_stopped` (speech ended)
    - Server: `input_audio_buffer.commited` (audio buffer committed)
    - Server: `conversation.item.created` (user message created from buffer)
3. **Server Audio Output**
    - Server: `response.created` (response generation started)
    - Server: `response.output_item.added` (new output item)
    - Server: `response.content_part.added` (assistant message content chunk)
    - Server: `response.audio_transcript.delta` (incremental transcript)
    - Server: `response.audio.delta` (incremental audio)
    - Server: `response.audio_transcript.done` (transcript complete)
    - Server: `response.audio.done` (audio complete)
    - Server: `response.content_part.done` (content part complete)
    - Server: `response.output_item.done` (output item complete)
    - Server: `response.done` (response complete)

**Manual Mode:**
- Omit `turn_detection` or set to manual. Client controls audio chunking and commit.

---

## Lifecycle: Qwen-Omni Realtime Session

**1. Session Initialization**
- Client connects to the WebSocket endpoint.
- Client sends a `session.update` event with configuration (modalities, VAD, audio formats, etc.).
- Server responds with `session.created` or `session.updated`.

**2. Multimodal Streaming**
- **Audio**: Client continuously streams PCM16 audio chunks via `input_audio_buffer.append`.
- **Video**: Client periodically sends image frames via `input_image_buffer.append` (2 FPS recommended).
- Server processes both audio and visual input for comprehensive understanding.

**3. Server VAD Events**
- Server emits:
  - `input_audio_buffer.speech_started` (speech detected)
  - `input_audio_buffer.speech_stopped` (speech ended)
  - `input_audio_buffer.committed` (audio segment ready for processing)

**4. Response Generation**
- Server emits:
  - `response.created` (processing started)
  - `conversation.item.created` (user message created from audio/video)
  - `response.audio.delta` (streaming audio response)
  - `response.audio_transcript.delta` (streaming text transcription)
  - `response.done` (response complete)

**5. Session Management**
- Client can send `input_audio_buffer.clear` to reset audio buffer
- Client can manage session with `session.update` events
- Server handles automatic recovery and reconnection

## Implementation Notes

**MediaCaptureManager Configuration for Server VAD:**
```javascript
// CORRECT: Set onAudioData callback in constructor to avoid warnings
const manager = new MediaCaptureManager({
    audio: { echoCancellation: true, noiseSuppression: true },
    video: { width: 640, height: 480 }, // Multimodal support
    vadMode: 'server',
    onAudioData: (pcm16Buffer) => {
        // Stream to WebSocket via input_audio_buffer.append
        websocket.send(pcm16Buffer);
    },
    onFrame: (frameInfo) => {
        // Send periodic frames via input_image_buffer.append
        if (frameInfo.dataUrl) {
            websocket.send(frameInfo.dataUrl);
        }
    },
    maxFrames: 10,
    captureRateMs: 500 // 2 FPS for video
});

// INCORRECT: Setting callback after construction causes warning
const manager = new MediaCaptureManager({ vadMode: 'server' });
manager.options.onAudioData = callback; // ⚠️ Triggers warning
```

**Best Practices:**
- Always provide `onAudioData` callback in constructor for server VAD mode
- Enable multimodal capture (audio + video) for enhanced understanding
- Use 16kHz PCM16 audio format for optimal compatibility
- Implement proper error handling and reconnection logic
- Handle all lifecycle events for robust user experience

**4. LLM Processing**
- Server sends:
  - `response.created` (response generation started)
  - `response.audio.delta` (streaming audio response, if enabled)
  - `response.done` (response complete)

**5. Transcription (if enabled)**
- Server emits:
  - `conversation.item.input_audio_transcription.completed` (transcription result)
  - `conversation.item.input_audio_transcription.failed` (if error)

**6. Session End**
- Client or server can close the session at any time.

---

## 2. Multi-round Conversation

- Both models require the full `messages` array for context. The API does not store history; you must send all previous messages for context.
- **Example:**
  ```js
  const messages = [
    { role: "system", content: "You are a helpful assistant." },
    { role: "user", content: "Hello" },
    { role: "assistant", content: "Hi, how can I help you?" },
    { role: "user", content: "Tell me a joke." }
  ];
  ```
- **References:**
  - [Multi-round conversation doc](https://www.alibabacloud.com/help/en/model-studio/multi-round-conversation?spm=a2c63.p38356.help-menu-2400256.d_0_1_1.66585a168nQ4Ii&scm=20140722.H_2866125._.OR_help-T_intl~en-V_1)

---

## 3. Omni Modality Reference

- **Supported Modalities:** text, audio, image, video
- **Example payload:**
  ```js
  {
    role: "user",
    content: [
      { type: "text", text: "Describe this image" },
      { type: "image_url", image_url: { url: "data:image/jpeg;base64,..." } }
    ]
  }
  ```
- **References:**
  - [Qwen-Omni Modality](https://www.alibabacloud.com/help/en/model-studio/qwen-omni?spm=a2c63.p38356.help-menu-2400256.d_0_2_3.7b9f6bebW7JbcN)

---

## 4. Prompt Engineering Guidance

- See `/src/agent/prompts` for prompt templates and best practices.
- **Best practices:**
  - Use clear system instructions.
  - Maintain role separation (`system`, `user`, `assistant`).
  - Leverage prompt templates for consistent behavior.
- **References:**
  - [Prompt Engineering Guide](https://www.alibabacloud.com/help/en/model-studio/prompt-engineering-guide?spm=a2c63.p38356.help-menu-2400256.d_0_8_0.3d8e11d0UUm4kU)

---

## 5. Tool Calling & LangChain JS Integration

- **Supported:** Both HTTP and Realtime models support tool calling.
- **Integration:**
  - See `/src/agent/models/AliyunBailianChatModel.js` for model integration.
  - See `/src/agent/tools/tts.js` for TTS and audio tool examples.
  - Tool schemas use [zod](https://github.com/colinhacks/zod) for validation.
  - Tool binding and invocation follow LangChain v0.3 and LangGraph JS patterns.
- **References:**
  - [Aliyun Tool Calling Doc](https://help.aliyun.com/document_detail/2862208.html)

### LangChain JS Tool Calling Example

```js
import { z } from 'zod';

const GetWeather = {
  name: "GetWeather",
  description: "Get the current weather in a given location",
  schema: z.object({
    location: z.string().describe("The city and state, e.g. San Francisco, CA")
  }),
}

const GetPopulation = {
  name: "GetPopulation",
  description: "Get the current population in a given location",
  schema: z.object({
    location: z.string().describe("The city and state, e.g. San Francisco, CA")
  }),
}

const llmWithTools = llm.bindTools([GetWeather, GetPopulation]);
const aiMsg = await llmWithTools.invoke(
  "Which city is hotter today and which is bigger: LA or NY?"
);
console.log(aiMsg.tool_calls);
```

**Sample tool_calls output:**
```js
[
  { name: 'GetWeather', args: { location: 'Los Angeles, CA' }, type: 'tool_call' },
  { name: 'GetWeather', args: { location: 'New York, NY' }, type: 'tool_call' },
  { name: 'GetPopulation', args: { location: 'Los Angeles, CA' }, type: 'tool_call' },
  { name: 'GetPopulation', args: { location: 'New York, NY' }, type: 'tool_call' }
]
```

### Structured Output Example

```js
const Joke = z.object({
  setup: z.string().describe("The setup of the joke"),
  punchline: z.string().describe("The punchline to the joke"),
  rating: z.number().optional().describe("How funny the joke is, from 1 to 10")
}).describe('Joke to tell user.');

const structuredLlm = llm.withStructuredOutput(Joke, { name: "Joke" });
const jokeResult = await structuredLlm.invoke("Tell me a joke about cats");
console.log(jokeResult);
```

**Sample output:**
```js
{
  setup: "Why don't cats play poker?",
  punchline: "Why don't cats play poker? Because they always have an ace up their sleeve!"
}
```

### JSON Object Response Format

```js
const jsonLlm = llm.bind({ response_format: { type: "json_object" } });
const jsonLlmAiMsg = await jsonLlm.invoke(
  "Return a JSON object with key 'randomInts' and a value of 10 random ints in [0-99]"
);
console.log(jsonLlmAiMsg.content);
```

**Sample output:**
```js
{
  "randomInts": [23, 87, 45, 12, 78, 34, 56, 90, 11, 67]
}
```

### Multimodal Input Example

```js
import { HumanMessage } from '@langchain/core/messages';

const imageUrl = "https://example.com/image.jpg";
const imageData = await fetch(imageUrl).then(res => res.arrayBuffer());
const base64Image = Buffer.from(imageData).toString('base64');

const message = new HumanMessage({
  content: [
    { type: "text", text: "describe the weather in this image" },
    {
      type: "image_url",
      image_url: { url: `data:image/jpeg;base64,${base64Image}` },
    },
  ]
});

const imageDescriptionAiMsg = await llm.invoke([message]);
console.log(imageDescriptionAiMsg.content);
```

---

## 6. Role Playing

- **Reference:**
  - `/src/agent/prompts` for role templates.
  - [Aliyun Role Playing Doc](https://help.aliyun.com/document_detail/2874763.html)
- **Usage:**
  - Set `role` in messages (`system`, `user`, `assistant`, or custom roles).
  - Use prompt templates to define persona/role behavior.

---

## 7. Voice Cloning

- **Reference:**
  - [Aliyun Voice Cloning Doc](https://help.aliyun.com/document_detail/2842586.html)
  - `/src/agent/tools/tts.js` (see `AgentTTSService`)
  - `/ui.js` and `/talkingavatar.js` for UI integration
- **Usage:**
  - Pass `voiceProfile` or `referenceAudio` in TTS tool calls for voice cloning.
  - See `AgentTTSService.speak()` for implementation details.

---

## 6. Input Restrictions & Error Codes (Video, Audio, Multimodal)

### Video Input Restrictions
- **Supported Formats:** MP4, AVI, MKV, MOV, FLV, WMV
- **Max File Size:**
  - Qwen2.5-VL series: ≤ 1 GB (video URL), ≤ 10 MB (Base64 via OpenAI SDK), ≤ 100 MB (DashScope SDK)
  - Other models: ≤ 150 MB (video URL)
- **Duration:**
  - Qwen2.5-VL series: 2 seconds – 10 minutes
  - Other models: 2 seconds – 40 seconds
- **Dimensions:** No strict limit, but files are resized to ~600,000 pixels for processing
- **Audio in Video:** Not supported for video understanding (audio is ignored)
- **Frame Extraction:**
  - Default: 0.5s per frame (OpenAI SDK, not configurable)
  - DashScope SDK: `fps` parameter controls frame extraction rate

### Audio Input Restrictions
- **Format:** PCM16, 16kHz recommended
- **Silence Handling:** Silent segments in audio will result in the video subject closing their mouth (for video retalk)
- **Duration Mismatch:**
  - By default, output is truncated to the shorter of audio/video
  - To extend video to match longer audio, set `parameters.video_extension = true` (video will loop in reverse/forward alternation)

### Image Input Restrictions
- **Format:** JPEG, PNG, etc. (see model docs)
- **Max Size:** Recommended ≤ 1,003,520 pixels per image
- **Multi-image:** Total token count for all images must be within model's max input (see QVQ model docs)

### Multi-person Video
- Only one person can be replaced in video retalk
- If multiple faces, the largest face is chosen unless a reference image is provided (`input.ref_image_url`)
- If no face is detected, or face does not match reference, error 400 is returned

### Error Codes (Common)
| Code | Key                      | Description (EN)                                        | Description (CN)         |
| ---- | ------------------------ | ------------------------------------------------------- | ------------------------ |
| 400  | InvalidFile.Content      | The input image has no human body or multi human bodies | 输入图片中没有人或有多人 |
| 400  | InvalidFile.FaceNotMatch | No matched face in video with provided reference image  | 参考图与视频人脸匹配失败 |

---

## 7. Common Questions & Best Practices

1. **Audio/Video Length Mismatch:**
   - Output is truncated to the shorter duration by default
   - To extend video to match audio, set `parameters.video_extension = true` (video will loop as needed)
2. **Silent Audio Segments:**
   - Video subject will close mouth during silent audio
3. **No/Partial Face in Video:**
   - If audio has speech but video has no/partial face, original video is retained, audio plays as normal
4. **Multiple People in Video:**
   - Only one person can be replaced; reference image can specify which face
   - If no reference, largest face is chosen

---

## 8. Example: Multimodal Payloads

### Video + Text
```js
{
  role: "user",
  content: [
    { type: "video_url", video_url: { url: "https://example.com/video.mp4" } },
    { type: "text", text: "Describe this video." }
  ]
}
```

### Audio + Video (Video Retalk)
```js
{
  role: "user",
  content: [
    { type: "audio_url", audio_url: { url: "https://example.com/audio.wav" } },
    { type: "video_url", video_url: { url: "https://example.com/video.mp4" } },
    { type: "text", text: "Lip sync this video to the audio." }
  ],
  parameters: { video_extension: true }
}
```

---

## Codebase Cross-References
- `/src/agent/models/AliyunBailianChatModel.js`: Model integration, tool calling, multimodal support
- `/src/agent/tools/tts.js`: TTS, voice cloning, audio playback tools
- `/src/agent/prompts/`: Prompt templates and role guidance
- `/ui.js`, `/talkingavatar.js`: UI and avatar integration for voice cloning

---

## Official Documentation Links
- [Realtime API](https://help.aliyun.com/zh/model-studio/realtime)
- [Multi-round Conversation](https://www.alibabacloud.com/help/en/model-studio/multi-round-conversation?spm=a2c63.p38356.help-menu-2400256.d_0_1_1.66585a168nQ4Ii&scm=20140722.H_2866125._.OR_help-T_intl~en-V_1)
- [Qwen-Omni Modality](https://www.alibabacloud.com/help/en/model-studio/qwen-omni?spm=a2c63.p38356.help-menu-2400256.d_0_2_3.7b9f6bebW7JbcN)
- [Prompt Engineering Guide](https://www.alibabacloud.com/help/en/model-studio/prompt-engineering-guide?spm=a2c63.p38356.help-menu-2400256.d_0_8_0.3d8e11d0UUm4kU)
- [Tool Calling](https://help.aliyun.com/document_detail/2862208.html)
- [Role Playing](https://help.aliyun.com/document_detail/2874763.html)
- [Voice Cloning](https://help.aliyun.com/document_detail/2842586.html)
