---
description:
globs:
alwaysApply: false
---
# Mem0 Integration Guide

## Overview
This guide outlines how to integrate Mem0's memory capabilities with the TalkingHead agent system. Mem0 provides a powerful memory layer that can enhance the avatar's conversational abilities and personalization.

## Key Components

### Memory Layer
The memory layer is implemented in [memory.js](mdc:src/modules/talkinghead/src/agent/memory.js) and provides:
- Long-term memory storage
- Context-aware memory retrieval
- Memory persistence across sessions

### Agent Integration
The main agent implementation is in [agent.js](mdc:src/modules/talkinghead/src/agent/agent.js) and includes:
- Mood and emotion management
- Pose and gesture control
- System prompt generation
- Memory-enhanced responses

## Integration Steps

1. Initialize Mem0:
```javascript
import { Memory } from 'mem0ai';

const memory = new Memory({
    // Configuration options
    maxTokens: 1000,
    temperature: 0.7
});
```

2. Enhance Agent with Memory:
```javascript
// In agent.js
const avatarAgent = {
    memory: null,
    
    initialize(talkingHead) {
        // Existing initialization
        this.talkingHead = talkingHead;
        this.status = new AvatarStatus();
        
        // Initialize Mem0
        this.memory = new Memory();
        this.initialized = true;
    }
};
```

3. Memory-Enhanced Response Generation:
```javascript
async function generateResponse(text, user_id) {
    // Retrieve relevant memories
    const relevant_memories = await memory.search({
        query: text,
        user_id: user_id,
        limit: 3
    });
    
    // Format memories for context
    const memories_str = relevant_memories.results
        .map(entry => `- ${entry.memory}`)
        .join('\n');
        
    // Generate response with memory context
    const system_prompt = `You are a helpful AI. Answer based on query and memories.
User Memories:
${memories_str}`;
    
    // Use with existing LLM integration
    return generateLLMResponse(system_prompt, text);
}
```

## Best Practices

1. Memory Management:
- Store important user preferences and context
- Use memory for personalization
- Implement memory cleanup for outdated information

2. Performance Optimization:
- Limit memory retrieval to relevant context
- Use appropriate memory chunking
- Implement caching for frequently accessed memories

3. Error Handling:
- Implement fallbacks for memory operations
- Handle memory initialization failures gracefully
- Log memory-related errors for debugging

## API Reference

### Memory Operations
- `memory.add(messages, user_id)`: Add new memories
- `memory.search(query, user_id, limit)`: Retrieve relevant memories
- `memory.clear(user_id)`: Clear user memories
- `memory.getStats()`: Get memory statistics

### Agent Integration
- `avatarAgent.setMoodFromText(text)`: Set mood with memory context
- `avatarAgent.setPoseFromText(text)`: Set pose with memory context
- `avatarAgent.prepareSystemPrompt()`: Generate prompts with memory

## Example Usage

```javascript
// Initialize with memory
await avatarAgent.initialize(talkingHead);

// Generate response with memory
const response = await avatarAgent.generateResponse(
    "What's my favorite color?",
    "user123"
);

// Add to memory
await avatarAgent.memory.add([
    { role: "user", content: "My favorite color is blue" },
    { role: "assistant", content: "I'll remember that!" }
], "user123");
```

## Troubleshooting

Common issues and solutions:
1. Memory initialization failures
2. Slow memory retrieval
3. Memory context relevance
4. Memory persistence issues

For more details, refer to the [Mem0 documentation](https://docs.mem0.ai/overview).
