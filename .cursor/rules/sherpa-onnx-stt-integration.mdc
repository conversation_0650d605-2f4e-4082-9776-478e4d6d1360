---
description: 
globs: 
alwaysApply: false
---
# Sherpa-ONNX STT Integration Guide

This guide explains how to integrate the Sherpa-ONNX WebSocket server with the existing STT services implementation.

## Integration Overview

### 1. New STT Service Class
Add a new `SherpaOnnxSTTService` class that extends the base `STTService`:

```javascript
class SherpaOnnxSTTService extends STTService {
    constructor(options = {}) {
        super(options);
        this.wsServer = null;
        this.port = options.port || 6006;
        this.modelConfig = options.modelConfig || {
            transducer: {
                encoder: './path/to/encoder.onnx',
                decoder: './path/to/decoder.onnx',
                joiner: './path/to/joiner.onnx',
            },
            tokens: './path/to/tokens.txt',
            numThreads: 1,
            provider: 'cpu',
            sampleRate: 16000
        };
    }
}
```

### 2. WebSocket Server Integration
The service should automatically start the WebSocket server when initialized:

```javascript
async initialize() {
    try {
        // Start WebSocket server
        this.wsServer = await this._startWebSocketServer();
        return true;
    } catch (error) {
        console.error('[SherpaOnnxSTTService] Initialization failed:', error);
        return false;
    }
}
```

### 3. Audio Processing
Handle audio processing through WebSocket:

```javascript
async listen(options = {}) {
    if (!this.wsServer) {
        throw new Error('WebSocket server not initialized');
    }

    // Create WebSocket connection
    const ws = new WebSocket(`ws://localhost:${this.port}`);
    
    // Handle audio streaming
    ws.onopen = () => {
        // Start audio capture
        this._startAudioCapture(ws);
    };

    // Handle transcription results
    ws.onmessage = (event) => {
        const result = JSON.parse(event.data);
        this.onResult(result.text, result.type === 'final');
    };
}
```

## Implementation Details

### 1. WebSocket Server Setup
The server should be started automatically when the service is initialized:

```javascript
async _startWebSocketServer() {
    const server = http.createServer();
    const wss = new WebSocket.Server({ server });
    
    // Initialize Sherpa-ONNX recognizer
    const recognizer = new sherpa_onnx.OnlineRecognizer(this.modelConfig);
    
    wss.on('connection', (ws) => {
        let stream = recognizer.createStream();
        
        ws.on('message', async (message) => {
            if (Buffer.isBuffer(message)) {
                // Process audio data
                const samples = this._convertToFloat32(message);
                stream.acceptWaveform(this.modelConfig.sampleRate, samples);
                
                // Get transcription
                while (recognizer.isReady(stream)) {
                    recognizer.decode(stream);
                }
                
                const result = recognizer.getResult(stream);
                ws.send(JSON.stringify({
                    type: 'partial',
                    text: result.text
                }));
            }
        });
    });
    
    server.listen(this.port);
    return server;
}
```

### 2. Audio Format Conversion
Handle audio format conversion for Sherpa-ONNX:

```javascript
_convertToFloat32(buffer) {
    const pcm16Data = new Int16Array(buffer.buffer, buffer.byteOffset, buffer.length / 2);
    const samples = new Float32Array(pcm16Data.length);
    for (let i = 0; i < pcm16Data.length; i++) {
        samples[i] = pcm16Data[i] / 32768.0;
    }
    return samples;
}
```

### 3. Service Factory Integration
Update the `STTServiceFactory` to support Sherpa-ONNX:

```javascript
static createService(type, options = {}) {
    switch (type.toLowerCase()) {
        // ... existing cases ...
        case 'sherpa-onnx':
            return new SherpaOnnxSTTService(options);
        default:
            throw new Error(`Unknown STT service type: ${type}`);
    }
}
```

## Usage Example

```javascript
// Create Sherpa-ONNX STT service
const sttService = STTServiceFactory.createService('sherpa-onnx', {
    port: 6006,
    modelConfig: {
        transducer: {
            encoder: './models/encoder.onnx',
            decoder: './models/decoder.onnx',
            joiner: './models/joiner.onnx',
        },
        tokens: './models/tokens.txt',
        numThreads: 1,
        provider: 'cpu',
        sampleRate: 16000
    }
});

// Initialize service (starts WebSocket server)
await sttService.initialize();

// Start listening
const { stopListening } = await sttService.listen({
    language: 'en',
    continuous: true
});
```

## Best Practices

1. **Error Handling**
   - Implement proper error handling for WebSocket connections
   - Handle server startup failures gracefully
   - Manage connection timeouts and disconnections

2. **Resource Management**
   - Clean up WebSocket connections properly
   - Release audio resources when stopping
   - Handle server shutdown gracefully

3. **Configuration**
   - Make port configurable
   - Allow model path customization
   - Support different audio formats

4. **Performance**
   - Optimize audio processing
   - Handle concurrent connections efficiently
   - Implement proper buffering

## Security Considerations

1. **WebSocket Security**
   - Implement proper authentication
   - Use secure WebSocket (WSS) in production
   - Validate incoming connections

2. **Resource Limits**
   - Implement connection limits
   - Monitor memory usage
   - Handle large audio streams properly
