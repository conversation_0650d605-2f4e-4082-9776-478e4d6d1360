---
description: 
globs: 
alwaysApply: true
---
# Project Structure Overview

This project is a TypeScript-based application with a modular architecture. Here's the key structure:

## Core Directories
- `src/` - Main source code directory
  - `apps/` - Application-specific code
  - `modules/` - Reusable modules and components
  - `ui/` - User interface components
  - `server/` - Server-side code
  - `utils/` - Utility functions and helpers
  - `config/` - Configuration files
  - `recognition/` - Recognition-related functionality
  - `animation/` - Animation components and logic
  - `effects/` - Visual effects and processing
  - `visualization/` - Data visualization components

## Configuration Files
- [vite.config.ts](mdc:vite.config.ts) - Vite build configuration
- [tsconfig.json](mdc:tsconfig.json) - TypeScript configuration
- [package.json](mdc:package.json) - Project dependencies and scripts

## Server
- [server.js](mdc:server.js) - Main server entry point
- [nginx.conf](mdc:nginx.conf) - Nginx server configuration

## Documentation
- [README.md](mdc:README.md) - Project documentation
- [PHONE_APP_DEBUGGING.md](mdc:PHONE_APP_DEBUGGING.md) - Debugging guide

Before do code implementation, find the src on exisiting code to avoid implementation redudency. 
