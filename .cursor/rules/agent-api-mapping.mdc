---
description:
globs:
alwaysApply: false
---
# Agent System API Mapping Guide

## Overview

This guide provides detailed mapping between current TalkingAvatar implementation and the new agent system APIs. Use this for implementing the replacement strategy.

## Core Service Mapping

### Current Dual Service Pattern → Agent Service

#### Before (Current Implementation)
```javascript
// In TalkingAvatar constructor
import { LLMStreamProcessor } from '@/media/streaming/index.ts';
import { getLLMService } from '@/server/llm.ts';

class TalkingAvatar {
    constructor(viewer, options = {}) {
        this.streamProcessor = null; // LLMStreamProcessor instance
        this.useStreaming = true;
    }
    
    async _initializedStreaming() {
        this.streamProcessor = new LLMStreamProcessor({...});
    }
    
    async _getServerLLMResponse(input, contextInfo = {}) {
        const llmService = await getLLMService();
        const result = await llmService.generateResponse(input, options);
    }
}
```

#### After (Agent System)
```javascript
// In TalkingAvatar constructor  
import { LangChainCoreService } from '@/agent/core.js';

class TalkingAvatar {
    constructor(viewer, options = {}) {
        this.agentService = null; // LangChainCoreService instance
        this.useStreaming = true; // Maintained for compatibility
    }
    
    async _initializeAgentService() {
        this.agentService = new LangChainCoreService({
            vllmEndpoint: this._getVllmEndpoint(),
            ttsService: this.ttsServiceInstance,
            audioPlayer: this.streamingAudioPlayer,
            animationController: this
        });
    }
    
    async _getAgentResponse(input, contextInfo = {}) {
        const response = await this.agentService.generateResponse(input, {
            ...contextInfo,
            stream: this.useStreaming,
            useTools: true
        });
    }
}
```

## Method Mapping

### 1. Text Processing Methods

#### `handleUserInput()` Method Updates

**Current Implementation** (lines 1925-2059 in [talkingavatar.js](mdc:app/viewer/talkingavatar.js)):
```javascript
async handleUserInput(input, isAudio = false) {
    // Current flow: Complex branching based on llmService type
    switch (this.llmService) {
        case 'serverLLM':
            aiResponseText = await this._getServerLLMResponse(
                isAudio && this.bypassASR ? audioData : text, 
                contextInfo
            );
            break;
        // Other cases...
    }
}
```

**Agent System Implementation**:
```javascript
async handleUserInput(input, isAudio = false) {
    // Simplified unified flow
    let processedInput = input;
    
    // Handle audio input conversion if needed
    if (isAudio && input instanceof Float32Array) {
        processedInput = await this._convertAudioForAgent(input);
    }
    
    const contextInfo = this._buildContextInfo(isAudio);
    const response = await this._getAgentResponse(processedInput, contextInfo);
    
    // Handle UI updates and callbacks
    this._handleResponseCallbacks(response);
    
    return response;
}
```

#### `_getServerLLMResponse()` → `_getAgentResponse()`

**Current Implementation** (lines 2079-2249 in [talkingavatar.js](mdc:app/viewer/talkingavatar.js)):
```javascript
async _getServerLLMResponse(input, contextInfo = {}) {
    const { language = 'english', gender = 'male', mood = 'neutral', isAudio = false } = contextInfo;
    
    // Get LLM service
    const llmService = await getLLMService();
    
    // Handle streaming initialization
    if (this.useStreaming && !this.streamProcessor) {
        await this._initializedStreaming();
    }
    
    // Complex branching for audio vs text
    if (isAudio) {
        // Audio processing path with conversion and media stream processing
        const mediaResponse = await llmService.processMediaStream({...});
        // Animation analysis for audio responses
        const animationResult = await generateResponse(animationAnalysisPrompt, {...});
    } else {
        // Text processing path with animation support
        const result = await generateResponse(input, {
            streamProcessor: this.streamProcessor,
            useAnimation: true,
            onAnimationTrigger: async (animation) => {
                await this._triggerAnimation(animation);
            }
        });
    }
}
```

**Agent System Implementation**:
```javascript
async _getAgentResponse(input, contextInfo = {}) {
    const { 
        language = 'english', 
        gender = 'male', 
        mood = 'neutral', 
        isAudio = false,
        asrMetadata = null 
    } = contextInfo;
    
    // Single unified call - agent handles multimodal automatically
    const response = await this.agentService.generateResponse(input, {
        language,
        gender,
        mood,
        sessionId: this._getSessionId(),
        useTools: true, // Enables animation tools
        stream: this.useStreaming,
        asrMetadata,
        // Agent service will automatically call registered tools including animation
    });
    
    // Extract structured response
    const { response: responseText, animation, reasoning } = response;
    
    // Animation is already triggered via tools, just log
    if (animation) {
        console.log(`[TalkingAvatar] Animation triggered via agent tools: ${animation}`);
    }
    
    return responseText;
}
```

### 2. Streaming Methods

#### `_initializedStreaming()` → `_initializeAgentService()`

**Current Implementation** (lines 612-704 in [talkingavatar.js](mdc:app/viewer/talkingavatar.js)):
```javascript
async _initializedStreaming() {
    // Complex LLMStreamProcessor setup with multiple callbacks
    this.streamProcessor = new LLMStreamProcessor({
        ttsService: this.ttsServiceInstance,
        skipTTS: false,
        enableLogging: true,
        maxBufferTime: 5.0,
        minBufferTime: 0.2,
        audioPlayer: this.streamingAudioPlayer,
        ttsChunkConfig: {
            bySentence: true,
            minLength: 10,
            maxLength: 100,
            timeoutMs: 500
        },
        onTextChunk: (data) => {
            // UI updates
            if (data.fullText && this.onConversationUpdate) {
                this.onConversationUpdate({
                    type: 'assistantMessage',
                    text: data.fullText,
                    isPartial: true
                });
            }
        },
        onAudioChunk: (audioData) => {
            // Speaking state management
            this._setSpeakingState(true);
        },
        onAnimationTrigger: async (animation) => {
            // Animation system integration
            await this._triggerAnimation(animation);
        }
    });
    
    const initialized = await this.streamProcessor.initialize();
}
```

**Agent System Implementation**:
```javascript
async _initializeAgentService() {
    // Simple agent service initialization
    this.agentService = new LangChainCoreService({
        vllmEndpoint: this._getVllmEndpoint(),
        model: this._getModel(),
        temperature: 0.7,
        maxTokens: 2048,
        services: {
            ttsService: this.ttsServiceInstance,
            audioPlayer: this.streamingAudioPlayer,
            animationController: this // TalkingAvatar acts as animation controller
        }
    });
    
    // Agent service auto-initializes tools including animation tools
    // Tools are automatically bound to the LLM instance
    
    console.log('[TalkingAvatar] Agent service initialized with animation tools');
    return true;
}
```

### 3. Animation Integration

#### Current Animation Callback Pattern → Tool-based Animation

**Current Implementation**:
```javascript
// Manual animation callback in generateResponse options
const result = await generateResponse(input, {
    useAnimation: true,
    onAnimationTrigger: async (animation) => {
        console.log('[TalkingAvatar] Animation triggered by LLM callback:', animation);
        await this._triggerAnimation(animation);
    }
});
```

**Agent System Implementation**:
```javascript
// Animation tools are registered during agent service initialization
// In _initializeAgentService():
this.agentService = new LangChainCoreService({
    services: {
        animationController: this // Pass TalkingAvatar as controller
    }
});

// Animation tool will automatically call this._triggerAnimation() when invoked by LLM
// No manual callback setup needed - handled by tool system

// TalkingAvatar must implement animation controller interface:
async triggerAnimation(animationData) {
    // This method will be called by animation tools
    return this._triggerAnimation(animationData);
}
```

### 4. Conversation Context

#### Current Session Management → Agent Session Management

**Current Implementation**:
```javascript
// Session ID derived from mesh filename
const sessionId = meshFileName || 'default-session';

// Conversation history managed in llm.ts
import { addConversationTurn, getConversationHistory } from '../../src/server/llm.ts';
addConversationTurn(sessionId, text, responseText);
```

**Agent System Implementation**:
```javascript
// Session ID still derived from mesh filename
const sessionId = this._getSessionId();

// Conversation context handled by agent service automatically
const response = await this.agentService.generateResponse(input, {
    sessionId, // Agent service maintains conversation history internally
    // History is automatically included in prompts
});

// Helper method for consistent session ID
_getSessionId() {
    return this.avatarMesh?.userData?.fileName || 'default-session';
}
```

## Configuration Mapping

### Environment and Endpoint Configuration

#### Current vLLM Endpoint Configuration
```javascript
// In _initializedStreaming()
const downloadServerPort = typeof window !== 'undefined' &&
    window.location &&
    window.location.origin ?
    window.location.origin.replace(/:\d+/, `:${import.meta.env.VITE_DOWNLOAD_SERVER_PORT || '2994'}`) :
    'http://localhost:2994';

const vllmEndpoint = `${downloadServerPort}/vllm-proxy/v1/chat/completions`;
```

#### Agent System Endpoint Configuration
```javascript
// In _initializeAgentService()
_getVllmEndpoint() {
    // Use config from agent system
    return config.endpoints.vllm || this._buildVllmEndpoint();
}

_buildVllmEndpoint() {
    const downloadServerPort = typeof window !== 'undefined' &&
        window.location &&
        window.location.origin ?
        window.location.origin.replace(/:\d+/, `:${import.meta.env.VITE_DOWNLOAD_SERVER_PORT || '2994'}`) :
        'http://localhost:2994';
    
    return `${downloadServerPort}/vllm-proxy`;
}
```

## Error Handling Mapping

### Current Error Handling → Agent Error Handling

#### Current Error Patterns
```javascript
try {
    const result = await llmService.generateResponse(input, options);
    if (!result.success) {
        throw new Error(result.error || 'Failed to generate response');
    }
} catch (error) {
    console.error('[TalkingAvatar] Error getting server LLM response:', error);
    throw error;
}
```

#### Agent System Error Handling
```javascript
try {
    const response = await this.agentService.generateResponse(input, options);
    // Agent service throws on errors, returns structured response on success
    return response.response || response;
} catch (error) {
    console.error('[TalkingAvatar] Error in agent response:', error);
    
    // Graceful degradation
    if (error.name === 'NetworkError') {
        return "I'm having trouble connecting to my language service. Please try again.";
    } else if (error.name === 'ToolError') {
        return "I understood your request but had trouble with the animation. Let me respond anyway: ...";
    }
    
    throw error;
}
```

## Callback and Event Mapping

### UI Update Callbacks

#### Current LLMStreamProcessor Callbacks
```javascript
onTextChunk: (data) => {
    if (data.fullText && this.onConversationUpdate) {
        this.onConversationUpdate({
            type: 'assistantMessage',
            text: data.fullText,
            isPartial: true
        });
    }
},
onAudioChunk: (audioData) => {
    this._setSpeakingState(true);
},
onComplete: (data) => {
    this._setSpeakingState(false);
}
```

#### Agent System Stream Handling
```javascript
// In _getAgentResponse() when streaming is enabled
if (this.useStreaming) {
    const stream = await this.agentService.generateResponse(input, {
        ...options,
        stream: true
    });
    
    let fullText = '';
    for await (const chunk of stream) {
        if (chunk.content) {
            fullText += chunk.content;
            
            // UI update callback
            if (this.onConversationUpdate) {
                this.onConversationUpdate({
                    type: 'assistantMessage',
                    text: fullText,
                    isPartial: !chunk.done
                });
            }
        }
        
        // Handle tool calls in stream
        if (chunk.tool_calls) {
            // Tools are executed automatically by agent service
            // Animation tools will call this._triggerAnimation automatically
        }
    }
    
    return fullText;
}
```

## Initialization Sequence Mapping

### Current TalkingAvatar Initialization
```javascript
async initialize() {
    // 1. Initialize basic components
    // 2. Initialize TTS service
    await this._initializeTTSService();
    
    // 3. Initialize streaming if enabled
    if (this.useStreaming) {
        await this._initializedStreaming();
    }
    
    // 4. Initialize STT service
    await this._initializeSTTService();
}
```

### Agent System Initialization
```javascript
async initialize() {
    // 1. Initialize basic components (same)
    
    // 2. Initialize TTS service (same)
    await this._initializeTTSService();
    
    // 3. Initialize agent service (replaces streaming initialization)
    await this._initializeAgentService();
    
    // 4. Initialize STT service (same)  
    await this._initializeSTTService();
}
```

## Dependencies Update

### Import Statement Changes

#### Remove Current Imports
```javascript
// Remove these imports
import { LLMStreamProcessor } from '@/media/streaming/index.ts';
import { getLLMService } from '@/server/llm.ts';
```

#### Add Agent System Imports
```javascript
// Add these imports
import { LangChainCoreService } from '@/agent/core.js';
import { config } from '@/server/config.ts'; // For endpoint configuration
```

### Property Updates in Constructor

#### Remove Current Properties
```javascript
// Remove these properties
this.streamProcessor = null;
// Keep useStreaming for compatibility
```

#### Add Agent Properties
```javascript
// Add these properties
this.agentService = null;
this.agentInitialized = false;
```

## Utility Methods

### New Helper Methods for Agent Integration

```javascript
class TalkingAvatar {
    /**
     * Get vLLM endpoint for agent service
     */
    _getVllmEndpoint() {
        return config.endpoints.vllm || this._buildVllmEndpoint();
    }
    
    /**
     * Get model configuration for agent service
     */
    _getModel() {
        return config.llm.vllm.model || 'meta-llama/Llama-2-7b-chat-hf';
    }
    
    /**
     * Build context info for agent requests
     */
    _buildContextInfo(isAudio = false) {
        return {
            language: this.voiceConfig.currentLanguage,
            gender: this.voiceConfig.currentGender,
            mood: this.moodName || 'neutral',
            isAudio,
            asrMetadata: this._lastTranscriptionMetadata
        };
    }
    
    /**
     * Handle agent response and callbacks
     */
    _handleResponseCallbacks(response) {
        // Notify UI of completion
        if (this.onConversationUpdate) {
            this.onConversationUpdate({
                type: 'aiMessage',
                text: response
            });
        }
        
        // Update emotional state if needed
        const emotion = this._detectEmotionFromResponse(response);
        if (emotion !== 'neutral') {
            this.setEmotion(emotion);
        }
    }
    
    /**
     * Convert audio data for agent processing
     */
    async _convertAudioForAgent(audioData) {
        if (this.bypassASR) {
            // Convert Float32Array to format expected by agent multimodal processor
            const { convertFloat32ToWav } = await import('@/media/utils/audioUtils.js');
            const wavBlob = await convertFloat32ToWav(audioData, {
                sampleRate: 16000,
                numChannels: 1,
                bitDepth: 16
            });
            
            return {
                audio: await wavBlob.arrayBuffer(),
                text: '' // Agent will handle transcription
            };
        } else {
            // Use existing transcription flow, then send text to agent
            const transcription = await this._transcribeAudioWithSTT(audioData);
            return transcription;
        }
    }
}
```

This comprehensive mapping guide provides the exact API transformations needed to replace the current dual LLM service pattern with the unified agent system while maintaining all existing functionality.
