{"name": "hologram-software", "version": "1.0.0", "type": "module", "main": "index.html", "description": "Looking Glass Holographic Applications", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "test": "node test/run-tests.js quick", "test:unit": "node test/run-tests.js unit", "test:streaming": "node test/run-tests.js streaming", "test:integration": "node test/run-tests.js integration", "test:real-api": "node test/run-tests.js realApi", "test:voice-cloning": "node test/run-tests.js voiceCloning", "test:animation": "node test/run-tests.js animation", "test:all": "node test/run-tests.js all", "test:watch": "node test/run-tests.js watch", "test:coverage": "node test/run-tests.js coverage", "test:proxy": "node scripts/test-proxy-setup.js", "test:verify-aliyun-fix": "node --no-warnings test/src/agent/models/aliyun/utils/verify-fix.js", "test:aliyun": "vitest run test/src/agent/models/aliyun/aliyun-all-tests.js", "test:aliyun-websocket": "vitest run test/src/agent/models/aliyun/websocket/index.test.js", "launch": "node scripts/launch.js", "launch:mobile": "node scripts/launch-with-mobile.js", "server": "tsx src/server/server.ts", "server:dev": "tsx watch src/server/server.ts", "ws-server": "node server.js", "ws-server:dev": "nodemon server.js"}, "dependencies": {"@babylonjs/havok": "^1.3.10", "@dimforge/rapier3d-compat": "^0.15.0", "@google/genai": "^1.10.0", "@gradio/client": "^1.14.1", "@langchain/community": "^0.3.46", "@langchain/core": "^0.3.59", "@langchain/langgraph": "^0.3.3", "@langchain/openai": "^0.5.13", "@mediapipe/camera_utils": "^0.3.1675466862", "@mediapipe/drawing_utils": "^0.3.1675466124", "@mediapipe/tasks-vision": "^0.10.22-rc.20250304", "@ricky0123/vad-web": "^0.0.24", "@types/express": "^5.0.1", "@types/multer": "^1.4.12", "@types/node": "^22.14.0", "audio-buffer-utils": "^5.1.2", "audiobuffer-to-wav": "^1.0.0", "axios": "^1.9.0", "babylonjs": "^8.1.1", "bezier-easing": "^2.1.0", "cbor-js": "^0.1.0", "cors": "^2.8.5", "date-fns": "^4.1.0", "draco3d": "^1.5.7", "express": "^5.1.0", "kalman-filter": "^2.3.0", "langchain": "^0.3.27", "multer": "^1.4.5-lts.2", "node-fetch": "^3.3.2", "nodemon": "^3.1.9", "pinyin": "^4.0.0-alpha.2", "segmentit": "^2.0.3", "sherpa-onnx": "^1.11.3", "socket.io": "^4.8.1", "three": "0.175.0", "ws": "^8.18.1", "zod": "^3.25.47"}, "devDependencies": {"@babel/core": "^7.27.3", "@babel/preset-env": "^7.27.2", "@babel/preset-typescript": "^7.27.1", "@babylonjs/core": "^8.1.1", "@babylonjs/inspector": "^8.1.1", "@babylonjs/loaders": "^8.1.1", "@babylonjs/materials": "^8.1.1", "@rollup/plugin-replace": "^6.0.2", "@rollup/plugin-typescript": "^12.1.2", "@types/cors": "^2.8.17", "@types/ws": "^8.18.1", "@vitejs/plugin-basic-ssl": "^2.0.0", "babel-jest": "^30.0.0-beta.3", "cardboard-vr-display": "^1.0.19", "dotenv": "^16.6.1", "gl-matrix": "^3.4.3", "jest": "^29.7.0", "jest-environment-jsdom": "^30.0.0-beta.3", "rollup": "^4.39.0", "rollup-plugin-license": "^3.6.0", "rollup-plugin-typescript-paths": "^1.5.0", "supertest": "^7.1.1", "three": "^0.175.0", "tslib": "^2.8.1", "tsx": "^4.19.3", "typescript": "5.8.3", "vite": "^6.2.5", "vite-plugin-node-polyfills": "^0.23.0", "vite-plugin-top-level-await": "^1.5.0", "vite-plugin-wasm": "^3.4.1", "vitest": "^3.2.3", "webgl-strict-types": "^1.0.5"}, "directories": {"doc": "doc"}, "repository": {"type": "git", "url": "git+https://github.com/xxlbigbrother/Hologram-Software.git"}, "keywords": [], "author": "", "license": "ISC", "bugs": {"url": "https://github.com/xxlbigbrother/Hologram-Software/issues"}, "homepage": "https://github.com/xxlbigbrother/Hologram-Software#readme", "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}