#!/bin/bash

# Create directories
mkdir -p public/mediapipe/pose

# List of required files
FILES=(
    "pose_landmark_full.tflite"
    "pose_web.binarypb"
    "pose_solution_packed_assets.data"
    "pose_solution_packed_assets_loader.js"
    "pose_solution_simd_wasm_bin.js"
    "pose_solution_simd_wasm_bin.wasm"
)

# Download each file
cd public/mediapipe/pose
for file in "${FILES[@]}"; do
    echo "Downloading $file..."
    curl -O "https://cdn.jsdelivr.net/npm/@mediapipe/pose/${file}"
done