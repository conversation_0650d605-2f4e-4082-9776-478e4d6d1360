#!/usr/bin/env node
/**
 * Test script to verify the Aliyun WebSocket proxy setup
 */

import { spawn } from 'child_process';
import { fileURLToPath } from 'url';
import path from 'path';
import fs from 'fs';

const __dirname = path.dirname(fileURLToPath(import.meta.url));
const projectRoot = path.resolve(__dirname, '..');

console.log('🧪 Testing Aliyun WebSocket Proxy Setup...\n');

// Check 1: Environment variables
console.log('1. Checking environment variables...');
const apiKey = process.env.VITE_DASHSCOPE_API_KEY || process.env.VITE_ALIYUN_API_KEY;
if (apiKey) {
    console.log('   ✅ API key found: ' + apiKey.substring(0, 8) + '...');
} else {
    console.log('   ❌ No API key found. Set VITE_DASHSCOPE_API_KEY or VITE_ALIYUN_API_KEY');
    console.log('   Example: export VITE_DASHSCOPE_API_KEY="sk-your-key-here"');
}

// Check 2: Required files
console.log('\n2. Checking required files...');
const requiredFiles = [
    'src/server/server.ts',
    'src/server/middleware/proxy.ts',
    'examples/browser-realtime-client.html',
    'doc/browser-realtime-websocket-proxy.md'
];

requiredFiles.forEach(file => {
    const filePath = path.join(projectRoot, file);
    const exists = fs.existsSync(filePath);
    console.log(`   ${exists ? '✅' : '❌'} ${file}`);
});

// Check 3: Dependencies
console.log('\n3. Checking dependencies...');
try {
    const packageJson = JSON.parse(fs.readFileSync(path.join(projectRoot, 'package.json'), 'utf8'));
    const deps = { ...packageJson.dependencies, ...packageJson.devDependencies };

    const requiredDeps = ['express', 'ws', 'tsx', 'cors'];
    requiredDeps.forEach(dep => {
        if (deps[dep]) {
            console.log(`   ✅ ${dep}: ${deps[dep]}`);
        } else {
            console.log(`   ❌ ${dep}: missing`);
        }
    });
} catch (error) {
    console.log('   ❌ Error reading package.json');
}

// Check 4: Available scripts
console.log('\n4. Available scripts:');
console.log('   npm run server:dev  - Start development server');
console.log('   npm run server      - Start production server');
console.log('   npm run launch      - Launch main application');

// Instructions
console.log('\n🚀 Next steps:');
console.log('1. Set API key: export VITE_DASHSCOPE_API_KEY="sk-your-key-here"');
console.log('2. Start server: npm run server:dev');
console.log('3. Open browser: examples/browser-realtime-client.html');
console.log('4. Test connection and audio streaming');

console.log('\n📋 Quick test URLs:');
console.log('- Server status: http://localhost:3000/proxy-status');
console.log('- WebSocket: ws://localhost:3000/ws?model=qwen-omni-turbo-realtime');
console.log('- Test client: file://' + path.join(projectRoot, 'examples/browser-realtime-client.html'));

console.log('\n✨ Setup complete! The proxy is ready to use.');
