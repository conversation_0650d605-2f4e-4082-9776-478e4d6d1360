/**
 * Enhanced Launch Script with Mobile Control Support
 * 
 * This script launches the specified app with the WebSocket server for mobile control.
 * Usage: npm run launch:mobile [app_name] [mode]
 * Example: npm run launch:mobile viewer dev
 */

import { spawn } from 'child_process';
import { fileURLToPath } from 'url';
import path from 'path';
import { APPS } from '../app/index.js';
import { config } from 'dotenv';

const __dirname = path.dirname(fileURLToPath(import.meta.url));
const appName = process.argv[2] || 'viewer';
const mode = process.argv[3] || 'dev';

// Load environment variables from .env file
config({ path: path.join(__dirname, '../.env') });

// Merge with process.env
const env = {
    ...process.env,
    APP_NAME: appName,
    APP_PATH: APPS[appName]?.path,
    APP_ENTRY: APPS[appName]?.entry,
    NODE_ENV: mode === 'dev' ? 'development' : 'production'
};

const app = Object.values(APPS).find(a => a.name === appName);
if (!app) {
    console.error(`App "${appName}" not found. Available apps:`,
        Object.values(APPS).map(a => a.name).join(', '));
    process.exit(1);
}

console.log(`Launching ${app.name} in ${mode} mode with mobile control support...`);

// Function to spawn a process with proper output handling
function spawnProcess(command, args, options, name) {
    console.log(`Starting ${name}...`);

    const proc = spawn(command, args, {
        ...options,
        stdio: 'pipe'
    });

    // Handle stdout
    proc.stdout.on('data', (data) => {
        const lines = data.toString().trim().split('\n');
        lines.forEach(line => {
            console.log(`[${name}] ${line}`);
        });
    });

    // Handle stderr
    proc.stderr.on('data', (data) => {
        const lines = data.toString().trim().split('\n');
        lines.forEach(line => {
            console.error(`[${name}] ${line}`);
        });
    });

    // Handle process exit
    proc.on('close', (code) => {
        if (code !== 0) {
            console.error(`[${name}] Process exited with code ${code}`);
        } else {
            console.log(`[${name}] Process exited normally`);
        }
    });

    proc.on('error', (error) => {
        console.error(`[${name}] Error:`, error);
    });

    return proc;
}

// Server infrastructure removed - only WebSocket server needed for mobile control
// Start the WebSocket server for mobile control
const wsServer = spawnProcess('node', [
    path.join(__dirname, '../server.js')
], { env }, 'WebSocket Server');

// Start Vite dev server with agent-based architecture
const viteServer = spawnProcess('npm', ['run', 'dev'], {
    env: {
        ...env,
        AGENT_MODE_ENABLED: 'true', // Flag to indicate agent-based architecture
        WEBSOCKET_SERVER_STARTED: 'true' // Flag to indicate WebSocket server is running
    }
}, 'Vite Server');

// Handle process termination
process.on('SIGINT', () => {
    console.log('\nShutting down servers...');
    wsServer.kill();
    viteServer.kill();
    process.exit(0);
});

process.on('SIGTERM', () => {
    console.log('\nReceived SIGTERM, shutting down servers...');
    wsServer.kill();
    viteServer.kill();
    process.exit(0);
});

console.log('All servers are starting. Press Ctrl+C to stop all servers.');
