#!/usr/bin/env node
/**
 * Proxy Setup Validation Script
 * Validates that the <PERSON>yun proxy configuration is correct
 */

// Load environment variables
import { config } from 'dotenv';
import path from 'path';
import { fileURLToPath } from 'url';

const __dirname = path.dirname(fileURLToPath(import.meta.url));
config({ path: path.resolve(__dirname, '../.env') });

console.log('\n=== Aliyun Proxy Setup Validation ===\n');

// Check environment variables
const checks = [
    {
        name: 'VITE_DASHSCOPE_API_KEY',
        value: process.env.VITE_DASHSCOPE_API_KEY,
        required: true,
        description: 'Client-side API key for Aliyun Bailian'
    },
    {
        name: 'VITE_DASHSCOPE_API_KEY',
        value: process.env.VITE_DASHSCOPE_API_KEY,
        required: true,
        description: 'Server-side API key for WebSocket proxy'
    },
    {
        name: 'VITE_ALIYUN_MODEL',
        value: process.env.VITE_ALIYUN_MODEL,
        required: false,
        description: '<PERSON><PERSON> model to use (defaults to qwen-omni-turbo-realtime-latest)'
    },
    {
        name: 'VITE_DOWNLOAD_SERVER_PORT',
        value: process.env.VITE_DOWNLOAD_SERVER_PORT,
        required: false,
        description: 'Download server port (defaults to 2994)'
    }
];

let allValid = true;

for (const check of checks) {
    const status = check.value ? '✅' : check.required ? '❌' : '⚠️';
    const valueDisplay = check.value
        ? (check.name.includes('KEY') ? '[REDACTED]' : check.value)
        : 'Not set';

    console.log(`${status} ${check.name}: ${valueDisplay}`);
    console.log(`   ${check.description}`);

    if (check.required && !check.value) {
        allValid = false;
        console.log(`   ⚠️  This is required for the proxy to work`);
    }
    console.log('');
}

// Additional checks
console.log('=== Configuration Validation ===\n');

if (process.env.VITE_DASHSCOPE_API_KEY && process.env.VITE_DASHSCOPE_API_KEY) {
    if (process.env.VITE_DASHSCOPE_API_KEY === process.env.VITE_DASHSCOPE_API_KEY) {
        console.log('✅ API keys match (recommended)');
    } else {
        console.log('⚠️  API keys differ - this may be intentional but usually they should be the same');
    }
} else {
    console.log('❌ Cannot validate API key consistency - missing keys');
    allValid = false;
}

console.log('');

// Summary
console.log('=== Summary ===\n');
if (allValid) {
    console.log('✅ Proxy setup appears to be correctly configured!');
    console.log('');
    console.log('Next steps:');
    console.log('1. Start the server: npm run dev');
    console.log('2. The WebSocket proxy will be available at: ws://localhost:2994/ws');
    console.log('3. Check proxy status at: http://localhost:2994/proxy-status');
} else {
    console.log('❌ Proxy setup has issues that need to be resolved.');
    console.log('');
    console.log('Required fixes:');
    console.log('1. Set missing environment variables in .env file');
    console.log('2. Get API key from: https://bailian.console.aliyun.com/');
    console.log('3. Ensure both VITE_DASHSCOPE_API_KEY and VITE_DASHSCOPE_API_KEY are set');
}

console.log('\n=====================================\n');

process.exit(allValid ? 0 : 1);
