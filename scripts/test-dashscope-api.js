/**
 * Test script to verify Aliyun DashScope API key validity and credits
 * 
 * Run with: node scripts/test-dashscope-api.js
 */

// Read API key from .env file or environment
import * as dotenv from 'dotenv';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import fs from 'fs';

// Get directory name for current module
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Load .env file
dotenv.config();

// Try to read API key from .env file if not in environment
let apiKey = process.env.VITE_DASHSCOPE_API_KEY;

if (!apiKey) {
    try {
        const envPath = join(dirname(__dirname), '.env');
        const envContent = fs.readFileSync(envPath, 'utf8');
        const match = envContent.match(/VITE_DASHSCOPE_API_KEY=([^\s#]+)/);
        if (match && match[1]) {
            apiKey = match[1];
        }
    } catch (err) {
        console.error('Error reading .env file:', err.message);
    }
}

if (!apiKey) {
    console.error('❌ No VITE_DASHSCOPE_API_KEY found in environment or .env file');
    process.exit(1);
}

console.log(`Testing API key: ${apiKey.substring(0, 5)}...${apiKey.substring(apiKey.length - 4)}`);

// Simple function to test the API key using fetch
async function testApiKey() {
    const baseUrl = 'https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation';

    try {
        const response = await fetch(baseUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${apiKey}`,
            },
            body: JSON.stringify({
                model: 'qwen-turbo',
                input: {
                    messages: [
                        {
                            role: 'user',
                            content: 'Hello, this is a test message to verify API key validity'
                        }
                    ]
                }
            })
        });

        const data = await response.json();

        if (response.ok) {
            console.log('✅ API key is valid and has sufficient credits');
            console.log('Response:', JSON.stringify(data, null, 2));
            return true;
        } else {
            console.error('❌ API key validation failed');
            console.error(`Status: ${response.status} ${response.statusText}`);
            console.error('Error:', JSON.stringify(data, null, 2));

            if (response.status === 402) {
                console.error('\n❌ INSUFFICIENT CREDITS: Your Aliyun DashScope account needs to be recharged');
                console.error('Visit https://dashscope.console.aliyun.com/ to check your account balance and recharge');
            }

            return false;
        }
    } catch (error) {
        console.error('❌ Error testing API key:', error.message);
        return false;
    }
}

// Test the alternate API key if the first one fails
async function testAlternateApiKey() {
    let commentedApiKey;

    try {
        const envPath = join(dirname(__dirname), '.env');
        const envContent = fs.readFileSync(envPath, 'utf8');
        const match = envContent.match(/# *VITE_DASHSCOPE_API_KEY=([^\s#]+)/);
        if (match && match[1]) {
            commentedApiKey = match[1];
        } else {
            commentedApiKey = 'sk-b9e0e1f2b6a7469492e50889e25baec2'; // Hardcoded from .env comment
        }
    } catch (err) {
        commentedApiKey = 'sk-b9e0e1f2b6a7469492e50889e25baec2'; // Fallback to hardcoded key
        console.error('Error reading .env file for alternate key:', err.message);
    }

    console.log(`\nTesting alternate API key: ${commentedApiKey.substring(0, 5)}...${commentedApiKey.substring(commentedApiKey.length - 4)}`);

    const baseUrl = 'https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation';

    try {
        const response = await fetch(baseUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${commentedApiKey}`,
            },
            body: JSON.stringify({
                model: 'qwen-turbo',
                input: {
                    messages: [
                        {
                            role: 'user',
                            content: 'Hello, this is a test message to verify the alternate API key validity'
                        }
                    ]
                }
            })
        });

        const data = await response.json();

        if (response.ok) {
            console.log('✅ Alternate API key is valid and has sufficient credits');
            console.log('Consider updating your .env file to:');
            console.log(`VITE_DASHSCOPE_API_KEY=${commentedApiKey}`);
            console.log(`# VITE_DASHSCOPE_API_KEY=${apiKey}`);
            return true;
        } else {
            console.error('❌ Alternate API key validation failed');
            console.error(`Status: ${response.status} ${response.statusText}`);
            console.error('Error:', JSON.stringify(data, null, 2));
            return false;
        }
    } catch (error) {
        console.error('❌ Error testing alternate API key:', error.message);
        return false;
    }
}

// Main execution
async function main() {
    console.log('🔍 Testing Aliyun DashScope API key validity...\n');

    const isMainKeyValid = await testApiKey();

    if (!isMainKeyValid) {
        console.log('\n🔍 Testing alternate API key from .env file...');
        await testAlternateApiKey();
    }

    console.log('\n📝 Summary:');
    console.log('If both API keys failed with 402 Insufficient Credits error, you need to:');
    console.log('1. Visit https://dashscope.console.aliyun.com/');
    console.log('2. Check your account balance');
    console.log('3. Recharge your account or create a new API key');
}

main().catch(error => {
    console.error('Unhandled error:', error);
}); 