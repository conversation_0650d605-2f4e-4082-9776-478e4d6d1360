#!/bin/bash

# Script to clean up redundant debug test files after consolidation
# These files have been merged into the structured test suite

echo "🧹 Cleaning up redundant debug test files..."

DEBUG_DIR="/Users/<USER>/code/space_time_inpaint/dev/Hologram-Software/test/debug"

# Files that have been merged into the main test suite
FILES_TO_REMOVE=(
    "test-qwen-omni-1011-fix.js"
    "aliyun-api-compliance-test.js"
    "websocket-1011-advanced-debug.js"
    "test-qwen-omni-session-config.js"
    "test-qwen-omni-session-config-simple.js"
    "test-pcm16-format-fix.js"
    "audio-format-diagnosis.js"
    "test-valid-pcm16-audio.js"
    "test-fixed-audio-validation.js"
    "test-voice-logic.js"
    "test-gender-voice.js"
    "websocket-connection-test.js"
    "websocket-session-test.js"
    "test-blob-handling.js"
    "env-helper.js"
    "test-init.js"
)

# Create a backup directory
BACKUP_DIR="${DEBUG_DIR}/backup_$(date +%Y%m%d_%H%M%S)"
mkdir -p "$BACKUP_DIR"

echo "📦 Creating backup in $BACKUP_DIR"

# Move files to backup and track what was moved
MOVED_FILES=()
for file in "${FILES_TO_REMOVE[@]}"; do
    FILE_PATH="${DEBUG_DIR}/${file}"
    if [ -f "$FILE_PATH" ]; then
        mv "$FILE_PATH" "$BACKUP_DIR/"
        MOVED_FILES+=("$file")
        echo "  ✅ Moved $file to backup"
    else
        echo "  ⚠️  File not found: $file"
    fi
done

# Create a summary file
SUMMARY_FILE="${BACKUP_DIR}/MOVED_FILES_SUMMARY.md"
cat > "$SUMMARY_FILE" << EOF
# Moved Debug Test Files Summary

These files were moved to backup after being consolidated into the structured test suite.

## Moved Files (${#MOVED_FILES[@]} total):

EOF

for file in "${MOVED_FILES[@]}"; do
    echo "- \`$file\`" >> "$SUMMARY_FILE"
done

cat >> "$SUMMARY_FILE" << EOF

## New Test Structure:

- **AliyunBailianChatModel.test.js** - Core unit tests
- **AliyunBailianChatModel.integration.test.js** - Integration tests (includes 1011 error fixes)
- **AliyunBailianChatModel.audio.test.js** - Audio processing tests
- **AliyunBailianChatModel.voice.test.js** - Voice selection tests
- **AliyunBailianChatModel.websocket.test.js** - WebSocket management tests
- **AliyunBailianChatModel.utils.test.js** - Utility functions and helpers

## Migration Mapping:

- \`test-qwen-omni-1011-fix.js\` → \`AliyunBailianChatModel.integration.test.js\`
- \`aliyun-api-compliance-test.js\` → \`AliyunBailianChatModel.integration.test.js\`
- \`websocket-1011-advanced-debug.js\` → \`AliyunBailianChatModel.integration.test.js\`
- \`test-qwen-omni-session-config*.js\` → \`AliyunBailianChatModel.voice.test.js\`
- \`test-pcm16-format-fix.js\` → \`AliyunBailianChatModel.audio.test.js\`
- \`audio-format-diagnosis.js\` → \`AliyunBailianChatModel.audio.test.js\`
- \`test-valid-pcm16-audio.js\` → \`AliyunBailianChatModel.audio.test.js\`
- \`test-fixed-audio-validation.js\` → \`AliyunBailianChatModel.audio.test.js\`
- \`test-voice-logic.js\` → \`AliyunBailianChatModel.voice.test.js\`
- \`test-gender-voice.js\` → \`AliyunBailianChatModel.voice.test.js\`
- \`websocket-connection-test.js\` → \`AliyunBailianChatModel.websocket.test.js\`
- \`websocket-session-test.js\` → \`AliyunBailianChatModel.websocket.test.js\`
- \`test-blob-handling.js\` → \`AliyunBailianChatModel.websocket.test.js\`
- \`env-helper.js\` → \`AliyunBailianChatModel.utils.test.js\`
- \`test-init.js\` → \`AliyunBailianChatModel.utils.test.js\`

## Benefits:

1. **Better Organization** - Tests grouped by functionality
2. **Reduced Redundancy** - Duplicate code eliminated
3. **Easier Maintenance** - Clear separation of concerns
4. **Better Coverage** - Comprehensive test coverage
5. **Reusable Utilities** - Common helpers centralized

## Restore Instructions:

To restore any of these files, simply copy them back from this backup directory to the debug folder.

Backup created on: $(date)
EOF

echo ""
echo "📋 Summary:"
echo "  • Moved ${#MOVED_FILES[@]} files to backup"
echo "  • Backup location: $BACKUP_DIR"
echo "  • Summary file: $SUMMARY_FILE"
echo ""
echo "✅ Cleanup completed successfully!"
echo ""
echo "📚 The test suite is now organized as follows:"
echo "  • test/src/agent/models/AliyunBailianChatModel.test.js (core unit tests)"
echo "  • test/src/agent/models/AliyunBailianChatModel.integration.test.js (integration tests)"
echo "  • test/src/agent/models/AliyunBailianChatModel.audio.test.js (audio tests)"
echo "  • test/src/agent/models/AliyunBailianChatModel.voice.test.js (voice tests)"
echo "  • test/src/agent/models/AliyunBailianChatModel.websocket.test.js (websocket tests)"
echo "  • test/src/agent/models/AliyunBailianChatModel.utils.test.js (utilities)"
echo "  • test/src/agent/models/README.md (documentation)"
