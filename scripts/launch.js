import { spawn } from 'child_process';
import { fileURLToPath } from 'url';
import path from 'path';
import { APPS } from '../app/index.js';
import { config } from 'dotenv';

const __dirname = path.dirname(fileURLToPath(import.meta.url));
const appName = process.argv[2] || 'viewer';
const mode = process.argv[3] || 'dev';

// Load environment variables from .env file
config({ path: path.join(__dirname, '../.env') });

// Merge with process.env
const env = {
    ...process.env,
    APP_NAME: appName,
    APP_PATH: APPS[appName]?.path,
    APP_ENTRY: APPS[appName]?.entry,
    NODE_ENV: mode === 'dev' ? 'development' : 'production'
};

const app = Object.values(APPS).find(a => a.name === appName);
if (!app) {
    console.error(`App "${appName}" not found. Available apps:`,
        Object.values(APPS).map(a => a.name).join(', '));
    process.exit(1);
}

console.log(`Launching ${app.name} in ${mode} mode...`);

// Function to wait for download server to be ready
function waitForDownloadServer(port, maxAttempts = 10, delay = 1000) {
    return new Promise((resolve, reject) => {
        let attempts = 0;

        function checkServer() {
            attempts++;
            fetch(`http://localhost:${port}/proxy-status`)
                .then(response => {
                    if (response.ok) {
                        console.log(`Download server is ready on port ${port}`);
                        resolve(port);
                    } else {
                        throw new Error(`Server responded with status ${response.status}`);
                    }
                })
                .catch(error => {
                    if (attempts >= maxAttempts) {
                        reject(new Error(`Download server not ready after ${maxAttempts} attempts: ${error.message}`));
                    } else {
                        console.log(`Waiting for download server... (attempt ${attempts}/${maxAttempts})`);
                        setTimeout(checkServer, delay);
                    }
                });
        }

        checkServer();
    });
}

// Start the download server first
console.log('Starting download server...');
const downloadServer = spawn('npx', [
    'tsx',
    'watch',
    path.join(__dirname, '../src/server/server.ts')
], {
    stdio: 'inherit',
    env: {
        ...env,
        LAUNCH_CONTEXT: JSON.stringify({
            isLaunchScript: true,
            appName,
            nodeEnv: env.NODE_ENV
        })
    }
});

downloadServer.on('error', (error) => {
    console.error('Error starting download server:', error);
    process.exit(1);
});

// Give the download server a moment to start, then start Vite
setTimeout(async () => {
    console.log('Starting Vite dev server...');
    const viteServer = spawn('npm', ['run', 'dev'], {
        stdio: 'inherit',
        env: {
            ...env,
            DOWNLOAD_SERVER_STARTED: 'true' // Flag to indicate download server is running
        }
    });

    viteServer.on('error', (error) => {
        console.error('Error starting Vite server:', error);
        downloadServer.kill(); // Kill download server if Vite fails
        process.exit(1);
    });

    // Store vite server reference for cleanup
    env.viteServer = viteServer;
}, 2000); // Wait 2 seconds for download server to initialize

// Handle process termination
process.on('SIGINT', () => {
    console.log('\nShutting down servers...');
    downloadServer.kill();
    if (env.viteServer) {
        env.viteServer.kill();
    }
});

process.on('SIGTERM', () => {
    console.log('\nReceived SIGTERM, shutting down servers...');
    downloadServer.kill();
    if (env.viteServer) {
        env.viteServer.kill();
    }
}); 