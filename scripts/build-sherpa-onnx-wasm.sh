#!/bin/bash
# Script to build sherpa-onnx WebAssembly files from ONNX models
# This script automates the process of setting up Emscripten SDK,
# cloning sherpa-onnx, and building WASM files for ASR models.
#
# Usage: ./build-sherpa-onnx-wasm.sh [OPTIONS]
#
# Options:
#   -m, --model MODEL_TYPE   Model type to build (dolphin, senseVoice, or custom model name)
#   -u, --url MODEL_URL      URL to download the model from (required for custom models)
#   -p, --path MODEL_PATH    Path where model files will be stored (required for custom models)
#   -f, --force              Force rebuild even if files exist
#   -h, --help               Show this help message

# Enable error handling
set -e  # Exit on error
set -o pipefail  # Exit if any command in a pipe fails

# Log file
LOG_FILE="sherpa-onnx-build.log"

# Logging function
log() {
  echo "$@"
  echo "$(date '+%Y-%m-%d %H:%M:%S') - $@" >> "$LOG_FILE"
}

# Error handling function
handle_error() {
  local line=$1
  local command=$2
  local code=$3
  log "Error on line $line: Command '$command' exited with status $code"
  exit $code
}

# Set up error trap
trap 'handle_error ${LINENO} "$BASH_COMMAND" $?' ERR

# Check for required dependencies
check_dependencies() {
  log "=== Checking for required dependencies ==="

  # Check for CMake
  if ! command -v cmake &> /dev/null; then
    log "Error: CMake is not installed or not in PATH"
    log "Please install CMake before continuing."
    log ""
    log "On macOS, you can install it with:"
    log "  brew install cmake"
    log ""
    log "On Ubuntu/Debian, you can install it with:"
    log "  sudo apt-get install cmake"
    log ""
    log "On Windows, you can download it from:"
    log "  https://cmake.org/download/"
    log ""
    exit 1
  fi

  log "CMake found: $(cmake --version | head -n 1)"

  # Check for wget or curl
  if ! command -v wget &> /dev/null && ! command -v curl &> /dev/null; then
    log "Error: Neither wget nor curl is installed"
    log "Please install wget or curl before continuing."
    log ""
    log "On macOS, you can install wget with:"
    log "  brew install wget"
    log ""
    log "On Ubuntu/Debian, you can install it with:"
    log "  sudo apt-get install wget"
    log ""
    exit 1
  fi

  if command -v wget &> /dev/null; then
    log "wget found: $(wget --version | head -n 1)"
  else
    log "curl found: $(curl --version | head -n 1)"
  fi

  log "All required dependencies are installed."
}

# Save the original directory
ORIGINAL_DIR=$(pwd)

# Configuration
EMSDK_VERSION="3.1.48"  # Recommended version for sherpa-onnx
SHERPA_ONNX_REPO="https://github.com/k2-fsa/sherpa-onnx.git"
# OUTPUT_DIR="public/models/tts"
# MODEL_DIR="public/models"

# Parse command line arguments
MODEL_TYPE="dolphin"  # Default model
MODEL_URL=""          # Custom model URL
MODEL_PATH=""         # Custom model path
FORCE_REBUILD=false
CUSTOM_MODEL=false

print_usage() {
  echo "Usage: $0 [OPTIONS]"
  echo "Build sherpa-onnx WebAssembly files from ONNX models"
  echo ""
  echo "Options:"
  echo "  -m, --model MODEL_TYPE   Model type to build (dolphin, senseVoice, or custom model name)"
  echo "  -u, --url MODEL_URL      URL to download the model from (required for custom models)"
  echo "  -p, --path MODEL_PATH    Path where model files will be stored (required for custom models)"
  echo "  -f, --force              Force rebuild even if files exist"
  echo "  -h, --help               Show this help message"
}

while [[ $# -gt 0 ]]; do
  case $1 in
    -m|--model)
      MODEL_TYPE="$2"
      shift 2
      ;;
    -u|--url)
      MODEL_URL="$2"
      CUSTOM_MODEL=true
      shift 2
      ;;
    -p|--path)
      MODEL_PATH="$2"
      CUSTOM_MODEL=true
      shift 2
      ;;
    -f|--force)
      FORCE_REBUILD=true
      shift
      ;;
    -h|--help)
      print_usage
      exit 0
      ;;
    *)
      echo "Unknown option: $1"
      print_usage
      exit 1
      ;;
  esac
done

# Set model-specific paths for known models
if [ "$CUSTOM_MODEL" = false ]; then
  case $MODEL_TYPE in
    "dolphin")
      MODEL_PATH="tts/dolphin-small-ctc-multi-lang"
      MODEL_URL="https://github.com/k2-fsa/sherpa-onnx/releases/download/asr-models/sherpa-onnx-dolphin-small-ctc-multi-lang-int8-2025-04-02.tar.bz2"
      ;;
    "senseVoice")
      MODEL_PATH="tts/sherpa-onnx-sense-voice-zh-en-ja-ko-yue-2024-07-17"
      MODEL_URL="https://github.com/k2-fsa/sherpa-onnx/releases/download/asr-models/sherpa-onnx-sense-voice-zh-en-ja-ko-yue-2024-07-17.tar.bz2"
      ;;
    *)
      # For unknown models, require URL and path
      if [ -z "$MODEL_URL" ] || [ -z "$MODEL_PATH" ]; then
        echo "Error: For custom model '$MODEL_TYPE', you must specify both --url and --path"
        print_usage
        exit 1
      fi
      ;;
  esac
fi

# Validate custom model parameters
if [ "$CUSTOM_MODEL" = true ]; then
  if [ -z "$MODEL_URL" ]; then
    echo "Error: --url is required for custom models"
    print_usage
    exit 1
  fi

  if [ -z "$MODEL_PATH" ]; then
    echo "Error: --path is required for custom models"
    print_usage
    exit 1
  fi

  # If MODEL_PATH doesn't start with "tts/", add it
  if [[ ! "$MODEL_PATH" =~ ^tts/ ]]; then
    MODEL_PATH="tts/$MODEL_PATH"
    echo "Adjusted model path to: $MODEL_PATH"
  fi
fi

# Check for required dependencies
check_dependencies

# Create output directory
# log "Creating output directory..."
# mkdir -p "$MODEL_DIR/$MODEL_PATH"

# Verify directory was created
# if [ ! -d "$MODEL_DIR/$MODEL_PATH" ]; then
#   log "Error: Failed to create model directory $MODEL_DIR/$MODEL_PATH"
#   exit 1
# fi

# log "Output directory created successfully"

# Check if WASM files already exist
if [ "$FORCE_REBUILD" = false ] && [ -f "$MODEL_DIR/$MODEL_PATH/sherpa-onnx-asr.js" ] && [ -f "$MODEL_DIR/$MODEL_PATH/sherpa-onnx-asr.wasm" ]; then
  log "WASM files for $MODEL_TYPE already exist. Use --force to rebuild."
  exit 0
fi

# Create a temporary directory for building in the current directory
BUILD_DIR="./temp/sherpa-onnx-build/$MODEL_TYPE"
mkdir -p "$BUILD_DIR"
cd "$BUILD_DIR"

# Save model information for later use by copy-wasm-files.js
MODEL_INFO_FILE="model-info.json"
cat > "$MODEL_INFO_FILE" << EOF
{
  "modelType": "$MODEL_TYPE",
  "modelPath": "$MODEL_PATH",
  "modelUrl": "$MODEL_URL",
  "buildDir": "$BUILD_DIR"
}
EOF

log "Saved model information to $BUILD_DIR/$MODEL_INFO_FILE"

# Initialize the log fileMODEL_DIR
echo "=== Sherpa-ONNX WASM Build Log ===" > "$LOG_FILE"
echo "Date: $(date)" >> "$LOG_FILE"
echo "Model Type: $MODEL_TYPE" >> "$LOG_FILE"
echo "Force Rebuild: $FORCE_REBUILD" >> "$LOG_FILE"
# echo "Output Directory: $OUTPUT_DIR/$MODEL_TYPE" >> "$LOG_FILE"
# echo "Model Directory: $MODEL_DIR/$MODEL_PATH" >> "$LOG_FILE"
echo "Build Directory: $BUILD_DIR" >> "$LOG_FILE"
echo "===================================" >> "$LOG_FILE"

log "=== Using build directory: $BUILD_DIR ==="

log "=== Setting up Emscripten SDK ==="

# Check if emsdk directory already exists
if [ -d "emsdk" ]; then
  log "emsdk directory already exists, skipping clone"
  cd emsdk
  log "Updating emsdk..."
  git pull
else
  log "Cloning emsdk repository..."
  git clone https://github.com/emscripten-core/emsdk.git
  cd emsdk
fi

# Install and activate the specified Emscripten version
log "Installing Emscripten version $EMSDK_VERSION..."
./emsdk install "$EMSDK_VERSION"
log "Activating Emscripten version $EMSDK_VERSION..."
./emsdk activate "$EMSDK_VERSION"
log "Setting up Emscripten environment..."
source ./emsdk_env.sh

cd ..

log "=== Setting up sherpa-onnx repository ==="

# Check if sherpa-onnx directory already exists
if [ -d "sherpa-onnx" ]; then
  log "sherpa-onnx directory already exists, skipping clone"
  cd sherpa-onnx
  log "Updating sherpa-onnx..."
  git pull
else
  log "Cloning sherpa-onnx repository..."
  git clone "$SHERPA_ONNX_REPO"
  cd sherpa-onnx
fi

log "=== Downloading and preparing model files ==="
cd wasm/asr/assets
log "Current directory: $(pwd)"

# Check if model files already exist
if [ -f "model.onnx" ] && [ -f "tokens.txt" ]; then
  log "Model files already exist, skipping download"
else
  # Download and extract the model
  log "Downloading model from $MODEL_URL"
  MODEL_ARCHIVE=$(basename "$MODEL_URL")

  # Download the model archive
  wget -O "$MODEL_ARCHIVE" "$MODEL_URL"

  # Check if download was successful
  if [ ! -f "$MODEL_ARCHIVE" ]; then
    log "Failed to download model archive from $MODEL_URL"
    exit 1
  fi

  # Extract the model archive
  log "Extracting model archive..."
  tar xf "$MODEL_ARCHIVE"

  # Check if extraction was successful
  if [ $? -ne 0 ]; then
    log "Failed to extract model archive $MODEL_ARCHIVE"
    exit 1
  fi

  # Extract model directory name from the archive
  log "Extracting model directory name from $MODEL_ARCHIVE..."
  # Use a more reliable method for Linux systems
  if ! MODEL_DIR_NAME=$(tar -tf "$MODEL_ARCHIVE" 2>/dev/null | grep -v "\/$" | head -1 | xargs dirname 2>/dev/null | sort -u | head -1); then
    # Fallback method if the above fails
    MODEL_DIR_NAME=$(basename "$MODEL_ARCHIVE" .tar.bz2)
    log "Using fallback method: derived directory name from archive filename: $MODEL_DIR_NAME"
  else
    log "Extracted model directory name: $MODEL_DIR_NAME"
  fi

  # Check if the model directory exists
  if [ ! -d "$MODEL_DIR_NAME" ]; then
    log "Model directory $MODEL_DIR_NAME not found after extraction"
    exit 1
  fi

  # List the contents of the extracted directory
  ls -la "$MODEL_DIR_NAME" >> "$LOG_FILE"

  # Move files to the correct locations
  if [ "$MODEL_TYPE" = "dolphin" ]; then
    # For Dolphin model (CTC-based)
    log "Copying Dolphin model files..."
    if [ -f "$MODEL_DIR_NAME/model.int8.onnx" ]; then
      mv "$MODEL_DIR_NAME/model.int8.onnx" model.onnx
    else
      log "Error: model.int8.onnx not found in $MODEL_DIR_NAME"
      exit 1
    fi

    if [ -f "$MODEL_DIR_NAME/tokens.txt" ]; then
      mv "$MODEL_DIR_NAME/tokens.txt" ./
    else
      log "Error: tokens.txt not found in $MODEL_DIR_NAME"
      exit 1
    fi
  elif [ "$MODEL_TYPE" = "senseVoice" ]; then
    # For SenseVoice model
    log "Copying SenseVoice model files..."
    if [ -f "$MODEL_DIR_NAME/model.onnx" ]; then
      cp "$MODEL_DIR_NAME/model.onnx" ./
    else
      log "Error: model.onnx not found in $MODEL_DIR_NAME"
      exit 1
    fi

    if [ -f "$MODEL_DIR_NAME/tokens.txt" ]; then
      cp "$MODEL_DIR_NAME/tokens.txt" ./
    else
      log "Error: tokens.txt not found in $MODEL_DIR_NAME"
      exit 1
    fi
  fi

  # Clean up the downloaded archive and extracted directory
  rm "$MODEL_ARCHIVE"
  # rm -rf "$MODEL_DIR_NAME"
fi



# Create symbolic links for encoder.onnx, decoder.onnx, and joiner.onnx if they don't exist
# log "Creating symbolic links for encoder.onnx, decoder.onnx, and joiner.onnx..."

# if [ ! -f "encoder.onnx" ] && [ -f "model.onnx" ]; then
#   log "Creating symbolic link: encoder.onnx -> model.onnx"
#   ln -sf model.onnx encoder.onnx
# fi

# if [ ! -f "decoder.onnx" ]; then
#   log "Creating empty decoder.onnx file"
#   touch decoder.onnx
# fi

# if [ ! -f "joiner.onnx" ]; then
#   log "Creating empty joiner.onnx file"
#   touch joiner.onnx
# fi

# log "Symbolic links created successfully"

cd ../../..

log "=== Building WASM files ==="

log "Running build-wasm-simd-asr.sh..."
./build-wasm-simd-asr.sh

# Check if build was successful
if [ $? -ne 0 ]; then
  log "Error: build-wasm-simd-asr.sh failed"
  exit 1
fi
fi

# log "=== Copying output files ==="

# # Ensure output directory exists
# mkdir -p "$MODEL_DIR/$MODEL_PATH"

# # Check if build was successful
# if [ ! -d "build-wasm-simd-asr/install/bin/wasm/asr" ]; then
#   log "Error: Build output directory not found"
#   exit 1
# fi

# # Print the current directory for debugging
# log "Current directory: $(pwd)"
# log "Checking for files in build-wasm-simd-asr/install/bin/wasm/asr/"

# # List the contents of the build directory to check available files
# log "Listing files in build output directory:"
# ls -la "build-wasm-simd-asr/install/bin/wasm/asr/" >> "$LOG_FILE"

# # Check for different possible file names
# WASM_JS_FILES=("sherpa-onnx-asr.js" "sherpa-onnx-wasm-main-asr.js")
# WASM_BINARY_FILES=("sherpa-onnx-asr.wasm" "sherpa-onnx-wasm-main-asr.wasm")
# WASM_DATA_FILES=("sherpa-onnx-asr.data" "sherpa-onnx-wasm-main-asr.data")

# # Ensure the model directory exists
# mkdir -p "$MODEL_DIR/$MODEL_PATH"

# # Copy JS file
# JS_FOUND=false
# for JS_FILE in "${WASM_JS_FILES[@]}"; do
#   if [ -f "build-wasm-simd-asr/install/bin/wasm/asr/$JS_FILE" ]; then
#     # Use cp -f to force overwrite if the file already exists
#     cp -f "build-wasm-simd-asr/install/bin/wasm/asr/$JS_FILE" "$MODEL_DIR/$MODEL_PATH/$JS_FILE"
#     # Verify the copy operation
#     if [ -f "$MODEL_DIR/$MODEL_PATH/$JS_FILE" ]; then
#       log "Copied $JS_FILE to $MODEL_DIR/$MODEL_PATH/$JS_FILE"
#       JS_FOUND=true
#       break
#     else
#       log "Error: Failed to copy $JS_FILE to $MODEL_DIR/$MODEL_PATH/$JS_FILE"
#     fi
#   fi
# done

# if [ "$JS_FOUND" = false ]; then
#   log "Error: No JavaScript file found in build output"
#   log "Checked for: ${WASM_JS_FILES[*]}"
#   exit 1
# fi

# # Copy WASM file
# WASM_FOUND=false
# for WASM_FILE in "${WASM_BINARY_FILES[@]}"; do
#   if [ -f "build-wasm-simd-asr/install/bin/wasm/asr/$WASM_FILE" ]; then
#     # Use cp -f to force overwrite if the file already exists
#     cp -f "build-wasm-simd-asr/install/bin/wasm/asr/$WASM_FILE" "$MODEL_DIR/$MODEL_PATH/$WASM_FILE"
#     # Verify the copy operation
#     if [ -f "$MODEL_DIR/$MODEL_PATH/$WASM_FILE" ]; then
#       log "Copied $WASM_FILE to $MODEL_DIR/$MODEL_PATH/$WASM_FILE"
#       WASM_FOUND=true
#       break
#     else
#       log "Error: Failed to copy $WASM_FILE to $MODEL_DIR/$MODEL_PATH/$WASM_FILE"
#     fi
#   fi
# done

# if [ "$WASM_FOUND" = false ]; then
#   log "Error: No WebAssembly binary file found in build output"
#   log "Checked for: ${WASM_BINARY_FILES[*]}"
#   exit 1
# fi

# # Copy DATA file if it exists
# DATA_FOUND=false
# for DATA_FILE in "${WASM_DATA_FILES[@]}"; do
#   if [ -f "build-wasm-simd-asr/install/bin/wasm/asr/$DATA_FILE" ]; then
#     # Use cp -f to force overwrite if the file already exists
#     cp -f "build-wasm-simd-asr/install/bin/wasm/asr/$DATA_FILE" "$MODEL_DIR/$MODEL_PATH/$DATA_FILE"
#     # Verify the copy operation
#     if [ -f "$MODEL_DIR/$MODEL_PATH/$DATA_FILE" ]; then
#       log "Copied $DATA_FILE to $MODEL_DIR/$MODEL_PATH/$DATA_FILE"
#       DATA_FOUND=true
#       break
#     else
#       log "Error: Failed to copy $DATA_FILE to $MODEL_DIR/$MODEL_PATH/$DATA_FILE"
#     fi
#   fi
# done

# # DATA file is optional, so we don't exit if it's not found
# if [ "$DATA_FOUND" = false ]; then
#   log "Warning: No DATA file found in build output. This may be normal for some models."
#   log "Checked for: ${WASM_DATA_FILES[*]}"
# fi

# # Copy model files to the project's model directory
# # if [ -f "wasm/asr/assets/model.onnx" ]; then
# #   # Use cp -f to force overwrite if the file already exists
# #   cp -f "wasm/asr/assets/model.onnx" "$MODEL_DIR/$MODEL_PATH/"
# #   # Verify the copy operation
# #   if [ -f "$MODEL_DIR/$MODEL_PATH/model.onnx" ]; then
# #     log "Copied model.onnx to $MODEL_DIR/$MODEL_PATH/"
# #   else
# #     log "Error: Failed to copy model.onnx to $MODEL_DIR/$MODEL_PATH/"
# #     exit 1
# #   fi
# # else
# #   log "Error: model.onnx not found in assets directory"
# #   exit 1
# # fi

# if [ -f "wasm/asr/assets/tokens.txt" ]; then
#   # Use cp -f to force overwrite if the file already exists
#   cp -f "wasm/asr/assets/tokens.txt" "$MODEL_DIR/$MODEL_PATH/"
#   # Verify the copy operation
#   if [ -f "$MODEL_DIR/$MODEL_PATH/tokens.txt" ]; then
#     log "Copied tokens.txt to $MODEL_DIR/$MODEL_PATH/"
#   else
#     log "Error: Failed to copy tokens.txt to $MODEL_DIR/$MODEL_PATH/"
#     exit 1
#   fi
# else
#   log "Error: tokens.txt not found in assets directory"
#   exit 1
# fi

# # Verify that all required files exist in the target directory
# log "Verifying all required files exist in $MODEL_DIR/$MODEL_PATH/"
# ls -la "$MODEL_DIR/$MODEL_PATH/" >> "$LOG_FILE"

# # Final verification of all required files
# REQUIRED_FILES=("model.onnx" "tokens.txt" "sherpa-onnx-asr.js" "sherpa-onnx-asr.wasm")
# MISSING_FILES=false

# for FILE in "${REQUIRED_FILES[@]}"; do
#   if [ ! -f "$MODEL_DIR/$MODEL_PATH/$FILE" ]; then
#     log "Error: Required file $FILE is missing from $MODEL_DIR/$MODEL_PATH/"
#     MISSING_FILES=true
#   fi
# done

# if [ "$MISSING_FILES" = true ]; then
#   log "Error: Some required files are missing. Build may be incomplete."
#   exit 1
# fi

# log "All files copied successfully"

# log "=== Build completed ==="
# # Return to the original directory
# cd "$ORIGINAL_DIR"

# log "Build files are kept in: $BUILD_DIR"
# log "Build log is available at: $BUILD_DIR/$LOG_FILE"
# log "You can resume or inspect the build by navigating to this directory."

# # Print a summary of the build
# log ""
# log "=== Build Summary ==="
# log "Model Type: $MODEL_TYPE"
# log "All Files (WASM and model files): $MODEL_DIR/$MODEL_PATH/"
# log "Build Directory: $BUILD_DIR"
# log "Build Log: $BUILD_DIR/$LOG_FILE"

# # Print a final message to the console (not to the log file)
# echo "=== Build completed successfully ==="
# echo "All files (WASM and model files) are available at: $MODEL_DIR/$MODEL_PATH/"
# echo "Build log is available at: $BUILD_DIR/$LOG_FILE"

# # Run the copy-wasm-files.js script to ensure all files are in the correct location
# echo ""
# echo "=== Running copy-wasm-files.js to finalize file placement ==="
# node "$ORIGINAL_DIR/scripts/copy-wasm-files.js" --model "$MODEL_TYPE" --source "$BUILD_DIR"

# # Remove any empty model-specific directory that might have been created incorrectly
# # if [ -d "$OUTPUT_DIR/$MODEL_TYPE" ] && [ "$MODEL_PATH" != "tts/$MODEL_TYPE" ]; then
# #   # Check if it's empty
# #   if [ -z "$(ls -A "$OUTPUT_DIR/$MODEL_TYPE")" ]; then
# #     rmdir "$OUTPUT_DIR/$MODEL_TYPE"
# #     echo "Removed empty directory: $OUTPUT_DIR/$MODEL_TYPE"
# #   fi
# # fi

echo ""
echo "=== All done! ==="
echo "You can now use the model in your application."
